{"name": "vue-antd-pro", "version": "2.0.0", "private": true, "scripts": {"serve": "cross-env vue-cli-service serve", "serve:test": "cross-env vue-cli-service serve --mode test", "build": "vue-cli-service build", "build-dev": "vue-cli-service build --mode dev", "build-test": "vue-cli-service build --mode test", "build-uat": "vue-cli-service build --mode uat", "build-prod": "vue-cli-service build --mode prod", "build:preview": "vue-cli-service build --mode preview", "lint": "vue-cli-service lint", "lint:nofix": "vue-cli-service lint --no-fix", "test:unit": "vue-cli-service test:unit", "postinstall": "opencollective-postinstall"}, "dependencies": {"@antv/data-set": "^0.11.7", "@antv/g2": "^4.1.0", "@dimakorotkov/tinymce-mathjax": "^1.0.6", "@microsoft/fetch-event-source": "^2.0.1", "@tinymce/tinymce-vue": "^3.2.3", "animate.css": "^4.1.0", "ant-design-vue": "^1.6.0", "axios": "^0.20.0", "cli-plugin-eslint": "^0.0.1", "core-js": "^2.6.12", "dayjs": "^1.11.13", "easy-typer-js": "^2.1.0", "echarts": "^4.7.0", "echarts-liquidfill": "^2.0.6", "echarts-stat": "^1.2.0", "element-ui": "^2.15.0", "enquire.js": "^2.1.6", "esdk-obs-browserjs": "^3.24.3", "eslint": "^7.0.0", "fabric": "^3.6.3", "html2canvas": "^1.1.4", "js-audio-recorder": "^1.0.6", "js-base64": "^3.6.0", "js-cookie": "^2.2.0", "jspdf": "^2.3.1", "katex": "^0.16.18", "lib-flexible": "^0.3.2", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "markdown-it": "^12.1.0", "markmap-common": "0.15.3", "markmap-lib": "0.15.4", "markmap-view": "0.15.4", "mathjax": "^3.2.0", "md5": "^2.2.1", "mermaid": "^8.14.0", "moment": "^2.24.0", "node-uuid": "^1.4.8", "nprogress": "^0.2.0", "pcm-player": "0.0.18", "postcss-import": "^12.0.1", "postcss-px2rem": "^0.3.0", "postcss-url": "^8.0.0", "px2rem-loader": "^0.1.9", "sass-loader": "^9.0.3", "save": "^2.4.0", "screenfull": "^5.0.2", "swiper": "^6.1.2", "tinymce": "^5.5.1", "vconsole": "^3.15.1", "vue": "^2.5.22", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.2.1", "vue-cropper": "^0.5.5", "vue-directive-image-previewer": "^2.2.2", "vue-easytable": "^2.21.8", "vue-fabric": "^0.1.40", "vue-json-excel": "^0.2.98", "vue-katex-auto-render": "^0.1.3", "vue-loading-overlay": "^5.0.3", "vue-ls": "^3.2.0", "vue-mathjax": "0.0.11", "vue-photo-preview": "^1.1.3", "vue-resource": "^1.5.1", "vue-router": "3", "vue-svg-component-runtime": "^1.0.1", "vue-video-player": "^5.0.2", "vuedraggable": "^2.24.1", "vuex": "^3.1.0", "wangeditor": "^3.1.1", "xmldom": "^0.3.0"}, "devDependencies": {"@ant-design/colors": "^3.1.0", "@babel/core": "^7.11.6", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.11.5", "@types/minimatch": "^6.0.0", "@types/node": "16.11.7", "@vue/cli-plugin-babel": "3.7.0", "@vue/cli-plugin-unit-jest": "^3.7.0", "@vue/cli-service": "3.7.0", "@vue/eslint-config-standard": "4.0.0", "@vue/test-utils": "^1.0.0-beta.20", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^23.6.0", "babel-loader": "^8.1.0", "babel-plugin-import": "^1.11.0", "babel-preset-env": "^1.7.0", "cache-loader": "^4.1.0", "compression-webpack-plugin": "^6.1.1", "cross-env": "^7.0.3", "eslint-plugin-html": "5.0.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.8.1", "less-loader": "^4.1.0", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.2", "ts-loader": "~8.2.0", "typescript": "4.4.4", "vue-happy-scroll": "^2.1.1", "vue-lazyload": "^1.3.3", "vue-loader": "^15.9.7", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.5.22", "webpack": "^4.44.1", "webpack-bundle-analyzer": "^3.6.0", "webpack-theme-color-replacer": "^1.2.15"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0, "no-tabs": 0, "quotes": [2, "single", {"avoidEscape": true, "allowTemplateLiterals": true}], "semi": [2, "never", {"beforeStatementContinuationChars": "never"}], "no-delete-var": 2, "prefer-const": [2, {"ignoreReadBeforeAssign": false}]}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "collective": {"type": "opencollective", "url": "https://opencollective.com/ant-design-pro-vue"}, "volta": {"node": "14.19.3"}}