pipeline{

    agent any
     environment {
        RELEASE_PATH = "${WORKSPACE}/releases"
        COMMIT_ID = sh(returnStdout: true, script: 'git rev-parse HEAD')
        RELEASE_FILE_NAME = "${env.JOB_NAME}-releases-${env.BUILD_NUMBER}.zip"
        GIT_COMMIT_USER = sh(returnStdout: true, script: 'git show -s --format=%an')
        GIT_COMMIT_EMAIL =  sh(returnStdout: true, script: 'git show -s --format=%ae')
        GIT_COMMIT_MESSAGE = sh(returnStdout: true, script: 'git show -s --format=%s')
    }
    stages{
        stage('Remove'){
            steps{
                echo 'Removeing'
                sh "rm -rf releases"
            }
        }
        stage('Build'){
            steps{
                echo 'Building'
                sh "mkdir releases"
                sh "mkdir releases/teacher"
                sh "cd ${WORKSPACE} && /nodejs/bin/npm ci && /nodejs/bin/npm run build-dev"
                sh "cd ${WORKSPACE}"
                sh "rm -rf /var/www/apps/student/*"
                // sh "rm -rf /var/www/apps/teacher/*"
                // sh "cp -R ${WORKSPACE}/src/Student.Web/dist/* ${WORKSPACE}/releases/student"
                // sh "cp -R ${WORKSPACE}/src/Uwoo.Web/dist/* ${WORKSPACE}/releases/teacher"
            }
        }
        stage('Publish'){
            steps{
                echo "publish"
                sh "cp -R ${WORKSPACE}/dist/* /var/www/apps/student/"
            }
        }
        stage('Backup'){
            steps{
                 echo "backup"
            //  dir('releases') {
            //         sh "zip -q -r ${RELEASE_FILE_NAME} *"
            //     }
            // sh "cp ${WORKSPACE}/releases/${RELEASE_FILE_NAME} /root/backup"
            }
        }
      
    }
     post {
        success{
                emailext (
                subject: '${DEFAULT_SUBJECT}',
                body: '${DEFAULT_CONTENT}',
                to: "")
        }
        failure {
            emailext (
                subject: '${DEFAULT_SUBJECT}',
                body: '${DEFAULT_CONTENT}',
                to: "")
        }

   }
}