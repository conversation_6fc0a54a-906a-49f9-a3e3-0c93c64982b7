
Web后台开发框架,.NETCore3.1+Ant Design Vue版本  


1.前端： 确保已安装nodejs和yarn
2.用VS Code 打开文件夹src\Uwoo.Web


3.输入命令：yarn
4.输入命令：yarn run serve


5.输入账号：Admin，密码：123456进入后台主页



有代码生成器，可以生成基本代码


##前端全局变量获取设置参考：axios-lougin.js

设置属性：
Object.defineProperty(Vue.prototype, '$rootUrl', { value: rootUrl() })

其他页面获取：
`${this.$rootUrl}/Paper/Exam_Paper/Word?subjectId=2`



前端可能出现的异常记录：
【
控制台如果看见这个异常表示接口返回的字段类型与前端类型不一致，类型上保持一致即可。
[Vue warn]: Invalid prop: type check failed for prop "value". Expected String, Object, Array, got Number with value 23.】
