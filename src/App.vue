<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <router-view />
    </a-config-provider>
    <el-dialog
      center
      :visible.sync="screenToggle"
      width="60%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <span slot="title"><b style="font-size: 28px">提示</b></span>
      <div class="screenToggleBox">
        <img src="./assets/images/screenToggle.png" alt="" />
      </div>
      <div class="screenToggleBox">
        <!-- <p><b>请旋转屏幕，横屏使用！</b></p> -->
        <p><b>请确保打开设备【屏幕旋转】开关后，旋转屏幕，横屏使用！</b></p>
        <br />
        <p><b>为确保您的正常使用，建议使用：</b><b style="color: #007aff">系统自带、谷歌、UC等浏览器。</b></p>
      </div>
    </el-dialog>
<!--  必须竖屏的提示  -->
    <el-dialog
      center
      :visible.sync="visibleElse"
      width="60%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <span slot="title"><b style="font-size: 28px">提示</b></span>
      <div class="screenToggleBox">
        <p><b>该页为拍照，请竖屏使用！</b></p>
      </div>
    </el-dialog>
    <!-- 学校选择 -->
    <el-dialog
      center
      :visible.sync="schoolVisible"
      width="40%"
      title="机构选择"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" >
        <div class="school-view">
          <p>请选择您现在正在就读的学校</p>
          <el-form-item prop="radioValue">
            <el-radio-group v-model="ruleForm.radioValue" @input="changeRadio">
              <el-radio :label="item.id" v-for="(item,index) in schoolInfo.schoolData" :key="index">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <div class="class-view">
          <p>请选择您就读所在的班级</p>
          <el-form-item prop="selectClassValue">
            <el-select v-model="ruleForm.selectClassValue" placeholder="请选择" size="small">
              <el-option
                v-for="item in schoolInfo.classData"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </div>

      </el-form>
      <!-- <div class="container">
        <div class="school-view">
          <p>请选择您现在正在就读的学校</p>
          <el-radio-group v-model="schoolInfo.radioValue" @input="changeRadio">
            <el-radio :label="item.id" v-for="(item,index) in schoolInfo.schoolData" :key="index">{{ item.name }}</el-radio>
          </el-radio-group>
        </div>
        <div class="class-view">
          <p>请选择您就读所在的班级</p>
          <el-select v-model="schoolInfo.selectClassValue" placeholder="请选择" size="small">
            <el-option
              v-for="item in schoolInfo.classData"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handelClickSubmit" size="small">确 定</el-button>
      </span>
    </el-dialog>
<!--  帮助地址  -->
    <div class="bangzhu" id="drag1" v-drag v-if="LoginSource == 2">
      <div @click="linkClick">帮助</div>
    </div>
  </div>
</template>

<script>
// import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import { AppDeviceEnquire } from '@/utils/mixin'
import { colorList } from './components/SettingDrawer/settingConfig'
import TokenCache from '@/utils/cache/TokenCache'
import drag from '@/utils/drag'

const TY_path = [
  { type_v: 'baosan', path: '/Base_Manage/Home/WxSubmitDSLogin' },
  { type_v: 'xuhui', path: '/Base_Manage/Home/XuHuiSubmitDSLogin' },
  { type_v: 'shanghai', path: '/Base_Manage/Home/SubmitDSLogin' },
]

export default {
  mixins: [AppDeviceEnquire],
  data() {
    return {
      locale: zhCN,
      Token: '',
      name: '',
      screenToggle: false,
      maintenNotice: false,
      visibleElse: false,
      // 机构选择
      schoolVisible:false,
      schoolInfo:{
        schoolData:[],// 当前学生绑定的学校和班级信息
        classData:[],// 班级信息
        queryData:{},
      },
      ruleForm:{
        radioValue:'',// 学校
        selectClassValue:'',// 班级
      },
      rules:{
        radioValue: [
          { required: true, message: '请选择学校', trigger: 'change' },
        ],
        selectClassValue: [
          { required: true, message: '请选择班级', trigger: 'change' }
        ],
      },
      // 登录渠道
      LoginSource: 0, // 1：专科专练 2：三个助手
    }
  },
  created() {
    let V_S_F_A_L_O = localStorage.getItem('V_S_F_A_L_O')
    if (!V_S_F_A_L_O) localStorage.setItem('V_S_F_A_L_O', this.$store.state.versionForAutoLoginOut)
    this.getUrlParams()
    // this.openNoticeDialog()
  },
  activated() {
    this.LoginSource = localStorage.getItem('LoginSource')
  },
  watch: {
    $route() {
      window.addEventListener('resize', this.watch, false)
      // const Home = window.location.href.split('?')[1]
      // if (this.$route.fullPath === '/Home/Index?' + Home) {
      //   this.getUrlParams()
      // }
      this.watch()
    },
  },
  mounted() {
    console.log(process.env.NODE_ENV,'1')
    window.addEventListener('resize', this.watch, false)
    this.watch()
    var reg = /^(192.168.110.|192.168.1.|**************)/
    if (window.location.hostname !== 'localhost' && !reg.test(window.location.hostname)) {
      // 判断非本地server时 http强制转换成https
      var targetProtocol = 'https:'
      if (window.location.protocol != targetProtocol)
        window.location.href = targetProtocol + window.location.href.substring(window.location.protocol.length)
    }
    if (document.location.href === 'https://www.eduwon.cn/') {
      window.addEventListener('resize', () => {})
      if (this._isMobile()) {
        this.$router.push('/MobileHome/Mobindex')
      } else {
        this.$router.push('/Index/index')
      }
    }
    this.LoginSource = localStorage.getItem('LoginSource')
  },
  methods: {
    linkClick(){
      // 点击事件触发时，判断当前状态是拖拽还是点击，若是拖拽，直接返回不继续执行
      const isDrag = document.getElementById('drag1').getAttribute('drag-flag')
      console.log('拖拽状态',isDrag)
      if (isDrag === 'true') {
        return
      }
      window.open('https://uwoo.obs.cn-east-2.myhuaweicloud.com/student-handbook.pdf', '_blank');
    },
    handelClickSubmit(){
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.schoolVisible = false;
          this.SubmitDSLogin()
        }
      });
    },
    /**
     * @description: 改变学校更换学校下所绑定的班级
     * @param {*} e
     * @return {*}
     */
    changeRadio(e){
      const item = this.schoolInfo.schoolData.filter(item=>item.id == e)
      this.schoolInfo.classData = item[0].classinfos
      this.ruleForm.selectClassValue = item[0].classinfos[0].id
    },
    /**
     * @description: 获取三个猪手登陆学生学校和班级
     * @return {*}
     */
    async GetStudentSchoolClassList(){
      const res = await this.$http.post('/Base_Manage/Home/GetStudentSchoolClassList', {tokend:this.schoolInfo.queryData.token});
      const {Success,Data,Msg} = res
      if(Success){
        this.schoolInfo.schoolData = Data;
        // 当前学生只有一个学校和班级 默认登陆
        if(Data.length == 1 && Data[0].classinfos.length == 1){
          if(this.schoolInfo.queryData.type = "eduwonSTU"){
            this.eduwonSubmitDSLogin()
          }else{
            this.SubmitDSLogin()
          }
        }else{
          this.schoolVisible = true;
        }
      }else{
        this.$message.error(Msg)
      }
    },
    GetPeriodList(subject) {
      this.$uwonhttp
        .post('/Period_Subject/Period_Subject/GetSubjectListByStudent', {
          userid: localStorage.getItem('UserId'),
          subject,
        })
        .then((res) => {
          const newRes = res.data.Data
          if (subject) {
            newRes.forEach((item) => {
              if (item.JX == subject) {
                localStorage.setItem('SubjectId', item.Id)
              }
            })
          } else {
            localStorage.setItem('SubjectId', newRes[0].Id)
          }
        })
    },
    getUrlParams() {
      setTimeout(()=>{
        const Token = this.$route.query.token ?? ''
        const name = this.$route.query.n ?? ''
        const type = this.$route.query.type ?? ''
        const subject = this.$route.query.subject ?? ''
        const term = this.$route.query.term ?? ''
        localStorage.setItem('CurrentSemester', term)
          if(type == 'xuhui'){
            TokenCache.setToken(Token)
            localStorage.setItem('isXH', 1)
            localStorage.setItem('PL_newP', '1')
            this.GetUserInfoByUserName(name)
          } else {
            if (Token && name && type && subject) {
              var array = Token.split(' ')
              var token = array.join('+')
              if (type == 'shanghai') {
                localStorage.setItem('PL_newP', '2')
              } else {
                localStorage.setItem('PL_newP', '1')
              }
              this.schoolInfo.queryData = {
                token, name, type, subject
              }
              if(type =='huangpu'){
                console.log(789)
                TokenCache.setToken(token)
                this.GetUserInfoByUserName(name, 2)
              }else{
                this.GetStudentSchoolClassList()
              }
            }
          }
      },200)
    },
    // 黄浦区统一认证登录获取三方平台token /DS/Api/HpResult
    async HpResult () {
      const url = new URL(window.location.href);
      const encodedRedirectValue = url.searchParams.get('redirect');
      const decodedRedirectValue = decodeURIComponent(encodedRedirectValue).slice(2);
      if(!decodedRedirectValue) return
      const queryParameters = new URLSearchParams(decodedRedirectValue);
      const code = queryParameters.get('code');
      const jm = queryParameters.get('jm');
      console.log(code)
      console.log(jm)
      if(code && jm){
        const res = await this.$http.get(`/DS/Api/HpResult?code=${code}&jm=${jm}`)
        const {Success,Data,Msg} = res
        if(Success){
          TokenCache.setToken(Data.Token)
          this.GetUserInfoByUserName(Data.ID, 2)
        }else{
          this.$message.error(Msg)
        }
      }
    },
    // 自己平台登陆三个助手
    async eduwonSubmitDSLogin(){
      const {token, name, type, subject} = this.schoolInfo.queryData
      const schoolItem = this.schoolInfo.schoolData[0]
      let params = {
        tokend: token,
        tySchoolId:this.ruleForm.radioValue || schoolItem.id,
        tyClassId:this.ruleForm.selectClassValue || schoolItem.classinfos[0].id
      }
      await this.$uwonhttp.post('/User/Login/StudentAppSubmitDSLogin', params).then((res) => {
        if (res.data.Success) {
          TokenCache.setToken(res.data.Data.Token)
          // debugger
          this.GetUserInfoByUserName(name, subject)
          localStorage.setItem('GetOut_url', token)
        } else {
          this.$message.error(res.Msg)
        }
      })
    },
    //统一用户登录
    async SubmitDSLogin() {
      const {token, name, type, subject} = this.schoolInfo.queryData
      const schoolItem = this.schoolInfo.schoolData[0]
      let params = {
        tokend: token,
        tySchoolId:this.ruleForm.radioValue || schoolItem.id,
        tyClassId:this.ruleForm.selectClassValue || schoolItem.classinfos[0].id
      }
      let path = '/Base_Manage/Home/SubmitDSLogin'
      let find_path = TY_path.find((item) => item.type_v == type)
      if (find_path !== undefined) path = find_path.path
      await this.$http.post(path, params).then((res) => {
        if (res.Success) {
          TokenCache.setToken(res.Data)
          // debugger
          this.GetUserInfoByUserName(name, subject)
          localStorage.setItem('GetOut_url', token)
        } else {
          this.$message.error(res.Msg)
        }
      })
    },

    async GetUserInfoByUserName(name, subject) {
      const res = await this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: name })
      if (res.Success) {
        if (res.Data.Roles != null) {
          localStorage.setItem('role', res.Data.Roles[0].Id)
          localStorage.setItem('SchoolId', res.Data.User.SchoolId)
          localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
          if (
            res.Data.Roles.some((item) => {
              return item.RoleName === '校领导'
            })
          ) {
            this.isSchoolHeader = true
            localStorage.setItem('isSchoolHeader', this.isSchoolHeader)
            localStorage.removeItem('isTeacher')
            localStorage.removeItem('isTeachingStaff')
            localStorage.setItem('role', res.Data.Roles[0].Id)
          }
        }
        if (!res.Data.Roles) {
          this.isStudent = '1'
          localStorage.setItem('isStudent', this.isStudent)
          localStorage.removeItem('isTeacher')
        } else {
          this.isTeacher = res.Data.Roles.some((item) => {
            return item.RoleName === '教师'
          })
          localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
          localStorage.setItem('isTeacher', this.isTeacher)
          localStorage.removeItem('isStudent')
          localStorage.setItem('role', res.Data.Roles[0].Id)
        }

        this.id = res.Data.UserType
        localStorage.setItem('Id', res.Data.UserType)
        localStorage.setItem('UserId', res.Data.User.Id)
        localStorage.setItem('IsVip', res.Data.IsVip)
        localStorage.setItem('LoginSource',res.Data.LoginSource) // 登录入口渠道 2：三个助手/1：专科专练
        this.GetPeriodList(subject)
        this.$router.push({
          path: '/Home/Introduce',
          query: {
            id: this.id,
          },
        })
      }
    },
    toQRLogin() {
      this.$router.push('/Compage/QRLogin')
    },
    _isMobile() {
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|Macintosh)/i
      )
      return flag
    },
    //监听是pc还是移动端
    watch() {
        let result = this._isMobile()
        if (result) {
          this.renderResize()
        }
    },
    //判断横屏或者竖屏
    renderResize() {
      let width = document.documentElement.clientWidth
      let height = document.documentElement.clientHeight
      if (this.$route.path === '/Student/Exam_Student/highSchoolCapture/photo') {
        this.screenToggle = false
        // this.visibleElse = false
        if (height < width) {
          this.visibleElse = true
        } else {
          this.visibleElse = false
        }
      } else {
        this.screenToggle = height > width ? true : false
      }
    },
  },
}
</script>
<style lang="less">
.school-view{
  margin-bottom: 20px;
  p{
    margin-bottom: 10px;
  }
}
#app {
  height: 100%;
}
.ant-btn-primary {
  background-color: #68bb97;
  border-color: #68bb97;
}
.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: #68bb97;
  border-color: #68bb97;
}
.ant-btn:hover,
.ant-btn:focus {
  color: #fff;
  background-color: #ddd;
  border-color: #ddd;
}
/deep/.ant-radio-checked .ant-radio-inner::after {
  background-color: #68bb97;
}
.screenToggleBox {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 0.5rem 0;
  flex-wrap: wrap;
  p {
    width: 100%;
    font-size: 0.25rem;
    // text-align: center;
    padding: 0 3%;
  }
}
.notice {
  font-size: 16px;
  padding-bottom: 20px;
}
.notice p {
  margin: 10px 0;
}
.focus_on {
  text-emphasis: black open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
.focus_on_red {
  text-emphasis: red open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
.focus_on_green {
  text-emphasis: green open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
.focus_on_orange {
  text-emphasis: orange open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
.focus_on_yellow {
  text-emphasis: yellow open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
.focus_on_blue {
  text-emphasis: blue open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
.focus_on_purple {
  text-emphasis: purple open;
  -webbit-text-emphasis: black open;
  text-emphasis-style: '●';
  -webkit-text-emphasis-style: '●';
  -webkit-text-emphasis-position: under;
  text-emphasis-position: under;
}
// 帮助样式
.bangzhu{
  width: 70px;
  height: 70px;
  border-radius: 50%;
  text-align: center;
  line-height: 70px;
  background: rgb(86,133,237);
  position: fixed;
  bottom: 50px;
  left: 50px;
  cursor: pointer;
  color: #fff;
  user-select: none;
  .el-link{
    font-size: 20px;
    .el-link--inner{
      color: #FFFFFF;
    }
  }
}
</style>
<style >
/* .el-message-box{
    width:2.5rem !important;
  } */
</style>
