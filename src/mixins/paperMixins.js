// 所有使用键盘的继承这些方法
export default {
  data () {
    return {
      isMobile: false,
      boardType: '' // key-数字键盘  sys-系统键盘
    }
  },
  methods: {
    closeToChangeInput () {
      this.boardType = 'sys'
      // const input_box = document.getElementById('AnswerPaper').getElementsByClassName('input_box')
      // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
      // const ps = document.getElementById('AnswerPaper').getElementsByClassName('virtual_input')

      // const inputs = $(`#Imgid${this.num}`).find('input')
      // const ps = $(`#Imgid${this.num}`).find('.virtual_input')
      // if (inputs.length == 0 && ps.length == 0) {
      //   return
      // }
      // for (let i = 0; i < ps.length; i++) {
      //   inputs[i].style.display = 'none'
      //   ps[i].style.display = 'inline-block'
      // }
      // if (this.currentInput) {
      //   this.put_val('n')
      // } else {
      //   this.findInputNoVal()
      // }
    },
    // input 点击
    onkeyup (e) {
      console.log(e)
      var event = {
        target: $(e.target).closest('.input_box').find('.virtual_input')[0]
      }
      if (!event.target) {
        if (e.target.className === 'input') {
          event = e
        } else {
          if (e.target.nodeName === 'BUTTON') {
            this.Banked_Cloze(e)
          } else {
            return
          }
        }
      }
      const inputs = $(`#Imgid${this.num}`).find('input')
      const ps = $(e.target).closest('.formatImg').find('.virtual_input')
      console.log(event)
      if (event.target?.MathQuill) {
        if (event.target.dataset.type == '5') {
          this.currentInput = ps[parseInt(event.target.dataset.index)] || inputs[parseInt(event.target.dataset.index)]
        }
        if (['42'].indexOf(event.target.dataset.index) == -1) {
          for (let i = 0; i < ps.length; i++) {
            if (ps[i].dataset.index == event.target.dataset.index) {
              this.currentInput = ps[i]
              break
            }
          }
        }
        const blink = document.getElementById('blink')
        if (blink) {
          blink.remove()
          clearInterval(this.blinkTimer)
          clearTimeout(this.debounceTimer)
        }
        this.addBlink()
        this.$refs.keyword.chengeVal(event.target.MathQuill.latex())
      } else {
        this.currentInput = event.target
        this.$refs.keyword.chengeVal(event.target?.value)
      }
      this.currentInputlength = inputs.length || ps.length // 待处理 涉及切换题目 这个有问题
      // this.Review_Key()
      // return
      const parentDom = $(event.target).closest('div')
      const inputDom = parentDom.find('input')
      const infoList = inputDom.attr('infolist') || ''
      if (infoList) {
        this.Review_Key()
      }
    },
    // 键盘 点击
    GetKeyVal (info) {
      if (this.itemType == 51) {
        this.$refs.RenderTable[0].onChangeValue(info.value ,this.row,this.column)
        //this.$refs.RenderTable[0].onChangeValue(`<span class=\"math-tex\">\\(${info.value}\\)</span>`  ,this.row,this.column)
        return 
      }
      // console.log(info, ':info');
      // console.log(this.boardType,"教学键盘")
      if (!this.currentInput) {
        this.$message({
          message: '请先选中您的目标输入框！',
          type: 'warning'
        })
        return
      }
      const key = info.value
      let list

      // if (this.boardType == 'key') {
      //   this.currentInput.innerHTML = key
      //   this.$refs.keyword.chengeVal(this.currentInput.innerHTML)
      //   this.addBlink()
      // } else {
      const parentDom = $(this.currentInput).closest('div')
      const inputObj = parentDom.find('input')
      const id = inputObj.attr('id')
      const infoList = JSON.stringify(info.infoList)
      inputObj.attr('infolist', infoList)
      if (info.type === 'key') {
        parentDom[0].MathQuill.latex(key)
      } else {
        this.currentInput.value = key
      }
      // }
      setTimeout(() => {
        if (!this.currentInput.MathQuill) {
          this.changeWidth(this.currentInput, 2)
        }
        // 填空|?不可变行表格填空
        const _PaperList = this.PaperList[this.CurrentNum - 1]
        const _dataIndex = this.currentInput.dataset.index
        const _dataType = this.currentInput.dataset.type
        // ##########################################
        if (_dataType == '5') {
          // if (_PaperList.StudentCorrectItems) {
          //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].Answer = key
          //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].IsKeyTrue = true
          //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].isDisabled = false
          // }
          _PaperList.AnswerList[parseInt(_dataIndex)] = key
          _PaperList.Answer = _PaperList.AnswerList.join('|')
          // let blank_len = $(this.currentInput).closest(`#Imgid${this.num}`).find('input').length
          const blank_len = $(`#Imgid${this.num}`).find('input').length
          if (_PaperList.AnswerList.length < blank_len) {
            _PaperList.HasAnswer = false
          } else if (_PaperList.AnswerList.length == blank_len) {
            _PaperList.HasAnswer = !_PaperList.AnswerList.some((e) => !e)
          }
        }
        if (['40', '42'].indexOf(this.currentInput.dataset.type) != -1) {
          this.currentInput.value = key
          // this.TableInputs = this.currentInputlength
          this.TableInputs =  $(`.tableTitle`).find('input').length
          const item = {
            id: this.currentInput.id || this.currentInput.dataset.index,
            // value: this.currentInput.value,
            value: key
          }
          const arr = []
          const idx = this.PaperList[this.CurrentNum - 1].AnswerList.findIndex((obj) => {
            return obj.id === this.currentInput.id || obj.id === this.currentInput.dataset.index
          })
          if (idx !== -1) {
            this.PaperList[this.CurrentNum - 1].AnswerList[idx].value = key
          } else {
            this.PaperList[this.CurrentNum - 1].AnswerList.push(item)
          }
          list = this.PaperList[this.CurrentNum - 1].AnswerList
          this.PaperList[this.CurrentNum - 1].Answer = ''
          for (let i = 0; i < list.length; i++) {
            if (i + 1 === list.length) {
              this.PaperList[this.CurrentNum - 1].Answer += list[i].value
            } else {
              if (list[i].id.split('.')[0] === list[i + 1].id.split('.')[0]) {
                this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + '|'
              } else {
                this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + ','
              }
            }
          }
          if (list.findIndex((item) => !item) == -1 && list.length == this.TableInputs) {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          } else {
            this.PaperList[this.CurrentNum - 1].HasAnswer = false
          }
        }
        // 思考问答题
        if (this.currentInput.dataset.type == '47') {
          // this.TableInputs = this.currentInputlength
          let normal_inputs = $(`#Imgid${this.num}`).find('input');
          let key_inputs = $(`#radio_box_input`).find('input')
          // let key_inputs = $(`#Imgid${this.num}`).find('input')
          // this.PaperList[this.CurrentNum - 1].AnswerList[this.currentInput.dataset.index] = this.currentInput.innerHTML
          // this.PaperList[this.CurrentNum - 1].AnswerList[this.currentInput.dataset.index] = this.currentInput.value
          if(key_inputs.length>0 ){
            this.PaperList[this.CurrentNum - 1].AnswerList[key_inputs[0].id] = key_inputs[0].value
          }else{
            this.PaperList[this.CurrentNum - 1].AnswerList[this.currentInput.dataset.index] = this.currentInput.value
          }
          list = this.PaperList[this.CurrentNum - 1].AnswerList
          this.PaperList[this.CurrentNum - 1].Answer = ''
          for (let i = 0; i < list.length; i++) {
            if (i + 1 === list.length) {
              this.PaperList[this.CurrentNum - 1].Answer += list[i]
            } else {
              this.PaperList[this.CurrentNum - 1].Answer += (list[i] ? list[i] : '') + '|'
            }
          }
          if (this.currentInput.value !== '' && this.show_radiobox == false) {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          } else if (this.show_radiobox === true) {
            // let key_inputs = 1
            if (this.Getvideo !== '没有') {
              // key_inputs = this.currentInputlength - 2
              localStorage.setItem('Type_41', list[0].split('|')[0] + '|' + this.Getvideo)
            } else {
              // key_inputs = 2
              // key_inputs = 1
              localStorage.setItem('Type_41', list[0].split('|')[0])
            }
            if (list.length == (key_inputs.length + key_inputs.length) && list.findIndex((item) => !item) == -1) {
              this.PaperList[this.CurrentNum - 1].HasAnswer = true
            } else {
              this.PaperList[this.CurrentNum - 1].HasAnswer = false
            }
          } else {
            this.PaperList[this.CurrentNum - 1].HasAnswer = false
          }
        }
        if(this.currentInput.dataset.type == '41'){
          
          this.get41Value(info)
        }
        // const diaMessage = this.PaperList.map((item, index) => {
        //   const a = item.AnswerList.length > 0 ? item.AnswerList.join('|') : item.Answer
        //   return `<div>答案${index + 1}: ${a} </div>`
        // }).join('')
        // this.$notify({
        //   title: '警告',
        //   dangerouslyUseHTMLString: true,
        //   message: diaMessage,
        //   type: 'warning'
        // })
      }, 200)
    },
    //处理41题型-可变行填空
    get41Value(info){
      let ids = this.currentInput.id.split('.')
      let indx = Number(ids[0])
      let ind =  Number(ids[1])
      this.BlankInput({target:{value:info.value}},indx,ind)
    },

    changeWidth (e, type) {
      if (!e.target) {
        const l = e.value.length
        e.style.width = (l + 1) * 20 + 'px'
        this.PaperList[this.CurrentNum - 1].WidhList[parseInt(e.dataset.index)] = e.style.width
      } else {
        const l = e.target?.value.length
        e.target.style.width = (l + 1) * 20 + 'px'
        this.PaperList[this.CurrentNum - 1].WidhList[parseInt(e.target.dataset.index)] = e.target.style.width
      }
    },
    // 拦截键盘数据
    handleInputInterceptor (event) {
      const t = $(event.target).closest('.virtual_input')
      const input = t.parent().find('input')
      if (this.boardType == 'key') {
        const value = t[0].MathQuill.latex()
        input.val(value)
      } else {
        this.handleInput({ target: input[0] })
      }
    },
    // 普通填空题  获取Answer
    handleInput (event) {
      if (this.boardType == 'key') {
        return
      }
      const inputs = $(`#Imgid${this.num}`).find('input')
      // const value = event.target.value.trim()
      // const value = this.str2utf8(event.target.value).replace(/\xe2\x80\x8b/g,"")
      const value = event.target.value
      this.$refs.keyword.chengeVal(value)
      let list
      let inputs_47
      this.changeWidth(event, 2)
      const _PaperList = this.PaperList[this.CurrentNum - 1]
      const _dataIndex = event.target.dataset.index
      const _dataType = event.target.dataset.type
      if (_dataType == '5') {
        console.log(9999)
        _PaperList.AnswerList[parseInt(_dataIndex)] = value
        // if (_PaperList.StudentCorrectItems) {
        //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].Answer = value
        //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].IsKeyTrue = true
        //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].isDisabled = false
        // }
        _PaperList.Answer = _PaperList.AnswerList.join('|')
        // 纠错
        // if (_PaperList.StudentCorrectItems) {
        //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].Answer = value
        //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].IsKeyTrue = true
        //   _PaperList.StudentCorrectItems[parseInt(_dataIndex)].isDisabled = false
        // }
        const blank_len = $(`#Imgid${this.num}`).find('input').length
        if (_PaperList.AnswerList.length < blank_len) {
          _PaperList.HasAnswer = false
        } else if (_PaperList.AnswerList.length == blank_len) {
          _PaperList.HasAnswer = !_PaperList.AnswerList.some((e) => !e)
        }
      }

      if (_dataType == '47') {
        inputs_47 = inputs.length
        this.PaperList[this.CurrentNum - 1].AnswerList[parseInt(event.target.id)] = value
        // console.log(this.PaperList,':47')
        list = this.PaperList[this.CurrentNum - 1].AnswerList
        this.PaperList[this.CurrentNum - 1].Answer = ''
        for (let i = 0; i < list.length; i++) {
          if (i + 1 === list.length) {
            this.PaperList[this.CurrentNum - 1].Answer += list[i]
          } else {
            this.PaperList[this.CurrentNum - 1].Answer += (list[i] ? list[i] : '') + '|'
          }
        }

        if (event.target.value !== '' && this.show_radiobox == false) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = true
        } else if (this.show_radiobox === true) {
          if (this.Getvideo !== '没有') {
            inputs_47 = inputs.length - 2
            localStorage.setItem('Type_41', list[0].split('|')[0] + '|' + this.Getvideo)
          } else {
            inputs_47 = 1
            localStorage.setItem('Type_41', list[0].split('|')[0])
          }
          if (list.length == inputs_47 && list.findIndex((item) => !item) == -1) {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          } else {
            this.PaperList[this.CurrentNum - 1].HasAnswer = false
          }
        } else {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        }
      }
      if (_dataType === '40' || _dataType == '42') {
        // if (event.target.attributes[5].value === '40' || event.target.attributes[5].value == '42') {
        // const inputs = 
        // this.TableInputs = inputs.length
        this.TableInputs = $(`.tableTitle`).find('input').length
        const item = {
          id: event.target.id,
          value: value
        }
        const arr = []
        const idx = this.PaperList[this.CurrentNum - 1].AnswerList.findIndex((obj) => {
          return obj.id === event.target.id
        })
        if (idx !== -1) {
          this.PaperList[this.CurrentNum - 1].AnswerList[idx].value = value
        } else {
          this.PaperList[this.CurrentNum - 1].AnswerList.push(item)
        }
        list = this.PaperList[this.CurrentNum - 1].AnswerList
        this.PaperList[this.CurrentNum - 1].Answer = ''
        for (let i = 0; i < list.length; i++) {
          if (i + 1 === list.length) {
            this.PaperList[this.CurrentNum - 1].Answer += list[i].value
          } else {
            if (list[i].id.split('.')[0] === list[i + 1].id.split('.')[0]) {
              this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + '|'
            } else {
              this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + ','
            }
          }
        }
        // console.log(list)
        // console.log(this.TableInputs)
        // console.log(list.findIndex((item) => !item) == -1 && list.length == this.TableInputs,':boolean')
        if (list.findIndex((item) => !item) == -1 && list.length == this.TableInputs) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = true
        } else {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        }
      }
      // const diaMessage = this.PaperList.map((item, index) => {
      //   const a = item.AnswerList.length > 0 ? item.AnswerList.join('|') : item.Answer
      //   return `<div>答案${index + 1}: ${a} </div>`
      // }).join('')
      // this.$notify({
      //   title: '警告',
      //   dangerouslyUseHTMLString: true,
      //   message: diaMessage,
      //   type: 'warning'
      // })
    },
    addBlink () {},
    // 使用教学键盘
    Review_Key () {
      if (this.itemType == 51) {
        if (!this.row) {
          //找到第一个输入框
          const tableData = this.$refs.RenderTable[0].tableData
          const columns = this.$refs.RenderTable[0].columns
          for (let i = 0; i < tableData.length; i++) {
            for (let j = 0; j < columns.length; j++) {
              const data=tableData[i][columns[j].field]
              if (data.type === 2&&data.stuAnswer==='') {
                this.row = tableData[i]
                this.column = columns[j]
                break
              }
            }
            if(this.row)break
          }
        }
        this.$refs.keyword.KeyWrod_True({
          key: 'key',
          value: this.row[this.column.field].stuAnswer
        })
        return 
      }
      
      if (this.currentInput) {
        // console.log(this.currentInput,':this.currentInput')
        const c = $(this.currentInput).closest('.input_box').find('.virtual_input')[0]
        // console.log(c,':c')
        if (c) {
          this.currentInput = c
        }
        //   if (this.boardType == 'key') {
        //     index = $(this.currentInput).attr('data-index')
        //   } else if (this.boardType == 'sys') {
        //     // closest()获得匹配选择器的第一个元素，从当前元素开始沿DOM树向上
        //     const parentDom = $(this.currentInput).closest('div')
        //     // find()查找jQ对象的所有后代，传入字符串选择器
        //     const inputDom = parentDom.find('input')
        //     // attr(name,value)获取|设置标签的属性值
        //     index = inputDom.attr('data-index')
        //     // 待处理
        //     infoList = inputDom.attr('infolist') || ''
        //   }
        //   value = this.PaperList[this.CurrentNum - 1].AnswerList[index] || ''
        // }
        // console.log(this.currentInput.MathQuill,':this.currentInput.MathQuill')
        if (this.currentInput.MathQuill) {
          this.$refs.keyword.KeyWrod_True({
            key: 'key',
            value: this.currentInput.MathQuill.latex()
          })
        } else {
          this.$refs.keyword.KeyWrod_True({
            key: 'sys',
            value: this.currentInput.value
          })
        }
      }

      this.isPcKey = 1
    },
    // 使用教学键盘|使用系统键盘
    Review_Key_pad (type) {
      this.boardType = type
      if (type === 'key') {
        // 使用教学绞盘 系统键盘不能获取焦点
        this.inputAddBlur()
        this.put_val()
        this.Review_Key()
      } else if (type === 'sys') {
        // 弹出系统键盘之前要确定默认哪个输入框获取焦点
        this.$refs.keyword.KeyWrod_False()
        this.inputAddBlur()
        // console.log(this.PaperList[0], ':this.PaperList[0]')
        this.put_val()
      }
    },
    // 切换键盘后给真实|虚拟输入框赋值
    put_val (isFocus) {
      const ansList = this.PaperList.length > 0 ? this.PaperList[this.num].AnswerList : []
      // console.log(ansList, ':ansList')
      const typeId = this.PaperList[this.num].TypeId
      let index = this.currentInput ? this.currentInput.dataset.index : 0
      if (this.boardType == 'sys') {
        const inputs = $(`#Imgid${this.num}`).find('input')
        if (ansList.length > 0) {
          if (['42'].indexOf(typeId) != -1) {
            for (let i = 0; i < inputs.length; i++) {
              const answer = ansList.find((item) => item.id == inputs[i].dataset.index)
              inputs[i].value = answer ? answer.value : ''
              if (this.currentInput && this.currentInput.dataset.index == inputs[i].dataset.index) {
                index = i
              }
            }
          }else {
            for (let i = 0; i < ansList.length; i++) {
              if (inputs[i]) inputs[i].value = ansList[i] || ''
            }
          }
        }
        if (isFocus != 'n' && index != -1) {
          if(inputs[index]){
            inputs[index].focus()
          }
        }
      }
      if (this.boardType == 'key') {
        const ps = $(`#Imgid${this.num}`).find('.virtual_input')
        if (ansList.length > 0) {
          if (['42'].indexOf(typeId) != -1) {
            for (let i = 0; i < ps.length; i++) {
              const answer = ansList.find((item) => item.id == ps[i].dataset.index)
              ps[i].innerHTML = answer ? answer.value : ''
              if (this.currentInput && this.currentInput.dataset.index == ps[i].dataset.index) {
                index = i
              }
            }
          } else {
            // for (let i = 0; i < ansList.length; i++) {
            //   ps[i].innerHTML = ansList[i] || ''
            // }
          }
        }
        // this.currentInput.blur()
      }
    },
    // 切换页面输入框类型 真实|模拟
    inputAddBlur () {
      if(!this.currentInput) return;
      if (!this.currentInput.MathQuill) {
        this.currentInput.focus()
      } else {
        this.currentInput.MathQuill.focus()
      }
      // const inputs = $(`#Imgid${this.num}`).find('input')
      // const ps = $(`#Imgid${this.num}`).find('.virtual_input')
      // const length = inputs.length || ps.length
      // for (let i = 0; i < length; i++) {
      //   if (this.boardType == 'key') {
      //     inputs[i].style.display = 'none'
      //     ps[i].style.display = 'block'
      //   } else if (this.boardType == 'sys') {
      //     ps[i].style.display = 'block'
      //     ps[i].MathQuill.focus()
      //   }
      // }
    },
    // 过滤出哪个虚拟输入框没有获得值
    findVituralInputNoVal () {
      // const p = document.getElementById('AnswerPaper').getElementsByClassName('virtual_input_p')
      const p = $(`#Imgid${this.num}`).find('.virtual_input_p')
      if (p.length == 0) {
        return
      }
      let index = 0
      for (let i = 0; i < p.length; i++) {
        if (p[i].innerHTML == '') {
          index = i
          break
        }
      }
      this.currentInput = p[index]
      this.addBlink()
    },
    // 过滤出哪个输入框没有获得值
    findInputNoVal (isFocus) {
      // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
      const inputs = $(`#Imgid${this.num}`).find('input')
      if (inputs.length == 0) {
        return
      }
      let index = 0
      for (let i = 0; i < inputs.length; i++) {
        if (inputs[i].value == '') {
          index = i
          break
        }
      }
      this.currentInput = inputs[index]
      if (isFocus != 'n') {
        inputs[index].focus()
      }
    },
    // 选词填空
    Banked_Cloze(e){
      // console.log(this.PaperList[this.num])
      if(this.isDisabled) return;
      const ind = e.target.dataset.ind
      const btns = $(`#Imgid${this.num}`).find('.ind_' + ind + '')
      const eventval = e.target.dataset.val
      const _PaperList = this.PaperList[this.CurrentNum - 1]
      const _options = this.PaperList[this.CurrentNum - 1].ItemChange.SelectFillBlank.Options
      this.AnswerArr[ind] = eventval
      _PaperList.Answer = this.AnswerArr.join('|')
      _PaperList.AnswerList = this.AnswerArr
      if (_PaperList.AnswerList.length < _options.length) {
        _PaperList.HasAnswer = false
      } else if (_PaperList.AnswerList.length == _options.length) {
        _PaperList.HasAnswer = !_PaperList.AnswerList.some((e) => !e)
      }
      for (let i = 0; i < btns.length; i++) {
        if (eventval === btns[i].getAttribute('data-val')) {
          btns[i].style.background = '#00d4af'
          btns[i].style.color = '#FFFFFF'
        } else {
          btns[i].style.background = '#DCDFE6'
          btns[i].style.color = '#000000'
        }
      }
    },
    _isMobile () {
      // 新增:Macintosh-ios设备
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowsdebuggerer|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|Macintosh)/i
      )
      this.isMobile = !!flag
    }

  },
  mounted () {
  },
  created () {
    this._isMobile()
  }
}
