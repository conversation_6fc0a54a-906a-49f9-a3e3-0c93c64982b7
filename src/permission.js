import router from '@/router'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import {
  setDocumentTitle,
  domTitle
} from '@/utils/domUtil'
import TokenCache from '@/utils/cache/TokenCache'
import OperatorCache from '@/utils/cache/OperatorCache'
import {
  initRouter
} from '@/utils/routerUtil'
import defaultSettings from '@/config/defaultSettings'
import store from './store/modules/LawReview/index'
NProgress.configure({
  showSpinner: false
}) // NProgress Configuration

// const whiteList = ['Index', 'Login', 'AppDown', 'UserLogin', 'Identity', 'TeacherRegister', 'StudentRegister', 'ManageRegister', 'Register', 'ForgetPassword', 'question', 'companyHome', 'refer', 'productProgram', 'aboutUs', 'QRLogin', 'armacok', 'companyIndex', 'Mobileindex'] // no redirect whitelist
const whiteList = ['Index', 'Login', 'AppDown', 'UserLogin', 'Identity', 'TeacherRegister', 'StudentRegister', 'ManageRegister', 'Register', 'ForgetPassword', 'question', 'Compage', 'Comindex', 'refer', 'productProgram', 'aboutUs', 'QRLogin', 'armacok', 'MobileHome', 'Mobindex', 'Abouts', 'Products', 'Qrlogin', 'Refers', 'companyIndex']
// window.localStorage.setItem(window.location.href.split('id=')[1])
// if (store.state.jwtToken) {
//   TokenCache.setToken(store.state.jwtToken)
// }
router.beforeEach((to, from, next) => {
  // const that = this
  NProgress.start() // start progress bar
  // console.log(to,":to")
  to.meta && (typeof to.meta.title !== 'undefined' && setDocumentTitle(`${to.meta.title} - ${domTitle}`))
  // 已授权
  if (TokenCache.getToken()) {
    OperatorCache.init(() => {
      if (to.path === '/Home/Login') {
        next({
          path: '/'
        })
        NProgress.done()
      }
      if (to.path === '/Home/Index' && Object.keys(to.query).indexOf('token')!= -1) {
        TokenCache.deleteToken()
        location.reload()
      } else {
        initRouter(to, from, next).then(() => {
          const redirect = decodeURIComponent(from.query.redirect || to.path)
          // 桌面特殊处理
          if (to.path === defaultSettings.desktopPath || to.path === '/404') {
            next()
          } else {
            if (to.path === redirect) {
              next()
            } else {
              // 跳转到目的路由
              next({
                path: redirect
              })
            }
          }
        })
      }
    })
  } else {
    if (whiteList.includes(to.name)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // localStorage.setItem('jwtToken', this.$cookieStore.getCookie('jwt'))
      // localStorage.setItem('jwtToken', store.state.jwtToken)
      // console.log('jwtToken', store.state.jwtToken)
      // location.href = `http://xh.eduwon.cn`
      if (document.location.href === 'http://gw.eduwon.cn/' || document.location.href === 'http://www.eduwon.cn/') {
        // if (document.location.href === 'http://localhost:5001/' || document.location.href === 'http://www.eduwon.cn/') {
        next({
          path: '/Index/index',
          query: {
            redirect: to.fullPath
          }
        })
        NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      } else {
        next({
          path: '/Home/Index',
          query: {
            redirect: to.fullPath
          }
        })
        NProgress.done() // if current page is login will not trigger afterEach hook, so manually handle it
      }
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})