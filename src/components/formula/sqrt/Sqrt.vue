<template>
  <div :id="this.elId" style="display:inline-block; padding: 5px 1px 0 18px; margin-right: 7px; position: relative;">
    <div :id="inputId" class="box empty" style="min-width: 0.853em; min-height: 1.08em;line-height:100%;z-index:3;position:relative;">
      <div class="item" v-for="(item, Index) in inputInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{ item }}</span>
      </div>
    </div>
    <div style="width: 104%; height: 100%; position: absolute;left:0;top:0;">
      <svg width="100%" height="100%" viewBox="0 0 400000 1080" preserveAspectRatio="xMinYMin slice"><path
        d="M95,702
c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14
c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54
c44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10
s173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429
c69,-144,104.5,-217.7,106.5,-221
l0 -0
c5.3,-9.3,12,-14,20,-14
H400000v40H845.2724
s-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7
c-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z
M834 80h400000v40h-400000z"></path></svg>
    </div>
  </div>
</template>

<script>
import bus from '../bus'

export default {
  name: 'Sqrt',

  data () {
    return {
      elId: '',
      inputId: '',
      input: null,
      inputValue: 0,
      inputInfoList: null
    }
  },
  mounted () {
    bus.currentEl = document.getElementById(this.inputId)
    bus.currentEl.classList.add('active')

    // 每次增删子节点时修改子元素宽度来防止样式变形
    const MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver
    // let observer = new MutationObserver(callback);
    const targetNode = document.getElementById(this.elId) // 获取监听 DOM 对象

    function callback (mutationList) {
      mutationList.forEach((mutation) => {
        switch (mutation.type) {
          case 'childList':
            targetNode.style.paddingLeft = targetNode.offsetHeight * 0.8 + 'px'
            break
        }
      })
    }

    const observerOptions = {
      childList: true, // 观察目标子节点的变化，添加或者删除
      subtree: true // 默认为 false，设置为 true 可以观察后代节点
    }

    this.observer = new MutationObserver(callback)
    this.observer.observe(targetNode, observerOptions)
  },
  computed: {
    text: function () {
      return '\\sqrt{\\smash[b]{' + this.inputValue + '}}'
    }
  },
  beforeMount () {
    this.elId = 'elId' + Math.floor(Math.random() * 10000)
    this.inputId = this.elId + 'input'
    console.log(bus)
    bus.parentCompMap[this.inputId] = this

    bus.childCompMap[this.inputId] = this.inputInfoList = []
  },
  beforeUnmount () {
    bus.parentCompMap[this.inputId] = null

    bus.childCompMap[this.inputId] = null

    this.observer.disconnect()
    this.observer = null
  },
  methods: {
    getKatex () {
      return '\\sqrt' + bus.getKatexStr(this.inputId)
    }
  }
}
</script>
