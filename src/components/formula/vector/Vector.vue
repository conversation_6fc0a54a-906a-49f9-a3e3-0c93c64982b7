<template>
  <div :id="this.elId" style="display:inline-block; padding: 1px 1px 0 1px; position: relative;">
    <div style="width:100%; line-height:0.5em;">→</div>
    <div class="box empty" :id="inputBottomId" style="min-width:1em;min-height: 1.08em; margin-top:1px">
      <div class="item" v-for="(item, Index) in inputBottomInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import bus from '../bus';

export default {
  name: 'Vector',

  data() {
    return {
      elId: '',
      inputBottomId: null,
      inputBottomInfoList: null
    }
  },
  mounted() {
    bus.currentEl = document.getElementById(this.inputBottomId);
    bus.currentEl.classList.add('active')
  },
  computed: {
    text: function() {
      return '\\sqrt{\\smash[b]{' + this.inputValue + '}}';
    }
  },
  beforeMount() {
    this.elId = "elId" + Math.floor(Math.random() * 10000);
    this.inputBottomId = this.elId + 'inputBottom';
    bus.parentCompMap[this.inputBottomId] = this;

    bus.childCompMap[this.inputBottomId] = this.inputBottomInfoList = [];
  },
  beforeUnmount () {
    bus.parentCompMap[this.inputBottomId] = null

    bus.childCompMap[this.inputBottomId] = null
  },
  methods: {
    getKatex() {
      return '\\vec' + bus.getKatexStr(this.inputBottomId)
    }
  }
}
</script>