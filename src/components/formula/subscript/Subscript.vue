<template>
  <div :id="this.elId" style="display:inline-block; position: relative;">
    <div class="box empty" :id="inputLeftId" style="min-width:1em;min-height: 1.2em;line-height:100%;display:inline-block;vertical-align: middle;margin-right:2px">
      <div class="item" v-for="(item, Index) in inputLeftInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
    <div class="box empty" :id="inputRightId" style="min-width:1em;min-height: 1em;display:inline-block;vertical-align: bottom;font-size:0.5em">
      <div class="item" v-for="(item, Index) in inputRightInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import bus from '../bus';

export default {
  name: 'Subscript',

  data () {
    return {
        elId: '',
        inputLeftId: '',
        inputRightId: null,
        inputValue: 0,
        inputLeftInfoList: null,
        inputRightInfoList: null
    }
  },
  mounted() {
    bus.currentEl = document.getElementById(this.inputLeftId);
    bus.currentComp = null;
    bus.currentEl.classList.add('active')

  },
  computed: {
    text: function() {
        return '\\sqrt{\\smash[b]{' + this.inputValue + '}}';
    }
  },
  beforeMount() {
    this.elId = "elId" + Math.floor(Math.random() * 10000);
    this.inputLeftId = this.elId + 'inputLeft';
    this.inputRightId = this.elId + 'inputRight';
    bus.parentCompMap[this.inputLeftId] = this;
    bus.parentCompMap[this.inputRightId] = this;

    bus.childCompMap[this.inputLeftId] = this.inputLeftInfoList = [];
    bus.childCompMap[this.inputRightId] = this.inputRightInfoList = [];
  },
  beforeUnmount () {
    bus.parentCompMap[this.inputLeftId] = null
    bus.parentCompMap[this.inputRightId] = null

    bus.childCompMap[this.inputLeftId] = null
    bus.childCompMap[this.inputRightId] = null
  },
  methods: {
    getKatex() {
      return bus.getKatexStr(this.inputLeftId) + '_' + bus.getKatexStr(this.inputRightId)
    }
  }
}
</script>