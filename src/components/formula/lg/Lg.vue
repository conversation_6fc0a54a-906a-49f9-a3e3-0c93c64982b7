<template>
  <div :id="this.elId" style="display:inline-block;">
    <div style="width:1.5em;min-height: 1.08em; height:100%;display:inline-block;">lg(</div>
    <div :id="inputId" class="box empty" style="min-width:1em;min-height: 1.08em; display:inline-block;">
      <div class="item" v-for="(item, Index) in inputInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
    <div style="width:0.6em;min-height: 1.08em; height:100%;display:inline-block;">)</div>
  </div>
</template>

<script>
import bus from '../bus.vue';

export default {
  name: 'Lg',

  data() {
    return {
        elId: '',
        inputId: null,
        inputInfoList: null
    }
  },
  mounted() {
    bus.currentEl = document.getElementById(this.inputId);
    bus.currentEl.classList.add('active')
  },
  beforeMount() {
    this.elId = "elId" + Math.floor(Math.random() * 10000);
    this.inputId = this.elId + 'input';
    bus.parentCompMap[this.inputId] = this;

    bus.childCompMap[this.inputId] = this.inputInfoList = []
  },
  beforeUnmount () {
    bus.parentCompMap[this.inputId] = null

    bus.childCompMap[this.inputId] = null
  },
  methods: {
    getKatex () {
      return '\\lg(' + bus.getKatexStr(this.inputId) + ')'
    }
  }
}
</script>