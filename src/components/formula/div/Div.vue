<template>
  <div :id="this.elId" style="display:table; padding: 5px 1px 0 15px; position: relative;">
    <div :id="inputLeftId" class="box empty" style="min-width:1em;min-height: 1.08em;line-height:100%;display:table-cell;vertical-align: middle;">
      <div class="item" v-for="(item, Index) in inputLeftInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
    <div style="width:1em; height:100%;display:table-cell;line-height:100%; vertical-align: middle;">/</div>
    <div :id="inputRightId" class="box empty" style="min-width:1em;min-height: 1.08em;display:table-cell;vertical-align: middle;">
      <div class="item" v-for="(item, Index) in inputRightInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
  </div>
</template> 

<script>
import bus from '../bus';

export default {
  name: 'Div',

  data() {
    return {
      elId: '',
      inputTopId: '',
      inputBottomId: null,
      inputValue: 0,
      inputLeftInfoList: null,
      inputRightInfoList: null
    }
  },
  mounted() {
    bus.currentEl = document.getElementById(this.inputLeftId);
    bus.currentComp = null;
    bus.currentEl.classList.add('active')
  },
  beforeMount() {
    this.elId = "elId" + Math.floor(Math.random() * 10000);
    this.inputLeftId = this.elId + 'inputLeft';
    this.inputRightId = this.elId + 'inputRight';
    bus.parentCompMap[this.inputLeftId] = this;
    bus.parentCompMap[this.inputRightId] = this;

    bus.childCompMap[this.inputLeftId] = this.inputLeftInfoList = [];
    bus.childCompMap[this.inputRightId] = this.inputRightInfoList = [];
  },
  beforeUnmount () {
    bus.parentCompMap[this.inputId] = null

    bus.childCompMap[this.inputId] = null
  },
  methods: {
    getKatex () {
      return '\\notin' + bus.getKatexStr(this.inputId)
    }
  }
}
</script>