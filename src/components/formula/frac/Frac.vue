<template>
  <div :id="this.elId" style="display:inline-block; padding: 1px 1px 0 0; position: relative;">
    <div :id="inputTopId" class="box empty" style="min-width:1em;min-height: 1.08em;line-height:100%;margin-bottom:2px">
      <div class="item" v-for="(item, Index) in inputTopInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
    <div style=" border-bottom: 2px solid #333; height:0"></div>
    <div :id="inputBottomId" class="box empty" style="min-width:1em;min-height: 1.08em; margin-top:2px">
      <div class="item" v-for="(item, Index) in inputBottomInfoList" :key="Index">
        <component :is="item.comp" v-if="typeof item == 'object'"></component>
        <span v-else>{{item}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import bus from '../bus';

export default {
  name: 'Frac',

  data () {
    return {
      elId: '',
      inputTopId: '',
      inputBottomId: null,
      inputValue: 0,
      inputTopInfoList: null,
      inputBottomInfoList: null
    }
  },
  mounted() {
    bus.currentEl = document.getElementById(this.inputTopId);
    bus.currentComp = null;
    bus.currentEl.classList.add('active')
  },
  beforeMount () {
    this.elId = "elId" + Math.floor(Math.random() * 10000);
    this.inputTopId = this.elId + 'inputTop';
    this.inputBottomId = this.elId + 'inputBottom';
    bus.parentCompMap[this.inputTopId] = this;
    bus.parentCompMap[this.inputBottomId] = this;

    bus.childCompMap[this.inputTopId] = this.inputTopInfoList = [];
    bus.childCompMap[this.inputBottomId] = this.inputBottomInfoList = [];
  },
  beforeUnmount () {
    bus.parentCompMap[this.inputTopId] = null
    bus.parentCompMap[this.inputBottomId] = null

    bus.childCompMap[this.inputTopId] = null
    bus.childCompMap[this.inputBottomId] = null
  },
  methods: {
    getKatex () {
      return '\\frac' + bus.getKatexStr(this.inputTopId) + bus.getKatexStr(this.inputBottomId)
    }
  }
}
</script>