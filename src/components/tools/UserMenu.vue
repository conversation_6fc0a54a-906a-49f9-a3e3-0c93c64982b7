<template>
  <div class="user-wrapper">
    <div class="header_box" v-if="userName=='18813172573'">
      <span class="user-name"><i class="el-icon-user"></i>{{ userName }}</span>
      <span @click="handleLogout()">
        <a-icon type="logout" />退出登陆</span>
    </div>
    <div class="content-box" v-if="userName!='18813172573'">
      <!-- <a href="https://pro.loacg.com/docs/getting-started" target="_blank">
        <span class="action">
          <a-icon type="question-circle-o"></a-icon>
        </span>
      </a> -->
      <!-- <notice-icon class="action" /> -->
      <span class="down-app">
        <!-- 下载教师APP -->
        <img src="@/assets/index/teacherAPP.png" alt="" />
      </span>
      <!-- 消息提示按钮 -->
      <img class="cur" @click="toNewsTick" src="@/assets/user/news.png" alt="" />

      <!-- <a-icon
        @click="toNewsTick"
        type="bell"
        :style="{ fontSize: '30px', color: '#ddd', verticalAlign: 'middle', cursor: 'pointer' }"
      /> -->
      <span class="logo-num">
        <a-badge v-if="allNotice" status="error" :offset="[-6]" /></span>
      <!-- <a-badge :count="3" title="Custom hover text" :offset="[-5,-15]">
        <a href="#" class="head-example"></a>
      </a-badge> -->
      <!-- 用户名称 -->
      <a-dropdown>
        <span class="action ant-dropdown-link user-dropdown-menu user">
          <a-avatar :src="userPhoto" size="small" icon="user" />
          <span class="user-name">{{ userName }}</span>
        </span>

        <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
          <!-- <a-menu-item key="1">
            <a href="javascript:;" @click="handleChangePwd()">
              <a-icon type="lock" />
              <span>修改密码</span>
            </a>
            <change-pwd-form ref="changePwd"></change-pwd-form>
          </a-menu-item> -->
          <a-menu-divider />
          <a-menu-item key="2">
            <a href="javascript:;">
              <a-icon type="edit" />
              <span @click="toPersonalSettings">个人设置</span>
            </a>
          </a-menu-item>
          <a-menu-item>
            <a href="javascript:;">
              <a-icon type="question-circle" />
              <span @click="toHelpFeedback">帮助&反馈</span>
            </a>
          </a-menu-item>
          <a-menu-item key="3">
            <a href="javascript:;" @click="handleLogout()">
              <a-icon type="logout" />
              <span>退出登录</span>
            </a>
          </a-menu-item>
        </a-menu>
      </a-dropdown>
      <!-- 右侧用户角色选择 -->
      <span class="sel cur">{{ schoolName }}</span>
      <!-- <span @click="toClassManage" class="sel cur">
        班级管理
      </span> -->
      <!-- <a-select v-if="admin" :defaultValue="defaultValue" style="width: 120px" @change="handleChange">
        <a-select-option value="1">校本练习</a-select-option>
        <a-select-option value="0">全区练习</a-select-option>
      </a-select> -->
      <a-select v-if="admin || (this.defaultValueRole && this.roleList.length !== 1)" :defaultValue="defaultValueRole" style="width: 120px" @change="handleChangeRole">
        <a-select-option v-for="item in roleList" :key="item.Id" :value="item.Id">{{ item.RoleName }}</a-select-option>
      </a-select>
    </div>
  </div>
</template>

<script>
// import NoticeIcon from '@/components/NoticeIcon'
// import { mapActions, mapGetters } from 'vuex'
import OperatorCache from '@/utils/cache/OperatorCache'
import TokenCache from '@/utils/cache/TokenCache'
import ChangePwdForm from './ChangePwdForm'
import store from '../../store/index.js'

export default {
  created() {
    this.userId = localStorage.getItem('UserId')
    this.userPhone = localStorage.getItem('mobilePhone')
    this.getTeacherList()
    // 消息通知
    this.getNewsList()
  },
  mounted() {
    localStorage.getItem('UserId')
    this.admin = localStorage.getItem('UserId')
    if (this.admin == 'Admin') {
      this.admin = 1
    } else {
      this.admin = 0
    }
  },
  name: 'UserMenu',
  components: {
    // NoticeIcon
    ChangePwdForm
  },
  data() {
    return {
      admin: '',
      isTeacher: false,
      userPhone: '',
      userName: '',
      schoolName: '',
      defaultValue: '1',
      defaultValueRole: '',
      allNotice: false,
      userPhoto: '',
      // 角色列表
      roleList: [],
      role: true,
      RoleType: ''
    }
  },
  methods: {
    op() {
      return OperatorCache.info
    },
    // ...mapActions(['Logout']),
    // ...mapGetters(['nickname', 'avatar']),
    handleLogout() {
      this.$confirm('确定退出吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        TokenCache.deleteToken()
        OperatorCache.clear()
        location.reload()
        localStorage.removeItem('UserId')
        localStorage.removeItem('passWord')
        localStorage.removeItem('isTeacher')
        localStorage.removeItem('isStudent')
        localStorage.removeItem('isSchoolHeader')
        localStorage.removeItem('IsVip')
        localStorage.removeItem('role')
        this.GetOut()
      })
    },

    GetOut() {
      const Web_token = localStorage.getItem('GetOut_url')
      window.location.href = `https://operator-api.sh-genius.cn/ucenter/auth/logout?client_id=eduwon_client&sign=2dd2bbd834c9282e79b93ebb8cf72670&eden_token=${Web_token}`
    },

    getNewsList() {
      this.$http
        .post('/Information/TeacherInformation/GetTeacherInformationList', { userId: this.userId })
        .then(res => {
          this.allNotice = res.Data.Items.some(item => {
            return item.IsRead === 0
          })
        })
    },
    // 用户信息
    async getTeacherList() {
      await this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.userPhone }).then(res => {
        // console.log(res, 'res')
        console.log('UserMenu.vue')
        this.schoolName = res.Data.SchoolName
        this.userPhoto = res.Data.User.Photo
        this.userName = res.Data.User.RealName
        this.roleList = res.Data.Roles
        this.defaultValueRole = res.Data.Roles[0].Id
        // res.Data.Roles.forEach(item => {})
        if (localStorage.getItem('role') !== '') {
          this.defaultValueRole = localStorage.getItem('role')
        }
        if (this.roleList.length === 1) {
          this.role = false
        }
      })
    },
    handleChangePwd() {
      this.$refs.changePwd.open()
    },
    // 获取用户选择框的值
    handleChange(value) {
      this.$store.dispatch('setRoleTypeData', value)
      this.defaultValue = value
    },
    // 切换角色id
    async handleChangeRole(value) {
      localStorage.removeItem('role')
      localStorage.setItem('role', value)
      await this.getTeacherList()
      this.$router.push({ path: '/Home/Introduce' })
      this.$router.go(0)
    },
    // 跳转个人设置
    toPersonalSettings() {
      this.$router.push({ path: '/Teacher/PersonalSettings' })
    },
    // 跳转帮助反馈
    toHelpFeedback() {
      this.$router.push({ path: '/Teacher/HelpFeedback/HelpFeedbackList' })
    },
    // 跳转消息提示
    toNewsTick() {
      this.$router.push({ path: '/Teacher/NewsTick' })
    },
    // 班级管理
    toClassManage() {
      this.$router.push({ path: '/Teacher/My_Class/StudentManage' })
    }
  }
}
</script>
<style lang="less">
.header_box {
  // span {
  //   font-size: 18px;
  // }
  span:nth-child(1) {
    font-size: 18px;
    margin-right: 100px;
    i {
      margin-right: 5px;
    }
  }
  span:nth-child(2) {
    font-size: 18px;
    margin-right: 40px;
    cursor: pointer;
    i {
      margin-right: 5px;
    }
  }
}
.user-name {
  margin-left: 15px;
}
.sel {
  margin-left: 10px;
  margin-right: 90px;
  padding: 3px 10px;
  // background-color: #68BB97;
  border-radius: 5px;
}
.layout.ant-layout .header .user-wrapper .action,
.layout.ant-layout .top-nav-header-index .user-wrapper .action {
  color: white !important;
}
.down-app {
  display: inline-block;
  position: relative;
  margin-right: 15px;
  cursor: pointer;
  img {
    display: none;
    position: absolute;
  }
}
.down-app:hover img {
  display: block;
}
.logo-num {
  position: relative;
  top: -13px;
}
.ant-btn-primary {
  background-color: #68bb97 !important;
  border: #68bb97 !important;
}
</style>
