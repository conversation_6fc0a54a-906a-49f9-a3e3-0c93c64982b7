<template>
  <div class="user-wrapper">
    <div class="content-box">
      <div
        class="box_item"
        v-for="(item, index) in arr"
        :key="index"
        v-if="item.loginSource.includes(loginSource)"
        @click="selected(item.name,item.indx)"
        :class="{ active: active == item.name }"
        :style="{&quot;background-color&quot;:headerIndex === item.indx ? &quot;#FFFFFF&quot; : &quot;#537566&quot;,&quot;color&quot;:headerIndex === item.indx ? &quot;#537566&quot; : &quot;#FFFFFF&quot;}"
      >
        <span>
          {{ item.name }}
        </span>
      </div>
      <span class="logo-num"> <a-badge v-if="allNotice" status="error" :offset="[-6]" /></span>
      <a-dropdown>
        <!-- <span class="action ant-dropdown-link user-dropdown-menu user">
          <a-avatar :src="userPhoto" size="small" icon="user" />
          <span>{{ userName }}</span>
        </span> -->
        <div class="block">
          <el-avatar size="medium" :src="userPhoto"></el-avatar>
          <span class="User_message">{{ userName }}</span>
        </div>

        <a-menu slot="overlay" class="user-dropdown-menu-wrapper">
          <a-menu-item>
            <a href="javascript:;">
              <a-icon class="pc_font_size16 font_size18 ipad_font_size20" type="heart" />
              <span class="pc_font_size16 font_size16 ipad_font_size18" @click="toStudentMyConcern">我的关注</span>
            </a>
          </a-menu-item>
          <a-menu-item>
            <a href="javascript:;">
              <a-icon class="pc_font_size16 font_size18 ipad_font_size20" type="tag" />
              <span class="pc_font_size16 font_size16 ipad_font_size18" @click="toStudentMyCollection">我的收藏</span>
            </a>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="2">
            <a href="javascript:;">
              <a-icon class="pc_font_size16 font_size18 ipad_font_size20" type="edit" />
              <span class="pc_font_size16 font_size16 ipad_font_size18" @click="toStudentSettings">个人设置</span>
            </a>
          </a-menu-item>
          <a-menu-item>
            <a href="javascript:;">
              <a-icon class="pc_font_size16 font_size18 ipad_font_size20" type="question-circle" />
              <span class="pc_font_size16 font_size16 ipad_font_size18" @click="toHelpFeedback">帮助&反馈</span>
            </a>
          </a-menu-item>
          <!-- <a-menu-item key="3">
            <a href="javascript:;" @click="handleLogout()">
              <a-icon type="logout" />
              <span>退出登录</span>
            </a>
          </a-menu-item> -->
        </a-menu>
      </a-dropdown>

      <!-- 右侧用户角色选择 -->
      <span class="user-select">
        {{ schoolName }}
        <span
          @click="handleLogout()"
          style="margin-left: 15px; cursor: pointer"
          v-if="canLoginOut"
        ><i class="el-icon-switch-button"></i>退出</span
        >
        <span @click="handleLogout()" style="margin-left: 15px; cursor: pointer" v-else>返回</span>
      </span>
    </div>
  </div>
</template>

<script>
import OperatorCache from '@/utils/cache/OperatorCache'
import TokenCache from '@/utils/cache/TokenCache'
import ChangePwdForm from './ChangePwdForm'
import { mapState } from 'vuex'
import {Session} from "@/utils/storage"

export default {
  computed: {
    ...mapState(['headerIndex']),
    loginSource() {
      return localStorage.getItem('LoginSource')
    }
  },
  created () {
    this.getTeacherList()
    this.userId = localStorage.getItem('UserId')
    this.userPhone = localStorage.getItem('mobilePhone')
    this.active = localStorage.getItem('ActiveName')
    this.schoolName = localStorage.getItem('schoolName')
    this.userPhoto = localStorage.getItem('userPhoto')
    this.userName = localStorage.getItem('userName')
    // localStorage.setItem('schoolName', res.Data.User.schoolName)
    // localStorage.setItem('userPhoto', res.Data.User.userPhoto)
    // localStorage.setItem('userName', res.Data.User.userName)
    // 消息通知
    this.getNewsList()
    this.Init()
  },
  name: 'UserMenu',
  components: {
    // NoticeIcon
    ChangePwdForm
  },
  data () {
    return {
      userPhone: '',
      userName: '',
      schoolName: '',
      allNotice: '',
      userPhoto: '',
      arr: [
        { indx: 1, name: '练习中心', loginSource: ['1', '2'] },
        { indx: 2, name: '我的错题', loginSource: ['1', '2'] },
        { indx: 3, name: '名师微课', loginSource: ['1'] },
        { indx: 4, name: '智能分析', loginSource: ['1'] }],
      active: '练习中心',
      canLoginOut: false,
      // headerIndex: 0
    }
  },
  watch: {
    // active(val) {
    //   if (val !== null) {
    //     this.active = localStorage.getItem('ActiveName')
    //   } else {
    //     this.active = '首页'
    //   }
    // }
  },
  methods: {
    Init () {
      const domain = document.domain
      this.canLoginOut = !(domain == 'stu.eduwon.cn')
    },
    selected (name, index) {
      this.active = name
      this.$store.commit('setHeaderIndex', index)
      localStorage.setItem('ActiveName', this.active)
      switch (name) {
        // case '首页':
        //   this.$router.push({ path: '/Home/Introduce' })
        //   break
        case '练习中心':
          this.$router.push({ path: '/Home/Introduce' })
          break
        case '我的错题':
          this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
          break
        case '名师微课':
          this.$router.push({ path: '/Student/Fine_micro/BoutiqueMicro' })
          break
        case '智能分析':
          this.$router.push({ path: '/Student/studentPersonalAnalysis' })
          break
      }
    },
    op () {
      return OperatorCache.info
    },
    handleLogout () {
      this.$confirm(`确定${this.canLoginOut ? '退出' : '返回'}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        TokenCache.deleteToken()
        OperatorCache.clear()
        // if (this.canLoginOut) {
        //   location.reload()
        // } else {
        //   location.href = 'https://sz-api.ai-study.net/home.html'
        // }
        // 三个助手使用
        if(localStorage.getItem('LoginSource') == 2){
          this.PushStudentVerb()
        }
        if(localStorage.getItem('isXH') == 1){
          window.location.href = 'https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308&identity=2'
        }else if(localStorage.getItem('LoginSource') == 1){
          location.reload()
        } else {
          // this.goOut()
          window.location.href = 'https://sz-api.ai-study.net/index.html'
        }
        localStorage.removeItem('ActiveName')
        localStorage.removeItem('UserId')
        localStorage.removeItem('passWord')
        localStorage.removeItem('IsVip')
        localStorage.removeItem('LoginSource')
        localStorage.removeItem('isXH')
        sessionStorage.clear()
        // localStorage.setItem('ActiveName', this.LoginActive)
      })
    },
    // 三个助手退出登陆需要调用接口
    async PushStudentVerb () {
      await this.$uwonhttp.post('/XAPI/XAPI/PushStudentVerb', { studentId: localStorage.getItem('UserId'), type: '1' })
    },
    getNewsList () {
      this.$http
        .post('/Information/TeacherInformation/GetTeacherInformationList', { userId: this.userId })
        .then((res) => {
          this.allNotice = res.Data.Items.some((item) => {
            return item.Type === 0
          })
        })
    },
    // 用户信息
    getTeacherList () {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.userId }).then((res) => {
        // console.log('StMenu.vue')
        this.schoolName = res.Data.SchoolName
        this.userPhoto = res.Data.User.Photo
        this.userName = res.Data.User.RealName
        localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
        localStorage.setItem('StClassId', res.Data.ClassId)
        Session.set('userInfo', res.Data)
      })
    },
    handleChangePwd () {
      this.$refs.changePwd.open()
    },
    // 获取用户选择框的值
    handleChange (value) {},
    // 首页
    toIndex () {
      this.$router.push({ path: '/Home/Introduce' })
    },
    // 作业中心
    toTaskCenter () {
      this.$router.push({ path: '/Student/TaskCenter' })
    },
    // 我的错题
    toMyWrong () {
      this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
    },
    // 精品微课
    toMicro () {
      this.$router.push({ path: '/Student/Fine_micro/BoutiqueMicro' })
    },
    // 线上辅导
    toOnlineAna () {
      this.$router.push({ path: '/Student/OnlineCoach' })
    },
    // 学情分析
    toStudentAna () {
      this.$router.push({ path: '/Student/studentPersonalAnalysis' })
    },
    // 跳转我的关注
    toStudentMyConcern () {
      this.$router.push({ path: '/Student/List/StudentMyConcern' })
    },
    // 我的收藏
    toStudentMyCollection () {
      this.$router.push({ path: '/Student/List/StudentMyCollection' })
    },
    // 跳转个人设置
    toStudentSettings () {
      this.$router.push({ path: '/Student/List/StudentSetting' })
    },
    // 跳转帮助反馈
    toHelpFeedback () {
      this.$router.push({ path: '/Student/List/HelpFeedbackList' })
    },
    // 跳转消息提示
    toNewsTick () {
      this.$router.push({ path: '/Student/List/newTick' })
    }
  }
}
</script>
<style lang="less" scoped>
.m-r {
  margin-right: 80px;
}
.z-f-s {
  font-size: 18px;
}
.user-select {
  font-size: 18px;
  margin-right: 90px;
}
.layout.ant-layout .header .user-wrapper .action,
.layout.ant-layout .top-nav-header-index .user-wrapper .action {
  color: white !important;
}
.content-box {
  display: flex;
  height: 0.333333rem;
  .box_item {
    width: 110px;
    margin-right: 50px;
    text-align: center;
    cursor: pointer;
    span {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
  }
  // .active {
  //   background-color: #ffff;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #537566;
  // }
}
.down-app {
  display: inline-block;
  position: relative;
  margin-right: 15px;
  cursor: pointer;
  img {
    display: none;
    position: absolute;
  }
}
.down-app:hover img {
  display: block;
}
.logo-num {
  position: relative;
  top: -13px;
}
.ant-btn-primary {
  background-color: #68bb97 !important;
  border: #68bb97 !important;
}
.User_message {
  font-size: 18px;
  margin-left: 10px;
  vertical-align: middle;
}
.block {
  margin-right: 18px;
  /deep/.el-avatar {
    vertical-align: middle;
  }
}
</style>
