import MultiTab from '@/components/MultiTab'
import IconSelector from '@/components/IconSelector'
import ExceptionPage from '@/components/Exception'
import BasicDialog from '@/components/basic/basic-dialog'

export {
  MultiTab,
  ExceptionPage,
  IconSelector,
  BasicDialog
}

// const files = require.context("./basic/", false, /\.vue$/)

// class Components {
//   install(Vue) {
//     files.keys().forEach(item => {
//       const module = files(item).default || files(item)
//       Vue.component(module.name, module)
//     })
//   }
// }

// export default new Components()
