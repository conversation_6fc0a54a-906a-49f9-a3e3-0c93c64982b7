<template>
  <span @click="startMeet" class="messageTitle">修改会议信息
    <a-modal v-model="modalVisib" title="修改会议信息" :closable="false" :width="800">
      <a-input placeholder="填写会议主题" v-model="mes.title" style="border-radius:100px;background-color:#f5f5f5;"></a-input>
      <div class="line startTime">
        <span class="condTitle">开始时间</span>
        <span class="right">
          <a-date-picker show-time format="YYYY-MM-DD HH:mm:ss" @change="startTimes">
          </a-date-picker>
        </span>
      </div>
      <div class="line endTime">
        <span class="condTitle">结束时间</span>
        <span class="right">
          <a-date-picker show-time format="YYYY-MM-DD HH:mm:ss" @change="endTimes">
          </a-date-picker>
        </span>
      </div>
      <div class="line place">
        <span class="condTitle">地点</span>
        <span class="right">
          <a-input v-model="mes.place"></a-input>
        </span>
      </div>
      <div class="line toPeople">
        <span class="condTitle">参与人</span>
        <span class="right">
          <span class="joinPeople" @click="showTeacher">{{ isShow?"收起":"展开" }}</span>
        </span>
      </div>
      <div class="line toPeople">
        <span class="condTitle"></span>
        <span class="right">
          <span class="allTeacherD" v-show="isShow">
            <div class="new-class" v-for="(item,index) in teacherList" :key="index">
              <div class="popup-col">{{ item.GradeName }}</div>
              <div class="new-infor">
                <span v-for="(m,ind) in item.FriendList" :key="ind" class="teachercheckbox">
                  <span class="ckeckboxSpan">
                    <input type="checkbox" :value="m.UserId" name="checkbox">
                  </span>
                  <span class="teacherName"> {{ m.RealName }} </span> &nbsp;&nbsp;&nbsp;
                </span>
              </div>
              <!-- <br> -->
              <!-- <br> -->
            </div>
            <div class="teacherEnter">
              <!-- style="text-align:center" -->
              <span class="bottomSpan" @click="reSet">重置</span>
              <span class="bottomSpan" @click="showTeacher">确定</span>
            </div>
          </span>
        </span>
      </div>
      <div class="line reMarkes" style="height:100px;line-height:100px">
        <span class="condTitle">备注</span>
        <span class="right">
          <a-input v-model="mes.remark"></a-input>
        </span>
      </div>
      <div class="line submitThing">
        <span class="condTitle">会议提交物</span>
        <a-radio-group v-model="mes.submitThing" @change="onChange">
          <a-radio :value="0">
            不需要
          </a-radio>
          <a-radio :value="1">
            需要
          </a-radio>
        </a-radio-group>
      </div>
      <template #footer>
        <a-button key="back" @click="handleCancel">
          取消
        </a-button>
        <a-button key="submit" type="primary" @click="submit">
          发起会议
        </a-button>
      </template>
    </a-modal>
  </span>
</template>

<script>
export default {
  name: 'Modal',
  data() {
    return {
      msg: {
        log: '日志'
      },
      modalVisib: false,
      mes: {
        title: '',
        remark: '',
        place: '',
        starttime: '',
        endtime: '',
        teachlist: [],
        submitThing: 0
      },
      teacherList: '',
      isShow: false
      // AllteacherList:'',
    }
  },
  props: ['warnReason', 'meetid'],
  created() {
    this.userId = localStorage.getItem('UserId')
    this.getTeacherList()
  },
  methods: {
    submit() {
      this.mes.teachlist = []
      var checkboxs = document.getElementsByName('checkbox')
      for (var i = 0; i < checkboxs.length; i++) {
        if (checkboxs[i].checked) {
          this.mes.teachlist.push(checkboxs[i].value)
        }
      }
      var teachers = this.mes.teachlist.join(',')
      this.modalVisib = false
      this.$emit('func', this.mes)
      this.$http
        .post('/Meeting/TeacherMeeting/UpdateTeacherMeeting', {
          Id: this.meetid,
          Title: this.mes.title,
          BeginTime: this.mes.starttime,
          EndTime: this.mes.endtime,
          Address: this.mes.place,
          TeacherIds: teachers,
          Remarks: this.mes.remark,
          IsFile: this.mes.submitThing,
          Reason: this.warnReason,
          CreatorId: this.userId
        })
        .then(res => {
          this.$router.go(0)
        })
    },
    changeVal(e) {
      this.mes.teachlist.push(e.target.value)
    },
    reSet() {
      var checkboxs = document.getElementsByName('checkbox')
      for (var i = 0; i < checkboxs.length; i++) {
        if (checkboxs[i].checked) {
          checkboxs[i].checked = false
        }
      }
    },
    showTeacher() {
      var checkboxs = document.getElementsByName('checkbox')
      for (var i = 0; i < checkboxs.length; i++) {
        if (checkboxs[i].checked) {
        }
      }
      this.isShow == false ? (this.isShow = true) : (this.isShow = false)
    },
    startMeet() {
      this.modalVisib = true
    },
    handleCancel() {
      this.mes.teachlist = []
      var checkboxs = document.getElementsByName('checkbox')
      for (var i = 0; i < checkboxs.length; i++) {
        if (checkboxs[i].checked) {
          this.mes.teachlist.push(checkboxs[i].value)
          checkboxs[i].checked = false
        }
      }
      this.modalVisib = false
    },
    // 开始时间
    startTimes(data, time) {
      this.mes.starttime = time
    },

    /// 结束时间
    endTimes(data, time) {
      this.mes.endtime = time
    },
    // 是否有提交物
    onChange(e) {},
    getTeacherList() {
      this.$http
        .post('/Friend/FriendRelationShip/GetTeacherListByShool', {
          userId: this.userId
        })
        .then(res => {
          var Data = res.Data.Items[0].GradeList
          this.teacherList = Data

          //     const list=[]
          //     for(var i=0;i<Data.length;i++){
          //         for(var a=0;a<Data[i].FriendList.length;a++){
          //             list.push(Data[i].FriendList[a])
          //         }

          //     }
          //    this.AllteacherList=list
        })
    }
  }
}
</script>

<style lang="less" scoped>
.submitSerach {
  border: 1px solid;
  height: 100%;
  width: 100%;
}
// 邀请会议人
.new-class {
  margin-bottom: 10px;
  // background-color: #ccc;
  // border-radius: 5px;
  .popup-col {
    background-color: rgba(101, 188, 156);
    text-indent: 15px;
  }
  .new-infor {
    padding: 0 15px;
    background-color: #e6e1e1;
  }
}
.line {
  position: relative;
  height: 40px;
  margin: 10px;
  line-height: 40px;
  // border: 1px solid;
  width: 100%;
}
.condTitle {
  position: relative;
  width: 100px;
  height: 100%;
  display: inline-block;
  text-align: center;
}
/deep/ .startTime {
  .ant-calendar-picker-input {
    width: 100%;
    border-radius: 0px;
  }
}
/deep/ .endTime {
  .ant-calendar-picker-input {
    width: 100%;
    border-radius: 0px;
  }
}
/deep/ .reMarkes {
  .ant-input {
    height: 100%;
    width: 86%;
    border-radius: 0px;
  }
}
/deep/ .place {
  .ant-input {
    height: 100%;
    width: 86%;
    border-radius: 0px;
  }
}
.toPeople {
  position: relative;
}
/deep/ .toPeople {
  .ant-select {
    height: 100%;
  }
  .ant-select-selection {
    height: 100%;
    width: 100%;
    border-radius: 0px;
  }
}
.joinPeople {
  vertical-align: middle;
  display: inline-block;
  position: relative;
  width: 86%;
  height: 40px;
  border: 1px solid #d9d9d9;
  cursor: pointer;
  text-align: center;
}
.allTeacherD {
  overflow: auto;
  height: 350px;
  padding: 20px;
  vertical-align: middle;
  display: inline-block;
  position: relative;
  width: 86%;
  cursor: pointer;
  z-index: 2;
  background-color: white;
}
input[type='checkbox'] {
  vertical-align: -5%;
  // width: 20px;
  // height: 20px;
  appearance: checkbox;
  position: relative;
}
.teacherName {
  position: relative;
  height: 20px;
  line-height: 20px;
  vertical-align: -5%;
  width: 11%;
  display: inline-block;
}
#ShowOrHide {
  top: 30px;
  position: absolute;
  z-index: 10;
  background-color: white;
}
.bottomSpan {
  width: 100px;
  height: 40px;
  line-height: 40px;
  display: inline-block;
  color: white;
  text-align: center;
  border-radius: 5px;
  position: relative;
}
.bottomSpan:nth-of-type(1) {
  background-color: #d6d6d6;
}
.bottomSpan:nth-of-type(2) {
  background-color: #68bb97;
  float: right;
}
.right {
  display: inline-block;
  width: 80%;
  height: 100%;
  // border: 1px solid;
}
.messageTitle {
  color: white;
}
</style>
