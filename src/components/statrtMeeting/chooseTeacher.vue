<template>
    <div id="app">
        <div id="tacher1">
            <div id="schoolName" @click="showGrade">
                {{Schoolnames}} <span style="float:right;"><a-icon v-show="turnState==1" type="down" /><a-icon v-show="turnState==2" type="up" /></span>
            </div>
            <div v-if="turnState==2">
                <div class="grade" v-for="(m,indexs) in allMessage" :key="indexs">
                    <span>{{m.GradeName}}</span>
                    <div class="teacherName" v-for="(l) in m.FriendList" :key="l.FriendsId" @click="isCheckedName(l.FriendsId)">
                        <span class="nameDetail">{{l.RealName}}</span>
                        <span :key="l.FriendsId" style="float:right;" :ref="l.FriendsId"><a-icon type="check-circle" /></span>
                    </div>
                </div>
            </div>
        </div>
        <div id="tacher2">
            <p>t2</p>
        </div>
    </div>
</template>

<script>
export default {
    data(){
        return{
            Schoolnames:'',
            allMessage:'',
            options:'',
            checkedOptions:'',
            turnState:1,
        }
    },
    created(){
        this.getAllTeacher()
    },
    methods:{
          getAllTeacher(){
            this.$http.post('/Friend/FriendRelationShip/GetTeacherListByShool', {
                userId:74214
            }).then(res=>{
                var Data=res.Data.Items[0]
                this.Schoolnames=Data.SchoolName
                this.allMessage=Data.GradeList
            })
        },
        showGrade(){    
            this.turnState==1?this.turnState=2:this.turnState=1;
        },
        isCheckedName(index){
            var teachid=index
            alert(teachid)
        }
    },
}
</script>

<style lang="less" scoped>
#app{
    position: relative;
}
#tacher1{
    display: inline-block;
    vertical-align: middle;
    width: 50%;
    height: 100%;
}
#tacher2{
    display: inline-block;
    vertical-align: middle;
    width: 50%; 
    height: 100%;
}
#schoolName{
    width: 100%;
    height: 30px;
    border: 1px solid;
    line-height: 30px;
}
.grade{
    position: relative;
    margin-left: 20px;
    border: 1px solid black;
}
.teacherName{
    position: relative;
    width: 80%;
    height: 30px;
    border: 1px solid;
    line-height: 30px;
    margin-left: 20px;
}
.nameDetail{
    height: 100%;
    border: 1px solid;
}
</style>