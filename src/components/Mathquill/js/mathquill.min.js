/**
 * MathQuill v0.10.1, by <PERSON>, <PERSON><PERSON>, and <PERSON>
 * http://mathquill.com | <EMAIL>
 *
 * This Source Code Form is subject to the terms of the
 * Mozilla Public License, v. 2.0. If a copy of the MPL
 * was not distributed with this file, You can obtain
 * one at http://mozilla.org/MPL/2.0/.
 */
!function(){function t(){}function e(t){var e=t.length-1;return function(){var n=g.call(arguments,0,e),i=g.call(arguments,e);return t.apply(this,n.concat([i]))}}function n(t){return e(function(e,n){"function"!=typeof e&&(e=b(e));var i=function(t){return e.apply(t,[t].concat(n))};return t.call(this,i)})}function i(t){var e=g.call(arguments,1);return function(){return t.apply(this,e)}}function s(t,e){if(!e)throw new Error("prayer failed: "+t)}function r(t){s("a direction was passed",t===w||t===q)}function o(t,e,n){s("a parent is always present",t),s("leftward is properly set up",function(){return e?e[q]===n&&e.parent===t:t.ends[w]===n}()),s("rightward is properly set up",function(){return n?n[w]===e&&n.parent===t:t.ends[q]===e}())}function a(){window.console&&console.warn('You are using the MathQuill API without specifying an interface version, which will fail in v1.0.0. Easiest fix is to do the following before doing anything else:\n\n    MathQuill = MathQuill.getInterface(1);\n    // now MathQuill.MathField() works like it used to\n\nSee also the "`dev` branch (2014–2015) → v0.10.0 Migration Guide" at\n  https://github.com/mathquill/mathquill/wiki/%60dev%60-branch-(2014%E2%80%932015)-%E2%86%92-v0.10.0-Migration-Guide')}function l(t){return a(),Lt(t)}function c(e){function n(t){var e,n;return t&&t.nodeType?(e=x(t).children(".mq-root-block").attr(zt),n=e&&O.byId[e].controller,n?s[n.KIND_OF_MQ](n):null):null}function i(t,e){var n,i,r;e&&e.handlers&&(e.handlers={fns:e.handlers,APIClasses:s});for(n in e)e.hasOwnProperty(n)&&(i=e[n],r=L[n],t[n]=r?r(i):i)}var s,r,o;if(!(R<=e&&e<=z))throw"Only interface versions between "+R+" and "+z+" supported. You specified: "+e;s={},n.L=w,n.R=q,n.saneKeyboardEvents=F,n.config=function(t){return i(E.p,t),this},n.registerEmbed=function(t,e){if(!/^[a-z][a-z0-9]*$/i.test(t))throw"Embed name must start with letter and be only letters and digits";I[t]=e},r=s.AbstractMathQuill=v(A,function(t){t.init=function(t){this.__controller=t,this.__options=t.options,this.id=t.id,this.data=t.data},t.__mathquillify=function(t){var e,n=this.__controller,i=n.root,s=n.container;n.createTextarea(),e=s.addClass(t).contents().detach(),i.jQ=x('<span class="mq-root-block"/>').attr(zt,i.id).appendTo(s),this.latex(e.text()),this.revert=function(){return s.empty().unbind(".mathquill").removeClass("mq-editable-field mq-math-mode mq-text-mode").append(e)}},t.config=function(t){return i(this.__options,t),this},t.el=function(){return this.__controller.container[0]},t.text=function(){return this.__controller.exportText()},t.latex=function(t){return t=S(t),arguments.length>0?(this.__controller.renderLatexMath(t),this.__controller.blurred&&this.__controller.cursor.hide().parent.blur(),this):(console.log(this.__controller),this.__controller.exportLatex())},t.html=function(){return this.__controller.root.jQ.html().replace(/ mathquill-(?:command|block)-id="?\d+"?/g,"").replace(/<span class="?mq-cursor( mq-blink)?"?>.?<\/span>/i,"").replace(/ mq-hasCursor|mq-hasCursor ?/,"").replace(/ class=(""|(?= |>))/g,"")},t.reflow=function(){return this.__controller.root.postOrder("reflow"),this}}),n.prototype=r.prototype,s.EditableField=v(r,function(e,n){e.__mathquillify=function(){return n.__mathquillify.apply(this,arguments),this.__controller.editable=!0,this.__controller.delegateMouseEvents(),this.__controller.editablesTextareaEvents(),this},e.focus=function(){return this.__controller.textarea.focus(),this},e.blur=function(){return this.__controller.textarea.blur(),this},e.write=function(t){return this.__controller.writeLatex(t),this.__controller.scrollHoriz(),this.__controller.blurred&&this.__controller.cursor.hide().parent.blur(),this},e.empty=function(){var t=this.__controller.root,e=this.__controller.cursor;return t.eachChild("postOrder","dispose"),t.ends[w]=t.ends[q]=0,t.jQ.empty(),delete e.selection,e.insAtRightEnd(t),this},e.cmd=function(t){var e,n=this.__controller.notify(),i=n.cursor;return/^\\[a-z]+$/i.test(t)&&!i.isTooDeep()?(t=t.slice(1),(e=T[t])&&(t=e(t),i.selection&&t.replaces(i.replaceSelection()),t.createLeftOf(i.show()),this.__controller.scrollHoriz())):i.parent.write(i,t),n.blurred&&i.hide().parent.blur(),this},e.select=function(){var t=this.__controller;for(t.notify("move").cursor.insAtRightEnd(t.root);t.cursor[w];)t.selectLeft();return this},e.clearSelection=function(){return this.__controller.cursor.clearSelection(),this},e.moveToDirEnd=function(t){return this.__controller.notify("move").cursor.insAtDirEnd(t,this.__controller.root),this},e.moveToLeftEnd=function(){return this.moveToDirEnd(w)},e.moveToRightEnd=function(){return this.moveToDirEnd(q)},e.keystroke=function(e){var n;for(e=e.replace(/^\s+|\s+$/g,"").split(/\s+/),n=0;n<e.length;n+=1)this.__controller.keystroke(e[n],{preventDefault:t});return this},e.typedText=function(t){for(var e=0;e<t.length;e+=1)this.__controller.typedText(t.charAt(e));return this},e.dropEmbedded=function(t,e,n){var i,s=t-x(window).scrollLeft(),r=e-x(window).scrollTop(),o=document.elementFromPoint(s,r);this.__controller.seek(x(o),t,e),i=Et().setOptions(n),i.createLeftOf(this.__controller.cursor)},e.clickAt=function(t,e,n){n=n||document.elementFromPoint(t,e);var i=this.__controller,s=i.root;return It.contains(s.jQ[0],n)||(n=s.jQ[0]),i.seek(x(n),t+pageXOffset,e+pageYOffset),i.blurred&&this.focus(),this},e.ignoreNextMousedown=function(t){return this.__controller.cursor.options.ignoreNextMousedown=t,this}}),n.EditableField=function(){throw"wtf don't call me, I'm 'abstract'"},n.EditableField.prototype=s.EditableField.prototype;for(o in D)!function(t,i){var r=s[t]=i(s);n[t]=function(i,s){var o,a=n(i);return a instanceof r||!i||!i.nodeType?a:(o=C(r.RootBlock(),x(i),E()),o.KIND_OF_MQ=t,r(o).__mathquillify(s,e))},n[t].prototype=r.prototype}(o,D[o]);return n}function u(t){var e,n="moveOutOf deleteOutOf selectOutOf upOutOf downOutOf".split(" ");for(e=0;e<n.length;e+=1)!function(e){t[e]=function(t){this.controller.handle(e,t)}}(n[e]);t.reflow=function(){this.controller.handle("reflow"),this.controller.handle("edited"),this.controller.handle("edit")}}function h(t,e,n){return v(K,{ctrlSeq:t,htmlTemplate:"<"+e+" "+n+">&0</"+e+">"})}function f(t){var e=this.parent,n=t;do{if(n[q])return t.insLeftOf(e);n=n.parent.parent}while(n!==e);t.insRightOf(e)}function p(t,e){t.jQadd=function(){e.jQadd.apply(this,arguments),this.delimjQs=this.jQ.children(":first").add(this.jQ.children(":last")),this.contentjQ=this.jQ.children(":eq(1)")},t.reflow=function(){var t=this.contentjQ.outerHeight()/parseFloat(this.contentjQ.css("fontSize"));dt(this.delimjQs,Mt(1+.2*(t-1),1.2),1.2*t)}}function d(t,e){var e=e||t,n=St[t],s=St[e];Q[t]=i(Ct,w,t,n,e,s),Q[n]=i(Ct,q,t,n,e,s)}var m,g,b,v,w,q,x,y,O,k,T,Q,j,_,C,S,D,E,L,A,I,R,z,M,B,F,N,$,P,W,U,H,V,G,K,Z,Y,X,J,tt,et,nt,it,st,rt,ot,at,lt,ct,ut,ht,ft,pt,dt,mt,gt,bt,vt,wt,qt,xt,yt,Ot,kt,Tt,Qt,jt,_t,Ct,St,Dt,Et,Lt,At,It=window.jQuery,Rt="mathquill-command-id",zt="mathquill-block-id",Mt=Math.min,Bt=Math.max;if(!It)throw"MathQuill requires jQuery 1.5.2+ to be loaded first";g=[].slice,b=e(function(t,n){return e(function(e,i){if(t in e)return e[t].apply(e,n.concat(i))})}),v=function(t,e,n){function i(t){return"object"==typeof t}function s(t){return"function"==typeof t}function r(){}return function t(n,o){function a(){var t=new l;return s(t.init)&&t.init.apply(t,arguments),t}function l(){}var c,u,h;return void 0===o&&(o=n,n=Object),a.Bare=l,c=r.prototype=n.prototype,u=l.prototype=a.prototype=a.p=new r,u.constructor=a,a.extend=function(e){return t(a,e)},(a.open=function(t){if(h={},s(t)?h=t.call(a,u,c,a,n):i(t)&&(h=t),i(h))for(var r in h)e.call(h,r)&&(u[r]=h[r]);return s(u.init)||(u.init=n),a})(o)}}(0,{}.hasOwnProperty),w=-1,q=1,x=v(It,function(t){t.insDirOf=function(t,e){return t===w?this.insertBefore(e.first()):this.insertAfter(e.last())},t.insAtDirEnd=function(t,e){return t===w?this.prependTo(e):this.appendTo(e)}}),y=v(function(t){t.parent=0,t[w]=0,t[q]=0,t.init=function(t,e,n){this.parent=t,this[w]=e,this[q]=n},this.copy=function(t){return y(t.parent,t[w],t[q])}}),O=v(function(t){function e(){return i+=1}t[w]=0,t[q]=0,t.parent=0;var i=0;this.byId={},t.init=function(){this.id=e(),O.byId[this.id]=this,this.ends={},this.ends[w]=0,this.ends[q]=0},t.dispose=function(){delete O.byId[this.id]},t.toString=function(){return"{{ MathQuill Node #"+this.id+" }}"},t.jQ=x(),t.jQadd=function(t){return this.jQ=this.jQ.add(t)},t.jQize=function(t){function e(t){var n,i;for(t.getAttribute&&(n=t.getAttribute("mathquill-command-id"),i=t.getAttribute("mathquill-block-id"),n&&O.byId[n].jQadd(t),i&&O.byId[i].jQadd(t)),t=t.firstChild;t;t=t.nextSibling)e(t)}var n;for(t=x(t||this.html()),n=0;n<t.length;n+=1)e(t[n]);return t},t.createDir=function(t,e){r(t);var n=this;return n.jQize(),n.jQ.insDirOf(t,e.jQ),e[t]=n.adopt(e.parent,e[w],e[q]),n},t.createLeftOf=function(t){return this.createDir(w,t)},t.selectChildren=function(t,e){return _(t,e)},t.bubble=n(function(t){var e;for(e=this;e&&!1!==t(e);e=e.parent);return this}),t.postOrder=n(function(t){return function e(n){n.eachChild(e),t(n)}(this),this}),t.isEmpty=function(){return 0===this.ends[w]&&0===this.ends[q]},t.isStyleBlock=function(){return!1},t.children=function(){return k(this.ends[w],this.ends[q])},t.eachChild=function(){var t=this.children();return t.each.apply(t,arguments),this},t.foldChildren=function(t,e){return this.children().fold(t,e)},t.withDirAdopt=function(t,e,n,i){return k(this,this).withDirAdopt(t,e,n,i),this},t.adopt=function(t,e,n){return k(this,this).adopt(t,e,n),this},t.disown=function(){return k(this,this).disown(),this},t.remove=function(){return this.jQ.remove(),this.postOrder("dispose"),this.disown()}}),k=v(function(t){t.init=function(t,e,n){if(n===m&&(n=w),r(n),s("no half-empty fragments",!t==!e),this.ends={},t){s("withDir is passed to Fragment",t instanceof O),s("oppDir is passed to Fragment",e instanceof O),s("withDir and oppDir have the same parent",t.parent===e.parent),this.ends[n]=t,this.ends[-n]=e;var i=this.fold([],function(t,e){return t.push.apply(t,e.jQ.get()),t});this.jQ=this.jQ.add(i)}},t.jQ=x(),t.withDirAdopt=function(t,e,n,i){return t===w?this.adopt(e,n,i):this.adopt(e,i,n)},t.adopt=function(t,e,n){var i,s,r;return o(t,e,n),i=this,i.disowned=!1,(s=i.ends[w])?(r=i.ends[q],e||(t.ends[w]=s),n?n[w]=r:t.ends[q]=r,i.ends[q][q]=n,i.each(function(n){n[w]=e,n.parent=t,e&&(e[q]=n),e=n}),i):this},t.disown=function(){var t,e,n=this,i=n.ends[w];return!i||n.disowned?n:(n.disowned=!0,t=n.ends[q],e=i.parent,o(e,i[w],i),o(e,t,t[q]),i[w]?i[w][q]=t[q]:e.ends[w]=t[q],t[q]?t[q][w]=i[w]:e.ends[q]=i[w],n)},t.remove=function(){return this.jQ.remove(),this.each("postOrder","dispose"),this.disown()},t.each=n(function(t){var e=this,n=e.ends[w];if(!n)return e;for(;n!==e.ends[q][q]&&!1!==t(n);n=n[q]);return e}),t.fold=function(t,e){return this.each(function(n){t=e.call(this,t,n)}),t}}),T={},Q={},j=v(y,function(t){t.init=function(t,e){this.parent=t,this.options=e;var n=this.jQ=this._jQ=x('<span class="mq-cursor">&#8203;</span>');this.blink=function(){n.toggleClass("mq-blink")},this.upDownCache={}},t.show=function(){return this.jQ=this._jQ.removeClass("mq-blink"),"intervalId"in this?clearInterval(this.intervalId):(this[q]?this.selection&&this.selection.ends[w][w]===this[w]?this.jQ.insertBefore(this.selection.jQ):this.jQ.insertBefore(this[q].jQ.first()):this.jQ.appendTo(this.parent.jQ),this.parent.focus()),this.intervalId=setInterval(this.blink,500),this},t.hide=function(){return"intervalId"in this&&clearInterval(this.intervalId),delete this.intervalId,this.jQ.detach(),this.jQ=x(),this},t.withDirInsertAt=function(t,e,n,i){var s=this.parent;this.parent=e,this[t]=n,this[-t]=i,s!==e&&s.blur&&s.blur(this)},t.insDirOf=function(t,e){return r(t),this.jQ.insDirOf(t,e.jQ),this.withDirInsertAt(t,e.parent,e[t],e),this.parent.jQ.addClass("mq-hasCursor"),this},t.insLeftOf=function(t){return this.insDirOf(w,t)},t.insRightOf=function(t){return this.insDirOf(q,t)},t.insAtDirEnd=function(t,e){return r(t),this.jQ.insAtDirEnd(t,e.jQ),this.withDirInsertAt(t,e,0,e.ends[t]),e.focus(),this},t.insAtLeftEnd=function(t){return this.insAtDirEnd(w,t)},t.insAtRightEnd=function(t){return this.insAtDirEnd(q,t)},t.jumpUpDown=function(t,e){var n,i,s=this;s.upDownCache[t.id]=y.copy(s),n=s.upDownCache[e.id],n?n[q]?s.insLeftOf(n[q]):s.insAtRightEnd(n.parent):(i=s.offset().left,e.seek(i,s))},t.offset=function(){var t=this,e=t.jQ.removeClass("mq-cursor").offset();return t.jQ.addClass("mq-cursor"),e},t.unwrapGramp=function(){var t=this.parent.parent,e=t.parent,n=t[q],i=this,s=t[w];if(t.disown().eachChild(function(i){i.isEmpty()||(i.children().adopt(e,s,n).each(function(e){e.jQ.insertBefore(t.jQ.first())}),s=i.ends[q])}),!this[q])if(this[w])this[q]=this[w][q];else for(;!this[q];){if(this.parent=this.parent[q],!this.parent){this[q]=t[q],this.parent=e;break}this[q]=this.parent.ends[w]}this[q]?this.insLeftOf(this[q]):this.insAtRightEnd(e),t.jQ.remove(),t[w].siblingDeleted&&t[w].siblingDeleted(i.options,q),t[q].siblingDeleted&&t[q].siblingDeleted(i.options,w)},t.startSelection=function(){var t,e=this.anticursor=y.copy(this),n=e.ancestors={};for(t=e;t.parent;t=t.parent)n[t.parent.id]=t},t.endSelection=function(){delete this.anticursor},t.select=function(){var t,e,n,i,r,o,a,l=this.anticursor;if(this[w]===l[w]&&this.parent===l.parent)return!1;for(t=this;t.parent;t=t.parent)if(t.parent.id in l.ancestors){e=t.parent;break}if(s("cursor and anticursor in the same tree",e),n=l.ancestors[e.id],o=q,t[w]!==n)for(a=t;a;a=a[q])if(a[q]===n[q]){o=w,i=t,r=n;break}return o===q&&(i=n,r=t),i instanceof y&&(i=i[q]),r instanceof y&&(r=r[w]),this.hide().selection=e.selectChildren(i,r),this.insDirOf(o,this.selection.ends[o]),this.selectionChanged(),!0},t.clearSelection=function(){return this.selection&&(this.selection.clear(),delete this.selection,this.selectionChanged()),this},t.deleteSelection=function(){this.selection&&(this[w]=this.selection.ends[w][w],this[q]=this.selection.ends[q][q],this.selection.remove(),this.selectionChanged(),delete this.selection)},t.replaceSelection=function(){var t=this.selection;return t&&(this[w]=t.ends[w][w],this[q]=t.ends[q][q],delete this.selection),t},t.depth=function(){for(var t=this,e=0;t=t.parent;)e+=t instanceof V?1:0;return e},t.isTooDeep=function(t){if(this.options.maxDepth!==m)return this.depth()+(t||0)>this.options.maxDepth}}),_=v(k,function(t,e){t.init=function(){e.init.apply(this,arguments),this.jQ=this.jQ.wrapAll('<span class="mq-selection"></span>').parent()},t.adopt=function(){return this.jQ.replaceWith(this.jQ=this.jQ.children()),e.adopt.apply(this,arguments)},t.clear=function(){return this.jQ.replaceWith(this.jQ[0].childNodes),this},t.join=function(t){return this.fold("",function(e,n){return e+n[t]()})}}),C=v(function(t){t.init=function(t,e,n){this.id=t.id,this.data={},this.root=t,this.container=e,this.options=n,t.controller=this,this.cursor=t.cursor=j(t,n)},t.handle=function(t,e){var n,i=this.options.handlers;i&&i.fns[t]&&(n=i.APIClasses[this.KIND_OF_MQ](this),e===w||e===q?i.fns[t](e,n):i.fns[t](n))};var e=[];this.onNotify=function(t){e.push(t)},t.notify=function(){for(var t=0;t<e.length;t+=1)e[t].apply(this.cursor,arguments);return this}}),S=function(t){const e=t||"",n=/\\begin\{matrix\}(.*?)\\end\{matrix\}/gi,i=/(\\log|\\ln|\\lg)\((.*?)\)/gi;return e.replace(n,function(t){const e=/\\begin\{matrix\}(.*?)\\end\{matrix\}/i,n=t.match(e)[1].split("\\\\");return"\\lines{"+n[0]+"}{"+n[1]+"}"}).replace(i,function(t){return t.replace("(","{").replace(")","}").replace("\\l","\\cl")})},D={},E=v(),L={},A=v(),I={},l.prototype=A.p,l.VERSION="v0.10.1",l.interfaceVersion=function(t){if(1!==t)throw"Only interface version 1 supported. You specified: "+t;return a=function(){window.console&&console.warn('You called MathQuill.interfaceVersion(1); to specify the interface version, which will fail in v1.0.0. You can fix this easily by doing this before doing anything else:\n\n    MathQuill = MathQuill.getInterface(1);\n    // now MathQuill.MathField() works like it used to\n\nSee also the "`dev` branch (2014–2015) → v0.10.0 Migration Guide" at\n  https://github.com/mathquill/mathquill/wiki/%60dev%60-branch-(2014%E2%80%932015)-%E2%86%92-v0.10.0-Migration-Guide')},a(),l},l.getInterface=c,R=c.MIN=1,z=c.MAX=2,l.noConflict=function(){return window.MathQuill=M,l},M=window.MathQuill,window.MathQuill=l,B=v(function(t,e,n){function i(t,e){throw t=t?"'"+t+"'":"EOF","Parse Error: "+e+" at "+t}var r,o,a;t.init=function(t){this._=t},t.parse=function(t){function e(t,e){return e}return this.skip(a)._(""+t,e,i)},t.or=function(t){s("or is passed a parser",t instanceof n);var e=this;return n(function(n,i,s){function r(e){return t._(n,i,s)}return e._(n,i,r)})},t.then=function(t){var e=this;return n(function(i,r,o){function a(e,i){var a=t instanceof n?t:t(i);return s("a parser is returned",a instanceof n),a._(e,r,o)}return e._(i,a,o)})},t.many=function(){var t=this;return n(function(e,n,i){function s(t,n){return e=t,o.push(n),!0}function r(){return!1}for(var o=[];t._(e,s,r););return n(e,o)})},t.times=function(t,e){arguments.length<2&&(e=t);var i=this;return n(function(n,s,r){function o(t,e){return h.push(e),n=t,!0}function a(t,e){return c=e,n=t,!1}function l(t,e){return!1}var c,u,h=[],f=!0;for(u=0;u<t;u+=1)if(!(f=i._(n,o,a)))return r(n,c);for(;u<e&&f;u+=1)f=i._(n,o,l);return s(n,h)})},t.result=function(t){return this.then(o(t))},t.atMost=function(t){return this.times(0,t)},t.atLeast=function(t){var e=this;return e.times(t).then(function(t){return e.many().map(function(e){return t.concat(e)})})},t.map=function(t){return this.then(function(e){return o(t(e))})},t.skip=function(t){return this.then(function(e){return t.result(e)})},this.string=function(t){var e=t.length,i="expected '"+t+"'";return n(function(n,s,r){var o=n.slice(0,e);return o===t?s(n.slice(e),o):r(n,i)})},r=this.regex=function(t){s("regexp parser is anchored","^"===t.toString().charAt(1));var e="expected "+t;return n(function(n,i,s){var r,o=t.exec(n);return o?(r=o[0],i(n.slice(r.length),r)):s(n,e)})},o=n.succeed=function(t){return n(function(e,n){return n(e,t)})},n.fail=function(t){return n(function(e,n,i){return i(e,t)})},n.letter=r(/^[a-z]/i),n.letters=r(/^[a-z]*/i),n.digit=r(/^[0-9]/),n.digits=r(/^[0-9]*/),n.whitespace=r(/^\s+/),n.optWhitespace=r(/^\s*/),n.any=n(function(t,e,n){return t?e(t.slice(1),t.charAt(0)):n(t,"expected any character")}),n.all=n(function(t,e,n){return e("",t)}),a=n.eof=n(function(t,e,n){return t?n(t,"expected EOF"):e(t,t)})}),F=function(){function e(t){var e,i=t.which||t.keyCode,s=n[i],r=[];return t.ctrlKey&&r.push("Ctrl"),t.originalEvent&&t.originalEvent.metaKey&&r.push("Meta"),t.altKey&&r.push("Alt"),t.shiftKey&&r.push("Shift"),e=s||String.fromCharCode(i),r.length||s?(r.push(e),r.join("-")):e}var n={8:"Backspace",9:"Tab",10:"Enter",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Esc",32:"Spacebar",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",45:"Insert",46:"Del",144:"NumLock"};return function(n,i){function s(t){y=t,clearTimeout(g),g=setTimeout(t)}function r(e){s(function(n){y=t,clearTimeout(g),e(n)})}function o(e){y(),y=t,clearTimeout(g),q.val(e),e&&q[0].select&&q[0].select(),b=!!e}function a(){var t=q[0];return"selectionStart"in t&&t.selectionStart!==t.selectionEnd}function l(){i.keystroke(e(v),v)}function c(t){t.target===q[0]&&(v=t,w=null,b&&r(function(t){t&&"focusout"===t.type||!q[0].select||q[0].select()}),l())}function u(t){t.target===q[0]&&(v&&w&&l(),w=t,s(f))}function h(t){t.target===q[0]&&v&&!w&&s(f)}function f(){if(!a()){var t=q.val();1===t.length?(q.val(""),i.typedText(t)):t&&q[0].select&&q[0].select()}}function p(){v=w=null}function d(t){t.target===q[0]&&(document.activeElement!==q[0]&&q.focus(),s(m))}function m(){var t=q.val();q.val(""),t&&i.paste(t)}var g,b,v=null,w=null,q=It(n),x=It(i.container||q),y=t;return x.bind("keydown keypress input keyup focusout paste",function(t){y(t)}),b=!1,x.bind({keydown:c,keypress:u,keyup:h,focusout:p,cut:function(){r(function(){i.cut()})},copy:function(){r(function(){i.copy()})},paste:d}),{select:o}}}(),C.open(function(t,e){t.exportText=function(){return this.root.foldChildren("",function(t,e){return t+e.text()})}}),C.open(function(t){t.focusBlurEvents=function(){function t(){clearTimeout(n),r.selection&&r.selection.jQ.addClass("mq-blur"),e()}function e(){r.hide().parent.blur(),i.container.removeClass("mq-focused"),x(window).unbind("blur",t)}var n,i=this,s=i.root,r=i.cursor;i.textarea.focus(function(){i.blurred=!1,clearTimeout(n),i.container.addClass("mq-focused"),r.parent||r.insAtRightEnd(s),r.selection?(r.selection.jQ.removeClass("mq-blur"),i.selectionChanged()):r.show()}).blur(function(){i.blurred=!0,n=setTimeout(function(){s.postOrder("intentionalBlur"),r.clearSelection().endSelection(),e()}),x(window).bind("blur",t)}),i.blurred=!0,r.hide().parent.blur()},t.unbindFocusBlurEvents=function(){this.textarea.unbind("focus blur")}}),C.open(function(t){t.keystroke=function(t,e){this.cursor.parent.keystroke(t,e,this)}}),O.open(function(t){t.keystroke=function(t,e,n){var i=n.cursor;switch(t){case"Ctrl-Shift-Backspace":case"Ctrl-Backspace":n.ctrlDeleteDir(w);break;case"Shift-Backspace":case"Backspace":n.backspace();break;case"Esc":case"Tab":return void n.escapeDir(q,t,e);case"Shift-Tab":case"Shift-Esc":return void n.escapeDir(w,t,e);case"End":n.notify("move").cursor.insAtRightEnd(i.parent);break;case"Ctrl-End":n.notify("move").cursor.insAtRightEnd(n.root);break;case"Shift-End":for(;i[q];)n.selectRight();break;case"Ctrl-Shift-End":for(;i[q]||i.parent!==n.root;)n.selectRight();break;case"Home":n.notify("move").cursor.insAtLeftEnd(i.parent);break;case"Ctrl-Home":n.notify("move").cursor.insAtLeftEnd(n.root);break;case"Shift-Home":for(;i[w];)n.selectLeft();break;case"Ctrl-Shift-Home":for(;i[w]||i.parent!==n.root;)n.selectLeft();break;case"Left":n.moveLeft();break;case"Shift-Left":n.selectLeft();break;case"Ctrl-Left":break;case"Right":n.moveRight();break;case"Shift-Right":n.selectRight();break;case"Ctrl-Right":break;case"Up":n.moveUp();break;case"Down":n.moveDown();break;case"Shift-Up":if(i[w])for(;i[w];)n.selectLeft();else n.selectLeft();case"Shift-Down":if(i[q])for(;i[q];)n.selectRight();else n.selectRight();case"Ctrl-Up":case"Ctrl-Down":break;case"Ctrl-Shift-Del":case"Ctrl-Del":n.ctrlDeleteDir(q);break;case"Shift-Del":case"Del":n.deleteForward();break;case"Meta-A":case"Ctrl-A":for(n.notify("move").cursor.insAtRightEnd(n.root);i[w];)n.selectLeft();break;default:return}e.preventDefault(),n.scrollHoriz()},t.moveOutOf=t.moveTowards=t.deleteOutOf=t.deleteTowards=t.unselectInto=t.selectOutOf=t.selectTowards=function(){s("overridden or never called on this node")}}),C.open(function(t){function e(t,e){var n=t.notify("upDown").cursor,i=e+"Into",s=e+"OutOf";return n[q][i]?n.insAtLeftEnd(n[q][i]):n[w][i]?n.insAtRightEnd(n[w][i]):n.parent.bubble(function(t){var e=t[s];if(e&&("function"==typeof e&&(e=t[s](n)),e instanceof O&&n.jumpUpDown(t,e),!0!==e))return!1}),t}this.onNotify(function(t){"move"!==t&&"upDown"!==t||this.show().clearSelection()}),t.escapeDir=function(t,e,n){r(t);var i=this.cursor;if(i.parent!==this.root&&n.preventDefault(),i.parent!==this.root)return i.parent.moveOutOf(t,i),this.notify("move")},L.leftRightIntoCmdGoes=function(t){if(t&&"up"!==t&&"down"!==t)throw'"up" or "down" required for leftRightIntoCmdGoes option, got "'+t+'"';return t},t.moveDir=function(t){r(t);var e=this.cursor,n=e.options.leftRightIntoCmdGoes;return e.selection?e.insDirOf(t,e.selection.ends[t]):e[t]?e[t].moveTowards(t,e,n):e.parent.moveOutOf(t,e,n),this.notify("move")},t.moveLeft=function(){return this.moveDir(w)},t.moveRight=function(){return this.moveDir(q)},t.moveUp=function(){return e(this,"up")},t.moveDown=function(){return e(this,"down")},this.onNotify(function(t){"upDown"!==t&&(this.upDownCache={})}),this.onNotify(function(t){"edit"===t&&this.show().deleteSelection()}),t.deleteDir=function(t){var e,n;return r(t),e=this.cursor,n=e.selection,this.notify("edit"),n||(e[t]?e[t].deleteTowards(t,e):e.parent.deleteOutOf(t,e)),e[w].siblingDeleted&&e[w].siblingDeleted(e.options,q),e[q].siblingDeleted&&e[q].siblingDeleted(e.options,w),e.parent.bubble("reflow"),this},t.ctrlDeleteDir=function(t){r(t);var e=this.cursor;return!e[t]||e.selection?this.deleteDir(t):(this.notify("edit"),t===w?k(e.parent.ends[w],e[w]).remove():k(e[q],e.parent.ends[q]).remove(),e.insAtDirEnd(t,e.parent),e[w].siblingDeleted&&e[w].siblingDeleted(e.options,q),e[q].siblingDeleted&&e[q].siblingDeleted(e.options,w),e.parent.bubble("reflow"),this)},t.backspace=function(){return this.deleteDir(w)},t.deleteForward=function(){return this.deleteDir(q)},this.onNotify(function(t){"select"!==t&&this.endSelection()}),t.selectDir=function(t){var e,n=this.notify("select").cursor,i=n.selection;r(t),n.anticursor||n.startSelection(),e=n[t],e?i&&i.ends[t]===e&&n.anticursor[-t]!==e?e.unselectInto(t,n):e.selectTowards(t,n):n.parent.selectOutOf(t,n),n.clearSelection(),n.select()||n.show()},t.selectLeft=function(){return this.selectDir(w)},t.selectRight=function(){return this.selectDir(q)}}),N=function(){function t(t){var e=V();return t.adopt(e,0,0),e}function e(t){var e,n=t[0]||V();for(e=1;e<t.length;e+=1)t[e].children().adopt(n,n.ends[q],0);return n}var n=B.string,i=B.regex,s=B.letter,r=B.any,o=B.optWhitespace,a=B.succeed,l=B.fail,c=s.map(function(t){return et(t)}),u=i(/^[^${}\\_^]/).map(function(t){return U(t)}),h=i(/^[^\\a-eg-zA-Z]/).or(n("\\").then(i(/^[a-z]+/i).or(i(/^\s+/).result(" ")).or(r))).then(function(t){var e=T[t];return e?e(t).parser():l("unknown command: \\"+t)}),f=h.or(c).or(u),p=n("{").then(function(){return m}).skip(n("}")),d=o.then(p.or(f.map(t))),m=d.many().map(e).skip(o),g=n("[").then(d.then(function(t){return"]"!==t.join("latex")?a(t):l()}).many().map(e).skip(o)).skip(n("]")),b=m;return b.block=d,b.optBlock=g,b}(),C.open(function(t,e){t.exportLatex=function(){return this.root.latex().replace(/(\\[a-z]+) (?![a-z])/gi,"$1")},L.maxDepth=function(t){return"number"==typeof t?t:m},t.writeLatex=function(t){var e=this.notify("edit").cursor;return e.parent.writeLatex(e,t),this},t.renderLatexMath=function(t){var e,n=this.root,i=this.cursor,s=(i.options,n.jQ),r=B.all,o=B.eof,a=N.skip(o).or(r.result(!1)).parse(t);n.eachChild("postOrder","dispose"),n.ends[w]=n.ends[q]=0,a&&a.prepareInsertionAt(i)?(a.children().adopt(n,0,0),e=a.join("html"),s.html(e),n.jQize(s.children()),n.finalizeInsert(i.options)):s.empty(),delete i.selection,i.insAtRightEnd(n)},t.renderLatexText=function(t){var e,n,i,s,r,o,a,l,c,u,h=this.root,f=this.cursor;if(h.jQ.children().slice(1).remove(),h.eachChild("postOrder","dispose"),h.ends[w]=h.ends[q]=0,delete f.selection,f.show().insAtRightEnd(h),e=B.regex,n=B.string,i=B.eof,s=B.all,r=n("$").then(N).skip(n("$").or(i)).map(function(t){var e,n=Y(f);return n.createBlocks(),e=n.ends[w],t.children().adopt(e,0,0),n}),o=n("\\$").result("$"),a=o.or(e(/^[^$]/)).map(U),l=r.or(a).many(),c=l.skip(i).or(s.result(!1)).parse(t)){for(u=0;u<c.length;u+=1)c[u].adopt(h,h.ends[q],0);h.jQize().appendTo(h.jQ),h.finalizeInsert(f.options)}}}),C.open(function(e){E.p.ignoreNextMousedown=t,e.delegateMouseEvents=function(){var e=this.root.jQ;this.container.bind("mousedown.mathquill",function(n){function i(t){o=x(t.target)}function s(t){u.anticursor||u.startSelection(),c.seek(o,t.pageX,t.pageY).cursor.select(),o=m}function r(t){u.blink=h,u.selection||(c.editable?u.show():f.detach()),a.unbind("mousemove",i),x(t.target.ownerDocument).unbind("mousemove",s).unbind("mouseup",r)}var o,a=x(n.target).closest(".mq-root-block"),l=O.byId[a.attr(zt)||e.attr(zt)],c=l.controller,u=c.cursor,h=u.blink,f=c.textareaSpan,p=c.textarea;n.preventDefault(),n.target.unselectable=!0,u.options.ignoreNextMousedown(n)||(u.options.ignoreNextMousedown=t,c.blurred&&(c.editable||a.prepend(f),p.focus()),u.blink=t,c.seek(x(n.target),n.pageX,n.pageY).cursor.startSelection(),a.mousemove(i),x(n.target.ownerDocument).mousemove(s).mouseup(r))})}}),C.open(function(t){t.seek=function(t,e,n){var i,r,o,a=this.notify("select").cursor;return t&&((i=t.attr(zt)||t.attr(Rt))||(r=t.parent(),i=r.attr(zt)||r.attr(Rt))),o=i?O.byId[i]:this.root,s("nodeId is the id of some Node that exists",o),a.clearSelection().show(),o.seek(e,a),this.scrollHoriz(),this}}),C.open(function(t){t.scrollHoriz=function(){var t,e,n,i,s,r=this.cursor,o=r.selection,a=this.root.jQ[0].getBoundingClientRect();if(o)if(n=o.jQ[0].getBoundingClientRect(),i=n.left-(a.left+20),s=n.right-(a.right-20),o.ends[w]===r[q])if(i<0)e=i;else{if(!(s>0))return;e=n.left-s<a.left+20?i:s}else if(s>0)e=s;else{if(!(i<0))return;e=n.right-i>a.right-20?s:i}else if((t=r.jQ[0].getBoundingClientRect().left)>a.right-20)e=t-(a.right-20);else{if(!(t<a.left+20))return;e=t-(a.left+20)}this.root.jQ.stop().animate({scrollLeft:"+="+e},100)}}),C.open(function(t){E.p.substituteTextarea=function(){return x("<textarea autocapitalize=off autocomplete=off autocorrect=off spellcheck=false x-palm-disable-ste-all=true />")[0]},t.createTextarea=function(){var t,e=this.textareaSpan=x('<span class="mq-textarea"></span>'),n=this.options.substituteTextarea();if(!n.nodeType)throw"substituteTextarea() must return a DOM element, got "+n;n=this.textarea=x(n).appendTo(e),t=this,t.cursor.selectionChanged=function(){t.selectionChanged()}},t.selectionChanged=function(){var t=this;mt(t.container[0]),t.textareaSelectionTimeout===m&&(t.textareaSelectionTimeout=setTimeout(function(){t.setTextareaSelection()}))},t.setTextareaSelection=function(){this.textareaSelectionTimeout=m;var t="";this.cursor.selection&&(t=this.cursor.selection.join("latex"),this.options.statelessClipboard&&(t="$"+t+"$")),this.selectFn(t)},t.staticMathTextareaEvents=function(){function t(){s.detach(),e.blurred=!0}var e=this,n=(e.root,e.cursor),i=e.textarea,s=e.textareaSpan;this.container.prepend(It('<span class="mq-selectable">').text("$"+e.exportLatex()+"$")),e.blurred=!0,i.bind("cut paste",!1).bind("copy",function(){e.setTextareaSelection()}).focus(function(){e.blurred=!1}).blur(function(){n.selection&&n.selection.clear(),setTimeout(t)}),e.selectFn=function(t){i.val(t),t&&i.select()}},E.p.substituteKeyboardEvents=F,t.editablesTextareaEvents=function(){var t=this,e=t.textarea,n=t.textareaSpan,i=this.options.substituteKeyboardEvents(e,this);this.selectFn=function(t){i.select(t)},this.container.prepend(n),this.focusBlurEvents()},t.unbindEditablesEvents=function(){var t=this,e=t.textarea,n=t.textareaSpan;this.selectFn=function(t){e.val(t),t&&e.select()},n.remove(),this.unbindFocusBlurEvents(),t.blurred=!0,e.bind("cut paste",!1)},t.typedText=function(t){if("\n"===t)return this.handle("enter");var e=this.notify().cursor;e.parent.write(e,t),this.scrollHoriz()},t.cut=function(){var t=this,e=t.cursor;e.selection&&setTimeout(function(){t.notify("edit"),e.parent.bubble("reflow")})},t.copy=function(){this.setTextareaSelection()},t.paste=function(t){this.options.statelessClipboard&&(t="$"===t.slice(0,1)&&"$"===t.slice(-1)?t.slice(1,-1):"\\text{"+t+"}"),this.writeLatex(t).cursor.show()}}),$=v(O,function(t,e){t.finalizeInsert=function(t,e){var n=this;n.postOrder("finalizeTree",t),n.postOrder("contactWeld",e),n.postOrder("blur"),n.postOrder("reflow"),n[q].siblingCreated&&n[q].siblingCreated(t,w),n[w].siblingCreated&&n[w].siblingCreated(t,q),n.bubble("reflow")},t.prepareInsertionAt=function(t){var e,n=t.options.maxDepth;if(n!==m){if((e=t.depth())>n)return!1;this.removeNodesDeeperThan(n-e)}return!0},t.removeNodesDeeperThan=function(t){for(var e,n=0,i=[[this,n]];i.length;)e=i.shift(),e[0].children().each(function(s){var r=s instanceof V?1:0;n=e[1]+r,n<=t?i.push([s,n]):(r?s.children():s).remove()})}}),P=v($,function(t,e){t.init=function(t,n,i){var s=this;e.init.call(s),s.ctrlSeq||(s.ctrlSeq=t),n&&(s.htmlTemplate=n),i&&(s.textTemplate=i)},t.replaces=function(t){t.disown(),this.replacedFragment=t},t.isEmpty=function(){return this.foldChildren(!0,function(t,e){return t&&e.isEmpty()})},t.parser=function(){var t=N.block,e=this;return t.times(e.numBlocks()).map(function(t){
e.blocks=t;for(var n=0;n<t.length;n+=1)t[n].adopt(e,e.ends[q],0);return e})},t.createLeftOf=function(t){var n=this,i=n.replacedFragment;n.createBlocks(),e.createLeftOf.call(n,t),i&&(i.adopt(n.ends[w],0,0),i.jQ.appendTo(n.ends[w].jQ),n.placeCursor(t),n.prepareInsertionAt(t)),n.finalizeInsert(t.options),n.placeCursor(t)},t.createBlocks=function(){var t,e,n=this,i=n.numBlocks(),s=n.blocks=Array(i);for(t=0;t<i;t+=1)e=s[t]=V(),e.adopt(n,n.ends[q],0)},t.placeCursor=function(t){t.insAtRightEnd(this.foldChildren(this.ends[w],function(t,e){return t.isEmpty()?t:e}))},t.moveTowards=function(t,e,n){var i=n&&this[n+"Into"];e.insAtDirEnd(-t,i||this.ends[-t])},t.deleteTowards=function(t,e){this.isEmpty()?e[t]=this.remove()[t]:this.moveTowards(t,e,null)},t.selectTowards=function(t,e){e[-t]=this,e[t]=this[t]},t.selectChildren=function(){return _(this,this)},t.unselectInto=function(t,e){e.insAtDirEnd(-t,e.anticursor.ancestors[this.id])},t.seek=function(t,e){function n(t){var e={};return e[w]=t.jQ.offset().left,e[q]=e[w]+t.jQ.outerWidth(),e}var i,s=this,r=n(s);return t<r[w]?e.insLeftOf(s):t>r[q]?e.insRightOf(s):(i=r[w],void s.eachChild(function(o){var a=n(o);return t<a[w]?(t-i<a[w]-t?o[w]?e.insAtRightEnd(o[w]):e.insLeftOf(s):e.insAtLeftEnd(o),!1):t>a[q]?void(o[q]?i=a[q]:r[q]-t<t-a[q]?e.insRightOf(s):e.insAtRightEnd(o)):(o.seek(t,e),!1)}))},t.numBlocks=function(){var t=this.htmlTemplate.match(/&\d+/g);return t?t.length:0},t.html=function(){var t,e,n,i=this,r=i.blocks,o=" mathquill-command-id="+i.id,a=i.htmlTemplate.match(/<[^<>]+>|[^<>]+/g);for(s("no unmatched angle brackets",a.join("")===this.htmlTemplate),t=0,e=a[0];e;t+=1,e=a[t])if("/>"===e.slice(-2))a[t]=e.slice(0,-2)+o+"/>";else if("<"===e.charAt(0)){s("not an unmatched top-level close tag","/"!==e.charAt(1)),a[t]=e.slice(0,-1)+o+">",n=1;do{t+=1,e=a[t],s("no missing close tags",e),"</"===e.slice(0,2)?n-=1:"<"===e.charAt(0)&&"/>"!==e.slice(-2)&&(n+=1)}while(n>0)}return a.join("").replace(/>&(\d+)/g,function(t,e){return" mathquill-block-id="+r[e].id+">"+r[e].join("html")})},t.latex=function(){return this.foldChildren(this.ctrlSeq,function(t,e){return t+"{"+(e.latex()||" ")+"}"})},t.textTemplate=[""],t.text=function(){var t=this,e=0;return t.foldChildren(t.textTemplate[e],function(n,i){e+=1;var s=i.text();return n&&"("===t.textTemplate[e]&&"("===s[0]&&")"===s.slice(-1)?n+s.slice(1,-1)+t.textTemplate[e]:n+s+(t.textTemplate[e]||"")})}}),W=v(P,function(e,n){e.init=function(t,e,i){i||(i=t&&t.length>1?t.slice(1):t),n.init.call(this,t,e,[i])},e.parser=function(){return B.succeed(this)},e.numBlocks=function(){return 0},e.replaces=function(t){t.remove()},e.createBlocks=t,e.moveTowards=function(t,e){e.jQ.insDirOf(t,this.jQ),e[-t]=this,e[t]=this[t]},e.deleteTowards=function(t,e){e[t]=this.remove()[t]},e.seek=function(t,e){t-this.jQ.offset().left<this.jQ.outerWidth()/2?e.insLeftOf(this):e.insRightOf(this)},e.latex=function(){return this.ctrlSeq},e.text=function(){return this.textTemplate},e.placeCursor=t,e.isEmpty=function(){return!0}}),U=v(W,function(t,e){t.init=function(t,n){e.init.call(this,t,"<span>"+(n||t)+"</span>")}}),H=v(W,function(t,e){t.init=function(t,n,i){e.init.call(this,t,'<span class="mq-binary-operator">'+n+"</span>",i)}}),V=v($,function(t,e){t.join=function(t){return this.foldChildren("",function(e,n){return e+n[t]()})},t.html=function(){return this.join("html")},t.latex=function(){return this.join("latex")},t.text=function(){return this.ends[w]===this.ends[q]&&0!==this.ends[w]?this.ends[w].text():this.join("text")},t.keystroke=function(t,n,i){return!i.options.spaceBehavesLikeTab||"Spacebar"!==t&&"Shift-Spacebar"!==t?e.keystroke.apply(this,arguments):(n.preventDefault(),void i.escapeDir("Shift-Spacebar"===t?w:q,t,n))},t.moveOutOf=function(t,e,n){n&&this.parent[n+"Into"]||!this[t]?e.insDirOf(t,this.parent):e.insAtDirEnd(-t,this[t])},t.selectOutOf=function(t,e){e.insDirOf(t,this.parent)},t.deleteOutOf=function(t,e){e.unwrapGramp()},t.seek=function(t,e){var n=this.ends[q];if(!n||n.jQ.offset().left+n.jQ.outerWidth()<t)return e.insAtRightEnd(this);if(t<this.ends[w].jQ.offset().left)return e.insAtLeftEnd(this);for(;t<n.jQ.offset().left;)n=n[w];return n.seek(t,e)},t.chToCmd=function(t,e){var n;return t.match(/^[a-eg-zA-Z]$/)?et(t):/^\d$/.test(t)?J(t):e&&e.typingSlashWritesDivisionSymbol&&"/"===t?T["÷"](t):e&&e.typingAsteriskWritesTimesSymbol&&"*"===t?T["×"](t):(n=Q[t]||T[t])?n(t):U(t)},t.write=function(t,e){var n=this.chToCmd(e,t.options);t.selection&&n.replaces(t.replaceSelection()),t.isTooDeep()||n.createLeftOf(t.show())},t.writeLatex=function(t,e){var n,i=B.all,s=B.eof,r=N.skip(s).or(i.result(!1)).parse(e);r&&!r.isEmpty()&&r.prepareInsertionAt(t)&&(r.children().adopt(t.parent,t[w],t[q]),n=r.jQize(),n.insertBefore(t.jQ),t[w]=r.ends[q],r.finalizeInsert(t.options,t),r.ends[q][q].siblingCreated&&r.ends[q][q].siblingCreated(t.options,w),r.ends[w][w].siblingCreated&&r.ends[w][w].siblingCreated(t.options,q),t.parent.bubble("reflow"))},t.focus=function(){return this.jQ.addClass("mq-hasCursor"),this.jQ.removeClass("mq-empty"),this},t.blur=function(){return this.jQ.removeClass("mq-hasCursor"),this.isEmpty()&&this.jQ.addClass("mq-empty"),this}}),E.p.mouseEvents=!0,D.StaticMath=function(t){return v(t.AbstractMathQuill,function(e,n){this.RootBlock=V,e.__mathquillify=function(t,e){return this.config(t),n.__mathquillify.call(this,"mq-math-mode"),this.__options.mouseEvents&&(this.__controller.delegateMouseEvents(),this.__controller.staticMathTextareaEvents()),this},e.init=function(){n.init.apply(this,arguments),this.__controller.root.postOrder("registerInnerField",this.innerFields=[],t.InnerMathField)},e.latex=function(){var e=n.latex.apply(this,arguments);return arguments.length>0&&this.__controller.root.postOrder("registerInnerField",this.innerFields=[],t.InnerMathField),e}})},G=v(V,u),D.MathField=function(e){return v(e.EditableField,function(e,n){this.RootBlock=G,e.__mathquillify=function(e,i){return this.config(e),i>1&&(this.__controller.root.reflow=t),n.__mathquillify.call(this,"mq-editable-field mq-math-mode"),delete this.__controller.root.reflow,this}})},D.InnerMathField=function(t){return v(t.MathField,function(t,e){t.makeStatic=function(){this.__controller.editable=!1,this.__controller.root.blur(),this.__controller.unbindEditablesEvents(),this.__controller.container.removeClass("mq-editable-field")},t.makeEditable=function(){this.__controller.editable=!0,this.__controller.editablesTextareaEvents(),this.__controller.cursor.insAtRightEnd(this.__controller.root),this.__controller.container.addClass("mq-editable-field")}})},K=v(O,function(t,e){function n(t){var e,n;if(t.jQ[0].normalize(),e=t.jQ[0].firstChild)return s("only node in TextBlock span is Text node",3===e.nodeType),n=Z(e.data),n.jQadd(e),t.children().disown(),n.adopt(t,0,0)}t.ctrlSeq="\\text",t.replaces=function(t){t instanceof k?this.replacedText=t.remove().jQ.text():"string"==typeof t&&(this.replacedText=t)},t.jQadd=function(t){e.jQadd.call(this,t),this.ends[w]&&this.ends[w].jQadd(this.jQ[0].firstChild)},t.createLeftOf=function(t){var n,i=this;if(e.createLeftOf.call(this,t),t.insAtRightEnd(i),i.replacedText)for(n=0;n<i.replacedText.length;n+=1)i.write(t,i.replacedText.charAt(n));i[q].siblingCreated&&i[q].siblingCreated(t.options,w),i[w].siblingCreated&&i[w].siblingCreated(t.options,q),i.bubble("reflow")},t.parser=function(){var t=this,e=B.string,n=B.regex;return B.optWhitespace.then(e("{")).then(n(/^[^}]*/)).skip(e("}")).map(function(e){return 0===e.length?k():(Z(e).adopt(t,0,0),t)})},t.textContents=function(){return this.foldChildren("",function(t,e){return t+e.text})},t.text=function(){return'"'+this.textContents()+'"'},t.latex=function(){var t=this.textContents();return 0===t.length?"":"\\text{"+t.replace(/\\/g,"\\backslash ").replace(/[{}]/g,"\\$&")+"}"},t.html=function(){return'<span class="mq-text-mode" mathquill-command-id='+this.id+">"+this.textContents()+"</span>"},t.moveTowards=function(t,e){e.insAtDirEnd(-t,this)},t.moveOutOf=function(t,e){e.insDirOf(t,this)},t.unselectInto=t.moveTowards,t.selectTowards=P.prototype.selectTowards,t.deleteTowards=P.prototype.deleteTowards,t.selectOutOf=function(t,e){e.insDirOf(t,this)},t.deleteOutOf=function(t,e){this.isEmpty()&&e.insRightOf(this)},t.write=function(t,n){var i,s;t.show().deleteSelection(),"$"!==n?t[w]?t[w].appendText(n):Z(n).createLeftOf(t):this.isEmpty()?(t.insRightOf(this),U("\\$","$").createLeftOf(t)):t[q]?t[w]?(i=K(),s=this.ends[w],s.disown().jQ.detach(),s.adopt(i,0,0),t.insLeftOf(this),e.createLeftOf.call(i,t)):t.insLeftOf(this):t.insRightOf(this),this.bubble("reflow")},t.writeLatex=function(t,e){t[w]?t[w].appendText(e):Z(e).createLeftOf(t),this.bubble("reflow")},t.seek=function(t,e){var i,s,r,o,a,l,c,u;for(e.hide(),i=n(this),s=this.jQ.width()/this.text.length,r=Math.round((t-this.jQ.offset().left)/s),r<=0?e.insAtLeftEnd(this):r>=i.text.length?e.insAtRightEnd(this):e.insLeftOf(i.splitRight(r)),o=t-e.show().offset().left,a=o&&o<0?w:q,l=a;e[a]&&o*l>0;)e[a].moveTowards(a,e),l=o,o=t-e.offset().left;a*o<-a*l&&e[-a].moveTowards(-a,e),e.anticursor?e.anticursor.parent===this&&(c=e[w]&&e[w].text.length,this.anticursorPosition===c?e.anticursor=y.copy(e):(this.anticursorPosition<c?(u=e[w].splitRight(this.anticursorPosition),e[w]=u):u=e[q].splitRight(this.anticursorPosition-c),e.anticursor=y(this,u[w],u))):this.anticursorPosition=e[w]&&e[w].text.length},t.blur=function(t){V.prototype.blur.call(this),t&&(""===this.textContents()?(this.remove(),t[w]===this?t[w]=this[w]:t[q]===this&&(t[q]=this[q])):n(this))},t.focus=V.prototype.focus}),Z=v(O,function(t,e){function n(t,e){return e.charAt(t===w?0:-1+e.length)}t.init=function(t){e.init.call(this),this.text=t},t.jQadd=function(t){this.dom=t,this.jQ=x(t)},t.jQize=function(){return this.jQadd(document.createTextNode(this.text))},t.appendText=function(t){this.text+=t,this.dom.appendData(t)},t.prependText=function(t){this.text=t+this.text,this.dom.insertData(0,t)},t.insTextAtDirEnd=function(t,e){r(e),e===q?this.appendText(t):this.prependText(t)},t.splitRight=function(t){var e=Z(this.text.slice(t)).adopt(this.parent,this,this[q]);return e.jQadd(this.dom.splitText(t)),this.text=this.text.slice(0,t),e},t.moveTowards=function(t,e){var i,s;return r(t),i=n(-t,this.text),s=this[-t],s?s.insTextAtDirEnd(i,t):Z(i).createDir(-t,e),this.deleteTowards(t,e)},t.latex=function(){return this.text},t.deleteTowards=function(t,e){this.text.length>1?t===q?(this.dom.deleteData(0,1),this.text=this.text.slice(1)):(this.dom.deleteData(-1+this.text.length,1),this.text=this.text.slice(0,-1)):(this.remove(),this.jQ.remove(),e[t]=this[t])},t.selectTowards=function(t,e){var i,s,o,a;return r(t),i=e.anticursor,s=n(-t,this.text),i[t]===this?(o=Z(s).createDir(t,e),i[t]=o,e.insDirOf(t,o)):(a=this[-t],a?a.insTextAtDirEnd(s,t):(o=Z(s).createDir(-t,e),o.jQ.insDirOf(-t,e.selection.jQ)),1===this.text.length&&i[-t]===this&&(i[-t]=this[-t])),this.deleteTowards(t,e)}}),T.text=T.textnormal=T.textrm=T.textup=T.textmd=K,T.em=T.italic=T.italics=T.emph=T.textit=T.textsl=h("\\textit","i",'class="mq-text-mode"'),T.strong=T.bold=T.textbf=h("\\textbf","b",'class="mq-text-mode"'),T.sf=T.textsf=h("\\textsf","span",'class="mq-sans-serif mq-text-mode"'),T.tt=T.texttt=h("\\texttt","span",'class="mq-monospace mq-text-mode"'),T.textsc=h("\\textsc","span",'style="font-variant:small-caps" class="mq-text-mode"'),T.uppercase=h("\\uppercase","span",'style="text-transform:uppercase" class="mq-text-mode"'),T.lowercase=h("\\lowercase","span",'style="text-transform:lowercase" class="mq-text-mode"'),Y=v(P,function(t,e){t.init=function(t){e.init.call(this,"$"),this.cursor=t},t.htmlTemplate='<span class="mq-math-mode">&0</span>',t.createBlocks=function(){e.createBlocks.call(this),this.ends[w].cursor=this.cursor,this.ends[w].write=function(t,e){"$"!==e?V.prototype.write.call(this,t,e):this.isEmpty()?(t.insRightOf(this.parent),this.parent.deleteTowards(dir,t),U("\\$","$").createLeftOf(t.show())):t[q]?t[w]?V.prototype.write.call(this,t,e):t.insLeftOf(this.parent):t.insRightOf(this.parent)}},t.latex=function(){return"$"+this.ends[w].latex()+"$"}}),X=v(G,function(t,e){t.keystroke=function(t){if("Spacebar"!==t&&"Shift-Spacebar"!==t)return e.keystroke.apply(this,arguments)},t.write=function(t,e){if(t.show().deleteSelection(),"$"===e)Y(t).createLeftOf(t);else{var n;"<"===e?n="&lt;":">"===e&&(n="&gt;"),U(e,n).createLeftOf(t)}}}),D.TextField=function(t){return v(t.EditableField,function(t,e){this.RootBlock=X,t.__mathquillify=function(){return e.__mathquillify.call(this,"mq-editable-field mq-text-mode")},t.latex=function(t){return arguments.length>0?(this.__controller.renderLatexText(t),this.__controller.blurred&&this.__controller.cursor.hide().parent.blur(),this):this.__controller.exportLatex()}})},Q["\\"]=v(P,function(t,e){t.ctrlSeq="\\",t.replaces=function(t){this._replacedFragment=t.disown(),this.isEmpty=function(){return!1}},t.htmlTemplate='<span class="mq-latex-command-input mq-non-leaf">\\<span>&0</span></span>',t.textTemplate=["\\"],t.createBlocks=function(){e.createBlocks.call(this),this.ends[w].focus=function(){return this.parent.jQ.addClass("mq-hasCursor"),this.isEmpty()&&this.parent.jQ.removeClass("mq-empty"),this},this.ends[w].blur=function(){return this.parent.jQ.removeClass("mq-hasCursor"),this.isEmpty()&&this.parent.jQ.addClass("mq-empty"),this},this.ends[w].write=function(t,e){t.show().deleteSelection(),e.match(/[a-z]/i)?U(e).createLeftOf(t):(this.parent.renderCommand(t),"\\"===e&&this.isEmpty()||t.parent.write(t,e))},this.ends[w].keystroke=function(t,n,i){return"Tab"===t||"Enter"===t||"Spacebar"===t?(this.parent.renderCommand(i.cursor),void n.preventDefault()):e.keystroke.apply(this,arguments)}},t.createLeftOf=function(t){if(e.createLeftOf.call(this,t),this._replacedFragment){var n=this.jQ[0];this.jQ=this._replacedFragment.jQ.addClass("mq-blur").bind("mousedown mousemove",function(t){return x(t.target=n).trigger(t),!1}).insertBefore(this.jQ).add(this.jQ)}},t.latex=function(){return"\\"+this.ends[w].latex()+" "},t.renderCommand=function(t){var e,n;this.jQ=this.jQ.last(),this.remove(),this[q]?t.insLeftOf(this[q]):t.insAtRightEnd(this.parent),e=this.ends[w].latex(),e||(e=" "),n=T[e],n?(n=n(e),this._replacedFragment&&n.replaces(this._replacedFragment),n.createLeftOf(t)):(n=K(),n.replaces(e),n.createLeftOf(t),t.insRightOf(n),this._replacedFragment&&this._replacedFragment.remove())}}),T.notin=T.cong=T.equiv=T.oplus=T.otimes=v(H,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","&"+t+";")}}),T["≠"]=T.ne=T.neq=i(H,"\\ne ","&ne;"),T["∗"]=T.ast=T.star=T.loast=T.lowast=i(H,"\\ast ","&lowast;"),T.therefor=T.therefore=i(H,"\\therefore ","&there4;"),T.cuz=T.because=i(H,"\\because ","&#8757;"),T.prop=T.propto=i(H,"\\propto ","&prop;"),T["≈"]=T.asymp=T.approx=i(H,"\\approx ","&asymp;"),T.isin=T.in=i(H,"\\in ","&isin;"),T.ni=T.contains=i(H,"\\ni ","&ni;"),T.notni=T.niton=T.notcontains=T.doesnotcontain=i(H,"\\not\\ni ","&#8716;"),T.sub=T.subset=i(H,"\\subset ","&sub;"),T.sup=T.supset=T.superset=i(H,"\\supset ","&sup;"),T.nsub=T.notsub=T.nsubset=T.notsubset=i(H,"\\not\\subset ","&#8836;"),T.nsup=T.notsup=T.nsupset=T.notsupset=T.nsuperset=T.notsuperset=i(H,"\\not\\supset ","&#8837;"),T.sube=T.subeq=T.subsete=T.subseteq=i(H,"\\subseteq ","&sube;"),T.supe=T.supeq=T.supsete=T.supseteq=T.supersete=T.superseteq=i(H,"\\supseteq ","&supe;"),T.nsube=T.nsubeq=T.notsube=T.notsubeq=T.nsubsete=T.nsubseteq=T.notsubsete=T.notsubseteq=i(H,"\\not\\subseteq ","&#8840;"),T.nsupe=T.nsupeq=T.notsupe=T.notsupeq=T.nsupsete=T.nsupseteq=T.notsupsete=T.notsupseteq=T.nsupersete=T.nsuperseteq=T.notsupersete=T.notsuperseteq=i(H,"\\not\\supseteq ","&#8841;"),T.mathbb=v(P,function(e){e.createLeftOf=t,e.numBlocks=function(){return 1},e.parser=function(){var t=B.string,e=B.regex,n=B.optWhitespace;return n.then(t("{")).then(n).then(e(/^[NPZQRCH]/)).skip(n).skip(t("}")).map(function(t){return T[t]()})}}),T.N=T.naturals=T.Naturals=i(U,"\\mathbb{N}","&#8469;"),T.P=T.primes=T.Primes=T.projective=T.Projective=T.probability=T.Probability=i(U,"\\mathbb{P}","&#8473;"),T.Z=T.integers=T.Integers=i(U,"\\mathbb{Z}","&#8484;"),T.Q=T.rationals=T.Rationals=i(U,"\\mathbb{Q}","&#8474;"),T.R=T.reals=T.Reals=i(U,"\\mathbb{R}","&#8477;"),T.C=T.complex=T.Complex=T.complexes=T.Complexes=T.complexplane=T.Complexplane=T.ComplexPlane=i(U,"\\mathbb{C}","&#8450;"),T.H=T.Hamiltonian=T.quaternions=T.Quaternions=i(U,"\\mathbb{H}","&#8461;"),T.quad=T.emsp=i(U,"\\quad ","    "),T.qquad=i(U,"\\qquad ","        "),T.diamond=i(U,"\\diamond ","&#9671;"),T.bigtriangleup=i(U,"\\bigtriangleup ","&#9651;"),T.ominus=i(U,"\\ominus ","&#8854;"),T.uplus=i(U,"\\uplus ","&#8846;"),T.bigtriangledown=i(U,"\\bigtriangledown ","&#9661;"),T.sqcap=i(U,"\\sqcap ","&#8851;"),T.triangleleft=i(U,"\\triangleleft ","&#8882;"),T.sqcup=i(U,"\\sqcup ","&#8852;"),T.triangleright=i(U,"\\triangleright ","&#8883;"),T.odot=T.circledot=i(U,"\\odot ","&#8857;"),T.bigcirc=i(U,"\\bigcirc ","&#9711;"),T.dagger=i(U,"\\dagger ","&#0134;"),T.ddagger=i(U,"\\ddagger ","&#135;"),T.wr=i(U,"\\wr ","&#8768;"),T.amalg=i(U,"\\amalg ","&#8720;"),T.models=i(U,"\\models ","&#8872;"),T.prec=i(U,"\\prec ","&#8826;"),T.succ=i(U,"\\succ ","&#8827;"),T.preceq=i(U,"\\preceq ","&#8828;"),T.succeq=i(U,"\\succeq ","&#8829;"),T.simeq=i(U,"\\simeq ","&#8771;"),T.mid=i(U,"\\mid ","&#8739;"),T.ll=i(U,"\\ll ","&#8810;"),T.gg=i(U,"\\gg ","&#8811;"),T.parallel=i(U,"\\parallel ","&#8741;"),T.nparallel=i(U,"\\nparallel ","&#8742;"),T.bowtie=i(U,"\\bowtie ","&#8904;"),T.sqsubset=i(U,"\\sqsubset ","&#8847;"),T.sqsupset=i(U,"\\sqsupset ","&#8848;"),T.smile=i(U,"\\smile ","&#8995;"),T.sqsubseteq=i(U,"\\sqsubseteq ","&#8849;"),T.sqsupseteq=i(U,"\\sqsupseteq ","&#8850;"),T.doteq=i(U,"\\doteq ","&#8784;"),T.frown=i(U,"\\frown ","&#8994;"),T.vdash=i(U,"\\vdash ","&#8870;"),T.dashv=i(U,"\\dashv ","&#8867;"),T.nless=i(U,"\\nless ","&#8814;"),T.ngtr=i(U,"\\ngtr ","&#8815;"),T.longleftarrow=i(U,"\\longleftarrow ","&#8592;"),T.longrightarrow=i(U,"\\longrightarrow ","&#8594;"),T.Longleftarrow=i(U,"\\Longleftarrow ","&#8656;"),T.Longrightarrow=i(U,"\\Longrightarrow ","&#8658;"),T.longleftrightarrow=i(U,"\\longleftrightarrow ","&#8596;"),T.updownarrow=i(U,"\\updownarrow ","&#8597;"),T.Longleftrightarrow=i(U,"\\Longleftrightarrow ","&#8660;"),T.Updownarrow=i(U,"\\Updownarrow ","&#8661;"),T.mapsto=i(U,"\\mapsto ","&#8614;"),T.nearrow=i(U,"\\nearrow ","&#8599;"),T.hookleftarrow=i(U,"\\hookleftarrow ","&#8617;"),T.hookrightarrow=i(U,"\\hookrightarrow ","&#8618;"),T.searrow=i(U,"\\searrow ","&#8600;"),T.leftharpoonup=i(U,"\\leftharpoonup ","&#8636;"),T.rightharpoonup=i(U,"\\rightharpoonup ","&#8640;"),T.swarrow=i(U,"\\swarrow ","&#8601;"),T.leftharpoondown=i(U,"\\leftharpoondown ","&#8637;"),T.rightharpoondown=i(U,"\\rightharpoondown ","&#8641;"),T.nwarrow=i(U,"\\nwarrow ","&#8598;"),T.ldots=i(U,"\\ldots ","&#8230;"),T.cdots=i(U,"\\cdots ","&#8943;"),T.vdots=i(U,"\\vdots ","&#8942;"),T.ddots=i(U,"\\ddots ","&#8945;"),T.surd=i(U,"\\surd ","&#8730;"),T.triangle=i(U,"\\triangle ","&#9651;"),T.ell=i(U,"\\ell ","&#8467;"),T.top=i(U,"\\top ","&#8868;"),T.flat=i(U,"\\flat ","&#9837;"),T.natural=i(U,"\\natural ","&#9838;"),T.sharp=i(U,"\\sharp ","&#9839;"),T.wp=i(U,"\\wp ","&#8472;"),T.bot=i(U,"\\bot ","&#8869;"),T.clubsuit=i(U,"\\clubsuit ","&#9827;"),T.diamondsuit=i(U,"\\diamondsuit ","&#9826;"),T.heartsuit=i(U,"\\heartsuit ","&#9825;"),T.spadesuit=i(U,"\\spadesuit ","&#9824;"),T.parallelogram=i(U,"\\parallelogram ","&#9649;"),T.square=i(U,"\\square ","&#11036;"),T.oint=i(U,"\\oint ","&#8750;"),T.bigcap=i(U,"\\bigcap ","&#8745;"),T.bigcup=i(U,"\\bigcup ","&#8746;"),T.bigsqcup=i(U,"\\bigsqcup ","&#8852;"),T.bigvee=i(U,"\\bigvee ","&#8744;"),T.bigwedge=i(U,"\\bigwedge ","&#8743;"),T.bigodot=i(U,"\\bigodot ","&#8857;"),T.bigotimes=i(U,"\\bigotimes ","&#8855;"),T.bigoplus=i(U,"\\bigoplus ","&#8853;"),T.biguplus=i(U,"\\biguplus ","&#8846;"),T.lfloor=i(U,"\\lfloor ","&#8970;"),T.rfloor=i(U,"\\rfloor ","&#8971;"),T.lceil=i(U,"\\lceil ","&#8968;"),T.rceil=i(U,"\\rceil ","&#8969;"),T.opencurlybrace=T.lbrace=i(U,"\\lbrace ","{"),T.closecurlybrace=T.rbrace=i(U,"\\rbrace ","}"),T.lbrack=i(U,"["),T.rbrack=i(U,"]"),T.slash=i(U,"/"),T.vert=i(U,"|"),T.perp=T.perpendicular=i(U,"\\perp ","&perp;"),T.nabla=T.del=i(U,"\\nabla ","&nabla;"),T.hbar=i(U,"\\hbar ","&#8463;"),T.AA=T.Angstrom=T.angstrom=i(U,"\\text\\AA ","&#8491;"),T.ring=T.circ=T.circle=i(U,"\\circ ","&#8728;"),T.bull=T.bullet=i(U,"\\bullet ","&bull;"),T.setminus=T.smallsetminus=i(U,"\\setminus ","&#8726;"),T.not=T["¬"]=T.neg=i(U,"\\neg ","&not;"),T["…"]=T.dots=T.ellip=T.hellip=T.ellipsis=T.hellipsis=i(U,"\\dots ","&hellip;"),T.converges=T.darr=T.dnarr=T.dnarrow=T.downarrow=i(U,"\\downarrow ","&darr;"),T.dArr=T.dnArr=T.dnArrow=T.Downarrow=i(U,"\\Downarrow ","&dArr;"),T.diverges=T.uarr=T.uparrow=i(U,"\\uparrow ","&uarr;"),T.uArr=T.Uparrow=i(U,"\\Uparrow ","&uArr;"),T.to=i(H,"\\to ","&rarr;"),T.rarr=T.rightarrow=i(U,"\\rightarrow ","&rarr;"),T.implies=i(H,"\\Rightarrow ","&rArr;"),T.rArr=T.Rightarrow=i(U,"\\Rightarrow ","&rArr;"),T.gets=i(H,"\\gets ","&larr;"),T.larr=T.leftarrow=i(U,"\\leftarrow ","&larr;"),T.impliedby=i(H,"\\Leftarrow ","&lArr;"),T.lArr=T.Leftarrow=i(U,"\\Leftarrow ","&lArr;"),T.harr=T.lrarr=T.leftrightarrow=i(U,"\\leftrightarrow ","&harr;"),T.iff=i(H,"\\Leftrightarrow ","&hArr;"),T.hArr=T.lrArr=T.Leftrightarrow=i(U,"\\Leftrightarrow ","&hArr;"),T.Re=T.Real=T.real=i(U,"\\Re ","&real;"),T.Im=T.imag=T.image=T.imagin=T.imaginary=T.Imaginary=i(U,"\\Im ","&image;"),T.part=T.partial=i(U,"\\partial ","&part;"),T.infty=T.infin=T.infinity=i(U,"\\infty ","&infin;"),T.pounds=i(U,"\\pounds ","&pound;"),T.alef=T.alefsym=T.aleph=T.alephsym=i(U,"\\aleph ","&alefsym;"),T.xist=T.xists=T.exist=T.exists=i(U,"\\exists ","&exist;"),T.nexists=T.nexist=i(U,"\\nexists ","&#8708;"),T.and=T.land=T.wedge=i(H,"\\wedge ","&and;"),T.or=T.lor=T.vee=i(H,"\\vee ","&or;"),T.o=T.O=T.empty=T.emptyset=T.oslash=T.Oslash=T.nothing=T.varnothing=i(H,"\\varnothing ","&empty;"),T.cup=T.union=i(H,"\\cup ","&cup;"),T.cap=T.intersect=T.intersection=i(H,"\\cap ","&cap;"),T.deg=T.degree=i(U,"\\degree ","&deg;"),T.ang=T.angle=i(U,"\\angle ","&ang;"),T.measuredangle=i(U,"\\measuredangle ","&#8737;"),J=v(U,function(t,e){t.createLeftOf=function(t){t.options.autoSubscriptNumerals&&t.parent!==t.parent.parent.sub&&(t[w]instanceof tt&&!1!==t[w].isItalic||t[w]instanceof yt&&t[w][w]instanceof tt&&!1!==t[w][w].isItalic)?(T._().createLeftOf(t),e.createLeftOf.call(this,t),t.insRightOf(t.parent.parent)):e.createLeftOf.call(this,t)}}),tt=v(W,function(t,e){t.init=function(t,n){e.init.call(this,t,"<var>"+(n||t)+"</var>")},t.text=function(){var t=this.ctrlSeq;return this.isPartOfOperator?"\\"==t[0]?t=t.slice(1,t.length):" "==t[t.length-1]&&(t=t.slice(0,-1)):(!this[w]||this[w]instanceof tt||this[w]instanceof H||"\\ "===this[w].ctrlSeq||(t="*"+t),!this[q]||this[q]instanceof H||this[q]instanceof yt||(t+="*")),t}}),E.p.autoCommands={_maxLength:0},L.autoCommands=function(t){var e,n,i,s,r;if(!/^[a-z]+(?: [a-z]+)*$/i.test(t))throw'"'+t+'" not a space-delimited list of only letters';for(e=t.split(" "),n={},i=0,s=0;s<e.length;s+=1){if(r=e[s],r.length<2)throw'autocommand "'+r+'" not minimum length of 2';if(T[r]===rt)throw'"'+r+'" is a built-in operator name';n[r]=1,i=Bt(i,r.length)}return n._maxLength=i,n},et=v(tt,function(t,e){function n(t){return!t||t instanceof H||t instanceof Ot}t.init=function(t){return e.init.call(this,this.letter=t)},t.createLeftOf=function(t){var n,i,s,r,o;if(e.createLeftOf.apply(this,arguments),n=t.options.autoCommands,i=n._maxLength,i>0){for(s="",r=this,o=0;r instanceof et&&r.ctrlSeq===r.letter&&o<i;)s=r.letter+s,r=r[w],o+=1;for(;s.length;){if(n.hasOwnProperty(s)){for(o=1,r=this;o<s.length;o+=1,r=r[w]);return k(r,this).remove(),t[w]=r[w],T[s](s).createLeftOf(t)}s=s.slice(1)}}},t.italicize=function(t){return this.isItalic=t,this.isPartOfOperator=!t,this.jQ.toggleClass("mq-operator-name",!t),this},t.finalizeTree=t.siblingDeleted=t.siblingCreated=function(t,e){e!==w&&this[q]instanceof et||this.autoUnItalicize(t)},t.autoUnItalicize=function(t){var e,i,s,r,o,a,l,c,u,h,f,p,d=t.autoOperatorNames;if(0!==d._maxLength){for(e=this.letter,i=this[w];i instanceof et;i=i[w])e=i.letter+e;for(s=this[q];s instanceof et;s=s[q])e+=s.letter;k(i[q]||this.parent.ends[w],s[w]||this.parent.ends[q]).each(function(t){t.italicize(!0).jQ.removeClass("mq-first mq-last mq-followed-by-supsub"),t.ctrlSeq=t.letter});t:for(r=0,o=i[q]||this.parent.ends[w];r<e.length;r+=1,o=o[q])for(a=Mt(d._maxLength,e.length-r);a>0;a-=1)if(l=e.slice(r,r+a),d.hasOwnProperty(l)){for(c=0,u=o;c<a;c+=1,u=u[q])u.italicize(!1),h=u;f=nt.hasOwnProperty(l),o.ctrlSeq=(f?"\\":"\\operatorname{")+o.ctrlSeq,h.ctrlSeq+=f?" ":"}",st.hasOwnProperty(l)&&h[w][w][w].jQ.addClass("mq-last"),n(o[w])||o.jQ.addClass("mq-first"),n(h[q])||(h[q]instanceof yt?(p=h[q],(p.siblingCreated=p.siblingDeleted=function(){p.jQ.toggleClass("mq-after-operator-name",!(p[q]instanceof Ct))})()):h.jQ.toggleClass("mq-last",!(h[q]instanceof Ct))),r+=a-1,o=h;continue t}}}}),nt={},it=E.p.autoOperatorNames={_maxLength:9},st={limsup:1,liminf:1,projlim:1,injlim:1},function(){var t,e,n,i,s="arg deg det dim exp gcd hom inf ker lg lim ln log max min sup limsup liminf injlim projlim Pr".split(" ");for(t=0;t<s.length;t+=1)nt[s[t]]=it[s[t]]=1;for(e="sin cos tan arcsin arccos arctan sinh cosh tanh sec csc cot coth".split(" "),t=0;t<e.length;t+=1)nt[e[t]]=1;for(n="sin cos tan sec cosec csc cotan cot ctg".split(" "),t=0;t<n.length;t+=1)it[n[t]]=it["arc"+n[t]]=it[n[t]+"h"]=it["ar"+n[t]+"h"]=it["arc"+n[t]+"h"]=1;for(i="gcf hcf lcm proj span".split(" "),t=0;t<i.length;t+=1)it[i[t]]=1}(),L.autoOperatorNames=function(t){var e,n,i,s,r;if(!/^[a-z]+(?: [a-z]+)*$/i.test(t))throw'"'+t+'" not a space-delimited list of only letters';for(e=t.split(" "),n={},i=0,s=0;s<e.length;s+=1){if(r=e[s],r.length<2)throw'"'+r+'" not minimum length of 2';n[r]=1,i=Bt(i,r.length)}return n._maxLength=i,n},rt=v(W,function(t,e){t.init=function(t){this.ctrlSeq=t},t.createLeftOf=function(t){var e,n=this.ctrlSeq;for(e=0;e<n.length;e+=1)et(n.charAt(e)).createLeftOf(t)},t.parser=function(){var t,e=this.ctrlSeq,n=V();for(t=0;t<e.length;t+=1)et(e.charAt(t)).adopt(n,n.ends[q],0);return B.succeed(n.children())}});for(ot in it)it.hasOwnProperty(ot)&&(T[ot]=rt);T.operatorname=v(P,function(e){e.createLeftOf=t,e.numBlocks=function(){return 1},e.parser=function(){return N.block.map(function(t){return t.children()})}}),T.f=v(et,function(t,e){t.init=function(){W.p.init.call(this,this.letter="f",'<var class="mq-f">f</var>')},t.italicize=function(t){return this.jQ.html("f").toggleClass("mq-f",t),e.italicize.apply(this,arguments)}}),T[" "]=T.space=i(U,"\\ ","&nbsp;"),T["'"]=T.prime=i(U,"'","&prime;"),T["″"]=T.dprime=i(U,"″","&Prime;"),T.backslash=i(U,"\\backslash ","\\"),Q["\\"]||(Q["\\"]=T.backslash),T.$=i(U,"\\$","$"),at=v(W,function(t,e){t.init=function(t,n){e.init.call(this,t,'<span class="mq-nonSymbola">'+(n||t)+"</span>")}}),T["@"]=at,T["&"]=i(at,"\\&","&amp;"),T["%"]=i(at,"\\%","%"),T.alpha=T.beta=T.gamma=T.delta=T.zeta=T.eta=T.theta=T.iota=T.kappa=T.mu=T.nu=T.xi=T.rho=T.sigma=T.tau=T.chi=T.psi=T.omega=v(tt,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","&"+t+";")}}),T.phi=i(tt,"\\phi ","&#981;"),T.phiv=T.varphi=i(tt,"\\varphi ","&phi;"),T.epsilon=i(tt,"\\epsilon ","&#1013;"),T.epsiv=T.varepsilon=i(tt,"\\varepsilon ","&epsilon;"),T.piv=T.varpi=i(tt,"\\varpi ","&piv;"),T.sigmaf=T.sigmav=T.varsigma=i(tt,"\\varsigma ","&sigmaf;"),T.thetav=T.vartheta=T.thetasym=i(tt,"\\vartheta ","&thetasym;"),T.upsilon=T.upsi=i(tt,"\\upsilon ","&upsilon;"),T.gammad=T.Gammad=T.digamma=i(tt,"\\digamma ","&#989;"),T.kappav=T.varkappa=i(tt,"\\varkappa ","&#1008;"),T.rhov=T.varrho=i(tt,"\\varrho ","&#1009;"),T.pi=T["π"]=i(at,"\\pi ","&pi;"),T.lambda=i(at,"\\lambda ","&lambda;"),T.Upsilon=T.Upsi=T.upsih=T.Upsih=i(W,"\\Upsilon ",'<var style="font-family: serif">&upsih;</var>'),T.Gamma=T.Delta=T.Theta=T.Lambda=T.Xi=T.Pi=T.Sigma=T.Phi=T.Psi=T.Omega=T.forall=v(U,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","&"+t+";")}}),lt=v(P,function(t){t.init=function(t){this.latex=t},t.createLeftOf=function(t){var e=N.parse(this.latex);e.children().adopt(t.parent,t[w],t[q]),t[w]=e.ends[q],e.jQize().insertBefore(t.jQ),e.finalizeInsert(t.options,t),e.ends[q][q].siblingCreated&&e.ends[q][q].siblingCreated(t.options,w),e.ends[w][w].siblingCreated&&e.ends[w][w].siblingCreated(t.options,q),t.parent.bubble("reflow")},t.parser=function(){var t=N.parse(this.latex).children();return B.succeed(t)}}),T["¹"]=i(lt,"^1"),T["²"]=i(lt,"^2"),T["³"]=i(lt,"^3"),T["¼"]=i(lt,"\\frac14"),T["½"]=i(lt,"\\frac12"),T["¾"]=i(lt,"\\frac34"),ct=v(H,function(t){t.init=U.prototype.init,t.contactWeld=t.siblingCreated=t.siblingDeleted=function(t,e){function n(t){return t[w]?t[w]instanceof H||/^[,;:\(\[]$/.test(t[w].ctrlSeq)?"":"mq-binary-operator":t.parent&&t.parent.parent&&t.parent.parent.isStyleBlock()?n(t.parent.parent):""}if(e!==q)return this.jQ[0].className=n(this),this}}),T["+"]=i(ct,"+","+"),T["–"]=T["-"]=i(ct,"-","&minus;"),T["±"]=T.pm=T.plusmn=T.plusminus=i(ct,"\\pm ","&plusmn;"),T.mp=T.mnplus=T.minusplus=i(ct,"\\mp ","&#8723;"),Q["*"]=T.sdot=T.cdot=i(H,"\\cdot ","&middot;","*"),ut=v(H,function(t,e){t.init=function(t,n){this.data=t,this.strict=n;var i=n?"Strict":"";e.init.call(this,t["ctrlSeq"+i],t["html"+i],t["text"+i])},t.swap=function(t){this.strict=t;var e=t?"Strict":"";this.ctrlSeq=this.data["ctrlSeq"+e],this.jQ.html(this.data["html"+e]),this.textTemplate=[this.data["text"+e]]},t.deleteTowards=function(t,n){if(t===w&&!this.strict)return this.swap(!0),void this.bubble("reflow");e.deleteTowards.apply(this,arguments)}}),ht={ctrlSeq:"\\le ",html:"&le;",text:"≤",ctrlSeqStrict:"<",htmlStrict:"&lt;",textStrict:"<"},ft={ctrlSeq:"\\ge ",html:"&ge;",text:"≥",ctrlSeqStrict:">",htmlStrict:"&gt;",textStrict:">"},T["<"]=T.lt=i(ut,ht,!0),T[">"]=T.gt=i(ut,ft,!0),T["≤"]=T.le=T.leq=i(ut,ht,!1),T["≥"]=T.ge=T.geq=i(ut,ft,!1),pt=v(H,function(t,e){t.init=function(){e.init.call(this,"=","=")},t.createLeftOf=function(t){if(t[w]instanceof ut&&t[w].strict)return t[w].swap(!1),void t[w].bubble("reflow");e.createLeftOf.apply(this,arguments)}}),T["="]=pt,T["×"]=T.times=i(H,"\\times ","&times;","[x]"),T["÷"]=T.div=T.divide=T.divides=i(H,"\\div ","&divide;","[/]"),Q["~"]=T.sim=i(H,"\\sim ","~","~"),mt=t,gt=document.createElement("div"),bt=gt.style,vt={transform:1,WebkitTransform:1,MozTransform:1,OTransform:1,msTransform:1};for(qt in vt)if(qt in bt){wt=qt;break}wt?dt=function(t,e,n){t.css(wt,"scale("+e+","+n+")")}:"filter"in bt?(mt=function(t){t.className=t.className},dt=function(t,e,n){function i(){t.css("marginRight",(s.width()-1)*(e-1)/e+"px")}var s,r;e/=1+(n-1)/2,t.css("fontSize",n+"em"),t.hasClass("mq-matrixed-container")||t.addClass("mq-matrixed-container").wrapInner('<span class="mq-matrixed"></span>'),s=t.children().css("filter","progid:DXImageTransform.Microsoft.Matrix(M11="+e+",SizingMethod='auto expand')"),i(),r=setInterval(i),x(window).load(function(){clearTimeout(r),i()})}):dt=function(t,e,n){t.css("fontSize",n+"em")},xt=v(P,function(t,e){t.init=function(t,n,i){e.init.call(this,t,"<"+n+" "+i+">&0</"+n+">")}}),T.mathrm=i(xt,"\\mathrm","span",'class="mq-roman mq-font"'),T.mathit=i(xt,"\\mathit","i",'class="mq-font"'),T.mathbf=i(xt,"\\mathbf","b",'class="mq-font"'),T.mathsf=i(xt,"\\mathsf","span",'class="mq-sans-serif mq-font"'),T.mathtt=i(xt,"\\mathtt","span",'class="mq-monospace mq-font"'),T.underline=i(xt,"\\underline","span",'class="mq-non-leaf mq-underline"'),T.overline=T.bar=i(xt,"\\overline","span",'class="mq-non-leaf mq-overline"'),T.overrightarrow=i(xt,"\\overrightarrow","span",'class="mq-non-leaf mq-overarrow mq-arrow-right"'),T.overleftarrow=i(xt,"\\overleftarrow","span",'class="mq-non-leaf mq-overarrow mq-arrow-left"'),T.overleftrightarrow=i(xt,"\\overleftrightarrow","span",'class="mq-non-leaf mq-overarrow mq-arrow-both"'),T.overarc=i(xt,"\\overarc","span",'class="mq-non-leaf mq-overarc"'),T.dot=v(P,function(t,e){
t.init=function(){e.init.call(this,"\\dot",'<span class="mq-non-leaf"><span class="mq-dot-recurring-inner"><span class="mq-dot-recurring">&#x2d9;</span><span class="mq-empty-box">&0</span></span></span>')}}),T.textcolor=v(P,function(t,e){t.setColor=function(t){this.color=t,this.htmlTemplate='<span class="mq-textcolor" style="color:'+t+'">&0</span>'},t.latex=function(){return"\\textcolor{"+this.color+"}{"+this.blocks[0].latex()+"}"},t.parser=function(){var t=this,n=B.optWhitespace,i=B.string,s=B.regex;return n.then(i("{")).then(s(/^[#\w\s.,()%-]*/)).skip(i("}")).then(function(n){return t.setColor(n),e.parser.call(t)})},t.isStyleBlock=function(){return!0}}),T.class=v(P,function(t,e){t.parser=function(){var t=this,n=B.string,i=B.regex;return B.optWhitespace.then(n("{")).then(i(/^[-\w\s\\\xA0-\xFF]*/)).skip(n("}")).then(function(n){return t.cls=n||"",t.htmlTemplate='<span class="mq-class '+n+'">&0</span>',e.parser.call(t)})},t.latex=function(){return"\\class{"+this.cls+"}{"+this.blocks[0].latex()+"}"},t.isStyleBlock=function(){return!0}}),yt=v(P,function(t,e){t.ctrlSeq="_{...}^{...}",t.createLeftOf=function(t){if(this.replacedFragment||t[w]||!t.options.supSubsRequireOperand)return e.createLeftOf.apply(this,arguments)},t.contactWeld=function(t){var e,n,i,s,r,o;for(e=w;e;e=e===w&&q)if(this[e]instanceof yt){for(n="sub";n;n="sub"===n&&"sup")i=this[n],s=this[e][n],i&&(s?i.isEmpty()?o=y(s,0,s.ends[w]):(i.jQ.children().insAtDirEnd(-e,s.jQ),r=i.children().disown(),o=y(s,r.ends[q],s.ends[w]),e===w?r.adopt(s,s.ends[q],0):r.adopt(s,0,s.ends[w])):this[e].addBlock(i.disown()),this.placeCursor=function(t,n){return function(i){i.insAtDirEnd(-e,t||n)}}(s,i));this.remove(),t&&t[w]===this&&(e===q&&o?o[w]?t.insRightOf(o[w]):t.insAtLeftEnd(o.parent):t.insRightOf(this[e]));break}},E.p.charsThatBreakOutOfSupSub="",t.finalizeTree=function(){this.ends[w].write=function(t,e){if(t.options.autoSubscriptNumerals&&this===this.parent.sub){if("_"===e)return;var n=this.chToCmd(e,t.options);return n instanceof W?t.deleteSelection():t.clearSelection().insRightOf(this.parent),n.createLeftOf(t.show())}t[w]&&!t[q]&&!t.selection&&t.options.charsThatBreakOutOfSupSub.indexOf(e)>-1&&t.insRightOf(this.parent),V.p.write.apply(this,arguments)}},t.moveTowards=function(t,n,i){n.options.autoSubscriptNumerals&&!this.sup?n.insDirOf(t,this):e.moveTowards.apply(this,arguments)},t.deleteTowards=function(t,n){if(n.options.autoSubscriptNumerals&&this.sub){var i=this.sub.ends[-t];i instanceof W?i.remove():i&&i.deleteTowards(t,n.insAtDirEnd(-t,this.sub)),this.sub.isEmpty()&&(this.sub.deleteOutOf(w,n.insAtLeftEnd(this.sub)),this.sup&&n.insDirOf(-t,this))}else e.deleteTowards.apply(this,arguments)},t.latex=function(){function t(t,e){var n=e&&e.latex();return e?t+"{"+(n||" ")+"}":""}return t("_",this.sub)+t("^",this.sup)},t.text=function(){function t(t,e){var n=e&&e.text();return e?t+(1===n.length?n:"("+(n||" ")+")"):""}return t("_",this.sub)+t("^",this.sup)},t.addBlock=function(t){"sub"===this.supsub?(this.sup=this.upInto=this.sub.upOutOf=t,t.adopt(this,this.sub,0).downOutOf=this.sub,t.jQ=x('<span class="mq-sup"/>').append(t.jQ.children()).attr(zt,t.id).prependTo(this.jQ)):(this.sub=this.downInto=this.sup.downOutOf=t,t.adopt(this,0,this.sup).upOutOf=this.sup,t.jQ=x('<span class="mq-sub"></span>').append(t.jQ.children()).attr(zt,t.id).appendTo(this.jQ.removeClass("mq-sup-only")),this.jQ.append('<span style="display:inline-block;width:0">&#8203;</span>'));for(var e=0;e<2;e+=1)!function(t,e,n,i){t[e].deleteOutOf=function(s,r){if(r.insDirOf(this[s]?-s:s,this.parent),!this.isEmpty()){var o=this.ends[s];this.children().disown().withDirAdopt(s,r.parent,r[s],r[-s]).jQ.insDirOf(-s,r.jQ),r[-s]=o}t.supsub=n,delete t[e],delete t[i+"Into"],t[n][i+"OutOf"]=f,delete t[n].deleteOutOf,"sub"===e&&x(t.jQ.addClass("mq-sup-only")[0].lastChild).remove(),this.remove()}}(this,"sub sup".split(" ")[e],"sup sub".split(" ")[e],"down up".split(" ")[e])},t.reflow=function(){var t,e,n,i,s,r=this.jQ,o=r.prev();o.length&&(t=r.children(".mq-sup"),t.length&&(e=parseInt(t.css("font-size")),n=t.offset().top+t.height(),i=n-o.offset().top-.7*e,s=parseInt(t.css("margin-bottom")),t.css("margin-bottom",s+i)))}}),T.subscript=T._=v(yt,function(t,e){t.supsub="sub",t.htmlTemplate='<span class="mq-supsub mq-non-leaf"><span class="mq-sub">&0</span><span style="display:inline-block;width:0">&#8203;</span></span>',t.textTemplate=["_"],t.finalizeTree=function(){this.downInto=this.sub=this.ends[w],this.sub.upOutOf=f,e.finalizeTree.call(this)}}),T.superscript=T.supscript=T["^"]=v(yt,function(t,e){t.supsub="sup",t.html=function(){return e.html.call(this)},t.htmlTemplate='<span class="mq-supsub mq-non-leaf mq-sup-only"><span class="mq-sup">&0</span></span>',t.textTemplate=["^(",")"],t.finalizeTree=function(){this.upInto=this.sup=this.ends[q],this.sup.downOutOf=f,e.finalizeTree.call(this)}}),T.cmdsub=v(v(P,p),function(t,e){t.ctrlSeq="\\cmdsub",t.htmlTemplate='<span class="mq-non-leaf">&0</span><span class="mq-supsub mq-non-leaf"><span class="mq-sub">&1</span><span style="display:inline-block;width:0">&#8203;</span></span>',t.textTemplate=["sub(",",",")"],t.latex=function(){return this.blocks[0].latex()+"_{"+this.blocks[1].latex()+"}"},t.reflow=function(){}}),T.cmdsup=v(v(P,p),function(t,e){t.ctrlSeq="\\cmdsup",t.htmlTemplate='<span class="mq-non-leaf">&0</span><span class="mq-supsub mq-non-leaf mq-sup-only"><span class="mq-sup">&1</span></span>',t.textTemplate=["sup(",",",")"],t.latex=function(){return this.blocks[0].latex()+"^{"+this.blocks[1].latex()+"}"},t.reflow=function(){}}),T.clog=v(P,function(t,e){t.ctrlSeq="\\clog",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-operator-name">log(</span><span class="mq-operator-name">&0</span><span class="mq-operator-name">)</span></span>',t.latex=function(){return"\\log("+this.blocks[0].latex()+")"}}),T.clg=v(v(P,p),function(t,e){t.ctrlSeq="\\clg",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-operator-name">lg(</span><span class="mq-operator-name">&0</span><span class="mq-operator-name">)</span></span>',t.latex=function(){return"\\lg("+this.blocks[0].latex()+")"}}),T.cln=v(v(P,p),function(t,e){t.ctrlSeq="\\cln",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-operator-name">ln(</span><span class="mq-operator-name">&0</span><span class="mq-operator-name">)</span></span>',t.latex=function(){return"\\ln("+this.blocks[0].latex()+")"}}),Ot=v(P,function(t,e){t.init=function(t,e){var n='<span class="mq-large-operator mq-non-leaf"><span class="mq-to"><span>&1</span></span><big>'+e+'</big><span class="mq-from"><span>&0</span></span></span>';W.prototype.init.call(this,t,n)},t.createLeftOf=function(t){e.createLeftOf.apply(this,arguments),t.options.sumStartsWithNEquals&&(et("n").createLeftOf(t),pt().createLeftOf(t))},t.latex=function(){function t(t){return 1===t.length?t:"{"+(t||" ")+"}"}return this.ctrlSeq+"_"+t(this.ends[w].latex())+"^"+t(this.ends[q].latex())},t.parser=function(){var t,e=B.string,n=B.optWhitespace,i=B.succeed,s=N.block,r=this,o=r.blocks=[V(),V()];for(t=0;t<o.length;t+=1)o[t].adopt(r,r.ends[q],0);return n.then(e("_").or(e("^"))).then(function(t){var e=o["_"===t?0:1];return s.then(function(t){return t.children().adopt(e,e.ends[q],0),i(r)})}).many().result(r)},t.finalizeTree=function(){this.downInto=this.ends[w],this.upInto=this.ends[q],this.ends[w].upOutOf=this.ends[q],this.ends[q].downOutOf=this.ends[w]}}),T["∑"]=T.sum=T.summation=i(Ot,"\\sum ","&sum;"),T["∏"]=T.prod=T.product=i(Ot,"\\prod ","&prod;"),T.coprod=T.coproduct=i(Ot,"\\coprod ","&#8720;"),T["∫"]=T.int=T.integral=v(Ot,function(t,e){t.init=function(){W.prototype.init.call(this,"\\int ",'<span class="mq-int mq-non-leaf"><big>&int;</big><span class="mq-supsub mq-non-leaf"><span class="mq-sup"><span class="mq-sup-inner">&1</span></span><span class="mq-sub">&0</span><span style="display:inline-block;width:0">&#8203</span></span></span>')},t.createLeftOf=P.p.createLeftOf}),kt=T.frac=T.dfrac=T.cfrac=T.fraction=v(P,function(t,e){t.ctrlSeq="\\frac",t.htmlTemplate='<span class="mq-fraction mq-non-leaf"><span class="mq-numerator">&0</span><span class="mq-denominator">&1</span><span style="display:inline-block;width:0">&#8203;</span></span>',t.textTemplate=["(",")/(",")"],t.finalizeTree=function(){}}),Tt=T.over=Q["/"]=v(kt,function(e,n){e.createLeftOf=function(e){if(!this.replacedFragment){for(var i=e[w];i&&!(i instanceof H||i instanceof(T.text||t)||i instanceof Ot||"\\ "===i.ctrlSeq||/^[,;:()]$/.test(i.ctrlSeq));)i=i[w];i instanceof Ot&&i[q]instanceof yt&&(i=i[q],i[q]instanceof yt&&i[q].ctrlSeq!=i.ctrlSeq&&(i=i[q])),i===e[w]||e.isTooDeep(1)||(this.replaces(k(i[q]||e.parent.ends[w],e[w])),e[w]=i)}n.createLeftOf.call(this,e)}}),Qt=T.sqrt=T["√"]=v(P,function(t,e){t.ctrlSeq="\\sqrt",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-scaled mq-sqrt-prefix">&radic;</span><span class="mq-non-leaf mq-sqrt-stem">&0</span></span>',t.textTemplate=["sqrt(",")"],t.parser=function(){return N.optBlock.then(function(t){return N.block.map(function(e){var n=jt();return n.blocks=[t,e],t.adopt(n,0,0),e.adopt(n,t,0),n})}).or(e.parser.call(this))},t.reflow=function(){var t=this.ends[q].jQ;dt(t.prev(),1,t.innerHeight()/+t.css("fontSize").slice(0,-2)-.1)}}),T.hat=v(P,function(t,e){t.ctrlSeq="\\hat",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-hat-prefix">^</span><span class="mq-hat-stem">&0</span></span>',t.textTemplate=["hat(",")"]}),jt=T.nthroot=v(Qt,function(t,e){t.htmlTemplate='<sup class="mq-nthroot mq-non-leaf">&0</sup><span class="mq-scaled"><span class="mq-sqrt-prefix mq-scaled">&radic;</span><span class="mq-sqrt-stem mq-non-leaf">&1</span></span>',t.textTemplate=["sqrt[","](",")"],t.latex=function(){return"\\sqrt["+this.ends[w].latex()+"]{"+this.ends[q].latex()+"}"}}),_t=v(P,function(t,e){t.init=function(t,n,i){var s='<span class="mq-non-leaf"><span class="mq-diacritic-above">'+n+'</span><span class="mq-diacritic-stem">&0</span></span>';e.init.call(this,t,s,i)}}),T.vec=i(_t,"\\vec","&rarr;",["vec(",")"]),T.tilde=i(_t,"\\tilde","~",["tilde(",")"]),Ct=v(v(P,p),function(e,n){e.init=function(t,e,i,s,r){n.init.call(this,"\\left"+s,m,[e,i]),this.side=t,this.sides={},this.sides[w]={ch:e,ctrlSeq:s},this.sides[q]={ch:"."===r?"":i,ctrlSeq:r}},e.numBlocks=function(){return 1},e.html=function(){return this.htmlTemplate='<span class="mq-non-leaf"><span class="mq-scaled mq-paren'+(this.side===q?" mq-ghost":"")+'">'+this.sides[w].ch+'</span><span class="mq-non-leaf">&0</span><span class="mq-scaled mq-paren'+(this.side===w?" mq-ghost":"")+'">'+this.sides[q].ch+"</span></span>",n.html.call(this)},e.latex=function(){return"\\left"+this.sides[w].ctrlSeq+this.ends[w].latex()+"\\right"+(this.sides[q].ctrlSeq||".")},e.matchBrack=function(t,e,n){return n instanceof Ct&&n.side&&n.side!==-e&&(!t.restrictMismatchedBrackets||St[this.sides[this.side].ch]===n.sides[n.side].ch||{"(":"]","[":")"}[this.sides[w].ch]===n.sides[q].ch)&&n},e.closeOpposing=function(t){t.side=0,t.sides[this.side]=this.sides[this.side],t.delimjQs.eq(this.side===w?0:1).removeClass("mq-ghost").html(this.sides[this.side].ch)},e.createLeftOf=function(t){var e,i,s;this.replacedFragment||(e=t.options,i="|"===this.sides[w].ch?this.matchBrack(e,q,t[q])||this.matchBrack(e,w,t[w])||this.matchBrack(e,0,t.parent.parent):this.matchBrack(e,-this.side,t[-this.side])||this.matchBrack(e,-this.side,t.parent.parent)),i?(s=this.side=-i.side,this.closeOpposing(i),i===t.parent.parent&&t[s]&&k(t[s],t.parent.ends[s],-s).disown().withDirAdopt(-s,i.parent,i,i[s]).jQ.insDirOf(s,i.jQ),i.bubble("reflow")):(i=this,s=i.side,i.replacedFragment?i.side=0:t[-s]&&(i.replaces(k(t[-s],t.parent.ends[-s],s)),t[-s]=0),n.createLeftOf.call(i,t)),s===w?t.insAtLeftEnd(i.ends[w]):t.insRightOf(i)},e.placeCursor=t,e.unwrap=function(){this.ends[w].children().disown().adopt(this.parent,this,this[q]).jQ.insertAfter(this.jQ),this.remove()},e.deleteSide=function(t,e,n){var i,s,r,o=this.parent,a=this[t],l=o.ends[t];if(t===this.side)return this.unwrap(),void(a?n.insDirOf(-t,a):n.insAtDirEnd(t,o));if(i=n.options,s=!this.side,this.side=-t,this.matchBrack(i,t,this.ends[w].ends[this.side]))this.closeOpposing(this.ends[w].ends[this.side]),r=this.ends[w].ends[t],this.unwrap(),r.siblingCreated&&r.siblingCreated(n.options,t),a?n.insDirOf(-t,a):n.insAtDirEnd(t,o);else{if(this.matchBrack(i,t,this.parent.parent))this.parent.parent.closeOpposing(this),this.parent.parent.unwrap();else{if(e&&s)return this.unwrap(),void(a?n.insDirOf(-t,a):n.insAtDirEnd(t,o));const c=St[this.sides[this.side].ctrlSeq];this.sides[t]={ch:"."===c?"":St[this.sides[this.side].ch],ctrlSeq:c},this.delimjQs.removeClass("mq-ghost").eq(t===w?0:1).addClass("mq-ghost").html(this.sides[t].ch)}a?(r=this.ends[w].ends[t],k(a,l,-t).disown().withDirAdopt(-t,this.ends[w],r,0).jQ.insAtDirEnd(t,this.ends[w].jQ.removeClass("mq-empty")),r.siblingCreated&&r.siblingCreated(n.options,t),n.insDirOf(-t,a)):e?n.insDirOf(t,this):n.insAtDirEnd(t,this.ends[w])}},e.deleteTowards=function(t,e){this.deleteSide(-t,!1,e)},e.finalizeTree=function(){this.ends[w].deleteOutOf=function(t,e){this.parent.deleteSide(t,!0,e)},this.finalizeTree=this.intentionalBlur=function(){this.delimjQs.eq(this.side===w?1:0).removeClass("mq-ghost"),this.side=0}},e.siblingCreated=function(t,e){e===-this.side&&this.finalizeTree()}}),St={"(":")",")":"(","[":"]","]":"[","{":"}","}":"{","\\{":".","\\}":"\\{","&lang;":"&rang;","&rang;":"&lang;","\\langle ":"\\rangle ","\\rangle ":"\\langle ","|":"|","\\lVert ":"\\rVert ","\\rVert ":"\\lVert "},d("["),d("{","\\{"),T.langle=i(Ct,w,"&lang;","&rang;","\\langle ","\\rangle "),T.rangle=i(Ct,q,"&lang;","&rang;","\\langle ","\\rangle "),Q["|"]=i(Ct,w,"|","|","|","|"),T.lVert=i(Ct,w,"&#8741;","&#8741;","\\lVert ","\\rVert "),T.rVert=i(Ct,q,"&#8741;","&#8741;","\\lVert ","\\rVert "),T.left=v(P,function(t){t.parser=function(){var t=B.regex,e=B.string,n=(B.succeed,B.optWhitespace);return n.then(t(/^(?:[([|]|\\\{|\\langle(?![a-zA-Z])|\\lVert(?![a-zA-Z]))/)).then(function(i){var s="\\"===i.charAt(0)?i.slice(1):i;return"\\langle"==i&&(s="&lang;",i+=" "),"\\lVert"==i&&(s="&#8741;",i+=" "),N.then(function(r){return e("\\right").skip(n).then(t(/^(?:[\])|]|\\\}|.|\\rangle(?![a-zA-Z])|\\rVert(?![a-zA-Z]))/)).map(function(t){var e,n,o=t;return"."==o&&(o=""),e="\\"===o.charAt(0)?o.slice(1):o,"\\rangle"==o&&(e="&rang;",o+=" "),"\\rVert"==o&&(e="&#8741;",o+=" "),n=Ct(0,s,e,i,o),n.blocks=[r],r.adopt(n,0,0),n})})})}}),T.right=v(P,function(t){t.parser=function(){return B.fail("unmatched \\right")}}),Dt=T.binom=T.binomial=v(v(P,p),function(t,e){t.ctrlSeq="\\binom",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-paren mq-scaled">(</span><span class="mq-non-leaf"><span class="mq-array mq-non-leaf"><span>&0</span><span>&1</span></span></span><span class="mq-paren mq-scaled">)</span></span>',t.textTemplate=["choose(",",",")"]}),T.bigg=v(v(P,p),function(t,e){t.ctrlSeq="\\bigg",t.htmlTemplate='<span class="mq-bigg"><span class="mq-non-leaf">&0</span><span style="display: inline-block;margin: 0 5px">/</span><span class="mq-non-leaf">&1</span></span>',t.textTemplate=["bigg(",",",")"],t.reflow=function(){var t=this.ends[q].jQ;dt(t.prev(),1,t.innerHeight()/+t.css("fontSize").slice(0,-2)-.1)},t.latex=function(){return this.blocks[0].latex()+"/"+this.blocks[1].latex()}}),T.lines=v(v(P,p),function(t,e){t.ctrlSeq="\\lines",t.htmlTemplate='<span class="mq-non-leaf"><span class="mq-non-leaf"><span class="mq-array mq-non-leaf"><span>&0</span><span style="height: 2px"></span><span>&1</span></span></span></span>',t.textTemplate=["lines(",",",")"],t.latex=function(){return"\\begin{matrix} "+this.blocks[0].latex()+" \\\\"+this.blocks[1].latex()+"\\end{matrix}"}}),T.choose=v(Dt,function(t){t.createLeftOf=Tt.prototype.createLeftOf}),T.editable=T.MathQuillMathField=v(P,function(t,e){t.ctrlSeq="\\MathQuillMathField",t.htmlTemplate='<span class="mq-editable-field"><span class="mq-root-block">&0</span></span>',t.parser=function(){var t=this,n=B.string,i=B.regex,s=B.succeed;return n("[").then(i(/^[a-z][a-z0-9]*/i)).skip(n("]")).map(function(e){t.name=e}).or(s()).then(e.parser.call(t))},t.finalizeTree=function(t){var e=C(this.ends[w],this.jQ,t);e.KIND_OF_MQ="MathField",e.editable=!0,e.createTextarea(),e.editablesTextareaEvents(),e.cursor.insAtRightEnd(e.root),u(e.root)},t.registerInnerField=function(t,e){t.push(t[this.name]=e(this.ends[w].controller))},t.latex=function(){return this.ends[w].latex()},t.text=function(){return this.ends[w].text()}}),Et=T.embed=v(W,function(t,e){t.setOptions=function(t){function e(){return""}return this.text=t.text||e,this.htmlTemplate=t.htmlString||"",this.latex=t.latex||e,this},t.parser=function(){var t=this,e=B.string,n=B.regex,i=B.succeed;return e("{").then(n(/^[a-z][a-z0-9]*/i)).skip(e("}")).then(function(s){return e("[").then(n(/^[-\w\s]*/)).skip(e("]")).or(i()).map(function(e){return t.setOptions(I[s](e))})})}}),Lt=c(1);for(At in Lt)!function(t,e){"function"==typeof e?(l[t]=function(){return a(),e.apply(this,arguments)},l[t].prototype=e.prototype):l[t]=e}(At,Lt[At])}();
