import './js/mathquill'
import './js/mathquill.css'
import './reset.css'

export default {
  bind (el, binding) {
    const value = [...binding.value.list]
    $(el).find('.input_box .virtual_input').each((index, ele) => {
      var mathField = MathQuill.MathField(ele, {
        restrictMismatchedBrackets: true,
        handlers: {
          edit (e) {
            const oldValue = $(ele).parent().find('input')[0].value;
            const newValue = mathField?.latex()?.replace(/\u200B/g,'');
            if (oldValue !== newValue && newValue !== undefined) {
              $(ele).parent().find('input').val(newValue);
              binding.value.input({ target: ele });
            }
            // el.input({
            //   target: {
            //     value: e.latex()
            //   }
            // })
            // console.log(e);
            // _this.value = _this.mathField.latex();
          }
        }
      })
      mathField.text = function () {
        // 获取值
        return mathField.latex();
      }
      if (value[index]) {
        mathField.latex(value[index] || '')
      }
      ele.MathQuill = mathField
    })
  },
  update (el, binding) {
    const oldValue = binding.oldValue
    const value = binding.value
    if (oldValue.showKey_color !== value.showKey_color) {
      if (!value.showKey_color) {
        $(el).find('textarea').removeAttr('readonly')
        $(el).find('input').removeAttr('readonly')
      } else {
        $(el).find('textarea').attr('readonly', 'true')
        $(el).find('input').attr('readonly', 'true')
      }
    }
  }
}
