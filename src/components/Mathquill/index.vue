<template>
  <div style="height: 100%">
    <div class="mathquill" ref="mathquill" v-if="type === 'key'"></div>
    <div class="input" v-else>
      <input ref="input" v-model="value" @click="blurEvent" @input="blurEvent" inputmode="none" />
    </div>
  </div>
</template>

<script>
import './js/mathquill'
import './js/mathquill.css'
export default {
  name: '<PERSON>qui<PERSON>',
  props: {
    content: '',
  },
  data() {
    return {
      mathField: null,
      value: '',
      type: 'sys', // sys 系统 key 公式
      // type: 'key', // sys 系统 key 公式
      blurIndex: 0,
    }
  },
  methods: {
    checkType(type) {
      this.type = type
    },
    initValue(value) {
      if (this.type === 'key') {
        var htmlElement = this.$refs.mathquill
        var mathField = MathQuill.MathField(htmlElement, {
          restrictMismatchedBrackets: true,
          substituteTextarea: function () {
            const t = document.createElement('textarea')
            t.readOnly = true
            return t
          },
          handlers: {
            edit(e) {
              // console.log(e,'e');
              // _this.value = _this.mathField.latex();
            },
          },
        })
        mathField.latex(value)
        mathField.focus()
        this.mathField = mathField
      } else {
        this.value = value
        this.inputFocusInput(this.value.length)
      }
    },
    getValue() {
      if (this.type == 'key') {
        const value = this.mathField.latex()
        return value
      } else {
        return this.getText()
      }
    },
    getText() {
      return this.value.replace(/\u200B/g, '')
    },
    clearValue() {
      if (this.type === 'key') {
        this.mathField?.latex('')
      } else {
        this.value = ''
        this.inputFocusInput(0)
      }
    },
    moveMouseLeft(i = 1) {
      if (this.type === 'key') {
        for (var a = 0; a < i; a++) {
          this.mathField.keystroke('Left')
        }
      } else {
        if (0 < this.blurIndex) {
          this.inputFocusInput(this.blurIndex - 1)
        }
      }
    },
    moveMouseRight(i = 1) {
      if (this.type === 'key') {
        for (var a = 0; a < i; a++) {
          this.mathField.keystroke('Right')
        }
      } else {
        if (this.blurIndex < this.value.length) {
          this.inputFocusInput(this.blurIndex + 1)
        }
      }
    },
    backspace() {
      if (this.type === 'key') {
        this.mathField.keystroke('Backspace')
      } else {
        const strValue = this.value.substring(0, this.blurIndex - 1)
        const endStrValue = this.value.substring(this.blurIndex, this.value.length)
        this.value = strValue + endStrValue
        this.inputFocusInput(this.blurIndex - 1)
      }
    },
    // 获取光标所在位置的index
    blurEvent() {
      this.inputFocusInput(this.$refs.input.selectionStart)
    },
    setValue(value) {
      if (this.type === 'key') {
        this.mathField.latex(value)
      } else {
        this.value = value
        const input = this.$refs.input
        input.focus()
        this.blurIndex = this.value.length - 1
        this.inputFocusInput()
      }
    },
    // index 光标移动
    append(value, index) {
      if (this.type === 'key') {
        this.mathField.write(value)
        if (index) {
          this.moveMouseLeft(index)
        }
      } else {
        const startValue = this.value.substring(0, this.blurIndex)
        const endValue = this.value.substring(this.blurIndex, this.value.length)
        this.value = startValue + value + endValue
        this.inputFocusInput(this.blurIndex + value.length)
      }
    },
    cmd(value) {
      this.mathField.cmd(value)
    },
    inputFocusInput(t) {
      if (t < 0) {
        t = 0
      }
      this.blurIndex = t
      this.$nextTick(() => {
        const input = this.$refs.input
        input.focus()
        input.setSelectionRange(t, t)
      })
    },
  },
  mounted() {},
}
</script>

<style lang="less" scoped>
.mathquill {
  font-size: 25px;
}
.input {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  input {
    width: 100%;
  }
}
</style>
