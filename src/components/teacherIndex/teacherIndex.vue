<template>
  <div style="position: relative">
    <!-- 当前时间 -->
    <div class="newTime clearfix">
      <span class="fl" style="margin-top: 23px">
        <span><img src="@/assets/index/闹钟.png" alt="" /></span>
        <span>{{ data }}</span>
        <span>{{ week }}</span>
        <span>{{ time }}</span>
      </span>
      <span v-if="this.classId" class="fg def-color">
        <span @click="handleChangeClass(item.ClassId)" class="defaluat-class" :class="{ 'color-def': classId === item.ClassId }" v-for="item in classInfo" :key="item.ClassId">{{ item.ClassName }}</span>
      </span>
    </div>
    <!-- 课程通知/数据 -->
    <div class="class">
      <ul class="class-notice">
        <!-- 班级数据 -->
        <li class="first-li">
          <div class="fl Statistics-div">
            <p class="Statistics-p1">
              <span>
                <p>{{ teacherData.TodayLoginCount }}</p>
                <p>&nbsp;&nbsp;今日登录人数</p>
              </span>
              <span class="yes-login-span">
                <p>{{ teacherData.YesterdayLoginCount }}</p>
                <p>&nbsp;&nbsp;昨日登录人数</p>
              </span>
              <span style="display: inline-block">
                <p>{{ teacherData.RegisterCount }}</p>
                <p>&nbsp;&nbsp;&nbsp;总注册人数</p>
              </span>
              <span style="display: inline-block">
                <p>{{ teacherData.TodayDoPeopleCount }}</p>
                <p>今日做练习人数</p>
              </span>
              <span style="display: inline-block">
                <p>{{ teacherData.YesterdayDoPeopleCount }}</p>
                <p>昨日做练习人数</p>
              </span>
            </p>
            <p class="Statistics-p2">
              <!-- <span style="display: inline-block;">
                <p>{{ teacherData.TodayDoPeopleCount }}</p>
                <p>今日做练习人数</p>
              </span> -->
              <!-- <span style="display: inline-block;">
                <p >{{ teacherData.YesterdayDoPeopleCount }}</p>
                <p>昨日做练习人数</p>
              </span> -->
              <span style="display: inline-block">
                <p>{{ teacherData.TotalDoPeopleCount }}</p>
                <p>累计做练习人数</p>
              </span>
              <span style="display: inline-block">
                <p>{{ teacherData.TodayDoPaperCount }}</p>
                <p>今日做练习张数</p>
              </span>
              <span style="display: inline-block">
                <p>{{ teacherData.YesterdayDoPaperCount }}</p>
                <p>昨日做练习张数</p>
              </span>
              <span style="display: inline-block">
                <p>{{ teacherData.TotalDoPaperCount }}</p>
                <p>累计做练习张数</p>
              </span>
            </p>
            <p class="Statistics-p3">
              <!-- <span >
                <p>{{ teacherData.TodayDoPaperCount }}</p>
                <p>今日做练习张数</p>
              </span>
              <span style="display: inline-block;">
                <p >{{ teacherData.YesterdayDoPaperCount }}</p>
                <p>昨日做练习张数</p>
              </span>
              <span style="display: inline-block;">
                <p >{{ teacherData.TotalDoPaperCount }}</p>
                <p>累计做练习张数</p>
              </span> -->
            </p>
          </div>
        </li>
        <li class="second-li">
          <div class="data-Statistics">
            <div style="text-align: center">
              <a-progress type="circle" :percent="teacherData.AvgCorrect" :strokeColor="teacherProgressColor" :width="150" :format="
                  () => {
                    if (teacherData.AvgCorrect === 100) {
                      return '100%'
                    } else if (!teacherData.AvgCorrect) {
                      return '...'
                    } else {
                      return teacherData.AvgCorrect + '%'
                    }
                  }
                " />
              <p>班级综合平均正确率</p>
            </div>
          </div>
        </li>
        <!-- 作业 -->
        <li class="third-li" style="text-align: center">
          <span class="arrangement mb">
            <p><img @click="toArrangmentTask" src="@/assets/index/布置作业.png" alt="" /></p>
            <p style="font-size: 16px">布置作业</p>
          </span>
          <span class="arrangement bgc">
            <p><img @click="toInspectTask" src="@/assets/index/检查作业.png" alt="" /></p>
            <p style="font-size: 16px">检查作业</p>
          </span>

          <!-- <img style="margin-bottom:48px;" @click="toArrangmentTask" src="@/assets/index/布置作业.png" alt=""> -->
          <!-- <img @click="toInspectTask" src="@/assets/index/检查作业.png" alt=""> -->
        </li>
      </ul>
    </div>
    <!-- 正确率统计 -->
    <div class="correct-rate">
      <p class="subject">
        <span class="pretice-span">本学期练习正确率趋势</span>
        <!-- <span class="fg area">
          <a-switch default-checked @change="onChange" />&nbsp;&nbsp;
          <span class="col-btn" :class="{'cur-col': !section}">全区</span> /
          <span class="col-btn" :class="{'cur-col': section}">班级</span>
        </span> -->
        <span class="fg area">
          <span @click="wholeAreaPractice" class="practice-switch" :class="{ 'def-practice': defalut === '1' }">全区练习</span>
          <span @click="schoolPractice" class="practice-switch" :class="{ 'def-practice': defalut === '2' }">校本练习</span>
          <!-- <span @click="classPractice" class="practice-switch" :class="{ 'def-practice': defalut === '3'}">班级练习</span> -->
        </span>
      </p>
      <!-- <p class="clearfix">
        <a-select class="fg" default-value="week" style="width: 120px" @change="handleChange">
          <a-select-option value="week">
            近一周
          </a-select-option>
          <a-select-option value="month">
            近一月
          </a-select-option>
          <a-select-option value="xq">
            本学期
          </a-select-option>
        </a-select>
      </p> -->
      <!-- 检查练习 -->
      <div class="example" v-if="load" style="text-align: center">
        <a-spin />
      </div>
      <div v-show="this.defalut === '1' || this.defalut === '2'" class="correct" ref="situationAnalysis"></div>
      <!-- <div v-show="this.defalut === '2' || this.defalut === '3'" class="school-partivce">
          <img src="@/assets/lack/暂无搜索记录.png" alt="">
          <p v-show="this.defalut === '2'" style="color: #ccc">暂无校本练习数据</p>
          <p v-show="this.defalut === '3'" style="color: #ccc">暂无班级练习数据</p>
        </div> -->
    </div>
    <!-- 本校和本学期统计数据 -->
    <div class="school-statistics clearfix">
      <!-- 本校练习统计 -->

      <div class="fl m-r" style="backgroundcolor: #fff; border-radius: 5px">
        <div class="example" v-if="loadSchool" style="text-align: center; width: 100%">
          <a-spin />
        </div>
        <p class="schoolAnalysis-p">
          做练习情况趋势图
          <span class="select-date">
            <a-select class="fg" default-value="week" style="width: 120px" @change="handlePracticeTrendChange">
              <a-select-option value="week"> 近一周 </a-select-option>
              <a-select-option value="month"> 近一月 </a-select-option>
              <a-select-option value="xq"> 本学期 </a-select-option>
            </a-select>
          </span>
        </p>
        <div ref="schoolAnalysis" class="schoolAnalysis"></div>
      </div>
      <!-- 本学期做题时间段统计 -->
      <div class="fl" style="backgroundcolor: #fff; border-radius: 5px">
        <div class="example" v-if="loadTime" style="text-align: center; width: 100%">
          <a-spin />
        </div>
        <p class="schoolAnalysis-p">
          本学期集中做题时段
          <span class="select-date-two">
            <a-select class="fg" default-value="week" style="width: 120px" @change="handleMakeQuestionTime">
              <a-select-option value="week"> 近一周 </a-select-option>
              <a-select-option value="month"> 近一月 </a-select-option>
              <a-select-option value="xq"> 本学期 </a-select-option>
            </a-select>
          </span>
        </p>
        <div ref="newsemesterAnalysis" class="schoolAnalysis"></div>
        <!-- <div ref="semesterAnalysis" class="schoolAnalysis make-question" style="width:787px"></div> -->
        <!-- <canvas id="myCanvas" width="390" height="340" class="time-mr" style="">
          Your browser does not support the HTML5 canvas tag.
        </canvas> -->
      </div>
    </div>
    <!-- <p>Copyright©上海有我科技有限公司，All Rights Reserved</p> -->
    <div style="position: relative; margin-top: 170px">
      <footerLogo></footerLogo>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import '@/utils/utils.less'
import echarts from 'echarts'
import footerLogo from '@/components/XuHuiFooter/FooterLogo'

export default {
  components: {
    footerLogo
  },
  created() {
    const data = moment(new Date()).format('YYYY年MM月DD日')
    this.timer = window.setInterval(() => {
      this.time = moment(new Date()).format('a h:mm:ss')
    }, 1000)
    const week = moment(new Date()).format('dddd')
    this.data = data
    this.week = week
    this.UserName = localStorage.getItem('mobilePhone')
    // 获取用户信息
    this.getUserInfo()
  },
  mounted() {
    this.init()
  },
  watch: {
    $route() {
      // const ChangId = window.location.href.split('?')[1]
      if (this.$route.fullPath.indexOf('/Home/Introduce') !== -1) {
        // window.clearInterval(this.timer)
        // this.init()
        // this.getLatelyCorrect()
        this.getClassData()
        this.getPracticeTrend()
        this.getMakeQuestion()
        // 通知
        this.NoticeRecord()
      }
    }
  },
  data() {
    return {
      load: true,
      loadSchool: true,
      loadTime: true,
      teacherProgressColor: '#3CB98F',
      // 本学期校本切换
      areaTypeId: 0,
      // 全区/班级
      section: false,
      // 日期
      data: '',
      // 星期
      week: '',
      // 时间
      time: '',
      timer: '',
      // 角色ID
      userId: '',
      // 班级ID
      classId: '',
      // 用户名
      UserName: '',
      // 老师班级信息
      classInfo: [],
      // 老师班级信息
      teacherData: {},
      // 区/班
      classOrArea: 1,
      // 近期正确率区间
      correctTime: 'week',
      // 近期练习趋势区间
      PracticeTrend: 'week',
      // 集中做题时段
      MakeQuestionTime: 'week',
      Notice: {},
      // 历史通知
      historyNoticeList: [],
      red: '',
      // 本校做练习情况趋势图Y轴最大值
      schoolAnalysisId: '',
      defalut: '1',
      backgroundImg: false,
      // 做题时间段
      makeQues: []
    }
  },
  methods: {
    init() {
      const myChart = echarts.init(this.$refs.situationAnalysis)
      myChart.setOption({
        title: {
          text: '(百分比：%)' // 正确率
        },
        dataZoom: [
          {
            start: 0, // 默认为0
            end: 30, // 默认为100
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            handleSize: 0, // 滑动条的 左右2个滑动条的大小
            height: 14, // 组件高度
            // left: '10%', // 左边的距离
            // right: '10%', // 右边的距离
            bottom: 0, //
            border: 'none',
            borderColor: '#fff',
            fillerColor: '#B5BEB9',
            borderRadius: 7,
            backgroundColor: '#F8F8F8', // 两边未选中的滑动条区域的颜色
            showDataShadow: false, // 是否显示数据阴影 默认auto
            showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
            realtime: true, // 是否实时更新
            filterMode: 'filter'
          }
        ],
        // 提示框内容
        tooltip: {
          trigger: 'axis'
          // formatter: function (params) {
          //   return `${params[0].data}%`
          // }
        },
        // 自定义颜色
        color: ['#FF8B7F', '#71B6A1', '#52B4FF'],
        legend: {
          data: ['我班正确率', '我校正确率', '全区正确率']
          // selected: { '我班正确率': true, '我校正确率': false, '全区正确率': false }
          // (点击其他)默认只展示一条折线
          // selectedMode: 'single'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            // saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}%'
            // formatter: function (value, index) {  //Y轴的自定义刻度值，对应上图
            //             if (index == 0) {
            //                 value = 0
            //             } else if (index == 1) {
            //                 value = 60
            //             } else if (index == 2) {
            //                 value = 70
            //             } else if (index == 3) {
            //                 value = 80
            //             } else if (index == 4) {
            //                 value = 90
            //             } else if (index == 5) {
            //                 value = 100
            //             }
            //             return value+'%';
            //         }
          },
          triggerEvent: true,
          max: '100'
          // splitNumber: 2
        },
        series: [
          {
            name: '我班正确率',
            type: 'line',
            // stack: '总量',
            data: [65, 78, 82, 75, 69, 73, 98, 89, 73, 98, 73, 98, 73, 78, 82, 75],
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1, // 设置线宽
                  type: 'solid' // 'dotted'虚线 'solid'实线
                }
                // pointer-events: none;
                // cursor: defalut
              }
            }
            // 背景阴影渐变
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //     offset: 0,
            //     color: 'rgba(250, 69, 56, 0.5)'
            //   }, {
            //     offset: 0.8,
            //     color: 'rgba(255, 255, 255, 1)'
            //   }])
            // }
          },
          {
            name: '我校正确率',
            type: 'line',
            // stack: '总量',
            data: [73, 98, 89, 73, 98, 73, 70, 85, 89, 73, 98, 73, 98, 73, 98, 73, 98],
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1, // 设置线宽
                  type: 'solid' // 'dotted'虚线 'solid'实线
                }
              }
            }
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //     offset: 0,
            //     color: 'rgba(113, 182, 161, 1)'
            //   }, {
            //     offset: 1,
            //     color: 'rgba(255, 255, 255, 1)'
            //   }])
            // }
          },
          {
            name: '全区正确率',
            type: 'line',
            // stack: '总量',
            data: [65, 78, 82, 73, 98, 73, 98, 73, 89, 73, 98, 73, 98, 73, 98, 73, 98],
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1, // 设置线宽
                  type: 'dotted' // 'dotted'虚线 'solid'实线
                }
              }
            }
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //     offset: 0,
            //     color: 'rgba(0, 135, 204, 0.7)'
            //   }, {
            //     offset: 1,
            //     color: 'rgba(5, 26, 51,0)'
            //   }])
            // }
          }
        ]
      })
      const schoolAnalysis = echarts.init(this.$refs.schoolAnalysis)
      schoolAnalysis.setOption({
        title: {
          // text: '本校做练习情况趋势图',
          // textAlign: 'left'
        },

        dataZoom: [
          {
            start: 0, // 默认为0
            end: 100 - 1500 / 50, // 默认为100
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            handleSize: 0, // 滑动条的 左右2个滑动条的大小
            height: 14, // 组件高度
            // left: '10%', // 左边的距离
            // right: '10%', // 右边的距离
            bottom: 0, // 右边的距离
            border: 'none',
            borderColor: '#fff',
            fillerColor: '#B5BEB9',
            borderRadius: 7,
            backgroundColor: '#F8F8F8', // 两边未选中的滑动条区域的颜色
            showDataShadow: false, // 是否显示数据阴影 默认auto
            showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
            realtime: true, // 是否实时更新
            filterMode: 'filter'
          }
          // {
          //   height: 14,
          //   type: 'slider',
          //   show: true,
          //   xAxisIndex: [0],
          //   // left: '20%',
          //   bottom: 0,
          //   start: 10,
          //   end: 100
          // }
        ],
        // 提示框内容
        tooltip: {
          trigger: 'axis'
          // formatter: function (params) {
          //   return `${params[0].data}%`
          // }
        },
        // 自定义颜色
        color: ['#FF8B7F', '#088767', '#52B4FF'],
        legend: {
          data: ['登录人数', '做练习张数', '做练习人数']
          // selected: { '我班正确率': true, '我校正确率': false, '全区正确率': false }
          // (点击其他)默认只展示一条折线
          // selectedMode: 'single'
        },

        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {},
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value',
          max: this.schoolAnalysisId,
          axisLabel: {
            formatter: '{value}'
          },
          triggerEvent: true
        },
        series: [
          {
            name: '登录人数',
            type: 'line',
            // stack: '总量',
            data: [65, 78, 82, 75, 69, 73, 98, 89, 73, 98, 73, 98, 73, 78, 82, 75],
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1, // 设置线宽
                  type: 'solid' // 'dotted'虚线 'solid'实线
                }
              }
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(255, 139, 127, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(251, 250, 249, 0)'
                }
              ])
            }
          },
          {
            name: '做练习张数',
            type: 'line',
            // stack: '总量',
            data: [220, 182, 191, 234, 290, 330, 310, 125, 89, 73, 98, 73, 98, 73, 98, 73, 98],
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1, // 设置线宽
                  type: 'solid' // 'dotted'虚线 'solid'实线
                }
              }
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(113, 182, 161, 0.7)'
                },
                {
                  offset: 1,
                  color: 'rgba(229, 241, 237, 0)'
                }
              ])
            }
          },
          {
            name: '做练习人数',
            type: 'line',
            // stack: '总量',
            data: [150, 232, 201, 154, 190, 330, 410, 165, 89, 73, 98, 73, 98, 73, 98, 73, 98],
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1, // 设置线宽
                  type: 'dotted' // 'dotted'虚线 'solid'实线
                }
              }
            }
            // areaStyle: {
            //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //     offset: 0,
            //     color: 'rgba(0, 135, 204, 0.7)'
            //   }, {
            //     offset: 1,
            //     color: 'rgba(5, 26, 51,0)'
            //   }])
            // }
          }
        ]
      })

      // 新做题时段
      const newsemesterAnalysis = echarts.init(this.$refs.newsemesterAnalysis)
      newsemesterAnalysis.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#a5c244',
              width: 2
            }
          }
        },
        angleAxis: {
          type: 'category',
          data: ['0', '2', '4', '6', '8', '10', '12', '14', '16', '18', '20', '22'],
          boundaryGap: false,
          axisLabel: {
            rich: {
              '<style_name>': {
                align: 'center'
              }
            },
            show: true
          },
          axisLine: {
            show: false
          },
          axisTick: {
            lineStyle: {
              width: 0.5
            },
            length: 3
          }
        },
        radiusAxis: {
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        polar: {},
        series: [
          {
            type: 'bar',
            data: [1, 2, 3, 4, 3, 5, 1],
            coordinateSystem: 'polar',
            stack: 'a',
            itemStyle: { color: '#43715A' }
          }
        ],
        legend: {}
      })
      // var c = document.getElementById('myCanvas')
      // var ctx = c.getContext('2d')
      // ctx.beginPath()
      // ctx.lineWidth = 2
      // ctx.strokeStyle = '#eee'
      // ctx.arc(204, 159, 135, 0, 2 * Math.PI)
      // ctx.stroke()

      // // 时刻度
      // ctx.save()
      // ctx.translate(204, 159)

      // var hour = [6, '', 8, '', 10, '', 12, '', 14, '', 16, '', 18, '', 20, '', 22, '', 0, '', 2, '', 4, '']
      // for (var i = 0; i < 24; i++) {
      //   const v = Math.PI / 12
      //   ctx.rotate(v)
      //   ctx.beginPath()
      //   ctx.moveTo(0, -50)
      //   ctx.lineTo(0, -135)
      //   ctx.closePath()
      //   ctx.lineWidth = 0.5
      //   ctx.setLineDash([3, 3])
      //   ctx.strokeStyle = '#ccc'
      //   ctx.stroke()
      // }
      // ctx.restore()

      // hour.forEach(function (num, i) {
      //   var rad = 2 * Math.PI / 24 * i
      //   var x = Math.cos(rad) * 150
      //   var y = Math.sin(rad) * 150
      //   ctx.font = '12px sans-serif'
      //   ctx.textAlign = 'center'
      //   ctx.textBaseline = 'middle'
      //   ctx.fillStyle = '#000'
      //   ctx.fill()
      //   ctx.fillText(num, x + 204, y + 159)
      // })
      // 新的时间段

      // 设置图表随窗口大小变化
      window.addEventListener('resize', function() {
        myChart.resize()
        schoolAnalysis.resize()
        newsemesterAnalysis.resize()
      })
    },
    // 全区练习
    wholeAreaPractice() {
      this.defalut = '1'
      // this.getLatelyCorrect()
      this.areaTypeId = 0
      this.getClassData()
    },
    // 校本练习
    schoolPractice() {
      this.defalut = '2'
      this.areaTypeId = 1
      this.getClassData()
    },
    // 班级练习
    classPractice() {
      this.defalut = '3'
    },
    // 通知列表
    NoticeRecord() {
      this.$http
        .post('/Notice/NoticeRecord/GetNoticeRecordListToday', {
          Type: 0,
          Day: '',
          Content: '',
          PageNo: 1,
          Today: 0,
          classId: this.classId
        })
        .then(res => {
          this.Notice = res.Data[0]
          if (res.Data.length === 0) {
            this.historyNotice()
          } else {
            this.backgroundImg = true
          }
        })
    },
    // 历史通知
    historyNotice() {
      this.$http
        .post('/Notice/NoticeRecord/GetNoticeRecordList', {
          Type: 0,
          Day: '',
          Content: '',
          PageNo: 1,
          Today: 1,
          classId: this.classId
        })
        .then(res => {
          if (res.Data.length !== 0) {
            this.historyNoticeList = res.Data[0]
            this.backgroundImg = true
          }
        })
    },
    handleOk(e) {
      this.visible = false
    },
    handleCancel(e) {
      this.visible = false
    },
    HistoricalNotice() {
      this.$router.push({
        path: '/Teacher/My_Notice/NoticeRecordInfo',
        query: {
          classId: this.classId
        }
      })
    },
    // 获取用户信息
    async getUserInfo() {
      const res = await this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.UserName })
      console.log(res, '教师用户信息')
      this.userId = res.Data.User.Id
      this.classId = res.Data.ClassInfo[0].ClassId
      this.classInfo = res.Data.ClassInfo
      localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
      // 获取登录，注册等信息
      this.teacherStatistics()
      // 获取近期正确率数据
      // this.getLatelyCorrect()
      this.getClassData()
      // 获取练习情况趋势图
      this.getPracticeTrend()
      // 获取做题时段
      this.getMakeQuestion()
      // 通知
      this.NoticeRecord()
    },
    // 登录练习，做卷等信息
    teacherStatistics() {
      var that = this
      this.$http
        .post('/HomePageDataView/TeacherDataView/GetTeacherStatistics', { userId: this.userId, classId: this.classId })
        .then(res => {
          that.teacherData = res.Data
        })
    },
    // 获取近期正确率数据
    getLatelyCorrect() {
      // this.$http.post('/HomePageDataView/TeacherDataView/GetPracticeAccuracy', { userId: this.userId, type: '0', classId: this.classId, classOrArea: this.classOrArea, whereTime: this.correctTime }).then(res => {
      //   const list = res.Data.ListAccuracy
      //   const PaperTitle = list.map(item => {
      //     return item.PaperTitle
      //   })
      //   const Class = list.map(item => {
      //     return item.Class
      //   })
      //   const School = list.map(item => {
      //     return item.School
      //   })
      //   const Area = list.map(item => {
      //     return item.Area
      //   })
      //   const myChart = echarts.init(this.$refs.situationAnalysis)
      //   myChart.setOption({
      //     xAxis: {
      //       data: PaperTitle
      //     },
      //     series: [
      //       {
      //         data: Class
      //       },
      //       {
      //         data: School
      //       },
      //       {
      //         data: Area
      //       }
      //     ]
      //   })
      //   this.load = false
      // })
    },
    getClassData() {
      this.$uwonhttp
        .post('/Report/TeacherReport/GetAnalyticsFromTeacher', {
          classId: this.classId,
          userId: this.userId,
          areaType: this.areaTypeId
        })
        .then(res => {
          this.classMakeSubject = res.data.Data
          const classData = res.data.Data.Line
          const chapter = classData.map(item => {
            return item.PaperTitle
          })
          const Class = classData.map(item => {
            return item.Class
          })
          const School = classData.map(item => {
            return item.School
          })
          const Area = classData.map(item => {
            return item.Area
          })
          // 图表分析
          const myChart = echarts.init(this.$refs.situationAnalysis)
          myChart.setOption({
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: chapter
            },
            series: [
              {
                data: this.getData(Class)
              },
              {
                data: this.getData(School)
              },
              {
                data: this.getData(Area)
              }
            ]
          })
          this.load = false
        })
    },
    // 获取练习情况趋势图
    getPracticeTrend() {
      this.$http
        .post('/HomePageDataView/TeacherDataView/GetWebExercises', {
          classId: this.classId,
          whereTime: this.PracticeTrend
        })
        .then(res => {
          const arr = []
          res.Data.forEach(item => {
            arr.push(item['DoPaperCount'])
            arr.push(item['DoPeopleCount'])
            arr.push(item['LoginCount'])
          })
          this.schoolAnalysisId = Math.max(...arr)
          const time = res.Data.map(item => {
            return item.Time
          })
          const loginCount = res.Data.map(item => {
            return item.LoginCount
          })
          const DoPaperCount = res.Data.map(item => {
            return item.DoPaperCount
          })
          const DoPeopleCount = res.Data.map(item => {
            return item.DoPeopleCount
          })
          const schoolAnalysis = echarts.init(this.$refs.schoolAnalysis)
          schoolAnalysis.setOption({
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: time
            },
            series: [
              {
                data: loginCount
              },
              {
                data: DoPaperCount
              },
              {
                data: DoPeopleCount
              }
            ],
            yAxis: {
              type: 'value',
              max: this.schoolAnalysisId,
              axisLabel: {
                formatter: '{value}'
              },
              triggerEvent: true
            }
          })
          this.loadSchool = false
        })
    },
    // 获取做题时段
    getMakeQuestion() {
      this.$http
        .post('/HomePageDataView/TeacherDataView/GetTimeInterval', {
          classId: this.classId,
          userId: this.userId,
          whereTime: this.MakeQuestionTime
        })
        .then(res => {
          this.makeQues = res.Data
          const StuCount = res.Data.map(item => {
            return item.StuCount
          })
          const time = res.Data.map(item => {
            return item.Time
          })
          // const valueNum = []
          const stuNum = []

          for (var i = 0; i < StuCount.length; i++) {
            stuNum.push({ value: [StuCount[i], time[i]] })
          }

          const newsemesterAnalysis = echarts.init(this.$refs.newsemesterAnalysis)
          newsemesterAnalysis.setOption({
            tooltip: {
              trigger: 'axis',
              formatter: params => {
                let stuNum = ''
                this.makeQues.forEach(value => {
                  if (params[0].name === value.Time) {
                    stuNum = value.Proportion
                  }
                })
                return (
                  '时间:' + params[0].name + '<br>' + '人数:' + params[0].value + '<br>' + '人数占比:' + stuNum + '%'
                )
              }
            },
            series: [
              {
                data: StuCount,
                itemStyle: { color: '#43715A' }
              }
            ]
          })
          this.loadTime = false
        })
    },
    // 选择班级
    handleChangeClass(value) {
      this.classId = value
      // 获取近期正确率数据
      // this.getLatelyCorrect()
      this.getClassData()
      this.teacherStatistics()
      this.getPracticeTrend()
      this.getMakeQuestion()
      this.NoticeRecord()
    },
    // 选择近期练习正确率图表维度
    handleChange(value) {
      this.correctTime = value
      this.load = true
      // 获取近期正确率数据
      // this.getLatelyCorrect()
      this.getClassData()
    },
    // 近期练习情况趋势图
    handlePracticeTrendChange(value) {
      this.PracticeTrend = value
      this.loadSchool = true
      this.getPracticeTrend()
    },
    // 集中做题时段
    handleMakeQuestionTime(value) {
      this.MakeQuestionTime = value
      this.loadTime = true
      this.getMakeQuestion()
    },
    // 全区/班级选择
    onChange(checked) {
      this.section = !checked
      if (checked) {
        this.classOrArea = 1
      } else {
        this.classOrArea = 0
      }
      // 获取近期正确率数据
      // this.getLatelyCorrect()
      this.getClassData()
    },
    // 跳转布置作业
    toArrangmentTask() {
      // this.$router.push({ path: '/Teacher/My_Task/ArrangementTask' })
      this.$router.push({ path: '/Teacher/My_Task/ArrangmentPracticeAttribute' })
    },
    // 跳转检查练习
    toInspectTask() {
      this.$router.push({ path: '/Teacher/My_Task/AllPractice' })
    },
    // 处理折线图y轴显示
    getData(temp) {
      var jsonString = []
      for (var i = 0; i < temp.length; i++) {
        var json = {}
        json.name = temp[i] > 100 ? 0 : temp[i]
        json.value = this.initPointData(temp[i])
        jsonString.push(json)
      }
      return jsonString
    },
    // 折线图坐标点算法
    initPointData(pointData) {
      var baseNum = 20
      var returnPosition = 0
      if (pointData <= 60) {
        returnPosition = pointData / 3
      } else if (pointData > 60 && pointData <= 100) {
        returnPosition = (pointData % 60) * 2 + baseNum
      } else {
        returnPosition = 0
      }
      return returnPosition
    }
  }
}
</script>

<style lang="less" scoped>
.notice {
  color: #c8de82;
}
// 统计
.Statistics-div {
  margin-left: 60px;
}
.Statistics-p1 {
  margin-top: 35px;
  span {
    margin-right: 20px;
  }
  span:nth-child(1) {
    display: inline-block;
    // width: 98px;
    // margin-right: 25px;
  }
  span:nth-child(2) {
    display: inline-block;
    // width: 98px;
    // margin-right: 25px;
  }
}
.Statistics-p2 {
  margin: 30px 0;
  span {
    margin-right: 20px;
  }
}
.Statistics-p3 {
  span:nth-child(1) {
    display: inline-block;
    // width: 98px;
    margin-right: 25px;
  }
}
// 做题时段的
.time-mr {
  margin-top: -335px;
  margin-left: 200px;
}
#echarts5 {
  width: 100%;
  height: 80%;
  position: absolute;
  // border: 1px solid black;
}
// 校本练习
.school-partivce {
  text-align: center;
}
// 通知列表
.text-over-flow {
  font-size: 16px;
}
// 通知时间
.notice-create-time {
  position: absolute;
  right: 77px;
  bottom: 0;
  font-size: 18px;
}
.arrangement {
  // display: inline-block;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 273px;
  height: 100%;
  margin-left: 20px;
  text-align: center;
  background-color: #ece200;
  border-radius: 5px;
  cursor: pointer;
  p:nth-child(1) {
    margin-top: 29px;
  }
}
.img-his {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(165, 166, 167);
}
.def-color {
  .color-def {
    border: none;
    color: #fff;
    background-color: #68bb97;
  }
}
.new-notice {
  margin-bottom: 25px;
  margin-top: 20px;
  span:nth-child(1) {
    font-size: 20px;
  }
}
.defaluat-class {
  // display: inline-block;
  width: 100px;
  height: 40px;
  line-height: 40px;
  margin-top: 20px;
  margin-right: 35px;
  padding: 10px 15px;
  text-align: center;
  border-radius: 20px;
  color: #b5b5b5;
  // background-color: #F4F4F4;
  border: 1px solid #b5b5b5;
  cursor: pointer;
}
.col-btn {
  color: #ddd;
}
.cur-col {
  color: #68bb97;
  cursor: pointer;
}
/deep/.ant-select-selection--single {
  right: 52px;
}
.select-date {
  display: inline-block;
  position: relative;
  right: -34%;
}
.select-date-two {
  display: inline-block;
  position: relative;
  right: -34%;
}
// 历史
.history {
  font-size: 16px;
  padding: 6px 6px 0 0;
  color: RGBA(200, 234, 77, 1);
  cursor: pointer;
}
// 时间
.newTime {
  margin-bottom: 20px;
  span {
    margin-right: 20px;
    img {
      width: 26px;
    }
  }
}

// 课程通知/数据
.class {
  .class-notice {
    display: flex;
    margin-bottom: 40px;
    // width: 80%;
    // margin-right: 100px;
    .first-li {
      // padding-bottom: 20px;
      padding-right: 60px;
    }
    .second-li {
      background-color: rgb(189, 211, 206);
      border-radius: 5px;
    }
    .third-li {
      display: flex;
      margin-right: 0;
    }
    li {
      // width: 48%;
      // float: left;
      // flex: 1.6;
      margin-right: 40px;
      // padding: 8px 35px;
      .bgc {
        background-color: #c4d400;
      }
      .mb {
        margin-bottom: 9px;
      }
    }
    // li:nth-child(1) {
    //   // height: 254px;
    //   position: relative;
    //   overflow: hidden;
    //   white-space:nowrap;
    //   text-overflow: ellipsis;
    //   border-radius: 5px;
    //   background-color: #9AC313;
    // }
    .background {
      background: #9ac313 url('../../assets/index/小喇叭.png') no-repeat 75%;
    }
    li:nth-child(1) {
      border-radius: 5px;
      background-color: rgb(189, 211, 206);
      p {
        span:nth-child(2) {
          // margin-right: 50px;
          p:nth-child(1) {
            // font-size: 18px;
            font-weight: 400;
            margin-bottom: 0;
            // .yes-login-span {
            //   // margin-right: 79px;
            // }
          }
        }
        span:nth-child(2) {
          // margin-right: 55px;
          p:nth-child(1) {
            // font-size: 18px;
            font-weight: 400;
            margin-bottom: 0;
          }
        }
        span {
          text-align: center;
          p:nth-child(1) {
            margin-bottom: 0;
            font-size: 25px;
          }
        }
      }
    }
    li:nth-child(2) {
      flex: 1.5;
      margin: 0;
      padding: 0;
      // text-align: right;
      img {
        cursor: pointer;
      }
    }
    .data-Statistics {
      // margin-right: 200px;
      /deep/.ant-progress-text {
        font-size: 30px;
      }
      margin-top: 6%;
      p:nth-child(1) {
        font-size: 20px;
        text-align: center;
      }
      div {
        // margin-top: -10px;
        p:nth-child(2) {
          margin-top: 10px;
        }
      }
    }
  }
}
// 正确率题目标题
.subject {
  text-align: center;
  margin-top: 50px;
  .area {
    position: absolute;
    right: 52px;
  }
  .practice-switch {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    border-radius: 20px;
    color: #b5b5b5;
    border: 1px solid #b5b5b5;
    cursor: pointer;
  }
  .def-practice {
    color: #fff;
    background-color: #68bb97;
    border: none;
  }
  .pretice-span {
    font-size: 22px;
    margin-bottom: 20px;
    color: #4d5753;
  }
  span:nth-of-type(2) {
    margin-left: 8px;
    margin-right: 8px;
  }
}
// 正确率统计
.correct-rate {
  padding: 15px 15px;
  background-color: #fff;
  border-radius: 5px;
}
// 正确率
.accuracy {
  margin-bottom: 20px;
}
// 正确率统计
.correct {
  height: 437px;
}
// 进度条
.ant-progress-line {
  width: 350px;
}
.ant-progress-inner {
  background-color: #ddd;
}
.accuracy-details /deep/ .ant-progress-inner {
  background-color: red;
}
// 本校和本学期统计数据
.school-statistics {
  height: 475px;
  margin-top: 35px;
  div {
    display: inline-block;
    width: 49%;
    padding: 25px 25px 0 0;
    box-sizing: border-box;
    p {
      text-align: center;
    }
    .schoolAnalysis {
      width: 100%;
      height: 442px;
    }
  }
  .m-r {
    margin-right: 19px;
  }
  .schoolAnalysis-p {
    font-size: 22px;
  }
}
.make-question {
  position: relative;
  left: 23px;
  top: 32px;
}
// 试题分析
.title-analysis {
  position: relative;
  margin-top: 20px;
  // 发起辅导
  .launch-coach {
    float: right;
    width: 104px;
    height: 34px;
    line-height: 34px;
    margin-top: 12px;
    margin-right: 34%;
    text-align: center;
    border-radius: 17px;
    background-color: #00aaff;
    cursor: pointer;
  }
  // 试卷内容
  .paper-info {
    font-size: 20px;
    margin-left: 30px;
    margin-right: 319px;
  }
  // 对应答案和解析 班级正确详情
  .answer-ana-details {
    margin-right: 319px;
    p {
      margin-bottom: 20px;
    }
  }
  // 正确详情
  .accuracy-details {
    .ant-progress-bg {
      background-color: green;
    }
    span {
      color: #00aaff;
    }
    p:nth-child(1) {
      span {
        color: #00aaff;
        cursor: pointer;
      }
    }
  }
}
#zr_0 {
  position: absolute;
  left: 23px;
  top: 32px;
}
</style>
