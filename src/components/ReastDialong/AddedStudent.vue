<template>
  <el-dialog title="添加学生" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false">
    <el-form :label-position="labelPosition" label-width="80px" :model="formLabel" :rules="rules" ref="formLabel">
      <el-form-item label="姓名" prop="UserName">
        <el-input v-model="formLabel.UserName" placeholder="请输入姓名"></el-input>
      </el-form-item>
      <el-form-item label="学号" prop="studyNo">
        <el-input v-model="formLabel.studyNo" placeholder="请输入学号（1-99）" autocomplete="off" oninput="value=value.replace(/^\.+|[^\d.]/g,'')" @blur="salaryChange"></el-input>
      </el-form-item>
      <el-form-item label="学校" prop="SchoolName">
        <el-select v-model="formLabel.SchoolName" filterable placeholder="请选择学校" @change="getScholls">
          <el-option v-for="item in GetSchoolsList" :key="item.SchoolID" :label="item.SchoolName" :value="{value:item.SchoolID,label:item.SchoolName}">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="班级" prop="ClassName">
        <el-select v-model="formLabel.ClassName" filterable placeholder="请选择班级" @change="getClass" :disabled="disabled">
          <el-option v-for="item in GetClassList" :key="item.ClassId" :label="item.ClassName" :value="{value:item.ClassId,label:item.ClassName}">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="教师名称" prop="TeacherName">
        <el-input v-model="formLabel.TeacherName" placeholder="请输入教师名称" :disabled="disabled"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible  = false">取 消</el-button>
      <el-button type="primary" @click="submitForm('formLabel')">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      userId: '',
      PhoneNo: '',
      dialogVisible: false,
      labelPosition: 'right',
      disabled: true,
      formLabel: {
        UserName: '',
        SchoolName: '',
        SchoolId: '',
        ClassName: '',
        ClassId: '',
        TeacherName: '',
        studyNo: ''
      },
      GetSchoolsList: [],
      GetClassList: [],
      rules: {
        UserName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        studyNo: [
          { required: true, message: '请输入学号', trigger: 'blur' },
          { min: 1, max: 2, message: '请输入1-99之间的数字', trigger: 'blur' }
        ],
        SchoolName: [{ required: true, message: '请选择学校', trigger: 'blur' }],
        ClassName: [{ required: true, message: '请选择班级', trigger: 'blur' }],
        TeacherName: [{ required: true, message: '请输入教师', trigger: 'blur' }]
      }
    }
  },
  computed: {
    newObj() {
      return {
        SchoolName: this.formLabel.SchoolName
      }
    }
  },
  watch: {
    newObj: {
      handler(val) {
        if (val !== '') {
          this.disabled = false
        }
      },
      deep: true
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.PhoneNo = localStorage.getItem('PhoneNum')
  },

  mounted() {},
  methods: {
    openDialog() {
      this.dialogVisible = true
    },
    //学校信息
    async GetSchools() {
      const res = await this.$uwonhttp.post('/Report/School/GetSchools')
      this.GetSchoolsList = res.data.Data
    },
    //获取学校
    async getScholls(params) {
      this.formLabel.SchoolName = params.label
      this.formLabel.SchoolId = params.value
      this.formLabel.ClassName = ''
      this.formLabel.TeacherName = ''
      await this.GetClassBySchoolId(this.formLabel.SchoolId)
    },
    //查询班级
    async GetClassBySchoolId(value) {
      const res = await this.$uwonhttp.get('/Class/ClassManager/GetClassBySchoolId', {
        params: {
          schoolId: value
        }
      })
      this.GetClassList = res.data.Data
    },
    //获取班级
    async getClass(params) {
      this.formLabel.ClassName = params.label
      this.formLabel.ClassId = params.value
      await this.GetTeacherName(this.formLabel.ClassId)
    },
    //获取教师姓名
    async GetTeacherName(value) {
      const res = await this.$uwonhttp.get('/User/UserManager/GetTeacherName', {
        params: {
          classId: value
        }
      })
      this.formLabel.TeacherName = res.data.Data
    },
    //新增
    async submitForm(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          let params = {
            UserName: this.formLabel.UserName,
            SchoolId: this.formLabel.SchoolId,
            ClassId: this.formLabel.ClassId,
            StudyNo: this.formLabel.studyNo,
            PhoneNo: this.PhoneNo
          }
          const res = await this.$uwonhttp.post('/User/UserManager/BindStudent', params)
          if (res.data.Success == true) {
            this.dialogVisible = false
            // this.$message.success('添加成功')
            this.$confirm('添加成功!','提示',{
              confirmButtonText:'确定',
              type:'success'
            }).then(()=>{
              this.$router.go(0)
            })
          } else {
            this.$message.error(res.data.Msg)
            this.dialogVisible = false
          }
        } else {
          return false
        }
      })
    },

    salaryChange(e) {
      this.formLabel.studyNo = e.target.value
    },
    clearRuleForm() {}
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}
/deep/ .el-dialog__body {
  border-bottom: 1px solid #ebebeb;
}
/deep/ .el-select {
  display: inline-block;
  position: relative;
  width: 100%;
}
</style>
