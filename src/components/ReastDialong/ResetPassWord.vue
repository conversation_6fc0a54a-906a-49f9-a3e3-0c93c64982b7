<template>
  <el-dialog title="修改密码" :visible.sync="FormPassWorddialog" width="25%" :close-on-click-modal="false">
    <el-form :model="ruleForm" ref="ruleForm" :rules="rules">
      <el-form-item prop="oldPwd">
        <el-input v-model="ruleForm.oldPwd" type="password" placeholder="请输入原密码" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item prop="newPwd">
        <el-input v-model="ruleForm.newPwd" type="password" placeholder="请输入新密码" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item prop="getNewPwd">
        <el-input v-model="ruleForm.getNewPwd" type="password" placeholder="请再次输入密码" autocomplete="off"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="FormPassWorddialog = false">取 消</el-button>
      <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import TokenCache from '@/utils/cache/TokenCache'

export default {
  props: {},
  data() {
    var checkAge = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('原密码不能为空'))
      } else {
        callback()
      }
    }
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (this.ruleForm.getNewPwd !== '') {
          this.$refs.ruleForm.validateField('getNewPwd')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.ruleForm.newPwd) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      userId: '',
      FormPassWorddialog: false,
      ruleForm: {
        oldPwd: '',
        newPwd: '',
        getNewPwd: ''
      },
      rules: {
        newPwd: [{ validator: validatePass, trigger: 'blur' }],
        getNewPwd: [{ validator: validatePass2, trigger: 'blur' }],
        oldPwd: [{ validator: checkAge, trigger: 'blur' }]
      }
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
  },
  mounted() {},
  methods: {
    openDialog() {
      this.FormPassWorddialog = true
    },
    clearRuleForm() {
      this.ruleForm = {}
    },
    async submitForm(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          let params = {
            userId: this.userId,
            oldPwd: this.ruleForm.oldPwd,
            newPwd: this.ruleForm.newPwd
          }
          const res = await this.$uwonhttp.post('/User/UserManager/UpdatePwdByOld', params)
          if (res.data.Success != true) {
            this.$message.error(res.data.Msg)
            this.FormPassWorddialog = false
            this.$router.go(0)
          } else {
            this.$message.success(res.data.Msg)
            this.FormPassWorddialog = false
            TokenCache.deleteToken()
            location.reload()
            localStorage.removeItem('UserId')
            localStorage.removeItem('passWord')
            localStorage.removeItem('isTeacher')
            localStorage.removeItem('isStudent')
            localStorage.removeItem('isSchoolHeader')
            localStorage.removeItem('IsVip')
            localStorage.removeItem('role')
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}
/deep/ .el-dialog__body {
  border-bottom: 1px solid #ebebeb;
}
</style>
