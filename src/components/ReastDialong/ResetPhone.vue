<template>
  <el-dialog title="修改手机号" :visible.sync="FormPhonedialog" width="25%" :close-on-click-modal="false">
    <el-form :model="FormPhone" :rules="changeRules" ref="FormPhone" :hide-required-asterisk="true">
      <el-form-item prop="phoneNum">
        <el-input v-model="FormPhone.phoneNum" prefix-icon="el-icon-mobile-phone" placeholder="请输入手机号" autocomplete="off"></el-input>
      </el-form-item>
      <div class="clearfloat">
        <div class="code_input">
          <el-form-item prop="newCode">
            <el-input v-model="FormPhone.newCode" placeholder="验证码" prefix-icon="el-icon-lock" autocapitalize="off" autocomplete="off" spellcheck="false"></el-input>
          </el-form-item>
        </div>
        <div class="code_btn">
          <el-button slot="append" type="info" class="getMessage" :disabled="codeCountdown > 0" v-html="codeBtnText" @click="changeRestart">
          </el-button>
        </div>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="FormPhonedialog = false">取 消</el-button>
      <el-button type="primary" @click="submitChange('FormPhone')">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { regEx } from '@/utils/regEx'
import { getCode } from '@/mixins/getCode'
const validateTel = (rule, value, callback) => {
  if (value === undefined) {
    callback(new Error('请输入手机号码'))
  } else if (!regEx.mobile.test(value)) {
    callback(new Error('请输入正确手机格式'))
  } else {
    callback()
  }
}
export default {
  props: {},
  mixins: [getCode],
  data() {
    return {
      userId: '',
      FormPhonedialog: false,
      FormPhone: {
        phoneNum: '',
        newCode: ''
      },
      changeRules: {
        phoneNum: { required: true, validator: validateTel, trigger: 'blur' },
        newCode: { required: true, message: '请输入验证码', trigger: 'blur' }
      }
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
  },
  computed: {},
  mounted() {},
  methods: {
    openDialog() {
      this.FormPhonedialog = true
    },
    clearVal() {
      this.FormPhone = {}
    },
    submitPhone() {
      this.FormPhonedialog = false
    },
    //获取验证码
    async changeRestart() {
      try {
        if (this.codeCountdown <= 0) {
          if (!this.FormPhone.phoneNum) {
            this.$refs.FormPhone.validateField('phoneNum')
            return false
          }
          await this.$uwonhttp
            .get('/User/UserManager/GetPhoneNumCode', {
              params: {
                phoneNum: this.FormPhone.phoneNum
              }
            })
            .then(res => {
              if (res.status === 200) {
                this.getCode()
              }
            })
        }
      } catch (e) {}
    },
    //确认修改  xh302
    async submitChange(formName) {
      try {
        this.$refs[formName].validate(async valid => {
          if (valid) {
            let params = {
              Userid: this.userId,
              PhoneNum: this.FormPhone.phoneNum,
              Code: this.FormPhone.newCode
            }
            const res = await this.$uwonhttp.post('/User/UserManager/UpdatePhoneNum', params)
            if (res.status == 200) {
              this.$message.success('手机号码更改成功')
              this.FormPhonedialog = false
              this.$router.go(0)
            }
          } else {
            return false
          }
        })
      } catch (e) {}
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}
/deep/ .el-dialog__body {
  padding: 30px 20px 10px;
  border-bottom: 1px solid #ebebeb;
}
/deep/ .el-input {
  width: 60%;
}
.clearfloat {
  display: flex;
  .code_input {
    width: 100%;
  }
}
</style>
