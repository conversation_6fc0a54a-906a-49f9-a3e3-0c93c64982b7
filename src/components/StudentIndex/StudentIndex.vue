<template>
  <div class="index-main">
    <div class="swiper_block">
      <el-carousel class="_block_height" :loop="true" :autoplay="true">
        <el-carousel-item v-if="State !== 0">
          <div class="newRele">
            <img width="100%" height="100%" src="@/assets/student/banner2.png" alt="" />
            <div class="newPosi">
              <div>{{ bannerTitle }}</div>
              <div @click="routerStudentAnalysis">前去查看</div>
            </div>
          </div>
        </el-carousel-item>
        <el-carousel-item>
          <div>
            <img width="100%" height="100%" src="@/assets/student/welcomeBanner.png" alt="" />
          </div>
        </el-carousel-item>
      </el-carousel>
      <div class="banner_abs">
        <p>{{ bgUserinfo.SchoolName }}</p>
        <p>当前所在班级：{{ bgUserinfo.ClassName }}</p>
        <p v-show="bgUserinfo.TeacherName != null">当前学科任课老师：{{ bgUserinfo.TeacherName }}</p>
      </div>
    </div>
    <!--    多学科-->
    <div style="margin-bottom: 40px">
      <switch-Multidisciplinary :sortShow="sortShow" widthNum="90%" @preserveReturn="getSortList" @handleClick="newHandleClick"></switch-Multidisciplinary>
    </div>
    <!--    多学科结束-->
    <!-- 练习导航 -->
    <div>
      <img @click="toWrongBank" class="nav-img cur" src="@/assets/student/错题本.png" alt="" />
      <img @click="toTaskCenter" class="nav-img cur" src="@/assets/student/练习记录.png" alt="" />
      <img @click="toMedal" style="margin-right: 0" class="nav-img cur" src="@/assets/student/勋章成就.png" alt="" />
    </div>
    <div class="fg practice" @click="toTaskCenter">更多练习 <i class="el-icon-arrow-right"></i></div>
    <div class="week-practice">
      <p class="h3">今日待完成练习：{{ numberCopies.length }}份</p>
      <div class="stuIndex-default" v-show="showCopies">
        <img src="@/assets/lack/暂无练习记录.png" alt="" />
        <p>暂无练习记录哦</p>
      </div>
      <ul class="clearfix" :class="{ 'stuIndex-ul': numberCopies.length !== 0 }">
        <li class="fl" v-for="(item, index) in paperData" :key="index">
          <div class="clearfix">
            <div class="clearfix-header">
              <div style="display: flex;justify-content: space-between;">
                <div>
                  <span @mouseenter="showChapterName(item.PaperId)" @mouseleave="hideChapterName" class="chapter-width ipad_font_size22" style="display: inline-block">{{ item.ChapterName.substring(0,8) }}</span>
                  <span :class="{ 'chapter-show': showChaName === item.PaperId }" class="class-area">
                  {{ item.PaperTypeName }}
                </span>
                </div>
                <div class="xuekeStyle ipad_font_size18" :style="{'background-color':bgColog[item.SubjectId+'bg'],'color':bgColog[item.SubjectId]}">
                  {{ item.SubjectName }}
                </div>
              </div>
              <div class="subjectTitle">
                <p class="til-top ipad_font_size20">
                  {{ item.Title.substring(0,10) }}
                  <img class="micro-icon" v-if="item.IsVideo === 1" src="@/assets/student/icon-micro.png" alt="" />
                  <img class="imgIsdzb" v-if="item.IsPhotograph == 1" src="@/assets/index/bianZu.png" />
                </p>
                <span class="Coach_span ipad_font_size16" v-show="item.PaperType === -2">辅导助手</span>
              </div>
            </div>
            <div class="clearfix-content">
              <div class="clearfix-content_titl" v-if="item.DoPaperType === 0 || item.DoPaperType === 2">
                <p class="ipad_font_size18">共{{ item.ItemCount }}道题</p>
                <p class="pClor ipad_font_size18">发布时间：{{ item.CreateTime }}</p>
              </div>
              <div class="correct-chart" v-if="item.DoPaperType === 1">
                <p class="p-chart">
                  <a-progress
                    type="circle"
                    :percent="item.Accuracy"
                    height="100px"
                    :width="50"
                    :strokeColor="accuracyColor"
                    :format="
                      () => {
                        if (item.Accuracy === 100) {
                          return '100%'
                        } else {
                          return item.Accuracy + '%'
                        }
                      }
                    " /><br />
                  <span>正确率</span>
                </p>
                <p class="p-chart" v-if="item.RevisionCount !== 0">
                  <a-progress type="circle" :percent="(item.RevisionCount / item.ItemCount) * 100" :width="50" :format="() => item.RevisionCount + '/' + item.ItemCount" :strokeColor="stuStatistics">
                  </a-progress><br />
                  <span>待订正错题数</span>
                </p>
              </div>
              <div class="fg">
                <div class="r-info" v-if="item.DoPaperType === 1">
                  <div class="t-c" v-if="item.Rank === 1">
                    <img src="@/assets/student/excellent100.png" alt="" />
                    <p>优秀</p>
                  </div>
                  <div class="t-c" v-if="item.Rank === 2">
                    <img src="@/assets/student/excellent.png" alt="" />
                    <p>优秀</p>
                  </div>
                  <div class="t-c" v-if="item.Rank === 3">
                    <img src="@/assets/student/good.png" alt="" />
                    <p>良好</p>
                  </div>
                  <div class="t-c" v-if="item.Rank === 4">
                    <img src="@/assets/student/qualified.png" alt="" />
                    <p>合格</p>
                  </div>
                  <div class="t-c" v-if="item.Rank === 5">
                    <img src="@/assets/student/strive.png" alt="" />
                    <p>需努力</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="position: relative" class="answer">
            <span v-if="item.DoPaperType === 1" class="operation"><span @click="toReviewWrong(item.PaperId, item.ChapteId)">复习回顾</span></span>
            <span v-if="item.DoPaperType === 0 || item.DoPaperType === 2" class="operation">
              <span @click="toAnswerPaper(item)" class="pc_font_size16 font_size18 ipad_font_size20" style="background-color: #68bb97">
                开始答题<img v-if="item.HaveSubjectiveItem === 1" class="sub-img" src="@/assets/student/u2364.png" alt="" /></span>
            </span>
          </div>
        </li>
      </ul>
    </div>
    <div class="fg more-micro" v-if="weekVideo.length !== 0" @click="toBoutiqueMicro">
      更多微课<i class="el-icon-arrow-right"></i>
    </div>
    <div class="week-micro" v-if="weekVideo.length !== 0">
      <p class="h3">精品微课</p>
      <div v-show="showMicro" class="stuIndex-default">
        <img src="@/assets/lack/暂无微课视频.png" alt="" />
        <p>暂无对应微课视频哦</p>
      </div>
      <ul class="clearfix stuIndex-wk">
        <li class="fl" @click="toMicroDetails(item)" v-for="item in weekVideo" :key="item.Id">
          <div
            class="micro-hei"
            :style="{
              backgroundImage: 'url(' + item.CoverUrl + ')',
              backgroundRepeat: 'no-repeat',
              backgroundSize: '100% 100%',
              borderRadius: '5px 5px 0 0',
            }">
            <img class="img" src="@/assets/student/播放.png" alt="" />
          </div>

          <div class="micro-details">
            <div style="margin-top: 10px;">
              <div style="display: flex;justify-content: space-between;">
                <el-tooltip class="item ipad_font_size22" effect="dark" placement="top-start">
                  <div slot="content">
                    <div>{{ item.Name }}</div>
                  </div>
                  <div>{{ item.Name | ellipsis }}</div>
                </el-tooltip>
                <div class="xuekeStyle ipad_font_size18" :style="{'background-color':bgColog[item.SubjectId+'bg'],'color':bgColog[item.SubjectId]}">
                  {{ item.SubjectName }}
                </div>
              </div>
              <div class="fon_size ipad_font_size18">{{ item.CreateTime }}</div>
            </div>
            <div class="text-over-flow clearfix fon_size ipad_font_size18">
              <span class="fl">授课老师: {{ item.UserName }}</span>
              <span class="fg">{{ item.ClickNum }}人已学</span>
            </div>
          </div>
        </li>
      </ul>
    </div>
<!--  点阵笔试卷提示  -->
    <el-dialog
      title="提示"
      :visible.sync="smallbellVisible"
      width="24%">
      <div class="smallbell">
        <img src="@/assets/icon/smallbell.png" />
        <h1>练习名称</h1>
        <p>请小朋友使用点阵笔，在纸质上进行作答此份练习噢！</p>
      </div>
      <div style="display: flex;justify-content: center;margin-top: 26px;">
        <el-button type="primary" size="medium" @click="smallbellVisible = false">知 道 啦</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/utils.less'
import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
import 'swiper/swiper-bundle.css'
import switchMultidisciplinary from '@/components/switchMultidisciplinary/switchMultidisciplinary'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { debounce } from 'lodash'
import myMixIn from '@/mixIn'
export default {
  components: {
    Swiper,
    SwiperSlide,
    switchMultidisciplinary
  },
  mixins: [myMixIn],
  name: 'Student',
  created () {
    const id = localStorage.getItem('UserId')
    this.userId = localStorage.getItem('UserId')
    // console.log(localStorage.getItem('SubjectId'))
    this.SubjectId = localStorage.getItem('SubjectId')
    this.getPastPaper(id)
    this.getWeekPracticePush(id)
    this.getUserInfo()
    this.$store.commit('setHeaderIndex', 0)
    // 多学科
    this.bgColog = CorrespondingColor()
  },
  watch: {
    immediate: true,
    $route () {
      const id = localStorage.getItem('UserId')
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Home/Introduce') {
        this.SubjectId = localStorage.getItem('SubjectId')
        this.getPastPaper(id)
        this.$store.commit('setHeaderIndex', 0)
      }
    }
  },
  data () {
    return {
      isTime: true,
      userId: '',
      // 状态 0推送练习未完成 1已完成本学期所有练习 2已完成专课专练所有练习 3已完成辅导助手练习
      State: 0,
      GradeId: '',
      // 试卷数据信息
      paperData: [],
      // 试卷ID
      paperId: '',
      // 本周微课信息
      weekVideo: [],
      // 正确率
      accuracyColor: '#68BB97',
      // 待订正
      stuStatistics: '#F87175',
      swiperOptions: {
        // 自动播放
        autoplay: true,
        // 环路播放
        loop: true,
        // 前进后退按钮
        direction: 'horizontal',
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true
        }
      },
      // 带完成份数
      numberCopies: 0,
      // 显示缺省
      showCopies: false,
      copiesChapterId: '',
      // 显示微课
      showMicro: false,
      showChaName: '1',
      bannerTitle: '',
      //  多学科
      sortShow: true,
      bgUserinfo: {
        SchoolName: null,
        ClassName: null,
        TeacherName: null
      },
      SubjectId: '-1',
      bgColog: {},
      smallbellVisible: false
    }
  },
  filters: {
    ellipsis (_val) {
      if (!_val) return ''
      if (_val.length > 10) {
        return _val.slice(0, 10) + '...'
      }
      return _val
    }
  },
  methods: {
    // 多学科选择学科点击事件
    newHandleClick: debounce(function (val) {
      if (val === '-1') {
        this.SubjectId = '-1'
      } else {
        this.SubjectId = val
      }
      this.getPastPaper(this.userId)
    }, 800),
    // 获取排序后的值
    getSortList (val) {
      let str = ''
      str = val.map(item => {
        return item.Id
      }).join(',')
      this.addUserSubject(str)
    },
    showChapterName (id) {
      this.showChaName = id
    },
    hideChapterName () {
      this.showChaName = '1'
    },
    prevPhoto () {
      this.$refs.swiper.$swiper.slidePrev()
    },
    nextPhoto () {
      this.$refs.swiper.$swiper.slideNext()
    },
    toAnswer (id) {
      this.$router.push({
        path: '/Student/Exam_Student/ExamAnswer',
        query: {
          paperId: id
        }
      })
    },
    // 获取本周学生试卷信息数据
    // 当天练习   , IsToday: 1
    getPastPaper (id) {
      var domain = document.domain
      if (domain == 'st.eduwon.cn') {
        this.isTime = false
      } else {
        this.isTime = true
      }
      const Platform = parseInt(localStorage.getItem('PL_newP'))
      this.$http
        .post('/Student/Exam_Student/GetStudentPaper', {
          UserId: id,
          PageNo: 1,
          PageIndex: 6,
          PaperType: 0,
          IsToday: 1,
          IsDo: 1,
          SubjectId: this.SubjectId,
          Platform
        })
        .then(res => {
          this.bgUserinfo.TeacherName = res.Data.TeacherName
          if (res.Data.Items.length === 0) {
            this.showCopies = true
            // this.showMicro = true
            this.numberCopies = res.Data.Items
            this.paperData = []
          } else {
            const arrData = res.Data.Items
            this.showCopies = false
            this.paperData = res.Data.Items
            this.numberCopies = res.Data.Items.filter(value => {
              return value.DoPaperType === 0
            })
            const arr = this.paperData.map(value => {
              return value.ChapteId
            })
            this.copiesChapterId = arr.join(',')
          }
          this.getWeekVideo(this.userId)
        })
    },

    // 获取学生的精品微课
    getWeekVideo (id) {
      this.$http
        .post('/Paper/Exam_MicroLesson/GetWeekVideoBy', {
          userId: id,
          PageIndex: 1,
          PageRows: 6,
          chapterId: this.copiesChapterId
        })
        .then(res => {
          if (res.Data.length !== 0) {
            this.weekVideo = res.Data
            this.showMicro = false
          } else {
            this.weekVideo = []
            this.showMicro = true
          }
        })
    },
    // 获取学生联系题是否全部推送
    getWeekPracticePush (id) {
      this.$uwonhttp
        .post('/Paper/Paper/GetStudentPaperTip', {
          UserId: id
        })
        .then((res) => {
          const newData = res.data.Data
          this.bannerTitle = newData.TipContent
          this.State = newData.State
        })
    },
    // 学生端添加自选学科排序
    addUserSubject (data) {
      this.$uwonhttp
        .post('/Period_Subject/Period_Subject/AddUserSubject', {
          Userid: this.userId,
          SubJectIds: data
        }).then((res) => {
          if (res.data.Success) {
            this.$message.success('保存成功')
            this.GetPeriodList(this.userId)
          } else {
            this.$message.error('保存失败')
          }
        })
    },
    // 获取用户信息
    getUserInfo () {
      const name = localStorage.getItem('userName')
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: name }).then(res => {
        // console.log(res)
        this.bgUserinfo.SchoolName = res.Data.SchoolName
        this.bgUserinfo.ClassName = res.Data.ClassName
        localStorage.setItem('LoginSource', res.Data.LoginSource)
      })
    },
    // 跳转微课详情
    toMicroDetails (item) {
      const LoginSource = localStorage.getItem('LoginSource')
      if(this.$store.state.limitingJobPermissions == 0 && LoginSource == 1){
        this.popupPrompt()
        return false
      }
      const id = item.Id
      const SubjectIdName = {
        Id: item.SubjectId,
        name: item.SubjectName
      }
      // const SubjectIdName = {
      //   Id: '1',
      //   name: '数学'
      // }
      this.$router.push({
        path: '/Student/Fine_micro/MicroDetails',
        query: {
          videoId: id,
          SubjectIdName: JSON.stringify(SubjectIdName) // 学科ID
        }
      })
    },
    // 新跳转至答题页面
    toAnswerPaper (item) {
        const Subject = {name:item.SubjectName,SubjectId: item.SubjectId}
        localStorage.setItem('Multidisciplinary',JSON.stringify(Subject))
        const LoginSource = localStorage.getItem('LoginSource')
        if(item.IsDZB === 1){
          this.smallbellVisible = true
          return false
        }
        if(LoginSource == '1'){
          if(this.$store.state.limitingJobPermissions == 0){
            this.popupPrompt()
            return false
          }
          this.$router.push({
            path: '/Student/Exam_Student/AnswerReady',
            query: {
              paperId: item.PaperId,
              HaveSpecial: item.HaveSpecial,
              type: 10,
              IsExistSubjectItem:item.IsExistSubjectItem
            }
          })
        } else {
          this.$router.push({
            path:'/Student/Exam_Student/AnswerPaper',
            query:{
              paperId: item.PaperId
            }
          })
        }
    },
    // 跳转到复习回顾
    toReviewWrong (id, chapterId) {
      this.$router.push({ path: '/Student/Exam_Student/ReviewWrong', query: { paperId: id, chapterId: chapterId } })
    },
    // 跳转智能分析界面
    routerStudentAnalysis () {
      this.$router.push('/Student/studentPersonalAnalysis')
    },
    // 更多练习
    toTaskCenter () {
      this.$router.push('/Student/TaskCenter')
    },
    // 更多微课
    toBoutiqueMicro () {
      this.$router.push('/Student/Fine_micro/BoutiqueMicro')
    },
    // 错题本
    toWrongBank () {
      this.$router.push('/Student/Fine_micro/WrongQuestionBank')
    },
    // 勋章
    toMedal () {
      this.$message.success('暂未开放')
    },
    toTest () {
      this.$router.push({ path: '/Student/demo' })
    }
  }
}
</script>

<style lang="less" scoped>
@color: #********;
.fon_size {
  font-size: 16px;
}
.answer {
  text-align: center;
}
.newRele{
  position: relative;
  .newPosi{
    position: absolute;
    left: 45%;
    top: 56%;
    div:nth-child(1){
      font-size: 20px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #8F8F8F;
      line-height: 28px;
      margin-bottom: 20px;
      text-align: center;
    }
    div:nth-child(2){
      font-size: 22px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      padding: 13px 60px;
      background: #ED9349;
      border-radius: 30px;
    }
  }
}
.subjectTitle{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.xuekeStyle{
  font-size: 16px;
  padding: 4px 14px;
  border-radius: 100px;
  //margin-left: 20px;
}
//轮播图
.swiper_block{
  //height: 460px;
  margin-bottom: 40px;
  position: relative;
  .banner_abs{
    position: absolute;
    left: 0px;
    top: 0px;
    padding: 20px 28px;
    //border-radius: 20px;
    background-color: rgba(0,0,0,0.5);
    font-family: PingFangSC-Semibold, PingFang SC;
    border-radius: 10px;
    color: #FFFFFF;
    p{
      text-align: center;
    }
    p:nth-child(1){
      font-size: 20px;
      font-weight: 600;
    }
    p:nth-child(2){
      font-size: 18px;
      font-weight: 500;
    }
    p:nth-child(3){
      font-size: 18px;
      font-weight: 500;
    }
  }
  ._block_height{
    height: 400px;
  }
}
/deep/.el-carousel__item {
  top: 0;
  left: 0;
  display: inline-block;
  overflow: hidden;
  z-index: 0;
  border-radius: 10px;
}
/deep/.el-carousel__container {
  position: relative;
  height: 400px;
}
///deep/.el-carousel__item, .el-carousel__mask {
//  height: auto;
//  position: absolute;
//  width: 100%;
//}
.clearfix-header {
  .clearfix-title {
    margin-top: 15px;
    font-size: 15px;
    font-family: PingFangSC-Medium, 'PingFang SC';
    font-weight: 500;
    color: rgb(77, 87, 83);
  }
}

.chapter-width {
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #4d5753;
  vertical-align: middle;
}
.clearfix-content {
  line-height: 25px;
  .clearfix-content_titl{
    margin: 15px 0 34px 0;
    p{
      font-size: 16px;
    }
    .pClor{
      color: #ccc;
    }
  }
}
// .chapter-width:hover {
//   overflow: visible;
//   z-index: 2;
// }
// 练习缺省
.stuIndex-default {
  // height: 450px;
  text-align: center;
  color: #ccc;
}
.til-top {
  font-size: 18px;
  color: rgb(104, 187, 151);
  .imgIsdzb{
    width: 28px;
    height: 28px;
    margin-left: 8px;
  }
}
// 微课图标
.micro-icon {
  width: 20px;
}
.stuIndex-ul {
  overflow: auto;
  font-size: 14px;
  display: flex;
  // justify-content: space-between;
  flex-wrap: wrap;
}
.stuIndex-wk {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-around;
}
// 校，班，区
.class-area {
  font-size: 16px;
  margin-left: 0.03125rem;
  padding: 0px 3px;
  text-align: center;
  color: #fff;
  background-color: #839398;
  border-radius: 13px;
  vertical-align: middle;
}
// 主观标识
.sub-img {
  // position: absolute;
  // left: 31%;
  // top: 65%;
  width: 20px;
  margin-left: 8px;
  color: #fff;
  img {
    width: 20px;
  }
}
.h3 {
  margin-top: 30px;
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #4d5753;
}
.nav-img {
  width: 457px;
  margin-right: 40px;
}
// 轮播
//.swiper-container {
//  height: 451px;
//}
//.swiper-button-prev,
//.swiper-container-rtl .swiper-button-next {
//}
//.swiper-button-next,
//.swiper-container-rtl .swiper-button-prev {
//}
// 微课高度
.micro-hei {
  height: 170px;
}
.text-over-flow {
  margin-top: 10px;
}
.swiper-slide {
  text-align: center;
  font-size: 18px;
  // background: #fff;

  /* Center slide text vertically */
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  ima{
    width: 100%;
    height: 100%;
  }

}
.index-main {
  width: 1451px;
  // width:100%;
  margin: 0 auto;
}
// 历史/菜单
//.notice-menu {
//  position: relative;
//}
.basic-infor {
  height: 30px;
  line-height: 30px;
  margin-bottom: 20px;
  font-size: 15px;
  text-indent: 20px;
  border-radius: 5px;
  background-color: rgba(0, 170, 255, 0.747);
}
// 往日练习
.practice {
  // margin-right: 160px;
  margin-top: 30px;
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #b5b5b5;
  cursor: pointer;
}
// 练习的正确率/正确数
.p-chart {
  display: inline-block;
  margin-top: 30px;
  font-size: 12px;
  text-align: center;
  color: #9ba6a9;
}
// 本周练习
.week-practice {
  li {
    position: relative;
    width: 24%;
    height: auto;
    padding: 12px;
    margin-top: 18px;
    margin-bottom: 22px;
    border-radius: 5px;
    margin-right: 14px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .operation {
      margin-bottom: 0;
      span:nth-child(1) {
        //font-size: 16px;
        display: inline-block;
        width: 136px;
        padding: 6px 0;
        //height: 30px;
        margin-right: 28px;
        //line-height: 30px;
        color: #fff;
        text-align: center;
        background-color: #bdd3ce;
        border-radius: 100px;
        cursor: pointer;
      }
    }
  }
  li:hover {
    box-shadow: 8px 8px 7px grey;
  }
  .l-details {
    .chapter-show {
      display: none;
    }
    p:nth-child(2) {
      font-size: 13px;
      margin-bottom: 5px;
    }
    p:nth-child(3) {
      margin-bottom: 15px;
      span {
        margin-left: 20px;
        width: 15px;
        padding: 1px 2px;
        border: 1px solid #000;
        background-color: #def;
        border-radius: 5px;
      }
    }
  }
  .r-info {
    position: relative;
    .t-c {
      text-align: center;
    }
    img {
      margin-bottom: 2px;
    }
    span {
      position: absolute;
      top: 20px;
      right: 25px;
    }
    p {
      text-align: center;
    }
  }
}
// 更多微课
.more-micro {
  margin: 13px 20px 0 0;
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #b5b5b5;
  cursor: pointer;
}

// 本周微课
.week-micro {
  // border: 2px solid #ccc;
  border-radius: 5px;
  li {
    position: relative;
    width: 415px;
    height: 245px;
    margin-top: 18px;
    margin-right: 60px;
    margin-bottom: 40px;
    border-radius: 5px;
    // background-color: #********;
    background-color: #fff;
    cursor: pointer;
    .img {
      position: absolute;
      top: 29%;
      left: 50%;
      transform: translateX(-50%);
      font-size: 55px;
    }
  }
  h2 {
    height: 40px;
    line-height: 40px;
    margin-top: 37px;
    text-indent: 20px;
    background-color: #ccc;
  }
  // 微课内容
  .micro-details {
    padding: 0px 15px;
    background-color: #fff;
    border-radius: 0px 0px 5px 5px;
    .item{
      font-size: 20px;
    }
    // 微课描述
    // .micro-describe {
    //   // width: 200px;
    // }
  }
}
.Coach_span {
  font-size: 14px;
  padding: 4px 10px;
  border-radius: 25px;
  color: #31c0ce;
  background-color: rgba(49, 192, 206, 0.2);
  //text-align: center;
  //display: inline-block;
  margin-top: 6px;
}
// 是否为点阵笔试卷弹窗提示
.smallbell{
  position: relative;
  img{
    width: 100px;
    position: absolute;
    right: 10px;
    top: -50px;
  }
  h1{
    padding-top: 20px;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
  }
  p{
    padding-top: 10px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
  }
}
</style>
