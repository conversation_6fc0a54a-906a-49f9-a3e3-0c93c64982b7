<template>
  <div>
    <!-- <h1>班级信息</h1> -->
    <!-- 导航信息 -->
    <div class="Review" @click="Review" v-if="this.ReviewStatus!=='0'">
      <img src="@/assets/路径 2.png" />
    </div>
    <ul class="clearfix nav-data">
      <li class="nav-data-one">
        {{ this.viewModel.PaperTitle }}
      </li>
      <li class="paperType">
        <span>{{ this.viewModel.PaperTypeName }}</span>
      </li>
      <li class="nav-data-two" style="">
        <p style="font-size: 14px">提交练习人数</p>
        <a-progress :percent="this.viewModel.ClassFinish" :showInfo="showInfo" :strokeWidth="strokeWidth" />
        <span style="font-size: 14px">{{ this.viewModel.DoPaperCount }}/{{ this.viewModel.TotalCount }}</span>
      </li>
      <li class="nav-data-three" style="">
        {{ this.viewModel.ClassName }}
      </li>
      <li class="nav-data-top">
        <span style="font-size: 17px">正确率</span>
        <span style="margin-left: 11px; margintop: -9px">{{ this.viewModel.ClassCorrect }}%</span>
      </li>
      <li class="nav-data-top">
        <span style="font-size: 17px">平均用时</span>
        <span style="margin-left: 18px; margin-top: -9px">{{ this.viewModel.DoTime }}</span>
      </li>
      <li class="nav-data-top">
        <span class="cur task-report" style="" @click="toTaskReport">查看作业报告</span>
      </li>
    </ul>
    <div class="tabs">
      <span class="tabBtn" v-for="(item, index) in tabsList" :key="index" @click="tabsDetail(item.name)" :class="{ active: active == item.name }">{{ item.name }}</span>

      <p class="count_down">
        <span>
          <el-tooltip class="item" effect="dark" content="请先设置刷新时间!" placement="left">
            <el-switch v-model="switchState" @change="getSwitch" :disabled='disabled' active-value="1" active-color="#13ce66" inactive-color="#D7D7D7" inactive-value="0"></el-switch>
          </el-tooltip>

        </span>
        <!-- 刷新频率:{{ hour }}:{{ minute }}:{{ second }} <i @click="countTime" class="el-icon-s-tools"></i> -->
        刷新频率: {{ second }} 秒 <i @click="countTime" class="el-icon-s-tools"></i>
      </p>
    </div>
    <div class="new-class-charts" v-if="active == '练习数据分布'">
      <div class="title">
        <span class="title_Frist">{{ this.viewModel.ClassName }}练习数据</span>
      </div>

      <div class="Frist_chart" ref="FristChart"></div>
    </div>
    <!-- 当前班级练习数据 -->
    <div class="new-class-data" v-if="active == '练习成绩分布'">
      <div class="new-class-data-p">
        <span class="title_name">{{ this.viewModel.ClassName }}练习数据</span>

        &nbsp;&nbsp;&nbsp;
        <span v-if="this.review != '无需批改'">
          <span v-if="this.correctingCount !== 0" @click="toBatchModify" class="fg mr">批量批改</span>
          <span v-if="this.correctingCount !== 0" class="fg" style="color: #68bb97">待批改份数: <span style="color: #ff9c74">{{ this.correctingCount }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          </span></span>

        <div class="perform">
          <div class="item-color">
            <span>优秀</span>
            <span class="excellent"></span>
          </div>
          <div class="item-color">
            <span>良好</span>
            <div class="good"></div>
          </div>
          <div class="item-color">
            <span>合格</span>
            <div class="qualified"></div>
          </div>
          <div class="item-color">
            <span>须努力</span>
            <div class="workHard"></div>
          </div>
          <div class="item-color">
            <span>未做</span>
            <div class="Notdone"></div>
          </div>
        </div>
      </div>

      <div class="chart" ref="chart"></div>
    </div>
    <countDoling ref="getCount" @timeStart="timeStart" v-on:getBusiness="getBusiness"></countDoling>
    <!-- 学生作答详情 -->
    <div class="student-answer" v-if="active == '练习成绩分布'">
      <a-table :columns="columns" :data-source="data" :pagination="{ pageSize: 20 }" :loading="loading" :scroll="{ y: 447 }" bordered style="font-size: 15px">
        <template slot="cao" slot-scope="text, record">
          <span v-if="record.Review != '无需批改'">
            <span v-if="record.DoTime != '未作答' && record.UserId != null" style="padding-right: 38px; font-size: 18px"><a href="javascript:;" @click="toBatchModify(record.UserId)">批改</a></span>
          </span>
          <span v-if="record.DoTime == '未作答'" style="font-size: 18px"><a href="javascript:;" @click="Remind(record.UserId)">提醒</a></span>
          <span v-if="record.DoTime != '未作答' && record.UserId != null" style="font-size: 18px"><a href="javascript:;" @click="toTaskDetails(record)">查看</a></span>
        </template>
        <template slot="RevisionStates" slot-scope="text, record">
          <span v-if="record.RevisionStates !== '未订正'">{{ text }}</span>
          <span v-if="record.RevisionStates === '未订正'">还有<span style="color: red; font-size: 17px">{{ record.RevisionCount }}题</span>未订正</span>
        </template>
      </a-table>
      <p class="student-answer-p fg cur" v-if="this.IsRevision == 1" @click="Revision()">一键提醒未订正同学</p>
      <p class="student-answer-p fg cur" v-if="this.rank == '未做'" @click="Remind('')">一键提醒未做同学</p>
    </div>
    <p class="footer">Copyright©上海有我科技有限公司，All Rights Reserved</p>
  </div>
</template>

<script>
import '@/utils/utils.less'
import echarts from 'echarts'
import countDoling from '@/components/count_down/count'

const columns = [
  {
    title: '序号',
    dataIndex: 'Index',
    align: 'center',
    key: 'Index',
    scopedSlots: { customRender: 'Index' },
    width: 60
  },
  {
    title: '学生姓名',
    dataIndex: 'StudentName',
    align: 'center',
    key: 'StudentName',
    width: 80
  },
  {
    title: '答题正确率',
    dataIndex: 'Correct',
    align: 'center',
    key: 'Correct' + '%',
    ellipsis: true,
    width: 150
  },
  {
    title: '完成时间',
    dataIndex: 'DoTime',
    align: 'center',
    key: 'DoTime',
    width: 80
  },
  {
    title: '分数等级',
    dataIndex: 'Rank',
    align: 'center',
    key: 'Rank',
    width: 80
  },
  {
    title: '订正状态',
    dataIndex: 'RevisionStates',
    align: 'center',
    key: 'RevisionStates',
    width: 80,
    scopedSlots: { customRender: 'RevisionStates' }
  },
  {
    title: '提交时间',
    dataIndex: 'CreateTime',
    align: 'center',
    key: 'CreateTime',
    width: 80
  },
  {
    title: '操作',
    dataIndex: '5',
    align: 'center',
    width: 150,
    scopedSlots: { customRender: 'cao' }
  }
]
export default {
  name: 'ClassData',
  props: {},
  components: {
    countDoling
  },
  watch: {
    $route() {
      this.id = this.$route.query.paperId
      this.classId = this.$route.query.classId
      this.ReviewStatus = this.$route.query.ReviewStatus
      const ChangId = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Teacher/My_Task/InspectionWorkData?' + ChangId) {
        this.$router.go(0)
        this.getDataView()
        this.getStudentAnswer()
        this.GetBarChart()
        this.GetPaperStudentItemForChartWeb()
      }
    },
    // 监听数值变化
    second: {
      handler(newVal) {
        this.num(newVal)
        // if (newVal == 0 && this.minutes == 0 && this.hours == 0) {
        if (newVal == 0) {
          if ((this.switchState = '1')) {
            this.getTime()
            if (this.active == '练习数据分布') {
              this.getDataView()
              this.GetPaperStudentItemForChartWeb()
            }
            if (this.active == '练习成绩分布') {
              this.getDataView()
              this.GetBarChart()
            }
          }
        }
      }
    },
    minute: {
      handler(newVal) {
        this.num(newVal)
      }
    },
    hour: {
      handler(newVal) {
        this.num(newVal)
      }
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.id = this.$route.query.paperId
    this.classId = this.$route.query.classId
    this.showClassList = this.$route.query.showClassId
    this.ReviewStatus = this.$route.query.ReviewStatus
    this.getDataView()
    this.getStudentAnswer()
    this.GetBarChart()
    this.GetPaperStudentItemForChartWeb()
  },
  mounted() {},
  data() {
    return {
      ReviewStatus: '',
      loading: true,
      active: '练习数据分布',
      // 显示全区/年级正确率
      showClassList: '',
      id: '',
      classId: '',
      // 练习试卷的id
      paperId: '',
      showInfo: false,
      // 进度条宽度
      strokeWidth: 20,
      data: [],
      columns,
      viewModel: {},
      titleName: '',
      className: '',
      BarChart: [],
      rank: '100',
      rankName: '未做',
      IsRevision: 0,
      review: '',
      correctingCount: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      switchState: '0',
      time: '',
      timer: null,
      getUserId: '',
      PaperType: '',
      disabled: true,
      tabsList: [{ name: '练习数据分布' }, { name: '练习成绩分布' }]
    }
  },
  computed: {
    // 初始化数据
    second() {
      return this.num(this.seconds)
    },
    minute() {
      return this.num(this.minutes)
    },
    hour() {
      return this.num(this.hours)
    }
  },
  methods: {
    tabsDetail(name) {
      this.active = name
      if (name == '练习数据分布') {
        this.$nextTick(() => {
          this.GetPaperStudentItemForChartWeb()
        })
      }
      if (name == '练习成绩分布') {
        this.$nextTick(() => {
          this.GetBarChart()
          this.getStudentAnswer()
        })
      }
    },
    async GetPaperStudentItemForChartWeb() {
      const res = await this.$uwonhttp.post('/Paper/TeacherPaper/GetPaperStudentItemForChartWeb', {
        PaperId: this.id,
        ClassId: this.classId
      })
      var Data = res.data.Data
      const XName = Data.map(val => {
        return val.X
      })
      const YName = Data.map(val => {
        return val.Y
      })
      const myFristChart = echarts.init(this.$refs.FristChart)
      myFristChart.setOption({
        color: ['#03A6F1'],
        title: {
          subtext: '单位(人)',
          padding: [10, 10, 50, 50],
          textStyle: {
            fontSize: 18,
            color: '#000000FF',
            fontStyle: 'normal',
            fontWeight: 'normal'
          }
        },
        legend: {
          data: ['人数']
        },
        xAxis: {
          type: 'category',
          data: XName
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: true
          }
        },
        grid: {
          left: '4%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        series: [
          {
            data: YName,
            type: 'bar',
            barWidth: 40, //柱图宽度
            label: {
              show: true,
              position: 'top', // ---相对位置
              color: '#000'
            },
            itemStyle: {
              normal: {
                color: function(params) {
                  if (params.name == '未做') {
                    return '#FF9166'
                  } else {
                    return '#33C4F5'
                  }
                }
              }
            }
          }
        ]
      })
    },

    async GetBarChart() {
      const resJson = await this.$uwonhttp.post('/Paper/TeacherPaper/GetPaperStudentScoreForChartWeb', {
        PaperId: this.id,
        ClassId: this.classId
      })
      this.BarChart = resJson.data.Data
      this.ring(this.BarChart)
    },
    getSwitch(value) {
      if (value == 1) {
        this.switchState = '1'
        if ((this.switchState = '1')) {
          this.timeStart()
          this.getTime()
          if (this.active == '练习数据分布') {
            this.getDataView()
            this.GetPaperStudentItemForChartWeb()
          }
          if (this.active == '练习成绩分布') {
            this.getDataView()
            this.GetBarChart()
          }
        }
      }
      if (value == 0) {
        this.switchState = '0'
        window.clearInterval(this.timer)
      }
    },
    getTime() {
      this.seconds = localStorage.getItem('seconds')
    },
    getBusiness(param1, param2, param3, param4) {
      this.seconds = param1
      localStorage.setItem('seconds', this.seconds)
      this.switchState = '1'
      this.timeStart()
      this.disabled = false
    },
    countTime() {
      this.$refs.getCount.openDialog()
      this.$refs.getCount.clear()
    },
    // 防止数值小于10时，出现一位数
    num(n) {
      return n < 10 ? '0' + n : '' + n
    },
    timeStart() {
      this.add()
    },
    // 倒计时函数
    add() {
      if (this.timer) {
        window.clearInterval(this.timer)
      }

      this.timer = window.setInterval(() => {
        if (this.hours !== 0 && this.minutes === 0 && this.seconds === 0) {
          this.hours -= 1
          this.minutes = 59
          this.seconds = 59
        } else if (this.hours === 0 && this.minutes !== 0 && this.seconds === 0) {
          this.minutes -= 1
          this.seconds = 59
        } else if (this.hours === 0 && this.minutes === 0 && this.seconds === 0) {
          this.seconds = 0
        } else if (this.hours !== 0 && this.minutes !== 0 && this.seconds === 0) {
          this.minutes -= 1
          this.seconds = 59
        } else {
          this.seconds -= 1
        }
      }, 1000)
    },
    ring(datas) {
      const myChart = echarts.init(this.$refs.chart)

      myChart.setOption({
        xAxis: {
          type: 'category',
          data: ['100', '99-90', '89-80', '79-60', '59-0', '未做'],
          axisLabel: {
            textStyle: {
              color: '#000',
              fontSize: '15',
              itemSize: ''
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#000',
              fontSize: '15',
              itemSize: ''
            }
          }
        },

        series: [
          {
            data: datas,
            type: 'bar',
            itemStyle: {
              normal: {
                label: {
                  // ---图形上的文本标签
                  formatter: function(params) {
                    if (params.name === '100') {
                      return params.value
                    } else if (params.name === '99-90') {
                      return `${params.value}`
                    } else if (params.name === '89-80') {
                      return `${params.value}`
                    } else if (params.name === '79-60') {
                      return `${params.value}`
                    } else if (params.name === '59-0') {
                      return `${params.value}`
                    } else {
                      return `${params.value}`
                    }
                  },
                  rich: {
                    img1: {
                      backgroundColor: {
                        image:
                          'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1605074303494&di=3871f451c5a232233ddd64cb07969063&imgtype=0&src=http%3A%2F%2Fbpic.588ku.com%2Felement_origin_min_pic%2F18%2F02%2F24%2Fee79cb33232a6f2bcce1512cfa251993.jpg'
                      },
                      height: 20,
                      borderRadius: 5
                    }
                  },
                  show: true,
                  fontSize: '15',
                  position: 'top', // ---相对位置
                  // rotate: 0, // ---旋转角度
                  color: '#000'
                },
                color: function(params) {
                  // 注意，如果颜色太少的话，后面颜色不会自动循环，最好多定义几个颜色
                  var colorList = ['#33C4F5', '#33C4F5', '#8DE3E8', '#B0CB5C', '#FFC23D', '#FF9166']
                  return colorList[params.dataIndex]
                }
              }
            },
            barWidth: '45', // ---柱形宽度
            barCategoryGap: '30%' // ---柱形间距
          }
        ],
        axisLabel: {
          // ---坐标轴 标签
          show: true, // ---是否显示
          inside: false, // ---是否朝内
          rotate: 0, // ---旋转角度
          margin: 5 // ---刻度标签与轴线之间的距离
          // color:'red',				//---默认取轴线的颜色
        }
      })
      var that = this
      myChart.on('click', function(params) {
        that.rank = params.name
        if (params.name === '100') {
          that.rank = '100'
        } else if (params.name === '99-90') {
          that.rank = '99-90'
        } else if (params.name === '89-80') {
          that.rank = '89-80'
        } else if (params.name === '79-60') {
          that.rank = '79-60'
        } else if (params.name === '59-0') {
          that.rank = '59-0'
        } else if (params.name === '未做') {
          that.rank = '未做'
        }
        that.getStudentAnswer()
      })
      // 设置图表随窗口大小变化
      window.addEventListener('resize', function() {
        myChart.resize()
      })
    },
    // 跳转作业详情
    toTaskDetails(record) {
      this.getUserId = record.UserId
      this.$router.push({
        path: '/Teacher/My_Task/TaskDetails',
        query: {
          paperId: this.id,
          studentId: this.getUserId
        }
      })
    },
    // 提醒单个学生未做
    Remind(studentId) {
      this.$uwonhttp.post('/Report/TeacherReport/RemindNotDoStudent', {
        paperId: this.id,
        classId: this.classId,
        teacherId: this.userId,
        studentId: studentId
      })
      this.$notification.open({
        message: '温馨提示',
        description: '已提醒未做题同学',
        icon: <a-icon type="smile" style="color: #108ee9" />
      })
    },
    Revision() {
      this.$uwonhttp.post('/Report/TeacherReport/SendPushNotCorrect', {
        paperId: this.id,
        classId: this.classId,
        teacherId: this.userId
      })
      this.$notification.open({
        message: '温馨提示',
        description: '已提醒未订正同学',
        icon: <a-icon type="smile" style="color: #108ee9" />
      })
    },
    // 跳转作业报告
    toTaskReport() {
      this.$router.push({
        path: '/Teacher/My_Task/TaskReport/TaskReport',
        query: {
          paperId: this.id,
          classId: this.classId
        }
      })
    },
    // 跳转修改页面
    toBatchModify() {
      this.$router.push({
        path: '/Teacher/My_Task/InspectionWork/BatchModify',
        query: {
          classId: this.classId,
          paperId: this.id
        }
      })
    },
    async getDataView() {
      const resJson = await this.$uwonhttp.post('/Paper/TeacherPaper/GetTestingWorkTop', {
        paperId: this.id,
        classId: this.classId,
        userId: this.userId
      })
      this.viewModel = resJson.data.Data
      this.PaperType = resJson.data.Data.PaperTypeName
      localStorage.setItem('PaperType', this.PaperType)
      this.titleName = resJson.data.Data.PaperTitle
      localStorage.setItem('titleName', this.titleName)
    },
    getStudentAnswer() {
      this.$uwonhttp
        .post('/Paper/TeacherPaper/GetTestingWorkTail', {
          paperId: this.id,
          classId: this.classId,
          rank: this.rank
        })
        .then(resJson => {
          if (resJson.data.Data[0].UserId === null) {
            this.data = [{}]
          } else {
            this.data = resJson.data.Data
            this.correctingCount = this.data[0].CorrectingCount
          }
          this.review = this.data[0].Review
          this.IsRevision = this.data[0].IsRevision

          this.loading = false
        })
    },
    Review() {
      let WorksObj = {
        classId: this.classId,
        paperId: this.id,
        PaperTitle: this.viewModel.PaperTitle
      }
      localStorage.setItem('WorksParams', JSON.stringify(WorksObj))
      this.$router.push({
        path: '/Teacher/My_Task/TeacherWorkshops',
        query: {
          classId: this.classId,
          paperId: this.id,
          PaperTitle: this.viewModel.PaperTitle,
          Show_BackBtn: 2
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.title {
  background: #ffff;
  .title_Frist {
    position: relative;
    left: 43%;
    font-size: 18px;
    top: 25px;
  }
}

.title_name {
  position: relative;
  left: 43%;
}
.Frist_chart {
  width: 100%;
  height: 600px;
  background: #fff;
}
.perform {
  position: relative;
  left: 32%;
  display: flex;
  .item-color {
    width: 120px;
    span {
      margin-right: 50px;
      vertical-align: middle;
    }
    .excellent {
      width: 0.130208rem;
      height: 0.052083rem;
      display: block;
      bottom: 28px;
      left: 47px;
      background: #33c4f5;
      position: relative;
    }
    .good {
      width: 0.130208rem;
      height: 0.052083rem;
      display: block;
      bottom: 28px;
      left: 47px;
      background: #8de3e8;
      position: relative;
    }
    .qualified {
      width: 0.130208rem;
      height: 0.052083rem;
      display: block;
      bottom: 28px;
      left: 47px;
      background: #b0cb5c;
      position: relative;
    }
    .workHard {
      width: 0.130208rem;
      height: 0.052083rem;
      display: block;
      bottom: 28px;
      left: 62px;

      background: #ffc23d;
      position: relative;
    }
    .Notdone {
      width: 0.130208rem;
      height: 0.052083rem;
      display: block;
      bottom: 28px;
      left: 47px;
      background: #ff9166;
      position: relative;
    }
  }
}
.tabs {
  display: flex;
  .count_down {
    position: relative;
    left: 70%;
    top: 5px;
    color: #68bb97;
    font-size: 18px;
  }
  .tabBtn {
    display: inline-block;
    width: 114px;
    line-height: 30px;
    height: 30px;
    margin-bottom: 20px;
    margin-right: 40px;
    text-align: center;
    color: #fff;
    background-color: #ddd;
    border-radius: 5px;
    cursor: pointer;
  }
  .active {
    background-color: #68bb97;
  }
}

// 表格
/deep/.ant-table-thead {
  /deep/.ant-table-row-cell-break-word {
    color: #fff;
    background-color: #68bb97;
  }
}
/deep/.ant-table-tbody tr:nth-child(odd) {
  background-color: #f4f4f4;
}
.mr {
  padding: 3px 6px;
  line-height: 36px;
  text-align: center;
  margin-right: 35px;
  cursor: pointer;
  color: #fff;
  background-color: #68bb97;
  border-radius: 10px;
}
// 进度条
.ant-progress-line {
  width: 200px;
}
// 导航信息
.nav-data {
  display: flex;
  height: 105px;
  justify-content: space-between;
  // margin: 0 10px;
  margin-bottom: 25px;
  // padding: 0 20px;
  background-color: #fff;
  border-radius: 5px;
  .nav-data-one {
    line-height: 105px;
    font-size: 18px;
    position: relative;
    right: 2%;
  }
  .nav-data-two {
    margin-top: 20px;
    margin-right: 120px;
  }
  .nav-data-three {
    line-height: 105px;
    color: black;
    font-size: 17px;
  }
  .nav-data-top {
    margin-top: 40px;
    font-size: 15px;
  }
  .task-report {
    font-size: 16px;
    color: #7bc4a5;
  }
  li {
    // flex: 1;
  }
  li /deep/ .ant-progress-inner {
    background-color: #ddd;
  }
}
// 试卷完成信息
.paper-info {
  display: flex;
  margin: 0 10px;
  height: 20%;
  li {
    // float: left;
    flex: 1;
    font-size: 18px;
    width: 30%;
    height: 105px;
    margin-right: 40px;
    padding: 35px 0 0 30px;
    border-radius: 5px;
    background-color: #fff;
    span {
      padding: 0 10px;
    }
    span:nth-child(3) {
      color: black;
      text-align: right;
      margin-right: 5%;
      font-size: 15px;
    }
  }
  li:nth-child(3) {
    margin-right: 0;
  }
}
// 当前班级数据
.new-class-data {
  background: #fff;
  .new-class-data-p {
    // width: 233px;
    height: 48px;
    font-size: 18px;
    line-height: 48px;
    margin-top: 15px;
    margin-left: 25px;
    span:nth-child(1) {
    }
  }

  .chart {
    height: 600px;
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 5px;
  }
}
// 学生作答详情
.student-answer {
  .student-answer-p {
    width: 233px;
    height: 48px;
    font-size: 18px;
    line-height: 48px;
    margin-left: 25px;
    margin-bottom: 25px;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    background-color: #68bb97;
  }
}
.paperType {
  span {
    border-radius: 22px;
    padding: 1px 5px;
    line-height: 105px;
    color: #fff;
    font-size: 16px;
    background-color: #6fbd9c;
    position: relative;
    right: 100%;
  }
}
.Review {
  background: #26be94;
  position: absolute;
  right: 0;
  top: 45%;
  cursor: pointer;
  text-align: -webkit-center;
  img {
    width: 40px;
  }
}
</style>
