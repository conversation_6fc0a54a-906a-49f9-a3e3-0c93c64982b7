<template>
  <span>
    <span @click="showModal">设置发布</span>
    <!-- 弹出提示框 -->
    <a-modal title="设置发布信息" v-model="visible" @ok="hideModal" okText="发布" cancelText="取消">
      <p class="m-b">
        发布班级：
        <a-checkbox-group @change="classChange1">
          <a-checkbox v-for="item in classInfor" :key="item.Id" :value="item.id">{{ item.ClassName }}</a-checkbox>
          <a-checkbox value="1244868595092033536">一年级二班</a-checkbox>
          <a-checkbox value="1244874559723671552">一年级二班</a-checkbox>

        </a-checkbox-group>
      </p>
      <!-- 自定义发布时间 -->
      <div class="m-b release-time">
        发布时间:
        <a-radio-group @change="onChange" v-model="value">
          <a-radio :value="1">立即发送</a-radio>
          <br>
          <a-radio :value="2" @click="handleClick">
            自定义时间:
            <span v-if="value == 1">
              <a-date-picker :showTime="{ format: 'HH:mm' }" format="YYYY-MM-DD HH:mm" disabled />
            </span>
            <span v-else>
              <a-date-picker :showTime="{ format: 'HH:mm' }" format="YYYY-MM-DD HH:mm" @change="StartTimeChange" />
            </span>
          </a-radio>
        </a-radio-group>
      </div>
      <!-- 作答截止时间 -->
      <div>
        作答截至时间：
        <a-date-picker :showTime="{ format: 'HH:mm' }" format="YYYY-MM-DD HH:mm" @change="TimeChange" />
      </div>
    </a-modal>
  </span>

</template>

<script>
import moment from 'moment'
export default {
  name: 'SetUpRelease',
  props: {
    paperId: {
      type: String
    }
  },
  data() {
    return {
      // 老师对应班级的信息
      classInfor: [],
      // 是否立即发送
      ImmediatelyPublish: true,
      // 班级id
      ClassId: [],
      // 自定义开始时间
      PublishDateTime: '',
      // 作答截止时间
      AnswerEndDateTime: ''
    }
  },
  methods: {
    // 处理当前时间并赋值
    handleTime() {
      const time = new Date()
      const Time = moment(time).format('YYYY-MM-DD HH:mm')
      this.PublishDateTime = Time
    },
    // 显示模态框
    showModal() {
      this.visible = true

      const userId = JSON.parse(sessionStorage.getItem('userId'))
      const id = userId.Id
      // 获取老师班级对应班级
      this.$http.post('/ClassManage/Exam_Class/GetListByUserId', { userId: id }).then(res => {
        this.classInfor = res.Data
      })
      // 处理立即发送默认时间
      this.handleTime()
    },
    // 隐藏模态框 (确认按钮)
    hideModal() {
      const paperId = this.$route.query.paperId

      if (this.PublishDateTime === '' || this.AnswerEndDateTime === '') {
        return this.$message.error('请正确选择时间')
      }
      if (this.ClassId.length === 0) {
        return this.$message.error('请选择需要发布的班级')
      }
      this.visible = false
      this.$http
        .post('/Paper/Exam_Paper/PublishPaper', {
          paperId,
          ClassIds: this.ClassId,
          ImmediatelyPublish: false,
          PublishDateTime: this.PublishDateTime,
          AnswerEndDateTime: this.AnswerEndDateTime
        })
        .then(res => {})
      if (this.value === 1) {
        this.$message.success('成功发布')
      } else {
        this.$message.success('设置成功，将在设定时间发布')
      }
    },
    // 多选框 选择班级
    classChange1(checkedValues) {
      this.ClassId = checkedValues
    },
    // 获取自定义开始时间
    StartTimeChange(data, dataString) {
      this.PublishDateTime = dataString
    },
    // 获取作答截止时间
    TimeChange(data, dataString) {
      this.AnswerEndDateTime = dataString
    },
    // 切换自定义时间
    handleClick() {
      this.ImmediatelyPublish = !this.ImmediatelyPublish
    }
  }
}
</script>

<style lang="less" scoped>
.m-b {
  margin-bottom: 20px;
}
</style>
