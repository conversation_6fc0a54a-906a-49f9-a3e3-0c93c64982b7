<template>
  <div>
    <div v-if="microVideoShow" class="noMicro">
      <p><img src="@/assets/lack/暂无微课视频.png" alt=""></p>
      <p>暂无微课视频</p>
    </div>
    <!-- 微课信息详情 -->
    <div class="clearfix" id="microInfo">
      <div class="micro-details fl" v-for="item in teacherMicro" :key="item.Id">
        <!-- 视频 -->
        <div class="video" @click="toVideoDetails(item.Id)" :style="{ backgroundImage:'url(' + item.CoverUrl + ')',backgroundRepeat:'no-repeat',backgroundSize:'100% 100%' }">
          <a-icon type="play-circle" style="fontSize: 42px;color:#fff;" />
        </div>
        <!-- 右侧详情 -->
        <div class="video-details">
          <div>
            <p class="written-words">{{ item.ChapterName }}</p>
            <p class="written-words">{{ item.Name }}</p>
            <p class="f-c">
              <span><img style="vertical-align: middle;" src="@/assets/teacher/时长.png" alt="">{{ item.Duration }}</span>
              <span><img style="vertical-align: middle;" src="@/assets/teacher/播放量.png" alt="">{{ item.ClickNum }}次</span>
            </p>
          </div>
          <!-- <p class="score">课程评分：</p> -->
          <span class="score-star f-c clearfix">
            <a-rate v-model="item.Score" disabled /><span class="fg time">{{ item.CreateTime }}</span>
          </span>
        </div>
      </div>
    </div>
    <div class="paging">
      <a-pagination
        v-if="teacherMicro.length !== 0"
        hideOnSinglePage
        :defaultCurrent="1"
        :total="totalNum"
        @change="handleChange" />
    </div>
    <!-- <p :class="{ 'pos': this.heightPage < this.browserHeight, 'footer': this.heightPage > this.browserHeight}">Copyright©上海有我科技有限公司，All Rights Reserved</p> -->
    <footerLogo :heightPage="heightPage"></footerLogo>
  </div>
</template>

<script>
import footerLogo from '@/components/newFooterLogo/newFooterLogo'
export default {
  components: {
    footerLogo
  },
  name: 'MicroVideo',
  props: {

  },
  created () {
    this.chapterNum = this.$route.query.chapterNum
    this.getTeacherMicroData()
  },
  updated () {
    // this.browserHeight = window.innerHeight - 100
    this.heightPage = document.getElementById('microInfo').offsetHeight
  },
  data () {
    return {
      userId: '',
      // 微课数据
      teacherMicro: [],
      // 搜索框的值
      videoName: '',
      chapterNum: '',
      GradeId: '',
      SubjectId: '2',
      totalNum: 10,
      pagination: {
        current: 1,
        pageSize: 8
      },
      sorter: { field: 'Id', order: 'asc' },
      microVideoShow: false,
      heightPage: 0,
      browserHeight: 0
    }
  },
  methods: {
    // 获取微课信息详情
    getTeacherMicroData () {
      this.$http
        .post('/Paper/Exam_MicroLesson/GetDataByWhere', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'Id',
          SortType: this.sorter.order,
          videoName: this.videoName,
          chapter: this.chapterNum,
          gradeId: this.GradeId,
          subjectId: this.SubjectId,
          userId: null,
          ...this.item,
          ...this.filters
        })
        .then(resJson => {
          if (resJson.Data.length === 0) {
            this.microVideoShow = true
          } else {
            this.microVideoShow = false
          }
          this.teacherMicro = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
          this.totalNum = resJson.Total
        })
    },
    // 跳转到视频详情
    toVideoDetails (id) {
      this.$router.push({ path: '/Paper/Exam_MicroLesson/VideoDetails',
        query: {
          videoId: id
        }
      })
    },
    // 分页
    handleChange (pageNumber) {
      this.pagination.current = pageNumber
      this.getTeacherMicroData()
    }
  }
}
</script>

<style lang="less" scoped>
  .f-c {
    font-size: 12px;
    color: #B5B5B5;
  }
  .noMicro {
     position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: rgba(165, 166, 167);
  }
  .written-words {
    width: 280px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
  }
  // 微课信息详情
  .micro-details {
    position: relative;
    // width: 625px;
    margin-right: 20px;
    margin-bottom: 75px;
    border-radius: 5px;
    background-color: #fff;
    .video {
      width: 320px;
      height: 200px;
      line-height: 215px;
      text-align: center;
      border-radius: 5px 5px 0 0;
      cursor: pointer;
    }
    .video-details {
      // position: absolute;
      // left: 344px;
      // top: 8px;
      padding: 10px;
      p {
        margin: 0;
      }
      div {
        p:nth-child(1) {
          margin-top: 10px;
        }
        p:nth-child(2) {
          font-size: 15px;
          font-weight: 700;
          margin-bottom: 22px;
        }
        p:nth-child(3) {
          padding-bottom: 10px;
          border-bottom: 1px solid #cccc;
          span {
            margin-right: 35px;
          }
        }
        // p:nth-child(4) {
        //   margin-bottom: 29px;
        // }
        // p:nth-child(5) {
        //   margin-bottom: 0;
        //   font-size: 12px;
        // }
      }
      .score {
      position: absolute;
      left: 158px;
      top: 85px;
      width: 71px;
      }
      .score-star {
        .time {
          margin-top: 8px;
          margin-right: 27px;
        }
      }
    }
  }
  .paging {

    text-align: center;
     /deep/.ant-pagination-item:focus, .ant-pagination-item:hover {
      border-color: #68BB97;
    }
    /deep/.ant-pagination-item-active a {
      color: #000;
    }
    /deep/ .ant-pagination-item-active {
      border-color: #68BB97;
      background-color: #68BB97;
    }
  }
  .pos {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
  }
</style>
