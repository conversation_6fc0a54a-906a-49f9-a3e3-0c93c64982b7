<template>
  <div style="overflow: hidden;">
    <div class="example" v-if="showSubject">
      <a-spin />
    </div>
    <div id="gradually-analysis">
      <div class="paper-info-data">
        <!-- 试卷名称 -->
        <div class="title">
          <p>{{ paperName }}</p>
          <p>{{ className }}</p>
        </div>
        <!-- 切换正确率和做题时长 -->
        <div>
          <!-- <span :class="{ 'duration': !duration }">正确率</span>&nbsp; -->
          <!-- <a-switch default-checked @change="onChange" size="small" />&nbsp; -->
          <!-- <span :class="{ 'duration': duration }">做题时长</span> -->
        </div>
        <!-- 图表fenxi -->
        <div v-if="this.area !== 'area'" class="situationAnalysis" ref="situationAnalysis"></div>
        <div v-show="this.area === 'area'" class="situationAnalysis" ref="situationAnalysisArea"></div>
      </div>
      <!-- 试卷题目内容分析 -->
      <div class="title-analysis">
        <div style="height: 600px;width: 75%">

          <happy-scroll color="red" size="3" id="math-tex" v-if="paperList.length !== 0">
            <div class="title-data" v-for="(item, index) in paperList" :key="index" :id="item.ItemID">
              <div class="paper-info">
                <span>{{ index + 1 }}、&nbsp;&nbsp;</span>
                <span v-html="item.OriItemTitle" style="width:75%;margin-bottom: 25px;"></span>
                <!-- 选项内容 -->
                <div>
                  <span v-for="(ite,ind) in item.Opts" :key="ind" v-show="item.ItemTypeId !== 5">
                    <span v-if="item.ItemType === 2 || item.ItemType === 10">{{ ite.Opt }}.</span>
                    <span v-html="ite.OptContent" style="margin-right: 25px"></span>
                  </span>
                </div>
                <!-- <div class="test-option" v-if="index === key && (newTextType === '1' || newTextType === '2' || newTextType === '3')">
                  <span v-for="i in option" :key="i.Id">
                    <span>{{ i.Option }} . </span>
                    <span v-html="i.Content"></span>
                  </span>
                </div> -->
              </div>
              <!-- 对应答案和解析 班级正确详情 -->
              <div class="answer-ana-details">
                <!-- <div class="launch-coach" @click="launchCoach">发起辅导</div> -->
                <!-- <div class="launch-coach">推荐参考答案</div> -->
                <p><span>【答案:】 </span><span v-html="item.Answer"></span></p>
                <p><span>【解析:】 </span><span v-html="item.Analyze"></span></p>
                <!-- 正确率 -->
                <div class="accuracy">
                  <a-table :columns="columns" :data-source="item.AdminAccuracys" bordered :pagination="pagination">
                  </a-table>
                </div>
                <!-- 正确详情 -->
                <div class="accuracy-details">
                  <p>正确{{ item.ClassRight }}人 &nbsp;
                    <a-progress :percent="item.ClassRight/(item.ClassError+item.ClassRight)*100" :showInfo="showInfo" :strokeWidth="strokeWidth" />
                    &nbsp;错误{{ item.ClassError }}人&nbsp;&nbsp;&nbsp;&nbsp;
                    <span v-if="item.ClassRight !== 0 || item.ClassError !== 0" class="packInfo" @click="handleTable(item.ItemID,$event)">展开详情↓</span>&nbsp;
                    <!-- <a-icon v-show="packUp === '展开详情' " type="down" /> -->
                    <!-- <a-icon v-show="packUp === '收起详情' && openItemId === item.ItemID" type="up" /> -->
                  </p>
                  <div class="example" v-if="load">
                    <a-spin />
                  </div>
                  <!-- :rowClassName="tableRowClass" -->
                  <transition v-if="!load" name="slide-fade">
                    <a-table v-show="showAccuracy && (item.ClassRight !== 0 || item.ClassError !== 0) && openItemId === item.ItemID" :columns="column" :data-source="ItemErrorInfos" :pagination="pagination" bordered
                      :rowClassName="tableRowClass"></a-table>
                  </transition>
                </div>
              </div>
            </div>
          </happy-scroll>
        </div>

        <!-- 对应按钮 -->
        <!-- @scroll="onScroll" -->
        <div class="title-btn" :class="{'isFixed': isFixed}">
          <p>{{ paperName }}</p>
          <a v-for="(i, j) in paperList" :key="j" class="title-btn-whole" :class="{ 'color': color === i.ItemID }" @click="switchTitle(j, i.ItemID, i.ItemTypeId) ">
            <a-progress type="circle" :percent="i.ClassPer" :width="subWid" :format="() => { if(i.ClassPer === 100) { return '100%'}else { return i.ClassPer+ '%'} }" /><br>
            <p>{{ j+1 }}</p>
          </a>
        </div>
      </div>
    </div>
    <!-- <p class="footer">Copyright©上海有我科技有限公司，All Rights Reserved</p> -->
    <footerLogo :heightPage="heightPage"></footerLogo>
  </div>
</template>

<script>
import echarts from 'echarts'
import { HappyScroll } from 'vue-happy-scroll'
import footerLogo from '@/components/newFooterLogo/newFooterLogo'
const column = [
  {
    title: '选项',
    dataIndex: 'Answer',
    key: 'Answer',
    align: 'center'
  },
  {
    title: '人数',
    dataIndex: 'Count',
    key: 'Count',
    align: 'center'
  },
  {
    title: '学生',
    dataIndex: 'Name',
    key: 'Name',
    align: 'center'
  }
]
const dataDetails = [
  {
    key: '',
    Answer: '',
    Count: '',
    Name: ''
  }
]
const columns = [
  {
    title: '-----',
    dataIndex: 'average',
    key: 'average',
    align: 'center'
  },
  {
    title: '我班',
    dataIndex: 'ClassPer',
    key: 'ClassPer',
    align: 'center'
  },
  {
    title: '我校',
    dataIndex: 'SchoolPer',
    key: 'SchoolPer',
    align: 'center'
  },
  {
    title: '全区',
    dataIndex: 'AllPer',
    key: 'AllPer',
    align: 'center'
  }
]
const data = [
  {
    key: 'Num',
    name: '平均正确率',
    age: '85%',
    address: '65%',
    area: '75%'
  }
  // ,
  // {
  //   key: '2',
  //   name: '平均用时',
  //   age: "2'35'",
  //   address: "2'12'",
  //   area: "1'55'"
  // }
]
const column1 = [
  {
    title: '作答情况',
    dataIndex: 'name',
    key: 'name',
    align: 'center'
  },
  {
    title: '人数',
    dataIndex: 'num',
    key: 'num',
    align: 'center'
  },
  {
    title: '学生',
    dataIndex: 'student',
    key: 'student',
    align: 'center'
  }
]
export default {
  components: {
    HappyScroll,
    footerLogo
  },
  name: 'SubjectAnalysis',
  props: {
    isShowSub: {
      type: Number,
      defalute: 0,
      required: true
    }
    // id: {
    //   type: String,
    //   defalute: '1',
    //   required: true
    // }
  },
  wacth: {
    $route() {
      this.area = this.$route.query.area
      const ChangeId = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Teacher/My_Task/InspectionWorkData?' + ChangeId) {
        window.clearInterval(this.timer)
      }
    }
  },
  created() {
    // 获取试卷信息

    var paperId = this.$route.query.paperId
    this.classId = this.$route.query.classId
    this.area = this.$route.query.area
    // this.getPaperData(paperId)
    this.getPaperDataList(paperId)
    // 获取试卷名称
    this.getPaperTitle(paperId)
    this.paperId = paperId
    this.getClassName()
    this.$nextTick(() => {
      this.init()
      //  if(this.commonsVariable.isMathjaxConfig){//判断是否初始配置，若无则配置。
      //       this.commonsVariable.initMathjaxConfig();
      //   }
      //   this.commonsVariable.MathQueue('math-tex')
    })
  },
  mounted() {
    this.init()
    window.addEventListener('scroll', this.initHeight)
    this.$nextTick(() => {
      this.offsetTop = document.querySelector('.title-analysis').offsetTop
    })
  },
  updated() {
    this.heightPage = document.getElementById('gradually-analysis').offsetHeight
  },
  data() {
    return {
      subWid: 50,
      area: '',
      // 展示逐题分析
      showSubject: true,
      showSubjectOne: '',
      heightPage: 0,
      // 正确率或做题时长
      duration: false,
      // 试卷ID
      paperId: '',
      // 试卷信息
      paperData: [],
      // 折线数据
      brokenLine: [],
      // 题目切换
      key: 0,
      // 试卷名
      paperName: '',
      // 题目选项
      option: [],
      // 当前题目id
      newItemId: '',
      // 当前题目类型
      newTextType: '',
      // 切换题目颜色
      color: '',
      // 答案
      Answer: '',
      // 解析
      analysis: '',
      ClassError: 0,
      ClassRight: 0,
      // 测试----------------------
      // 平均正确率表格行
      columns,
      // 平均正确率表格列
      data,
      // 正确详情表格行
      column,
      // 正确详情表格列
      dataDetails,
      column1,
      // 测试----------------------
      // 表格去除分页
      pagination: false,
      offsetTop: 0,
      // 吸顶
      isFixed: false,
      // 显示进度条的数值或图标
      showInfo: false,
      // 进度条宽度
      strokeWidth: 20,
      // 收起详情
      packUp: '展开详情',
      // 显示正确详情
      showAccuracy: false,
      // 班级名称
      className: '',
      classId: '',
      paperList: [],
      openItemId: '',
      ItemErrorInfos: [],
      load: false
    }
  },
  watch: {
    $route() {
      if (this.$route.fullPath !== '/Teacher/My_Task/InspectionWorkData') {
        window.removeEventListener('scroll', this.initHeight)
      } else {
        window.addEventListener('scroll', this.initHeight)
      }
    }
  },
  methods: {
    onScroll() {
      // const scrollItems = document.querySelectorAll('.sub-info')
    },
    // 表格行类名
    tableRowClass(record, index) {
      if (record.IsRight === 1) {
        return 'rowClass'
      }
    },
    init() {
      if (this.area !== 'area') {
        // 我班练习
        const myChart = echarts.init(this.$refs.situationAnalysis)
        myChart.setOption({
          title: {
            text: '(百分比：%)' // 正确率
          },
          dataZoom: [
            {
              start: 0, // 默认为0
              end: 100, // 默认为100
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              handleSize: 0, // 滑动条的 左右2个滑动条的大小
              height: 24, // 组件高度
              // left: '10%', // 左边的距离
              // right: '10%', // 右边的距离
              bottom: -15, //
              border: 'none',
              borderColor: '#fff',
              fillerColor: '#B5BEB9',
              borderRadius: 7,
              backgroundColor: '#F8F8F8', // 两边未选中的滑动条区域的颜色
              showDataShadow: false, // 是否显示数据阴影 默认auto
              showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
              realtime: true, // 是否实时更新
              filterMode: 'filter'
            }
          ],
          // 提示框内容
          tooltip: {
            trigger: 'axis'
            // formatter: function (params) {
            //   return `${params[0].data}%`
            // }
          },
          // 自定义颜色
          color: ['#FF8B7F', '#71B6A1', '#52B4FF'],
          legend: {
            data: ['我班正确率'],
            selected: { 我班正确率: true }
            // (点击其他)默认只展示一条折线
            // selectedMode: 'single'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              // saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value',
            // min: 0,
            // max: 50,
            // interval: 50,
            axisLabel: {
              formatter: '{value}%'
            },
            triggerEvent: true
          },
          series: [
            {
              name: '我班正确率',
              type: 'line',
              // stack: '总量',
              data: [65, 78, 82, 75, 69, 73, 98, 89, 73, 98, 73, 98, 73, 78, 82, 75],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 1, // 设置线宽
                    type: 'solid' // 'dotted'虚线 'solid'实线
                  }
                }
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(125, 59, 188, 0.7)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(5, 26, 51,0)'
                  }
                ])
              }
            }
            // {
            //   name: '我校正确率',
            //   type: 'line',
            //   // stack: '总量',
            //   data: [220, 182, 191, 234, 290, 330, 310, 125, 89, 73, 98, 73, 98, 73, 98, 73, 98],
            //   itemStyle: {
            //     normal: {
            //       lineStyle: {
            //         width: 1, // 设置线宽
            //         type: 'solid' // 'dotted'虚线 'solid'实线
            //       }
            //     }
            //   },
            //   areaStyle: {
            //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //       offset: 0,
            //       color: 'rgba(0, 144, 255,0.7)'
            //     }, {
            //       offset: 1,
            //       color: 'rgba(5, 26, 51,0)'
            //     }])
            //   }
            // },
            // {
            //   name: '全区正确率',
            //   type: 'line',
            //   // stack: '总量',
            //   data: [150, 232, 201, 154, 190, 330, 410, 165, 89, 73, 98, 73, 98, 73, 98, 73, 98],
            //   itemStyle: {
            //     normal: {
            //       lineStyle: {
            //         width: 3, // 设置线宽
            //         type: 'dotted' // 'dotted'虚线 'solid'实线
            //       }
            //     }
            //   },
            //   areaStyle: {
            //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            //       offset: 0,
            //       color: 'rgba(0, 135, 204, 0.7)'
            //     }, {
            //       offset: 1,
            //       color: 'rgba(5, 26, 51,0)'
            //     }])
            //   }
            // }
          ]
        })
        // 设置图表随窗口大小变化
        window.addEventListener('resize', function() {
          myChart.resize()
        })
      } else {
        // 区级练习
        const myChart1 = echarts.init(this.$refs.situationAnalysisArea)
        myChart1.setOption({
          title: {
            text: '(百分比：%)' // 正确率
          },
          dataZoom: [
            {
              start: 0, // 默认为0
              end: 100, // 默认为100
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              handleSize: 0, // 滑动条的 左右2个滑动条的大小
              height: 24, // 组件高度
              // left: '10%', // 左边的距离
              // right: '10%', // 右边的距离
              bottom: -15, //
              border: 'none',
              borderColor: '#fff',
              fillerColor: '#B5BEB9',
              borderRadius: 7,
              backgroundColor: '#F8F8F8', // 两边未选中的滑动条区域的颜色
              showDataShadow: false, // 是否显示数据阴影 默认auto
              showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
              realtime: true, // 是否实时更新
              filterMode: 'filter'
            }
          ],
          // 提示框内容
          tooltip: {
            trigger: 'axis'
            // formatter: function (params) {
            //   return `${params[0].data}%`
            // }
          },
          // 自定义颜色
          color: ['#FF8B7F', '#71B6A1', '#52B4FF'],
          legend: {
            data: ['我班正确率', '我校正确率', '全区正确率'],
            selected: { 我班正确率: true, 我校正确率: false, 全区正确率: false }
            // (点击其他)默认只展示一条折线
            // selectedMode: 'single'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              // saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value',
            min: 0,
            // interval: 50,
            axisLabel: {
              formatter: '{value}%'
            },
            triggerEvent: true
          },
          series: [
            {
              name: '我班正确率',
              type: 'line',
              // stack: '总量',
              data: [65, 78, 82, 75, 69, 73, 98, 89, 73, 98, 73, 98, 73, 78, 82, 75],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 3, // 设置线宽
                    type: 'solid' // 'dotted'虚线 'solid'实线
                  }
                }
              }
              // areaStyle: {
              //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              //     offset: 0,
              //     color: 'rgba(125, 59, 188, 0.7)'
              //   }, {
              //     offset: 1,
              //     color: 'rgba(5, 26, 51,0)'
              //   }])
              // }
            },
            {
              name: '我校正确率',
              type: 'line',
              // stack: '总量',
              data: [220, 182, 191, 234, 290, 330, 310, 125, 89, 73, 98, 73, 98, 73, 98, 73, 98],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 3, // 设置线宽
                    type: 'solid' // 'dotted'虚线 'solid'实线
                  }
                }
              }
              // areaStyle: {
              //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              //     offset: 0,
              //     color: 'rgba(0, 144, 255,0.7)'
              //   }, {
              //     offset: 1,
              //     color: 'rgba(5, 26, 51,0)'
              //   }])
              // }
            },
            {
              name: '全区正确率',
              type: 'line',
              // stack: '总量',
              data: [150, 232, 201, 154, 190, 330, 410, 165, 89, 73, 98, 73, 98, 73, 98, 73, 98],
              itemStyle: {
                normal: {
                  lineStyle: {
                    width: 3, // 设置线宽
                    type: 'dotted' // 'dotted'虚线 'solid'实线
                  }
                }
              }
              // areaStyle: {
              //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              //     offset: 0,
              //     color: 'rgba(0, 135, 204, 0.7)'
              //   }, {
              //     offset: 1,
              //     color: 'rgba(5, 26, 51,0)'
              //   }])
              // }
            }
          ]
        })
        // 设置图表随窗口大小变化
        window.addEventListener('resize', function() {
          myChart1.resize()
        })
      }
    },
    // 获取试卷信息
    getPaperData(paperid) {
      this.$uwonhttp
        .post('/Paper/TeacherPaper/GetPaperOneByOneAnalyze', {
          PaperId: paperid,
          ClassId: this.classId
        })
        .then(res => {
          this.paperData = res.data.Data
          // 当前题目类型(默认类型)
          this.newTextType = this.paperData.Items[0].ItemType
          // 切换题目时颜色(默认颜色)
          this.color = this.paperData.Items[0].ItemID
          // 处理题目选项(默认选项)
          this.switchTitleOption(this.paperData.Items[0].ItemId)
          // 处理答案解析
          this.handleAnswerSny(0)
          // 处理显示折现图数据
          this.handleDiscountChart(this.paperData.Details)
          // 正确率表格
          this.data = [
            {
              name: '平均正确率',
              age: this.paperData.Items[0].ClassPer + '%',
              address: this.paperData.Items[0].SchoolPer + '%',
              area: this.paperData.Items[0].AllPer + '%'
            }
          ]
          this.paperData.Details.forEach((item, index) => {
            const reg = /(#&\d+@)/g
            // const reg = /(#@)/g
            const inputele = ' '
            const stem = item.OriItemTitle.replace(reg, inputele)
            item.OriItemTitle = stem
          })
          this.getPaperItemErrorInfo(res.data.Data.Items[0].ItemID)
        })
    },
    // 新试卷信息
    getPaperDataList(paperid) {
      this.$http
        .post('/report/TeacherReport/GetPaperOneByOneAnalyze', {
          PaperId: paperid,
          ClassId: this.classId
        })
        .then(res => {
          if (res.Success) {
            this.showSubject = false
          } else {
            return (this.showSubject = true)
          }
          this.paperList = res.Data.Items
          this.paperList.forEach(ite => {
            ite.AdminAccuracys.forEach(value => {
              const key = 'average'
              value[key] = '平均正确率'
            })
          })
          // this.data = res.Data.Items
          res.Data.Items.forEach((item, index) => {
            // const reg = /(#@)/g
            const reg = /(#&\d+@)/g
            const inputele = ' '
            const stem = item.OriItemTitle.replace(reg, inputele)
            item.OriItemTitle = stem
          })

          // 当前题目类型(默认类型)
          this.newTextType = this.paperList[0].ItemType
          // 切换题目时颜色(默认颜色)
          this.color = this.paperList[0].ItemID
          // 处理题目选项(默认选项)
          // this.switchTitleOption(this.paperList.ItemId)
          // 处理答案解析
          this.handleAnswerSny(0)
          // 处理显示折现图数据
          this.handleDiscountChart(this.paperList)

          // this.ClassRight =
        })
    },
    // 获取试卷名称
    getPaperTitle(paperid) {
      this.$http
        .post('/Paper/Exam_Paper/GetTheData', {
          id: paperid
        })
        .then(res => {
          this.paperName = res.Data.Title
        })
    },
    getClassName() {
      this.$http
        .post('/ClassManage/Exam_Class/GetTheData', {
          id: this.classId
        })
        .then(res => {
          this.className = res.Data.ClassName
        })
    },
    // 切换题目
    switchTitle(index, ItemId, typeId) {
      document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.newItemId = ItemId
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.color = ItemId
      // 切换题目时的类型ID
      this.newTextType = typeId
      // 处理试卷切换题目选项
      // this.switchTitleOption(ItemId)
      // 处理答案解析
      this.handleAnswerSny(index)
      // 处理切换按钮显示不全
      // this.handleBtnIncomplete()
      // 正确率显示(隐藏)
      this.showAccuracy = false
      if (this.showAccuracy) {
        this.packUp = '收起详情'
      } else {
        this.packUp = '展开详情'
      }
      // this.getPaperItemErrorInfo(ItemId)
    },
    // 处理切换题目时选项
    switchTitleOption(ItemId) {
      this.$http
        .post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', {
          itemId: ItemId
        })
        .then(res => {
          this.option = res.Data
        })
    },
    // 处理答案和解析
    handleAnswerSny(index) {
      this.analysis = '略'
      this.analysis = this.paperList[index].Analyze
      this.Answer = this.paperList[index].Answer
      this.ClassError = this.paperList[index].ClassError
      this.ClassRight = this.paperList[index].ClassRight
      // 正确率表格
      // this.data = [{ name: '平均正确率', age: this.paperData.Items[index].ClassPer + '%', address: this.paperData.Items[index].SchoolPer + '%', area: this.paperData.Items[index].AllPer + '%' }]
    },
    // 处理显示折现图数据
    handleDiscountChart(data) {
      const ClassAvg = data.map(item => {
        return item.ClassPer
      })
      const SchoolAvg = data.map(item => {
        return item.SchoolPer
      })
      const AreaAvg = data.map(item => {
        return item.AllPer
      })
      this.brokenLine = data.map((item, index) => {
        return `第${index + 1}题`
      })
      if (this.area !== 'area') {
        const myChart = echarts.init(this.$refs.situationAnalysis)
        myChart.setOption({
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.brokenLine
          },
          series: [
            {
              data: ClassAvg
            }
            // {
            //   data: SchoolAvg
            // },
            // {
            //   data: AreaAvg
            // }
          ]
        })
      } else {
        const myChart1 = echarts.init(this.$refs.situationAnalysisArea)
        myChart1.setOption({
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.brokenLine
          },
          series: [
            {
              data: ClassAvg,
              type: 'line'
            },
            {
              data: SchoolAvg,
              type: 'line'
            },
            {
              data: AreaAvg,
              type: 'line'
            }
          ]
        })
      }
      // this.showSubjectOne = '1'
    },
    // 处理切换按钮显示不全
    // handleBtnIncomplete () {
    //   const btnHeight = document.getElementsByClassName('title-btn')[0].offsetHeight
    //   const wholeHeight = document.getElementsByClassName('title-analysis')[0].offsetHeight
    //   const whole = document.getElementsByClassName('title-analysis')[0]
    //   if (btnHeight > wholeHeight) {
    //     whole.style.height = btnHeight + 'px'
    //   } else {
    //     whole.style.height = wholeHeight + 'px'
    //   }
    // },
    // 吸顶
    // initHeight () {
    //   // var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
    //   this.offsetTop = document.querySelector('.title-analysis').getBoundingClientRect().top
    //   if (this.offsetTop < 0) {
    //     this.isFixed = true
    //   } else {
    //     this.isFixed = false
    //   }
    // },
    // 展开/收起 详情
    handleTable(itemId, e) {
      // info.forEach(item => {
      //   item.innerText = '展开详情'
      // })
      this.load = true
      if (e.target.innerText === '展开详情↓') {
        const info = document.getElementsByClassName('packInfo')
        for (let key of info) {
          key.innerText = '展开详情↓'
        }
        e.target.innerText = '收起详情↑'
        this.showAccuracy = true
        this.openItemId = itemId
        this.packUp = '收起详情↑'
        this.$http
          .post('/report/TeacherReport/GetPaperItemErrorInfo', {
            classId: this.classId,
            ItemId: itemId,
            paperId: this.paperId
          })
          .then(res => {
            // if (res.Success) {

            // }
            this.ItemErrorInfos = res.Data
            this.load = false
          })
      } else {
        this.load = false
        const info = document.getElementsByClassName('packInfo')
        for (let key of info) {
          key.innerText = '展开详情↓'
        }
        e.target.innerText = '展开详情↓'
        this.packUp = '展开详情↓'
        this.showAccuracy = false
        this.openItemId = ''
      }
    },
    // 发起辅导跳转精准辅导
    launchCoach() {
      this.$router.push({
        path: '/Teacher/My_Coach/AccurateCoach',
        query: {
          paperId: '889e9855-9fe1-488b-b9c3-4a6a2cbbf04d',
          subject: this.key + 1,
          id: this.color
        }
      })
    },
    // switch开关
    // onChange (checked) {
    //   this.duration = !checked
    //   if (checked) {
    //     // this.chartTitle = "(时长: 时' 分' 秒')"
    //     const myChart = echarts.init(this.$refs.situationAnalysis)
    //     myChart.setOption({
    //       title: {
    //         text: '(百分比: %)'
    //       }
    //     })
    //   } else {
    //     const myChart = echarts.init(this.$refs.situationAnalysis)
    //     myChart.setOption({
    //       title: {
    //         text: "(时长: 时' 分' 秒')"
    //       }
    //     })
    //   }
    // },
    
    getPaperItemErrorInfo(itemId) {
      this.$uwonhttp
        .post('/Paper/TeacherPaper/GetPaperItemErrorInfo', {
          classId: this.classId,
          ItemId: itemId,
          paperId: this.paperId
        })
        .then(res => {
          this.dataDetails = res.data.Data
        })
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.initHeight)
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-table-tbody {
  .rowClass {
    color: green;
  }
}

// 正确率和做题时长
.duration {
  color: #00aaff;
}
.example {
  text-align: center;
}
.paper-info-data {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
}
// 逐题分析
.title-analysis {
  display: flex;
  margin-top: 20px;
  padding: 25px;
  background-color: #fff;
  border-radius: 5px;
  .title-data {
    flex: 1;
    margin-bottom: 32px;
    padding-bottom: 30px;
    background-color: #dddddd4d;
    border-radius: 5px;
  }
  // 发起辅导
  .launch-coach {
    float: right;
    width: 104px;
    height: 34px;
    line-height: 34px;
    margin-top: 12px;
    margin-right: 1%;
    text-align: center;
    border-radius: 17px;
    background-color: #68bb97;
    color: #fff;
    cursor: pointer;
  }
}
// 学情分析
.situationAnalysis {
  height: 437px;
}
// 试卷名称
.title {
  text-align: center;
  p:nth-child(1) {
    font-size: 18px;
  }
  p:nth-child(2) {
    color: blue;
  }
}
// 试卷内容
.paper-info {
  font-size: 20px;
  margin-left: 30px;
  margin-right: 339px;
}
.paper-info img {
  max-width: 600px !important;
}
.paper-info div img {
  max-width: 600px !important;
}
img {
  max-width: 600px !important;
}
// 题目选项
.test-option {
  margin: 25px 0;
  font-size: 15px;
  span {
    display: inline-block;
    margin-right: 50px;
    span /deep/ img {
      width: 30px;
    }
  }
}
// 题目按钮
.title-btn {
  // position: absolute;
  // right: 0;
  // top: 0;
  // flex: 1;
  width: 317px;
  background-color: #fff;
  /deep/.ant-progress-inner {
    width: 50px !important;
    height: 50px !important;
  }
  p {
    margin-bottom: 15px;
    font-size: 18px;
    text-align: center;
  }
  .title-btn-whole {
    display: inline-block;
    text-align: center;
    margin: 0 20px 25px 0;
    cursor: pointer;
  }
  // 切换颜色
  .color {
    border-bottom: 2px solid #00aaff;
  }
}
// 对应答案和解析 班级正确详情
.answer-ana-details {
  margin-right: 339px;
  background-color: #fff;
  margin-left: 10px;
  border-radius: 8px;
  width: 75%;
  font-size: 15px;
  p {
    margin-bottom: 20px;
  }
}
// 正确率
.accuracy {
  margin-bottom: 20px;
}
// 正确详情
.accuracy-details {
  .ant-progress-bg {
    background-color: green;
  }
  span {
    color: #00aaff;
  }
  p:nth-child(1) {
    span {
      color: #00aaff;
      cursor: pointer;
    }
  }
}
// 进度条
.ant-progress-line {
  width: 350px;
}
.ant-progress-inner {
  background-color: #ddd;
}
.accuracy-details /deep/ .ant-progress-inner {
  background-color: red;
}
// 吸顶
.isFixed {
  position: fixed;
  top: 0;
}
// 显示正确详情哦
.display {
  display: none;
}
// 动画
.slide-fade-enter-active {
  transition: all 0.5s ease;
}
.slide-fade-leave-active {
  transition: all 0.2s ease;
}
.slide-fade-enter, .slide-fade-leave-to
  /* .slide-fade-leave-active for below version 2.1.8 */ {
  // transform: translateX(10px);
  opacity: 0;
}
// 表格
/deep/.ant-table-thead {
  /deep/.ant-table-align-center {
    color: #fff;
    background-color: #68bb97;
  }
}
/deep/.ant-table-tbody tr:nth-child(odd) {
  background-color: #f4f4f4;
}
/deep/.ant-select-selection {
  border: none;
}
</style>
