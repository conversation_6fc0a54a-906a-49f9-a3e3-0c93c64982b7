<template>
  <div class="teacher-reg-info-right fg">
    <div>
      <p class="pfont font_size18 ipad_font_size20" :class="{'fontSiz': this.fonts}">
        <span><img v-show="this.showYpJy === 1" style="width:25px;margin-right: 5px" src="@/assets/logo/yp-logo.png" alt=""></span>
        <webName :class="{'f-s': this.showYpJy === 1}" :realmName="realmName" :id="1"></webName><span v-show="this.showYpJy !== 1" class="span"></span>
        <span v-show="this.showYpJy !== 1">专科专练</span><br>
        <!-- <span v-show="this.showYpJy !== 1">打造一流教育</span><br> -->
        <span v-show="this.showYpJy === 1" class="m-t">精准练习，提质增效</span>
      </p>
      <slot>
        <p class="acc-num font_size20 ipad_font_size22">已有账号？</p>
      </slot>
      <p class="login-st pc_font_size16 font_size18 ipad_font_size20" @click="toUserLogin">马上去登录</p>
    </div>
  </div>
</template>

<script>
import webName from '@/components/WebsiteName/WebsiteName'
export default {
  components: {
    webName
  },
  created() {
    this.realmName = window.location.href.split('/')[2]
    if (this.realmName === this.Facturl.$hreYpaName) {
      this.showYpJy = 1
    } else {
      this.showYpJy = 0
    }
    // const realmName = window.location.href.split('/')[2]
    // if (realmName === this.$hrefName) {
    //   this.conentName = '徐汇网校'
    //   this.fonts = false
    // } else {
    //   this.conentName = '黄浦区小学数学学习平台'
    //   this.fonts = true
    // }
  },
  methods: {
    // 登录页
    toUserLogin() {
      this.$router.push({ path: '/Home/UserLogin' })
    }
  },
  data() {
    return {
      showYpJy: 0,
      realmName: '',
      conentName: '',
      fonts: false
    }
  }
}
</script>

<style lang="less" scoped>
.f-s {
  font-size: 16px;
}
.m-t {
  display: inline-block;
  margin-top: 20px;
  font-size: 24px;
}
.acc-num {
  margin-top: 50px;
  margin-bottom: 30px;
  font-size: 25px;
}
// 登录
.login-st {
  width: 38%;
  height: 44px;
  line-height: 44px;
  margin: 0 auto;
  border-radius: 100px;
  background-color: #68bb99;
  cursor: pointer;
}
.teacher-reg-info-right {
  position: relative;
  width: 40%;
  height: 100%;
  background: url('../../assets/login/位图.png') center center;
  background-size: cover;
  div {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    .pfont {
      font-size: 18px;
      .span {
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 4px;
        background-color: #fff;
        position: relative;
        top: -3px;
      }
    }
    .fontSiz {
      font-size: 22px;
    }
    p:nth-child(2) {
    }
  }
}
</style>
