<template>
  <span class="full-screen" @click="fullScreen">全屏</span>
</template>

<script>
import screenfull from 'screenfull'
export default {
  name: 'FullScreen',
  methods: {
    // 全屏
    fullScreen() {
      screenfull.toggle()
      if (!screenfull.enabled) {
        screenfull.on('change', () => {
          if (screenfull.isFullscreen) {
            // 全屏时，要执行的操作
            // this.$message.success('全屏啦')
            document.getElementsByClassName('header-animat')[0].style.display = 'none'
            document.getElementsByClassName('ant-pro-multi-tab')[0].style.display = 'none'
            document.getElementsByClassName('ant-layout-sider')[0].style.display = 'none'
            document.getElementsByClassName('ant-layout sidemenu content-width-Fixed')[0].style.paddingLeft = '0px'
          } else {
            // 取消全屏时，要执行的操作
            document.getElementsByClassName('header-animat')[0].style.display = 'block'
            document.getElementsByClassName('ant-pro-multi-tab')[0].style.display = 'block'
            document.getElementsByClassName('ant-layout-sider')[0].style.display = 'block'
            document.getElementsByClassName('ant-layout sidemenu content-width-Fixed')[0].style.paddingLeft = '200px'
            // this.$message.success('退出全屏成功')
          }
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
.full-screen {
  padding: 10px;
  background-color: #00aaff;
  border-radius: 5px;
  cursor: pointer;
}
</style>
