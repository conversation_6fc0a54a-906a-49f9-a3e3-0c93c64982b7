<template>
  <el-dialog :title="title" :width="width" :visible.sync="dialog" :custom-class="customClass" :close-on-click-modal="isClose" :before-close="handleCancel">
    <slot name="content"></slot>
    <div class="dialog-footer" slot="footer" v-if="isShow">
      <el-button class="is-cancel" @click="handleCancel">{{cancelBtn}}</el-button>
      <el-button class="is-confirm" @click="handleConfirm">{{btn}}</el-button>
    </div>
  </el-dialog>
</template>

<script>
// 纯弹窗,不耦合任何业务,所有的业务逻辑处理在引入的父组件完成
export default {
  name: 'BasicDialog',
  props: {
    // 弹窗宽度
    width: {
      type: String,
      default: '50%'
    },
    // 弹窗标题
    title: {
      type: String,
      default: ''
    },
    // 弹窗开关
    dialog: {
      type: Boolean,
      default: false
    },
    // 弹窗按钮(确认操作)
    btn: {
      type: String,
      default: '确认'
    },
    cancelBtn: {
      type: String,
      default: '取消'
    },
    // 取消、关闭弹窗的函数
    callCancel: {
      type: Function
    },
    // 确认操作函数,并关闭弹窗
    callConfirm: {
      type: Function
    },
    isClose: {
      type: Boolean,
      default: false
    },
    isShow: {
      type: Boolean,
      default: true
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  methods: {
    // 取消按钮函数,调用父组件的取消弹窗函数处理弹窗关闭的逻辑以及业务处理
    handleCancel() {
      if (this.callCancel) {
        return this.callCancel()
      }
      this.$emit('update:dialog', false)
    },
    // 确认按钮函数,调用父组件的确认弹窗函数处理弹窗关闭的逻辑以及业务处理
    handleConfirm() {
      this.callConfirm && this.callConfirm()
    }
  },
  mounted() {}
}
</script>
<style lang="less" >
.el-dialog {
  &.allot-dialog {
    .el-dialog__body {
      .el-form {
        .el-form-item {
          &__content {
            width: calc(100% - 110px);
          }
        }
      }
    }
  }

  &.is-content {
    .el-dialog__header {
      //   background-color: $jnh-color6;
      text-align: center;
      .el-dialog__headerbtn .el-dialog__close {
        color: #fff;
    }
      .el-dialog__title {
        color: #fff;
      }
    }
    .el-dialog__body {
      padding: 0 0 20px;
    }
    .el-dialog__footer {
      text-align: center;
      /deep/ .el-button {
        width: 140px;
        height: 40px;
      }
    }
  }
}
</style>
