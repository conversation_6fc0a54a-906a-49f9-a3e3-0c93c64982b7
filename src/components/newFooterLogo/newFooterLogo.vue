<template>
  <p :class="{ 'pos': this.heightPage < this.browserHeight || this.heightPage === this.browserHeight, 'footer': this.heightPage > this.browserHeight}">Copyright©上海有我科技有限公司，All Rights Reserved</p>
</template>

<script>
export default {
  mounted () {
    this.browserHeight = window.innerHeight
  },
  data () {
    return {
      browserHeight: 0
    }
  },
  props: {
    heightPage: {
      type: Number,
      required: true
    }
  }
}
</script>

<style lang="less" scoped>
  .pos {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: #B5B5B5;
  }
  .footer {
    font-size: 12px;
    margin-top: 80px;
    text-align: center;
    color: #B5B5B5;
  }
</style>
