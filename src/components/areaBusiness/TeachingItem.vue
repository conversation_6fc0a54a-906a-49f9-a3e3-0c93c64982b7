<template>
  <div class="con_mand">
    <div class="rdae_dia">
      <div class="dia_hea">
        <span>诊断信息</span>
        <el-badge :value="this.classItem.length" :max="99" class="item">
          <el-button @click="toRealTimeTest" size="small" class="btnAll">查看全部</el-button>
        </el-badge>
      </div>
      <div class="tren_bor"></div>
      <div class="hea_bor"></div>
      <div class="scroll">
        <happy-scroll color="#B5BEB9" hide-horizontal :min-length-v="20">
          <div class="con_card">
            <div class="card_ti" v-for="item in classItem" :key="item.id">
              <div class="ber_ri"></div>
              <div class="ber_title">{{ item.Content }}</div>
              <div class="ber_ro">
                <span class="ro_time">{{ item.CreateTime }}</span>
                <span class="ro_btns" @click="btnss(item.Id)" :class="{ td: colors == item.Id }">查看</span>
              </div>
            </div>
            <div v-show="isshow" class="null_data">
              <img src="@/assets/lack/暂无搜索记录.png" alt="" />
              <p style="text-align: center; font-size: 15px">暂无</p>
            </div>
          </div>
        </happy-scroll>
      </div>
    </div>
  </div>
</template>
<script>
import eventBus from '@/components/eventBus/eventBus'
export default {
  data() {
    return {
      classItem: [],
      isshow: false,
      colors: ''
    }
  },
  mounted() {
    eventBus.$on('classone', classone => {
      if (classone != []) {
        this.classItem = classone
        this.isshow = false
      }
      if (classone.length == 0) {
        this.isshow = true
      }
    })
  },
  components: {},
  methods: {
    // 全部预警页面
    toRealTimeTest() {
      // this.$emit('toTesting')
      this.$router.push({ path: '/RegionManage/RealTimeTesting/RealTimeTesting' })
    },
    btnss(id) {
      this.colors = id
      this.$router.push({
        path: '/RegionManage/RealTimeTesting/TestingDetail',
        query: {
          earlyWarningPushId: id
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.dia_hea {
  margin: 10px;
  display: flex;
  justify-content: space-between;
  span {
    font-size: 16px;
    font-weight: 600;
    color: #4d5753;
    line-height: 38px;
  }
}
.tren_bor {
  width: 489px;
  height: 1px;
  margin: 16px 0 0 16px;
  background: #f1f1f1;
}
.scroll {
  width: 370px;
  height: 794px;
  margin: 24px 10px 12px 24px;
  .con_card {
    .card_ti {
      position: relative;
      width: 348px;
      height: 120px;
      background: #f6f6f6;
      border-radius: 8px;
      margin: 0 0 12px 0;
      .ber_ri {
        position: absolute;
        top: 1px;
        width: 28px;
        height: 9px;
        background: #ff4c40;
        border-radius: 8px;
      }
      .ber_title {
        position: absolute;
        top: 20px;

        left: 32px;
        margin-right: 58px;
        font-size: 15px;
        color: #4d5753;
        font-weight: 600;
        font-family: PingFangSC-Regular, PingFang SC;
      }
      .ber_ro {
        display: flex;
        justify-content: space-between;
        position: absolute;
        bottom: 26px;
        left: 37px;
        .ro_time {
          font-size: 15px;
          color: #b5b5b5;
          line-height: 15px;
          //   width: 151px;
          margin-top: 3px;
        }
        .ro_btns {
          width: 56px;
          height: 24px;
          font-size: 15px;
          color: #5b7d85;
          line-height: 23px;
          padding-left: 12px;
          border-radius: 4px;
          border: 1px solid #5b7d85;
          margin-left: 60px;
          cursor: pointer;
        }
      }
    }
  }
}
.null_data {
  position: absolute;
  left: 10%;
  top: 40%;
}
.btnAll {
  background-color: #3cbb8f;
  color: #fff;
}
</style>
