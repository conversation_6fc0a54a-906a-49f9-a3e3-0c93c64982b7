<template>
  <div class="statis">
    <div class="hp_map" ref="HpMap" id="rdehp"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {}
  },
  created() {},
  mounted() {
    this.initMap()
  },
  components: {},
  methods: {
    async initMap() {
      var myChart = echarts.init(document.getElementById('rdehp'))
      const ret = await this.$http.get(document.location.origin + '/static/lib/huangpu.json')
      echarts.registerMap('huangpu', ret)
      myChart.hideLoading()
      var scatterData = [
        { name: '第一中心小学', value: [121.44909, 31.230124], itemStyle: { color: '#FF6200' } },
        { name: '徽宁路第三小学', value: [121.492594, 31.211275], itemStyle: { color: '#FF6200' } },
        { name: '卢湾三中心小学', value: [121.471917, 31.21765], itemStyle: { color: '#FF6200' } },
        { name: '上海市裘锦秋实验学校', value: [121.465328, 31.239911], itemStyle: { color: '#FF6200' } },
        { name: '曹光彪小学', value: [121.472579, 31.23539], itemStyle: { color: '#FF6200w' } },
        { name: '上外—黄浦外国语小学', value: [121.49327, 31.200089], itemStyle: { color: '#FF6200w' } },
        { name: '上海师范大学附属卢湾实验小学', value: [121.482146, 31.208684], itemStyle: { color: '#FF6200' } },
        { name: '四川南路小学', value: [121.490188, 31.230566], itemStyle: { color: '#FF6200' } },
        { name: '报童小学', value: [121.483175, 31.235006], itemStyle: { color: '#FFB900' } },
        { name: '重庆北路小学', value: [121.469039, 31.225996], itemStyle: { color: '#FFB900' } },
        { name: '梅溪小学', value: [121.489121, 31.215486], itemStyle: { color: '#FFB900' } },
        { name: '上海市实验小学', value: [121.485449, 31.223251], itemStyle: { color: '#FFB900' } },
        { name: '回民小学', value: [121.430774, 31.237086], itemStyle: { color: '#FFB900' } },
        { name: '光明小学', value: [121.493581, 31.223431], itemStyle: { color: '#FFB900' } },
        { name: '董家渡路第二小学', value: [121.496203, 31.222747], itemStyle: { color: '#FFB900' } },
        { name: '新凌小学', value: [121.487116, 31.203401], itemStyle: { color: '#FFB900' } },
        { name: '上海市七色花小学', value: [121.46882, 31.21993], itemStyle: { color: '#FFB900' } },
        { name: '淮海中路小学', value: [121.473108, 31.220997], itemStyle: { color: '#00D4AF' } },
        { name: '海华小学', value: [121.468074, 31.202491], itemStyle: { color: '#00D4AF' } },
        { name: '巨鹿路第一小学', value: [121.460128, 31.215583], itemStyle: { color: '#00D4AF' } },
        { name: '瞿溪路小学', value: [121.473444, 31.198885], itemStyle: { color: '#00D4AF' } },
        { name: '瑞金二路小学', value: [121.466991, 31.208348], itemStyle: { color: '#00D4AF' } },
        { name: '卢湾一中心小学', value: [121.473693, 31.211782], itemStyle: { color: '#00D4AF' } },
        { name: '复兴中路第二小学', value: [121.472822, 31.215629], itemStyle: { color: '#00D4AF' } },
        { name: '黄浦区教育学院', value: [121.476993, 31.195393], itemStyle: { color: '#00D4AF' } },
        { name: '北京东路小学', value: [121.479205, 31.239255], itemStyle: { color: '#00D4AF' } },
        { name: '蓬莱路第二小学', value: [121.490061, 31.218111], itemStyle: { color: '#00D4AF' } },
        { name: '上海师范专科学校附属小学', value: [121.482043, 31.202112], itemStyle: { color: '#00D4AF' } },
        { name: '卢湾二中心小学', value: [121.462712, 31.214387], itemStyle: { color: '#00D4AF' } },
        { name: '复兴东路第三小学', value: [121.486731, 31.221157], itemStyle: { color: '#00D4AF' } },
        { name: '中华路第三小学', value: [121.497913, 31.217201], itemStyle: { color: '#00D4AF' } },
      ]
      var max = 480
      var min = 9 // todo
      var maxSize4Pin = 100
      var minSize4Pin = 20

      var convertData = function (data) {
        var res = []
        for (var i = 0; i < data.length; i++) {
          var geoCoord = scatterData[data[i].name]
          if (geoCoord) {
            res.push({
              name: data[i].name,
              value: geoCoord.concat(data[i].value),
            })
          }
        }
        return res
      }
      var option = {
        geo: {
          map: 'huangpu',
          aspectScale: 1,
          layoutCenter: ['48%', '50%'], //位置
          layoutSize: '90%', //大小
          //   zoom: 1.1, //这里是关键，一定要放在 series中
          roam: false,
          zoom: 1.1,
          itemStyle: {
            normal: { label: { show: false } },
            emphasis: { label: { show: false } },
          },
          label: {
            show: false, // 展示标签
          },
          itemStyle: {
            normal: {
              borderColor: '#02AAFF',
              borderWidth: 0.5,
              color: '#AFEBFE',
              shadowBlur: 3,
              shadowColor: 'rgba(128, 217, 248, 1)',
              shadowOffsetY: 10,
              shadowOffsetX: 10,
            },
            emphasis: {
              //   areaColor: 'rgba(2,170,255,0.5)',
            },
            emphasis: {
              areaColor: '#AFEBFE',
              itemStyle: {
                // color: {
                //   type: 'linear-gradient',
                //   x: 0,
                //   y: 800,
                //   x2: 0,
                //   colorStops: [
                //     {
                //       offset: 0,
                //       color: 'rgba(175,238,238, 0)', // 0% 处的颜色
                //     },
                //     {
                //       offset: 1,
                //       color: 'rgba(	47,79,79, .2)', // 50% 处的颜色
                //     },
                //   ],
                //   global: true, // 缺省为 false
                // },
              },
            },
          },
          z: 1,
        },
        series: [
          {
            data: scatterData, // 配置散点的坐标数据
            type: 'effectScatter',
            coordinateSystem: 'geo', // 指明散点使用的坐标系统  geo的坐标系统

            zlevel: 1,
            symbolSize: 10,
            rippleEffect: {
              scale: 1, // 设置涟漪动画的缩放比例
              period: 5,
              brushType: 'fill',
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                textStyle: {
                  color: '#000000',
                  lineHeight: 10,
                },
                formatter(params) {
                  return params.data.name
                },
              },
            },

            // roam: true,
            // zoom: 5,

            // // zoom: 2.5,
            // // zoom: 2.5, //这里是关键，一定要放在 series中
            itemStyle: {
              normal: {
                show: false,
                // opacity: 0.5,
                // color: '#00D4AF',
                normal: { label: { show: false } },
                emphasis: { label: { show: false } },
                // shadowBlur: 10,
              },
            },
          },
          // {
          //   name: 'lable',
          //   type: 'scatter',
          //   coordinateSystem: 'geo',
          //   symbol: 'pin',
          //   symbolSize: [20, 20],
          //   hoverAnimation: true,
          //   zlevel: 2,
          //   label: {
          //     normal: {
          //       show: true,
          //       textStyle: {
          //         color: '#2A2D2C',
          //         lineHeight: 18,
          //         fontSize: 14
          //       },
          //       formatter (params) {
          //         return params.data.name
          //       }
          //     }
          //   },
          //   // itemStyle: {
          //   //   normal: {
          //   //     color: '#FFE579', //标志颜色
          //   //     opacity: 1,
          //   //     borderColor: '#FFE579',
          //   //     // shadowBlur: 30,
          //   //     borderWidth: 0.6,
          //   //   },
          //   // },
          //   showEffectOn: 'render',
          //   rippleEffect: {
          //     brushType: 'stroke'
          //   },
          //   data: scatterData
          // }
        ],
      }
      myChart.setOption(option)
    },
  },
}
</script>
<style lang="less" scoped>
.hp_map {
  width: 610px;
  height: 800px;
  position: absolute;
  left: 44%;
}
</style>
