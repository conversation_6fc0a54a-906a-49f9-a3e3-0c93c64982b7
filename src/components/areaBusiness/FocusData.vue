<template>
  <div class="con_mand">
    <div class="tren_title">
      <span> 集中做题时间段</span>
    </div>
    <div class="tren_bor"></div>
    <div class="tren_sele">
      <el-select v-model="classValue" placeholder="请选择" class="seles" style="width: 150px; height: 50px" @change="getClassItem">
        <el-option v-for="item in ClassOptions" :key="item.value" :label="item.label" :value="item"> </el-option>
      </el-select>
      <el-select v-model="studValue" placeholder="请选择" class="seles" style="width: 150px" @change="getStudItem">
        <el-option v-for="item in StudOptions" :key="item.value" :label="item.label" :value="item"> </el-option>
      </el-select>
    </div>
    <div class="con_focs" ref="focus_ec"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      ClassOptions: [
        {
          value: '0',
          label: '全部年级'
        },
        {
          value: '1',
          label: '一年级'
        },
        {
          value: '2',
          label: '二年级'
        },
        {
          value: '3',
          label: '三年级'
        },
        {
          value: '4',
          label: '四年级'
        },
        {
          value: '5',
          label: '五年级'
        }
      ],
      StudOptions: [
        {
          value: 'week',
          label: '近一周'
        },
        {
          value: 'month',
          label: '近一月'
        },
        {
          value: 'xq',
          label: '本学期'
        }
      ],
      classValue: '全部年级',
      studValue: '近一周',
      userId: '',
      gradeId: '0',
      whereTime: 'week',
      datalist: []
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
  },
  mounted() {
    // this.GetTimeIntervalBySchool()
    this.GetTimeIntervalBySchool()
  },
  components: {},
  methods: {
    getClassItem(item) {
      this.classValue = item.label
      this.gradeId = item.value
      switch (item.value) {
        case '0':
          this.gradeId = 0
          this.GetTimeIntervalBySchool()
          break
        case '1':
          this.gradeId = 1
          this.GetTimeIntervalBySchool()
          break
        case '2':
          this.gradeId = 2
          this.GetTimeIntervalBySchool()
          break
        case '3':
          this.gradeId = 3
          this.GetTimeIntervalBySchool()
          break
        case '4':
          this.gradeId = 4
          this.GetTimeIntervalBySchool()
          break
        case '5':
          this.gradeId = 5
          this.GetTimeIntervalBySchool()
          break
      }
    },
    getStudItem(item) {
      this.studValue = item.label
      this.whereTime = item.value
      switch (item.value) {
        case 'week':
          this.whereTime = 'week'
          this.GetTimeIntervalBySchool()
          break
        case 'month':
          this.whereTime = 'month'
          this.GetTimeIntervalBySchool()
          break
        case 'xq':
          this.whereTime = 'xq'
          this.GetTimeIntervalBySchool()
          break
      }
    },
    async GetTimeIntervalBySchool() {
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetTimeIntervalBySchool', {
        userId: this.userId,
        grade: this.gradeId,
        whereTime: this.whereTime
      })
      this.datalist = res.Data
      var greenPieData = [
        {
          value: 30,
          name: '00:00-01:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)'
            }
          }
        },
        {
          value: 30,
          name: '02:00-03:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)'
            }
          }
        },
        {
          value: 30,
          name: '04:00-05:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)'
            }
          }
        },
        {
          value: 30,
          name: '06:00-07:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)'
            }
          }
        },
        {
          value: 30,
          name: '08:00-09:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)'
            }
          }
        },
        {
          value: 30,
          name: '10:00-11:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.2)'
            }
          }
        },
        {
          value: 30,
          name: '12:00-13:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.2)'
            }
          }
        },
        {
          value: 30,
          name: '14:00-15:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.2)'
            }
          }
        },
        {
          value: 30,
          name: '16:00~17:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)'
            }
          }
        },
        {
          value: 30,
          name: '18:00~19:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)'
            }
          }
        },
        {
          value: 30,
          name: '20:00~21:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)'
            }
          }
        },
        {
          value: 30,
          name: '22:00~23:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)'
            }
          }
        }
      ]
      // 蓝色海螺 Series Data
      var bluePieData = [
        {
          value: this.datalist[0].StuCount,
          name: this.datalist[0].Time
        },
        {
          value: this.datalist[1].StuCount,
          name: this.datalist[1].Time
        },
        {
          value: this.datalist[2].StuCount,
          name: this.datalist[2].Time
        },
        {
          value: this.datalist[3].StuCount,
          name: this.datalist[3].Time
        },
        {
          value: this.datalist[4].StuCount,
          name: this.datalist[4].Time
        },
        {
          value: this.datalist[5].StuCount,
          name: this.datalist[5].Time
        },
        {
          value: this.datalist[6].StuCount,
          name: this.datalist[6].Time
        },
        {
          value: this.datalist[7].StuCount,
          name: this.datalist[7].Time
        },
        {
          value: this.datalist[8].StuCount,
          name: this.datalist[8].Time
        },
        {
          value: this.datalist[9].StuCount,
          name: this.datalist[9].Time
        },
        {
          value: this.datalist[10].StuCount,
          name: this.datalist[10].Time
        },
        {
          value: this.datalist[11].StuCount,
          name: this.datalist[11].Time
        }
      ]

      // 角度对应的时间
      var deg_clock = {
        0: 0,
        30: 2,
        60: 4,
        90: 6,
        120: 8,
        150: 10,
        180: 12,
        210: 14,
        240: 16,
        270: 18,
        300: 20,
        330: 22,
        360: 24
      }

      // 根据时钟区域下标找到对应图片
      function formatterClockPic(i) {
        if (i == -1 || i === 0 || i == 23) {
          return '{imgOwi|}'
        }
        if (i == 1 || i == 2 || i == 3 || i == 4 || i == 5 || i == 19 || i == 21 || i == 22) {
          return '{imgMoon|}'
        }
        if (i == 6) {
          return '{imgOffice|}'
        }
        if (i == 7 || i == 8 || i == 9 || i == 10 || i == 11 || i == 13 || i == 14 || i == 15 || i == 17) {
          return '{imgSun|}'
        }
        if (i == 12 || i == 18) {
          return '{imgMeal|}'
        }
        if (i == 16) {
          return '{imgCoffee|}'
        }
        if (i == 20) {
          return '{imgPc|}'
        }
      }

      function tooltipFormatterClockPie(i) {
        if (i == -1 || i === 0 || i == 23) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 1 || i == 2 || i == 3 || i == 4 || i == 5 || i == 19 || i == 21 || i == 22) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 6) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 7 || i == 8 || i == 9 || i == 10 || i == 11 || i == 13 || i == 14 || i == 15 || i == 17) {
          return '<img src="https://tongji.baidu.com/research/img/icon-day.5342b895fe23eafdb202b563ee5efbab.svg">'
        }
        if (i == 12 || i == 18) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 16) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 20) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
      }
      const myChart = echarts.init(this.$refs.focus_ec)
      myChart.setOption({
        polar: {
          radius: [63, 150],
          center: ['50%', '50%']
        },
        // 极坐标
        angleAxis: {
          min: 0,
          max: 360,
          startAngle: 91,
          //         interval: 28,
          clockwise: true, // 顺时针
          zlevel: 1,
          // 圆线
          axisLine: {
            show: false
          },
          // 圆线上的刻度
          axisTick: {
            show: false,

            lineStyle: {
              color: '#fff'
            }
          },
          // 圆线上的刻度标签
          axisLabel: {
            color: '#666666',
            interval: '2',
            fontWeight: 'bold',
            formatter: function(deg) {
              return deg_clock[deg]
            }
          },
          // 圆中直区域分割线
          splitLine: {
            show: true,
            lineStyle: {
              color: '#EEEEEE',
              type: 'dashed'
            }
          },
          // 圆中被分割的区域
          splitArea: {
            show: false
          }
        },
        // 刻度
        axisTick: {
          show: false,
          lineStyle: {
            color: '#666666'
          }
        },
        // 刻度标签
        axisLabel: {
          show: false
        },
        radiusAxis: {
          show: false
        },
        tooltip: {
          trigger: 'item'
        },

        series: [
          {
            name: 'green',
            type: 'pie',
            radius: [63, 50],
            center: ['49.5%', '50%'],
            roseType: 'radius',
            zlevel: 2,
            tooltip: {
              show: false
            },
            label: {
              color: '#666666',
              show: true,
              formatter: '',
              position: 'outside'
            },
            emphasis: {
              label: {
                show: true,
                formatter: function(params) {
                  return formatterClockPic(params.dataIndex - 2)
                },
                // rich: clockPic,
                color: '#fff'
              }
            },
            labelLine: {
              show: false,
              length: 40,
              length2: 0
            },
            hoverAnimation: true, // hover时是否有放大效果
            itemStyle: {
              normal: {
                color: 'rgba(23, 240, 204,0)'
              },
              emphasis: {
                color: 'rgba(23, 240, 204)'
              }
            },
            data: greenPieData
          },
          {
            name: '时段',
            type: 'pie',
            radius: [40, 130],
            center: ['50%', '50%'],
            roseType: 'area', // 是否展示成南丁格尔图，通过半径区分数据大小。可选择两种模式：radius/area
            tooltip: {
              show: true
            },
            label: {
              show: false,
              position: 'center',
              color: '#666666'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
                formatter: ['{a|{d}%}', '{b|{b}}'].join('\n'),
                zlevel: 10,
                rich: {
                  a: {
                    color: '#424242',
                    fontSize: 18,
                    lineHeight: 30,
                    verticalAlign: 'bottom'
                  },
                  b: {
                    color: '#a7a9c7',
                    fontSize: 10,
                    lineHeight: 24
                  }
                }
              }
            },
            labelLine: {
              show: false
            },
            zlevel: 10,
            hoverAnimation: false, // hover时是否有放大效果
            clockwise: true, // 饼图的扇区是否是顺时针排布
            startAngle: 92, // 起始角度
            minAngle: 40, // 最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
            itemStyle: {
              normal: {
                color: '#43715A'
              },
              emphasis: {
                color: '#9AC313'
              }
            },
            data: bluePieData
          }
        ]
      })
    }
  }
}
</script>
<style lang="less" scoped>
.con_mand {
  width: 100%;
  height: 100%;
}
.tren_title {
  padding: 20px 0 0 16px;
  justify-content: space-around;
  span {
    font-size: 16px;
    font-weight: 600;
    color: #4d5753;
    line-height: 35px;
  }
}
.tren_sele {
  margin: 7px 11px;
  display: flex;

  .seles {
    width: 86px;
    height: 20px;
    margin-right: 10px;
  }
}
.tren_bor {
  width: 100%;
  height: 1px;
  margin: 16px 0 0 16px;
  background: #f1f1f1;
}
.con_focs {
  width: 90%;
  height: 300px;
  margin: auto;
}
</style>
