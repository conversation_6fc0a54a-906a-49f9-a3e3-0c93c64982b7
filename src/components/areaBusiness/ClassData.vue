<template>
  <div class="con_class">
    <div class="tren_title">
      <span> 各年级数据（本学期）</span>
    </div>
    <div class="tren_bor"></div>
    <div class="class_title">
      <div class="title1">
        <div class="title_ro"></div>
        <span class="title_st">正确率</span>
      </div>
      <div class="title1">
        <div class="title_ro"></div>
        <span class="title_st">完成率</span>
      </div>
    </div>
    <div class="bar_title">
      <span style="color: #5cc0ff">一年级</span>
      <span style="color: #5cc0ff">{{ acclist }}%</span>
      <span style="color: #ffbf1f">二年级</span>
      <span style="color: #ffbf1f">{{ acclist2 }}%</span>
      <span style="color: #448870">三年级</span>
      <span style="color: #448870">{{ acclist3 }}%</span>
      <span style="color: #79bebd">四年级</span>
      <span style="color: #79bebd">{{ acclist4 }}%</span>
      <span style="color: #f57579">五年级</span>
      <span style="color: #f57579">{{ acclist5 }}%</span>
    </div>
    <div class="bar_title1">
      <span style="color: #5cc0ff">一年级</span>
      <span style="color: #5cc0ff">{{ finlist }}%</span>
      <span style="color: #ffbf1f">二年级</span>
      <span style="color: #ffbf1f">{{ finlist2 }}%</span>
      <span style="color: #448870">三年级</span>
      <span style="color: #448870">{{ finlist3 }}%</span>
      <span style="color: #79bebd">四年级</span>
      <span style="color: #79bebd">{{ finlist4 }}%</span>
      <span style="color: #f57579">五年级</span>
      <span style="color: #f57579">{{ finlist5 }}%</span>
    </div>
    <div class="con_ech_var">
      <div class="con_left">
        <div class="con_bar" style="width: 200%; height: 100%" id="classech" ref="class_bar"></div>
      </div>
      <div class="con_right">
        <div class="con_bar" ref="class_bar2" style="width: 200%; height: 100%"></div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      userId: '',
      classData: [],
      // 正确率
      acclist: '',
      acclist2: '',
      acclist3: '',
      acclist4: '',
      acclist5: '',
      // 完成率
      finlist: '',
      finlist2: '',
      finlist3: '',
      finlist4: '',
      finlist5: '',
      // 参与度
      Parlist: '',
      Parlist1: '',
      Parlist2: '',
      Parlist3: '',
      Parlist4: '',
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
  },
  mounted() {
    this.ClassList()
    this.ClassListTow()
  },
  methods: {
    async ClassList() {
      const myChart = echarts.init(this.$refs.class_bar)
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetTeacherManageTop', {
        userId: this.userId,
      })
      this.acclist = res.Data[0].Accuracy.toString()
      this.acclist2 = res.Data[1].Accuracy.toString()
      this.acclist3 = res.Data[2].Accuracy.toString()
      this.acclist4 = res.Data[3].Accuracy.toString()
      this.acclist5 = res.Data[4].Accuracy.toString()

      myChart.setOption({
        // legend: {
        //   x:'top',
        //   itemWidth:14,
        //   itemHeight: 14,
        //   itemGap: 17,
        //   borderRadius: 100,
        // },
        // title: {
        //   text: '| 正确率',
        // },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a} : <br />{c}%',
        },
        polar: {
          center: ['33%', '25%'],
          radius: '250%', // 图形大小
        },
        angleAxis: {
          show: false,
          startAngle: 90,
          min: 0,
          max: 100,
        },
        radiusAxis: {
          type: 'category',
          show: false,
          data: ['一年级', '二年级', '三年级', '四年级', '五年级'],
        },
        series: [
          {
            type: 'bar',
            name: '五年级',
            coordinateSystem: 'polar',
            barWidth: 9, // 宽度
            barCategoryGap: '40%',
            data: [this.acclist5],
            roundCap: true,
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
            itemStyle: {
              color: '#f57579',
            },
          },
          {
            type: 'bar',
            name: '四年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            data: [this.acclist4],
            roundCap: true,
            itemStyle: {
              color: '#79bebd',
            },
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
          {
            type: 'bar',
            name: '三年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            data: [this.acclist3],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#448870',
            },
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
          {
            type: 'bar',
            name: '二年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            data: [this.acclist2],
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
            roundCap: true,
            itemStyle: {
              color: '#facc14',
            },
          },
          {
            type: 'bar',
            name: '一年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            data: [this.acclist],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#5cc0ff',
            },
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
        ],
      })
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    async ClassListTow() {
      const myChart = echarts.init(this.$refs.class_bar2)
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetTeacherManageTop', {
        userId: this.userId,
      })
      this.finlist = res.Data[0].Finish.toString()
      this.finlist2 = res.Data[1].Finish.toString()
      this.finlist3 = res.Data[2].Finish.toString()
      this.finlist4 = res.Data[3].Finish.toString()
      this.finlist5 = res.Data[4].Finish.toString()
      myChart.setOption({
        // legend: {
        //   x:'top',
        // },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a} : <br />{c}%',
        },
        polar: {
          center: ['33%', '25%'],
          radius: '250%', // 图形大小
        },
        angleAxis: {
          show: false,
          startAngle: 90,
          min: 0,
          max: 100,
        },
        radiusAxis: {
          type: 'category',
          show: false,
          data: ['一年级', '二年级', '三年级', '四年级', '五年级'],
        },
        series: [
          {
            type: 'bar',
            name: '五年级',
            coordinateSystem: 'polar',
            barWidth: 9, // 宽度
            barCategoryGap: '40%',
            // data: ["76.25","47.09","22.09"],
            data: [this.finlist5],
            roundCap: true,
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
            itemStyle: {
              color: '#f57579',
            },
          },
          {
            type: 'bar',
            name: '四年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            // data: ["14.09","55.09","27.09"]
            data: [this.finlist4],
            roundCap: true,
            itemStyle: {
              color: '#79bebd',
            },
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
          {
            type: 'bar',
            name: '三年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.finlist3],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#448870',
            },
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
          {
            type: 'bar',
            name: '二年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.finlist2],
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
            roundCap: true,
            itemStyle: {
              color: '#facc14',
            },
          },
          {
            type: 'bar',
            name: '一年级',
            coordinateSystem: 'polar',
            barWidth: 9,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.finlist],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#5cc0ff',
            },
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
        ],
      })
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    async ClassListTress() {
      const myChart = echarts.init(this.$refs.class_bar3)
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetTeacherManageTop', {
        userId: 1,
      })
      this.Parlist = res.Data[0].Partake.toString()
      this.Parlist1 = res.Data[1].Partake.toString()
      this.Parlist2 = res.Data[2].Partake.toString()
      this.Parlist3 = res.Data[3].Partake.toString()
      this.Parlist4 = res.Data[4].Partake.toString()
      myChart.setOption({
        // legend: {
        //   x:'top',
        // },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a} : <br />{c}%',
        },
        polar: {
          center: ['25%', '25%'],
          radius: '280%', // 图形大小
        },
        angleAxis: {
          show: false,
          startAngle: 90,
          min: 0,
          max: 100,
        },
        radiusAxis: {
          type: 'category',
          show: false,
          data: ['一年级', '二年级', '三年级', '四年级', '五年级'],
        },
        series: [
          {
            type: 'bar',
            name: '五年级',
            coordinateSystem: 'polar',
            barWidth: 12, // 宽度
            barCategoryGap: '50%',
            // data: ["76.25","47.09","22.09"],
            data: [this.Parlist4],
            roundCap: true,
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
            itemStyle: {
              color: '#f57579',
            },
          },
          {
            type: 'bar',
            name: '四年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["14.09","55.09","27.09"]
            data: [this.Parlist3],
            roundCap: true,
            itemStyle: {
              color: '#79bebd',
            },
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
          {
            type: 'bar',
            name: '三年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.Parlist2],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#448870',
            },
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
          {
            type: 'bar',
            name: '二年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.Parlist1],
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB',
            },
            roundCap: true,
            itemStyle: {
              color: '#facc14',
            },
          },
          {
            type: 'bar',
            name: '一年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.Parlist],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#5cc0ff',
            },
            backgroundStyle: {
              color: '#E8EAEB',
            },
          },
        ],
      })
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
}
</script>
<style lang="less" scoped>
.con_class {
  width: 100%;
  height: 100%;
  // display: flex;
  position: relative;
  .tren_title {
    padding: 20px 0 0 16px;
    span {
      font-size: 16px;
      font-weight: 600;
      color: #4d5753;
      line-height: 35px;
    }
    .tren_sele {
      margin: -3px 0 0 24px;
      .seles {
        width: 86px;
        height: 20px;
        margin-right: 16px;
      }
    }
  }
  .tren_bor {
    width: 489px;
    height: 1px;
    margin: 16px 0 0 16px;
    background: #f1f1f1;
  }
  .class_title {
    display: flex;
    justify-content: space-evenly;
    .title1 {
      width: 50%;
      display: flex;
      align-items: center;
      margin-top: 20px;
      /* margin-right: 1.328125rem; */
      margin-left: 17px;
      .title_ro {
        width: 6px;
        height: 21px;
        background: #5b7d85;
        border-radius: 12px;
        margin-right: 18px;
      }
      .title_st {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.7);
        font-weight: 600;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }
  }
  .con_ech_var {
    display: flex;
    justify-content: space-around;
    width: 100%;
    height: 300px;
    .con_left {
      width: 230px;
      margin-top: 30px;
      .con_bar {
        width: 100%;
        height: 100%;
        position: relative;
        right: 32%;
        top: 22%;
      }
    }
    .con_right {
      width: 230px;
      margin-top: 30px;
      .con_bar {
        width: 100%;
        height: 100%;
        position: relative;
        right: 32%;
        top: 20%;
      }
    }
  }
  .bar_title {
    display: flex;
    flex-direction: column;
    position: absolute;
    left: 40%;
    top: 26%;
    span {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }
  .bar_title1 {
    display: flex;
    flex-direction: column;
    position: absolute;
    // top: 145px;
    left: 87%;
    top: 26%;
    span {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }
  .bar_title2 {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 185px;
    left: 918px;
    span {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }
}
</style>
