<template>
  <div class="statis">
    <div class="dia_hea">
      <span>全区监测数据</span>
      <el-button @click="openTesting" size="small" class="btnAll">查看全部</el-button>
    </div>
    <div class="tren_bor"></div>
    <div class="con_mansta">
      <div class="con_title">
        <div class="tologin">
          <div class="to_title1">全区做练习累积张次</div>
          <div class="to_title2">{{ manslist.DoPaperTotal }}</div>
        </div>
        <div class="item_one"></div>
        <div class="tologin">
          <div class="to_title1">全区登录累积人次</div>
          <div class="to_title2">{{ manslist.LoginTotal }}</div>
        </div>
      </div>
      <div class="mansta_lo">
        <div class="tologin">
          <div class="to_title1">做题总数</div>
          <div class="to_title2">{{ manslist.ItemCount }}</div>
        </div>
        <div class="tologin">
          <div class="to_title1">做练习数</div>
          <div class="to_title2">{{ manslist.PaperCount }}</div>
        </div>
        <div class="tologin">
          <div class="to_title1">学生总数</div>
          <div class="to_title2">{{ manslist.StudentCount }}</div>
        </div>
        <div class="tologin">
          <div class="to_title1">班级总数</div>
          <div class="to_title2">{{ manslist.ClassCount }}</div>
        </div>
      </div>
      <div class="mansta_lo">
        <div class="tologin">
          <div class="to_title1">今日练习</div>
          <div class="to_title2">{{ manslist.TodayDoPaperCount }}</div>
        </div>
        <div class="tologin">
          <div class="to_title1">练习人数</div>
          <div class="to_title2">{{ manslist.TodayDoCount }}</div>
        </div>
        <div class="tologin">
          <div class="to_title1">今日登录</div>
          <div class="to_title2">{{ manslist.TodayLogin }}</div>
        </div>
        <div class="tologin">
          <div class="to_title1">注册教师</div>
          <div class="to_title2">{{ manslist.TeacherCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      manslist: [],
      userId: ''
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.getManlist()
  },
  components: {},
  methods: {
    async getManlist() {
      const res = await this.$uwonhttp.post('/Manager/ManagerReport/UseMonitoring', { UserId: this.userId })
      this.manslist = res.data.Data
    },
    openTesting() {
      this.$router.push({ path: '/RegionManage/businessArea/TestingPage' })
    }
  }
}
</script>
<style lang="less" scoped>
.statis {
  //   margin: 10px;
  .dia_hea {
    display: flex;
    padding: 5px 0 0 16px;
    justify-content: space-between;
    span {
      font-size: 16px;
      font-weight: 600;
      color: #4d5753;
      line-height: 38px;
    }
  }
  .tren_bor {
    width: 489px;
    height: 1px;
    margin: 16px 0 0 16px;
    background: #f1f1f1;
  }
  .btnAll {
    right: 0.067708rem;
    height: 30px;
    top: 8px;
    line-height: 7px;
    position: relative;
    background-color: #3cbb8f;
    color: #fff;
  }
}
.con_mansta {
  margin: 35px 0;
  .con_title {
    margin-bottom: 40px;
    display: flex;
    justify-content: space-around;
    position: relative;
    top: 15px;
    .item_one {
      width: 1px;
      background: #ccc;
    }
    .tologin {
      .to_title1 {
        font-size: 14px;
        color: #4d5753;
      }
      .to_title2 {
        font-size: 22px;
        color: #3cbb8f;
        margin-top: 3px;
        font-weight: 600;
        font-family: PingFangSC-Medium, PingFang SC;
      }
    }
  }
  .mansta_lo {
    margin-bottom: 55px;
    position: relative;
    display: flex;
    justify-content: space-around;
    left: 23px;
    top: 22px;

    .tologin {
      width: 100px;
      .to_title1 {
        font-size: 14px;
        color: #4d5753;
      }
      .to_title2 {
        font-size: 22px;
        color: #3cbb8f;
        margin-top: 3px;
        font-weight: 600;
        font-family: PingFangSC-Medium, PingFang SC;
      }
    }
  }
}
/deep/.el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}
/deep/.el-pagination {
  white-space: nowrap;
  padding: 0.010417rem 0.026042rem;
  color: #303133;
  font-weight: 700;
  position: relative;
  left: 25%;
  top: 14px;
}
</style>
