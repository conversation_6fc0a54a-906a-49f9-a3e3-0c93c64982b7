<template>
  <div class="con_mand">
    <!-- <el-loading
      v-loading.fullscreen.lock="fullscreenLoading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    ></el-loading> -->
    <div class="tren_title">
      <span>教学实时诊断预警</span>
      <div class="tren_sele">
        <el-select v-model="classValue" placeholder="请选择" @change="getGradeId">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item"> </el-option>
        </el-select>
      </div>
    </div>
    <div class="map_to">
      <div class="to_ye"></div>
      <div class="to_re"></div>
      <div class="to_ge"></div>
    </div>

    <div v-show="isShowMap">
      <div class="map_con" ref="map_co" id="rdewa"></div>
    </div>
    <div v-show="HPShowMap">
      <hpMap ref="hpDetail"></hpMap>
    </div>
    <!-- <div class="map_con" ref="map_co" id="rdewa"></div> -->

    <!-- <div v-show="!isShowMap" class="lack-map">
      <img src="@/assets/lack/暂无搜索记录.png" alt="" />
      <p style="text-align: center; font-size: 15px">敬请期待</p>
    </div> -->
  </div>
</template>
<script>
import * as echarts from 'echarts'
import eventBus from '@/components/eventBus/eventBus'
import hpMap from '@/components/areaBusiness/hpMap'
export default {
  data() {
    return {
      options: [
        {
          value: '0',
          label: '全部年级',
        },
        {
          value: '1',
          label: '一年级',
        },
        {
          value: '2',
          label: '二年级',
        },
        {
          value: '3',
          label: '三年级',
        },
        {
          value: '4',
          label: '四年级',
        },
        {
          value: '5',
          label: '五年级',
        },
      ],
      classValue: '全部年级',
      gradeId: '0', // 年级id
      isShowMap: false,
      classone: [],
      HPShowMap: false,
      userId: '',
      // fullscreenLoading: false,
    }
  },
  mounted() {
    this.initMap()
    this.GetEarlyWarningPush()
    // this.$refs.hpDetail.initMap()
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    const realmName = window.location.href.split('/')[2]
    // if (realmName === this.Facturl.$hrefName) {
    if (realmName === 'xh.eduwon.cn') {
      this.isShowMap = true // 徐汇
      this.HPShowMap = false // 黄浦
    } else if (realmName === 'localhost:5001') {
      this.isShowMap = true
      this.HPShowMap = false // 黄浦
    } else {
      this.isShowMap = false
    }
    if (realmName === 'hp.eduwon.cn') {
      this.HPShowMap = true // 黄浦
      this.isShowMap = false // 徐汇
    } else if (realmName === 'localhost:5001') {
      this.HPShowMap = true
      this.isShowMap = false // 徐汇
    } else {
      this.HPShowMap = false
    }
    // if (realmName === 'xh.eduwon.cn') {
    //   this.isShowMap = true // 徐汇
    // } else {
    //   this.isShowMap = false
    // }
    // if (realmName === 'hp.eduwon.cn') {
    //   this.HPShowMap = true
    // } else {
    //   this.isShowMap = false
    // }
  },
  components: {
    hpMap,
  },
  methods: {
    getGradeId(item) {
      // this.fullscreenLoading = true
      this.classValue = item.label
      this.gradeId = item.value
      switch (item.value) {
        case '0':
          this.gradeId = 0
          this.GetEarlyWarningPush()
          break
        case '1':
          this.gradeId = 1
          this.GetEarlyWarningPush()
          break
        case '2':
          this.gradeId = 2
          this.GetEarlyWarningPush()
          break
        case '3':
          this.gradeId = 3
          this.GetEarlyWarningPush()
          break
        case '4':
          this.gradeId = 4
          this.GetEarlyWarningPush()
          break
        case '5':
          this.gradeId = 5
          this.GetEarlyWarningPush()
          break
      }
    },
    async GetEarlyWarningPush() {
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetEarlyWarningPush', {
        userId: this.userId,
        gradeId: this.gradeId,
      })
      this.classone = res.Data
      eventBus.$emit('classone', this.classone)
    },
    async initMap() {
      var myChart = echarts.init(document.getElementById('rdewa'))
      const ret = await this.$http.get(document.location.origin + '/static/lib/xujiahui.json')
      echarts.registerMap('xuhui', ret)
      myChart.hideLoading()
      var scatterData = [
        { name: '向阳小学', value: [121.456975, 31.207594], itemStyle: { color: '#FF6200' } },
        { name: '建襄小学', value: [121.453288, 31.203303], itemStyle: { color: '#FF6200' } },
        { name: '高一小学', value: [121.443507, 31.199537], itemStyle: { color: '#FF6200' } },
        { name: '爱菊小学', value: [121.442532, 31.213038], itemStyle: { color: '#FF6200' } },
        { name: '世界小学', value: [121.439849, 31.207624], itemStyle: { color: '#FF6200w' } },
        { name: '虹桥路小学', value: [121.429095, 31.196174], itemStyle: { color: '#FF6200w' } },
        // { name: "日晖新村小学", value: [121.464707, 31.19537] },
        { name: '教科院实验小学', value: [121.453331, 31.137782], itemStyle: { color: '#FF6200' } },
        { name: '交大附小', value: [121.427643, 31.192254], itemStyle: { color: '#FF6200' } },
        { name: '东二小学', value: [121.451001, 31.183158], itemStyle: { color: '#FFB900' } },
        { name: '求知小学', value: [121.436676, 31.169672], itemStyle: { color: '#FFB900' } },
        { name: '东三小学', value: [121.432903, 31.174072], itemStyle: { color: '#FFB900' } },
        { name: '田林小学', value: [121.420684, 31.170854], itemStyle: { color: '#FFB900' } },
        // { name: "田林三小", value: [121.427524, 31.170786] },
        // { name: "吴中路小学", value: [121.422441, 31.191893] },
        // { name: "上师大一附小", value: [121.418283, 31.155369] },
        // { name: "高一小学（华展校区）", value: [121.521931, 31.305145] },
        // { name: "汇师小学（中城校区）", value: [121.445356, 31.161514] },
        // { name: "上海小学", value: [121.436159, 31.138316] },
        // { name: '园南小学',value: [121.441078,31.141612] },
        // { name: '龙华小学',value: [121.450825,31.171285] },
        // { name: '龙南小学',value: [121.453709,31.155924] },
        // { name: '长桥二小',value: [121.443935,31.134668] },
        // { name: '启新小学',value: [121.41989,31.13093] },
        // { name: '上实验附小',value: [121.429164,31.131372] },
        // { name: '徐浦小学',value: [121.460984,31.122602] },
        // { name: '一中心小学',value: [121.455830,31.212686] },
        // { name: '逸夫小学',value: [121.443125,31.133642] },
        // { name: '汇师小学',value: [121.432959,31.188136] },
        // { name: '光启小学',value: [121.438591,31.183509] },
        { name: '江南新村小学', value: [121.462576, 31.192572], itemStyle: { color: '#FFB900' } },
        { name: '田林四小', value: [121.426714, 31.176783], itemStyle: { color: '#FFB900' } },
        { name: '樱花园小学', value: [121.41349, 31.147002], itemStyle: { color: '#FFB900' } },
        { name: '向阳育才小学', value: [121.43834, 31.14891], itemStyle: { color: '#FFB900' } },
        { name: '华泾小学', value: [121.451945, 31.126244], itemStyle: { color: '#FFB900' } },
        { name: '盛大花园小学', value: [121.453633, 31.176011], itemStyle: { color: '#00D4AF' } },
        { name: '华理大附小', value: [121.423829, 31.137896], itemStyle: { color: '#00D4AF' } },
        { name: '康宁科技实验小学', value: [121.426649, 31.15585], itemStyle: { color: '#00D4AF' } },
        { name: '体职院附小', value: [121.446959, 31.131938], itemStyle: { color: '#00D4AF' } },
        { name: '漕开发小学', value: [121.401339, 31.156795], itemStyle: { color: '#00D4AF' } },
        // { name: '世界外国语小学',value: [121.418098,31.150304] },
        { name: '师三实验学校', value: [121.43819, 31.162909], itemStyle: { color: '#00D4AF' } },
        { name: '位育体校', value: [121.44394, 31.118045], itemStyle: { color: '#00D4AF' } },
        { name: '位育实验学校', value: [121.453575, 31.217757], itemStyle: { color: '#00D4AF' } },
        { name: '区实验小学', value: [121.453331, 31.137782], itemStyle: { color: '#00D4AF' } },
        { name: '徐教院附小', value: [121.438083, 31.133658], itemStyle: { color: '#00D4AF' } },
        { name: '康健外国语小学', value: [121.413692, 31.154404], itemStyle: { color: '#00D4AF' } },
      ]
      var max = 480
      var min = 9 // todo
      var maxSize4Pin = 100
      var minSize4Pin = 20

      var convertData = function (data) {
        var res = []
        for (var i = 0; i < data.length; i++) {
          var geoCoord = scatterData[data[i].name]
          if (geoCoord) {
            res.push({
              name: data[i].name,
              value: geoCoord.concat(data[i].value),
            })
          }
        }
        return res
      }
      var option = {
        geo: {
          map: 'xuhui',
          aspectScale: 1,
          layoutCenter: ['50%', '50%'], // 地图位置
          layoutSize: '100%',
          roam: false,
          zoom: 1.4,
          label: {
            show: false, // 展示标签
          },
          itemStyle: {
            normal: {
              borderColor: '#02AAFF',
              borderWidth: 0.5,
              color: '#AFEBFE',
              shadowBlur: 3,
              shadowColor: 'rgba(128, 217, 248, 1)',
              shadowOffsetY: 10,
              shadowOffsetX: 10,
            },
            emphasis: {
              areaColor: 'rgba(2,170,255,0.5)',
            },
            emphasis: {
              areaColor: 'rgba((255, 182, 64, 1))',
              itemStyle: {
                color: {
                  type: 'linear-gradient',
                  x: 0,
                  y: 800,
                  x2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(175,238,238, 0)', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(	47,79,79, .2)', // 50% 处的颜色
                    },
                  ],
                  global: true, // 缺省为 false
                },
              },
            },
          },
          z: 99,
        },
        series: [
          {
            data: scatterData, // 配置散点的坐标数据
            type: 'effectScatter',
            coordinateSystem: 'geo', // 指明散点使用的坐标系统  geo的坐标系统
            zlevel: 2,
            symbolSize: 10,
            rippleEffect: {
              scale: 1, // 设置涟漪动画的缩放比例
              period: 5,
              brushType: 'fill',
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                textStyle: {
                  color: '#000000',
                  lineHeight: 10,
                },
                formatter(params) {
                  return params.data.name
                },
              },
            },
            zoom: 10, //这里是关键，一定要放在 series中
            itemStyle: {
              normal: {
                show: true,
                // opacity: 0.5,
                color: '#00D4AF',
                // normal: { label: { show: true } },
                // emphasis: { label: { show: true } },
                // shadowBlur: 10,
              },
            },
          },
          // {
          //   name: 'lable',
          //   type: 'scatter',
          //   coordinateSystem: 'geo',
          //   symbol: 'pin',
          //   symbolSize: [20, 20],
          //   hoverAnimation: true,
          //   zlevel: 2,
          //   label: {
          //     normal: {
          //       show: true,
          //       textStyle: {
          //         color: '#2A2D2C',
          //         lineHeight: 18,
          //         fontSize: 14
          //       },
          //       formatter (params) {
          //         return params.data.name
          //       }
          //     }
          //   },
          //   // itemStyle: {
          //   //   normal: {
          //   //     color: '#FFE579', //标志颜色
          //   //     opacity: 1,
          //   //     borderColor: '#FFE579',
          //   //     // shadowBlur: 30,
          //   //     borderWidth: 0.6,
          //   //   },
          //   // },
          //   showEffectOn: 'render',
          //   rippleEffect: {
          //     brushType: 'stroke'
          //   },
          //   data: scatterData
          // }
        ],
      }
      myChart.setOption(option)
    },
  },
}
</script>
<style lang="less" scoped>
.tren_title {
  display: flex;
  padding: 20px 0 0 16px;
  span {
    font-size: 16px;
    font-weight: 600;
    color: #4d5753;
    line-height: 35px;
  }
  .tren_sele {
    margin: -3px 0 0 24px;
    .seles {
      width: 100px;
      height: 20px;
      margin-right: 16px;
    }
  }
}
.map_to {
  display: flex;
  margin-top: 10px;
  .to_ye {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-left: 15px;
    background: #ff6200;
  }
  .to_re {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-left: 15px;
    background: #ffb900;
  }
  .to_ge {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-left: 15px;
    background: #00d4af;
  }
}
.map_con {
  width: 540px;
  height: 763px;
  position: absolute;
  left: 44%;
}
.lack-map {
  position: absolute;
  top: 45%;
  left: 50%;
}
</style>
