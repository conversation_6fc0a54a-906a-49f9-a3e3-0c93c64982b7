<template>
  <div class="clearfix footer" :class="{ 'footer': this.isTeacher, 'stu-footer': this.isStudent === '1' }">
    <span style="position: relative;">
      <span style="marginRight: 30px">专课专练app下载</span>
      <span class="android">
        <img class="android-img" src="@/assets/user/徐汇网校安卓二维码.png" alt="">
        <img src="@/assets/user/安卓.png" alt="">
      </span>
      <span class="ios">
        <img class="ios-img" src="@/assets/user/徐汇网校苹果二维码.png" alt="">
        <img src="@/assets/user/iOS.png" alt="">
      </span>
    </span>
    <span>
      <span style="marginRight: 32px"><img src="@/assets/user/有我科技***********" alt=""></span>
      <span style="marginRight: 16px;fontSize: 7px">课程服务咨询</span>
      <span style="marginRight: 72px;fontSize: 16px">021-54486816&nbsp;或&nbsp;16621560846</span>
    </span>
    <span style="position: relative; ">
      <!-- <span style="marginRight: 68px;"><img src="@/assets/user/icon-phone.png" alt=""></span> -->
      <span class="weixin" style="marginRight: 78px;">
        <img class="weixin-img" src="@/assets/user/微信二维码.png" alt="">
        <img src="@/assets/user/<EMAIL>" alt="">
      </span>
      <span class="qq">
        <img class="qq-img" src="@/assets/user/qq.png" alt="">
        <img src="@/assets/user/<EMAIL>" alt="">
      </span>
    </span>
    <div class="footer_tit">
      <img src="../../assets/logo/beian.png" alt="">
      <span class="tit_sp1">&nbsp;&nbsp;沪公网安备 31010402006348号</span>
      <span class="tit_sp2"><a href="https://beian.miit.gov.cn/">沪ICP备17048448号-1</a></span>
    </div>
    <div class="foten_youwo">
      <span>Copyright©上海有我科技有限公司，All Rights Reserved</span>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.isStudent = localStorage.getItem('isStudent')
    this.isTeacher = localStorage.getItem('isTeacher')
  },
  data() {
    return {
      isTeacher: false,
      isStudent: ''
    }
  },
  methods: {}
}
</script>

<style lang="less" scoped>
@media screen and (min-width: 320px) and (max-width: 750px) {
}
.stu-footer {
  // margin-top: 141px;
  // padding-top: 50px;
  // text-align: center;
  padding: 25px;
  margin-top: 20px;

  background-color: #202a3d;
  span {
    display: inline-block;
    color: #fff;
  }
  span:nth-of-type(1) {
    margin-right: 154px;
  }
  p {
    color: #fff;
  }
  p:nth-of-type(1) {
    width: 80%;
    margin: 0 auto;
    margin-top: 33px;
    margin-bottom: 28px;
    border-bottom: 1px solid #2d3950;
  }
  p:nth-of-type(2) {
    font-size: 12px;
  }
  .android {
    margin-right: 75px;
    cursor: pointer;
  }
  .android-img {
    position: absolute;
    left: -21px;
    top: -153px;
    display: none;
  }
  .android:hover .android-img {
    display: block;
    width:314px;
    height:151px;
  }
  .ios {
    cursor: pointer;
  }
  .ios:hover .ios-img {
    display: block;
    width:314px;
    height:151px;
  }
  .ios-img {
    position: absolute;
    left: 88px;
    top: -153px;
    display: none;
  }
  .weixin {
    cursor: pointer;
  }
  .weixin:hover .weixin-img {
    display: block;
  }
  .weixin-img {
    position: absolute;
    left: -48px;
    top: -136px;
    display: none;
  }
  .qq {
    cursor: pointer;
  }
  .qq:hover .qq-img {
    display: block;
  }
  .qq-img {
    position: absolute;
    left: 76px;
    top: -136px;
    display: none;
  }
}
.footer {
  // height: 315px;
  // margin-top: 139px;
  // padding-top: 59px;
  // padding-bottom: 38px;
  // text-align: center;
  // background-color: #202a3d;
  span {
    display: inline-block;
    color: #fff;
  }
  span:nth-of-type(1) {
    margin-right: 154px;
  }
  p {
    color: #fff;
  }
  p:nth-of-type(1) {
    width: 80%;
    margin: 0 auto;
    margin-top: 33px;
    margin-bottom: 28px;
    border-bottom: 1px solid #2d3950;
  }
  p:nth-of-type(2) {
    font-size: 12px;
  }
  .android {
    margin-right: 75px;
    cursor: pointer;
  }
  .android-img {
    position: absolute;
    left: -21px;
    top: -153px;
    display: none;
  }
  .android:hover .android-img {
    display: block;
    width:314px;
    height:151px;
  }
  .ios {
    cursor: pointer;
  }
  .ios:hover .ios-img {
    display: block;
  }
  .ios-img {
    position: absolute;
    left: 88px;
    top: -153px;
    display: none;
  }
  .weixin {
    cursor: pointer;
  }
  .weixin:hover .weixin-img {
    display: block;
  }
  .weixin-img {
    position: absolute;
    left: -48px;
    top: -136px;
    display: none;
  }
  .qq {
    cursor: pointer;
  }
  .qq:hover .qq-img {
    display: block;
  }
  .qq-img {
    position: absolute;
    left: 76px;
    top: -136px;
    display: none;
  }
}
.footer_tit {
  margin-top: 30px;
  font-size: 14px;
  /deep/ .tit_sp1 {
    margin-right: 0;
  }
  .tit_sp2 {
    cursor: pointer;
    font-size: 14px;
    transform: translateX(-100px);
    a {
      color: #fff;
    }
  }
}
.foten_youwo {
  margin: 5px 0 0 60px;
}
</style>
