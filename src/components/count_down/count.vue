<template>
  <div>
    <el-dialog title="刷新时间设置" :visible.sync="dialogVisible" width="30%" center :close-on-click-modal="false">
      <span>
        <el-form ref="form" label-width="100px">
          <!-- <el-form-item label="自动刷新"> -->
          <!-- <el-switch
              v-model="switchState"
              @change="getSwitch"
              active-value="1"
              active-color="#13ce66"
              inactive-color="#ff4949"
              inactive-value="0"
            ></el-switch> -->
          <!-- </el-form-item> -->
          <!-- <el-form-item label="刷新倒计时">
            <el-input-number
              v-model="hours"
              @change="hoursChange"
              :min="0"
              :max="12"
              label="描述文字"
            ></el-input-number>
            时
          </el-form-item>
          <el-form-item label="">
            <el-input-number
              v-model="minutes"
              @change="minutesChange"
              :min="0"
              :max="59"
              label="描述文字"
            ></el-input-number>
            分
          </el-form-item> -->
          <el-form-item label="刷新倒计时">
            <!-- <el-form-item label=""> -->
            <el-input-number v-model="seconds" @change="secondsChange" :min="0" :max="59" label="描述文字"></el-input-number>
            秒
          </el-form-item>
        </el-form>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="Submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      dialogVisible: false,
      // switchState: '1',
      hours: '',
      minutes: '',
      seconds: ''
    }
  },
  created() {},
  methods: {
    openDialog() {
      this.dialogVisible = true
    },

    // getSwitch(value) {
    //   if (value === 1) {
    //     this.switchState = 1
    //   }
    //   // if (value === 0) {
    //   //   this.switchState = 0
    //   // }
    // },
    // colseSwitch() {
    //   this.switchState = 0
    // },
    // hoursChange(value) {
    //   this.hours = value
    // },
    // minutesChange(value) {
    //   this.minutes = value
    // },
    secondsChange(value) {
      this.seconds = value
    },
    clear() {
      // this.hours = 0
      // this.minutes = 0
      this.seconds = 1
    },
    // this.switchState
    //  this.$emit('getBusiness', this.hours, this.minutes, this.seconds)
    Submit() {
      this.$emit('getBusiness', this.seconds)
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .el-dialog__header {
  background-color: #68bb97;

  border-bottom: 1px solid #ebebeb;
}
/deep/.el-dialog__title {
  line-height: 0.125rem;
  font-size: 0.09375rem;
  color: #fff;
}
</style>
