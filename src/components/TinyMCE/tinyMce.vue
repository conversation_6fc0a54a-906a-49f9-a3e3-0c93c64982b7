<template>
  <div class="tinymce-editor">
    <editor v-model="myValue" :init="init" :disabled="disabled" @onClick="onClick">
    </editor>
  </div>
</template>
<script>
import tinymce from 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'

import 'tinymce/themes/silver'
// import 'tinymce/icons/default/icons'
// tinymce/themes/modern/theme
// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/image' // 插入上传图片插件
import 'tinymce/plugins/media' // 插入视频插件
import 'tinymce/plugins/table' // 插入表格插件
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/wordcount' // 字数统计插件
import 'tinymce/icons/default'

import mathjax from '@dimakorotkov/tinymce-mathjax'

import { colorList } from '../SettingDrawer/settingConfig'

export default {
  components: {
    Editor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    height: {
      default: 300
    },
    showMathJax: {
      defautl: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      default: 'image mathjax imagetools'
    },
    // plugins: 'code kityformula-editor',
    toolbar: {
      default: ' undo redo |mathjax |  image  | removeformat  '
    }
  },
  data() {
    return {
      init: {
        language_url: '/tinymce/zh_CN.js', //public目录下
        language: 'zh_CN',
        skin_url: '/tinymce/skins/ui/oxide', //public目录下
        height: this.height,
        imagetools_toolbar: 'rotateleft rotateright | flipv fliph | editimage imageoptions',
        branding: false, //是否禁用
        menubar: false, //顶部菜单栏显示
        plugins: this.plugins, // 父组件传入 或者 填写个默认的插件 要选用什么插件都可以 去官网可以查到
        toolbar: this.toolbar, // 工具栏 我用到的也就是lists image media table wordcount 这些 根据需求而定
        // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
        // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
        mathjax: {
          lib: '/tinymce/plugins/mathjax/es5/tex-mml-chtml.js'
        },
        // 官网抄的图片上传 项目如果用了vue-resource可以用$http 因为比较懒就没改
        images_upload_handler: (blobInfo, success, failure) => {
          var xhr, formData
          xhr = new XMLHttpRequest()
          xhr.withCredentials = false
          xhr.open('POST', `${this.$rootUrl}/Base_Manage/Upload/UploadFileByForm`)

          xhr.onload = function() {
            var json
            if (xhr.status != 200) {
              failure('HTTP Error: ' + xhr.status)
              return
            }
            json = JSON.parse(xhr.responseText)

            // if (!json || typeof json.img_url != 'string') {
            //   failure('Invalid JSON: ' + xhr.responseText);
            //   return;
            // }
            success(json.thumbUrl)
          }

          formData = new FormData()
          formData.append('file', blobInfo.blob(), blobInfo.filename())

          xhr.send(formData)
        }
      },
      myValue: this.value
    }
  },
  mounted() {
    tinymce.init({})
  },
  methods: {
    onClick(e) {
      this.$emit('onClick', e, tinymce)
    },
    compress(base64String, w, quality) {
      var getMimeType = function(urlData) {
        var arr = urlData.split(',')
        var mime = arr[0].match(/:(.*?);/)[1]
        // return mime.replace("image/", "");
        return mime
      }
      var newImage = new Image()
      var imgWidth, imgHeight

      var promise = new Promise(resolve => (newImage.onload = resolve))
      newImage.src = base64String
      return promise.then(() => {
        imgWidth = newImage.width
        imgHeight = newImage.height
        var canvas = document.createElement('canvas')
        var ctx = canvas.getContext('2d')
        if (Math.max(imgWidth, imgHeight) > w) {
          if (imgWidth > imgHeight) {
            canvas.width = w
            canvas.height = (w * imgHeight) / imgWidth
          } else {
            canvas.height = w
            canvas.width = (w * imgWidth) / imgHeight
          }
        } else {
          canvas.width = imgWidth
          canvas.height = imgHeight
        }
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(newImage, 0, 0, canvas.width, canvas.height)
        var base64 = canvas.toDataURL(getMimeType(base64String), quality)
        return base64
      })
    }
  },
  watch: {
    value(newValue) {
      this.myValue = newValue
    },
    myValue(newValue) {
      this.$emit('input', newValue)
    }
  }
}
</script>