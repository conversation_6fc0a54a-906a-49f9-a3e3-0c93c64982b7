<template>
  <div class="switch">
    <div class="switch_list" :style="{'width':widthNum}" v-if="multidisciplinaryList.length > 0">
      <el-tabs v-model="activeName" @tab-click="handleClick(activeName)">
        <el-tab-pane v-for="(item,index) in multidisciplinaryList" :key="index" :label="item.Name" :name="item.Id"></el-tab-pane>
      </el-tabs>
    </div>
    <div v-if="sortShow" class="switch_sort" @click="sortBtn">
      <img src="@/assets/student/sortImg.png" />
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="handleClose">
      <div>
        <el-table
          :data="tableData"
          style="width: 100%;font-size: 16px"
          :header-cell-style="{color: '#606060', fontSize: '18px',textAlign:'center'}">
          <el-table-column
            width="160"
            align="center"
            label="排序">
            <template slot-scope="scope">
              {{ scope.$index+1 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="Name"
            width="260"
            align="center"
            label="学科">
          </el-table-column>
          <el-table-column
            label="操作">
            <template slot-scope="scope">
              <div class="sortImgBtn">
                <div class="sortImgBtn_i" @click="switchUp(scope.$index)">
                  <i class="el-icon-arrow-up"></i>
                </div>
                <div class="sortImgBtn_i" @click="switchDown(scope.$index)">
                  <i class="el-icon-arrow-down"></i>
                </div>
              </div>
              <!--              <el-button class='sortStyle' icon="el-icon-arrow-up" @click="switchUp(scope.row)"></el-button>-->
              <!--              <el-button class='sortStyle' icon="el-icon-arrow-down" @click="switchDown(scope.row)"></el-button>-->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="bottom_btn">
        <div @click="Reset">重 置</div>
        <div @click="preserve">保 存</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SwitchMultidisciplinary',
  props: {
    sortShow: Boolean,
    widthNum: String
  },
  data () {
    return {
      activeName: null,
      dialogVisible: false,
      multidisciplinaryList: [],
      tableData: []
    }
  },
  created () {
    this.GetPeriodList(localStorage.getItem('UserId'))
  },
  watch: {
    $route () {
      this.activeName = localStorage.getItem('SubjectId')
    }
  },
  methods: {
    sortBtn () {
      const arr = []
      this.multidisciplinaryList.forEach(res => {
        if (res.Id !== '-1') {
          arr.push(res)
        }
      })
      this.tableData = arr
      this.dialogVisible = true
    },
    handleClose () {
      this.dialogVisible = false
    },
    handleClick (tab) {
      localStorage.setItem('SubjectId', tab)
      this.$emit('handleClick', tab)
    },
    // 切换数组位置
    swapArray (arr, index1, index2) {
      arr[index1] = arr.splice(index2, 1, arr[index1])[0]
      return arr
    },
    // 提升位置
    switchUp (index) {
      if (index !== 0) {
        this.swapArray(this.tableData, index, index - 1)
      } else {
        this.$message.warning('已经处于置顶无法上移')
      }
    },
    // 降低位置
    switchDown (index) {
      if (index + 1 !== this.tableData.length) {
        this.swapArray(this.tableData, index, index + 1)
      } else {
        this.$message.warning('已经处于底部无法下移')
      }
    },
    // 重置
    Reset () {
      const arr = []
      this.multidisciplinaryList.forEach(res => {
        if (res.Id !== '-1') {
          arr.push(res)
        }
      })
      this.tableData = arr
    },
    // 保存
    preserve () {
      this.$emit('preserveReturn', this.tableData)
      this.handleClose()
    },
    GetPeriodList (id) {
      const subject = localStorage.getItem('SubjectId') !== null ? localStorage.getItem('SubjectId') : '-1'
      this.$uwonhttp
        .post('/Period_Subject/Period_Subject/GetSubjectListByStudent', {
          userid: id,
          subject
        }).then((res) => {
          const newRes = res.data.Data
          // newRes.unshift({ Name: '全部', Id: '-1' })
          this.multidisciplinaryList = newRes
          if (!localStorage.getItem('SubjectId')) {
            localStorage.setItem('SubjectId', newRes[0].Id)
            this.activeName = newRes[0].Id
          } else {
            this.activeName = localStorage.getItem('SubjectId')
          }
        })
    }
  }
}
</script>

<style lang='less' scoped>
.switch{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  //.switch_list{
  //  width: 90%;
  //}
  .switch_sort{
    img{
      width: 44px;
      height: 43px;
    }
  }
}
.sortImgBtn{
  display: flex;
  justify-content: center;
  .sortImgBtn_i{
    padding:0px 4px;
    border: 1px solid #6d737b;
    border-radius: 4px;
    margin: 0 10px;
  }
}
.bottom_btn{
  display: flex;
  justify-content: center;
  div{
    padding: 10px 48px;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 500;
    margin: 54px 8px 0;
  }
  div:nth-child(1){
    color: #366CFF;
    background-color: rgba(54, 108, 255, 0.14);
  }
  div:nth-child(2){
    color: #FFFFFF;
    background-color: #366CFF;
  }
}
/deep/.el-tabs__header {
  padding: 0;
  position: relative;
  margin: auto;
}
/deep/.el-tabs__nav-next{
  position: absolute;
  cursor: pointer;
  line-height: 50px;
  font-size: 0.0625rem;
  color: #909399;
}
/deep/.el-tabs__nav-prev {
  position: absolute;
  cursor: pointer;
  line-height: 50px;
  font-size: 0.0625rem;
  color: #909399;
}
/deep/.el-dialog__title {
  font-size: 20px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #212121;
  line-height: 40px;
}
/deep/.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding-left: 36px;
}
/deep/.el-tabs--top .el-tabs__item.is-top:last-child {
  padding-right: 36px;
}
/deep/.el-tabs__item {
  //width: 160px;
  padding: 0 36px;
  height: 50px;
  border-radius: 12px;
  box-sizing: border-box;
  text-align: center;
  line-height: 50px;
  display: inline-block;
  list-style: none;
  font-size: 20px;
  font-weight: 500;
  color: #797979;
  position: relative;
}
/deep/.el-tabs__item.is-active {
  background-color: #80B99A;
  color: #FFFFFF;
}
/*去掉切换时el-tab-pane底部的蓝色下划线*/
/deep/.el-tabs__active-bar {
  background-color: transparent !important;
}
/*去掉tabs底部的下划线*/
/deep/.el-tabs__nav-wrap::after {
  position: static !important;
}
</style>
