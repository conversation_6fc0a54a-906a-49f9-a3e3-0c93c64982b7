<template>
  <div class="con_class">
    <div class="class_leng">
      <div class="leng_ti">
        <div class="leng_co"></div>
        <span class="leng_te">一年级</span>
      </div>
      <div class="leng_ti">
        <div class="leng_co" style="background: #facc14"></div>
        <span class="leng_te">二年级</span>
      </div>
      <div class="leng_ti">
        <div class="leng_co" style="background: #448870"></div>
        <span class="leng_te">三年级</span>
      </div>
      <div class="leng_ti">
        <div class="leng_co" style="background: #79bebd"></div>
        <span class="leng_te">四年级</span>
      </div>
      <div class="leng_ti">
        <div class="leng_co" style="background: #f57579"></div>
        <span class="leng_te">五年级</span>
      </div>
    </div>
    <div class="class_title">
      <div class="title1">
        <div class="title_ro"></div>
        <span class="title_st">正确率</span>
      </div>
      <div class="title1">
        <div class="title_ro"></div>
        <span class="title_st">完成率</span>
      </div>
      <!-- <div class="title1">
        <div class="title_ro"></div>
        <span class="title_st">参与度</span>
      </div> -->
    </div>
    <div class="bar_title">
      <span style="color: #5cc0ff">{{ acclist }}%</span>
      <span style="color: #ffbf1f">{{ acclist2 }}%</span>
      <span style="color: #448870">{{ acclist3 }}%</span>
      <span style="color: #79bebd">{{ acclist4 }}%</span>
      <span style="color: #f57579">{{ acclist5 }}%</span>
    </div>
    <div class="bar_title1">
      <span style="color: #5cc0ff">{{ finlist }}%</span>
      <span style="color: #ffbf1f">{{ finlist2 }}%</span>
      <span style="color: #448870">{{ finlist3 }}%</span>
      <span style="color: #79bebd">{{ finlist4 }}%</span>
      <span style="color: #f57579">{{ finlist5 }}%</span>
    </div>
    <!-- <div class="bar_title2">
      <span style="color: #5cc0ff">{{ Parlist }}%</span>
      <span style="color: #ffbf1f">{{ Parlist1 }}%</span>
      <span style="color: #448870">{{ Parlist2 }}%</span>
      <span style="color: #79bebd">{{ Parlist3 }}%</span>
      <span style="color: #f57579">{{ Parlist4 }}%</span>
    </div> -->
    <div class="con_ech_var">
      <div class="con_bar" style="width: 100%; height: 454px" id="classech" ref="class_bar"></div>
      <div class="con_bar" ref="class_bar2" style="width: 100%; height: 454px"></div>
      <!-- <div class="con_bar" ref="class_bar3" style="width: 100%; height: 454px"></div> -->
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data () {
    return {
      userId: '',
      classData: [],
      // 正确率
      acclist: '',
      acclist2: '',
      acclist3: '',
      acclist4: '',
      acclist5: '',
      // 完成率
      finlist: '',
      finlist2: '',
      finlist3: '',
      finlist4: '',
      finlist5: '',
      // 参与度
      Parlist: '',
      Parlist1: '',
      Parlist2: '',
      Parlist3: '',
      Parlist4: ''
    }
  },
  created () {
    this.userId = localStorage.getItem('UserId')
  },
  mounted () {
    this.ClassList()
    this.ClassListTow()
    // this.ClassListTress()
    // this.classDatas()
  },
  methods: {
    // async ClassDatas() {
    //   const { data: res } = await this.$http.post(
    //     '/HomePageDataView/TeacherManage/GetTeacherManageTop',
    //     {
    //       userId: 1,
    //     }
    //   )
    //   this.classData = res.Data
    // },
    async ClassList () {
      const myChart = echarts.init(this.$refs.class_bar)
      const res = await this.$http.post(
        '/HomePageDataView/TeacherManage/GetTeacherManageTop',
        {
          userId: this.userId
        }
      )
      this.acclist = res.Data[0].Accuracy.toString()
      this.acclist2 = res.Data[1].Accuracy.toString()
      this.acclist3 = res.Data[2].Accuracy.toString()
      this.acclist4 = res.Data[3].Accuracy.toString()
      this.acclist5 = res.Data[4].Accuracy.toString()

      myChart.setOption({
        // legend: {
        //   x:'top',
        //   itemWidth:14,
        //   itemHeight: 14,
        //   itemGap: 17,
        //   borderRadius: 100,
        // },
        // title: {
        //   text: '| 正确率',
        // },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a} : <br />{c}%'
        },
        polar: {
          center: ['33%', '25%'],
          radius: '280%' // 图形大小
        },
        angleAxis: {
          show: false,
          startAngle: 90,
          min: 0,
          max: 100
        },
        radiusAxis: {
          type: 'category',
          show: false,
          data: ['一年级', '二年级', '三年级', '四年级', '五年级']
        },
        series: [
          {
            type: 'bar',
            name: '五年级',
            coordinateSystem: 'polar',
            barWidth: 12, // 宽度
            barCategoryGap: '50%',
            data: [this.acclist5],
            roundCap: true,
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            },
            itemStyle: {
              color: '#f57579'
            }
          },
          {
            type: 'bar',
            name: '四年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            data: [this.acclist4],
            roundCap: true,
            itemStyle: {
              color: '#79bebd'
            },
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            }
          },
          {
            type: 'bar',
            name: '三年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            data: [this.acclist3],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#448870'
            },
            backgroundStyle: {
              color: '#E8EAEB'
            }
          },
          {
            type: 'bar',
            name: '二年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            data: [this.acclist2],
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            },
            roundCap: true,
            itemStyle: {
              color: '#facc14'
            }
          },
          {
            type: 'bar',
            name: '一年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            data: [this.acclist],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#5cc0ff'
            },
            backgroundStyle: {
              color: '#E8EAEB'
            }
          }
        ]
      })
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    async ClassListTow () {
      const myChart = echarts.init(this.$refs.class_bar2)
      const res = await this.$http.post(
        '/HomePageDataView/TeacherManage/GetTeacherManageTop',
        {
          userId: this.userId
        }
      )
      this.finlist = res.Data[0].Finish.toString()
      this.finlist2 = res.Data[1].Finish.toString()
      this.finlist3 = res.Data[2].Finish.toString()
      this.finlist4 = res.Data[3].Finish.toString()
      this.finlist5 = res.Data[4].Finish.toString()
      myChart.setOption({
        // legend: {
        //   x:'top',
        // },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a} : <br />{c}%'
        },
        polar: {
          center: ['28%', '25%'],
          radius: '280%' // 图形大小
        },
        angleAxis: {
          show: false,
          startAngle: 90,
          min: 0,
          max: 100
        },
        radiusAxis: {
          type: 'category',
          show: false,
          data: ['一年级', '二年级', '三年级', '四年级', '五年级']
        },
        series: [
          {
            type: 'bar',
            name: '五年级',
            coordinateSystem: 'polar',
            barWidth: 12, // 宽度
            barCategoryGap: '50%',
            // data: ["76.25","47.09","22.09"],
            data: [this.finlist5],
            roundCap: true,
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            },
            itemStyle: {
              color: '#f57579'
            }
          },
          {
            type: 'bar',
            name: '四年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["14.09","55.09","27.09"]
            data: [this.finlist4],
            roundCap: true,
            itemStyle: {
              color: '#79bebd'
            },
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            }
          },
          {
            type: 'bar',
            name: '三年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.finlist3],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#448870'
            },
            backgroundStyle: {
              color: '#E8EAEB'
            }
          },
          {
            type: 'bar',
            name: '二年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.finlist2],
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            },
            roundCap: true,
            itemStyle: {
              color: '#facc14'
            }
          },
          {
            type: 'bar',
            name: '一年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.finlist],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#5cc0ff'
            },
            backgroundStyle: {
              color: '#E8EAEB'
            }
          }
        ]
      })
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
    async ClassListTress () {
      const myChart = echarts.init(this.$refs.class_bar3)
      const res = await this.$http.post(
        '/HomePageDataView/TeacherManage/GetTeacherManageTop',
        {
          userId: 1
        }
      )
      this.Parlist = res.Data[0].Partake.toString()
      this.Parlist1 = res.Data[1].Partake.toString()
      this.Parlist2 = res.Data[2].Partake.toString()
      this.Parlist3 = res.Data[3].Partake.toString()
      this.Parlist4 = res.Data[4].Partake.toString()
      myChart.setOption({
        // legend: {
        //   x:'top',
        // },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{a} : <br />{c}%'
        },
        polar: {
          center: ['25%', '25%'],
          radius: '280%' // 图形大小
        },
        angleAxis: {
          show: false,
          startAngle: 90,
          min: 0,
          max: 100
        },
        radiusAxis: {
          type: 'category',
          show: false,
          data: ['一年级', '二年级', '三年级', '四年级', '五年级']
        },
        series: [
          {
            type: 'bar',
            name: '五年级',
            coordinateSystem: 'polar',
            barWidth: 12, // 宽度
            barCategoryGap: '50%',
            // data: ["76.25","47.09","22.09"],
            data: [this.Parlist4],
            roundCap: true,
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            },
            itemStyle: {
              color: '#f57579'
            }
          },
          {
            type: 'bar',
            name: '四年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["14.09","55.09","27.09"]
            data: [this.Parlist3],
            roundCap: true,
            itemStyle: {
              color: '#79bebd'
            },
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            }
          },
          {
            type: 'bar',
            name: '三年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.Parlist2],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#448870'
            },
            backgroundStyle: {
              color: '#E8EAEB'
            }
          },
          {
            type: 'bar',
            name: '二年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.Parlist1],
            showBackground: true,
            backgroundStyle: {
              color: '#E8EAEB'
            },
            roundCap: true,
            itemStyle: {
              color: '#facc14'
            }
          },
          {
            type: 'bar',
            name: '一年级',
            coordinateSystem: 'polar',
            barWidth: 12,
            barCategoryGap: '40%',
            // data: ["9.66","23.09","53.09"]
            data: [this.Parlist],
            showBackground: true,
            roundCap: true,
            itemStyle: {
              color: '#5cc0ff'
            },
            backgroundStyle: {
              color: '#E8EAEB'
            }
          }
        ]
      })
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.con_class {
  width: 100%;
  height: 100%;
  // display: flex;
  position: relative;
  .class_leng {
    margin: 24px 0 0 32px;
    display: flex;
    .leng_ti {
      display: flex;
      .leng_co {
        margin-top: 5px;
        width: 14px;
        height: 14px;
        background: #5cc0ff;
        border-radius: 50%;
      }
      .leng_te {
        margin-right: 30px;
        font-size: 16px;
        color: #000000;
        margin-left: 8px;
      }
    }
  }
  .class_title {
    display: flex;
    justify-content: space-evenly;
    // padding-right: 200px;
    margin: 55px 0 0 32px;
    .title1 {
    width: 50%;

      display: flex;
      align-items: center;
      margin-right: 255px;
      .title_ro {
        width: 6px;
        height: 21px;
        background: #5b7d85;
        border-radius: 12px;
        margin-right: 16px;
      }
      .title_st {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.7);
        font-weight: 600;
        font-family: PingFangSC-Regular, PingFang SC;
      }
    }
  }
  // .con_ech {
  //   width: 100%;
  //   height: 454px;
  // }
  .con_ech_var {
    height: 300px;
    display: flex;
    .con_bar {
      width: 100%;
      height: 454px;
      margin-top: 10px;
    }
  }
  .bar_title {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 145px;
    left: 335px;
    span {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }
  .bar_title1 {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 145px;
    left: 835px;
    span {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }
  .bar_title2 {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 185px;
    left: 918px;
    span {
      margin-bottom: 8px;
      font-size: 12px;
    }
  }
}
</style>
