<template>
  <div class="con_focu">
    <!-- <div class="lack">
      <img src="@/assets/lack/暂无搜索记录.png" alt="">
      <p style="text-align:center;font-size:15px">敬请期待</p>
    </div> -->
    <div class="tren_title">
      <span>教学实时诊断预警</span>
      <div class="tren_sele">
        <a-select class="seles" default-value="0" @change="rdaewChange">
          <a-select-option value="0">全部年级</a-select-option>
          <a-select-option value="1">一年级</a-select-option>
          <a-select-option value="2">二年级</a-select-option>
          <a-select-option value="3">三年级</a-select-option>
          <a-select-option value="4">四年级</a-select-option>
          <a-select-option value="5">五年级</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="tren_bor"></div>
    <div class="con_rdae">
      <div class="rdae_dia">
        <div class="dia_hea">
          <span class="hea_left"
            >待解决问题
            <a-badge :count="this.classone.length">
              <a href="#" class="head-example" />
            </a-badge>
          </span>

          <a @click="toRealTimeTest" class="gea_right cur">查看全部>></a>
        </div>
        <div class="hea_bor"></div>
        <div class="scroll">
          <happy-scroll color="#B5BEB9" hide-horizontal :min-length-v="20">
            <div class="con_card">
              <div class="card_ti" v-for="item in classone" :key="item.id" v-show="isshow">
                <div class="ber_ri"></div>
                <div class="ber_title">{{ item.Content }}</div>
                <div class="ber_ro">
                  <span class="ro_time">{{ item.CreateTime }}</span>
                  <span class="ro_btns" @click="btnss(item.Id)" :class="{ td: colors == item.Id }">查看</span>
                </div>
              </div>
              <div v-show="isshow">
                <img src="@/assets/lack/暂无搜索记录.png" alt="" />
                <p style="text-align: center; font-size: 15px">暂无</p>
              </div>
              <!-- <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div>
              <div class="card_ti"></div> -->
            </div>
          </happy-scroll>
        </div>
      </div>
      <div class="right_map">
        <a-button class="map_title" @click="MapShowModel">
          <span>预警规则</span>
        </a-button>
        <span @click="toEarlyWarning" class="fg set-role cur">设置预警规则</span>
        <a-modal class="rda_modal" width="600" v-model="visible" centered title="预警规则" @ok="MaphandleOk">
          <div class="red_ale">
            <span class="modal_p1">红色预警:</span>
            <span class="modal_p2">正确率:</span>
            <span class="modal_p3">一年级~三年级: 低于90% (不含)</span>
            <span class="modal_p3">四年级~五年级: 低于70% (不含)</span>
            <span class="modal_p2">完成率: </span>
            <span class="modal_p3">一年级~三年级: 低于20% (不含)</span>
            <span class="modal_p3">四年级~五年级: 低于20% (不含)</span>
          </div>
          <div class="red_ale">
            <span class="modal_p1" style="color: #ffb900">黄色预警:</span>
            <span class="modal_p2">正确率:</span>
            <span class="modal_p3">一年级~三年级: 90%~95% (不含)</span>
            <span class="modal_p3">四年级~五年级: 70%~80% (不含)</span>
            <span class="modal_p2">完成率: </span>
            <span class="modal_p3">一年级~三年级: 低于20% (不含)</span>
            <span class="modal_p3">四年级~五年级: 低于20% (不含)</span>
          </div>
          <div class="red_ale">
            <span class="modal_p1" style="color: #00d4af">绿色显示:</span>
            <span class="modal_p2">正确率:</span>
            <span class="modal_p3">一年级~三年级: 95%以上 (不含)</span>
            <span class="modal_p3">四年级~五年级: 80%以上 (不含)</span>
            <span class="modal_p2">完成率: </span>
            <span class="modal_p3">一年级~三年级: 低于20% (不含)</span>
            <span class="modal_p3">四年级~五年级: 低于20% (不含)</span>
          </div>
        </a-modal>
        <div class="map_to">
          <div class="to_ye"></div>
          <div class="to_re"></div>
          <div class="to_ge"></div>
        </div>

        <div v-show="isShowMap">
          <div class="map_con" ref="map_co" id="rdewa"></div>
        </div>
        <div v-show="!isShowMap" class="lack-map">
          <img src="@/assets/lack/暂无搜索记录.png" alt="" />
          <p style="text-align: center; font-size: 15px">敬请期待</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  created() {
    this.userId = localStorage.getItem('UserId')
    const realmName = window.location.href.split('/')[2]

    if (realmName === this.Facturl.$hrefName) {
      this.isShowMap = true // 徐汇
    } else if (realmName === 'localhost:5001') {
      this.isShowMap = true
    } else {
      this.isShowMap = false
    }
  },
  data() {
    return {
      // isbgclor: {
      //   color: '#5b7d85',
      //   background: 'pink'
      // }
      userId: '',
      gradeId: '0', // 年级id
      isShowMap: false, // 显示地图
      colors: '',
      classone: [],
      classtow: [],
      istow: false,
      isshow: true,
      // 弹框初始化隐藏
      visible: false,
    }
  },
  mounted() {
    this.mapRdae()
    this.mappusList()
    // this.mappusListTow()
    this.getSchoolMap()
  },
  // watch: {
  //   classone(val) {
  //     this.mappusList()
  //   }
  // },
  methods: {
    async rdaewChange(value) {
      this.gradeId = value
      this.mappusList()
      // if (value == 1) {
      //   // const { data: res } = await this.$http.post('/HomePageDataView/TeacherManage/GetEarlyWarningPush',{usedId: 1,gradeId:1})
      //   // this.classoneTow = res.Data
      //   // this.classone = this.classoneTow
      //   this.isshow = false
      //   this.istow = true
      // } else if (value == 0) {
      //   this.istow = false
      //   this.isshow = true
      // } else if (value == 2) {

      // }
    },
    async mapRdae() {
      var myChart = echarts.init(document.getElementById('rdewa'))
      const ret = await this.$http.get(document.location.origin + '/static/lib/xujiahui.json')
      echarts.registerMap('xuhui', ret)
      myChart.hideLoading()
      var scatterData = [
        { name: '向阳小学', value: [121.456975, 31.207594], itemStyle: { color: '#FF6200' } },
        { name: '建襄小学', value: [121.453288, 31.203303], itemStyle: { color: '#FF6200' } },
        { name: '高一小学', value: [121.443507, 31.199537], itemStyle: { color: '#FF6200' } },
        { name: '爱菊小学', value: [121.442532, 31.213038], itemStyle: { color: '#FF6200' } },
        { name: '世界小学', value: [121.439849, 31.207624], itemStyle: { color: '#FF6200w' } },
        { name: '虹桥路小学', value: [121.429095, 31.196174], itemStyle: { color: '#FF6200w' } },
        // { name: "日晖新村小学", value: [121.464707, 31.19537] },
        { name: '教科院实验小学', value: [121.453331, 31.137782], itemStyle: { color: '#FF6200' } },
        { name: '交大附小', value: [121.427643, 31.192254], itemStyle: { color: '#FF6200' } },
        { name: '东二小学', value: [121.451001, 31.183158], itemStyle: { color: '#FFB900' } },
        { name: '求知小学', value: [121.436676, 31.169672], itemStyle: { color: '#FFB900' } },
        { name: '东三小学', value: [121.432903, 31.174072], itemStyle: { color: '#FFB900' } },
        { name: '田林小学', value: [121.420684, 31.170854], itemStyle: { color: '#FFB900' } },
        // { name: "田林三小", value: [121.427524, 31.170786] },
        // { name: "吴中路小学", value: [121.422441, 31.191893] },
        // { name: "上师大一附小", value: [121.418283, 31.155369] },
        // { name: "高一小学（华展校区）", value: [121.521931, 31.305145] },
        // { name: "汇师小学（中城校区）", value: [121.445356, 31.161514] },
        // { name: "上海小学", value: [121.436159, 31.138316] },
        // { name: '园南小学',value: [121.441078,31.141612] },
        // { name: '龙华小学',value: [121.450825,31.171285] },
        // { name: '龙南小学',value: [121.453709,31.155924] },
        // { name: '长桥二小',value: [121.443935,31.134668] },
        // { name: '启新小学',value: [121.41989,31.13093] },
        // { name: '上实验附小',value: [121.429164,31.131372] },
        // { name: '徐浦小学',value: [121.460984,31.122602] },
        // { name: '一中心小学',value: [121.455830,31.212686] },
        // { name: '逸夫小学',value: [121.443125,31.133642] },
        // { name: '汇师小学',value: [121.432959,31.188136] },
        // { name: '光启小学',value: [121.438591,31.183509] },
        { name: '江南新村小学', value: [121.462576, 31.192572], itemStyle: { color: '#FFB900' } },
        { name: '田林四小', value: [121.426714, 31.176783], itemStyle: { color: '#FFB900' } },
        { name: '樱花园小学', value: [121.41349, 31.147002], itemStyle: { color: '#FFB900' } },
        { name: '向阳育才小学', value: [121.43834, 31.14891], itemStyle: { color: '#FFB900' } },
        { name: '华泾小学', value: [121.451945, 31.126244], itemStyle: { color: '#FFB900' } },
        { name: '盛大花园小学', value: [121.453633, 31.176011], itemStyle: { color: '#00D4AF' } },
        { name: '华理大附小', value: [121.423829, 31.137896], itemStyle: { color: '#00D4AF' } },
        { name: '康宁科技实验小学', value: [121.426649, 31.15585], itemStyle: { color: '#00D4AF' } },
        { name: '体职院附小', value: [121.446959, 31.131938], itemStyle: { color: '#00D4AF' } },
        { name: '漕开发小学', value: [121.401339, 31.156795], itemStyle: { color: '#00D4AF' } },
        // { name: '世界外国语小学',value: [121.418098,31.150304] },
        { name: '师三实验学校', value: [121.43819, 31.162909], itemStyle: { color: '#00D4AF' } },
        { name: '位育体校', value: [121.44394, 31.118045], itemStyle: { color: '#00D4AF' } },
        { name: '位育实验学校', value: [121.453575, 31.217757], itemStyle: { color: '#00D4AF' } },
        { name: '区实验小学', value: [121.453331, 31.137782], itemStyle: { color: '#00D4AF' } },
        { name: '徐教院附小', value: [121.438083, 31.133658], itemStyle: { color: '#00D4AF' } },
        { name: '康健外国语小学', value: [121.413692, 31.154404], itemStyle: { color: '#00D4AF' } },
      ]
      var max = 480
      var min = 9 // todo
      var maxSize4Pin = 100
      var minSize4Pin = 20

      var convertData = function (data) {
        var res = []
        for (var i = 0; i < data.length; i++) {
          var geoCoord = scatterData[data[i].name]
          if (geoCoord) {
            res.push({
              name: data[i].name,
              value: geoCoord.concat(data[i].value),
            })
          }
        }
        return res
      }
      var option = {
        geo: {
          map: 'xuhui',
          aspectScale: 0.95,
          layoutCenter: ['50%', '50%'], // 地图位置
          layoutSize: '100%',
          roam: false,
          zoom: 1.25,
          // label: {
          //   show: true // 展示标签
          // },
          itemStyle: {
            normal: {
              borderColor: '#02AAFF',
              borderWidth: 0.5,
              color: '#AFEBFE',
              shadowBlur: 3,
              shadowColor: 'rgba(128, 217, 248, 1)',
              shadowOffsetY: 10,
              shadowOffsetX: 10,
            },
            emphasis: {
              areaColor: 'rgba(2,170,255,0.5)',
            },
            // emphasis: {
            //   areaColor: 'rgba((255, 182, 64, 1))',
            //   itemStyle: {
            //     color: {
            //       type: 'linear-gradient',
            //       x: 0,
            //       y: 800,
            //       x2: 0,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: 'rgba(175,238,238, 0)', // 0% 处的颜色
            //         },
            //         {
            //           offset: 1,
            //           color: 'rgba(	47,79,79, .2)', // 50% 处的颜色
            //         },
            //       ],
            //       global: true, // 缺省为 false
            //     },
            //   },
            // },
          },
          z: 99,
        },
        series: [
          {
            data: scatterData, // 配置散点的坐标数据
            type: 'effectScatter',
            coordinateSystem: 'geo', // 指明散点使用的坐标系统  geo的坐标系统
            zlevel: 2,
            symbolSize: 10,
            rippleEffect: {
              scale: 1, // 设置涟漪动画的缩放比例
              period: 5,
              brushType: 'fill',
            },
            label: {
              normal: {
                show: true,
                position: 'top',
                textStyle: {
                  color: '#000000',
                  lineHeight: 10,
                },
                formatter(params) {
                  return params.data.name
                },
              },
            },
            // itemStyle: {
            //   normal: {
            //     show: true,
            //     // opacity: 0.5,
            //     color: '#00D4AF',
            //     // shadowBlur: 10,
            //   },
            // },
          },
          // {
          //   name: 'lable',
          //   type: 'scatter',
          //   coordinateSystem: 'geo',
          //   symbol: 'pin',
          //   symbolSize: [20, 20],
          //   hoverAnimation: true,
          //   zlevel: 2,
          //   label: {
          //     normal: {
          //       show: true,
          //       textStyle: {
          //         color: '#2A2D2C',
          //         lineHeight: 18,
          //         fontSize: 14
          //       },
          //       formatter (params) {
          //         return params.data.name
          //       }
          //     }
          //   },
          //   // itemStyle: {
          //   //   normal: {
          //   //     color: '#FFE579', //标志颜色
          //   //     opacity: 1,
          //   //     borderColor: '#FFE579',
          //   //     // shadowBlur: 30,
          //   //     borderWidth: 0.6,
          //   //   },
          //   // },
          //   showEffectOn: 'render',
          //   rippleEffect: {
          //     brushType: 'stroke'
          //   },
          //   data: scatterData
          // }
        ],
      }
      myChart.setOption(option)
    },
    async mappusList() {
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetEarlyWarningPush', {
        userId: this.userId,
        gradeId: this.gradeId,
      })
      this.classone = res.Data
      if (res.Data.length === 0) {
        this.isshow = false
      } else {
        this.isshow = true
      }
    },
    // async mappusListTow () {
    //   const res = await this.$http.post(
    //     '/HomePageDataView/TeacherManage/GetEarlyWarningPush',
    //     {
    //       usedId: this.userId,
    //       gradeId: 2
    //     }
    //   )
    //   this.classtow = res.Data
    // },
    async getSchoolMap() {
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetSchoolFinishAndAccuracy', {
        usedId: this.userId,
      })
      this.classtow = res.Data
    },
    btnss(id) {
      this.colors = id
      this.$router.push({
        path: '/RegionManage/RealTimeTesting/TestingDetail',
        query: {
          Id: id,
        },
      })
    },
    // 点击按钮 显示隐藏按钮
    MapShowModel() {
      this.visible = true
    },
    MaphandleOk(e) {
      this.visible = false
    },
    // 全部预警页面
    toRealTimeTest() {
      // this.$emit('toTesting')
      this.$router.push({ path: '/RegionManage/RealTimeTesting/RealTimeTesting' })
    },
    // 预警页面
    toEarlyWarning() {
      this.$router.push({ path: '/RegionManage/TeachingCenter/BackgroundConfigurate' })
    },
  },
}
</script>
<style lang="less" scoped>
.set-role {
  margin-right: 30px;
  padding: 3px 11px;
  color: #fff;
  background-color: #68bb97;
  border-radius: 5px;
}
/deep/.ant-badge-count {
  transform: translate(80%, -123%);
}
.lack {
  position: absolute;
  left: 45%;
  top: 45%;
}
.con_focu {
  position: relative;
  width: 100%;
  height: 100%;
  .tren_title {
    display: flex;
    padding: 20px 0 0 16px;
    span {
      font-size: 16px;
      font-weight: 600;
      color: #4d5753;
      line-height: 16px;
    }
    .tren_sele {
      margin: -3px 0 0 24px;
      .seles {
        width: 100px;
        height: 20px;
        margin-right: 16px;
      }
    }
  }
  .tren_bor {
    width: 1050px;
    height: 1px;
    margin: 16px 0 0 16px;
    background: #f1f1f1;
  }
  .con_focs {
    width: 100%;
    height: 100%;
  }
  .con_rdae {
    display: flex;
    .rdae_dia {
      width: 391px;
      height: 1014px;
      .dia_hea {
        display: flex;
        justify-content: space-between;
        .hea_left {
          font-size: 16px;
          color: #4d5753;
          line-height: 16px;
          margin: 19px 0 0 24px;
          font-weight: 600;
        }
        .gea_right {
          font-size: 12px;
          color: #b5b5b5;
          line-height: 12px;
          margin: 22px 23px 0 0;
          z-index: 22;
        }
      }
      .hea_bor {
        width: 346px;
        height: 1px;
        background: #f1f1f1;
        margin: 12px 0 0 24px;
      }
      .scroll {
        width: 370px;
        height: 900px;
        margin: 24px 10px 12px 24px;
        .con_card {
          .card_ti {
            position: relative;
            width: 348px;
            height: 206px;
            background: #f6f6f6;
            border-radius: 8px;
            margin: 0 0 12px 0;
            .ber_ri {
              position: absolute;
              top: 20px;
              width: 28px;
              height: 9px;
              background: #ff4c40;
            }
            .ber_title {
              position: absolute;
              top: 64px;
              left: 32px;
              margin-right: 58px;
              font-size: 15px;
              color: #4d5753;
              font-weight: 600;
              font-family: PingFangSC-Regular, PingFang SC;
            }
            .ber_ro {
              display: flex;
              justify-content: space-between;
              position: absolute;
              bottom: 26px;
              left: 32px;
              .ro_time {
                font-size: 15px;
                color: #b5b5b5;
                line-height: 15px;
                margin-top: 3px;
              }
              .ro_btns {
                width: 56px;
                height: 24px;
                font-size: 15px;
                color: #5b7d85;
                line-height: 23px;
                padding-left: 12px;
                border-radius: 4px;
                border: 1px solid #5b7d85;
                margin-left: 90px;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
    .lack-map {
      margin-top: 100px;
      text-align: center;
    }
    .right_map {
      width: 100%;
      margin: 15px 0 10px 50px;
      .map_title {
        margin-left: -15px;
        border: none;
        box-shadow: none;
        span {
          font-size: 20px;
          color: #4d5753;
          line-height: 20px;
          font-weight: 600;
        }
      }
      .map_to {
        display: flex;
        margin-top: 10px;
        .to_ye {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background: #ff6200;
        }
        .to_re {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          margin-left: 24px;
          background: #ffb900;
        }
        .to_ge {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          margin-left: 24px;
          background: #00d4af;
        }
      }
      .map_con {
        width: 587px;
        height: 763px;
        margin-top: 50px;
      }
    }
  }
}
.red_ale {
  display: flex;
  flex-direction: column;
  margin: 0 0 15px 0;
  .modal_p1 {
    font-size: 16px;
    color: #ff6200;
  }
  .modal_p2 {
    font-size: 14px;
    color: #000;
    font-weight: 600;
  }
  .modal_p3 {
    font-size: 12px;
    color: #4d5753;
  }
}

/deep/ .ant-select-selection--single {
  height: 24px;
}
/deep/ .ant-select-selection__rendered {
  line-height: 22px;
}
.card_ti:hover {
  background: #ffffff;
}
.td {
  background: #ff4c40;
  color: #fff;
  border: none;
}
</style>
