<template>
  <div class="con_tren">
    <div class="tren_title">
      <span>练习情况趋势图</span>
      <div class="tren_sele">
        <a-select class="seles" style="width: 100px" default-value="0" @change="trenhandchange">
          <a-select-option value="0">全部年级</a-select-option>
          <a-select-option value="1">一年级</a-select-option>
          <a-select-option value="2">二年级</a-select-option>
          <a-select-option value="3">三年级</a-select-option>
          <a-select-option value="4">四年级</a-select-option>
          <a-select-option value="5">五年级</a-select-option>
        </a-select>
        <a-select class="seles" default-value="近一周" @change="whereTimechange">
          <a-select-option value="week">近一周</a-select-option>
          <a-select-option value="month">近一月</a-select-option>
          <a-select-option value="xq">本学期</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="example" v-if="loadPractice">
      <a-spin />
    </div>
    <div class="tren_bor"></div>
    <div class="con_echae" ref="tren_ch"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      // 日期
      mapsArry: [],
      // 登录人数
      logincArry: [],
      // 做练习次数
      dopaArry: [],
      // 做练习人数
      dopeoArry: [],
      userId: '',
      grade: 0,
      whereTime: '',
      loadPractice: true // 加载
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.getExgercises(0)
  },
  mounted() {
    this.trendList()
  },
  watch: {
    // 登陆人数
    logincArry(val) {
      this.trendList()
    },
    // 做练习次数
    dopaArry(val) {
      this.trendList()
    },
    // 做练习人数
    dopeoArry(val) {
      this.trendList()
    },
    // 日期
    mapsArry(val) {
      this.trendList()
    }
  },
  methods: {
    trenhandchange(value) {
      this.grade = value
      this.getExgercises()
    },
    whereTimechange(value) {
      this.whereTime = value
      this.getExgercises()
    },
    getExgercises() {
      this.loadPractice = true
      this.$http
        .post('/HomePageDataView/TeacherManage/GetExercises', {
          grade: this.grade,
          userId: this.userId,
          whereTime: this.whereTime
        })
        .then(resJson => {
          this.logincArry = []
          this.dopeoArry = []
          this.dopaArry = []
          this.mapsArry = []
          resJson.Data.forEach((item, index) => {
            this.logincArry.push(item.LoginCount)
            this.dopeoArry.push(item.DoPaperCount)
            this.dopaArry.push(item.DoPeopleCount)
            this.mapsArry.push(item.Time)
            this.loadPractice = false
          })
        })
    },
    async trendList() {
      const myChart = echarts.init(this.$refs.tren_ch)
      myChart.setOption({
        tooltip: {
          trigger: 'axis'
          //   axisPointer: {
          //     type: 'cross',
          //     label: {
          //       backgroundColor: '#6a7985',
          //     },
          //   },
        },
        dataZoom: [
          {
            start: 0, // 默认为0
            end: 100, // 默认为100
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            handleSize: 0, // 滑动条的 左右2个滑动条的大小
            height: 10, // 组件高度
            // left: '10%', // 左边的距离
            // right: '10%', // 右边的距离
            bottom: 53, //
            border: 'none',
            borderColor: '#fff',
            fillerColor: '#B5BEB9',
            borderRadius: 7,
            backgroundColor: '#F8F8F8', // 两边未选中的滑动条区域的颜色
            showDataShadow: false, // 是否显示数据阴影 默认auto
            showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
            realtime: true, // 是否实时更新
            filterMode: 'empty'
          }
        ],
        color: ['rgba(255, 139, 127)', 'rgba(82, 180, 255)', 'rgba(97, 160, 168)'],
        legend: {
          top: '2%',
          itemGap: 20,
          icon: 'rect',
          itemHeight: 3,
          textStyle: {
            color: '#4D5753'
          },
          data: ['登录人数', '做练习次数', '做练习人数']
        },
        grid: {
          top: '20%',
          left: '4%',
          right: '3%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#EAEAEA',
                type: 'dashed'
              }
            },
            axisLabel: {
              color: '#9B9B9B'
            },
            data: this.mapsArry
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '(次数/人数)',
            nameGap: 30,
            nameTextStyle: {
              fontSize: 11,
              color: '#4D5753'
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#EAEAEA',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: '登录人数',
            type: 'line',
            stack: '总量',
            symbol: 'none',
            lineStyle: {
              normal: {
                color: 'rgba(255, 139, 127)'
              }
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#C24040' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(255,255,255,0.31)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            },
            // emphasis: {
            //   focus: 'series',
            // },
            data: this.logincArry
          },
          {
            name: '做练习次数',
            type: 'line',
            stack: '总量',
            symbol: 'none',
            lineStyle: {
              normal: {
                color: 'rgba(82, 180, 255)'
              }
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#088767' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(255,255,255,0.31)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            },
            // emphasis: {
            //   focus: 'series',
            // },
            data: this.dopeoArry
          },
          {
            name: '做练习人数',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              normal: {
                color: 'rgba(97, 160, 168)'
                // type: 'dashed'
              }
            },
            data: this.dopaArry
          }
        ]
      })
    }
  }
}
</script>
<style lang="less" scoped>
.example {
  left: 45%;
  position: absolute;
}
.con_tren {
  position: relative;
  width: 100%;
  height: 100%;
  .tren_title {
    display: flex;
    padding: 20px 0 0 16px;
    span {
      font-size: 14px;
      color: #4d5753;
      line-height: 14px;
    }
    .tren_sele {
      margin: -3px 0 0 24px;
      .seles {
        width: 86px;
        height: 20px;
        margin-right: 16px;
      }
    }
  }
  .tren_bor {
    width: 489px;
    height: 1px;
    margin: 16px 0 0 16px;
    background: #f1f1f1;
  }
  .con_echae {
    width: 100%;
    height: 100%;
  }
}
/deep/ .ant-select-selection--single {
  height: 24px;
}
/deep/ .ant-select-selection__rendered {
  line-height: 22px;
}
</style>
