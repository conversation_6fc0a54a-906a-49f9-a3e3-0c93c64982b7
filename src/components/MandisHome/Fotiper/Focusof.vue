<template>
  <div class="con_focu">
    <div class="tren_title">
      <span>集中做题时段</span>
      <div class="tren_sele">
        <a-select @change="handleChangGrade" class="seles" style="width: 100px" default-value="0">
          <a-select-option value="0">全部年级</a-select-option>
          <a-select-option value="1">一年级</a-select-option>
          <a-select-option value="2">二年级</a-select-option>
          <a-select-option value="3">三年级</a-select-option>
          <a-select-option value="4">四年级</a-select-option>
          <a-select-option value="5">五年级</a-select-option>
        </a-select>
        <a-select @change="handleTime" class="seles" default-value="week">
          <a-select-option value="week">近一周</a-select-option>
          <a-select-option value="month">近一月</a-select-option>
          <a-select-option value="xq">本学期</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="tren_bor"></div>
    <div class="con_focs" ref="focus_ec"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  created() {
    this.userId = localStorage.getItem('UserId')
  },
  data() {
    return {
      userId: '',
      gradeId: '0',
      whereTime: 'week',
      datalist: [],
    }
  },
  mounted() {
    this.focusList()
  },
  methods: {
    // 切换年级
    handleChangGrade(value) {
      this.gradeId = value
      this.focusList()
    },
    // 切换时间
    handleTime(value) {
      this.whereTime = value
      this.focusList()
    },
    async focusList() {
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetTimeIntervalBySchool', {
        userId: this.userId,
        grade: this.gradeId,
        whereTime: this.whereTime,
      })
      this.datalist = res.Data
      var greenPieData = [
        {
          value: 30,
          name: '00:00-01:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)',
            },
          },
        },
        {
          value: 30,
          name: '02:00-03:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)',
            },
          },
        },
        {
          value: 30,
          name: '04:00-05:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)',
            },
          },
        },
        {
          value: 30,
          name: '06:00-07:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)',
            },
          },
        },
        {
          value: 30,
          name: '08:00-09:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.1)',
            },
          },
        },
        {
          value: 30,
          name: '10:00-11:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.2)',
            },
          },
        },
        {
          value: 30,
          name: '12:00-13:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.2)',
            },
          },
        },
        {
          value: 30,
          name: '14:00-15:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.2)',
            },
          },
        },
        {
          value: 30,
          name: '16:00~17:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)',
            },
          },
        },
        {
          value: 30,
          name: '18:00~19:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)',
            },
          },
        },
        {
          value: 30,
          name: '20:00~21:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)',
            },
          },
        },
        {
          value: 30,
          name: '22:00~23:59',
          itemStyle: {
            emphasis: {
              color: 'rgba(23, 240, 204,0.3)',
            },
          },
        },
      ]
      const pieData = this.datalist
      // dataArrys = []
      const keyArry = Object.keys(res.Data[0])
      // keyArry.forEach(key => {
      //   this.echartData..series.push({
      //     name: key === 'Time' ? '你好' : key,
      //     data: res.Data.map(item => item[key]),
      //     type: 'line'
      //   })
      // })
      // 蓝色海螺 Series Data
      var bluePieData = [
        {
          value: this.datalist[0].StuCount,
          name: this.datalist[0].Time,
        },
        {
          value: this.datalist[1].StuCount,
          name: this.datalist[1].Time,
        },
        {
          value: this.datalist[2].StuCount,
          name: this.datalist[2].Time,
        },
        {
          value: this.datalist[3].StuCount,
          name: this.datalist[3].Time,
        },
        {
          value: this.datalist[4].StuCount,
          name: this.datalist[4].Time,
        },
        {
          value: this.datalist[5].StuCount,
          name: this.datalist[5].Time,
        },
        {
          value: this.datalist[6].StuCount,
          name: this.datalist[6].Time,
        },
        {
          value: this.datalist[7].StuCount,
          name: this.datalist[7].Time,
        },
        {
          value: this.datalist[8].StuCount,
          name: this.datalist[8].Time,
        },
        {
          value: this.datalist[9].StuCount,
          name: this.datalist[9].Time,
        },
        {
          value: this.datalist[10].StuCount,
          name: this.datalist[10].Time,
        },
        {
          value: this.datalist[11].StuCount,
          name: this.datalist[11].Time,
        },
      ]

      // 角度对应的时间
      var deg_clock = {
        0: 0,
        30: 2,
        60: 4,
        90: 6,
        120: 8,
        150: 10,
        180: 12,
        210: 14,
        240: 16,
        270: 18,
        300: 20,
        330: 22,
        360: 24,
      }

      // 区域带透明度的背景色
      var splitAreaColor = [
        'rgba(24, 237, 201,0.1)',
        // 'rgba(24, 237, 201,0.1)',
        // 'rgba(24, 237, 201,0.2)',
        // 'rgba(24, 237, 201,0.2)',
        // 'rgba(24, 237, 201,0.3)',
        // 'rgba(24, 237, 201,0.3)',
        // 'rgba(24, 237, 201,0.3)',
        // 'rgba(24, 237, 201,0.3)',
        // 'rgba(24, 237, 201,0.2)',
        // 'rgba(24, 237, 201,0.2)',
      ]

      // 根据时钟区域下标找到对应图片
      function formatterClockPic(i) {
        if (i == -1 || i === 0 || i == 23) {
          return '{imgOwi|}'
        }
        if (i == 1 || i == 2 || i == 3 || i == 4 || i == 5 || i == 19 || i == 21 || i == 22) {
          return '{imgMoon|}'
        }
        if (i == 6) {
          return '{imgOffice|}'
        }
        if (i == 7 || i == 8 || i == 9 || i == 10 || i == 11 || i == 13 || i == 14 || i == 15 || i == 17) {
          return '{imgSun|}'
        }
        if (i == 12 || i == 18) {
          return '{imgMeal|}'
        }
        if (i == 16) {
          return '{imgCoffee|}'
        }
        if (i == 20) {
          return '{imgPc|}'
        }
      }

      function tooltipFormatterClockPie(i) {
        if (i == -1 || i === 0 || i == 23) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 1 || i == 2 || i == 3 || i == 4 || i == 5 || i == 19 || i == 21 || i == 22) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 6) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 7 || i == 8 || i == 9 || i == 10 || i == 11 || i == 13 || i == 14 || i == 15 || i == 17) {
          return '<img src="https://tongji.baidu.com/research/img/icon-day.5342b895fe23eafdb202b563ee5efbab.svg">'
        }
        if (i == 12 || i == 18) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 16) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
        if (i == 20) {
          return '<img src="https://tongji.baidu.com/research/img/icon-night.3b047760d6f945c29dd9c38c05428751.svg">'
        }
      }
      const myChart = echarts.init(this.$refs.focus_ec)
      myChart.setOption({
        polar: {
          radius: [63, 150],
          center: ['50%', '50%'],
        },
        // 极坐标
        angleAxis: {
          min: 0,
          max: 360,
          startAngle: 91,
          //         interval: 28,
          clockwise: true, // 顺时针
          zlevel: 1,
          // 圆线
          axisLine: {
            show: false,
          },
          // 圆线上的刻度
          axisTick: {
            show: false,

            lineStyle: {
              color: '#fff',
            },
          },
          // 圆线上的刻度标签
          axisLabel: {
            color: '#666666',
            interval: '2',
            fontWeight: 'bold',
            formatter: function (deg) {
              return deg_clock[deg]
            },
          },
          // 圆中直区域分割线
          splitLine: {
            show: true,
            lineStyle: {
              color: '#EEEEEE',
              type: 'dashed',
            },
          },
          // 圆中被分割的区域
          splitArea: {
            show: false,
            // areaStyle: {
            //     color: splitAreaColor
            // }
          },
        },
        // 刻度
        axisTick: {
          show: false,
          lineStyle: {
            color: '#666666',
          },
        },
        // 刻度标签
        axisLabel: {
          show: false,
        },
        radiusAxis: {
          show: false,
        },
        tooltip: {
          trigger: 'item',
        },

        series: [
          {
            name: 'green',
            type: 'pie',
            radius: [63, 50],
            center: ['49.5%', '50%'],
            roseType: 'radius',
            zlevel: 2,
            tooltip: {
              show: false,
            },
            label: {
              color: '#666666',
              show: true,
              formatter: '',
              position: 'outside',
            },
            emphasis: {
              label: {
                show: true,
                formatter: function (params) {
                  return formatterClockPic(params.dataIndex - 2)
                },
                // rich: clockPic,
                color: '#fff',
              },
            },
            labelLine: {
              show: false,
              length: 40,
              length2: 0,
            },
            hoverAnimation: true, // hover时是否有放大效果
            itemStyle: {
              normal: {
                color: 'rgba(23, 240, 204,0)',
              },
              emphasis: {
                color: 'rgba(23, 240, 204)',
              },
            },
            data: greenPieData,
          },
          {
            name: '时段',
            type: 'pie',
            radius: [40, 130],
            center: ['50%', '50%'],
            roseType: 'area', // 是否展示成南丁格尔图，通过半径区分数据大小。可选择两种模式：radius/area
            tooltip: {
              show: true,
            },
            label: {
              show: false,
              position: 'center',
              color: '#666666',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold',
                formatter: ['{a|{d}%}', '{b|{b}}'].join('\n'),
                zlevel: 10,
                rich: {
                  a: {
                    color: '#424242',
                    fontSize: 18,
                    lineHeight: 30,
                    verticalAlign: 'bottom',
                  },
                  b: {
                    color: '#a7a9c7',
                    fontSize: 10,
                    lineHeight: 24,
                  },
                },
              },
            },
            labelLine: {
              show: false,
            },
            zlevel: 10,
            hoverAnimation: false, // hover时是否有放大效果
            clockwise: true, // 饼图的扇区是否是顺时针排布
            startAngle: 92, // 起始角度
            minAngle: 40, // 最小的扇区角度（0 ~ 360），用于防止某个值过小导致扇区太小影响交互
            itemStyle: {
              normal: {
                color: '#43715A',
              },
              emphasis: {
                color: '#9AC313',
              },
            },
            data: bluePieData,
          },
        ],
      })
      var arr = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22]

      // 默认高亮
      // chart.dispatchAction({
      //   type: 'highlight',
      //   // 可选，系列 index，可以是一个数组指定多个系列
      //   seriesIndex: [0, 1],
      //   // 可选，数据的 index
      //   dataIndex: 0,
      // })

      // chart.on(
      //   'mouseover',
      //   {
      //     seriesIndex: 0,
      //   },
      //   function (e) {
      //     // 取消高亮
      //     chart.dispatchAction({
      //       type: 'downplay',
      //       // 可选，系列 index，可以是一个数组指定多个系列
      //       seriesIndex: [0, 1],
      //       // 可选，数据的 index
      //       dataIndex: arr,
      //     })
      //     // 设置高亮
      //     chart.dispatchAction({
      //       type: 'highlight',
      //       // 可选，系列 index，可以是一个数组指定多个系列
      //       seriesIndex: [0, 1],
      //       // 可选，数据的 index
      //       dataIndex: e.dataIndex,
      //     })
      //     // 隐藏24小时图标
      //     chart.dispatchAction({
      //       type: 'hideTip',
      //       // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
      //       seriesIndex: [1],
      //       // 数据的 index，如果不指定也可以通过 name 属性根据名称指定数据
      //       dataIndex: arr,
      //     })
      //     // 显示24小时图标
      //     // chart.dispatchAction({
      //     //   type: 'showTip',
      //     //   // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
      //     //   seriesIndex: [1],
      //     //   // 数据的 index，如果不指定也可以通过 name 属性根据名称指定数据
      //     //   dataIndex: e.dataIndex,
      //     //   // 本次显示 tooltip 的位置。只在本次 action 中生效。
      //     //   // 缺省则使用 option 中定义的 tooltip 位置。
      //     //   position: [100, 200],
      //     // })
      //   }
      // )

      // chart.on(
      //   'mouseover',
      //   {
      //     seriesIndex: 1,
      //   },
      //   function (e) {
      //     // 取消高亮
      //     chart.dispatchAction({
      //       type: 'downplay',
      //       // 可选，系列 index，可以是一个数组指定多个系列
      //       seriesIndex: [0, 1],
      //       // 可选，数据的 index
      //       dataIndex: arr,
      //     })
      //     // 设置高亮
      //     chart.dispatchAction({
      //       type: 'highlight',
      //       // 可选，系列 index，可以是一个数组指定多个系列
      //       seriesIndex: [0, 1],
      //       // 可选，数据的 index
      //       dataIndex: e.dataIndex,
      //     })
      //     // 隐藏24小时图标
      //     chart.dispatchAction({
      //       type: 'hideTip',
      //       // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
      //       seriesIndex: [1],
      //       // 数据的 index，如果不指定也可以通过 name 属性根据名称指定数据
      //       dataIndex: arr,
      //     })
      //     // 显示24小时图标
      //     chart.dispatchAction({
      //       type: 'showTip',
      //       // 系列的 index，在 tooltip 的 trigger 为 axis 的时候可选。
      //       seriesIndex: [1],
      //       // 数据的 index，如果不指定也可以通过 name 属性根据名称指定数据
      //       dataIndex: e.dataIndex,
      //       // 本次显示 tooltip 的位置。只在本次 action 中生效。
      //       // 缺省则使用 option 中定义的 tooltip 位置。
      //       position: [100, 100],
      //     })
      //   }
      // )
    },
  },
}
</script>
<style lang="less" scoped>
.con_focu {
  width: 100%;
  height: 100%;
  .tren_title {
    display: flex;
    padding: 20px 0 0 16px;
    span {
      font-size: 14px;
      color: #4d5753;
      line-height: 14px;
    }
    .tren_sele {
      margin: -3px 0 0 24px;
      .seles {
        width: 86px;
        height: 20px;
        margin-right: 16px;
      }
    }
  }
  .tren_bor {
    width: 489px;
    height: 1px;
    margin: 16px 0 0 16px;
    background: #f1f1f1;
  }
  .con_focs {
    width: 100%;
    height: 100%;
  }
}
/deep/ .ant-select-selection--single {
  height: 24px;
}
/deep/ .ant-select-selection__rendered {
  line-height: 22px;
}
</style>
