<template>
  <div class="con_mansta">
    <div class="mansta_lo">
      <div class="tologin">
        <div class="to_title1">今日登录人数</div>
        <div class="to_title2">{{ manslist.TodayLogin }}</div>
      </div>
      <div class="tologin">
        <div class="to_title1">总做卷人数</div>
        <div class="to_title2">{{ manslist.TotalDoPeopleCount }}</div>
      </div>
      <div class="tologin">
        <div class="to_title1">视频总数</div>
        <div class="to_title2">{{ manslist.VideoCount }}</div>
      </div>
    </div>
    <div class="mansta_lo">
      <div class="tologin">
        <div class="to_title1">注册总人数</div>
        <div class="to_title2">{{ manslist.ItemCount }}</div>
      </div>
      <div class="tologin">
        <div class="to_title1">总做卷次数</div>
        <div class="to_title2">{{ manslist.TotalDoPaperCount }}</div>
      </div>
      <div class="tologin">
        <div class="to_title1">视频总播放数</div>
        <div class="to_title2">{{ manslist.ClickCount }}</div>
      </div>
    </div>
    <div class="mansta_lo">
      <div class="tologin">
        <div class="to_title1">试卷总数</div>
        <div class="to_title2">{{ manslist.PaperCount }}</div>
      </div>
      <div class="tologin">
        <div class="to_title1">学生总数</div>
        <div class="to_title2">{{ manslist.StudentCount }}</div>
      </div>
      <div class="tologin">
        <div class="to_title1">班级总数</div>
        <div class="to_title2">{{ manslist.ClassCount }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      manslist: []
    }
  },
  created () {
    this.userId = localStorage.getItem('UserId')
    this.getManlist()
  },
  methods: {
    async getManlist () {
      const res = await this.$http.post('/HomePageDataView/TeacherManage/GetAreaLoginInfo', { userId: this.userId })
      this.manslist = res.Data
    }
  }
}
</script>
<style lang="less" scoped>
.con_mansta {
  width: 100%;
  height: 100%;
  margin: 138px 10px 0 0px;
  .mansta_lo {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-around;
    .tologin {
      width: 100px;
      .to_title1 {
        font-size: 14px;
        color: #4d5753;
      }
      .to_title2 {
        font-size: 22px;
        color: #3cbb8f;
        margin-top: 3px;
        font-weight: 600;
        font-family: PingFangSC-Medium, PingFang SC;
      }
    }
  }
}
</style>
