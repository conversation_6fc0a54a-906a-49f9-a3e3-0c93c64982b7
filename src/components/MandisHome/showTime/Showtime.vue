<template>
  <div class="con_show">
    <div class="nav_tit">
      <!-- <div class="tit_img"></div> -->
      <img src="../../../assets/icons/timey.png" alt="" />
      <div class="showTime">
        <span>{{ nowDate }}</span>
        <span>{{ nowWeek }}</span>
        <span>{{ nowTime }}</span>
      </div>
      <div class="fg">
        <!-- <span @click="toDrive" class="drive cur">驾驶舱</span> -->
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      timer: null,
      nowWeek: '',
      nowDate: '',
      nowTime: '',
    }
  },

  mounted() {
    this.timer = setInterval(() => {
      this.setNowTimes()
    }, 1000)
  },

  methods: {
    setNowTimes() {
      const myDate = new Date()
      const wk = myDate.getDay()
      const yy = String(myDate.getFullYear())
      const mm = myDate.getMonth() + 1
      const dd = String(myDate.getDate() < 10 ? '0' + myDate.getDate() : myDate.getDate())
      const hou = String(myDate.getHours() < 10 ? '0' + myDate.getHours() : myDate.getHours())
      const min = String(myDate.getMinutes() < 10 ? '0' + myDate.getMinutes() : myDate.getMinutes())
      const sec = String(myDate.getSeconds() < 10 ? '0' + myDate.getSeconds() : myDate.getSeconds())
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const week = weeks[wk]
      let sd = ''
      if (hou < 5) {
        sd = '凌晨'
      } else if (hou < 11 && hou >= 5) {
        sd = '上午'
      } else if (hou < 13 && hou >= 11) {
        sd = '中午'
      } else if (hou < 19 && hou >= 13) {
        sd = '下午'
      } else if (hou < 24 && hou >= 19) {
        sd = '晚上'
      }
      this.nowDate = yy + '年' + mm + '月' + dd + '日'
      this.nowTime = sd + hou + ':' + min
      this.nowWeek = week
    },
    toDrive() {
      this.$router.push({ path: '/RegionManage/Armacok' })
    },
  },
}
</script>
<style lang="less" scoped>
.con_show {
  width: 100%;
  // height: 100%;
  .nav_tit {
    margin-top: 22px;
    // display: flex;
    .tit_img {
      margin: -5px 10px 0 -1px;
      img {
        width: 14px;
        height: 14px;
      }
    }
    .showTime {
      display: inline-block;
      color: #a8b3b6;
      font-size: 14px;
      line-height: 14px;
      vertical-align: middle;
      span {
        margin-right: 14px;
      }
    }
  }
  .drive {
    display: inline-block;
    margin-right: 15px;
    padding: 4px 15px;
    color: #fff;
    background-color: rgba(102, 188, 152);
    border-radius: 8px;
  }
}
</style>
