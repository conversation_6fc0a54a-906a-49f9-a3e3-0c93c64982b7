<template>
  <div class="VideoPlay">
    <video controls :src="url" :width="width" :height="height" :poster="posterUrl">您的浏览器不支持video标签</video>
  </div>
</template>
<script>
export default {
  name: 'VideoPlay',
  props: {
    url: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '800px',
    },
    height: {
      type: String,
      default: '600px',
    },
    posterUrl:{
      type:String,
      default:''
    }
  },
  data() {
    return {}
  },
}
</script>
<style lang="less" scoped>
.VideoPlay {
  margin: 0 auto;
  text-align: center;
}
</style>