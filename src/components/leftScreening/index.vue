<template>
  <div class="divGal">
    <el-popover
      placement="bottom"
      width="420"
      :disabled="popoverDis"
      trigger="click">
      <!-- 弹窗选择区 -->
      <div class="cardList">
        <div class="semester">
          <div class="txt">学期：</div>
          <div class="btnList">
            <el-button type="text" v-for="item in semesterList" :key="item.id" :class="{active: item.id == throwObj.semesterActive}" @click="allClick(item)">{{ item.label }}</el-button>
          </div>
        </div>
      </div>
      <!-- 展示区 -->
      <div slot="reference" class="headDis">
        <img src="@/assets/teacher/tikuChapter.png" alt="" />
        <div class="txtList" v-show="leftWidth >= 16">
          <div>{{throwObj.semesterActive == 1 ? '上' : '下'}}学期</div>
        </div>
      </div>
    </el-popover>
    <!--  章节目录  -->
    <div class="childList" v-show="leftWidth >= 16">
      <h1>章节目录</h1>
      <div>
        <el-tree
          ref="ELtree"
          :data="childList"
          :props="defaultProps"
          :default-expanded-keys="defaultExpandedKeys"
          :default-checked-keys="defaultCheckedKeys"
          node-key="ChapterId"
          show-checkbox
          @check="check">
          <el-tooltip
            :disabled="showTitle"
            effect="dark"
            :content="tooltipTitle"
            placement="top"
            slot-scope="{ node, data }"
          >
            <span class="span-ellipsis" @mouseover="onShowNameTipsMouseenter">{{ node.label }}</span>
          </el-tooltip>
        </el-tree>
      </div>
    </div>
    <div class="MenuFooter" v-show="!openInDialog">
      <!--      <i class="el-icon-s-unfold" v-if="leftWidth < 290" @click="$emit('changeWidth')"></i>-->
      <i class="el-icon-s-unfold" v-if="leftWidth < 16" @click="$emit('changeWidth')"></i>
      <i class="el-icon-s-fold" v-else @click="$emit('changeWidth')"></i>
    </div>
  </div>
</template>

<script>
// import { Session } from '@/utils/storage.js';

export default {
  name: 'index',
  props:{
    leftWidth: {
      type: Number,
      default: 16,
    },
    openInDialog:{
      type: Boolean,
      default: false,
    },
    popoverDis:{
      type: Boolean,
      default: false,
    },
  },
  data(){
    return {
      throwObj: {
        yearActive: null,
        semesterActive: null,
        gradeActive: null,
        // gradeActive: getSessionValue('ChapterGradeId', ''),
        chapterList: [],
      },
      semesterList:[
        {label: '上学期', id: 1},
        {label: '下学期', id: 2}
      ],
      // 树形结构
      childList: [],
      defaultProps: {
        children: 'Second',
        label: 'ChapterName',
      },
      defaultCheckedKeys: [],
      defaultExpandedKeys: [],
      showTitle: false,
      tooltipTitle: '',
    }
  },
  created() {
    this.getYear()
  },
  computed:{
    childParentLs(){
      return this.$store.state.chapterStorage.ChapterObj.ParentId
    },
    childListIds(){
      return this.$store.state.chapterStorage.ChapterObj.Ids
    },
    term(){
      return this.$store.state.chapterStorage.ChapterObj.Term
    },
  },
  // 使用watch监听childListIds的变化
  watch: {
    // 监听章节父级展开
    childParentLs(newValue, oldValue){
      this.defaultExpandedKeys = newValue
    },
    // 监听章节
    childListIds(newValue, oldValue) {
      this.$refs.ELtree.setCheckedKeys(newValue)
    },
    term(){
      this.getChild()
    },
  },
  methods:{
    check(checkedNodes, { checkedNodes: nodes }){
      if(nodes.length > 0){
        const data = nodes.filter(item => !item.Second)
        this.throwObj.chapterList = data
        const parentIds = [...new Set(data.map(item => item.ParentId))];
        const chapterIds = data.map(item => item.ChapterId);
        this.$store.dispatch('chapterStorage/setIds', chapterIds)
        this.$store.dispatch('chapterStorage/setParentId', parentIds)
      } else {
        this.$store.dispatch('chapterStorage/resetParentIdAndIds')
        this.clearAll()
      }
      // console.log(this.throwObj)
      this.$emit('getAllList',this.throwObj)
    },
    onShowNameTipsMouseenter(e){
      const target = e.target
      let textLength = target.clientWidth;
      let containerLength = target.scrollWidth;
      if (textLength < containerLength) {
        this.tooltipTitle = e.target.innerText;
        this.showTitle = false;
      } else {
        this.showTitle = true;
      }
    },
    // 弹窗点击事件
    allClick(val){
      this.$store.dispatch('chapterStorage/setTerm', val.id)
      this.$store.dispatch('chapterStorage/resetParentIdAndIds')
      this.throwObj.semesterActive = val.id
      this.$emit('getAllList',this.throwObj)
    },
    // 获取当前学年接口
    async getYear(){
      const { Term } = this.$store.state.chapterStorage.ChapterObj
      const { data:res } = await this.$uwonhttp.get(`SemesterTime/SemesterTime/GetNowYearSemesterAndList`)
      if(res.Success){
        // 作业设计弹窗执行逻辑
        this.throwObj.semesterActive = Term || res.Data.NowTerm;
        if (!Term) {
          this.$store.dispatch('chapterStorage/setTerm', res.Data.NowTerm);
        }
        this.getChild()
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取章节接口
    async getChild(){
      this.clearAll()
      const { Ids, ParentId } = this.$store.state.chapterStorage.ChapterObj
      const { data:res } = await this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', { userid: localStorage.getItem('UserId'), IsWeek: 1 })
      if(res.Success){
        this.childList = res.Data
        const chapterIdMap = new Map(Ids.map((id, index) => [id, index]));
        const arr = this.childList
          .filter(child => child?.Second?.length)
          .flatMap(child => child.Second)
          .filter(item => chapterIdMap.has(item.ChapterId))
        if(arr.length > 0){
          this.throwObj.chapterList = arr
          // 展开
          this.defaultExpandedKeys = ParentId
          // 选中
          this.defaultCheckedKeys = Ids
        } else {
          this.clearAll()
        }
        this.$emit('getAllList',this.throwObj)
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 章节清空逻辑
    clearAll(){
      this.throwObj.chapterList = []
      this.defaultExpandedKeys = []
      this.defaultCheckedKeys = []
    },
    // 清空数据
    empty(){
      this.childList = []
      this.clearAll()
      this.$store.dispatch('chapterStorage/resetParentIdAndIds')
    },
  }
}
</script>

<style lang="less" scoped>
.span-ellipsis {
  width: 100%;
  //overflow: hidden;
  //white-space: nowrap;
  //text-overflow: ellipsis;
  margin-right: 20px;
  font-size: 20px;
}
.divGal{
  height: 100%;
  position: relative;
  background: #FFFFFF;
}
.cardList{
  width: 100%;
  .academicYear,.semester,.grade{
    width: 100%;
    display: flex;
    margin: 10px 0;
    .txt{
      width: 14%;
      font-size: 18px;
    }
    .btnList{
      width: 86%;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .el-button{
        color: #6e6e6e;
        font-size: 18px;
        padding: 0 0 10px 0;
      }
    }
  }
}
.headDis{
  display: flex;
  align-items: center;
  background-color: #7fb99a;
  padding: 10px 0 10px 12px;
  img{
    width: 18px;
    height: 22px;
  }
  .txtList{
    display: flex;
    align-items: center;
    margin-left: 10px;
    >div{
      font-size: 20px;
      color: #FFFFFF;
    }
    >i{
      font-size: 18px;
      //margin: 0 2px;
      color: #FFFFFF;
    }
  }
}
.active {
  color: #7fb99a !important;
}
// 章节结构
.childList{
  height: 87%;
  padding: 0 14px;
  overflow: auto;
  h1{
    font-size: 18px;
    margin: 4px 0;
  }
  /deep/.el-tree{
    .el-tree-node{
      margin: 4px 0;
    }
    .el-tree-node>.el-tree-node__children {
      overflow: visible;
      background-color: transparent;
    }
    .el-tree-node__label {
      font-size: 20px;
    }
    .el-checkbox{
      //zoom: 130%;  //适配谷歌   不适配火狐
      zoom: var(--zoom);  //适配谷歌   不适配火狐
    }
  }
}
// 底部
.MenuFooter{
  width: 100%;
  color: #fff;
  padding: 13px 10px;
  display: flex;
  justify-content: right;
  position: absolute;
  bottom: 0;
  left: 0;
  i{
    color: #999;
    font-size: 30px;
    cursor: pointer;
  }
}
</style>