<template>
  <div class="user-nav">
    <div class="user-nav-img">
      <img v-if="xhLogo && this.showYpJy !== 1 && this.showTeam" src="@/assets/user/徐汇网校logo／绿色.png" alt="" />
      <img class="img-f" v-if="showhpLogo && this.showYpJy !== 1 && this.showTeam" src="@/assets/hplogo.png" alt="" />
      <img class="img-f" v-if="showhptogo && this.showYpJy !== 1 && this.showTeam" src="@/assets/logo/pt.png" alt="" />
      <img class="img-f" v-if="showhSjlogo && this.showYpJy !== 1 && this.showTeam" src="@/assets/logo/sj-sx.png" alt="" />
      <img class="img-f" v-if="showhYplogo && this.showYpJy === 1" src="@/assets/logo/yp-logo.png" alt="" />
      <!-- <img v-if="loalLogo" src="@/assets/student/excellent100.png" alt=""> -->
      <span class="name" :class="{ 'down-col': isDown === true }">
        <webName v-show="this.showYpJy === 1" :realmName="realmName"></webName><span v-if="this.showYpJy !== 1 && this.showTeam" class="spot" :class="{ 'down-col-bgc': isDown === true }"></span>
        <!-- <span v-if="this.showYpJy !== 1 && this.showTeam">打造一流教育</span> -->
      </span>
    </div>
    <!-- <ul class="ul-l">
      <li :class="{ 'down-menu': isDown === true }">首页</li>
      <li :class="{ 'down-menu': isDown === true }" @click="toLogin">练习中心</li>
      <li :class="{ 'down-menu': isDown === true }" @click="toLogin">名师微课</li>
      <li :class="{ 'down-menu': isDown === true }" @click="toLogin">线上辅导</li>
      <li :class="{ 'down-menu': isDown === true }" @click="toLogin">学情分析</li>
      <li :class="{ 'down-menu': isDown === true }" @click="toDownApp">
        <span>下载</span><img style="width: 18px; margin-left: 5px" src="@/assets/user/news.png" alt="" />
      </li>
      <li :class="{ 'down-menu': isDown === true }" class="login-register" @click="Login">登录</li>
    </ul> -->
  </div>
</template>

<script>
import webName from '@/components/WebsiteName/WebsiteName'
export default {
  components: {
    webName
  },

  created() {
    this.realmName = window.location.href.split('/')[2]
    const isdown = window.location.href
    const reg = RegExp(/AppDown/)
    if (isdown.match(reg) !== null) {
      this.isDown = true
    }
    if (this.realmName === this.Facturl.$hrefName) {
      this.xhLogo = true
    } else if (this.realmName === this.Facturl.$hrefHpName) {
      this.showhpLogo = true
    } else if (this.realmName === this.Facturl.$hrefHpName) {
      this.showhpLogo = true
    } else if (this.realmName === this.Facturl.$hrefPtName) {
      this.showhptogo = true
    } else if (this.realmName === this.Facturl.$hrefSjName) {
      this.showhSjlogo = true
    } else if (this.realmName === this.Facturl.$hreYpaName) {
      this.showhYplogo = true
      this.showYpJy = 1
    } else {
      this.loalLogo = true
      this.showYpJy = 0
      // this.showhpLogo = true
    }
  },
  methods: {
    toDownApp() {
      this.$router.push({ path: '/Home/AppDown', query: { id: '1' } })
    },
    toLogin() {
      this.$router.push({ path: '/Home/UserLogin' })
    },
    toTaskDetails() {
      this.$router.push({ path: '/Teacher/My_Task/TaskDetails' })
    },
    Login() {
      var domain = document.domain
      console.log(domain, 'domain')
      if (domain == 'zy.eduwon.cn' || domain == 'st.eduwon.cn') {
        window.location.href =
          'https://operator-api.sh-genius.cn/ucenter/auth/index?client_id=eduwon_client&sign=2dd2bbd834c9282e79b93ebb8cf72670&redirect_uri=https://adminapi.eduwon.cn/DS/Api/NewResult'
      } else {
        this.$router.push({ path: '/Home/UserLogin' })
      }
    }
  },
  data() {
    return {
      realmName: '',
      xhLogo: false,
      showhpLogo: false,
      loalLogo: false,
      isDown: false,
      showhptogo: false,
      showhSjlogo: false,
      showhYplogo: false,
      showYpJy: 0,
      showTeam: false
    }
  }
}
</script>

<style lang="less" scoped>
.name {
  color: #537566;
  font-size: 20px;
  margin-left: 5px;
}
.ul-l {
  li {
    font-size: 16px;
  }
}
.down-col {
  color: #fff;
}
.img-f {
  width: 21px;
}
.spot {
  font-size: 20px;
  margin: 0 5px;
  background-color: #537566;
}
.down-col-bgc {
  background-color: #fff;
}
// 导航栏
.user-nav {
  display: flex;
  height: 93px;
  // background: url('../../assets/user/header.png');
  background-size: 100% 100%;
  .user-nav-img {
    width: 191px;
    margin-top: 10px;
    text-align: center;
  }
  ul {
    display: flex;
    flex: 1;
    margin-top: 15px;
    li {
      // float: left;
      width: 12.2%;
      text-align: center;
      padding: 0 5px;
      color: #537566;
      cursor: pointer;

      img {
        width: 30px;
      }
    }
    .down-menu {
      color: #fff;
    }
    .login-register {
      text-align: left;
    }
  }
}
</style>
