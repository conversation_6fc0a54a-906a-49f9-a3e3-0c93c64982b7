<template>
  <div v-if="linkExamData" class="cont AnswerLine" :class="{ column: !isColumn }" :ref="`container_${SubType}`"
    :id="onlyId">
    <div class="cont_left" :class="{ column: isColumn }">
      <div class="cont_box" :class="[
        { active: sourceId == item.label },
        { cursor: !disabled },
        { flex_1: flexStyle },
        lIndex ? (isColumn ? 'mar_t_20' : 'mar_l_20') : '',
      ]" v-for="(item, lIndex) in linkExamData.motherRows" :key="item.label" :id="`${onlyId}_source_${item.label}`"
        @click="setSource(item.label)">
        <div v-if="linkExamData.orderNumShow">{{ item.label }}：</div>
        <div v-html="setImageClass(item.content)" v-katex></div>
      </div>
    </div>
    <div class="cont_right" :class="[{ column: isColumn }, isColumn ? 'mar_l_200' : 'mar_t_200']">
      <div class="cont_box" :class="[
        { active: targetId == item.label },
        { cursor: !disabled },
        { flex_1: flexStyle },
        rIndex ? (isColumn ? 'mar_t_20' : 'mar_l_20') : '',
      ]" v-for="(item, rIndex) in linkExamData.childRows" :key="item.label" :id="`${onlyId}_target_${item.label}`"
        @click="setTarget(item.label)">
        <div v-if="linkExamData.orderNumShow">{{ item.label }}：</div>
        <div v-katex v-html="setImageClass(item.content)"></div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AnswerLine',
  props: {
    linkExamData: Object,
    Answer: [String, Array], // 记录答案
    SureAnser: [String, Array], // 正确答案
    SubType: Number, // 0 学生答题 1 学生答题记录 2 正确答案 3 静态排列
    record: {
      // 记录id 处理多个答题
      type: Number,
      default: 0,
    },
    customStr: {
      // 记录正确与错误 处理多个答题
      type: String,
      default: 'true',
    },
  },
  data() {
    return {
      sourceId: null,
      targetId: null,
      receiveAnswer: [], // 作答答案
    }
  },
  computed: {
    disabled() {
      return this.SubType
    },
    flexStyle() {
      return this.linkExamData.rowTotal > 6 && this.isColumn ? true : false
    },
    isColumn() {
      return this.linkExamData.axis == 'y'
    },
    onlyId() {
      return `container_${this.SubType}_${this.customStr}_${this.record}`
    }
  },
  watch: {
    sourceId(nval, oval) {
      this.setConnection()
    },
    targetId(nval, oval) {
      this.setConnection()
    },
    receiveAnswer(nval, oval) {
      if (this.disabled) return
      this.$emit('handleLIne', JSON.stringify(nval))
    },
    isColumn(nval, oval) {
      let answerLines = document.getElementById(this.onlyId).querySelectorAll('.answer_line');
      for (let ele of Array.from(answerLines)) {
        ele.remove()
      }
      this.init();
    }
  },
  created() { },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    Object.assign(this.$data, this.$options.data())
  },
  methods: {
    init() {
      if (this.Answer.includes('undefined')) {
        this.receiveAnswer = []
      } else {
        this.receiveAnswer = Array.isArray(this.Answer) ? this.Answer : JSON.parse(this.Answer || '[]')
      }
      if (!this.linkExamData) return
      this.$nextTick(() => {
        this.receiveAnswer.forEach((item) => {
          for (let k in item) {
            let values = item[k] ? item[k].split('|') : []
            for (let v of values) {
              this.connectLine(k, v)
            }
          }
        })
      })
    },
    /**
     * 实现两个元素中心点的连线
     * <AUTHOR> 2022-12-26
     * @param  {Object} 起端Id
     * @param  {Object} 终点Id
     * @return {String} 返回连线的dom
     */
    drawLine(sourceId, targetId) {
      let startObj = document.getElementById(`${this.onlyId}_source_${sourceId}`),
        endObj = document.getElementById(`${this.onlyId}_target_${targetId}`)
      if (!(startObj && endObj)) return
      //起点元素中心坐标
      var y_start =
        !this.isColumn ? startObj.offsetTop + startObj.offsetHeight : startObj.offsetTop + startObj.offsetHeight / 2
      var x_start =
        !this.isColumn ? startObj.offsetLeft + startObj.offsetWidth / 2 : startObj.offsetLeft + startObj.offsetWidth
      //终点元素中心坐标
      var y_end = !this.isColumn ? endObj.offsetTop : endObj.offsetTop + endObj.offsetHeight / 2
      var x_end = !this.isColumn ? endObj.offsetLeft + endObj.offsetWidth / 2 : endObj.offsetLeft
      //用勾股定律计算出斜边长度及其夹角（即连线的旋转角度）
      var lx = x_end - x_start
      var ly = y_end - y_start
      //计算连线长度
      var length = Math.sqrt(lx * lx + ly * ly)
      //弧度值转换为角度值
      var c = (360 * Math.atan2(ly, lx)) / (2 * Math.PI)
      //连线中心坐标
      var midX = (x_end + x_start) / 2
      var midY = (y_end + y_start) / 2
      var deg = c <= -90 ? 360 + c : c //负角转换为正角
      let dom = document.createElement('div')
      dom.style = `top:${midY-2}px;left:${midX - length / 2}px;width:${length}px;transform:rotate(${deg}deg);`
      dom.setAttribute('class', `answer_line`)
      if (this.isSure(sourceId, targetId)) {
        dom.classList.add('sure')
      } else {
        dom.classList.add('error')
      }
      if (!this.disabled) {
        dom.classList.add('hover')
        dom.onclick = (e) => {
          this.$confirm('确定删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              dom.remove()
              let c_index = -1
              let flag = this.receiveAnswer.some((item, index) => {
                if (item[sourceId]) {
                  let values = item[sourceId].split('|')
                  if (values.includes(targetId)) {
                    let sv = values.filter((f) => f != targetId)
                    if (sv.length) {
                      item[sourceId] = sv.join('|')
                    } else {
                      c_index = index
                      return true
                    }
                  }
                }
              })
              if (flag) {
                this.receiveAnswer.splice(c_index, 1)
              } else {
                this.$emit('handleLIne', JSON.stringify(this.receiveAnswer))
              }
            })
            .catch(() => { })
        }
      }
      return dom
    },
    connectLine(sourceId, targetId) {
      let dom = this.drawLine(sourceId, targetId)
      document.getElementById(this.onlyId).appendChild(dom)
      let flag = this.receiveAnswer.some((item) => {
        if (item[sourceId]) {
          const values = item[sourceId].split('|')
          !values.includes(targetId) && (item[sourceId] = `${item[sourceId]}|${targetId}`)
          return true
        }
      })
      if (!flag) {
        this.receiveAnswer.push({ [sourceId]: `${targetId}` })
      } else {
        this.$emit('handleLIne', JSON.stringify(this.receiveAnswer))
      }
    },
    setSource(label) {
      if (this.disabled) return
      this.sourceId = label
    },
    setTarget(label) {
      if (this.disabled) return
      this.targetId = label
    },
    setConnection() {
      if (this.sourceId && this.targetId) {
        let flag = this.receiveAnswer.some((item, index) => {
          if (item[this.sourceId]) {
            let values = item[this.sourceId].split('|')
            if (values.includes(this.targetId)) {
              return true
            }
          }
        })
        if (!flag) {
          this.connectLine(this.sourceId, this.targetId)
        }
        this.sourceId = null
        this.targetId = null
      }
    },
    // 判断是否是正确的答案
    isSure(sourceId, targetId) {
      if (!this.disabled) return true
      if(!this.SureAnser) return true
      let flag = JSON.parse(this.SureAnser).some((item, index) => {
        if (item[sourceId]) {
          let values = item[sourceId].split('|')
          if (values.includes(targetId)) {
            return true
          }
        }
      })
      return flag
    },
    // 设置图片的class
    setImageClass(str) {
      const regex = new RegExp('<img')
      return str.replace(regex, `<img class="textarea_img"`)
    },
  },
}
</script>
<style lang="less" scoped>
::v-deep .textarea_img {
  max-width: 100%;
  max-height: 216px;
}

::v-deep .answer_line {
  position: absolute;
  height: 4px;

  &.sure {
    background: #61bb96;
  }

  &.error {
    background: rgb(236 43 38);
  }

  &.hover {
    &:hover {
      background: #994b0a;
      height: 6px;
      cursor: pointer;
    }
  }
}

.column {
  flex-direction: column;
}

.flex_1 {
  flex: 1;
}

.mar_t_200 {
  margin-top: 200px;
}

.mar_l_200 {
  margin-left: 200px;
}

.mar_t_20 {
  margin-top: 20px;
}

.mar_l_20 {
  margin-left: 20px;
}

.cursor {
  cursor: pointer;
}

.cont {
  padding: 20px;
  box-sizing: border-box;
  position: relative;
  /*一定加上这句，否则连线位置发生错乱*/
  display: flex;

  &_left {
    display: flex;
  }

  &_right {
    display: flex;
  }

  &_box {
    display: flex;
    align-items: center;
    max-width: 236px;
    max-height: 236px;
    font-size: 20px;
    border-radius: 4px;
    background: #e7e7e7;
    padding: 10px;
    box-sizing: border-box;
    user-select: none;
    overflow: auto;

    &.active {
      border: 1px solid #00b671;
      //border-radius: 4px;
      background-color: #ffffff;
      box-shadow: rgb(72, 73, 73) 0 0 10px;
    }
  }
}
</style>