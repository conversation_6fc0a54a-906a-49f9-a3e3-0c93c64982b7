<template>
  <div class="con_footer" v-show="footer">
    <div class="down_footer">
      <div class="appdown">
        <span class="down_title pc_font_size16 font_size18 ipad_font_size20">专课专练app下载</span>
        <span class="down_and">
          <img class="adn_img" src="../../assets/user/徐汇网校安卓二维码.png" v-show="isshowAnd" alt="">
          <img class="adimg" src="../../assets/user/安卓.png" alt="" @mouseover="channgeAdn" @mouseleave="mouleChage" />
        </span>
        <span class="down_ios">
          <img class="ios_img" src="../../assets/user/徐汇网校苹果二维码.png" v-if="isiosshow" alt="">
          <img class="osimg" src="../../assets/user/iOS.png" alt="" @mouseover="channgios" @mouseleave="mouleChageios"/>
        </span>
      </div>
      <div class="youwodown">
        <span class="youwo_img">
          <img src="../../assets/user/有我科技***********" alt="" />
        </span>
        <span class="youwo_pone1">课程服务咨询</span>
        <span class="youwo_pone2">021-54486816&nbsp;或&nbsp;16621560846</span>
        <span class="youwo_weixin">
          <img class="weixin_img" src="../../assets/user/微信二维码.png" v-if="isweixinshow" alt="">
          <img class="wimg1" src="../../assets/user/<EMAIL>" alt="" @mouseover="channgweixin" @mouseleave="mouleChageweixin" />
        </span>
        <span class="youwo_qq">
          <img class="qq_img" src="@/assets/user/qq.png" v-if="qqisshow" alt="">
          <img class="qimg1" src="../../assets/user/<EMAIL>" alt="" @mouseover="channgqq" @mouseleave="mouleChageqq" />
        </span>
      </div>
      <div class="footer_about">
        <div class="top_title">
          <span class="title_img">
            <img src="../../assets/logo/beian.png" alt="" />
          </span>
          <span class="title_bsp">&nbsp;&nbsp;沪公网安备 31010402006348号</span>
          <span class="title_a"><a href="https://beian.miit.gov.cn/">沪ICP备17048448号-1</a></span>
        </div>
        <div class="boot_title">Copyright©上海有我科技有限公司，All Rights Reserved</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      isshowAnd: false,
      isiosshow: false,
      isweixinshow: false,
      qqisshow: false,
      // 控制底部导航是否显示
      footer: true
    }
  },
  // watch: {
  //   $route(to, from) {
  //     console.log(to)
  //     // if (to.path === '/AIPagesAndComponents/index') {
  //     //   this.$router.push('/')
  //     // }
  //   }
  // },
  created() {
    console.log(this.$route)
  },
  methods: {
    channgeAdn() {
      this.isshowAnd = true
    },
    mouleChage() {
      this.isshowAnd= false
    },
    channgios() {
      this.isiosshow = true
    },
    mouleChageios() {
      this.isiosshow = false
    },
    channgweixin() {
      this.isweixinshow = true
    },
    mouleChageweixin() {
      this.isweixinshow = false
    },
    channgqq() {
      this.qqisshow = true
    },
    mouleChageqq() {
      this.qqisshow = false
    }
  }
}
</script>
<style lang="less" scoped>
.con_footer {
  // max-width: 750px;
  // min-width: 320px;
  height: 100%;
  background-color: #202a3d;
  .down_footer {
    padding: 50px 0 30px 20px;
    .appdown {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      .down_title {
        height: 36px;
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #d6d6d6;
        line-height: 36px;
        margin-right: 40px;
      }
      .down_and {
        margin-left: 40px;
        position: relative;
        .adn_img {
          position: absolute;
          top: -147px;
          right: -140px;
        }
        .adimg {
          width: 100px;
          // height: 100px;
        }
      }
      .down_ios {
        margin-left: 80px;
        position: relative;
        .ios_img {
          position: absolute;
          top: -147px;
          right: -140px;
        }
        .osimg {
          width: 100px;
        }
      }
    }
    .youwodown {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 15px 0 0;
      .youwo_img {
        img {
          width: 200px;
        }
      }
      .youwo_pone1 {
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #d6d6d6;
        line-height: 36px;
        margin-left: 60px;
      }
      .youwo_pone2 {
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #d6d6d6;
        line-height: 36px;
        margin-left: 40px;
      }
      .youwo_weixin {
        position: relative;
        margin-left: 60px;
        .weixin_img {
          position: absolute;
          top: -132px;
          right: -43px;
        }
        .wimg1 {
          width: 100px;
        }
      }
      .youwo_qq {
        position: relative;
        margin-left: 40px;
        .qq_img {
          position: absolute;
          top: -132px;
          right: -43px;
        }
        .qimg1 {
          width: 100px;
        }
      }
    }
    .footer_about {
      margin: 15px 15px 0 0;;
      .top_title {
        display: flex;
        justify-content: center;
        align-items: center;
        .title_img {
          img {
            width: 80px;
          }
        }
        .title_bsp {
          font-size: 32px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #9197a2;
          line-height: 32px;
        }
        .title_a {
          margin: -15px 0 0 35px;
          a {
            font-size: 32px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #9197a2;
            line-height: 32px;
          }
        }
      }
      .boot_title {
        margin-top: 7px;
        font-size: 30px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #9197a2;
        line-height: 30px;
        text-align: center;
      }
    }
  }
}
</style>
