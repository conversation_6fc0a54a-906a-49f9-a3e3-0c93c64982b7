<template>
  <div class="mains">
    <p @click="login"> 进入专课专练</p>
    <a-modal v-model="visible" title="登录" :centered="true" width="700px" :afterClose="close">
      <span class="loginErrText" v-show="loginErrorIsShow">
        {{loginErrText}}
      </span>
      <br>
      <br>
      <!-- <img @click="toQRLogin" class="loginImg" src="@/assets/cpmpany/二维码登录/login二维码.png" alt=""> -->
      <a-form :form="form1" @submit="toSubmit">
        <a-form-item has-feedback>
          <a-input v-decorator="[
                            'mobilephone',
                            { rules: [{ required: true, message: '请填写手机号'}] },
                        ]" placeholder="手机号"></a-input>
        </a-form-item>
        <a-form-item has-feedback>
          <a-input v-decorator="[
                            'passwordCheck',
                            { rules: [{ required: true, message: '请输入密码'}] },
                        ]" type="password" placeholder="密码"></a-input>
        </a-form-item>
        <br>
        <a-button type="primary" html-type="submit" class="login-form-button">
          登录
        </a-button>
        <div class="main">
          <a-checkbox v-decorator="[
                        'remember',
                            {
                                valuePropName: 'checked',
                                initialValue: true,
                            },
                        ]">下次自动登录</a-checkbox>
          <div class="reForget">
            <span @click="forgetPassWord"> 忘记密码</span>&nbsp;&nbsp;
          </div>
        </div>
        <br>
        <br>
        <!-- --------------<img src="@/assets/cpmpany/login/第三方账号登录.png" alt="">-------------- -->
        <br>
        <br>
        <!-- <img src="@/assets/cpmpany/login/logo_2x-灰色.png" alt=""> -->
        <br>
        <br>
        <div>
          <!-- <img src="@/assets/cpmpany/login/第三方账号登录备份.png" alt="">&nbsp;|
          <img src="@/assets/cpmpany/login/第三方账号登录备份 2.png" alt="">&nbsp;|
          <img src="@/assets/cpmpany/login/第三方账号登录备份 3.png" alt=""> -->
        </div>
      </a-form>
      <template #footer>
      </template>
    </a-modal>

    <a-modal v-model="forGetPassWord" title="找回密码" :centered="true" width="700px">
      <div class="errordiv">
        <span v-show="errorIsShow">{{errText}}</span>
      </div>
      <a-form :form="form2" @submit="toResetPassWord">
        <a-form-item>
          <a-input v-decorator="[
                            'mobilephone',
                            { rules: [{ required: true, message: '请输入手机号'}] },
                        ]" placeholder="请输入手机号" @change="mobilephoneValidate"></a-input>
        </a-form-item>
        <a-form-item has-feedback>
          <div class="code">
            <a-input style="width:50%;" v-decorator="[
                                'note',
                                { rules: [{ required: true, message: '请输入验证码'}] },
                            ]" placeholder="请输入验证码"></a-input>
            <span class="getCode" :class="{displayable:this.canClick}" @click="getCode()">{{codeContent}}</span>
          </div>
        </a-form-item>
        <a-form-item has-feedback>
          <a-input v-decorator="[
                            'password',
                            { rules: [{ required: true, message: '请输入新密码'}] },
                        ]" placeholder="请输入新密码" type="password"></a-input>
        </a-form-item>
        <a-form-item has-feedback>
          <a-input v-decorator="[
                            'againPassword',
                            { rules: [{ required: true, message: '请再次输入新密码'}] },
                        ]" placeholder="请再次输入新密码" type="password"></a-input>
        </a-form-item>
        <a-button type="primary" html-type="submit" class="login-form-button">
          确认重置
        </a-button>
      </a-form>
      <template #footer>
      </template>
    </a-modal>
  </div>
</template>

<script>
import TokenCache from '@/utils/cache/TokenCache'
export default {
  data() {
    return {
      visible: false,
      forGetPassWord: false,
      codeContent: '发送验证码',
      canClick: true,
      errorIsShow: false,
      loginErrorIsShow: false,
      loginErrText: '登录error文本',
      errText: 'error文本',
      totalTime: 60
    }
  },
  created() {},
  beforeCreate() {
    this.form1 = this.$form.createForm(this, { name: 'normal_login' })
    this.form2 = this.$form.createForm(this, { name: 'normal_login2' })
  },
  methods: {
    mobilephoneValidate(e) {
      if (e.target.value === '') {
        this.errorIsShow = false
        this.errText = ''
        this.canClick = true
      } else {
        if (!this.testMobilephone(e.target.value)) {
          this.errorIsShow = true
          this.errText = '手机号格式不正确'
          this.canClick = true
          // 图标
          // this.mobileSuc = ''
        } else {
          this.errorIsShow = false
          this.canClick = false
          // 图标
          // this.mobileSuc = 'success'
          if (this.codeContent === '发送验证码' || this.codeContent === '重新发送验证码') {
            this.canClick = false
          }
        }
      }
    },
    // 获取手机验证码
    getPhoneCount(mobilephone) {
      this.$uwonhttp
        .get('/User/UserManager/GetPhoneNumCode', {
          params: {
            phoneNum: mobilephone
          }
        })
        .then(res => {})
    },
    //点击获取验证码
    getCode() {
      if (this.canClick) return
      this.canClick = true
      const values = this.form2.getFieldsValue()
      this.$uwonhttp
        .post('/TeacherUserCenter/TeacherPersonal/CheckUserExistByPhone', { phoneNum: values.mobilephone })
        .then(res => {
          if (res.data.Data.Exist !== 1) {
            this.errorIsShow = true
            this.errText = '手机号未注册'
            this.canClick = true
          } else {
            this.getPhoneCount(values.mobilephone)
            this.codeContent = `${this.totalTime}s重新获取`
            const clock = window.setInterval(() => {
              this.totalTime--
              this.codeContent = `${this.totalTime}s重新获取`
              if (this.totalTime < 0) {
                window.clearInterval(clock)
                this.codeContent = '重新发送验证码'
                this.totalTime = 60
                this.canClick = false
              }
            }, 1000)
          }
        })
    },
    toResetPassWord(e) {
      e.preventDefault()
      const that = this
      this.form2.validateFields((err, values) => {
        if (!err) {
          that.$http
            .post('/HomePageDataView/TeacherDataView/CodeIsTrue', { code: values.note, phoneNum: values.mobilephone })
            .then(res => {
              if (!res.Success) {
                this.errorIsShow = true
                this.errText = res.Msg
              } else if (values.password !== values.againPassword) {
                this.errorIsShow = true
                this.errText = '密码不一致'
              } else {
                this.$uwonhttp
                  .post('/TeacherUserCenter/TeacherPersonal/UpdateMainPwd', {
                    phoneNum: values.mobilephone,
                    pwd: values.password,
                    Code: values.note,
                    IsStudent: 1
                  })
                  .then(res => {
                    this.errorIsShow = false
                    if (res.data.Success) {
                    } else {
                      this.errorIsShow = true
                      this.errText = res.data.Msg
                    }
                  })
              }
            })
        }
      })
    },
    // 号码校验
    testMobilephone(str) {
      const regex = /^1[3456789]\d{9}$/
      if (!regex.test(str)) {
        return false
      } else {
        return true
      }
    },
    toSubmit(e) {
      e.preventDefault()
      this.form1.validateFields((err, values) => {
        if (!err) {
          this.$http
            .post('/Base_Manage/Home/SubmitLogin', { userName: values.mobilephone, password: values.passwordCheck })
            .then(res => {
              if (res.Success) {
                // 保存token
                TokenCache.setToken(res.Data)
                if (values && values.remember) {
                  localStorage.setItem('passWord', passwordCheck)
                } else {
                  localStorage.removeItem('mobilePhone')
                  localStorage.removeItem('passWord')
                }
                localStorage.setItem('mobilePhone', values.mobilephone)
                this.$http
                  .post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: values.mobilephone })
                  .then(res => {
                    console.log('Modlogin.vue');
                    localStorage.setItem('SchoolId', res.Data.User.SchoolId)
                    if (!res.Data.Roles) {
                      this.isStudent = '1'
                      localStorage.setItem('isStudent', this.isStudent)
                      localStorage.removeItem('isTeacher')
                    } else {
                      this.isTeacher = res.Data.Roles.some(item => {
                        return item.RoleName === '教师'
                      })
                      localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
                      localStorage.setItem('isTeacher', this.isTeacher)
                      localStorage.removeItem('isStudent')
                      localStorage.setItem('role', res.Data.Roles[0].Id)
                    }
                    if (res.Data.Roles) {
                      if (
                        res.Data.Roles.some(item => {
                          return item.RoleName === '校领导'
                        })
                      ) {
                        this.isSchoolHeader = true
                        localStorage.setItem('isSchoolHeader', this.isSchoolHeader)
                        localStorage.removeItem('isTeacher')
                        localStorage.removeItem('isTeachingStaff')
                      }
                    }
                    this.id = res.Data.UserType
                    localStorage.setItem('Id', res.Data.UserType)
                    localStorage.setItem('UserId', res.Data.User.Id)
                    localStorage.setItem('IsVip', res.Data.IsVip)
                    localStorage.setItem('LoginSource', res.Data.LoginSource)
                    this.$message.success('登录成功')
                    this.$router.push({
                      path: '/Home/Introduce',
                      query: {
                        id: this.id
                      }
                    })
                  })
              } else {
                this.loginErrorIsShow = true
                this.loginErrText = '出现未知错误'
              }
            })
        }
      })
    },
    login() {
      this.visible = true
    },
    close() {
      this.visible = false
    },
    forgetPassWord() {
      this.visible = false
      this.forGetPassWord = true
    },
    afterClose() {
      this.visible = false
    },
    toQRLogin() {
      this.$router.push('/MobileHome/Qrlogin')
    }
  }
}
</script>

<style lang="less" scoped>
.main {
  position: relative;
  width: 70%;
  text-align: left;
  margin: 10px auto;
}
.reForget {
  position: absolute;
  top: 0px;
  right: 0px;
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #a5a5a5;
  cursor: pointer;
}
/deep/.ant-modal-title {
  text-align: center;
}
/deep/.ant-modal-header,
/deep/.ant-modal-footer {
  border: none;
}
.loginImg {
  position: absolute;
  width: 30px;
  height: 30px;
  right: 60px;
  top: 10px;
  cursor: pointer;
}
/deep/.ant-input {
  width: 60%;
  height: 50px;
  background-color: #f4f4f4;
  margin: 10px 0px;
}

/deep/.ant-modal-body {
  text-align: center;
}
.login-form-button {
  width: 60%;
  height: 50px;
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  border-radius: 2px;
}
/deep/.ant-btn-primary {
  background-color: #eaeaea;
}

.main {
  /deep/ .ant-checkbox-inner {
    color: #38c2cc;
    border-color: #38c2cc;
  }
  /deep/.ant-checkbox-checked .ant-checkbox-inner {
    background-color: #38c2cc;
    border-color: #38c2cc;
  }
}
.code {
  text-align: left;
  width: 60%;
  line-height: 50px;
  margin: 0px auto;
}
.getCode {
  display: inline-block;
  height: 50px;
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #464646;
  text-align: center;
  width: 150px;
  background-color: #38c2cc;
  border-radius: 2px;
  margin: 0px 20px;
  cursor: pointer;
}

.displayable {
  background-color: #d6d6d6;
  color: white;
  cursor: not-allowed;
}
.errordiv {
  height: 50px;
  border: none;
  text-align: center;
  color: red;
  span {
    border: 1px solid;
    border-radius: 100px;
    padding: 0px 10px;
  }
}
.loginErrText {
  padding: 10px;
  border: 1px solid red;
  margin: 0px auto;
  position: relative;
  border-radius: 100px;
  color: red;
}
/deep/ .ant-modal-content {
  width: 100%;
  margin: auto;
}
</style>