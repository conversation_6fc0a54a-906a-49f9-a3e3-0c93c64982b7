<template>
  <div :class="prefixCls">
    <div ref="editor" class="editor-wrapper"></div>
  </div>
</template>

<script>
import WEditor from 'wangeditor'

export default {
  name: 'WangEditor',
  props: {
    prefixCls: {
      type: String,
      default: 'ant-editor-wang'
    },
    // eslint-disable-next-line
    value: {
      type: String
    }
    // subTitle: {
    //   type: String,
    //   default: ''
    // }
  },
  data() {
    return {
      editor: null,
      editorContent: null,
      isChange: false // 是否为内部触发
    }
  },
  watch: {
    value(val, oldval) {
      // 仅处理外部触发,解决光标闪烁问题
      // this.editor.txt.html(val)
      // if (val === oldval) {
      //   this.editor.txt.html(val)
      // } else {
      //   this.isChange = false
      // }
      if (!this.isChange) {
        this.editor.txt.html(val)
      }
      this.isChange = false
    }
    // subTitle (val) {
    //   if (!this.isChange) {
    //     this.editor.txt.html(val)
    //   }
    //   this.isChange = false
    // }
  },
  mounted() {
    // this.editor = new WEditor(this.$refs.editor)
    // this.editor.customConfig.onchange = html => {
    //   this.$emit('catchData', html)
    // }
    this.initEditor()
    // this.init()
  },
  methods: {
    initEditor() {
      this.editor = new WEditor(this.$refs.editor)

      this.editor.customConfig.uploadImgShowBase64 = true // base 64 存储图片
      this.editor.customConfig.uploadImgServer = `${this.$rootUrl}/Base_Manage/Upload/UploadFileByForm` // 配置服务器端地址
      this.editor.customConfig.uploadFileName = 'file' // 后端接受上传文件的参数名
      this.editor.customConfig.uploadImgMaxSize = 10 * 1024 * 1024 // 将图片大小限制为 2M
      this.editor.customConfig.uploadImgMaxLength = 1 // 限制一次最多上传 1 张图片
      this.editor.customConfig.uploadImgTimeout = 3 * 60 * 1000 // 设置超时时间
      this.editor.customConfig.uploadImgHooks = {
        customInsert: function(insertImg, result, editor) {
          insertImg(result.url)
        }
      }
      this.editor.customConfig.onchange = html => {
        this.isChange = true
        // html = html.replace(/<xml>[\s\S]*?<\/xml>/ig, '')
        // html = html.replace(/<style>[\s\S]*?<\/style>/ig, '')
        // // html = html.replace(/<\/?[^>]*>/g, '');
        // html = html.replace(/[ | ]*\n/g, '\n')
        // html = html.replace(/&nbsp;/ig, '')
        this.$emit('input', html)
        this.$emit('catchData', html)
      }

      this.editor.customConfig.menus = ['image']
      this.editor.create()
      this.editor.txt.html(this.value)
      this.editor.txt.html(this.subTitle)
    }
    // init () {
    //   this.editor.config.menus =

    //   this.editor.cmd.do('insertHTML', '<input type="text">')
    // }
  }
}
</script>

<style lang="less" scoped>
.ant-editor-wang {
  .editor-wrapper {
    text-align: left;
  }
}
</style>
