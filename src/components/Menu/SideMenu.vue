<template>
  <a-layout-sider :class="['sider', isDesktop() ? null : 'shadow', theme, fixSiderbar ? 'ant-fixed-sidemenu' : null ]" :collapsible="collapsible" v-model="collapsed" :trigger="null">
    <s-menu :collapsed="collapsed" :menu="menus" :theme="theme" :mode="mode" @select="onSelect" style="padding: 16px 0px;"></s-menu>
  </a-layout-sider>

</template>

<script>
import Logo from '@/components/tools/Logo'
import SMenu from './index'
import { mixin, mixinDevice } from '@/utils/mixin'

export default {
  name: 'SideMenu',
  components: { Logo, SMenu },
  mixins: [mixin, mixinDevice],
  props: {
    mode: {
      type: String,
      required: false,
      default: 'inline'
    },
    theme: {
      type: String,
      required: false,
      default: 'dark'
    },
    collapsible: {
      type: Boolean,
      required: false,
      default: false
    },
    collapsed: {
      type: Boolean,
      required: false,
      default: true
    },
    menus: {
      type: Array,
      required: true
    }
  },
  data() {
    return {}
  },
  methods: {
    onSelect(obj) {
      this.$emit('menuSelect', obj)
    }
  }
}
</script>
<style lang="less" scoped>
.sider.ant-fixed-sidemenu {
  width: 200px !important;
  min-width: 100px !important;
}
/deep/.anticon svg {
  font-size: 24px;
  vertical-align: text-bottom;
  // vertical-align: middle;
}
/deep/.ant-menu-dark.ant-menu-inline .ant-menu-item,
.ant-menu-dark.ant-menu-inline .ant-menu-submenu-title {
  margin-bottom: 30px;
}
/deep/.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  height: 80px;
  margin-bottom: 15px;
  text-align: center;
  font-size: 15px;
}
/deep/.ant-menu-item .anticon + span {
  margin-top: 10px;
  font-size: 17px;
}
/deep/.ant-menu-submenu-title .anticon + span {
  display: block;
  //  font-size: 20px;
}
/deep/.ant-menu-submenu-title .anticon {
  font-size: 23px;
}
/deep/.ant-menu-item {
  padding-left: 0 !important;
  text-align: center;
  color: #fff;
}
</style>
