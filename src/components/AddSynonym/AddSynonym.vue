<template>
  <div>
    <div>
      <p>同义答案：</p>
      <textarea
        name=""
        id=""
        cols="30"
        rows="3"
        style="resize: none"
        v-model="synonymousAnswer"
        @blur="get"></textarea>
    </div>
    {{ sd }}
  </div>
</template>

<script>
export default {
  name: 'SynonymAnswer',
  props: {
    sd: {
      type: [Number, String],
      default: 0
    }
  },
  data () {
    return {
      synonymousAnswer: '',
      allSynonymAnswer: []
    }
  },
  methods: {
    get () {
      if (this.synonymousAnswer !== '') {
        this.allSynonymAnswer.push(this.synonymousAnswer)
      }
      // const answer = [...new Set(this.allSynonymAnswer)]
      // this.allSynonymAnswer = answer
      // this.allSynonymAnswer = this.allSynonymAnswer.join('|')
      this.allSynonymAnswer.join('|')
      this.$emit('get', this.synonymousAnswer, this.sd + 1)
    }
  }
}
</script>

<style>

</style>
