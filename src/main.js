// ie polyfill
import '@babel/polyfill'

import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store/'
// import { VueAxios } from './utils/request'
import AxiosPlugin from '@/utils/plugin/axios-plugin'
import {
  setCookie,
  getCookie,
  delCookie
} from './cookies'

// mock
// import './mock'
// 动画
import animated from 'animate.css'

import bootstrap from './core/bootstrap'
import './core/use'
import './permission' // permission control
import './utils/filter' // global filter
import './utils/directives.js'
import './utils/SubmitBtn.js'
import operatorPlugin from './utils/plugin/operator-plugin'

import VideoPlayer from 'vue-video-player'
import VueLazyload from 'vue-lazyload'

import Katex from 'vue-katex-auto-render'

import JsonExcel from 'vue-json-excel'
import {
  HappyScroll
} from 'vue-happy-scroll'
import ecStat from 'echarts-stat'
import 'echarts-liquidfill'

import './styles/common.less'

// 导入全局枚举
import EnumList from './api/allenum'

import VueDirectiveImagePreviewer from 'vue-directive-image-previewer'
import 'vue-directive-image-previewer/dist/assets/style.css'
import 'vue-easytable/libs/theme-default/index.css'
// 导入工具样式 (例如清除浮动)
import '@/utils/util'

// 导入视频功能插件
import 'vue-video-player/src/custom-theme.css'
import 'video.js/dist/video-js.css'
import {
  message
} from 'ant-design-vue'
import vueSwiper from 'vue-awesome-swiper'
import 'swiper/swiper-bundle.css'
import 'vue-happy-scroll/docs/happy-scroll.css'
// 引入管理端全局变量文件
import SchoolManage from '@/config/schoolManage'

import VueMathjax from 'vue-mathjax'
import facturl from '@/store/facturl'
import PaperFormat from '@/common/PaperFormat'
import '../src/utils/viewport'
// 放大预览图片
import preview from 'vue-photo-preview'
import 'vue-photo-preview/dist/skin.css'
import commonFn from './common/commonFn.js'

// echarts导入vue原型
// import '../node_modules/swiper'
// import { fabric } from 'fabric'
// import { Fabric } from 'vue-fabric'

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
// import htmlToPdf from '@/utils/htmlToPdf'

// import globalVariable from '@/utils/MathJax.js'
// Vue.prototype.commonsVariable = globalVariable;

// 全局引入UI反馈组件
import ElPack from '@/utils/interaction.js'

import htmlToPdf from '../src/utils/htmlToPdf'

import TokenCache from './utils/cache/TokenCache'
import OperatorCache from './utils/cache/OperatorCache'
// Vue.prototype.$echarts = echarts
// 全局引入公式组件
import Superscript from '@/components/formula/superscript/Superscript'
import Subscript from '@/components/formula/subscript/Subscript'
import Sqrt from '@/components/formula/sqrt/Sqrt'
import Sqrts from '@/components/formula/sqrts/Sqrts'
import Frac from '@/components/formula/frac/Frac'
import Div from '@/components/formula/div/Div'
import Vector from '@/components/formula/vector/Vector'
import Modulus from '@/components/formula/modulus/Modulus'
import Brace from '@/components/formula/brace/Brace'
import Sin from '@/components/formula/sin/Sin'
import Cos from '@/components/formula/cos/Cos'
import Tan from '@/components/formula/tan/Tan'
import Cot from '@/components/formula/cot/Cot'
import Ln from '@/components/formula/ln/Ln'
import Lg from '@/components/formula/lg/Lg'
import Log from '@/components/formula/log/Log'

import mathquill from '@/components/Mathquill/mathquill'

// import Vconsole from 'vconsole'
// const VConsole = new Vconsole()

// 引入字体尺寸

// import '@/utils/MathJax'
// import 'mathjax/es5/tex-svg'

import '@/assets/css/studentFontSize.less'
Vue.component('Subscript', Subscript)
Vue.component('Superscript', Superscript)
Vue.component('Sqrt', Sqrt)
Vue.component('Sqrts', Sqrts)
Vue.component('Frac', Frac)
Vue.component('Div', Div)
Vue.component('Modulus', Modulus)
Vue.component('Vector', Vector)
Vue.component('Brace', Brace)
Vue.component('Sin', Sin)
Vue.component('Cos', Cos)
Vue.component('Tan', Tan)
Vue.component('Cot', Cot)
Vue.component('Ln', Ln)
Vue.component('Lg', Lg)
Vue.component('Log', Log)
Vue.directive('katex', Katex)
Vue.directive('mathquill', mathquill)
Vue.prototype.$ElPack = ElPack
Vue.use(htmlToPdf)

// // 全局引入页面组件
// import installComponents from './components/index.js';
// Vue.use(installComponents)

// 下拉滚动条
// import  happyScroll  from 'vue-happy-scroll'
// Vue.use(happyScroll)
// import 'vue-happy-scroll/docs/happy-scroll.css'

const options = {
  fullscreenEl: false,
  closeEl: false,
  zoomEl: true
}
// import globalVariable from '@/components/globalVariable'
// Vue.prototype.commonsVariable = globalVariable

// 徐汇网站地址名称
Vue.prototype.Facturl = facturl
Vue.prototype.PaperFormat = PaperFormat


// 设置全局变量 校或地区
Vue.prototype.$SchoolManage = SchoolManage
Vue.component('downloadExcel', JsonExcel)
Vue.component('happy-scroll', HappyScroll)

// import SocketService from '@/utils/socket_service'
// // // 对服务端进行websocket的连接
// SocketService.Instance.connect()
// // // 其他的组件  this.$socket
// Vue.prototype.$socket = SocketService.Instance

// 全局提示的配置
message.config({
  top: '350px',
  duration: 0.8
})
require('video.js/dist/video-js.css')
require('vue-video-player/src/custom-theme.css')

// require('../node_modules/animate.css/animate.css')

Vue.config.productionTip = false

// mount axios Vue.$http and this.$http
// Vue.use(VueAxios)
// Vue.use(ElementUI)
Vue.use(VueLazyload)
Vue.use(animated)
Vue.use(AxiosPlugin)
Vue.use(operatorPlugin)
Vue.use(VideoPlayer)
Vue.use(VueDirectiveImagePreviewer)
Vue.use(vueSwiper)
Vue.use(JsonExcel)
Vue.use(HappyScroll)
Vue.use(ecStat)
Vue.use(preview, options)
Vue.use(VueMathjax)
Vue.use(preview)
Vue.use(commonFn)
Vue.use(ElementUI)
Vue.use(htmlToPdf)
// Vue.use(VConsole)

// Vue.prototype.globalVariable = globalVariable

// Vue.use(Fabric)

// Vue.use(fabric)
// export default fabric
// loading框设置局部刷新，且所有请求完成后关闭loading框
Vue.prototype.UwooEnumData = EnumList
Vue.prototype.$cookieStore = {
  setCookie,
  getCookie,
  delCookie
}

Vue.use(VueLazyload, {
  preLoad: 1.3,
  error: 'dist/error.png',
  loading: 'dist/loading.gif',
  attempt: 1
})

new Vue({
  router,
  store,
  created: bootstrap,
  render: h => h(App)
}).$mount('#app')
router.beforeEach((to, from, next) => {
  const timeArr = [{
    st: '2025-02-05 00:00',
    et: '2025-02-10 23:59'
  }]
  const now = new Date().getTime()
  const last = localStorage.getItem('LAST_TIP_SHOW') ? JSON.parse(localStorage.getItem('LAST_TIP_SHOW'))[localStorage.getItem('UserId')] : 0
  if (now - last > 24 * 60 * 60 * 1000) {
    for (let i = 0; i < timeArr.length; i++) {
      const st = new Date(timeArr[i].st).getTime()
      const et = new Date(timeArr[i].et).getTime()
      if (now > st && now < et) {
        ElementUI.MessageBox({
          showClose: false,
          closeOnClickModal: false,
          dangerouslyUseHTMLString: true,
          showConfirmButton: true,
          confirmButtonText: '我知道了',
          message: `
        <p style="font-size:20px; color: #4B6FEE ;text-align:center;">提示</p>
        <div style="font-size:14px;color:#333;">
        <p>亲爱的用户：</p>
        <p style="text-indent: 2em">
            您好，专课专练平台将于2025年02月05日-02月10日期间，完成新学期的切换工作。在此期间，平台将无法登录。请各位老师和学生合理安排专课专练平台使用的时间。
        </p>
        <p style="text-indent: 2em">
            有任何问题，请和专课专练工作人员联系。谢谢。
        </p>
        </div>
        `,
          callback: action => {
            const storeMsg = {}
            storeMsg[localStorage.getItem('UserId')] = new Date().getTime();
            localStorage.setItem('LAST_TIP_SHOW', JSON.stringify(storeMsg))
          }
        })
        break
      }
    }
  }
  /*2024/10/30号周凯亮与产品张森沟通后提出拿掉版本验证，后续有用到版本提示再重新制定梳理需求*/
  // const V_S_F_A_L_O = localStorage.getItem('V_S_F_A_L_O')
  // let platform = localStorage.getItem('PL_newP')
  // if (to.path === '/Home/register/IdentityCheck' || to.path === '/Home/UserLogin' && !platform) {
  //   localStorage.setItem('PL_newP', '1')
  //   platform = '1'
  // }
  // const versionForAutoLoginOut = store.state.versionForAutoLoginOut
  // if (to.fullPath === '/Home/UserLogin') {
  //   if (V_S_F_A_L_O !== versionForAutoLoginOut) {
  //     localStorage.setItem('V_S_F_A_L_O', versionForAutoLoginOut)
  //   }
  //   next()
  //   return
  // }
  // if (V_S_F_A_L_O !== versionForAutoLoginOut || !platform) {
  //   ElementUI.MessageBox({
  //     title: '版本更新提示',
  //     message: '亲爱的用户，感谢您一直以来的支持，为持续给您带来更佳的体验，平台进行了升级改版，请您重新登录后继续使用。',
  //     center: true,
  //     callback: action => {
  //       TokenCache.deleteToken()
  //       OperatorCache.clear()
  //       localStorage.clear()
  //       localStorage.setItem('V_S_F_A_L_O', versionForAutoLoginOut)
  //       if (document.domain === 'st.eduwon.cn') {
  //         location.href = 'https://sz-api.ai-study.net/home.html'
  //       } else {
  //         location.reload()
  //       }
  //     }
  //   })
  // } else {
    next()
  // }
})