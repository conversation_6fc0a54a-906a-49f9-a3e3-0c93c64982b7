.flex{
    display:flex;
}
.flex_direction_col{
    flex-direction:column,
}
.flex_space_between{
    justify-content: space-between;
}
.flex_start{
    justify-content: flex-start;
}
.flex_space_around{
    justify-content: space-around;
}
.flex_justify_center{
    justify-content: center;
}
.flex_flex_end{
    justify-content: flex-end;
}
.flex_align_center{
    align-items:center;
}
.flex_align_start{
    align-items:flex-start;
}
.flex_wrap{
    flex-wrap:wrap;
}
.flex_item_grow{
    flex-grow:1; //默认为0-不放大
}
.flex_item_shrink{
    flex-shrink:0; //默认为1-缩小
}
.back_white{
    background:#fff;
}
.font_color_white{
    color:#fff;
}
.font_color_333{
    color:#333;
}
.theme_color{
    color:#61bb96;
} 
.theme_back_color{
    background: #61bb96;
}
.margin_10{
    margin:10px;
}
.margin_lr_10{
    margin:0 10px;
}
.margin_lr_20{
    margin:0 20px;
}
.margin_tb_10{
    margin:10px 0;
}
.margin_tb_20{
    margin:20px 0;
}
.margin_r_15{
    margin-right:15px;
}
.margin_r_5{
    margin-right:5px;
}
.margin_l_15{
    margin-left:15px;
}
.margin_b_10{
    margin-bottom:10px;
}
.margin_b_5{
    margin-bottom:5px;
}
.border_radius_6{
    border-radius:6px;
}
.border_radius_4{
    border-radius:4px;
}
.cursor_pointer{
    cursor:pointer;
}
.font_size_14{
    font-size:14px;
}
.font_size_16{
    font-size:16px;
}
.font_size_18{
    font-size:18px;
}
.font_size_20{
    font-size:20px;
}
.font_size_26{
    font-size:26px;
}
.font_size_24{
    font-size:24px;
}
.font_weight_bold{
    font-weight:bold;
}
// /deep/.el-button{
//     background: #61bb96;
//     border-color: #61bb96;
//     font-size:18px;
// }
.pageTop{
    height:80px;
    line-height:60px;
    padding:10px;
    font-size:22px;
    border-radius:6px;
}  
.tip_mark{
    width:5px;
    background:rgb(39, 81, 216);
    border-radius:5px
}
.padding_20{
    padding:20px;
}
.padding_10{
    padding:10px;
}
.padding_5{
    padding:5px;
}
.padding_tb_10{
    padding:10px 0;
}
.padding_tb_15{
    padding:15px 0;
}
.padding_tb_5{
    padding:5px 0;
}
.padding_lr_10{
    padding: 0 10px;
}
.padding_b_10{
    padding-bottom:10px;
}
.padding_r_10{
    padding-right:10px;
}
.padding_t_20{
    padding-top:20px;
}
.padding_b_0{
    padding-bottom:0px;
}

.text_align_center{
    text-align:center;
}
.text_align_right{
    text-align:right;
}
.text_align_left{
    text-align:left;
}
.position_relative{
    position:relative;
}
.position_absolute{
    position:absolute;
}
.border_radius_circle{
    border-radius:50%;
}
.border_left{
    border-left:1px solid rgba(216, 216, 216, 1);
}
.line_height_22{
    line-height:22px;
}
.line_height_24{
    line-height:24px;
}
.line_height_30{
    line-height:30px;
}
.line_height_24{
    line-height:24px;
}
.line_height_40{
    line-height:40px;
}
.font_weight_bold{
    font-weight:bold;
}
.color_91{
    color:#919191;
}
.color_905DF5{
    color:#905DF5;
}
.color_E02020{  
    color: #E02020;
}
