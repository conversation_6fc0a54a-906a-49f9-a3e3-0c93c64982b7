/*
 * @Author: your name
 * @Date: 2022-02-07 11:43:47
 * @LastEditTime: 2022-03-14 20:18:10
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \优化\学生端优化\src\utils\cache\OperatorCache.js
 */
import { Axios } from "@/utils/plugin/axios-plugin"
import store from "@/store"
import router from "../../router"
let permissions = []
let inited = false

let OperatorCache = {
    info: {},
    inited() {
        return inited
    },
    init(callBack) {
        if (inited)
            callBack()
        else {
            Axios.post('/Base_Manage/Home/GetOperatorInfo').then(resJson => {
                if (!!!resJson.Success) {
                    inited = false
                    callBack()
                }
                else {
                    this.info = resJson.Data.UserInfo
                    permissions = resJson.Data.Permissions
                    // console.log(this.info, ' this.info')
                    // localStorage.setItem('userName', this.info.UserName)
                    // /Base_Manage/Home/GetOperatorInfo
                    // let IsSubject = 'false'
                    localStorage.setItem('IsSubject', this.info.IsSubject)
                    // localStorage.setItem('IsSubject', IsSubject)
                    inited = true
                  // 这里用做黄浦区学生答题限制
                  //   console.log(this.$store)
                  store.commit('limitingJobPermissions',resJson.Data.IsShow)
                    callBack()
                }

            })
        }
    },
    hasPermission(thePermission) {
        return permissions.includes(thePermission)
    },
    clear() {
        inited = false
        permissions = []
        this.info = {}
    }
}

export default OperatorCache