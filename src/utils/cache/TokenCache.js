const tokenKey = 'jwtToken'

const TokenCache = {
  getToken() {
    return localStorage.getItem(tokenKey)
  },
  setToken(newToken) {
    let reg = /[\u4e00-\u9fa5]+/g;
    var s = newToken.match(reg);
    if (s === null || s === undefined || s === "") {
      localStorage.setItem(tokenKey, newToken)
    } else {
      localStorage.removeItem(tokenKey)
    }
  },
  deleteToken() {
    localStorage.removeItem(tokenKey)
  }
}

export default TokenCache
