import Title from 'ant-design-vue/lib/skeleton/Title'

export function arrlistTitle (title, item, typeid, styl) {
  let Ttitle
  let optionS
  if (typeid == '52') {
    Ttitle = item.ItemChange.SelectFillBlank.Title
    optionS = item.ItemChange.SelectFillBlank.Options
  }
  if (typeid == '70') {
    Ttitle = item.ItemChange.MultitermSingle.Title
    optionS = item.ItemChange.MultitermSingle.Options
  }
  const ShowAnswer = item.Answer
  const pattern = /（）/gi
  const arr = Ttitle.match(pattern)
  for (let i = 0; i < arr.length; i++) {
    Ttitle = Ttitle.replace(arr[i], `(@${i})`)
  }
  const otherAnsCount = optionS.length
  for (let i = 0; i < otherAnsCount; i++) {
    if (typeid == '52') {
      Ttitle = Ttitle.replace(`(@${i})`, ` （${bankedFun(title, optionS[i], i, ShowAnswer).join(' ')}）`)
    }
    if (typeid == '70') {
      if (title !== '初始化' && title !== '回显') {
        if (styl === 1) {
          Ttitle = Ttitle.replace(`(@${i})`, ` （<select id="pet-select${i}" data-ind="${i}" style="border: 1px solid grey;border-radius: 2px;padding: 1px 4px;color:#68bb97;">${optionsDomObj(title, optionS[i].Content, i, item, styl).opt}</select>）`)
        } else {
          Ttitle = Ttitle.replace(`(@${i})`, ` （<select id="pet-select${i}" data-ind="${i}" style="border: 1px solid grey;border-radius: 2px;padding: 1px 4px;color:${optionsDomObj(title, optionS[i].Content, i, item).trueFlag ? '#68bb97' : '#F56C6C'}">${optionsDomObj(title, optionS[i].Content, i, item).opt}</select>）`)
        }
      } else {
        Ttitle = Ttitle.replace(`(@${i})`, ` （<select id="pet-select${i}" data-ind="${i}" style="border: 1px solid grey;border-radius: 2px;padding: 1px 4px;">${optionsDomObj(title, optionS[i].Content, i, item).opt}</select>）`)
      }
    }
  }
  return Ttitle
}
// 选词填空数据解析
function bankedFun (title, optionS, ind, ShowAnswer) {
  let returnEle = ''
  const btnTitle = []
  const bankedTitle = optionS.split('|')
  bankedTitle.forEach((res, index) => {
    if (title === '初始化') {
      btnTitle.push(
        "<button style='border:none;background-color: #DCDFE6;border-radius: 2px;color: #000000;padding:0 12px;' class=" +
          'ind_' +
          ind +
          "  name='button' data-inner = " +
          index +
          ' data-ind=' +
          ind +
          ' data-val=' +
          res +
          '>' +
          res +
          '</button>'
      )
    } else if (title === '回显') {
      const Show = ShowAnswer.split('|')
      if (res === Show[ind]) {
        btnTitle.push("<button style='border:none;background-color: #00d4af;border-radius: 2px;color: #FFFFFF;padding:0 12px;' class=" + 'ind_' + ind + "  name='button' data-inner = " + index + ' data-ind=' + ind + ' data-val=' + res + '>' + res + '</button>')
      } else {
        btnTitle.push("<button style='border:none;background-color: #DCDFE6;border-radius: 2px;color: #000000;padding:0 12px;' class=" + 'ind_' + ind + "  name='button' data-inner = " + index + ' data-ind=' + ind + ' data-val=' + res + '>' + res + '</button>')
      }
    } else {
      const Show = ShowAnswer.split('|')
      if (res === Show[ind]) {
        btnTitle.push("<span style='background-color:#68bb97;border-radius: 2px;color: #FFFFFF;padding:0 12px;'>" + res + '</span>')
      } else {
        btnTitle.push("<span style='background-color:#DCDFE6;border-radius: 2px;color: #000000;padding:0 12px;'>" + res + '</span>')
      }
    }
    btnTitle.join(' ')
    returnEle = btnTitle
  })
  return returnEle
}

function optionsDomObj (title, opt, ind, item, styl) {
  let trueFlag
  const newOpt = opt.split('|')
  const option = []
  const IsContent = item.ItemChange.MultitermSingle.Options
  newOpt.forEach((res, index) => {
    if (title === '初始化') {
      option.push('<option value="' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '">' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '</option>')
    } else if (title === '回显') {
      const Show = item.Answer.split('|')
      const newValue = IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res
      if (newValue === Show[ind]) {
        option.push('<option value="' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '" selected>' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '</option>')

      } else {
        option.push('<option value="' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '">' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '</option>')
      }
    } else {
      const Show = styl === 1 ? item.Answer.split('|') : item.UserAnswer.split('|')
      const newValue = IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res
      if (newValue === Show[ind]) {
        option.push('<option value="' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '" selected>' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '</option>')
        if (Show[ind] === item.Answer.split('|')[ind]) {
          trueFlag = true
        } else {
          trueFlag = false
        }
      } else {
        option.push('<option value="' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '" disabled>' + `${IsContent[ind].IsContentShow === 2 ? IsContent[ind].Options[index] : res}` + '</option>')
      }
    }
  })
  option.unshift('<option value="none" selected disabled hidden>请选择</option>')
  return {
    opt: option.join(''),
    trueFlag
  }
}

// 事件点击
export function onSelectClick (e, that) {
  const ind = e.target.dataset.ind
  const eventval = $(`#Imgid${that.num}`).find(`#${e.target.id}`).find('option:selected').text()
  const _PaperList = that.PaperList[that.CurrentNum - 1]
  const _options = that.PaperList[that.CurrentNum - 1].ItemChange.MultitermSingle.Options
  if (_options[ind].IsContentShow === 2) {
    that.arrValue = _options[ind].Content.split('|')
  } else {
    that.arrValue = []
  }
  if (eventval !== '请选择') {
    that.AnswerArr[ind] = eventval
    _PaperList.Answer = that.AnswerArr.join('|')
    _PaperList.AnswerList = that.AnswerArr
    if (_PaperList.AnswerList.length < _options.length) {
      _PaperList.HasAnswer = false
    } else if (_PaperList.AnswerList.length == _options.length) {
      _PaperList.HasAnswer = !_PaperList.AnswerList.some((e) => !e)
    }
  } else {
    return false
  }
}

