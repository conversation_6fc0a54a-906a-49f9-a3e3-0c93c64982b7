import axios from 'axios'
import TokenCache from '@/utils/cache/TokenCache'
import defaultSettings from '@/config/defaultSettings'
import ProcessHelper from '@/utils/helper/ProcessHelper'
import { Session } from '@/utils/storage'

const rootUrl = () => {
  if (ProcessHelper.isProduction() || ProcessHelper.isPreview()) {
    return defaultSettings.publishRootUrl
  } else {
    return defaultSettings.localRootUrl
  }
}
const rootUrl1 = () => {
  if (ProcessHelper.isProduction() || ProcessHelper.isPreview()) {
    return defaultSettings.publishRootUrl1
  } else {
    return defaultSettings.localRootUrl1
  }
}
export const Axios1 = axios.create({
  baseURL: rootUrl1(),
  timeout: defaultSettings.apiTimeout
})
export const Axios = axios.create({
  baseURL: rootUrl(),
  timeout: defaultSettings.apiTimeout
})

Axios1.interceptors.request.use(
  config => {
    // 设置以 form 表单的形式提交参数，如果以 JSON 的形式提交表单，可忽略
    // if (config.method === 'post') {
    //     // JSON 转换为 FormData
    //     const formData = new FormData()
    //     Object.keys(config.data).forEach(key => formData.append(key, config.data[key]))
    //     config.data = formData
    // }

    // 携带token
    if (TokenCache.getToken()) {
      config.headers.Authorization = 'Bearer ' + TokenCache.getToken()
      config.headers.Source = localStorage.getItem('PL_newP') || 1
      config.headers.SubjectId = localStorage.getItem('SubjectId')
      config.headers.Term = Session.get('ChapterObj') ? Session.get('ChapterObj')['Term'] : localStorage.getItem('CurrentSemester')
      // this.$cookieStore.setCookie('jwtToken', TokenCache.getToken(), '.eduwon.cn')
      // this.$cookieStore.setCookie('jwtToken', TokenCache.getToken(), 'hp.eduwon.cn')
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 在发送请求之前做某件事
Axios.interceptors.request.use(
  config => {
    // 设置以 form 表单的形式提交参数，如果以 JSON 的形式提交表单，可忽略
    // if (config.method === 'post') {
    //     // JSON 转换为 FormData
    //     const formData = new FormData()
    //     Object.keys(config.data).forEach(key => formData.append(key, config.data[key]))
    //     config.data = formData
    // }

    // 携带token
    if (TokenCache.getToken()) {
      config.headers.Authorization = 'Bearer ' + TokenCache.getToken()
      config.headers.Source = localStorage.getItem('PL_newP') || 1
      config.headers.SubjectId = localStorage.getItem('SubjectId')
      config.headers.Term = Session.get('ChapterObj') ? Session.get('ChapterObj')['Term'] : localStorage.getItem('CurrentSemester')
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 返回状态判断(添加响应拦截器)
Axios.interceptors.response.use(

  res => {
    // 授权失败
    if (!res.data.Success && res.data.ErrorCode == 0) {
      // TokenCache.deleteToken()
      // location.href = '/'
    }
    return res.data
  },
  error => {
    // debugger
    let errorMsg = ''
    // if (error.message.includes('timeout')) {
    //   errorMsg = '请求超时!'
    // } else {
    //   errorMsg = '请求异常!'
    // }
    if (error.code === 'ECONNABORTED' || error.message.indexOf('timeout') !== -1 || error.message === 'Network Error') {
      errorMsg = '请求超时!'
    } else {
      errorMsg = '请求异常!'
    }
    console.log(errorMsg, 'errorMsg')
    // return Promise.resolve({ Success: false, Msg: errorMsg })
    return Promise.reject(error)

  }
)


// 在发送请求之前做某件事
// Axios.interceptors.request.use('/KeyCapabilitie/ManageKeyCapabilitie/GetItemKeyCapaListByPaperId').then(res => {
//   console.log(res, '学习水平')
// })
export default {
  install(Vue) {
    Object.defineProperty(Vue.prototype, '$http', { value: Axios })
    Object.defineProperty(Vue.prototype, '$uwonhttp', { value: Axios1 })
    Object.defineProperty(Vue.prototype, '$rootUrl', { value: rootUrl() })
    Object.defineProperty(Vue.prototype, '$rootUrl1', { value: rootUrl1() })
    Object.defineProperty(Vue.prototype, '$token', { value: 'Bearer ' + TokenCache.getToken() })
  }
}
