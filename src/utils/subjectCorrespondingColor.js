export function CorrespondingColor () {
  const Color = {
    '1': '#0968FC', // 语文
    '1bg': '#E5EEFD',
    '10': '#0968FC', // 唱游
    '10bg': '#E5EEFD',
    '13': '#0968FC', // 书法
    '13bg': '#E5EEFD',
    '14': '#0968FC', // 心理健康
    '14bg': '#E5EEFD',
    '15': '#0968FC', // 科教版自然
    '15bg': '#E5EEFD',
    '16': '#0968FC', // 远东版自然
    '16bg': '#E5EEFD',
    '2': '#0968FC', // 数学
    '2bg': '#E5EEFD',
    '21': '#0968FC', // 语文
    '21bg': '#E5EEFD',
    '22': '#0968FC', // 数学
    '22bg': '#E5EEFD',
    '27': '#0968FC', // 历史
    '27bg': '#E5EEFD',
    '50': '#0968FC', // 语文
    '50bg': '#E5EEFD',
    '51': '#0968FC', // 数学
    '51bg': '#E5EEFD',
    '61': '#0968FC', // 历史
    '61bg': '#E5EEFD',
    '72': '#0968FC', // 历史（选科）
    '72bg': '#E5EEFD',
    '9': '#0968FC', // 自然
    '9bg': '#E5EEFD',
    '23': '#FA6400', // 英语
    '23bg': '#FDEEE4',
    '34': '#FA6400', // 信息科技
    '34bg': '#FDEEE4',
    '37': '#FA6400', // 牛津上海版英语
    '37bg': '#FDEEE4',
    '39': '#FA6400', // 生命科学
    '39bg': '#FDEEE4',
    '4': '#FA6400', // 科学与技术
    '4bg': '#FDEEE4',
    '40': '#FA6400', // 社会
    '40bg': '#FDEEE4',
    '5': '#FA6400', // 道德与法治
    '5bg': '#FDEEE4',
    '11': '#FA6400', // 信息科技
    '11bg': '#FDEEE4',
    '28': '#FA6400', // 地理
    '28bg': '#FDEEE4',
    '29': '#FA6400', // 科学
    '29bg': '#FDEEE4',
    '3': '#FA6400', // 英语
    '3bg': '#FDEEE4',
    '30': '#FA6400', // 道德与法治
    '30bg': '#FDEEE4',
    '52': '#FA6400', // 上教版英语
    '52bg': '#FDEEE4',
    '53': '#FA6400', // 上外版英语
    '53bg': '#FDEEE4',
    '57': '#FA6400', // 信息科技
    '57bg': '#FDEEE4',
    '74': '#FA6400', // 英语
    '74bg': '#FDEEE4',
    '75': '#FA6400', // 地理（选科）
    '75bg': '#FDEEE4',
    '76': '#FA6400', // 生命科学（选科）
    '76bg': '#FDEEE4',
    '62': '#FA6400', // 地理
    '62bg': '#FDEEE4',
    '63': '#FA6400', // 生命科学
    '63bg': '#FDEEE4',
    '12': '#6236FF', // 劳动技术
    '12bg': '#EEE9FD',
    '31': '#6236FF', // 音乐
    '31bg': '#EEE9FD',
    '32': '#6236FF', // 美术
    '32bg': '#EEE9FD',
    '33': '#6236FF', // 体育
    '33bg': '#EEE9FD',
    '35': '#6236FF', // 劳动技术
    '35bg': '#EEE9FD',
    '36': '#6236FF', // 体育与健身
    '36bg': '#EEE9FD',
    '38': '#6236FF', // 艺术
    '38bg': '#EEE9FD',
    '54': '#6236FF', // 艺术
    '54bg': '#EEE9FD',
    '55': '#6236FF', // 体育与健身
    '55bg': '#EEE9FD',
    '58': '#6236FF', // 劳动技术
    '58bg': '#EEE9FD',
    '65': '#6236FF', // 美术
    '65bg': '#EEE9FD',
    '64': '#6236FF', // 音乐
    '64bg': '#EEE9FD',
    '6': '#6236FF', // 音乐
    '6bg': '#EEE9FD',
    '68': '#6236FF', // 体育与健康
    '68bg': '#EEE9FD',
    '8': '#6236FF', // 体育与健身
    '8bg': '#EEE9FD',
    '7': '#6236FF', // 美术
    '7bg': '#EEE9FD',
    '24': '#FF306A', // 物理
    '24bg': '#FDE9EF',
    '25': '#FF306A', // 化学
    '25bg': '#FDE9EF',
    '26': '#FF306A', // 生物
    '26bg': '#FDE9EF',
    '56': '#FF306A', // 思想政治
    '56bg': '#FDE9EF',
    '59': '#FF306A', // 物理
    '59bg': '#FDE9EF',
    '60': '#FF306A', // 化学
    '60bg': '#FDE9EF',
    '66': '#FF306A', // 通用技术
    '66bg': '#FDE9EF',
    '67': '#FF306A', // 信息技术
    '67bg': '#FDE9EF',
    '69': '#FF306A', // 生物学
    '69bg': '#FDE9EF',
    '70': '#FF306A', // 思想政治（选科）
    '70bg': '#FDE9EF',
    '71': '#FF306A', // 化学（选科）
    '71bg': '#FDE9EF',
    '73': '#FF306A', // 物理（选科）
    '73bg': '#FDE9EF'
  }
  return Color
  // return { col: Color[ID], bg: Color[ID + 'bg'] }
  // console.log(Color[ID])
}
