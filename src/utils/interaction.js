import {
    Loading,
    MessageBox
} from "element-ui"

class ElPack {
    constructor(text, type, loading) {
        this.text = text
        this.type = type
        this.loading = loading
    }

    open(text) {
        this.loading = Loading.service({
            lock: false,	//全屏锁定的标识符
            text: text,	//文字提示
            spinner: 'el-icon-magic-stick',	//图片
            background: 'hsla(0,0%,100%,.9)',	//背景颜色
            target: document.querySelector('.div1')
        })
    }

    close() {
        this.loading.close()
    }


    confirm(text, type) {
        return MessageBox.confirm(text, "提示", {
            cancelButtonClass: 'is-cancel',
            confirmButtonClass: 'is-confirm',
            type: type,
        })
    }
}

export default new ElPack