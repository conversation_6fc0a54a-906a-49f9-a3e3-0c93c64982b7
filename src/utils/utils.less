.textOverflow() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.pop-info {
  padding: 0 20px;
  height: 700px;
  background-color: #fff;
  border-radius: 8px;
}

.textOverflowMulti(@line: 3, @bg: #fff) {
  position: relative;
  max-height: @line * 1.5em;
  margin-right: -1em;
  padding-right: 1em;
  overflow: hidden;
  line-height: 1.5em;
  text-align: justify;

  &::before {
    position: absolute;
    right: 14px;
    bottom: 0;
    padding: 0 1px;
    background: @bg;
    content: '...';
  }

  &::after {
    position: absolute;
    right: 14px;
    width: 1em;
    height: 1em;
    margin-top: 0.2em;
    background: white;
    content: '';
  }
}

.clearfix() {
  zoom: 1;

  &::before,
  &::after {
    display: table;
    content: ' ';
  }

  &::after {
    clear: both;
    height: 0;
    font-size: 0;
    visibility: hidden;
  }
}

.whole-mr {
  margin-right: 190px;
}

.fl {
  float: left;
}

.fg {
  float: right;
}

ul,
ol {
  list-style: none;
}

.cur {
  cursor: pointer;
}

.paging {
  margin-top: 40px;
  text-align: center;

  /deep/.ant-pagination-item:focus,
  .ant-pagination-item:hover {
    border-color: #68BB97;
    border-color: #68BB97;
  }

  /deep/.ant-pagination-item-active a {
    color: #000;
    border-color: #68BB97;
    background-color: #68BB97;
  }

  /deep/.ant-pagination-item-active {
    border-color: #68BB97;
  }
}

.footer {
  font-size: 12px;
  text-align: center;
  color: #B5B5B5;
}

.default-unit {
  height: 450px;
  margin-top: 100px;
  text-align: center;
  color: #ccc;
}

.Answer_content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 20px;

  .Item_Answer {
    width: 100%;
    margin-bottom: 20px;
    background: #ffff;
    padding: 20px;
    position: relative;
    min-height: 350px;

    .Card_header {
      position: relative;
      display: flex;

      div {
        width: 6px;
        height: 34px;
        background: #418869;
        border-radius: 3px;
      }

      span {
        font-size: 22px;
        color: #6d6d6d;
        line-height: 37px;
        margin-left: 10px;
      }

      .Answer_p {
        float: right;
      }
    }
  }



  .Progress_footer {
    display: flex;
    padding: 10px;
    justify-content: space-between;
    div:nth-child(1){
      img {
        width: 22px;
        height: 24px;
        vertical-align: middle;
      }
    }
    div:nth-child(2){
      img {
        width: 26px;
        height: 26px;
        vertical-align: middle;
      }
    }


    span {
      vertical-align: middle;
      //font-size: 17px;
      color: #b8b8b8;
    }

    .Name_time {
      //font-size: 18px;
      color: #5e5e5e;
      margin-top: 5px;
      display: block;
    }
  }

  .Answer_ul {
    width: 25%;
    position: absolute;
    right: 0;
    font-size: 23px;
    display: flex;
    justify-content: space-between;

    .Frist_p {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #71B6A1;
      display: inline-block;
      vertical-align: middle;
      margin-right: 6px;
    }

    .Second_p {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #F77174;
      display: inline-block;
      vertical-align: middle;
      margin-right: 6px;
    }
    .Third_p{
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #ECA46B;
      display: inline-block;
      vertical-align: middle;
      margin-right: 6px;
    }
  }

  .List_btn {
    display: flex;
    flex-wrap: wrap;
    left: 10%;

    .error-answer {
      display: inline-block;
      width: 41px;
      height: 40px;
      line-height: 40px;
      margin: 16px 16px 0 0;
      text-align: center;
      border: 1px solid #f77174;
      border-radius: 35px;
      cursor: pointer;
      color: #fff;
      background: #f77174;
    }

    .current-answer {
      display: inline-block;
      width: 41px;
      height: 40px;
      line-height: 40px;
      margin: 16px 16px 0 0;
      text-align: center;
      border: 1px solid #71b6a1;
      border-radius: 35px;
      cursor: pointer;
      color: #fff;
      background: #71b6a1;
    }
    .half-answer {
      display: inline-block;
      width: 41px;
      height: 40px;
      line-height: 40px;
      margin: 16px 16px 0 0;
      text-align: center;
      border: 1px solid #ECA46B;
      border-radius: 35px;
      cursor: pointer;
      color: #fff;
      background: #ECA46B;
    }
    .no-answer{
      display: inline-block;
      width: 41px;
      height: 40px;
      line-height: 40px;
      margin: 16px 16px 0 0;
      text-align: center;
      border: 1px solid grey;
      border-radius: 35px;
      cursor: pointer;
      color: #000000;
      background: #FFFFFF;
    }
  }

  .Prce_btn {
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 20px;


    .Number {
      width: 25%;
      background: #fff;
      border-color: #49aded;
      color: #49aded;
    }

    .Resove {
      width: 25%;
      background: #49aded;
      border-color: #49aded;
      color: #ffff;
    }

  }

  .pers {
    height: 328px;
    overflow: auto;
    margin-top: 10px;

    .pers_box {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      box-shadow: 0px 0px 7px 1px rgba(233, 233, 233, 0.5);
      border-radius: 6px;
      align-items:center;
      .pers_left {
        line-height: 28px;
        padding: 10px;

        span {
          //font-size: 18px;
          margin-left: 5px;
        }
      }

      .pers_right {
        position: relative;
        display:flex;
        .Practice_btn {
          //height:40px;
          background:#fff;
          border-color:#83caab;
          color:#83caab;
          // position: absolute;
          // right: 10px;
          // top: 35%;
        }
        .Practice_btn_back{
          background:#83caab;
          color:#fff;
        }
      }
    }
  }

}

.Type1_papers {
  width: 50%;
  color: #fff;
  background: #ed8c40;
  border-radius: 0 0.104167rem 0.104167rem 0;
}

.Type2_papers {
  width: 50%;
  color: #fff;
  background: #83caab;
  border-radius: 0 0.104167rem 0.104167rem 0;
}

.car_type1 {
  border: 1px solid #ed8c40;
  color: #ed8c40;
  //font-size: 12px;
  border-radius: 10px;
  width: 70px;
  display: inline-block;
  text-align: center;
}

.car_type2 {
  border: 1px solid #83caab;
  color: #83caab;
  //font-size: 12px;
  border-radius: 10px;
  width: 70px;
  display: inline-block;
  text-align: center;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: #F5F5F5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
  background-color: #c8c8c8;
}

// @media (min-width: 1024px){
//   body{font-size: 18px}
//   } /*>=1024的设备*/

//   @media (min-width: 1100px) {
//   body{font-size: 20px}
//   } /*>=1100的设备*/
//   @media (min-width: 1280px) {
//   body{font-size: 22px;}
//   } /*>=1280的设备*/

//   @media (min-width: 1366px) {

//   body{font-size: 24px;}
//   }  

//   @media (min-width: 1440px) {
//   body{font-size: 25px !important;}
//   } 

//   @media (min-width: 1680px) {
//   body{font-size: 28px;}
//   } 
//   @media (min-width: 1920px) {
//   body{font-size: 33px;}
//   } 