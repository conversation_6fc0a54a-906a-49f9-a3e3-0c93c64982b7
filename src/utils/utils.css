* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
.pop-info {
  padding: 0 20px;
  height: 700px;
  background-color: #fff;
  border-radius: 8px;
}
/* .chapter-width {
  width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chapter-width:hover {
  overflow: visible;
  z-index: 2;
} */
.whole-mr {
  margin-right: 190px;
}
.fl {
  float: left;
}
.fg {
  float: right;
}
ul,
ol {
  list-style: none;
}
.cur {
  cursor: pointer;
}
.paging {
  margin-top: 40px;
  text-align: center;
}
.paging /deep/ .ant-pagination-item:focus,
.paging .ant-pagination-item:hover {
  border-color: #68BB97;
}
.paging /deep/ .ant-pagination-item-active a {
  color: #000;
  border-color: #68BB97;
  background-color: #68BB97;
}
.paging /deep/ .ant-pagination-item-active {
  border-color: #68BB97;
}
.footer {
  font-size: 12px;
  margin-top: 80px;
  text-align: center;
  color: #B5B5B5;
}
.default-unit {
  height: 450px;
  margin-top: 100px;
  text-align: center;
  color: #ccc;
}
