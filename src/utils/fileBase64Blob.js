/* base64转file */
export const base64ConvertFile = function (urlData, filename) {
  if (typeof urlData != 'string') {
    return
  }
  let arr = urlData.split(',')
  let type = arr[0].match(/:(.*?);/)[1]
  let fileExt = type.split('/')[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], 'filename.' + fileExt, {
    type: type
  })
}
/* base64转blob */
export const dataURItoBlob = (dataURI) => {
  let byteString = atob(dataURI.split(',')[1])
  let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]
  let ab = new ArrayBuffer(byteString.length)
  let ia = new Uint8Array(ab)
  for (var i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i)
  }
  return new Blob([ab], { type: mimeString })
}
/*file转base64方法*/
// let reader = new FileReader()
// reader.readAsDataURL(file[0])
// console.log(reader)
