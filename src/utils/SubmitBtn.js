/*
 * @Author: your name
 * @Date: 2022-03-16 13:11:57
 * @LastEditTime: 2022-03-16 13:30:44
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \Student.Web\src\utils\SubmitBtn.js
 */
// 阻止按钮重复提交
import Vue from 'vue'

// 阻止按钮重复提交
Vue.directive("button", {
    bind: function (el, binding, vnode) { // 每当指令绑定到元素上的时候，会立即执行这个bind函数，只执行一次
        function clickHandler(e) {
            // 这里判断点击的元素是否是本身，是本身，则返回
            if (el.contains(e.target)) {
                if (!el.disabled) {
                    el.disabled = true
                    setTimeout(() => {
                        el.disabled = false
                    }, binding.value || 10000)
                }
                return false;
            }
            // 判断指令中是否绑定了函数    // 如果绑定了函数 则调用那个函数，此处binding.value就是handleClose方法
            if (binding.expression) {
                binding.value(e);
            }
        }
        // 给当前元素绑定个私有变量，方便在unbind中可以解除事件监听
        el.__vueClickOutside__ = clickHandler;
        document.addEventListener('click', clickHandler);
    },
    unbind(el, binding) {   // 解除事件监听
        document.removeEventListener('click', el.__vueClickOutside__);
        delete el.__vueClickOutside__;
    },
})