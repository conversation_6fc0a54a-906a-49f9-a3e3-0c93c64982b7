
import axios from 'axios'
import TokenCache from '@/utils/cache/TokenCache'
import ProcessHelper from '@/utils/helper/ProcessHelper'
import defaultSettings from '@/config/defaultSettings'
const rootUrl1 = () => {
  if (ProcessHelper.isProduction() || ProcessHelper.isPreview()) {
    return defaultSettings.publishRootUrl1
  } else {
    return defaultSettings.localRootUrl1
  }
}
export default {
  get(url, params) {
    return axios({
      method: 'get',
      url: rootUrl1() + url,
      params: params,
      headers: {
        'Authorization': 'Bearer ' + TokenCache.getToken()
      }
    }).then(res => {
      return Promise.resolve(res.data)
    })
  },
  post(url, params) {
    return axios({
      method: 'post',
      url: rootUrl1() + url,
      data: params,
      responseType: 'blob',
      headers: {
        'Authorization': 'Bearer ' + TokenCache.getToken()
      }
    })
  }
}
