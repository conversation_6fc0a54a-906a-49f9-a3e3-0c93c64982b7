<template>
  <a-layout :class="['layout', device]">
    <!-- SideMenu -->
    <a-drawer
      v-if="isMobile()"
      placement="left"
      :wrapClassName="`drawer-sider ${navTheme}`"
      :closable="false"
      :visible="collapsed"
      @close="drawerClose">
    </a-drawer>
    <side-menu
      v-if="this.isStudent !== '1'"
      mode="inline"
      :menus="menus"
      :theme="navTheme"
      :collapsed="collapsed"
      :collapsible="true"
      :canLoginOut="showFooter"></side-menu>
    <side-menu
      v-else-if="this.isTeacher === true"
      mode="inline"
      :menus="menus"
      :theme="navTheme"
      :collapsed="collapsed"
      :collapsible="true"></side-menu>
    <a-layout :class="[layoutMode, `content-width-${contentWidth}`]" :style="{ minHeight: '100vh' }">
      <global-header
        :mode="layoutMode"
        :menus="menus"
        :theme="navTheme"
        :collapsed="collapsed"
        :device="device"
        @toggle="toggle" />
      <!-- <head-of-head-navigation v-if="routeList.includes(path)"></head-of-head-navigation> -->
      <a-layout-content :style="{ height: '100%', margin: footer ? '12px 20px 0' : '0', paddingTop: fixedHeader ? '64px' : '0' }" :class="{ ha: haCss }">
        <multi-tab v-if="showCrumbs === 'Admin' || showCrumbs === '1357896248794812416'"></multi-tab>
        <transition name="page-transition">
          <router-view></router-view>
        </transition>
      </a-layout-content>

      <!-- layout footer -->
      <a-layout-footer v-if="isStudent === '1' && showFooter" class="footer-left">
        <!-- <global-footer /> -->
        <XuHuiFooter v-if="footer"></XuHuiFooter>
      </a-layout-footer>
      <div class="faIcon" v-show="faIconShow" @click="elIConBtn">
        <div>
          <h1>版权声明</h1>
          <p>上海市教师教育学院（上海市教育委员会教学研究室）拥有本网站市级教学资源的著作权，其中，参与建设的相关区教育学院、区教师进修学院、区教育发展研究院，与上海市教师教育学院（上海市教育委员会教学研究室）共同共有其参与建设部分的市级教学资源的著作权。</p>

          <p>未经著作权人的书面授权，任何单位或个人不得以任何方式使用、转载、引用、复制等，并明确禁止对本网站所属的服务器做镜像。著作权人保留追究侵权者法律责任之权利。</p>

          <p>本网站用户自行上传的资源或发布的内容，纯属提交者个人行为，与本网站立场无关。本网站对于用户自行上传的资源或发布的内容所引发的著作权纠纷或 其他纠纷，在尽到了法律要求的义务后不承担任何责任。</p>

          <p>本网站旨在促进基础教育高质量发展，并在有限的教育范围内提供用户教学使用。市级教学资源中部分图片、视频、音乐、文本等作品来源于相关专著、网络资源等，尽管已努力查询并标注来源，但未免有疏漏之处，如存在个别未标注的情形，敬请谅解！本网站教学资源中涉及的资料，如作者或权利人不希望我们在教学资源中使用其作品，请联系本网站，我们会立即将有关内容从网上移除。</p>
          <i class="el-icon-close elIconButton" @click="elIConBtn"></i>
        </div>
      </div>
      <!-- Setting Drawer (show in development mode) -->
      <!-- <setting-drawer v-if="!production"></setting-drawer> -->
    </a-layout>
  </a-layout>
</template>

<script>
import { triggerWindowResizeEvent } from '@/utils/util'
import { mapState, mapActions } from 'vuex'
import { mixin, mixinDevice } from '@/utils/mixin'
import config from '@/config/defaultSettings'

import RouteView from './RouteView'
import MultiTab from '@/components/MultiTab'
import SideMenu from '@/components/Menu/SideMenu'
import GlobalHeader from '@/components/GlobalHeader'
import headOfHeadNavigation from '@/layouts/headOfHeadNavigation'
import GlobalFooter from '@/components/GlobalFooter'
// import SettingDrawer from '@/components/SettingDrawer'
import { getAddRouter } from '@/utils/routerUtil'
import XuHuiFooter from '@/components/XuHuiFooter/XuHuiFooter'

export default {
  name: 'BasicLayout',
  mixins: [mixin, mixinDevice],
  components: {
    RouteView,
    MultiTab,
    SideMenu,
    GlobalHeader,
    GlobalFooter,
    XuHuiFooter,
    headOfHeadNavigation
    // SettingDrawer
  },
  data () {
    return {
      production: config.production,
      collapsed: false,
      menus: [],
      showCrumbs: '',
      isTeacher: false,
      isStudent: '',
      showTab: '',
      haCss: true,
      showFooter: false,
      // 版权声明是否显示
      faIconShow: false,
      footer: true,
      routeList:Object.freeze([
        '/Home/Introduce',
        '/Student/TaskCenter',
        '/Student/Fine_micro/WrongQuestionBank',
        '/Student/Fine_micro/BoutiqueMicro',
        '/Student/studentPersonalAnalysis',
      ]),
    }
  },
  computed: {
    ...mapState({
      // 动态主路由
      mainMenu: state => getAddRouter()
    }),
    path () {
      return this.$route.path
    }
  },
  watch: {
    sidebarOpened (val) {
      this.collapsed = !val
    },
    $route (to) {
      console.log('路由监听')
      if (to.path == '/Student/Exam_Student/AnswerPaper' || to.path == '/Student/Exam_Student/AnswerReady') {
        this.haCss = false
      } else {
        if (to.path === '/Student/Exam_Student/highSchoolCapture/photo' || to.path === '/AIPagesAndComponents/index') {
          this.footer = false
        } else {
          this.footer = true
          this.haCss = true
        }
      }
    }
  },
  created () {
    // 不展示底部footer
    if (['/Student/Exam_Student/highSchoolCapture/photo','/AIPagesAndComponents/index'].includes(this.$route.path)) {
      this.footer = false
    } else {
      this.footer = true
    }
    this.showTab = localStorage.getItem('role')
    this.isStudent = localStorage.getItem('isStudent')
    this.isTeacher = localStorage.getItem('isTeacher')
    this.menus = this.mainMenu.find(item => item.path === '/').children
    this.collapsed = !this.sidebarOpened
    this.showCrumbs = localStorage.getItem('UserId')
    this.Init()
    if (
      location.href.indexOf('/Student/Exam_Student/AnswerPaper') != -1 ||
      location.href.indexOf('/Student/Exam_Student/AnswerReady') != -1
    ) {
      this.haCss = false
    } else {
      this.haCss = true
    }
    // 是否不再自动提示版权声明
    // 获取班级或学生列表练习进度
    if (localStorage.getItem('copyrightNotice')) {
      this.faIconShow = false
    } else {
      if (localStorage.getItem('IsTY') > 0) {
        this.faIconShow = true
      } else {
        return false
      }
    }

  },
  mounted () {
    const userAgent = navigator.userAgent
    if (userAgent.indexOf('Edge') > -1) {
      this.$nextTick(() => {
        this.collapsed = !this.collapsed
        setTimeout(() => {
          this.collapsed = !this.collapsed
        }, 16)
      })
    }
  },
  methods: {
    Init () {
      const domain = document.domain
      this.showFooter = !(domain == 'st.eduwon.cn')
      // this.showFooter = !(domain == '192.168.110.134')
    },
    // 获取用户信息
    // getUserInfo () {
    //   this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.mobilephone }).then(res => {
    //     this.isTeacher = res.Data.Roles.some(item => {
    //       return item.RoleName === '教师'
    //     })
    //   })
    // },
    ...mapActions(['setSidebar']),
    toggle () {
      this.collapsed = !this.collapsed
      this.setSidebar(!this.collapsed)
      triggerWindowResizeEvent()
    },
    paddingCalc () {
      let left = ''
      if (this.sidebarOpened) {
        left = this.isDesktop() ? '200px' : '80px'
      } else {
        left = (this.isMobile() && '0') || (this.fixSidebar && '80px') || '0'
      }
      return left
    },
    menuSelect () {
      if (!this.isDesktop()) {
        this.collapsed = false
      }
    },
    drawerClose () {
      this.collapsed = false
    },
    // 关闭版权声明
    elIConBtn () {
      this.faIconShow = false
      localStorage.setItem('copyrightNotice', true)
    },
    // 显示版权声明
    trueShow (val) {
      this.faIconShow = val
    }
  }
}
</script>

<style lang="less">
@import url('../components/global.less');
.faIcon{
  width: 100%;
  height: 100%;
  z-index: 99;
  position: fixed;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0,0,0,0.4);
  div{
    width: 32%;
    padding: 30px 20px;
    border-radius: 6px;
    background-color: #FFFFFF;
    position: relative;
    h1{
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
    }
    p{
      text-indent: 36px;
      letter-spacing: 2px;
      font-size: 16px;
      margin: 10px;
    }
    .elIconButton{
      font-size: 20px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}
/*
 * The following styles are auto-applied to elements with
 * transition="page-transition" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the page transition by editing
 * these styles.
 */
.ha {
  // padding-left: 200px;
  //padding:0 20px;
  margin:0 auto;
}
.footer-left {
  padding-right: 190px;
}
.ant-layout-footer {
  margin-left: -200px;
}
.page-transition-enter {
  opacity: 0;
}
.ant-layout-content {
  // padding-right: 190px;

  /deep/.content {
    height: 100%;
  }
  /deep/.page-header-index-wide {
    height: 100%;
  }
  // div:nth-of-type(2) {
  //   height: 100%;
  // }
}

.page-transition-leave-active {
  opacity: 0;
}

.page-transition-enter .page-transition-container,
.page-transition-leave-active .page-transition-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

:global(.ant-layout-footer) {
  background-color: rgba(240, 242, 245) !important;
}
</style>
