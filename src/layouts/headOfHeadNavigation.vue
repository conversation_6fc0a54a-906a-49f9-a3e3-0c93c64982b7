<template>
  <div class="headerTabs" style="height: 54px;">
    <div class="tabs_left">
      <ul>
        <li v-for="item in tabsList" :key="item.ind" :class="{ 'liActive': item.ind === tabsActive }" v-show="item.level.includes(getLevel)" @click="Jump(item.ind)">{{item.label}}</li>
      </ul>
    </div>
    <div class="tabs_right">
    </div>
  </div>
</template>

<script>

export default {
  name: 'HeadOfHeadNavigation',
  components:{
  },
  data() {
    return {
      userId: '',
      teachingMaterial:[],
      chapterAll: [], // 原始数据
      chapterArray: [],
      classArray: [],
      textbookVersion: '',
      term: 3,
      chapter: [],
      classId: '',
      tabsList:[
        {
          label: '作业中心',
          ind: '1',
          level: [ '1', '2' ]
        },
        {
          label: '错题本',
          ind: '2',
          level: [ '1', '2' ]
        },
        {
          label: '名师微课',
          ind: '3',
          level: [ '1' ]
        },
        {
          label: '智能分析',
          ind: '4',
          level: [ '1' ]
        }
      ],
      tabsActive: '1'
    }
  },
  computed:{
    getLevel(){
      return localStorage.getItem('LoginSource')
    }
  },
  mounted() {
    this.Jump(sessionStorage.getItem('tabsActive') || this.tabsActive)
  },
  methods:{
  // 根据状态做跳转
    Jump(ind) {
      this.tabsActive = ind
      // 作业中心
      if(ind === '1') {
        this.$router.push({
          path:'/Home/Introduce'
        })
      }
      // 错题本
      if(ind === '2') {
        this.$router.push({
          path:'/Student/Fine_micro/WrongQuestionBank'
        })
      }
      // 名师微课
      if(ind === '3') {
        this.$router.push({
          path:'/Student/Fine_micro/BoutiqueMicro'
        })
      }
      // 智能分析
      if(ind === '4') {
        this.$router.push({
          path:'/Student/studentPersonalAnalysis',
        })
      }
    },
  }
}
</script>

<style lang="less" scoped>
.headerTabs{
  // height: 68px;
  padding: 0 16px 0 26px;
  background: #FFFFFF;
  display: flex;
  justify-content: space-between;
  .tabs_left,.tabs_right{
    display: flex;
    align-items: center;
  }
  .tabs_left{
    ul{
      height: 54px;
      background: transparent;
      border-radius: 0;
      padding: 0;
      list-style: none;
      display: flex;
      align-items: flex-end;
      font-weight: 400;
      font-size: 20px;
      color: #666666;
      li{
        width: auto;
        height: auto;
        line-height: 54px;
        text-align: center;
        border-radius: 0;
        cursor: pointer;
        padding: 0 24px;
        margin-right: 32px;
        position: relative;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
        
        &:hover {
          color: #537566;
        }
      }
      .liActive{
        background-color: transparent;
        color: #537566;
        border-bottom: 3px solid #537566;
      }
    }
  }
  .tabs_right{
    .one,.ther{
      display: flex;
      align-items: center;
      font-size: 24px;
      cursor: pointer;
      img{
        margin-right: 4px;
      }
    }
    .one{
      color: #109FFD;
      margin-right: 20px;
    }
    .ther{
      color: #547FFF;
    }
  }
}
</style>
