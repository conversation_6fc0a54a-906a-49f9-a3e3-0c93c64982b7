// eslint-disable-next-line
import { UserLayout, PageView } from '@/layouts'
import Index from '@/views/Home/Index'
// import Armacok from '@/views/RegionManage/Armacok'

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  // {
  //   path: '/Home',
  //   component: UserLayout,
  //   redirect: '/Home/Login',
  //   hidden: true,
  //   children: [
  //     {
  //       path: '/Home/Login',
  //       name: 'Login',
  //       component: () => import('@/views/Home/Login')
  //     }
  //   ]
  // },
  {
    path: '/Home',
    component: Index,
    // name: 'Index',
    redircet: '/Home/Index',
    children: [
      {
        path: '/Home/Index',
        name: 'Index',
        component: () => import('@/views/Home/Index')
      }
    ]
  },
  {
    path: '/Home/AppDown',
    name: 'AppDown',
    component: () => import('@/views/Home/AppDown')
  },
  {
    path: '/Home/UserLogin',
    name: 'UserLogin',
    component: () => import('@/views/Home/UserLogin')
  },
  // 身份选择
  {
    path: '/Home/register/IdentityCheck',
    name: 'Identity',
    component: () => import('@/views/Home/register/IdentityCheck')
  },
  // 教师注册
  {
    path: '/Home/register/TeacherRegister',
    name: 'TeacherRegister',
    component: () => import('@/views/Home/register/TeacherRegister'),
    meta: { keepAlive: true }
  },
  // 学生注册
  {
    path: '/Home/register/StudentRegister',
    name: 'StudentRegister',
    component: () => import('@/views/Home/register/StudentRegister')
  },
  // 管理员注册
  {
    path: '/Home/register/ManageRegister',
    name: 'ManageRegister',
    component: () => import('@/views/Home/register/ManageRegister')
  },
  // 注册通用
  {
    path: '/Home/register/Register',
    name: 'Register',
    component: () => import('@/views/Home/register/Register')
  },
  // 忘记密码
  {
    path: '/Home/ForgetPassword',
    name: 'ForgetPassword',
    component: () => import('@/views/Home/ForgetPassword')
  },
  {
    path: '/Question/QuestionSurvey',
    name: 'question',
    component: () => import('@/views/Question/QuestionSurvey')
  },
  // 公司首页
  {
    path: '/Index/index',
    name: 'companyIndex',
    component: () => import('@/views/Index/index')
  },
  // 资讯中心
  {
    path: '/Index/refer',
    name: 'refer',
    component: () => import('@/views/Index/refer')
  },
  // 产品方案
  {
    path: '/Index/productProgram',
    name: 'productProgram',
    component: () => import('@/views/Index/productProgram')
  },
  // 关于我们
  {
    path: '/Index/aboutUs',
    name: 'aboutUs',
    component: () => import('@/views/Index/aboutUs')
  },
  // 二维码login
  {
    path: '/Index/QRLogin',
    name: 'QRLogin',
    component: () => import('@/views/Index/QRLogin')
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/exception/404'),
    hidden: true
  },
  // {
  //   path: '/RegionManage/Armacok',
  //   name: 'armacok',
  //   component: () => import('@/views/RegionManage/Armacok')
  // },
  {
    path: '/MobileHome/Mobindex',
    name: 'Mobindex',
    component: () => import('@/views/MobileHome/Mobindex')
  },
  {
    path: '/MobileHome/Refers',
    name: 'Refers',
    component: () => import('@/views/MobileHome/Refers')
  },
  {
    path: '/MobileHome/Products',
    name: 'Products',
    component: () => import('@/views/MobileHome/Products')
  },
  {
    path: '/MobileHome/Abouts',
    name: 'Abouts',
    component: () => import('@/views/MobileHome/Abouts')
  },
  {
    path: '/MobileHome/Qrlogin',
    name: 'Qrlogin',
    component: () => import('@/views/MobileHome/Qrlogin')
  }
]
