import Vue from 'vue'
import Vuex from 'vuex'

import app from './modules/app'
import chapterStorage from '@/store/modules/chapterStorage'
// import LawReview from './modules/LawReview/index'

// import user from './modules/user'
// import permission from './modules/permission'
import getters from './getters'

Vue.use(Vuex)

export default new Vuex.Store({
  modules: {
    app,
    chapterStorage,
  },
  state: {
    versionForAutoLoginOut: 'v0.0.3',
    RoleType: 1,
    cur: 2,
    apiDocInfo: JSON.parse(localStorage.getItem('apiInfo')), // 市管理端错因详情Data
    CityParams: JSON.parse(localStorage.getItem('cityErrorInfo')), // 市管理端错因目标详情Data
    AreaInfo: JSON.parse(localStorage.getItem('AreaInfo')), // 市管理端区错因详情Data
    AreaTargetParams: JSON.parse(localStorage.getItem('AreaTargetInfo')), // 市管理端区错因目标详情Data
    EditParams: JSON.parse(localStorage.getItem('GetEditObj')), // 微课编辑Obj
    ClassAnalyParams: JSON.parse(localStorage.getItem('ClassAnaly')), // 分析报告---年级
    AreaAnalyParams: JSON.parse(localStorage.getItem('AreaAnaly')), // 分析报告---区域
    AnswerParams: JSON.parse(localStorage.getItem('AnswerPaper')),
    ExcellData: JSON.parse(localStorage.getItem('ExcellObj')),
    YearData: JSON.parse(localStorage.getItem('YearObj')),
    headerIndex: '',
    // 这里用做黄浦区学生答题限制
    limitingJobPermissions: 0
  },
  mutations: {
    setHeaderIndex (state, data) {
      // console.log(data)
      state.headerIndex = data
    },
    setRoleType (state, data) {
      state.RoleType = data
    },
    setCur (state, data) {
      state.cur = data
    },
    // 市管理端市错因详情
    setApiInfo (state, apiInfo) {
      localStorage.setItem('apiInfo', JSON.stringify(apiInfo))
      state.apiDocInfo = apiInfo
    },
    // 市管理端市错因目标详情Data
    CityTargetObj (state, TartgetInfo) {
      localStorage.setItem('cityErrorInfo', JSON.stringify(TartgetInfo))
      state.CityParams = TartgetInfo
    },
    // 市管理端区错因详情Data
    AreaApiInfo (state, DetailtInfo) {
      localStorage.setItem('AreaInfo', JSON.stringify(DetailtInfo))
      state.AreaInfo = DetailtInfo
    },
    // 市管理端区错因目标详情Data
    AreaTargetInfo (state, TargetInfo) {
      localStorage.setItem('AreaTargetInfo', JSON.stringify(TargetInfo))
      state.AreaTargetParams = TargetInfo
    },
    //
    GetEditRow (state, EditInfo) {
      localStorage.setItem('GetEditObj', JSON.stringify(EditInfo))
      state.EditParams = EditInfo
    },
    // 报告-----年级
    ClassAnalysis (state, Info) {
      localStorage.setItem('ClassAnaly', JSON.stringify(Info))
      state.ClassAnalyParams = Info
    },
    // 报告-----区域
    AreaAnalysis (state, Info) {
      localStorage.setItem('AreaAnaly', JSON.stringify(Info))
      state.AreaAnalyParams = Info
    },
    ItemAnswer (state, Info) {
      localStorage.setItem('AnswerPaper', JSON.stringify(Info))
      state.AnswerParams = Info
    },
    ExcellObj (state, Info) {
      localStorage.setItem('ExcellObj', JSON.stringify(Info))
      state.ExcellData = Info
    },
    YearInfo (state, Info) {
      // console.log(Info, 'Info')
      localStorage.setItem('YearObj', JSON.stringify(Info))
      state.YearData = state
    },
    // 这里用做黄浦区学生答题限制
    limitingJobPermissions (state, Info) {
      state.limitingJobPermissions = Info
    }
  },
  actions: {
    setRoleTypeData ({ commit, state }, data) {
      commit('setRoleType', data)
    },
    setCurData ({ commit, state }, data) {
      commit('setCur', data)
    }
  },
  getters: {
    getRoleType (state) {
      return state.RoleType
    },
    setCurData (state) {
      return state.cur
    }
  }
})
