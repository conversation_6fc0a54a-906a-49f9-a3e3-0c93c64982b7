import { get } from "lodash"

const state = {
  // useName: "sam"
  teacherList: []

}
const mutations = {
  //   updateData(state,array){
  //     state.teacherList = array;
  //   }
  SET_TEACHERLIST_DATA(_state, array) {
    _state.teacherList = array
  }

  // 教师列表
  // /Customer/CustomExercise/GetTeacherTeamList

}
const actions = {

  /*     getTeacherList({commit, state},data) {
                   commit('updateData', 'getInfo')
                }, */

  getTeacherList({ commit }, teacherList) {

    /* this.$uwonhttp.post('/Customer/CustomExercise/GetTeacherTeamList', obj).then((res) => {

                              }) */
    commit('SET_TEACHERLIST_DATA', teacherList)
    /*                   axios.get(INTERFACE.GET_CONTRACT_NUMBER).then(data => {
                            commit('SET_TEACHERLIST_DATA', {data})   // 改变state.number
                            resolve(data.data)
                          }).catch((err) => {
                            // window.Alert(err)
                          }) */
  }

}
const getters = {
  getTeacherList(state) {
    return state.teacherList
  }
}
// namespaced 属性，限定命名空间
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
