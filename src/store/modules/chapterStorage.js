/*仅供左侧章节树使用*/
import { Session } from '@/utils/storage'

// 优化后的 retValue 函数，添加空值检查
// const retValue = (key) => {
//   const chapterObj = Session.get('ChapterObj')
//   return chapterObj && chapterObj[key]
// }

// 通用方法：设置 ChapterObj 的属性
const setChapterObjValue = (state, key, payload) => {
  state.ChapterObj[key] = payload
  Session.set('ChapterObj', state.ChapterObj)
}

// 通用方法：重置 ChapterObj 的属性
const resetChapterObjValue = (state, key, defaultValue) => {
  state.ChapterObj[key] = defaultValue
  Session.set('ChapterObj', state.ChapterObj)
}

export default {
  namespaced: true,
  state: {
    ChapterObj: Session.get('ChapterObj') || {
      Year: null,
      Term: null,
      GradeId: null,
      ParentId: [],
      Ids: []
    }
  },
  mutations: {
    SET_YEAR(state, payload) {
      setChapterObjValue(state, 'Year', payload)
    },
    SET_TERM(state, payload) {
      setChapterObjValue(state, 'Term', payload)
    },
    SET_GRADEID(state, payload) {
      setChapterObjValue(state, 'GradeId', payload)
    },
    SET_PARENTID(state, payload) {
      setChapterObjValue(state, 'ParentId', payload)
    },
    SET_IDS(state, payload) {
      setChapterObjValue(state, 'Ids', payload)
    },
    RESET_YEAR(state) {
      resetChapterObjValue(state, 'Year', null)
    },
    RESET_TERM(state) {
      resetChapterObjValue(state, 'Term', null)
    },
    RESET_GRADEID(state) {
      resetChapterObjValue(state, 'GradeId', null)
    },
    RESET_PARENTID_IDS(state) {
      state.ChapterObj.ParentId = []
      state.ChapterObj.Ids = []
      Session.set('ChapterObj', state.ChapterObj)
    }
  },
  actions: {
    setYear({ commit }, payload) {
      commit('SET_YEAR', payload)
    },
    setTerm({ commit }, payload) {
      commit('SET_TERM', payload)
    },
    setGradeId({ commit }, payload) {
      commit('SET_GRADEID', payload)
    },
    setParentId({ commit }, payload) {
      commit('SET_PARENTID', payload)
    },
    setIds({ commit }, payload) {
      commit('SET_IDS', payload)
    },
    resetYear({ commit }) {
      commit('RESET_YEAR')
    },
    resetTerm({ commit }) {
      commit('RESET_TERM')
    },
    resetGradeId({ commit }) {
      commit('RESET_GRADEID')
    },
    resetParentIdAndIds({ commit }) {
      commit('RESET_PARENTID_IDS')
    }
  }
}
