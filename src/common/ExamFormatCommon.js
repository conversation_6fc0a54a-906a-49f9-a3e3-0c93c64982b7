const styleStr = `
  margin:5px 10px;
  border-bottom: 1px solid gray;
  border-radius: 4px;
  background: rgba(137, 225, 186, 0.4);
  line-height: 27px;
  font-size: 18px;
  color: #000;
  overflow: hidden;
  position: relative;
  text-align: left;
  top:20px;
  min-height: 27px;
`
// 分数填空格式化处理 暂时不用
function format_fraction(item) {
  // item-要替换的题目
  const regx = /\$\$\/frac.*?\$\$/g
  const regx1 = /\$\$\/frac\{/g
  const regx2 = /\}\{/g
  const regx3 = /\}\$\$/g
  const regxList = item.match(regx)
  if (regxList && regxList.length > 0) {
    regxList.forEach((regx_item) => {
      let fenshu = regx_item
      fenshu = fenshu.replace(
        regx1,
        `
            <div style="display:inline-block;vertical-align:middle;margin:0 8px;" >
          `
      )
      fenshu = fenshu.replace(
        regx2,
        `
            <hr style="width:100%;height:3px;background:black;margin:3px 0;position:relative;top:0px;"></hr>
          `
      )
      fenshu = fenshu.replace(
        regx3,
        `</div>
          `
      )
      item = item.replace(regx_item, fenshu)
    })
  }
  return item
}

// 空格格式化--增大
function format_space(item) {
  // item-要替换的题目
  const regx1 = /\#\#/g
  const regx2 = /\（\s{0,}\）/g // 匹配空格
  const regx3 = /(#@)/g
  const regx4 = /(#&\d+@)/g
  const inputele = ` &nbsp;&nbsp;&nbsp;&nbsp; `
  item = item.replace(regx1, inputele).replace(regx2, '（' + inputele + '）').replace(regx3, inputele).replace(regx4, inputele)
  return item
}

// flag 填空是否显示公式,只针对订正接口返回了userAnswer属性导致显示异常
// 处理填空
function format_input(item, tid, data,flag=true) {
  const regx = /\(\#\&\d+\@\)/g
  const regx1 = /\(\#\&/g
  const regx2 = /\@\)/g
  const regxList = item.match(regx)
  if (regxList && regxList.length > 0) {
    regxList.forEach((i, index) => {
      const value = i.replace(regx1, '').replace(regx2, '')
      if (data.IsFormula && data.IsFormula[index]) {
        item = item.replace(i, `
        <div style="display:inline-block">
        <div
            class="input_box"
            style='${styleStr}'
            data-type="${tid}"
            data-index="${value}"
          >
            <input type="text"  data-type="${tid}"  id="${value}" data-index="${value}"  autocomplete="off" style="width:100px;min-width:100px;max-width:600px;display: none; background: transparent;text-align:center;"  />
            <div
              style="
                  min-width: 100px;
                  text-align:center;
                "
              data-type="${tid}"
              data-index="${value}"
              type="${tid}"
              index="${value}"
              class="virtual_input"
            >
            <p data-type="${tid}"  data-index="${value}" class="virtual_input_p" style="white-space: nowrap"></p>
            </div>
          </div>
        </div>
        `)
      } else {
        //${data.UserAnswer?.length >0 ? 'disabled' : ''} 
        if(data.UserAnswer?.length >0 && flag){
          console.log(item.ShowUserAnswer)
          item = item.replace(i, `
            <input class="input" disabled value=${data.UserAnswer}  autocomplete="off"  type="text" style="width:100px;min-width: 100px;max-width:600px;border: 0px; border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
            data-type="${tid}"  id="${value}" data-index="${value}"/>
            `)
        }else{
          item = item.replace(i, `
            <input class="input"  autocomplete="off"  type="text" style="width:100px;min-width: 100px;max-width:600px;border: 0px; border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
            data-type="${tid}"  id="${value}" data-index="${value}"/>
            `)
        }
        
      }
    })
  }
  return item
}

function replaceInputTags(html, replacementText) {  
  // 使用正则表达式匹配所有<input>标签，包括自闭合和闭合标签形式  
  // 注意：这个正则表达式可能无法完美处理所有情况，特别是嵌套的HTML或特殊字符  
  const regex = /<input\s+[^>]*?>/gi;  
  // 使用replace方法和正则表达式来替换所有匹配的<input>标签  
  return html.replace(regex, replacementText);  
} 

// 单题提交的填空渲染
function SingleFormatInput(item, tid, data) {
  const regx = /\(\#\&\d+\@\)/g
  const regx1 = /\(\#\&/g
  const regx2 = /\@\)/g
  const regxList = item.match(regx)
  if (regxList && regxList.length > 0) {
    regxList.forEach((i, index) => {
      const value = i.replace(regx1, '').replace(regx2, '')
      // console.log(value)
      if (data.IsFormula && data.IsFormula[index]) {
        console.log(item,'=========>item')
        item = item.replace(i, `
        <div style="display:inline-block">
        <div
            class="input_box"
            style='${styleStr}'
            data-type="${tid}"
            data-index="${value}"
          >
            <input type="text"  data-type="${tid}"  id="${value}" data-index="${value}"  autocomplete="off" style="width:100px;min-width:100px;max-width:600px;display: none; background: transparent;text-align:center;"  />
            <div
              style="
                  min-width: 100px;
                  text-align:center;
                "
              data-type="${tid}"
              data-index="${value}"
              type="${tid}"
              index="${value}"
              class="virtual_input"
            >
            <p data-type="${tid}"  data-index="${value}" class="virtual_input_p" style="white-space: nowrap"></p>
            </div>
          </div>
        </div>
        `)
      } else {
        // console.log(data)
        //${data.UserAnswer?.length >0 ? 'disabled' : ''} 
        if(data.UserAnswer?.length >0){
          // console.log(item.ShowUserAnswer)
          item = item.replace(i, `
            <input class="input" disabled  autocomplete="off"  type="text" style="width:100px;min-width: 100px;max-width:600px;border: 0px; border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
            data-type="${tid}"  id="${value}" data-index="${value}"/>
            `)
        }else{
          item = item.replace(i, `
            <input class="input"  autocomplete="off"  type="text" style="width:100px;min-width: 100px;max-width:600px;border: 0px; border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
            data-type="${tid}"  id="${value}" data-index="${value}"/>
            `)
        }
        
      }
    })
  }else{
    const regex = /<input\s+[^>]*?>/gi;  
    console.log(data.Title.match(regex))
    const inputList = data.Title.match(regex)
    if(data.UserAnswer?.length >0){
      for(let i=0;i<inputList.length;i++){
        const str = `<input 
        class="input" 
        disabled 
        autocomplete="off"  
        type="text" 
        style="
          width:100px;
          min-width: 100px;
          max-width:600px;
          border: 0px; 
          border-radius:4px; 
          color:#000; 
          text-align:center; 
          border-bottom:1px solid grey;
          background:rgba(137, 225, 186, 0.4);
          margin-bottom: 10px;
          margin:0 2px 0 2px;"
          data-type="${tid}"  
          id="${i}" 
          data-index="${i}"
          />
          `
        item = replaceInputTags(data.Title,str)
      }
    }
  }
  return item
}
// 处理公式题答案
function formula_Answer(val, list) {
  let title = []
  console.log(val)
  console.log(list)

  val.map((res, index) => {
    if (res) {
      title.push(`<span class="math-tex">\\(${list[index]}\\)</span>`)
    } else {
      title.push(`${list[index]}`)
    }
  })
  return title.join('|')
}

function str2utf8(str) {
  return eval("'" + encodeURI(str).replace(/%/gm, '\\x') + "'")
}

function filterZuheti(data) {
  let zuheti = []
  let ids = []
  data.forEach((item, index) => {
    if (item.TypeId == '73' || item.ItemTypeId == 73) {
      item.oldIndex = index //记录位置 提交答案时候按这个记录插回原处
      zuheti.push(item)
      ids.push(item.ItemId)
    }
  })
  ids.forEach(item=>{
    let i = data.findIndex(s=>s.ItemId == item)
    data.splice(i,1)
  })

  return zuheti
}




export default {
  formula_Answer,
  format_fraction,
  format_space,
  format_input,
  str2utf8,
  filterZuheti,
  SingleFormatInput
}