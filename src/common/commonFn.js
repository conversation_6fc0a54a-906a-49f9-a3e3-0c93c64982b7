/**
 * 翻译公式专用 比如二分之一 MathJax对象不可少
 * **/
export default {
  // Vue.js的插件应当有一个公开方法 install。这个方法的第一个参数是 Vue 构造器，第二个参数是一个可选的选项对象。
  //自定义的方法最好以$开头
  install: function (Vue) {
    Vue.prototype.$MathJaxToDom = () => {
      window.MathJax = {
        options: {
          processHtmlClass: ['question-title-img', 'math-tex'],
          ignoreHtmlClass: '.*'
        },
        tex: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)']
          ]
        },
        svg: {
          fontCache: 'global'
        }
      }
      var script = document.createElement('script')
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML'
      script.async = true
      document.head.appendChild(script)
    }

    Vue.prototype.$formatDate = (date, fmt) => {
      if (!date) return
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (date.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      let o = {
        "M+": addZero(date.getMonth() + 1),
        "d+": addZero(date.getDate()),
        "h+": date.getHours(),
        "m+": date.getMinutes(),
        "s+": date.getSeconds()
      };
      for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
          let str = o[k] + "";
          fmt = fmt.replace(RegExp.$1, str);
        }
      }
      function addZero(num) {
        return num >= 10 ? num : "0" + num;
      }
      return fmt;
    }

  }
}