﻿<template>
  <a-modal :title="title" width="40%" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="loading">
      <a-form :form="form">
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Name', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['PhoneNum', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="QQ" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['CustomerQQ', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="上传二维码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-upload name="file" :multiple="true" :action="uploadPictureUrl" :headers="headers" @change="handleChange" v-decorator="['QRCode']">
            <a-button>
              <a-icon type="upload" />选择图片导入 </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item label="负责学校" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-select allowClear mode="multiple" v-decorator="['SchoolId', { rules: [{ required: false }] }]" :filterOption="filterOption">
            <a-select-option v-for="item in SchoolData" :key="item.Id">{{ item.SchoolName }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      entity: {},
      title: '',
      uploadPictureUrl: '',
      PictureUrl: '',
      headers: {
        authorization: 'authorization-text'
      },
      SchoolData: [],
      id: ''
    }
  },
  methods: {
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    openForm(id, title) {
      this.id = id
      // 参数赋值
      this.title = title || '编辑表单'
      this.loading = true
      this.uploadPictureUrl = `${this.$rootUrl}/Paper/Exam_MicroLesson/PictureFile`
      // 组件初始化
      this.init()

      // 编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()

          this.$http.post('/CustomerSchool/CustomerService/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.visible = true
      this.$http.post('/School/Exam_School/GetSchools', { async: '!@#123' }).then(resJson => {
        if (resJson.Success) {
          this.SchoolData = resJson.Data
        }
      })
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        // 校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())
          this.entity.QRCode = this.PictureUrl
          this.entity.Id = this.id
          this.loading = true
          this.$http.post('/CustomerSchool/CustomerService/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    handleChange(arg1, label, extra) {
      if (arg1.file.status === 'done') {
        if (arg1.file.response.Success) {
          this.PictureUrl = arg1.file.response.Data // this.$router.go(0)
        } else {
          this.$message.error(arg1.file.response.Msg, 2)
        }
      }
    }
  }
}
</script>
