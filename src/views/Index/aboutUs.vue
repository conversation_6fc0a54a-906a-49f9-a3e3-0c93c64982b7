<template>
  <div>
      <Head></Head>
      <div class="top">
            <div class="t-left">
                <div class="aboutUs"> 关于我们</div>
                <!-- <img src="@/assets/cpmpany/矩形@2x.png" alt="" style="width:60px"> -->
                <div class="text">
                    有我公司是一家着力于基础教育领域的高科技公司，拥有多项发明专利和产品著作权证书，长期进行学科教学各环节的优化探索研究。公司具备优秀的技术团队，有优秀的一线师资团队进行业务指导，与各区中小学校保有长期合作。
                </div>
            </div>
            <div class="t-right">
                <div class="companyTitle">公司简介</div> 
                <!-- <img src="@/assets/cpmpany/位图@2x.png" alt=""> -->
            </div>
      </div>
      <div class="bottom">
          <p>企业文化</p>
          <div>
                有我是种精神：拒绝自我，追求忘我无我，力争在团队和用户心中实现“有我”。
                有我是种态度：勇于接受挑战，面对困难与挑战，敢于大声说：有我！
                有我是信念与追求 ：英文名eduwon，追求与教育部门一起走向成功，实现教学“有我”更精彩。
          </div>
      </div>
      <Foot class="foot"></Foot>
  </div>
</template>
D:\vscode-demo\EduExamManageWebSiteScore\EduExamManageWebSiteScore\src\Uwoo.Web\src
<script>
import Head from '@/components/companyHead/companyHead'
import Foot from '@/components/XuHuiFooter/XuHuiFooter'
export default {
    data(){
        return{
        }
    },
    components:{
        Foot,
        Head,
    }
   
}
</script>

<style lang="less" scoped>
.top{
    height: 600px;
    padding-top: 120px;
    position: relative;
}
.t-left{
    display: inline-block;
    width: 20%;
    vertical-align: middle;
    height: 60%;
    position: relative;
    margin-left: 20%;
}
.t-right{
    position: relative;
    display: inline-block;
    width: 40%;
    margin-left: 5%;
    vertical-align: middle;
    height: 100%;
    img{
        width: 671px;
        height: 260px;
    }
}
.aboutUs{
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #272727;
}
.text{
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #818181;
}
.companyTitle{
    font-size: 32px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #292929;
    margin-bottom: 20px;
}
.bottom{
    height: 300px;
    background-color: #F8FAF9;
    position: relative;
    p{
        position: relative;
        font-size: 32px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #292929;
        text-align: center;
        top: 40px;
    }
    div{
        top: 50px;
        position: relative;
        width: 35%;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #818181;
        margin-left: 50%;
        transform: translateX(-50%);
    }
}
.foot{
    margin: 0px;
}
</style>