<template>
  <div class="main">
    <!-- <div class="all">
      <img class="leftImg" src="@/assets/cpmpany/二维码登录/信息.png" alt="">
      <div class="right">
        <p style="text-align:center;margin:30px 0px;font-size: 18px;font-family: PingFangSC-Medium, PingFang SC;font-weight: 500;color: #404040;">手机扫码&nbsp;&nbsp;安全登录</p>
        <p class="helpP">
          <span class="saoma">使用专课专练APP扫码登录</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          <span class="help">扫码帮助</span>&nbsp;
          <span class="hoverImg">
            <img src="@/assets/cpmpany/二维码登录/编组.png" alt="">
            <img class="showHideImg" src="@/assets/cpmpany/二维码登录/弹窗／扫码帮助.png" alt="">
          </span>
        </p>
        <div class="QRLogin">
          <img :src="'data:image/png;base64,' + qrUrl" />
        </div>
        <div style="text-align:center">
          <span class="bottom loginb">
            <QrLogin></QrLogin>
          </span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import QrLogin from '@/components/companyHead/ComQRLogin'
import TokenCache from '@/utils/cache/TokenCache'
export default {
  data() {
    return {
      qrUrl: '',
      newCode: ''
    }
  },
  components: {
    QrLogin
  },
  created() {
    this.makeGRLogin()
  },
  mounted() {},
  methods: {
    makeGRLogin() {
      this.$uwonhttp.get('/User/Login/NewLogiQR', {}).then(resJson => {
        if (resJson.data.Success) {
          this.qrUrl = resJson.data.Data.result
          this.newCode = resJson.data.Data.code
          this.initWebSocket()
        } else {
        }
      })
    },
    login(url, mobilephone, passwordCheck) {
      this.$http.post(url, { userName: mobilephone, password: passwordCheck }).then(res => {
        if (res.Success) {
          // 保存token
          TokenCache.setToken(res.Data)
          // if (values && values.remember) {
          //   localStorage.setItem('passWord', passwordCheck)
          // } else {
          //   localStorage.removeItem('mobilePhone')
          //   localStorage.removeItem('passWord')
          // }
          localStorage.setItem('mobilePhone', mobilephone)
          this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: mobilephone }).then(res => {
            localStorage.setItem('SchoolId', res.Data.User.SchoolId)
            console.log('QRLogin.vue')
            if (!res.Success) {
              this.format = true
              this.PromptText = res.Msg
            }
            if (!res.Data.Roles) {
              this.isStudent = '1'
              localStorage.setItem('isStudent', this.isStudent)
              localStorage.removeItem('isTeacher')
            } else {
              this.isTeacher = res.Data.Roles.some(item => {
                return item.RoleName === '教师'
              })
              localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
              localStorage.setItem('isTeacher', this.isTeacher)
              localStorage.removeItem('isStudent')
              localStorage.setItem('role', res.Data.Roles[0].Id)
            }
            if (res.Data.Roles) {
              if (
                res.Data.Roles.some(item => {
                  return item.RoleName === '校领导'
                })
              ) {
                this.isSchoolHeader = true
                localStorage.setItem('isSchoolHeader', this.isSchoolHeader)
                localStorage.removeItem('isTeacher')
                localStorage.removeItem('isTeachingStaff')
              }
            }
            this.id = res.Data.UserType
            localStorage.setItem('Id', res.Data.UserType)
            localStorage.setItem('UserId', res.Data.User.Id)
            localStorage.setItem('IsVip', res.Data.IsVip)
            this.$message.success('登录成功')
            this.$router.push({
              path: '/Home/Introduce',
              query: {
                id: this.id
              }
            })
          })
        } else {
          this.loginErrorIsShow = true
          this.loginErrText = res.Msg
        }
      })
    },
    initWebSocket() {
      // 初始化weosocket
      // const baseurl = this.$rootUrl1.replace('http://', '').replace('https://', '')
      // var scheme = document.location.protocol === 'https:' ? 'wss' : 'ws'
      const wsuri = 'wss://appapi.eduwon.cn/' + 'ws/qrscanlogin?code=' + this.newCode

      this.websock = new WebSocket(wsuri)
      this.websock.onmessage = this.websocketonmessage
      this.websock.onopen = this.websocketonopen
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    websocketonopen() {
      // 连接建立之后执行send方法发送数据
      // const actions = { scanlogin: 'conn' }
      // this.websocketsend(JSON.stringify(actions))
    },
    websocketonerror() {
      // this.initWebSocket()
    },
    websocketonmessage(e) {
      const redata = JSON.parse(e.data)
      // 数据接收
      if (redata.state === 1) {
        TokenCache.setToken(redata.token)
        if (redata.userType === 1) {
          // 教师登录
          this.login('/Base_Manage/Home/TeacherScanQrLogin', redata.phone, redata.t)
        } else if (redata.userType === 2) {
          // 学生登录
          this.login('/Base_Manage/Home/TeacherScanQrLogin', redata.phone, redata.t)
        }
      }
    },
    websocketsend(Data) {
      // 数据发送
      this.websock.send(Data)
    },
    websocketclose(e) {
      // this.initWebSocket()
    }
  }
}
</script>

<style lang="less" scoped>
.main {
  height: 100%;
  // background-image: url('../../assets/cpmpany/二维码登录/背景.png');
  background-repeat: no-repeat;
}
.all {
  width: 1000px;
  height: 700px;
  position: relative;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.leftImg {
  height: 100%;
  width: 35%;
  display: inline-block;
  vertical-align: middle;
}
.right {
  padding: 15px 15px;
  background: white;
  width: 65%;
  height: 100%;
  display: inline-block;
  vertical-align: middle;
}
.helpP {
  text-align: center;
  .saoma {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #787878;
  }
  .help {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #38c2cc;
  }
}
.bottom {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #787878;
}
.bottom {
  margin: 0px 10%;
  cursor: pointer;
}
.hoverImg:hover .showHideImg {
  display: inline-block;
}
.showHideImg {
  width: 600px;
  position: absolute;
  transform: translate(-6%, -47%);
  display: none;
}
.QRLogin {
  padding-top: 70px;
  height: 60%;
  text-align: center;
}
</style>
