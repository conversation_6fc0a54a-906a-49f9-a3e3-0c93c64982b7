<template>
  <div id="web">
    <Head></Head>
    <div class="left">
      <div class="raius"></div>
      <div class="raius"></div>
      <div class="main">
        <div class="title">专课专练&nbsp;&nbsp;实证教学天天见</div>
        <div class="title">注重教学管理 为教师赋能</div>
        <div class="title">
          <Login></Login>
        </div>
        <div class="title">
          专课专练是专门为学校教学管理打造的在线教育品牌，运用认知科学和智能技术为师生提供在线微课，智能练习，问题解决，学情分析等多样化的智能教育服务。我们长期致力于系统性地推动教育均衡发展，让每个孩子都能获得健康成长
        </div>
        <!-- <div class="title" @click="toQRLogin">扫码登录</div> -->
      </div>
    </div>
    <div class="image-d">
      <div class="image-inner"></div>
    </div>

    <div class="bottom">
      <!-- <span class="botImg"> <img src="@/assets/cpmpany/编组 2.png" alt="" /></span> -->
      <div class="bot-div">
        <span class="topTitle">侧重个性化辅导</span>
        <span class="underTitle"
          >基于数据管理和归因分析的逻辑将主题库资源按多级章节知识点体系重新归纳整理，生成海量题库资源，帮助学生自我提高。</span
        >
      </div>
    </div>
    <div class="bottom">
      <!-- <span class="botImg" id="second"> <img src="@/assets/cpmpany/编组 2.png" alt="" /></span> -->
      <div class="bot-div">
        <span class="topTitle">数据采集学情图表</span>
        <span class="underTitle"
          >通过教师与学生的行为数据智能生成学情图表，校级管理者，教研组长和学生家长可清晰查看教师与学生的教研与学习成果。</span
        >
      </div>
    </div>
    <div class="bottom">
      <!-- <span class="botImg"> <img src="@/assets/cpmpany/编组 2.png" alt="" /></span> -->
      <div class="bot-div">
        <span class="topTitle">学生试卷智能批改</span>
        <span class="underTitle">基于智能识别系统，精准识别学生答题答案，系统自动判别试卷，减少教师批改作业时间。</span>
      </div>
    </div>
    <div class="bottom">
      <div class="radius"></div>
      <!-- <span class="botImg" id="fouth"> <img src="@/assets/cpmpany/编组 2.png" alt="" /></span> -->
      <div class="bot-div">
        <span class="topTitle">数据致力精准教学管理</span>
        <span class="underTitle"
          >通过教师与学生的行为数据智能生成学习报告，学生家长，教师，校级管理者可对照学生薄弱点实施精准教研。</span
        >
      </div>
    </div>
    <Foot class="foot"></Foot>
  </div>
</template>
<script>
import Head from '@/components/companyHead/companyHead'
import Foot from '@/components/XuHuiFooter/XuHuiFooter'
import Login from '@/components/companyHead/login'
export default {
  data() {
    return {
      xh: '',
    }
  },
  components: {
    Foot,
    Head,
    Login,
  },
  created() {
    this.xh = localStorage.getItem('xh')
  },
  mounted() {
    window.addEventListener('resize', () => {})
    if (this._isMobile()) {
      this.$router.push('/MobileHome/Index')
    } else {
      this.$router.push('/Index/index')
    }
  },
  methods: {
    toQRLogin() {
      this.$router.push('/Index/QRLogin')
    },
    _isMobile() {
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|Macintosh)/i
      )
      return flag
    },
  },
}
</script>

<style lang="less" scoped>
#web {
  overflow: hidden;
}

.left {
  display: inline-block;
  width: 50%;
  height: 607px;
  position: relative;
}
.main {
  width: 60%;
  height: 440px;
  position: absolute;
  right: 0px;
  top: 150px;
  padding: 0px 20px 0px 0px;
}
.title:nth-child(1) {
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #262626;
}
.title:nth-child(2) {
  margin-top: 22px;
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #262626;
}
.title:nth-child(3) {
  margin-top: 10px;
  width: 130px;
  height: 28px;
  background: #0099fa;
  box-shadow: 0px 10px 20px 0px #0099fa33;
  border-radius: 15px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: white;
  text-align: center;
  line-height: 28px;
  cursor: pointer;
}
.title:nth-child(3) p {
  letter-spacing: 1px;
  width: 100%;
  transform: scale(0.8, 0.8);
}
.title:nth-child(4) {
  margin-top: 30px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #a9a9a9;
  width: 70%;
}
.title:nth-child(5) {
  margin-top: 14px;
  font-size: 20px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: white;
  width: 152px;
  height: 40px;
  background: linear-gradient(135deg, #bba7fc 0%, #866ef8 100%);
  box-shadow: 0px 11px 20px 0px #9079f880;
  border-radius: 38px;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
}
.raius:nth-child(1) {
  position: absolute;
  width: 28px;
  height: 28px;
  background: #ffbe55;
  top: 10%;
  right: 3%;
  border-radius: 50%;
}
.raius:nth-child(2) {
  position: absolute;
  width: 117px;
  height: 117px;
  top: 24%;
  background: #8fe25b;
  border-radius: 50%;
  transform: translateX(-50%);
}

.image-d {
  // background-image: url('../../assets/cpmpany/编组 14.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 50%;
  height: 607px;
  position: relative;
  display: inline-block;
  vertical-align: top;
}
.image-inner {
  position: relative;
  height: 472px;
  width: 80%;
  top: 15%;
  left: -18%;
  // background-image: url('../../assets/cpmpany/编组 12.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.bottom {
  width: 50%;
  display: inline-block;
  position: relative;
  height: 140px;
}

.radius {
  position: absolute;
  width: 81px;
  height: 81px;
  top: 0;
  right: 0px;
  background: #ffbe55;
  border-radius: 50%;
  transform: translate(50%, -50%);
}
.botImg {
  display: inline-block;
  height: 100%;
  position: relative;
  vertical-align: middle;
  line-height: 80px;
  margin-left: 40%;
  margin-right: 10px;
}

.bot-div {
  vertical-align: middle;
  display: inline-block;
  width: 400px;
  height: 100%;
}
.topTitle {
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #292929;
  display: inline-block;
  height: 30%;
  position: relative;
  line-height: 40px;
}
.underTitle {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  position: relative;
  display: inline-block;
  height: 70%;
  width: 400px;
}
.foot {
  margin-top: 10px;
}
.span {
  display: inline-block;
  height: 100%;
  margin: 0px 20px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: white;
  cursor: pointer;
}
.clicked {
  color: black;
}
#second,
#fouth {
  margin-left: 17%;
}
</style>
