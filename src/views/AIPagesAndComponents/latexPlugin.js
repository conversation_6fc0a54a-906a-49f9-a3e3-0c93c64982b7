export default function(md) {
  // 保护行内公式 \(...\)
  md.inline.ruler.before('escape', 'latex_inline', function(state, silent) {
    const start = state.pos
    const src = state.src

    // 检查是否以 \( 开头
    if (src.charCodeAt(start) !== 0x5C /* \ */) return false
    if (src.charCodeAt(start + 1) !== 0x28 /* ( */) return false

    // 查找匹配的 \)
    let pos = start + 2
    let found = false
    while (pos < src.length - 1) {
      if (src.charCodeAt(pos) === 0x5C /* \ */ && src.charCodeAt(pos + 1) === 0x29 /* ) */) {
        found = true
        pos += 2
        break
      }
      pos++
    }

    if (!found) return false

    if (!silent) {
      const token = state.push('latex_inline', '', 0)
      token.content = src.slice(start, pos)
      token.markup = '\\('
    }

    state.pos = pos
    return true
  })

  // 保护方括号数学公式 [...]
  md.inline.ruler.before('escape', 'math_bracket', function(state, silent) {
    const start = state.pos
    const src = state.src

    // 检查是否以 [ 开头
    if (src.charCodeAt(start) !== 0x5B /* [ */) return false

    // 查找匹配的 ]，并检查内容是否包含数学符号
    let pos = start + 1
    let found = false
    let content = ''

    while (pos < src.length) {
      if (src.charCodeAt(pos) === 0x5D /* ] */) {
        content = src.slice(start + 1, pos)
        // 检查内容是否包含数学符号（LaTeX命令、等号、分数等）
        if (/\\[a-zA-Z]+|[=+\-*/^_{}()]|\s*[a-zA-Z]+[_^]/.test(content)) {
          found = true
          pos += 1
          break
        } else {
          return false // 不是数学公式，让其他规则处理
        }
      }
      pos++
    }

    if (!found) return false

    if (!silent) {
      const token = state.push('math_bracket', '', 0)
      token.content = '[' + content + ']'
      token.markup = '['
    }

    state.pos = pos
    return true
  })

  // 保护块级公式 \[...\]
  md.block.ruler.before('paragraph', 'latex_block', function(state, start, end, silent) {
    const pos = state.bMarks[start] + state.tShift[start]
    const max = state.eMarks[start]
    const src = state.src

    // 检查行是否以 \[ 开头
    if (pos + 2 > max) return false
    if (src.charCodeAt(pos) !== 0x5C /* \ */) return false
    if (src.charCodeAt(pos + 1) !== 0x5B /* [ */) return false

    // 查找匹配的 \]，可能跨多行
    let lineNum = start
    let found = false
    let content = ''

    while (lineNum < end) {
      const lineStart = lineNum === start ? pos : state.bMarks[lineNum] + state.tShift[lineNum]
      const lineEnd = state.eMarks[lineNum]
      const lineContent = src.slice(lineStart, lineEnd)

      content += (lineNum === start ? lineContent : '\n' + lineContent)

      // 检查是否包含 \]
      const endMatch = lineContent.indexOf('\\]')
      if (endMatch !== -1) {
        found = true
        content = content.slice(0, content.lastIndexOf('\\]') + 2)
        break
      }
      lineNum++
    }

    if (!found) return false

    if (!silent) {
      const token = state.push('latex_block', '', 0)
      token.content = content
      token.markup = '\\['
      token.map = [start, lineNum + 1]
    }

    state.line = lineNum + 1
    return true
  })

  // 渲染器 - 直接输出LaTeX内容，不做任何转换
  md.renderer.rules.latex_inline = function(tokens, idx) {
    return tokens[idx].content
  }

  md.renderer.rules.latex_block = function(tokens, idx) {
    return tokens[idx].content
  }

  md.renderer.rules.math_bracket = function(tokens, idx) {
    return tokens[idx].content
  }
}
