<template>
  <div class="backColor" v-show="domShow">
    <div class="imgDongHua">
      <img class="userLog" src="@/assets/AIImg/4mIcon.gif" />
<!--      <img class="userLog" v-show="!imgState" src="@/assets/AIImg/4mIconJ.png" />-->
      <div class="boFang">
        <img class="play" src="@/assets/AIImg/4mFa.gif" />
<!--        <img class="pause" v-show="!imgState" src="@/assets/AIImg/4mTi.png" />-->
      </div>
      <i v-show="!imgState" class="el-icon-circle-close" @click="hide"></i>
    </div>
  </div>
</template>

<script>
import PCMPlayer from 'pcm-player'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import ProcessHelper from "@/utils/helper/ProcessHelper"
import defaultSettings from "@/config/defaultSettings"
export default {
  name: "audioDialog",
  data(){
    return {
      domShow: false,
      imgState: false,
    }
  },
  methods:{
    show(txt){
      this.initWebSocket(txt)
      this.domShow = true
    },
    hide(){
      this.domShow = false
    },
    initWebSocket(txt) {
      const that = this
      const player = new PCMPlayer({
        inputCodec: 'Int16', // 采样位数
        channels: 1,  // 通道
        sampleRate: 24000,  // 采样率 需要与后端采样率对齐
        flushingTime: 1000,  // pcm刷新间隔
        fftSize: 2048,
        onstatechange: (node, event, type) => {
          console.log(type)
        },  // 播放状态变化事件
        onended: (node, event) => {
          const time = setTimeout(()=>{
            that.imgState = false
            // console.log('播放结束')
            clearTimeout(time)
          },11000)
        },  // 播放结束事件
      })
      const ctrl = new AbortController();
      // fetchEventSource(`http://192.168.110.56:5004/AI/AI/AITextChangeVoiceSSE`,{
        fetchEventSource(`${this.rootUrl1()}/AI/AI/AITextChangeVoiceSSE`,{
        signal: ctrl.signal,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({Text:txt}),
        openWhenHidden: true,
        onopen:(e) => {
          console.log('建立链接')
          player.volume(1)
          that.imgState = true
        },
        onmessage: (msg) => {
          console.log(msg)
          let audioData = that.base64ToArrayBuffer(msg.data)// 创建实例
          player.feed(audioData) // 将PCM音频数据写入pcm-player
        },
        onclose() {
          console.log("SSE接收信息 断开");
          // 监听后端断开，前端主动断开
          ctrl.abort();
        },
        onerror(err) {
          console.log("SSE接收信息 异常");
          console.log(err)
          that.imgState = false
          throw err;
          //必须throw才能停止
        },
      })
    },
    base64ToArrayBuffer(base64) {
      const binaryString = window.atob(base64);
      const len = binaryString.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes;
    },
    // 接口api根地址
    rootUrl1() {
      if (ProcessHelper.isProduction() || ProcessHelper.isPreview()) {
        return defaultSettings.publishRootUrl1
      } else {
        return defaultSettings.localRootUrl1
      }
    },
  }
}
</script>

<style lang="less" scoped>
.backColor{
  width: 100vw;
  height: 100vh;
  background-color: rgba(0,0,0,0.3);
  position: fixed;
  top: 0;
  left: 0;
  .imgDongHua{
    width: 30%;
    margin: 8% auto 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    .userLog{
      width: 500px;
      height: 500px;
    }
    .boFang{
      .play{

      }
      .pause{
        margin-top: 60px;
      }
    }
    i{
      font-size: 40px;
      color: #FFFFFF;
      cursor: pointer;
      position: absolute;
      top: 0;
      right: 0;
    }
  }
}
</style>