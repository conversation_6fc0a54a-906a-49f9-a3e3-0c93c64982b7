<template>
  <div :class="['conversation','ipad_all_height','pc_all_height']">
    <div class="_top">
      <el-button icon="el-icon-back" class="ipad_font_size24" @click="$router.go(-1)">退出对话</el-button>
      <div class="_con_btn">
        <p
          v-for="item in teacherList"
          :key="item"
          class="ipad_font_size24"
          :style="{color: identityTab === item ? '#36ADFD' : '#5C5C5C', backgroundColor: identityTab === item ? '#36ADFD26' : '#FFFFFF' }"
          @click="identityTab = item"
        >{{ item }}</p>
      </div>
      <div></div>
    </div>
    <div :class="scrollClasses" ref="scrollClasses">
      <div class="scol_left">
<!--    暂停这里处理教师切换头像替换，数据清空    -->
        <img class="userImg" v-show="identityTab === '晓有老师'" src="@/assets/AIImg/girl.png" />
        <img class="userImg" v-show="identityTab === '晓我老师'" src="@/assets/AIImg/man.png" />
        <div class="_neiRong ipad_font_size26" style="max-width: 91.4%;">
          HI,{{UserName}}同学！我是{{ identityTab }}。<br/>
          如果在学习过程中有任何问题，都可以随时向我提问哦！
        </div>
      </div>
      <div v-if="rooteList.length">
        <div class="forList" v-for="(item,ind) in rooteList" :key="ind">
          <div class="scol_right" v-show="item.Ask !== ''">
            <div class="_neiRong ipad_font_size26" style="max-width: 84.4%;">
              <div class="userImgList">
                <div v-for="(ite,ins) in item.imgUrl" :key="ins+'txImg'" class="_ImgList">
                  <el-image
                    v-if="ite !== ''"
                    :src="item.imgUrl[ins]"
                    :preview-src-list="item.imgUrl"
                    fit="cover">
                  </el-image>
                </div>
              </div>
  <!--            <div v-html="item.Ask" v-katex></div>-->
              <div v-if="item.AskInfo">{{ item.AskInfo.AskText }}</div>
            </div>
            <img class="userImg" :src="userPhone" />
          </div>
          <div class="scol_left">
            <img class="userImg" v-show="identityTab === '晓有老师' && item.Answer !==''" src="@/assets/AIImg/girl.png" />
            <img class="userImg" v-show="identityTab === '晓我老师' && item.Answer !==''" src="@/assets/AIImg/man.png" />
            <div v-show="item.Answer !==''">
              <div class="_neiRong ipad_font_size26" style="max-width: 91.4%;">
                <!-- <vueTypewriter class="tl" ref="typewriter" :interval="20" :listContent="item.Answer" @typewriterEnd="typewriterEnd"> -->
                  <div class="comments">
                    <div v-html="md.render(cleanMathFormulas(item.Answer))" v-katex="katexOptions"></div>
                  </div>
                <!-- </vueTypewriter> -->
                <div class="tear_btn">
                  <el-button class="ipad_font_size24" type="text" icon="el-icon-refresh-right" @click="reGenerate(ind)">重新生成</el-button>
                  <p>|</p>
                  <el-button class="ipad_font_size24" type="text" icon="el-icon-mic" @click="AITextChangeVoice(item.Answer)">语音朗读</el-button>
                </div>
              </div>
            </div>
  <!--&lt;!&ndash;            <div class="xiangGuan">&ndash;&gt;-->
  <!--&lt;!&ndash;              <el-button>相关链接</el-button>&ndash;&gt;-->
  <!--&lt;!&ndash;              <el-button>相关链接</el-button>&ndash;&gt;-->
  <!--&lt;!&ndash;            </div>&ndash;&gt;-->
          </div>
        </div>
      </div>

      <div v-show="loading"><img style="width: 60px" src="@/assets/AIImg/aiLoding.gif"/></div>
    </div>
    <div class="sessionSending">
      <div v-if="userConversation.FileUrls.length > 0" class="imgList">
        <!--    根据上传图片判定中部内容区域height高度，图片最多上传3张    -->
        <div v-for="(sre,ind) in userConversation.FileUrls" :key="ind" class="_img">
          <img :src="sre" />
          <i class="el-icon-error" @click="delImg(ind)"></i>
        </div>
      </div>
      <el-input class="ipad_font_size24" type="textarea" :rows="2" resize="none" v-model="userConversation.Message" placeholder="请输入问题" @keydown.enter.native="enterEvent($event)"></el-input>
      <div class="importBtn">
        <el-upload
          class="upload-demo"
          ref="upload"
          action="#"
          :http-request="customizationRequest"
          :show-file-list="false"
          accept=".jpg,.jpeg,.png,.JPG,.JPEG"
          list-type="picture">
<!--          <el-button class="upFile ipad_font_size24" icon="el-icon-picture-outline" @click="handleCustomUpload">上传图片</el-button>-->
          <el-button class="upFile ipad_font_size24" icon="el-icon-picture-outline">上传图片</el-button>
        </el-upload>
        <el-button class="send ipad_font_size24" :disabled="sendBtn" icon="el-icon-s-promotion" @click="faSong">发送</el-button>
<!--        <el-button class="send ipad_font_size24" icon="el-icon-s-promotion" @click="start">发送</el-button>-->
      </div>
    </div>
<!--  弹窗音频  -->
    <audioDialog ref="audioDialog"></audioDialog>
  </div>
</template>

<script>
import defaultSettings from '@/config/defaultSettings'
import ProcessHelper from '@/utils/helper/ProcessHelper'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { Session } from '@/utils/storage';
// import MarkdownIt from 'markdown-it'
// import axios from "axios"
import audioDialog from "@/views/AIPagesAndComponents/component/audioDialog"
import vueTypewriter from "@/views/AIPagesAndComponents/component/vue-typewriter"
import latexPlugin from './latexPlugin'
const markdownit = require('markdown-it')
const md = markdownit({
  html: true,
  linkify: true,
  typographer: true
})
// 公式解析
md.use(latexPlugin)
export default {
  name: "index",
  data() {
    return{
      identityTab: '晓有老师',
      teacherList: ['晓有老师','晓我老师'],
      userPhone: localStorage.getItem('userPhoto'),
      // questionContent: '',
      // 用户名称
      UserName: localStorage.getItem('userName'),
      // 会话暂存数组
      rooteList: [],
      loading: false,
      // 用户信息发送入参
      userConversation:{
        StudentId: localStorage.getItem('UserId'),
        PaperId: '',
        ItemId: '',
        Message: '',
        InitialMessage: Session.get('AITEXT'),
        FileUrls:[]
      },
      // 打字机文本数组
      AitheAnswer: '',
      sendBtn: false,
      md: Object.freeze(md),
      // KaTeX 配置选项，支持方括号分隔符
      katexOptions: {
        delimiters: [
          {left: "$$", right: "$$", display: true},
          {left: "$", right: "$", display: false},
          {left: "\\(", right: "\\)", display: false},
          {left: "\\[", right: "\\]", display: true},
          {left: "[", right: "]", display: true}  // 添加方括号支持
        ]
      },
    }
  },
  components:{
    audioDialog,
    vueTypewriter
  },
  computed:{
    scrollClasses() {
      const hasImageUrl = this.userConversation.FileUrls.length > 0
      return [
        'scroll',
        hasImageUrl ? 'pc_img_cont_height' : 'pc_cont_height',
        hasImageUrl ? 'ipad_img_cont_height' : 'ipad_cont_height'
      ];
    }
  },
  created() {
    const { PaperId, ItemId } = this.$route.query
    this.userConversation.PaperId = PaperId
    this.userConversation.ItemId = ItemId
    this.GetAIDialogueContentRecord()
  },
  // 监听路由离开当前组件
  beforeRouteLeave(to, from, next) {
    if(to.path !== '/AIPagesAndComponents/index'){
      Session.remove('AITEXT')
    }
    next()
  },
  methods:{
    // 上传事件
    // handleCustomUpload(){
    //   // 判断是否有权限
    //   navigator.permissions.query({ name: 'camera' }).then(permission => {
    //     if (permission.state === 'denied') {
    //       console.log('请在浏览器设置中启用摄像头权限！');
    //     }
    //   });
    //   // 手动使用原生上传
    //   const input = document.createElement('input');
    //   input.type = 'file';
    //   input.accept = 'image/*';
    //   input.capture = 'camera'; // 或 'environment' 后置摄像头
    //   input.onchange = (e) => {
    //     const file = e.target.files[0];
    //     if (file) this.$refs.upload.upload(file);
    //   };
    //   input.click();
    // },
    // 文本域换行
    enterEvent(event){
      if(!event.shiftKey){
        event.preventDefault();
        this.faSong();
      }else{
        this.textarea += '\n';
      }
    },
    typewriterEnd(){
      this.sendBtn = false
    },
    // 重新生成
    reGenerate(ind){
      const { Ask, imgUrl } = this.rooteList[ind]
      this.rooteList.push({ Ask, Answer: '', imgUrl ,AskInfo:{ AskText: Ask, Files: '' }})
      this.userConversation.Message = Ask
      this.userConversation.FileUrls = imgUrl
      this.scrollToBottom()
      this.sendSSE(this.userConversation)
    },
    // 发送事件
    faSong(){
      const Num = this.rooteList.length
      if(Num == 20) {
        this.open()
        this.reset()
        return false
      }
      if(this.userConversation.Message == '') return false
      const { Message, FileUrls } = this.userConversation
      this.rooteList.push({ Ask: Message, Answer: '', imgUrl: FileUrls,AskInfo:{ AskText: Message, Files: '' } })
      
      this.sendSSE(this.userConversation)
    },
    // 图片上传
    async customizationRequest(file){
      const imgNum = this.userConversation.FileUrls.length
      if(imgNum == 3) return this.$message.warning('最多上传3张图片')
      const forData = new FormData()
      forData.append('file', file.file)
      const { data:res } = await this.$uwonhttp.post('/AI/AI/AIDialogueUploadFile', forData)
      // 文件链接
      this.userConversation.FileUrls.push(res.thumbUrl)
    },
    // 图片删除
    delImg(ind) {
      this.userConversation.FileUrls.splice(ind, 1)
    },
    // SSE请求方法
    sendSSE(reqList) {
      this.reset()
      const that = this
      that.loading = true
      const ctrl = new AbortController();
      // fetchEventSource(`http://192.168.110.56:5004/AI/AI/AIDialogue`,{
      fetchEventSource(`${this.rootUrl1()}/AI/AI/StudentItemAnalysisDialogue`,{
        signal: ctrl.signal,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reqList),
        openWhenHidden: true,
        onopen:(e) => {
          console.log('建立链接')
          that.AitheAnswer = ''
        },
        onmessage: (msg) => {
          console.log(msg.data)
           that.loading = false
          if (msg.data == "[DONE]") {
            ctrl.abort();
          } else {
            const { content } = JSON.parse(msg.data)
            if(content !== '[DONE]'){
              that.AitheAnswer += content
              that.rooteList[that.rooteList.length - 1].Answer = that.AitheAnswer
            }
          }
          this.$nextTick(()=>{
            this.scrollToBottom()
          })
        },
        onclose() {
          console.log("SSE接收信息 断开");
          that.rooteList[that.rooteList.length - 1].Answer = that.AitheAnswer
          that.loading = false
          that.sendBtn = true
          that.reset()
          // that.GetAIDialogueContentRecord()
          // 监听后端断开，前端主动断开
          ctrl.abort();
        },
        onerror(err) {
          // console.log("SSE接收信息 异常");
          that.loading = false
          that.sendBtn = false
          throw err;
          //必须throw才能停止
        },
      })
    },
    // 获取对话历史接口
    async GetAIDialogueContentRecord(){
      const { PaperId, ItemId, StudentId } = this.userConversation
      const reqData = {
        PaperId,
        ItemId,
        StudentId,
        Type: 3,
        PageIndex: 1,
        PageSize: 20
      }
      const { data: res } = await this.$uwonhttp.post('/AI/AI/GetAIDialogueContentRecord', reqData)
      if(res.Items.length){
        const newData = []
        res.Items.forEach(item => {
          // 使用正则截取item.Ask字符串中的http链接
          const httpUrl = item.Ask.match(/http[s]?:\/\/[^\s]+/g);
          if (httpUrl) {
            // 将http链接替换为图片链接
            item.Ask = item.Ask.replace(httpUrl[0], '');
          }
          // // 把字符串httpUrl[0]转为数组
          newData.push({
            Ask: item.Ask,
            Answer: item.Answer,
            AskInfo: item.AskInfo,
            imgUrl: httpUrl ? httpUrl[0].split('、') : []
          })
        })
        this.rooteList = newData
      }
      // console.log(this.rooteList)
      this.scrollToBottom()
    },
    // 上传图片接口
    rootUrl1() {
      if (ProcessHelper.isProduction() || ProcessHelper.isPreview()) {
        return defaultSettings.publishRootUrl1
      } else {
        return defaultSettings.localRootUrl1
      }
    },
    open() {
      this.$confirm('如果还没有明白，咨询下你的任课老师哦', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return false
      }).catch(() => {
        return false
      });
    },
    // 音频接口
    AITextChangeVoice(conten){
      this.$refs.audioDialog.show(conten)
    },
    // 处理文本格式
    // renderedMarkdown(txt) {
    //   // console.log(txt)
    //   txt = txt.replace(/<br>/g, '\n');
    //   return txt
    // },
    // 滚动
    scrollToBottom() {
      this.$nextTick(() => {
        const conversation = this.$refs.scrollClasses;
        conversation.scrollTop = conversation.scrollHeight;
      });
    },
    reset(){
      this.$nextTick(() => {
        this.userConversation.Message = ''
        this.userConversation.FileUrls = []
      })
    },
    // 清理数学公式中的<br>标签、\n换行符和空格
    cleanMathFormulas(text) {
      if (!text) return text

      let result = text

      // 第一步：清理公式前后的<br>标签（在处理公式内容之前）
      result = result
        // 清理公式前的<br>标签和空白
        .replace(/<br\s*\/?>\s*(\$\$|\\\[|\\\(|\$)/gi, '$1')
        .replace(/\s*<br\s*\/?>\s*(\$\$|\\\[|\\\(|\$)/gi, ' $1')
        // 清理公式后的<br>标签和空白
        .replace(/(\$\$|\\\]|\\\)|\$)\s*<br\s*\/?>\s*/gi, '$1 ')
        .replace(/(\$\$|\\\]|\\\)|\$)<br\s*\/?>/gi, '$1')

      // 第二步：处理公式内容
      // 定义数学公式的分隔符模式，按优先级排序（长的在前，避免冲突）
      const mathDelimiters = [
        { start: '$$', end: '$$', display: true },
        { start: '\\[', end: '\\]', display: true },
        { start: '\\(', end: '\\)', display: false },
        { start: '$', end: '$', display: false }
      ]

      // 对每种数学公式分隔符进行处理
      mathDelimiters.forEach(delimiter => {
        const { start, end } = delimiter

        // 使用更精确的正则表达式来匹配公式
        const formulaRegex = new RegExp(
          this.escapeRegExp(start) + '([\\s\\S]*?)' + this.escapeRegExp(end),
          'g'
        )

        result = result.replace(formulaRegex, (match, mathContent) => {
          // 清理数学公式内容中的干扰标签和字符
          const cleanedMath = mathContent
            .replace(/<br\s*\/?>/gi, '') // 移除<br>标签
            .replace(/<\/?\s*p\s*>/gi, '') // 移除<p>标签
            .replace(/\n/g, '') // 移除换行符
            .replace(/\r/g, '') // 移除回车符
            .replace(/\s+/g, ' ') // 将多个空格合并为一个
            .trim() // 移除首尾空白

          return start + cleanedMath + end
        })
      })

      // 第三步：最终清理，处理可能遗漏的情况
      result = result
        // 再次清理公式前后可能残留的<br>标签
        .replace(/<br\s*\/?>\s*(\$\$|\\\[|\\\(|\$)/gi, '$1')
        .replace(/(\$\$|\\\]|\\\)|\$)\s*<br\s*\/?>/gi, '$1')
        // 清理连续的<br>标签
        .replace(/(<br\s*\/?>){2,}/gi, '<br>')
        // 清理多余的空白
        .replace(/\s{2,}/g, ' ')

      return result
    },
    // 转义正则表达式特殊字符
    escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    }
  }
}
</script>

<style lang="less" scoped>
.conversation{
  height: 93vh;
  background-image: url("~@/assets/AIImg/background.png");
  padding: 15px 0 20px;
  ._top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20%;
    margin-bottom: 15px;
    .el-button{
      //height: 34px;
      padding: 8px 12px;
      border-radius: 60px;
      color: #5D55EB;
      font-weight: bold;
    }
    ._con_btn{
      width: 260px;
      height: 40px;
      background: #FFFFFF;
      border-radius: 100px;
      display: flex;
      padding: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: #cccccc 0px 2px 4px;
      p{
        width: 49%;
        height: 100%;
        line-height: 32px;
        font-size: 15px;
        color: #5C5C5C;
        text-align: center;
        border-radius: 100px;
      }
    }
  }
  .scroll{
    //height: 70vh;
    padding: 14px 20%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    //.forList{
      .scol_left,.scol_right{
        display: flex;
        margin: 16px 0;
        .userImg{
          width: 70px;
          height: 70px;
        }
        ._neiRong{
          //max-width: 91.4%;
          //max-width: 84.4%;
          padding: 10px 14px;
          background: #FFFFFF;
          border-radius: 6px;
          font-size: 18px;
          /deep/ul{
            list-style: inside;
          }
          .tear_btn{
            display: flex;
            align-items: center;
            .el-button{
              color: #5D55EB;
            }
            p{
              color: #5D55EB;
              margin: 0 10px;
            }
          }
          img{
            max-width: 100%;
          }
          .userImgList{
            display: flex;
            flex-wrap: wrap;
            ._ImgList{
              margin-right: 10px;
              .el-image{
                width: 240px;
                height: 240px
              }
            }
          }
        }
        .xiangGuan{
          margin-top: 12px;
          display: flex;
          .el-button{
            font-size: 15px;
            padding: 8px 14px;
            color: #5D55EB;
          }
        }
      }
      .scol_left{
        width: 100%;
        justify-content: left;
        .userImg{
          margin-right: 20px;
        }
      }
      .scol_right{
        width: 100%;
        justify-content: right;
        .userImg{
          border-radius: 50%;
          margin-left: 20px;
        }
      }
    //}
  }
  // 美化滚动条
  .scroll::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .scroll::-webkit-scrollbar-track {
    width: 6px;
    background: rgba(#101F1C, 0.1);
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  .scroll::-webkit-scrollbar-thumb {
    background-color: rgba(144,147,153,.5);
    background-clip: padding-box;
    min-height: 28px;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
    transition: background-color .3s;
    cursor: pointer;
  }

  .scroll::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144,147,153,.3);
  }
  .sessionSending{
    background: #FFFFFF;
    border-radius: 10px;
    padding: 8px;
    margin: 14px 20% 0;
    .imgList{
      display: flex;
      ._img{
        width: 60px;
        height: 60px;
        position: relative;
        margin-right: 16px;
        img{
          width: 100%;
          height: 100%;
          border-radius: 3px;
          object-fit: cover;
        }
        i{
          font-size: 14px;
          position: absolute;
          right: -6px;
          top: -6px;
          color: #FF0000FF;
          cursor: pointer;
        }
      }
    }
    .importBtn{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .upFile,.send{
        padding: 10px 14px;
        border-radius: 100px;
        border-color: #FFFFFF;
      }
      .upFile{
        background: #F7F7FAFF;
      }
      .send{
        color: #FFFFFF;
        background: linear-gradient(90deg, #36C0FD7F 0%, #3678FD7F 50%, #9A36FD7F 100%);
      }
    }
    /deep/.el-textarea__inner {
      border: none;
    }
    }
}
</style>