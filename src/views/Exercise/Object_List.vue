<template>
  <div class="Page_box">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" size="medium">
      <el-row>
        <el-col :span="4">
          <el-form-item label="区域">
            <el-select v-model="formInline.AreaId" placeholder="区域" @change="GetAreaVal">
              <el-option v-for="item in AreaOptions" :key="item.Id" :label="item.Name" :value="item.Id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="学校">
            <el-select v-model="formInline.SchoolId" placeholder="学校" @change="GetSchollVal">
              <el-option v-for="item in SchollOptions" :key="item.Id" :label="item.SchoolName" :value="item.Id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="年级">
            <el-select v-model="formInline.Grade" placeholder="年级" @change="GetGradeVal">
              <el-option v-for="item in GradeOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="班级">
            <el-select v-model="formInline.ClassId" placeholder="班级" @change="GetCalssVal">
              <el-option v-for="item in ClassOptions" :key="item.ClassId" :label="item.ClassName" :value="item.ClassId"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="学生名单">
            <el-select v-model="formInline.Rea_id" placeholder="学生名单" @change="GetRea_idVal">
              <el-option v-for="item in ReaOptions" :key="item.Id" :label="item.RealName" :value="item.Id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="查询类别">
            <el-select v-model="formInline.condition" placeholder="查询类别" @change="GetconditionVal">
              <el-option v-for="item in ConditionOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="formInline.keyword" placeholder="关键字"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button type="info" @click="onReset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table ref="singleTable" :data="tableData" v-loading="loading" :header-cell-style="{ background:'#F5F7F9',color:'#717171','text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
      <el-table-column type="index" label="序号" width="50">
      </el-table-column>
      <el-table-column prop="AreaName" label="区域">
      </el-table-column>
      <el-table-column prop="SchoolName" label="学校">
      </el-table-column>
      <el-table-column prop="Grade" label="年级">
        <template slot-scope="scope">
          <span>{{scope.row.Grade}}年级</span>
        </template>
      </el-table-column>
      <el-table-column prop="ClassName" label="班级">
      </el-table-column>
      <el-table-column prop="StudentName" label="学生">
      </el-table-column>
      <el-table-column prop="PaperTitle" label="对应练习名称" width="150">
      </el-table-column>
      <el-table-column prop="ItemTitle" label="原题题干" width="300">
        <template slot-scope="scope">
          <span v-html="scope.row.ItemTitle"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ChapterTarget" label="原题题目编码" width="300">
        <template slot-scope="scope">
          <span v-html="scope.row.ChapterTarget"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ItemAdaptiveTitle" label="推送练习题干" width="300">
        <template slot-scope="scope">
          <span v-html="scope.row.ItemAdaptiveTitle"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ItemAdaptiveChapterTarget" label="推送练习编码" width="300">
        <template slot-scope="scope">
          <span v-html="scope.row.ItemAdaptiveChapterTarget"></span>
        </template>
      </el-table-column>
      <el-table-column prop="PushCount" label="推送次数">
        <template slot-scope="scope">
          <span>{{scope.row.PushCount}}次</span>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInline.PageIndex" :page-size="formInline.PageRows" :page-sizes="[8]"
        layout="total, sizes, prev, pager, next, jumper" :total="totalNum">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.userId = localStorage.getItem('UserId')
    // this.GetAreaListBy()
    this.init()
  },
  data() {
    return {
      userId: '',
      loading: false,
      AreaOptions: [],
      SchollOptions: [],
      ClassOptions: [],
      ReaOptions: [],
      GradeOptions: [
        {
          value: '3',
          label: '三年级'
        },
        {
          value: '4',
          label: '四年级'
        }
      ],
      ConditionOptions: [
        {
          value: 'PaperTitle',
          label: '对应练习名称'
        },
        {
          value: 'ItemTitle',
          label: '原题题干'
        },
        {
          value: 'ItemAdaptiveTitle',
          label: '推送练习题干'
        }
      ],
      formInline: {
        AreaId: '',
        SchoolId: '',
        Grade: '',
        ClassId: '',
        Rea_id: '',
        condition: '',
        keyword: '',
        PageIndex: 1,
        PageRows: 8
      },
      totalNum: 0,
      tableData: []
    }
  },
  components: {},
  mounted() {},
  watch: {},
  computed: {},

  methods: {
    async init() {
      await this.GetAreaListBy()
      await this.onSubmit()
    },

    //获取当前登录人所负责的区 -目前使用的区
    async GetAreaListBy() {
      let params = {
        userId: this.userId
      }
      const res = await this.$uwonhttp.post('/Area/Base/GetAreaListBy', params)
      if (res.data.Success) {
        this.AreaOptions = res.data.Data
        this.formInline.AreaId = res.data.Data[0].Id
        this.GetSchoolsByAreaId()
      }
    },

    GetAreaVal(val) {
      this.formInline = {}
      this.formInline.AreaId = val
      this.GetSchoolsByAreaId()
    },

    // ​根据区获取所属学校
    async GetSchoolsByAreaId() {
      let params = {
        areaId: this.formInline.AreaId,
        schoolType: 2
      }
      const res = await this.$uwonhttp.post('/Area/Base/GetSchoolsByAreaId', params)
      if (res.data.Success) {
        this.SchollOptions = res.data.Data
      }
    },

    GetSchollVal(val) {
      this.formInline.SchoolId = val
      this.GetClassListBySchoolGrade()
    },

    GetGradeVal(val) {
      this.formInline.Grade = val
      this.GetClassListBySchoolGrade()
    },

    //根据学校年级获取班级 (2021届之前)
    async GetClassListBySchoolGrade() {
      let params = {
        schoolId: this.formInline.SchoolId,
        grade: this.formInline.Grade
      }
      const res = await this.$uwonhttp.post('/Class/ClassManager/GetClassListBySchoolGrade', params)
      if (res.data.Success) {
        this.ClassOptions = res.data.Data
        console.log(this.ClassOptions, '--------')
      }
    },

    GetCalssVal(val) {
      this.formInline.ClassId = val
      this.GetStduentByClassId()
    },

    //获取学生列表 by classid
    async GetStduentByClassId() {
      let params = {
        classId: this.formInline.ClassId
      }
      const res = await this.$http.post('/Student/Exam_Student/GetStduentByClassId', params)
      if (res.Success) {
        this.ReaOptions = res.Data
      }
    },

    GetRea_idVal(val) {
      this.formInline.Rea_id = val
    },

    GetconditionVal(val) {
      this.formInline.condition = val
    },

    handleSizeChange(val) {
      this.formInline.PageRows = val
    },

    handleCurrentChange(val) {
      this.formInline.PageIndex = val
    },

    async onSubmit() {
      this.loading = true
      let params = {
        ...this.formInline
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAdaptiveStudentList', params)
      if (res.data.Success) {
        this.loading = false
        this.tableData = res.data.Data
        this.totalNum = res.data.Total
      }
    },

    onReset() {
      this.formInline = {}
      this.init()
    }
  }
}
</script>
<style lang="less" scoped>
.Page_box {
  width: 100%;
  padding: 23px;
  background: #ffff;
  /deep/.el-pagination {
    text-align: center;
    margin-top: 20px;
  }
}
</style>    
