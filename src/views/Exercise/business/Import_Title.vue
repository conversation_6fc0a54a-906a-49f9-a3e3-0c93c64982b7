<template>
  <div>
    <el-dialog title="导入题目" :visible.sync="dialogVisible" width="30%" :close-on-click-modal="false">
      <el-form label-width="100px" class="demo-ruleForm">
        <el-form-item label="导入试卷" prop="pass">
          <el-upload class="upload-demo" :action="uploadWordUrl" :auto-upload='true' multiple :limit="3" :before-upload="beforeUploadFile" :file-list="fileList" :on-success='SuccessUpload'>
            <div class="Upload_box">
              <span>选择word文件导入</span>
            </div>
            <div slot="tip" class="el-upload__tip">只能上传word 文件</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
  },
  data() {
    return {
      fileList: [],
      dialogVisible: false
    }
  },
  components: {},
  mounted() {},
  watch: {},

  computed: {
    uploadWordUrl() {
      return `${this.$rootUrl1}/ExamItemAdaptive/ExamItemAdaptive/ItemAdaptiveWord`
    }
  },

  methods: {
    Init(row) {},

    open() {
      this.dialogVisible = true
    },

    beforeUploadFile(file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (extension !== 'docx') {
        this.$message.warning('只能上传后缀是.docx的文件')
        return false
      }
    },

    SuccessUpload(response, file, fileList) {
      if (response.Success) {
        this.$message({
          message: '导入成功',
          type: 'success'
        })
        this.dialogVisible = false
        this.$parent.GetItemAdaptiveDataList()
      } else {
        this.$message.error(response.Msg)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.Upload_box {
  width: 200px;
  border-radius: 5px;
  border: 1px solid #ccc;
  span {
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #989898;
  }
}
/deep/.el-select {
  width: 80%;
}
</style>    

