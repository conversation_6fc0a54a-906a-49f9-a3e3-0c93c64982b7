<template>
  <div class="Page_content">
    <el-drawer title="编辑题目" :visible.sync="drawer" direction="rtl" custom-class="demo-drawer" ref="drawer" size="80%">
      <!-- :before-close="handleClose" -->
      <div class="demo-drawer__content">
        <div class="drawer_Up">
          <div class="Up_left">
            <div class="Edit_box">
              <span>题干</span>
              <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="300" v-model="Row_Title"></TinyMceEditor>
            </div>
            <div class="Edit_box">
              <span>解析</span>
              <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="300" v-model="Row_Analysis"></TinyMceEditor>
            </div>
          </div>
          <div class="Up_right">

            <!-- 填空 -->
            <div class="Edit_box" v-if="this.Row_TypeId=='填空题'">
              <span>答案设置</span>
              <br />
              <el-input v-model="Row_Answer" :disabled="true"></el-input>
              <el-button class='Add_btn' @click="Add_synonymousBtn">添加同义</el-button>
              <!-- 同义答案 -->
              <div class="Input_box" v-for="(item,index) in Sy_nonymAnswer" :key="index" v-show="show_synonym">
                <el-input type="text" v-model="item.Content"></el-input>
              </div>
              <div class="Table_Btn" v-show="show_synonym">
                <span style="font-size: 18px;cursor: pointer;" @click="Add_Input"><i class="el-icon-circle-plus-outline"></i>添加一行</span>
                <span style="font-size: 18px;margin-left: 20px;cursor: pointer;" @click="Remove_Input"><i class="el-icon-remove-outline"></i>删除一行</span>
              </div>
            </div>

            <!-- 单选  判断 -->
            <div class="Edit_box" v-if="this.Row_TypeId=='单项选择题' || this.Row_TypeId=='判断题'">
              <span>选项设置</span>
              <br />
              <div class=" Radio_blank" v-for="Options_Answer in Options_Answer" :key="Options_Answer.id">
                <span class="options">
                  <a-radio-group @change="handleBlank" v-model="Blank_val">
                    <a-radio :value="Options_Answer.Option">{{ Options_Answer.Option }} 选项
                    </a-radio>
                  </a-radio-group>
                </span>
                <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="100" v-model="Options_Answer.Content"></TinyMceEditor>
              </div>
            </div>

            <!-- 多选题 -->
            <div class="Edit_box" v-if="this.Row_TypeId=='多项选择题'">
              <span>选项设置</span>
              <br />
              <div class="Radio_blank" v-for="Options_Check in Options_Check" :key="Options_Check.id">
                <span class="options">
                  <el-checkbox-group v-model="checked_Answer">
                    <el-checkbox :label="Options_Check.Option">{{ Options_Check.Option }}</el-checkbox>
                  </el-checkbox-group>
                </span>
                <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="100" v-model="Options_Check.Content"></TinyMceEditor>
              </div>
            </div>

            <div class="Table_Btn" v-if="this.Row_TypeId =='单项选择题' || this.Row_TypeId=='主观题' || this.Row_TypeId=='多项选择题'">
              <span style="font-size: 18px;cursor: pointer;" @click="AddOptions"><i class="el-icon-circle-plus-outline"></i>添加一行</span>
              <span style="font-size: 18px;margin-left: 20px;cursor: pointer;" @click="RemoveOptions"><i class="el-icon-remove-outline"></i>删除一行</span>
            </div>
          </div>
        </div>
      </div>
      <div class="demo-drawer__footer">
        <el-button @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="Save_Submit">确 定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import TinyMceEditor from '@/components/TinyMCE/tinyMce'
import WangEdit from '@/components/WangEditor/WangEditor'
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      Row_Title: '',
      Row_Analysis: '',
      Row_Answer: '',
      Options_Answer: [],
      Options_Check: [],
      Save_Options: [],
      Row_TypeId: '',
      Blank_val: '',
      checked_Answer: [],
      Add_RadioList: ['', '', ''],
      show_synonym: false,
      Row_Obj: {},
      Sy_nonymAnswer: [] //同义答案
    }
  },
  components: {
    TinyMceEditor,
    WangEdit
  },
  mounted() {},
  watch: {},
  computed: {},

  methods: {
    async Init(row) {
      this.Row_TypeId = row.TypeId
      this.Row_Obj = row
      await this.GetAnalysisDataByItemId(row.Id)
      await this.GetItemAdaptiveTheData(row.Id)
      await this.GetAnswerDataByItemId(row.Id)
      await this.GetOptionsByItemId(row.Id)
      await this.GetMoreAnswer(row.Id)
      if (this.Sy_nonymAnswer.length == 0) {
        this.show_synonym = false
      } else {
        this.show_synonym = true
      }
    },

    handleBlank(e) {
      this.Row_Answer = e.target.value
    },

    AddOptions() {
      switch (this.Options_Answer.length) {
        case 0:
          this.Options_Answer.push({
            Option: 'A',
            Content: ''
          })
          break
        case 1:
          this.Options_Answer.push({
            Option: 'B',
            Content: ''
          })
          break
        case 2:
          this.Options_Answer.push({
            Option: 'C',
            Content: ''
          })
          break
        case 3:
          this.Options_Answer.push({
            Option: 'D',
            Content: ''
          })
          break
        case 4:
          this.Options_Answer.push({
            Option: 'E',
            Content: ''
          })
          break
        case 5:
          this.Options_Answer.push({
            Option: 'F',
            Content: ''
          })
          break
        case 6:
          this.Options_Answer.push({
            Option: 'G',
            Content: ''
          })
          break
        default:
          this.$message.success('最多加到G哦')
      }
      // 填空增加
      this.Add_RadioList.push('')
    },

    RemoveOptions() {
      this.Options_Answer.pop()
      this.Add_RadioList.pop()
    },

    //添加同意
    Add_synonymousBtn() {
      this.show_synonym = true
      this.Sy_nonymAnswer.push({ Content: '' })
    },

    //添加一行 同义答案
    Add_Input() {
      this.Sy_nonymAnswer.push({ Content: '' })
    },

    //删除一行 同义答案
    Remove_Input() {
      this.Sy_nonymAnswer.pop()
    },

    open() {
      this.drawer = true
    },

    //获取题干
    async GetItemAdaptiveTheData(id) {
      let params = {
        Id: id
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAdaptiveTheData', params)
      if (res.data.Success) {
        this.Row_Title = res.data.Data.Title
        const reg = /(#&\d+@)/g
        const inputele = ' '
        const stem = this.Row_Title.replace(reg, inputele)
        this.Row_Title = stem
      }
    },

    //获取解析
    async GetAnalysisDataByItemId(id) {
      let params = {
        itemId: id
      }
      const res = await this.$uwonhttp.post(
        '/ExamItemAdaptive/ExamItemAdaptiveOptiAnswer/GetAnalysisDataByItemId',
        params
      )
      if (res.data.Success) {
        this.Row_Analysis = res.data.Data.Content
      }
    },

    //获取答案
    async GetAnswerDataByItemId(id) {
      let params = {
        itemId: id
      }
      const res = await this.$uwonhttp.post(
        '/ExamItemAdaptive/ExamItemAdaptiveOptiAnswer/GetAnswerDataByItemId',
        params
      )
      if (res.data.Success) {
        if (this.Row_TypeId == '填空题') {
          this.Row_Answer = res.data.Data.Content
        }
        if (this.Row_TypeId == '判断题' || this.Row_TypeId == '单项选择题') {
          this.Blank_val = res.data.Data.Content
        }
        if (this.Row_TypeId == '多项选择题') {
          const Check_Content = res.data.Data.Content
          const Replace_Check = Check_Content.replace(/\|/g, '')
          const Check_Arr = Replace_Check.split(',')
          const New_Arr = Check_Arr.filter(s => {
            return s
          })
          this.checked_Answer = New_Arr
        }
      }
    },

    //获取自适应题选项
    async GetOptionsByItemId(id) {
      let params = {
        // itemId: '1471760467838935040'
        itemId: id
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptiveOptiAnswer/GetOptionsByItemId', params)
      if (res.data.Success) {
        this.Options_Answer = res.data.Data
        this.Options_Check = res.data.Data
      }
    },

    //获取更多答案
    async GetMoreAnswer(id) {
      let params = {
        itemId: id
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetMoreAnswer', params)
      if (res.data.Success) {
        this.Sy_nonymAnswer = res.data.Data
      }
    },

    //保存
    async Save_Submit() {
      const Obj = this.Row_Obj
      if (Obj.TypeId == '填空题') {
        this.Save_Options = []
        if (this.show_synonym == 'false') {
          this.Sy_nonymAnswer = []
        } else {
        }
      }
      this.Save_Options = this.Options_Answer.map(val => {
        return {
          option: val.Option,
          Content: val.Content
        }
      })
      let params = {
        Title: this.Row_Title, //新建
        GradeTerm: Obj.GradeId + '_' + Obj.Term, //新建
        TypeId: Obj.typeId, //新建
        Answer: this.Row_Answer, //新建
        Analysis: this.Row_Analysis, //新建
        Deleted: Obj.Deleted,
        Id: Obj.Id,
        Status: Obj.Status,
        Chapter: Obj.Chapter, //新建
        CreatorId: Obj.CreatorId, //新建 ---userid
        Examine: Obj.Examine, //新建
        GradeId: Obj.GradeId,
        ItemType: Obj.ItemType, //新建  单项选择 1  多项 2
        Level: Obj.Level, //新建
        Md5: Obj.Md5,
        PlateTypeId: Obj.PlateTypeId, //新建
        ItemAttribute: Obj.ItemAttribute, //新建
        Options: this.Save_Options,
        otherAnswer: this.Sy_nonymAnswer
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/SaveItemInfo', params)
      if (res.data.Success) {
        this.$message({
          message: '编辑成功',
          type: 'success'
        })
        this.drawer = false
        this.$parent.GetItemAdaptiveDataList()
      } else {
        this.drawer = true
        this.$message.error('保存失败')
      }
    },

    cancelForm() {
      this.drawer = false
    },
    handleClose(done) {}
  }
}
</script>
<style lang="less" scoped>
.drawer_Up {
  display: flex;
  padding: 10px;
  justify-content: space-between;
  .Up_left {
    width: 50%;
    padding: 10px;
    background: #ffffff;
  }
  .Up_right {
    width: 49%;
    padding: 10px;
    background: #ffffff;
  }
}
.Edit_box {
  margin-bottom: 40px;
  span {
    font-size: 22px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #292929;
  }
  .Radio_blank {
    margin-bottom: 15px;
    /deep/.ant-radio + * {
      font-size: 16px;
      color: #000;
    }
  }
  /deep/.el-input {
    width: 70%;
    height: 45px;
    color: #888888;
    font-size: 19px;
  }
  /deep/.el-input__inner {
    background-color: rgba(137, 225, 186, 0.5);
    background: rgba(137, 225, 186, 0.5);
    border-color: rgba(137, 225, 186, 0.5);
  }
  .Add_btn {
    background: #d1b57f;
    color: #fff;
    width: 120px;
    border-color: #d1b57f;
    font-size: 15px;
    margin-left: 30px;
    line-height: 14px;
  }
  .Input_box {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
/deep/.el-drawer__header {
  font-size: 23px;
  padding: 10px;
  margin-bottom: 0;
  border-bottom: 1px solid #e8e8e8;
}
/deep/.el-dialog__close {
  display: none;
}
/deep/ .el-drawer__body {
  overflow-y: scroll;

  margin-bottom: 50px;
}
/deep/.demo-drawer__footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px;
  text-align: right;
  background-color: white;
}
</style>  


