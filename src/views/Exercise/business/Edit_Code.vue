<template>
  <div>
    <el-dialog title="编辑编码" :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false">
      <el-form ref="form" :label-position="labelPosition" label-width="100px">
        <el-form-item label="年级">
          <el-select v-model="Grade_Name" placeholder="年级" @change="GetGrade">
            <el-option v-for="item in ClassOptions" :key="item.GradeId" :label="item.GradeName" :value="{ value: item.GradeId, label: item.GradeName}"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标编码">
          <el-cascader v-model="ChapterTarget" :key="cascaderKey" :options="GetTargetListOption" placeholder="请选择错因编码" @change="getChangeChapter"></el-cascader>
        </el-form-item>
        <el-form-item label="学习水平">
          <el-select v-model="Examine" placeholder="请选择学习水平" @change="GetExamine">
            <el-option v-for="item in GetExamineOptions" :key="item.Id" :label="item.Code+'-'+item.ExamineName" :value="{ value: item.Id, label: item.Code+'-'+item.ExamineName}">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select v-model="Level" placeholder="请选择难度" @change="GetItemLevel" clearable>
            <el-option v-for="item in GetItemLevelOptions" :key="item.levelId" :label="item.LevelName" :value="{ value: item.levelId, label: item.LevelName}">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容领域">
          <el-select v-model="PlateType" placeholder="请选择内容领域" @change="GetPlateType">
            <el-option v-for="item in GetPlateTypeOptions" :key="item.id" :label="item.id+'-'+item.name" :value="{ value: item.id, label: item.id+'-'+item.name}">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="关键能力">
          <el-select v-model="Keycapa" multiple placeholder="请选择关键能力" @change="GetKeycapa">
            <el-option v-for="item in KeyCapabilitOptions" :key="item.Id" :label="item.Id+'-'+item.CapabilitiesName" :value="item.Id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="属性">
          <el-input v-model="ItemAttribute" :disabled="disabled"></el-input>
          <el-button style="margin-left: 10px;background: #ccc;border-color: #fff;color: #fff;" @click="Disabled_btn">生成属性</el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
  },
  data() {
    return {
      UserId: '',
      labelPosition: 'right',
      dialogVisible: false,
      ClassOptions: [], //年级下拉
      GetTargetListOption: [], //目标编码
      ChapterTarget_val: [],
      ChapterTarget_params: '',
      ChapterTarget: [], //目标
      GetExamineOptions: [], //学习水平
      GetItemLevelOptions: [], //难度等级
      GetPlateTypeOptions: [], //内容领域
      KeyCapabilitOptions: [], //关键能力
      ChapterTarget_ChapterId: '',
      Grade_Name: '', //年级
      Examine: '', //学习水平
      Examine_Id: '', //学习水平id
      Examine_row: '',
      Level: '', //难度
      Level_Id: '', //难度id
      Level_row: '',
      PlateType: '', // 内容领域
      PlateType_id: '', //内容领域id
      PlateType_row: '',
      TypeId: '',
      ItemType: '',
      Analysis: '',
      Answer: '',
      Keycapa: [], //关键能力
      Keycapa_id: [], //关键能力id
      Keycapa_row: [],
      GradeId: '',
      GradeTerm: '',
      cascaderKey: 1,
      ItemAttribute: '',
      disabled: true
    }
  },
  components: {},
  mounted() {},
  watch: {
    GetTargetListOption() {
      ++this.cascaderKey
    }
  },
  computed: {},

  methods: {
    async Init(row) {
      this.Grade_Name = row.GradeTerm
      this.GradeId = row.GradeId
      this.Term = row.Term
      this.GradeTerm = row.GradeId + '_' + row.Term
      this.ChapterTarget_val = row.ChapterTarget
      this.ChapterTarget = []
      this.Examine_row = row.Examine
      this.Level_row = row.Level
      this.PlateType_row = row.PlateTypeId
      this.Keycapa_row = row.keycapa
      this.ItemAttribute = ''
      this.PaperId = row.Id
      this.TypeId = row.typeId
      this.ItemType = row.ItemType
      this.Title = row.Title
      await this.GetGradeListByPaper()
      await this.GetExamineList()
      await this.GetItemLevelList()
      await this.GetPlateTypeList()
      await this.GetKeyCapabilitiesList()
      await this.GetAnalysisDataByItemId(row.Id)
      await this.GetAnswerDataByItemId(row.Id)
    },
    open() {
      this.dialogVisible = true
    },

    //获取年级筛选（年级_学期）
    async GetGradeListByPaper() {
      let params = {
        RoleId: 0
      }
      const res = await this.$uwonhttp.post('/Manager/ManagerPaper/GetGradeListByPaper', params)
      if (res.data.Success) {
        this.ClassOptions = res.data.Data
        await this.GetChapterTargetListByUserId()
      }
    },

    //获取年级筛选（年级_学期）
    GetGrade(item) {
      this.Grade_Name = item.label
      this.GradeTerm = item.value
      const Item_val = item.value
      this.GradeId = Item_val.substring(0, 1)
      this.Term = Item_val.charAt(Item_val.length - 1)
      this.GetChapterTargetListByUserId()
    },

    //获取章节目标列表
    async GetChapterTargetListByUserId() {
      let params = {
        Term: this.Term,
        Grade: this.GradeId,
        UserId: this.UserId
      }
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetChapterTargetListByUserId', params)
      if (res.data.Success) {
        const Data = res.data.Data
        if (Data.length !== 0) {
          Data.forEach(item => {
            item.Second.forEach(el => {
              el.Third.forEach(val => {
                if (val.IDNum == this.ChapterTarget_val) {
                  this.ChapterTarget.push(item.ChapterId, el.ChapterId, val.ChapterId)
                }
              })
            })
          })
          const new_TartgetArr = this.ChapterTarget
          this.ChapterTarget_params = new_TartgetArr[new_TartgetArr.length - 1]
          this.GetTargetListOption = this.formatData(res.data.Data)
        } else {
          this.GetTargetListOption = []
        }
      }
    },

    formatData(data) {
      let arr = []
      data.forEach(el => {
        arr.push({
          value: el.ChapterId,
          label: el.ChapterName + '(' + el.IDNum + ')',
          children: this.getData(el.Second)
        })
      })
      return arr
    },

    getData(Array) {
      let nodes = []
      Array.forEach(item => {
        nodes.push({
          value: item.ChapterId,
          label: item.ChapterName + '(' + item.IDNum + ')',
          children: this.getThirdData(item.Third)
        })
      })
      return nodes
    },

    getThirdData(Arr) {
      let Result = []
      Arr.forEach(i => {
        Result.push({
          value: i.ChapterId,
          label: i.ChapterName + '(' + i.IDNum + ')'
        })
      })
      return Result
    },

    getChangeChapter(val) {
      this.ChapterTarget = val.pop()
      const New_Chapter = this.ChapterTarget
      this.ChapterTarget_params = New_Chapter
    },

    //学习水平列表
    async GetExamineList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetExamineList')
      if (res.data.Success) {
        this.GetExamineOptions = res.data.Data
        this.GetExamineOptions.forEach(item => {
          if (item.Code == this.Examine_row) {
            this.Examine = item.Code + '-' + item.ExamineName
            this.Examine_Id = item.Id
          }
        })
      }
    },

    GetExamine(params) {
      this.Examine = params.label
      this.Examine_Id = params.value
    },

    //难度等级
    async GetItemLevelList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetItemLevelList')
      if (res.data.Success) {
        this.GetItemLevelOptions = res.data.Data
        this.GetItemLevelOptions.forEach(item => {
          if (parseInt(item.levelId) == this.Level_row) {
            this.Level = item.LevelName
            this.Level_Id = item.levelId
          }
        })
      }
    },

    GetItemLevel(params) {
      this.Level = params.label
      this.Level_Id = params.value
    },

    //内容领域
    async GetPlateTypeList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetPlateTypeList')
      if (res.data.Success) {
        this.GetPlateTypeOptions = res.data.Data
        this.GetPlateTypeOptions.forEach(item => {
          if (item.id == this.PlateType_row) {
            this.PlateType = item.id + '-' + item.name
            this.PlateType_id = item.id
          }
        })
      }
    },

    GetPlateType(params) {
      this.PlateType = params.label
      this.PlateType_id = params.value
    },

    //获取关键能力板块
    async GetKeyCapabilitiesList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetKeyCapabilitiesList')
      if (res.data.Success) {
        const Keycapa_Arr = this.Keycapa_row
        const temp = []
        this.KeyCapabilitOptions = res.data.Data
        for (let i = 0; i < Keycapa_Arr.length; i++) {
          temp.push({ key: i, id: Keycapa_Arr[i] })
        }
        if (Keycapa_Arr.length == 0) {
          this.Keycapa = ['无']
        } else {
          this.Keycapa = []
          const Key_Arr = this.KeyCapabilitOptions
          const new_Arr = []
          for (let i = 0; i < Key_Arr.length; i++) {
            for (let j = 0; j < temp.length; j++) {
              if (temp[j].id == Key_Arr[i].Id) {
                this.Keycapa.push(Key_Arr[i].Id)
              }
            }
          }
        }
      }
    },

    GetKeycapa(val) {
      this.Keycapa = val
      if (this.Keycapa.includes('无')) {
        this.Keycapa.shift()
      }
      if (this.Keycapa.length > 3) {
        this.$message.error('最多只能绑定三个能力')
        this.Keycapa.splice(-1)
      }
    },

    //获取解析
    async GetAnalysisDataByItemId(id) {
      let params = {
        itemId: id
      }
      const res = await this.$uwonhttp.post(
        '/ExamItemAdaptive/ExamItemAdaptiveOptiAnswer/GetAnalysisDataByItemId',
        params
      )
      if (res.data.Success) {
        this.Analysis = res.data.Data.Content
      }
    },

    //获取答案
    async GetAnswerDataByItemId(id) {
      let params = {
        itemId: id
      }
      const res = await this.$uwonhttp.post(
        '/ExamItemAdaptive/ExamItemAdaptiveOptiAnswer/GetAnswerDataByItemId',
        params
      )
      if (res.data.Success) {
        this.Answer = res.data.Data.Content
      }
    },

    async Disabled_btn() {
      let params = {
        GradeTerm: this.GradeTerm,
        ChapterTarget: this.ChapterTarget_params,
        Examine: this.Examine_Id,
        Level: Number(this.Level_Id),
        PlateTypeId: this.PlateType,
        Keycapa: this.Keycapa.join(',')
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAttribute', params)
      if (res.data.Success) {
        this.ItemAttribute = res.data.Data
      }
    },

    async onSubmit() {
      let params = {
        Level: this.Level_Id,
        Examine: this.Examine_Id,
        GradeId: this.GradeId,
        Term: this.Term,
        GradeTerm: this.GradeTerm,
        ItemAttribute: this.ItemAttribute,
        keycapa: this.Keycapa,
        PlateTypeId: this.PlateType_id,
        Chapter: this.ChapterTarget_params,
        Id: this.PaperId,
        TypeId: this.TypeId,
        ItemType: this.ItemType,
        Title: this.Title,
        Analysis: this.Analysis,
        Answer: this.Answer
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/SaveData', params)
      if (res.data.Success) {
        this.$message({
          message: '编辑成功',
          type: 'success'
        })
        this.dialogVisible = false
        this.$parent.GetItemAdaptiveDataList()
      } else {
        this.drawer = true
        this.$message.error('保存失败')
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-select {
  width: 100%;
}
/deep/.el-cascader {
  width: 100%;
}
/deep/.el-input {
  width: 80%;
}
</style>    

