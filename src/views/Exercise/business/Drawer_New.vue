<template>
  <div class="Page_content">
    <el-drawer title="新建题目" :visible.sync="drawer" direction="rtl" custom-class="demo-drawer" ref="drawer" size="80%">
      <!-- :before-close="handleClose" -->
      <div class="demo-drawer__content">
        <div class="drawer_Up">
          <div class="Up_left">
            <div class="Edit_box">
              <span>题干</span>
              <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="300" v-model="Row_Title"></TinyMceEditor>
            </div>
            <div class="Edit_box">
              <span>解析</span>
              <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="300" v-model="Row_Analysis"></TinyMceEditor>
            </div>
          </div>
          <div class="Up_right">

            <!-- 填空 -->
            <div class="Edit_box" v-if="this.Row_TypeId=='填空题'">
              <span>答案设置</span>
              <br />
              <el-input v-model="Row_Answer"></el-input>
              <el-button class='Add_btn' @click="Add_synonymousBtn">添加同义</el-button>
              <!-- 同义答案 -->
              <div class="Input_box" v-for="(item,index) in Sy_nonymAnswer" :key="index" v-show="show_synonym">
                <el-input type="text" v-model="item.content"></el-input>
              </div>
              <div class="Table_Btn" v-show="show_synonym">
                <span style="font-size: 18px;cursor: pointer;" @click="Add_Input"><i class="el-icon-circle-plus-outline"></i>添加一行</span>
                <span style="font-size: 18px;margin-left: 20px;cursor: pointer;" @click="Remove_Input"><i class="el-icon-remove-outline"></i>删除一行</span>
              </div>
            </div>

            <!-- 单选  判断 -->
            <div class="Edit_box" v-if="this.Row_TypeId=='单项选择题' || this.Row_TypeId=='判断题' || this.Row_TypeId=='主观题'">
              <span>选项设置</span>
              <br />
              <div class="Radio_blank" v-for="Options_Answer in Options_Answer" :key="Options_Answer.id">
                <span class="options">
                  <a-radio-group @change="handleBlank" v-model="Blank_val">
                    <a-radio :value="Options_Answer.Option">{{ Options_Answer.Option }} 选项
                    </a-radio>
                  </a-radio-group>
                </span>
                <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="100" v-model="Options_Answer.Content"></TinyMceEditor>
              </div>
            </div>

            <!-- 多选题 -->
            <div class="Edit_box" v-if="this.Row_TypeId=='多项选择题'">
              <span>选项设置</span>
              <br />
              <div class="Radio_blank" v-for="Options_Check in Options_Check" :key="Options_Check.id">
                <span class="options">
                  <el-checkbox-group v-model="checked_Answer">
                    <el-checkbox :label="Options_Check.Option">{{ Options_Check.Option }}</el-checkbox>
                  </el-checkbox-group>
                </span>
                <TinyMceEditor style="margin-top: 5px;" ref="edit" id="edi" :height="100" v-model="Options_Check.Content"></TinyMceEditor>
              </div>
            </div>

            <div class="Table_Btn" v-if="this.Row_TypeId =='单项选择题' || this.Row_TypeId=='主观题' || this.Row_TypeId=='多项选择题'">
              <span style="font-size: 18px;cursor: pointer;" @click="AddOptions"><i class="el-icon-circle-plus-outline"></i>添加一行</span>
              <span style="font-size: 18px;margin-left: 20px;cursor: pointer;" @click="RemoveOptions"><i class="el-icon-remove-outline"></i>删除一行</span>
            </div>
          </div>
        </div>
      </div>
      <div class="demo-drawer__footer">
        <el-button @click="cancelForm">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import TinyMceEditor from '@/components/TinyMCE/tinyMce'
import WangEdit from '@/components/WangEditor/WangEditor'
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
      Row_Title: '',
      Row_Analysis: '',
      Row_Answer: '',
      // Options_Answer: [{ Option: 'A' }, { Option: 'B' }, { Option: 'C' }],
      Options_Answer: [],
      Options_Check: [{ Option: 'A' }, { Option: 'B' }, { Option: 'C' }, { Option: 'D' }],
      Row_TypeId: '',
      Blank_val: '',
      checked_Answer: [],
      Add_RadioList: ['', '', ''],
      show_synonym: false,
      Sy_nonymAnswer: [], //同义答案
      Sub_Obj: {},
      Save_Options: [],
      ItemType: ''
    }
  },
  components: {
    TinyMceEditor,
    WangEdit
  },
  mounted() {},
  watch: {},
  computed: {},

  methods: {
    async Init(data) {
      this.Sub_Obj = data
      this.show_synonym = false
      this.Row_TypeId = data.ItemTypeName
      const Pd_Answer = [{ Option: 'A' }, { Option: 'B' }]
      const Dy_Answer = [{ Option: 'A' }, { Option: 'B' }, { Option: 'C' }]
      this.Options_Answer =
        this.Row_TypeId == '判断题' ? Pd_Answer : this.Row_TypeId == '单项选择题' ? Dy_Answer : Pd_Answer

      // 输出[(1, 2, 3)]
    },

    handleBlank(e) {
      this.Row_Answer = e.target.value
    },

    AddOptions() {
      switch (this.Options_Answer.length) {
        case 0:
          this.Options_Answer.push({
            Option: 'A',
            Content: ''
          })
          break
        case 1:
          this.Options_Answer.push({
            Option: 'B',
            Content: ''
          })
          break
        case 2:
          this.Options_Answer.push({
            Option: 'C',
            Content: ''
          })
          break
        case 3:
          this.Options_Answer.push({
            Option: 'D',
            Content: ''
          })
          break
        case 4:
          this.Options_Answer.push({
            Option: 'E',
            Content: ''
          })
          break
        case 5:
          this.Options_Answer.push({
            Option: 'F',
            Content: ''
          })
          break
        case 6:
          this.Options_Answer.push({
            Option: 'G',
            Content: ''
          })
          break
        default:
          this.$message.success('最多加到G哦')
      }
      // 填空增加
      this.Add_RadioList.push('')
    },

    RemoveOptions() {
      this.Options_Answer.pop()
      this.Add_RadioList.pop()
    },

    //添加同意
    Add_synonymousBtn() {
      this.show_synonym = true
      this.Sy_nonymAnswer.push({ content: '' })
    },

    //添加一行 同义答案
    Add_Input() {
      this.Sy_nonymAnswer.push({ content: '' })
    },

    //删除一行 同义答案
    Remove_Input() {
      this.Sy_nonymAnswer.pop()
    },

    open() {
      this.drawer = true
    },

    //保存
    async onSubmit() {
      const Obj = this.Sub_Obj
      if (Obj.ItemTypeName == '填空题') {
        this.ItemType = 4
        this.Save_Options = []
        if (this.show_synonym == 'false') {
          this.Sy_nonymAnswer = []
        } else {
        }
      }

      if (Obj.ItemTypeName == '单项选择题') {
        this.ItemType = 1
        this.Save_Options = this.Options_Answer.map(val => {
          return {
            option: val.Option,
            Content: val.Content
          }
        })
      }

      if (Obj.ItemTypeName == '判断题') {
        this.ItemType = 3
      }

      let params = {
        Title: this.Row_Title, //新建
        GradeTerm: Obj.GradeTerm, //新建
        TypeId: Obj.TypeId, //新建
        Answer: this.Row_Answer, //新建
        Analysis: this.Row_Analysis, //新建
        Chapter: Obj.Chapter, //新建
        CreatorId: Obj.CreatorId, //新建 ---userid
        Examine: Obj.Examine, //新建
        ItemType: this.ItemType, //新建  单项选择 1  多项 2
        Level: Obj.Level, //新建
        PlateTypeId: Obj.PlateTypeId, //新建
        ItemAttribute: Obj.ItemAttribute, //新建
        Options: this.Save_Options,
        otherAnswer: this.Sy_nonymAnswer,
        keycapa: Obj.Keycapa
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/SaveItemInfo', params)
      if (res.data.Success) {
        this.$message({
          message: '新建成功',
          type: 'success'
        })
        this.drawer = false
        await this.$parent.GetItemAdaptiveDataList()
      } else {
        this.drawer = true
        this.$message.error('新建失败')
      }
    },

    cancelForm() {
      this.drawer = false
    },
    handleClose(done) {}
  }
}
</script>
<style lang="less" scoped>
.drawer_Up {
  display: flex;
  padding: 10px;
  justify-content: space-between;
  .Up_left {
    width: 50%;
    padding: 10px;
    background: #ffffff;
  }
  .Up_right {
    width: 49%;
    padding: 10px;
    background: #ffffff;
  }
}
.Edit_box {
  margin-bottom: 40px;
  span {
    font-size: 22px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #292929;
  }
  .Radio_blank {
    margin-bottom: 15px;
    /deep/.ant-radio + * {
      font-size: 16px;
      color: #000;
    }
  }
  /deep/.el-input {
    width: 70%;
    height: 45px;
    color: #888888;
    font-size: 19px;
  }
  /deep/.el-input__inner {
    background-color: rgba(137, 225, 186, 0.5);
    background: rgba(137, 225, 186, 0.5);
    border-color: rgba(137, 225, 186, 0.5);
  }
  .Add_btn {
    background: #d1b57f;
    color: #fff;
    width: 120px;
    border-color: #d1b57f;
    font-size: 15px;
    margin-left: 30px;
    line-height: 14px;
  }
  .Input_box {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}
/deep/.el-drawer__header {
  font-size: 23px;
  padding: 10px;
  margin-bottom: 0;
  border-bottom: 1px solid #e8e8e8;
}
/deep/.el-dialog__close {
  display: none;
}
/deep/ .el-drawer__body {
  overflow-y: scroll;

  margin-bottom: 50px;
}
/deep/.demo-drawer__footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px;
  text-align: right;
  background-color: white;
}
</style>  


