<template>
  <div>
    <el-dialog title="题型" :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false" ref='from'>
      <el-form label-width="100px" class="demo-ruleForm">
        <!-- 年级 目标学 学习水平 难度 内容领域 关键能力 题型  属性-->
        <el-form-item label="年级">
          <el-select v-model="Grade_Name" placeholder="年级" @change="GetGrade">
            <el-option v-for="item in ClassOptions" :key="item.GradeId" :label="item.GradeName" :value="{ value: item.GradeId, label: item.GradeName}"> </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="目标编码">
          <el-cascader v-model="Chapter" :key="cascaderKey" :options="GetTargetListOption" placeholder="请选择错因编码" @change="getChangeChapter"></el-cascader>
        </el-form-item>

        <el-form-item label="学习水平">
          <el-select v-model="Examine" placeholder="请选择学习水平" @change="GetExamine">
            <el-option v-for="item in GetExamineOptions" :key="item.Id" :label="item.Code+'-'+item.ExamineName" :value="{ value: item.Id, label: item.Code+'-'+item.ExamineName}">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="难度">
          <el-select v-model="Level" placeholder="请选择难度" @change="GetItemLevel" clearable>
            <el-option v-for="item in GetItemLevelOptions" :key="item.levelId" :label="item.LevelName" :value="{ value: item.levelId, label: item.LevelName}">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容领域">
          <el-select v-model="PlateType" placeholder="请选择内容领域" @change="GetPlateType">
            <el-option v-for="item in GetPlateTypeOptions" :key="item.id" :label="item.id+'-'+item.name" :value="{ value: item.id, label: item.id+'-'+item.name}">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="关键能力">
          <el-select v-model="Keycapa" multiple placeholder="请选择关键能力" @change="GetKeycapa">
            <el-option v-for="item in KeyCapabilitOptions" :key="item.Id" :label="item.Id+'-'+item.CapabilitiesName" :value="item.Id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="题型选择" prop="pass">
          <el-select v-model="TypeId" placeholder="请选择题目类型" @change="GetTypeId">
            <el-option v-for="item in TypeOptions" :key="item.Id" :label="item.ItemTypeName" :value="item.Id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="属性">
          <el-input v-model="ItemAttribute" :disabled="true"></el-input>
          <el-button style="margin-left: 10px;background: #ccc;border-color: #fff;color: #fff;" @click="Disabled_btn">生成属性</el-button>
        </el-form-item>

      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import eventBus from '@/components/eventBus/eventBus'
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
  },
  data() {
    return {
      UserId: '',
      TypeId: '',
      ItemTypeName: '',
      TypeOptions: [],
      Grade_Name: '',
      GradeTerm: '',
      GradeId: '',
      Term: '',
      ClassOptions: [],
      Chapter: '',
      GetTargetListOption: [],
      GetExamineOptions: [],
      GetItemLevelOptions: [],
      GetPlateTypeOptions: [],
      KeyCapabilitOptions: [],
      cascaderKey: 1,
      itemid: '',
      Examine: '',
      Examine_Id: '',
      Level: '',
      Level_Id: '',
      PlateType: '',
      PlateType_id: '',
      Keycapa: [], //关键能力
      Keycapa_id: [], //关键能力id
      dialogVisible: false,
      ItemAttribute: ''
    }
  },
  components: {},
  mounted() {},
  watch: {
    GetTargetListOption() {
      ++this.cascaderKey
    }
  },
  computed: {},

  methods: {
    Init() {
      this.Clear_Obj()
      this.GetGradeListByPaper()
      this.GetExamItemTypeList()
      this.GetExamineList()
      this.GetItemLevelList()
      this.GetPlateTypeList()
      this.GetKeyCapabilitiesList()
    },

    Clear_Obj() {
      Object.assign(this.$data, this.$options.data())
    },

    //获取年级筛选（年级_学期）
    async GetGradeListByPaper() {
      let params = {
        RoleId: 0
      }
      const res = await this.$uwonhttp.post('/Manager/ManagerPaper/GetGradeListByPaper', params)
      if (res.data.Success) {
        this.ClassOptions = res.data.Data
        await this.GetChapterTargetListByUserId()
      }
    },

    //获取年级筛选（年级_学期）
    GetGrade(item) {
      this.Grade_Name = item.label
      this.GradeTerm = item.value
      const Item_val = item.value
      this.GradeId = Item_val.substring(0, 1)
      this.Term = Item_val.charAt(Item_val.length - 1)
      this.GetChapterTargetListByUserId()
    },

    //获取章节目标列表
    async GetChapterTargetListByUserId() {
      let params = {
        Term: this.Term,
        Grade: this.GradeId,
        UserId: this.UserId
      }
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetChapterTargetListByUserId', params)

      if (res.data.Success) {
        const Data = res.data.Data
        this.GetTargetListOption = this.formatData(res.data.Data)
      }
    },

    formatData(data) {
      let arr = []
      data.forEach(el => {
        arr.push({
          value: el.ChapterId,
          label: el.ChapterName + '(' + el.IDNum + ')',
          children: this.getData(el.Second)
        })
      })
      return arr
    },

    getData(Array) {
      let nodes = []
      Array.forEach(item => {
        nodes.push({
          value: item.ChapterId,
          label: item.ChapterName + '(' + item.IDNum + ')',
          children: this.getThirdData(item.Third)
        })
      })
      return nodes
    },

    getThirdData(Arr) {
      let Result = []
      Arr.forEach(i => {
        Result.push({
          value: i.ChapterId,
          label: i.ChapterName + '(' + i.IDNum + ')'
        })
      })
      return Result
    },

    getChangeChapter(val) {
      this.Chapter = val.pop()
    },

    //学习水平列表
    async GetExamineList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetExamineList')
      if (res.data.Success) {
        this.GetExamineOptions = res.data.Data
      }
    },

    GetExamine(params) {
      this.Examine = params.label
      this.Examine_Id = params.value
    },

    //难度等级
    async GetItemLevelList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetItemLevelList')
      if (res.data.Success) {
        this.GetItemLevelOptions = res.data.Data
      }
    },

    GetItemLevel(params) {
      this.Level = params.label
      this.Level_Id = params.value
    },

    //内容领域
    async GetPlateTypeList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetPlateTypeList')
      if (res.data.Success) {
        this.GetPlateTypeOptions = res.data.Data
      }
    },

    GetPlateType(params) {
      this.PlateType = params.label
      this.PlateType_id = params.value
    },

    //获取题型列表
    async GetExamItemTypeList() {
      const res = await this.$uwonhttp.get('/ExamItem/ExamItem/GetExamItemTypeList')
      if (res.data.Success) {
        this.TypeOptions = res.data.Data
      }
    },

    GetTypeId(val) {
      this.TypeId = val
    },

    //获取关键能力板块
    async GetKeyCapabilitiesList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetKeyCapabilitiesList')
      if (res.data.Success) {
        this.KeyCapabilitOptions = res.data.Data
      }
    },

    GetKeycapa(val) {
      this.Keycapa = val
      if (this.Keycapa.length > 3) {
        this.$message.error('最多只能绑定三个能力')
        this.Keycapa.splice(-1)
      }
    },

    open() {
      this.dialogVisible = true
    },

    async Disabled_btn() {
      let params = {
        GradeTerm: this.GradeTerm,
        ChapterTarget: this.Chapter,
        Examine: this.Examine_Id,
        Level: Number(this.Level_Id),
        PlateTypeId: this.PlateType,
        Keycapa: this.Keycapa.join(',')
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAttribute', params)
      if (res.data.Success) {
        this.ItemAttribute = res.data.Data
      }
    },

    onSubmit() {
      let ItemTypeName = ''
      this.TypeOptions.forEach(Item => {
        if (this.TypeId == Item.Id) {
          ItemTypeName = Item.ItemTypeName
        }
      })
      let Obj = {
        GradeTerm: this.GradeTerm,
        Chapter: this.Chapter,
        CreatorId: this.UserId,
        Examine: this.Examine_Id,
        Level: this.Level_Id,
        PlateTypeId: this.PlateType_id,
        ItemAttribute: this.ItemAttribute,
        ItemTypeName: ItemTypeName,
        TypeId: this.TypeId,
        Keycapa: this.Keycapa
      }
      eventBus.$emit('Obj', Obj)
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-select {
  width: 100%;
}
/deep/.el-cascader {
  width: 100%;
}
/deep/.el-input {
  width: 80%;
}
</style>    

