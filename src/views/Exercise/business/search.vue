<template>
  <div class="Page_content">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="年级">
            <el-select v-model="Grade" placeholder="年级" @change="GetGrade">
              <el-option v-for="item in ClassOptions" :key="item.GradeId" :label="item.GradeName" :value="{ value: item.GradeId, label: item.GradeName}"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="目标编码">
            <el-cascader v-model="ChapterTarget_Name" :key="cascaderKey" :options="GetTargetListOption" placeholder="请选择错因编码" @change="getChangeChapter"></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="学习水平">
            <el-select v-model="formInline.Examine" placeholder="请选择学习水平" @change="GetExamine">
              <el-option v-for="item in GetExamineOptions" :key="item.Id" :label="item.ExamineName" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="难度">
            <el-select v-model="formInline.Level" placeholder="请选择难度" @change="GetItemLevel" clearable>
              <el-option v-for="item in GetItemLevelOptions" :key="item.levelId" :label="item.LevelName" :value="item.levelId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="内容领域">
            <el-select v-model="formInline.PlateTypeId" placeholder="请选择内容领域" @change="GetPlateType">
              <el-option v-for="item in GetPlateTypeOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="关键能力">
            <el-select v-model="formInline.Keycapa" placeholder="请选择关键能力" @change="GetKeycapa">
              <el-option v-for="item in KeyCapabilitOptions" :key="item.Id" :label="item.CapabilitiesName" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="题型">
            <el-select v-model="formInline.TypeId" placeholder="请选择题目类型" @change="GetTypeId">
              <el-option v-for="item in TypeOptions" :key="item.Id" :label="item.WebName" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <div style="text-align:right;margin-right: 25%;">
            <el-form-item>
              <el-button class="from_btn" type="primary" @click="onSubmit">查询</el-button>
            </el-form-item>
            <el-form-item>
              <el-button class="from_btn" type="info" @click="onSubmit">重置 </el-button>
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
  },
  data() {
    return {
      UserId: '',
      ClassOptions: [],
      GetExamineOptions: [], //学习水平
      GetItemLevelOptions: [], //难度等级
      GetPlateTypeOptions: [], //内容领域
      KeyCapabilitOptions: [], //关键能力
      TypeOptions: [], //题目类型
      ChapterTarget_Name: '',
      ChapterTarget_Id: '',
      GetTargetListOption: [],
      Grade: '一上', //年级
      Grade_Target: '',
      Term_Target: '',
      formInline: {
        Term: '', //学期
        Examine: '', //学习水平
        Level: '', //难度等级
        PlateTypeId: '', //内容领域
        Keycapa: '', //关键能力
        TypeId: '', //题目ID
        user: '',
        region: ''
      },
      cascaderKey: 1
    }
  },
  components: {},
  mounted() {},
  watch: {},
  computed: {},

  methods: {
    onSubmit() {}
  }
}
</script>
<style lang="less" scoped>
.Page_content {
  margin-top: 20px;
}
.from_btn {
  width: 100px;
}
</style>    

