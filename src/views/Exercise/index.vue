<template>
  <div class="Page_box">
    <div class="Page_header">
      <p id="demo"></p>
      <el-button class="Data_btn" :style="{'background':this.multipleSelection.length === 0 ? '#CDCDCD':'#61BB96'}" @click="Delete_btn">删除</el-button>
      <el-button class="el_btn" @click="Export_btn" style="background:#CDCDCD">导出</el-button>
      <el-button class="el_btn" @click="New_subject">新建题目</el-button>
      <el-button class="el_btn" @click="Import_btn">导入题目</el-button>
    </div>
    <div class="Page_content">
      <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="年级">
              <el-select v-model="Grade" placeholder="年级" @change="GetGrade">
                <el-option v-for="item in ClassOptions" :key="item.GradeId" :label="item.GradeName" :value="{ value: item.GradeId, label: item.GradeName}"> </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="目标编码">
              <el-cascader v-model="formInline.ChapterTarget" :key="cascaderKey" :options="GetTargetListOption" placeholder="请选择错因编码" @change="getChangeChapter"></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="学习水平">
              <el-select v-model="formInline.Examine" placeholder="请选择学习水平" @change="GetExamine">
                <el-option v-for="item in GetExamineOptions" :key="item.Id" :label="item.ExamineName" :value="item.Id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="难度">
              <el-select v-model="formInline.Level" placeholder="请选择难度" @change="GetItemLevel" clearable>
                <el-option v-for="item in GetItemLevelOptions" :key="item.levelId" :label="item.LevelName" :value="item.levelId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="内容领域">
              <el-select v-model="formInline.PlateTypeId" placeholder="请选择内容领域" @change="GetPlateType">
                <el-option v-for="item in GetPlateTypeOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="关键能力">
              <el-select v-model="formInline.Keycapa" placeholder="请选择关键能力" @change="GetKeycapa">
                <el-option v-for="item in KeyCapabilitOptions" :key="item.Id" :label="item.CapabilitiesName" :value="item.Id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="题型">
              <el-select v-model="formInline.TypeId" placeholder="请选择题目类型" @change="GetTypeId">
                <el-option v-for="item in TypeOptions" :key="item.Id" :label="item.ItemTypeName" :value="item.Id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <div style="text-align:right;margin-right: 25%;">
              <el-form-item>
                <el-button class="from_btn" type="primary" @click="onSubmit">查询</el-button>
              </el-form-item>
              <el-form-item>
                <el-button class="from_btn" type="info" @click="onReset">重置 </el-button>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table :data="tableData" v-loading="loading" @selection-change="handleSelectionChange" border style="width: 100%" :header-cell-style="{ 'text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column fixed prop="GradeTerm" label="年级" width="150">
      </el-table-column>
      <el-table-column prop="Title" label="题干">
        <template slot-scope="scope">
          <span v-html="scope.row.Title"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ChapterTarget" label="目标编码" width="100">
      </el-table-column>
      <el-table-column prop="Examine" label="学习水平" width="100">
      </el-table-column>
      <el-table-column prop="Level" label="难度" width="100" sortable>
      </el-table-column>
      <el-table-column prop="PlateTypeId" label="内容领域" width="100">
      </el-table-column>
      <el-table-column prop="keycapa" label="关键能力" width="100">
        <template slot-scope="scope">
          <div v-show="scope.row.keycapa.length!==0">
            <span style="padding: 10px;" v-for="(item,index) in scope.row.keycapa" :key="index">{{item}}</span>
          </div>
          <div v-show="scope.row.keycapa.length==0">
            <span>无</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="TypeId" label="题型" width="100">
      </el-table-column>
      <el-table-column prop="CreateTime" label="上传时间" width="200" sortable>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="Edit_Code(scope.row)">编辑编码</el-button>
          <!-- <el-button type="text" size="small" @click="Edit_Type(scope.row)">修改题型</el-button> -->
          <el-button type="text" size="small" @click='Edit_Title(scope.row)'>编辑题目</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="PageIndex" :page-size="PageRows" :page-sizes="[8]" layout="total, sizes, prev, pager, next, jumper"
        :total="totalNum">
      </el-pagination>
    </div>
    <EditCode ref="Code_doaling"></EditCode>
    <EditType ref="Type_doaling"></EditType>
    <ImportTitle ref="Import_doaling"></ImportTitle>
    <DrawerType ref="DrawerType_doaling"></DrawerType>
    <DrawerNew ref="DrawerNew_doaling"></DrawerNew>
  </div>
</template>

<script>
import echarts from 'echarts'
import EditCode from './business/Edit_Code'
import EditType from './business/Edit_Type'
import ImportTitle from './business/Import_Title'
import DrawerType from './business/Drawer_Type'
import DrawerNew from './business/Drawer_New'
import eventBus from '@/components/eventBus/eventBus'
export default {
  created() {
    this.UserId = localStorage.getItem('UserId')
    this.SearchInit()
  },
  mounted() {
    eventBus.$on('Obj', Obj => {
      this.$refs.DrawerNew_doaling.open()
      this.$refs.DrawerNew_doaling.Init(Obj)
    })
  },
  data() {
    return {
      role: '',
      tableData: [],
      UserId: '',
      loading: false,
      ClassOptions: [],
      GetExamineOptions: [], //学习水平
      GetItemLevelOptions: [], //难度等级
      GetPlateTypeOptions: [], //内容领域
      KeyCapabilitOptions: [], //关键能力
      TypeOptions: [], //题目类型
      ChapterTarget_Name: '',
      ChapterTarget_Id: '',
      GetTargetListOption: [],
      Grade: '', //年级
      Grade_Target: '',
      Term_Target: '',
      Change_Visible: false,
      formInline: {
        GradeTerm: '', //学期
        Examine: '', //学习水平
        Level: '', //难度等级
        PlateTypeId: '', //内容领域
        Keycapa: '', //关键能力
        TypeId: '', //题目ID
        ChapterTarget: ''
      },
      PageIndex: 1,
      PageRows: 8,
      totalNum: 0,
      cascaderKey: 1,
      multipleSelection: []
    }
  },
  components: {
    EditCode,
    EditType,
    ImportTitle,
    DrawerType,
    DrawerNew
  },

  watch: {
    GetTargetListOption() {
      ++this.cascaderKey
    },
    multipleSelection: {
      handler(val) {
        console.log(val, 'val')
      },
      immediate: true
    }
  },
  computed: {},
  methods: {
    async SearchInit() {
      await this.GetGradeListByPaper()
      await this.GetExamineList()
      await this.GetItemLevelList()
      await this.GetPlateTypeList()
      await this.GetKeyCapabilitiesList()
      await this.GetExamItemTypeList()
      await this.GetItemAdaptiveDataList()
    },
    //获取年级筛选（年级_学期）
    async GetGradeListByPaper() {
      let params = {
        RoleId: 0
      }
      const res = await this.$uwonhttp.post('/Manager/ManagerPaper/GetGradeListByPaper', params)
      if (res.data.Success) {
        this.ClassOptions = res.data.Data
        // this.formInline.GradeTerm = this.ClassOptions[0].GradeId
        // this.Grade = this.ClassOptions[0].GradeName
        this.Grade_Target = this.ClassOptions[0].GradeId.slice(2)
        this.Term_Target = this.ClassOptions[0].GradeId.slice(2)
        await this.GetChapterTargetListByUserId()
      }
    },

    //获取年级筛选（年级_学期）
    GetGrade(item) {
      this.formInline.GradeTerm = item.value
      this.Grade = item.label
      const Target_val = item.value
      this.Grade_Target = Target_val.substring(0, 1)
      this.Term_Target = Target_val.charAt(Target_val.length - 1)
      this.GetChapterTargetListByUserId()
    },

    //获取章节目标列表
    async GetChapterTargetListByUserId() {
      let params = {
        Term: this.Term_Target,
        Grade: this.Grade_Target,
        UserId: this.UserId
      }
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetChapterTargetListByUserId', params)

      if (res.data.Success) {
        const Data = res.data.Data
        if (Data.length !== 0) {
          this.ChapterTarget_Name = Data[0].ChapterName + '(' + Data[0].IDNum + ')'
          this.GetTargetListOption = this.formatData(res.data.Data)
        } else {
          this.GetTargetListOption = []
        }
      }
    },
    formatData(data) {
      let arr = []
      data.forEach(el => {
        arr.push({
          value: el.ChapterId,
          label: el.ChapterName + '(' + el.IDNum + ')',
          children: this.getData(el.Second)
        })
      })
      return arr
    },
    getData(Array) {
      let nodes = []
      Array.forEach(item => {
        nodes.push({
          value: item.ChapterId,
          label: item.ChapterName + '(' + item.IDNum + ')',
          children: this.getThirdData(item.Third)
        })
      })
      return nodes
    },
    getThirdData(Arr) {
      let Result = []
      Arr.forEach(i => {
        Result.push({
          value: i.ChapterId,
          label: i.ChapterName + '(' + i.IDNum + ')'
        })
      })
      return Result
    },
    getChangeChapter(val) {
      this.formInline.ChapterTarget = val.pop()
    },
    //学习水平列表
    async GetExamineList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetExamineList')
      if (res.data.Success) {
        this.GetExamineOptions = res.data.Data
      }
    },

    GetExamine(val) {
      this.formInline.Examine = val
    },

    //难度等级
    async GetItemLevelList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetItemLevelList')
      if (res.data.Success) {
        this.GetItemLevelOptions = res.data.Data
      }
    },

    GetItemLevel(val) {
      this.formInline.Level = val
    },

    //内容领域
    async GetPlateTypeList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetPlateTypeList')
      if (res.data.Success) {
        this.GetPlateTypeOptions = res.data.Data
      }
    },

    GetPlateType(val) {
      this.formInline.PlateTypeId = val
    },

    //获取关键能力板块
    async GetKeyCapabilitiesList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetKeyCapabilitiesList')
      if (res.data.Success) {
        this.KeyCapabilitOptions = res.data.Data
      }
    },

    GetKeycapa(val) {
      this.formInline.Keycapa = val
    },

    //获取题型列表
    async GetExamItemTypeList() {
      const res = await this.$uwonhttp.get('/ExamItem/ExamItem/GetExamItemTypeList')
      if (res.data.Success) {
        this.TypeOptions = res.data.Data
      }
    },

    GetTypeId(val) {
      this.GetTypeId = val
    },

    onSubmit() {
      this.PageIndex = 1
      this.PageRows = 8
      this.GetItemAdaptiveDataList()
    },

    onReset() {
      this.formInline = {}
      this.Grade = ''
      this.GetItemAdaptiveDataList()
    },

    //获取自适应题列表
    async GetItemAdaptiveDataList() {
      this.loading = true
      let params = {
        ...this.formInline,
        PageRows: this.PageRows,
        PageIndex: this.PageIndex
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAdaptiveDataList', params)
      if (res.data.Success) {
        this.loading = false
        this.tableData = res.data.Data
        this.totalNum = res.data.Total
        this.tableData.forEach(item => {
          const reg = /(#&\d+@)/g
          const inputele = ' '
          const stem = item.Title.replace(reg, inputele)
          item.Title = stem
        })
      }
    },

    handleSizeChange(val) {
      this.PageRows = val
      this.GetItemAdaptiveDataList()
    },

    handleCurrentChange(val) {
      this.PageIndex = val
      this.GetItemAdaptiveDataList()
    },

    //编辑编码
    Edit_Code(row) {
      this.$refs.Code_doaling.Init(row)
      this.$refs.Code_doaling.open()
    },

    // //编辑题型
    // Edit_Type(row) {
    //   this.$refs.Type_doaling.Init(row)
    //   this.$refs.Type_doaling.open()
    // },

    //导入题目
    Import_btn() {
      this.$refs.Import_doaling.open()
    },

    //新建题目
    New_subject() {
      this.$refs.Type_doaling.Init()
      this.$refs.Type_doaling.open()
    },

    Edit_Title(row) {
      this.$refs.DrawerType_doaling.Init(row)
      this.$refs.DrawerType_doaling.open()
    },

    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    //删除
    Delete_btn() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: '请先选择删除的题目',
          type: 'warning'
        })
      } else {
        this.DeleteData()
      }
    },

    //导出
    Export_btn() {
      this.$message({
        message: '此功能暂未开放,敬请期待！',
        type: 'success'
      })
    },

    //删除
    async DeleteData() {
      const list = this.multipleSelection.map(val => {
        return val.Id
      })
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/DeleteData', {
        ids: JSON.stringify(list)
      })
      if (res.data.Success) {
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        this.GetItemAdaptiveDataList()
      }
    }
  }
}
</script>
<style lang="less" scoped>
.Page_box {
  width: 100%;
  padding: 10px;
  background: #ffff;
  .Page_header {
    /deep/.el-button {
      width: 100px;
      background: #61bb96;
      border-color: #fff;
      color: #fff;
    }
  }
  .Page_content {
    margin-top: 20px;
  }
  .from_btn {
    width: 100px;
  }
  /deep/.el-pagination {
    text-align: center;
    margin-top: 20px;
  }
}
</style>    
