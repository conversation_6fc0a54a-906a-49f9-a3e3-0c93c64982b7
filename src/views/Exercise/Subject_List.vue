<template>
  <div class="Page_box">
    <el-form :inline="true" :model="formInline" class="demo-form-inline" size="medium">
      <el-row>
        <el-col :span="4.8">
          <el-form-item label="年级">
            <el-select v-model="Grade" placeholder="年级" @change="GetGrade">
              <el-option v-for="item in ClassOptions" :key="item.GradeId" :label="item.GradeName" :value="{ value: item.GradeId, label: item.GradeName}"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4.8">
          <el-form-item label="目标编码">
            <el-cascader v-model="formInline.ChapterTarget" :key="cascaderKey" :options="GetTargetListOption" placeholder="请选择错因编码" @change="getChangeChapter"></el-cascader>
          </el-form-item>
        </el-col>

        <el-col :span="4.8">
          <el-form-item label="学习水平">
            <el-select v-model="formInline.Examine" placeholder="请选择学习水平" @change="GetExamine">
              <el-option v-for="item in GetExamineOptions" :key="item.Id" :label="item.ExamineName" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4.8">
          <el-form-item label="难度">
            <el-select v-model="formInline.Level" placeholder="请选择难度" @change="GetItemLevel" clearable>
              <el-option v-for="item in GetItemLevelOptions" :key="item.levelId" :label="item.LevelName" :value="item.levelId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4.8">
          <el-form-item label="内容领域">
            <el-select v-model="formInline.PlateTypeId" placeholder="请选择内容领域" @change="GetPlateType">
              <el-option v-for="item in GetPlateTypeOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4.8">
          <el-form-item label="关键能力">
            <el-select v-model="formInline.Keycapa" placeholder="请选择关键能力" @change="GetKeycapa">
              <el-option v-for="item in KeyCapabilitOptions" :key="item.Id" :label="item.CapabilitiesName" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4.8">
          <el-form-item label="题型">
            <el-select v-model="formInline.TypeId" placeholder="请选择题目类型" @change="GetTypeId">
              <el-option v-for="item in TypeOptions" :key="item.Id" :label="item.ItemTypeName" :value="item.Id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="7">
          <el-form-item label="查询类别">
            <el-select v-model="formInline.condition" placeholder="查询类别" @change="GetconditionVal">
              <el-option v-for="item in ConditionOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-input v-model="formInline.keyword" placeholder="关键字"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="5">
          <el-form-item>
            <el-button type="primary" @click="onSubmit">查询</el-button>
            <el-button type="info" @click="onReset">重置</el-button>
            <el-button type="success" @click="Choose_topic">选择题目</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-table ref="singleTable" :data="tableData" v-loading="loading" style="width: 100%" :header-cell-style="{ background:'#F5F7F9',color:'#717171','text-align': 'center' }" :cell-style="{ 'text-align': 'center' }">
      <el-table-column type="index" width="50" label="序号">
      </el-table-column>
      <el-table-column prop="AreaName" label="区域" width="120">
      </el-table-column>
      <el-table-column prop="SchoolName" label="学校" width="120">
      </el-table-column>
      <el-table-column prop="Grade" label="年级">
        <template slot-scope="scope">
          <span>{{scope.row.Grade}}年级</span>
        </template>
      </el-table-column>
      <el-table-column prop="ClassName" label="班级">
      </el-table-column>
      <el-table-column prop="StudentName" label="学生">
      </el-table-column>
      <el-table-column prop="PaperTitle" label="对应练习名称">
      </el-table-column>
      <el-table-column prop="ItemTitle" label="原题题干">
        <template slot-scope="scope">
          <span v-html="scope.row.ItemTitle"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ChapterTarget" label="原题题目编码">
      </el-table-column>
      <el-table-column prop="ItemAdaptiveTitle" label="推送练习题干">
        <template slot-scope="scope">
          <span v-html="scope.row.ItemAdaptiveTitle"></span>
        </template>
      </el-table-column>
      <el-table-column prop="ItemAdaptiveChapterTarget" label="推送练习编码">
      </el-table-column>
      <el-table-column prop="PushCount" label="推送次数">
        <template slot-scope="scope">
          <span>{{scope.row.PushCount}}次</span>
        </template>
      </el-table-column>
    </el-table>
    <div>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInline.PageIndex" :page-size="formInline.PageRows" :page-sizes="[8]"
        layout="total, sizes, prev, pager, next, jumper" :total="totalNum">
      </el-pagination>
    </div>
    <el-drawer title="题目" :visible.sync="table" direction="rtl" size="55%">
      <div v-loading="loading">
        <div style="margin-top: 15px;">
          <el-input placeholder="请输入题目" v-model="search" @input="handleInput" class="input-with-select">
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </div>
        <el-table :data="gridData" @selection-change="handleSelectionChange" border style="margin-top: 15px;" :header-cell-style="{ background:'#F5F7F9',color:'#717171','text-align': 'center' }"
          :cell-style="{ 'text-align': 'center' }">
          <el-table-column type="selection" width="50">
          </el-table-column>
          <el-table-column type="index" width="50" label="序号">
          </el-table-column>
          <el-table-column property="GradeTerm" width="100" label="年级"></el-table-column>
          <el-table-column property="TypeId" width="120" label="题型"></el-table-column>
          <el-table-column property="Title" label="题干">
            <template slot-scope="scope">
              <span v-html="scope.row.Title"></span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="demo-drawer__footer">
        <el-button>取 消</el-button>
        <el-button type="primary" @click="query_submit">确 定</el-button>
      </div>
    </el-drawer>

  </div>
</template>

<script>
export default {
  data() {
    return {
      role: '',
      tableData: [],
      UserId: '',
      loading: false,
      ClassOptions: [],
      GetExamineOptions: [], //学习水平
      GetItemLevelOptions: [], //难度等级
      GetPlateTypeOptions: [], //内容领域
      KeyCapabilitOptions: [], //关键能力
      TypeOptions: [], //题目类型
      ChapterTarget_Name: '',
      ChapterTarget_Id: '',
      GetTargetListOption: [],
      Grade: '', //年级
      Grade_Target: '',
      Term_Target: '',
      Change_Visible: false,
      PageIndex: 1,
      PageRows: 8,
      totalNum: 0,
      cascaderKey: 1,
      ConditionOptions: [
        {
          value: 'PaperTitle',
          label: '对应练习名称'
        },
        {
          value: 'ItemTitle',
          label: '原题题干'
        },
        {
          value: 'ItemAdaptiveTitle',
          label: '推送练习题干'
        }
      ],
      formInline: {
        GradeTerm: '', //学期
        Examine: '', //学习水平
        Level: '', //难度等级
        PlateTypeId: '', //内容领域
        Keycapa: '', //关键能力
        TypeId: '', //题目ID
        ChapterTarget: '',
        condition: '',
        keyword: '',
        PageIndex: 1,
        PageRows: 8
      },
      tableData: [],
      multipleSelection: [],
      table: false,
      gridData: [],
      search: '',
      loading: false
    }
  },
  created() {
    this.UserId = localStorage.getItem('UserId')
    this.SearchInit()
  },
  components: {},
  mounted() {},
  watch: {
    GetTargetListOption() {
      ++this.cascaderKey
    }
  },
  computed: {},

  methods: {
    async SearchInit() {
      await this.GetGradeListByPaper()
      await this.GetExamineList()
      await this.GetItemLevelList()
      await this.GetPlateTypeList()
      await this.GetKeyCapabilitiesList()
      await this.GetExamItemTypeList()
      await this.onSubmit()
    },
    //获取年级筛选（年级_学期）
    async GetGradeListByPaper() {
      let params = {
        RoleId: 0
      }
      const res = await this.$uwonhttp.post('/Manager/ManagerPaper/GetGradeListByPaper', params)
      if (res.data.Success) {
        this.ClassOptions = res.data.Data
        this.Grade = res.data.Data[4].GradeName
        this.formInline.GradeTerm = res.data.Data[4].GradeId
        this.Grade_Target = this.ClassOptions[0].GradeId.slice(2)
        this.Term_Target = this.ClassOptions[0].GradeId.slice(2)
        await this.GetChapterTargetListByUserId()
      }
    },

    //获取年级筛选（年级_学期）
    GetGrade(item) {
      this.formInline.GradeTerm = item.value
      this.Grade = item.label
      const Target_val = item.value
      this.Grade_Target = Target_val.substring(0, 1)
      this.Term_Target = Target_val.charAt(Target_val.length - 1)
      this.GetChapterTargetListByUserId()
    },

    //获取章节目标列表
    async GetChapterTargetListByUserId() {
      let params = {
        Term: this.Term_Target,
        Grade: this.Grade_Target,
        UserId: this.UserId
      }
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetChapterTargetListByUserId', params)

      if (res.data.Success) {
        const Data = res.data.Data
        if (Data.length !== 0) {
          this.ChapterTarget_Name = Data[0].ChapterName + '(' + Data[0].IDNum + ')'
          this.GetTargetListOption = this.formatData(res.data.Data)
        } else {
          this.GetTargetListOption = []
        }
      }
    },
    formatData(data) {
      let arr = []
      data.forEach(el => {
        arr.push({
          value: el.ChapterId,
          label: el.ChapterName + '(' + el.IDNum + ')',
          children: this.getData(el.Second)
        })
      })
      return arr
    },
    getData(Array) {
      let nodes = []
      Array.forEach(item => {
        nodes.push({
          value: item.ChapterId,
          label: item.ChapterName + '(' + item.IDNum + ')',
          children: this.getThirdData(item.Third)
        })
      })
      return nodes
    },
    getThirdData(Arr) {
      let Result = []
      Arr.forEach(i => {
        Result.push({
          value: i.ChapterId,
          label: i.ChapterName + '(' + i.IDNum + ')'
        })
      })
      return Result
    },
    getChangeChapter(val) {
      this.formInline.ChapterTarget = val.pop()
    },
    //学习水平列表
    async GetExamineList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetExamineList')
      if (res.data.Success) {
        this.GetExamineOptions = res.data.Data
      }
    },

    GetExamine(val) {
      this.formInline.Examine = val
    },

    //难度等级
    async GetItemLevelList() {
      const res = await this.$uwonhttp.post('/ErrorAnalysis/ManageErrorAnalysis/GetItemLevelList')
      if (res.data.Success) {
        this.GetItemLevelOptions = res.data.Data
      }
    },

    GetItemLevel(val) {
      this.formInline.Level = val
    },

    //内容领域
    async GetPlateTypeList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetPlateTypeList')
      if (res.data.Success) {
        this.GetPlateTypeOptions = res.data.Data
      }
    },

    GetPlateType(val) {
      this.formInline.PlateTypeId = val
    },

    //获取关键能力板块
    async GetKeyCapabilitiesList() {
      const res = await this.$uwonhttp.get('/report/TeacherDeepAnalysis/GetKeyCapabilitiesList')
      if (res.data.Success) {
        this.KeyCapabilitOptions = res.data.Data
      }
    },

    GetKeycapa(val) {
      this.formInline.Keycapa = val
    },

    GetconditionVal(val) {
      this.formInline.condition = val
    },

    //获取题型列表
    async GetExamItemTypeList() {
      const res = await this.$uwonhttp.get('/ExamItem/ExamItem/GetExamItemTypeList')
      if (res.data.Success) {
        this.TypeOptions = res.data.Data
      }
    },

    GetTypeId(val) {
      this.GetTypeId = val
    },

    handleSizeChange(val) {
      this.formInline.PageRows = val
    },

    handleCurrentChange(val) {
      this.formInline.PageIndex = val
    },

    async onSubmit() {
      this.loading = true
      let params = {
        ...this.formInline
      }
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAdaptiveItemList', params)
      if (res.data.Success) {
        this.loading = false
        this.tableData = res.data.Data
      }
    },

    async Choose_topic() {
      this.table = true
      this.search = ''
      await this.GetItemAdaptiveDataList()
    },

    async GetItemAdaptiveDataList() {
      this.loading = true
      let params = {
        ...this.formInline
      }
      delete params.PageIndex
      delete params.PageRows
      const res = await this.$uwonhttp.post('/ExamItemAdaptive/ExamItemAdaptive/GetItemAdaptiveDataList', params)
      if (res.data.Success) {
        this.loading = false
        this.gridData = res.data.Data
        this.gridData.forEach(item => {
          const reg = /(#&\d+@)/g
          const inputele = ' '
          const stem = item.Title.replace(reg, inputele)
          item.Title = stem
        })
      }
    },

    handleInput() {
      let result = []
      this.gridData.forEach(item => {
        if (item.Title.indexOf(this.search) > -1) {
          result.push(item)
        }
        if (this.search == '') {
          this.GetItemAdaptiveDataList()
        }
      })
      this.gridData = result
    },

    handleSelectionChange(val) {
      this.multipleSelection = val
    },

    onReset() {
      this.formInline = {}
      this.formInline.Grade = '三上'
      this.formInline.GradeTerm = '3_1'
      this.formInline.PageRows = 8
      this.onSubmit()
    },

    query_submit() {
      this.table = false
      this.tableData = this.multipleSelection
    }
  }
}
</script>
<style lang="less" scoped>
.Page_box {
  width: 100%;
  padding: 23px;
  background: #ffff;
  /deep/.el-pagination {
    text-align: center;
    margin-top: 20px;
  }
}
/deep/.el-drawer__header {
  font-size: 23px;
  padding: 10px;
  margin-bottom: 0;
  color: #1b1b1b;
  border-bottom: 1px solid #e8e8e8;
}
/deep/ .el-drawer__body {
  overflow-y: scroll;
  margin-bottom: 70px;
  padding: 20px;
}
/deep/.demo-drawer__footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-top: 1px solid #e8e8e8;
  padding: 10px;
  text-align: right;
  background-color: white;
}
</style>    
