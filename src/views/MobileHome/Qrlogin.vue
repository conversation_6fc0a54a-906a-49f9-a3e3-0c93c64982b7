<template>
  <div class="con_login" data-title="登录">
    <div class="con_box">
      <div class="all">
        <!-- <img class="leftImg" src="@/assets/cpmpany/二维码登录/信息.png" alt="" /> -->
        <!-- <img v-lazy="'/static/img/'"> -->
      </div>
      <div class="login_info">
        <div class="top_title">
          <span>手机扫码</span>
          <span>安全登录</span>
        </div>
        <div class="info_app">
          <span class="app_p1">使用专课专练APP扫码登录</span>
          <span class="app_p2">扫码帮助</span>
          <div class="show_img">
            <!-- <img class="imgs1" @mouseover="channglogi" @mouseleave="mouleChageLogin" src="../../assets/cpmpany/二维码登录/编组.png" alt="" />
            <img class="imgs2" v-if="islogin" src="../../assets/cpmpany/二维码登录/弹窗／扫码帮助.png" alt=""> -->
          </div>
        </div>
        <div class="QRLogin">
          <img v-lazy="'data:image/png;base64,' + qrUrl" />
        </div>
        <div class="login_bot" style="text-align: center">
          <span class="bottom loginb">
            <QrLogin></QrLogin>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import QrLogin from '@/components/companyHead/ComQRLogin'
import TokenCache from '@/utils/cache/TokenCache'
export default {
  data() {
    return {
      qrUrl: '',
      islogin: false
    }
  },
  components: {
    QrLogin
  },
  created() {
    this.makeGRLogin()
  },
  methods: {
    channglogi() {
      this.islogin = true
    },
    mouleChageLogin() {
      this.islogin = false
    },
    makeGRLogin() {
      this.$uwonhttp.get('/User/Login/LogiQR', {}).then(resJson => {
        if (resJson.data.Success) {
          this.qrUrl = resJson.data.Data
          console.log(this.qrUrl, 'this.qrUrl')
        } else {
        }
      })
    },
    login(url, mobilephone, passwordCheck) {
      this.$http.post(url, { userName: mobilephone, password: passwordCheck }).then(res => {
        if (res.Success) {
          // 保存token
          TokenCache.setToken(res.Data)
          if (values && values.remember) {
            localStorage.setItem('passWord', passwordCheck)
          } else {
            localStorage.removeItem('mobilePhone')
            localStorage.removeItem('passWord')
          }
          localStorage.setItem('mobilePhone', mobilephone)
          this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: mobilephone }).then(res => {
            localStorage.setItem('SchoolId', res.Data.User.SchoolId)
            console.log('QrLogin.vue')
            if (!res.Success) {
              this.format = true
              this.PromptText = res.Msg
            }
            if (!res.Data.Roles) {
              this.isStudent = '1'
              localStorage.setItem('isStudent', this.isStudent)
              localStorage.removeItem('isTeacher')
            } else {
              this.isTeacher = res.Data.Roles.some(item => {
                return item.RoleName === '教师'
              })
              localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
              localStorage.setItem('isTeacher', this.isTeacher)
              localStorage.removeItem('isStudent')
              localStorage.setItem('role', res.Data.Roles[0].Id)
            }
            if (res.Data.Roles) {
              if (
                res.Data.Roles.some(item => {
                  return item.RoleName === '校领导'
                })
              ) {
                this.isSchoolHeader = true
                localStorage.setItem('isSchoolHeader', this.isSchoolHeader)
                localStorage.removeItem('isTeacher')
                localStorage.removeItem('isTeachingStaff')
              }
            }
            this.id = res.Data.UserType
            localStorage.setItem('Id', res.Data.UserType)
            localStorage.setItem('UserId', res.Data.User.Id)
            localStorage.setItem('IsVip', res.Data.IsVip)
            this.$message.success('登录成功')
            this.$router.push({
              path: '/Home/Introduce',
              query: {
                id: this.id
              }
            })
          })
        } else {
          this.loginErrorIsShow = true
          this.loginErrText = res.Msg
        }
      })
    },
    initWebSocket() {
      // 初始化weosocket
      const baseurl = this.$rootUrl1.replace('http://', '').replace('https://', '')
      var scheme = document.location.protocol === 'https:' ? 'wss' : 'ws'
      const wsuri = scheme + '://' + baseurl + '/ws/qrscanlogin'

      this.websock = new WebSocket(wsuri)
      this.websock.onmessage = this.websocketonmessage
      this.websock.onopen = this.websocketonopen
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    websocketonopen() {
      // 连接建立之后执行send方法发送数据
      const actions = { scanlogin: 'conn' }
      this.websocketsend(JSON.stringify(actions))
    },
    websocketonerror() {
      // this.initWebSocket()
    },
    websocketonmessage(e) {
      const redata = JSON.parse(e.data)
      console.log(redata, 'redata')

      // 数据接收
      if (redata.state === 1) {
        TokenCache.setToken(redata.token)
        if (redata.userType === 1) {
          // 教师登录
          this.login('/Base_Manage/Home/TeacherScanQrLogin', redata.phone, redata.t)
        } else if (redata.userType === 2) {
          // 学生登录
        }
      }
    },
    websocketsend(Data) {
      // 数据发送
      this.websock.send(Data)
    },
    websocketclose(e) {
      // this.initWebSocket()
    }
  }
}
</script>
<style lang="less" scoped>
.con_login {
  position: relative;
  // max-width: 750px;
  // min-width: 320px;
  height: 100%;
  margin: auto;
  // overflow: hidden;
  box-sizing: border-box;
  // background-image: url('../../assets/cpmpany/二维码登录/背景.png');
  background-repeat: no-repeat;
  .con_box {
    display: flex;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .all {
      .leftImg {
        width: 600px;
        //  height: 200px;
      }
    }
    .login_info {
      width: 1000px;
      height: 1131px;
      background: #fff;
      .top_title {
        display: flex;
        justify-content: center;
        margin-top: 50px;
        span {
          font-size: 40px;
          font-weight: 600;
          color: #404040;
          font-family: PingFangSC-Medium, PingFang SC;
          margin-right: 50px;
        }
      }
      .info_app {
        margin-top: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        .app_p1 {
          font-size: 34px;
          color: #787878;
          margin-right: 40px;
        }
        .app_p2 {
          font-size: 34px;
          color: #38c2cc;
        }
        .show_img {
          position: relative;
          .imgs1 {
            width: 34px;
            height: 34px;
            margin-left: 5px;
          }
          .imgs2 {
            position: absolute;
            top: -332px;
            right: -10px;
            width: 400px;
          }
        }
      }
      .QRLogin {
        display: flex;
        justify-content: center;
        margin-top: 15px;
      }
      .login_bot {
        // margin-top: 15px;
        span {
          cursor: pointer;
          font-size: 42px;
        }
      }
    }
  }
}
</style>
