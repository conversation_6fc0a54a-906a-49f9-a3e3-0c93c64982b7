<template>
  <div class="con_index" data-title="首页">
    <Header></Header>
    <div class="nav_img">
      <!-- <img src="../../assets/cpmpany/编组 12.png" alt="" /> -->
    </div>
    <div class="ridu"></div>
    <div class="ridu1"></div>
    <!-- <div class="ridu2"></div> -->
    <div class="index_title">
      <div class="title1">专课专练&nbsp;&nbsp;实证教学天天见</div>
      <div class="title2">注重教学管理 为教师赋能</div>
      <!-- <div class="title_logo">
        <Healogin class="login_min"> </Healogin>
      </div> -->
      <div class="title3">
        专课专练是专门为学校教学管理打造的在线教育品牌，运用认知科学和智能技术为师生提供在线微课，智能练习，问题解决，学情分析等多样化的智能教育服务。我们长期致力于系统性地推动教育均衡发展，让每个孩子都能获得健康成长
      </div>
      <!-- <div class="title_btn" @click="toQRLogin">扫码登录</div> -->
    </div>
    <!-- <div class="image-d">
      <div class="image-inner"></div>
    </div> -->
    <div class="footer_title">
      <div class="title_img">
        <!-- <img src="@/assets/cpmpany/编组 2.png" alt="" /> -->
      </div>
      <div class="bot-div">
        <span class="topTitle">侧重个性化辅导</span>
        <p>基于数据管理和归因分析的逻辑将主题库资源按多级章节知识点</p>
        <p>体系重新归纳整理，生成海量题库资源，帮助学生自我提高。</p>
      </div>
    </div>
    <div class="footer_title">
      <div class="title_img">
        <!-- <img src="@/assets/cpmpany/编组 2.png" alt="" /> -->
      </div>
      <div class="bot-div">
        <span class="topTitle">数据采集学情图表</span>
        <p>通过教师与学生的行为数据智能生成学情图表，校级管理者，教</p>
        <p>研组长和学生家长可清晰查看教师与学生的教研与学习成果。</p>
      </div>
    </div>
    <div class="footer_title">
      <div class="title_img">
        <!-- <img src="@/assets/cpmpany/编组 2.png" alt="" /> -->
      </div>
      <div class="bot-div">
        <span class="topTitle">学生试卷智能批改</span>
        <p>基于智能识别系统，精准识别学生答题答案，系统自动判别试</p>
        <p>卷，减少教师批改作业时间。</p>
      </div>
    </div>
    <div class="footer_title">
      <div class="title_img">
        <!-- <img src="@/assets/cpmpany/编组 2.png" alt="" /> -->
      </div>
      <div class="bot-div">
        <span class="topTitle">数据致力精准教学管理</span>
        <p>通过教师与学生的行为数据智能生成学习报告，学生家长，教</p>
        <p>师，校级管理者可对照学生薄弱点实施精准教研。</p>
      </div>
    </div>
    <Footer class="ind_foot"></Footer>
  </div>
</template>
<script>
import Header from '@/components/Modilemod/Header'
import Footer from '@/components/Modilemod/Footer'
import Logins from '@/components/Modilemod/Modlogin'
import Healogin from '@/components/Modilemod/Healogin'
export default {
  data () {
    return {}
  },
  components: {
    Header,
    Logins,
    Footer,
    Healogin
  },
  methods: {
    toQRLogin () {
      this.$router.push('/MobileHome/Qrlogin')
    }
  }
}
</script>
<style lang="less" scoped>
body {
  width: 100%;
  height: 100%;
}
.con_index {
  position: relative;
  // max-width: 950px;
  // min-width: 320px;
  // height: 158%;
  margin: auto;
  overflow: hidden;
  background: #f2f2f2;
  box-sizing: border-box;
  .nav_img {
    img {
      width: 100%;
      margin-top: 15px;
    }
  }
  .ridu {
    position: absolute;
    top: 550px;
    right: 200px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: #ffbe55;
  }
  .ridu1 {
    position: absolute;
    top: 400px;
    // left: -59px;
    width: 59px;
    height: 118px;
    border-radius: 0 59px 59px 0;
    background: #8fe25b;
  }
  .ridu2 {
    position: absolute;
    bottom: 300px;
    right: 0;
    width: 59px;
    height: 118px;
    border-radius: 59px 0 0 59px;
    background: #ffbe55;
  }
  .index_title {
    // background-image: url('../../assets/cpmpany/编组 14.png');
    margin: 15px 20px 0 60px;
    .title1 {
      font-size: 60px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #262626;
    }
    .title2 {
      font-size: 52px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      margin-top: 20px;
      color: #262626;
    }
    .title_logo {
      margin: 20px 0 0 20px;
      width: 500px;
      height: 90px;
      background: #0099fa;
      box-shadow: 0px 10px 20px 0px #0099fa33;
      border-radius: 45px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: white;
      text-align: center;
      line-height: 93px;
      cursor: pointer;
    }
    .title3 {
      margin-top: 30px;
      font-size: 50px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #a9a9a9;
    }
    .title_btn {
      margin: 20px 0 0 20px;
      // font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: white;
      width: 500px;
      height: 90px;
      background: linear-gradient(135deg, #bba7fc 0%, #866ef8 100%);
      box-shadow: 0px 11px 20px 0px #9079f880;
      border-radius: 38px;
      text-align: center;
      line-height: 93px;
      cursor: pointer;
    }
  }
  .image-d {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 50%;
    height: 607px;
    position: relative;
    display: inline-block;
    vertical-align: top;
  }
  .footer_title {
    margin: 80px 0 0 170px;
    display: flex;
    .title_img {
      img {
        width: 100px;
        height: 100px;
        margin: 45px 20px 0 0;
      }
    }
    .bot-div {
      .topTitle {
        font-size: 54px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #292929;
        line-height: 40px;
      }
      p {
        font-size: 50px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }
  }
  .ind_foot {
    margin-top: 80px;
  }
}
</style>
