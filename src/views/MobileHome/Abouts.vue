<template>
  <div class="con_about" data-title="关于我们">
    <Header></Header>
    <div class="about_us">
      <div class="us_left">
        <div class="us_left_tit">关于我们</div>
        <div class="us_left_bor"></div>
        <div class="us_left_text">
          <p>有我公司是一家着力于基础教育领域的高科技公司，</p>
          <p>拥有多项发明专利和产品著作权证书，长期进行学科</p>
          <p>教学各环节的优化探索研究。公司具备优秀的技术团</p>
          <p>队，有优秀的一线师资团队进行业务指导，与各区中</p>
          <p>小学校保有长期合作。</p>
        </div>
      </div>
      <div class="compay">
        <div class="compay_title">公司简介</div>
        <div class="compay_img">
          <!-- <img src="../../assets/cpmpany/位图@2x.png" alt="" /> -->
        </div>
      </div>
      <div class="corpor">
        <div class="corpor_tit">企业文化</div>
        <div class="corpor_p">
          <p>有我是种精神：拒绝自我，追求忘我无我，力争在团队和用户心中实现</p>
          <p>"有我”。 有我是种态度：勇于接受挑战，面对困难与挑战，敢于大声说：</p>
          <p>有我！ 有我是信念与追求 ：英文名eduwon，追求与教育部门一起走向</p>
          <p>成功，实现教学“有我”更精彩。</p>
        </div>
      </div>
    </div>
    <Footer class="abo_foot"></Footer>
  </div>
</template>
<script>
import Header from '@/components/Modilemod/Header'
import Footer from '@/components/Modilemod/Footer'
export default {
  data () {
    return {}
  },
  components: {
    Header,
    Footer
  }
}
</script>
<style lang="less" scoped>
.con_about {
  // max-width: 750px;
  // min-width: 320px;
  // height: 100%;
  margin: auto;
  overflow: hidden;
  background: #f2f2f2;
  box-sizing: border-box;
  .us_left {
    margin: 150px 0 0 100px;
    .us_left_tit {
      height: 56px;
      font-size: 56px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #272727;
      line-height: 56px;
    }
    .us_left_bor {
      width: 220px;
      height: 6px;
      background: #38c2cc;
      margin: 8px 0 12px 0;
    }
    .us_left_text {
      p {
        font-size: 50px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #818181;
      }
    }
  }
  .compay {
    transform: translateX(23%);
    margin: 200px 0 50px 0;
    .compay_title {
      font-size: 62px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #292929;
      line-height: 62px;
    }
    .compay_img {
      margin-top: 40px;
      img {
        width: 70%;
      }
    }
  }
  .corpor {
    width: 100%;
    // background: #f8faf9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 100px;
    .corpor_tit {
      font-size: 62px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #292929;
      line-height: 62px;
      text-align: center;
    }
    .corpor_p {
      margin: 28px auto;
      p {
        font-size: 50px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #818181;
        line-height: 50px;
        margin-top: 18px;
      }
    }
  }
  .abo_foot {
    margin-top: 40px;
  }
}
</style>
