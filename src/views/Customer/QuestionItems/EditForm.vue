﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :maskClosable="false"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form">
        <a-form-item label="问题内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea
            v-decorator="['ItemContent', { rules: [{ required: true, message: '必填' }] }]"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="问题答案" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-textarea
            v-decorator="['ItemAnswer', { rules: [{ required: true, message: '必填' }] }]"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="问题类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            allowClear
            style="width: 300px"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="QuestionSubjectTreeData"
            placeholder="请选问题类型"
            treeDefaultExpandAll
            v-decorator="['QuestionSubjectId', { rules: [{ required: false }] }]"
          ></a-tree-select>
        </a-form-item>

        <a-form-item label="选择文件" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-upload
            name="file"
            :multiple="false"
            :action="uploadWordUrl"
            :headers="headers"
            :fileList="defaultFileList"
            @change="handleChange"
          >
            <a-button>
              <a-icon type="upload" />上传/图片/动态GIF/视频
            </a-button>
          </a-upload>
        </a-form-item>

        <a-form-item label="权重" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Sort', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      headers: {
        authorization: 'authorization-text'
      },
      uploadWordUrl: `${this.$rootUrl}/Base_Manage/Upload/UploadFileByForm`,
      entity: {},
      defaultFileList: [],
      QuestionSubjectTreeData: [],
      fileUrl: '',
      title: ''
    }
  },
  methods: {
    openForm(id, title) {
      //参数赋值
      this.title = title || '编辑表单'
      this.loading = true

      //组件初始化
      this.init()

      //编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()
          this.defaultFileList = []
          this.$http.post('/Customer/QuestionItems/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            
            if (resJson.Data.FileUrl != null && resJson.Data.FileUrl != '') {
              this.defaultFileList.push({
                uid: '1',
                name: resJson.Data.FileUrl,
                status: 'done',
                url: resJson.Data.FileUrl
              })
            }

            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.visible = true

      this.$http.post('/Customer/QuestionSubject/GetTreeDataList').then(resJson => {
        if (resJson.Success) {
          this.QuestionSubjectTreeData = resJson.Data
        }
      })
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())
          this.entity.FileUrl = this.fileUrl
          
          this.loading = true
          this.$http.post('/Customer/QuestionItems/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    handleChange(arg1) {
      
      let fileList = [...arg1.fileList];
      if (arg1.file.status == 'uploading') {
        this.defaultFileList = fileList
      }

      if (arg1.file.status == 'done') {
        this.fileUrl = arg1.file.response.url
        this.defaultFileList = fileList

      }
      if (arg1.file.status == 'removed') {
        this.fileUrl = ''
        this.defaultFileList = []
      }
    }
  }
}
</script>
