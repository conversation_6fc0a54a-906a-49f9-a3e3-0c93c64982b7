﻿<template>
  <a-card :bordered="false">
    <div class="table-operator">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()" :loading="loading">删除</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="ItemContent">内容</a-select-option>
                <a-select-option key="ItemAnswer">答案</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>

          <a-col :md="10" :sm="24">
            <a-form-item label="问题类型">
              <a-tree-select allowClear v-model="queryParam.questionsubjectid" style="width: 300px" :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }" :treeData="QuestionSubjectTreeData" placeholder="请选问题类型"
                treeDefaultExpandAll v-decorator="['QuestionSubjectId', { rules: [{ required: false }] }]"></a-tree-select>
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="24">
            <a-button type="primary" @click="getDataList">查询</a-button>
            <a-button style="margin-left: 8px" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :scroll="{ x: 1500, y: 600 }" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :pagination="pagination" :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true" size="small">
      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleUpper(record.Id,record.Upper==1?2:1)">{{record.Upper == 1 ?'下架':'上架'}}</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.Id],)">删除</a>
        </template>
      </span>

      <span slot="Resolved" slot-scope="text, record">
        <template>
          <a @click="showDetail(record.Id,1)">{{record.Resolved}}</a>
        </template>
      </span>

      <span slot="UnResolved" slot-scope="text, record">
        <template>
          <a @click="showDetail(record.Id,2)">{{record.UnResolved}}</a>
        </template>
      </span>
    </a-table>

    <a-drawer title="反馈记录" placement="right" :closable="false" @close="onClose" :visible="visible" :afterVisibleChange="afterVisibleChange">
      <p v-for="fbd in feddbackData" :key="fbd.Id">
        {{fbd.FeedBackState == 1 ?'已解决':'未解决'}}:
        {{fbd.CreateTime}}
      </p>

    </a-drawer>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'

const columns = [
  { title: '问题', dataIndex: 'ItemContent', align: 'center', width: 320, fixed: 'left' },
  { title: '问题类型', dataIndex: 'Subject', align: 'center', width: 120, fixed: 'left' },
  { title: '已解决', dataIndex: 'Resolved', align: 'center', width: 120, scopedSlots: { customRender: 'Resolved' } },
  {
    title: '未解决',
    dataIndex: 'UnResolved',
    align: 'center',
    width: 120,
    scopedSlots: { customRender: 'UnResolved' }
  },
  { title: '上下架状态', dataIndex: 'UpperStr', align: 'center', width: 120 },
  { title: '权重', dataIndex: 'Sort', align: 'center', width: 120 },
  // { title: '来源平台', dataIndex: 'SourcePlatformStr', width: '10%' },
  { title: '操作人', dataIndex: 'UserName', align: 'center', width: 120 },
  { title: '更新时间', dataIndex: 'LastChangeTime', align: 'center' },
  {
    title: '操作',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
    align: 'center',
    fixed: 'right',
    width: 220
  }
]

export default {
  components: {
    EditForm
  },
  created: function() {
    this.$http.post('/Customer/QuestionSubject/GetTreeDataList').then(resJson => {
      if (resJson.Success) {
        this.QuestionSubjectTreeData = resJson.Data
      }
    })
  },
  mounted() {
    this.getDataList()
  },
  data() {
    return {
      data: [],
      feddbackData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'Id', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      feedbackParam: {},
      QuestionSubjectTreeData: [],
      selectedRowKeys: [],
      rowClick: record => ({
        on: {
          click: () => {
            //this.showDetail()
          },
          dblclick: event => {}
        }
      }),
      visible: false
    }
  },

  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    getDataList() {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Customer/QuestionItems/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'Id',
          SortType: this.sorter.order,
          ...this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected() {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd() {
      this.$refs.editForm.openForm()
    },
    handleEdit(id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete(ids) {
      var thisObj = this
      this.$confirm({
        title: '确认删除吗?',
        onOk() {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/Customer/QuestionItems/DeleteData', { ids: JSON.stringify(ids) }).then(resJson => {
              resolve()
              if (resJson.Success) {
                thisObj.$message.success('操作成功!')
                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    },
    handleUpper(id, upper) {
      var thisObj = this
      this.$confirm({
        title: '确认修改?',
        onOk() {
          return new Promise((resolve, reject) => {
            thisObj.$http.post('/Customer/QuestionItems/Upper', { id: id, upper: upper }).then(resJson => {
              resolve()
              if (resJson.Success) {
                thisObj.$message.success('操作成功!')

                thisObj.getDataList()
              } else {
                thisObj.$message.error(resJson.Msg)
              }
            })
          })
        }
      })
    },
    afterVisibleChange(val) {},
    showDetail(id, state) {
      this.visible = true
      this.feedbackParam.questionId = id
      this.feedbackParam.state = state
      this.$http
        .post('/Customer/QuestionFeedBack/GetResolvedDataList', {
          PageIndex: 1,
          PageRows: 20,
          SortField: 'CreateTime',
          SortType: 'desc',
          ...this.feedbackParam
        })
        .then(resJson => {
          this.feddbackData = resJson.Data
        })
    },
    onClose() {
      this.visible = false
    }
  }
}
</script>