<template>
  <a-form :from="form" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol">
    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="处理状态"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.ProcessStateStr}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" align="middle">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="反馈类型"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.FaultTypeStr}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" align="middle">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="用户名"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.CreatorName}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" align="middle">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="QQ"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.QQ}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" align="middle">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="微信"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.WeChat}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" align="middle">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="年级"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.Grade}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="反馈时间"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.CreateTime}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="反馈问题"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-form-item label>{{entity.FaultContent}}</a-form-item>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" v-if="entity.UploadFileType==2">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="附件视频"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <div v-for="u in fileUrls" :key="u">
          <video-player class="video-player vjs-custom-skin" ref="videoPlayer" :playsinline="true" :options="playerOptions"></video-player>
        </div>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex" v-show="entity.UploadFileType=1">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="附件图片"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <div v-for="u in fileUrls" :key="u">
          <img v-image-preview width="100px" height="50px" style="margin-bottom:10px" :src="u" />
        </div>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label></a-form-item>
      </a-col>
      <a-col :span="8" :order="2"></a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="解决方案"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-textarea v-model="suggestionReplyEntity.ReplyMsg" placeholder="回复" :autoSize="{ minRows: 4, maxRows: 5 }" />
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label="处理状态"></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-radio-group @change="onChange" v-model="entity.ProcessState">
          <a-radio :value="2">处理中</a-radio>
          <a-radio :value="3">已处理</a-radio>
        </a-radio-group>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label></a-form-item>
      </a-col>
      <a-col :span="8" :order="2">
        <a-button type="primary" html-type="submit" class="login-form-button">提交</a-button>
      </a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label></a-form-item>
      </a-col>
      <a-col :span="8" :order="2"></a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>

    <a-row type="flex">
      <a-col :span="8" :order="1"></a-col>
      <a-col :span="2" :order="1">
        <a-form-item label></a-form-item>
      </a-col>
      <a-col :span="8" :order="2"></a-col>
      <a-col :span="6" :order="3"></a-col>
    </a-row>
  </a-form>
</template>

<style>
.ant-form-item-label label {
  font-weight: bold;
}
</style>

<script>
export default {
  data() {
    return {
      id: {},
      entity: {},
      suggestionReplyEntity: {},
      processState: 0,
      fileUrls: [],
      videoUrl: '',
      form: this.$form.createForm(this),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 12 }
      },
      ProcessStateArray: ['', '待处理', '待处理', '已经处理'],
      FaultTypeArray: ['', '性能故障', '功能异常', '产品建议', '其他反馈'],
      playerOptions: {
        playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
        autoplay: false, //如果true,浏览器准备好时开始回放。
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [
          {
            type: 'video/mp4', //这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
            src: '' //url地址
          }
        ],
        poster: '../../static/images/test.jpg', //你的封面地址
        // width: document.documentElement.clientWidth, //播放器宽度
        notSupportedMessage: '此视频暂无法播放，请稍后再试', //允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: true,
          durationDisplay: true,
          remainingTimeDisplay: false,
          fullscreenToggle: true //全屏按钮
        }
      }
    }
  },
  watch: {
    $route() {
      this.init()
    }
  },
  created: function() {
    this.init()
  },
  computed: {
    player() {
      return this.$refs.videoPlayer.player
    }
  },
  methods: {
    init() {
      this.fileUrls = []
      var id = this.$route.query.id
      if (id) {
        this.$nextTick(() => {
          this.id = id
          this.$http.post('/Customer/Suggestion/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            if (resJson.Data.UploadFileUrl) {
              this.fileUrls = resJson.Data.UploadFileUrl.split(',')
              if (resJson.Data.UploadFileType == 2) {
                // 1：图片 2 :视频
                this.playerOptions.sources[0].src = this.fileUrls[0]
              }
            }
            this.processState = resJson.Data.ProcessState
            this.entity.ProcessStateStr = this.ProcessStateArray[resJson.Data.ProcessState]
            this.entity.FaultTypeStr = this.FaultTypeArray[resJson.Data.FaultType]
          })
        })
      } else {
        this.loading = false
      }

      if (id) {
        this.$nextTick(() => {
          this.$http.post('/Customer/SuggestionReply/GetDatasBySuggesionId', { id: id }).then(resJson => {
            if (resJson.Data.length <= 0) {
              this.suggestionReplyEntity.ReplyMsg = ''
              return
            }

            this.suggestionReplyEntity = resJson.Data[0]
          })
        })
      } else {
        this.loading = false
      }
    },
    handleSubmit(e) {
      e.preventDefault()
      this.suggestionReplyEntity = Object.assign(this.suggestionReplyEntity, this.form.getFieldsValue())
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.suggestionReplyEntity = Object.assign(this.suggestionReplyEntity, this.form.getFieldsValue())
          this.suggestionReplyEntity.SuggestionId = this.entity.Id
          this.$http.post('/Customer/SuggestionReply/SaveData', this.suggestionReplyEntity).then(resJson => {
            if (resJson.Success) {
              this.updateProcessState()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    updateProcessState() {
      this.$http
        .post('/Customer/Suggestion/UpdateProcess', { id: this.entity.Id, processState: this.processState })
        .then(resJson => {
          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.$router.push({ path: '/Customer/Suggestion/List' })
          } else {
            this.$message.error(resJson.Msg)
          }
        })
    },
    onChange(e) {
      this.processState = e.target.value
    }
  }
}
</script>