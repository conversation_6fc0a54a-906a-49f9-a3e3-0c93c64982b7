﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form">
        <a-form-item label="反馈问题/建议" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['FaultContent', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="反馈类型 1 性能故障  2 功能异常 3 产品建议 4 其他反馈" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['FaultType', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="上传文件类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['UploadFileType', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="上传视频Url地址" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['UploadFileUrl', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="手机号码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Phone', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="wechat" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['WeChat', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="平台来源 1 学生端 2 教师端 3 管理端" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['SourcePlatform', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="客服处理状态 1 待处理 2 已经处理" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['ProcessState', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="处理速度评分" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['ProcessingSpeedScore', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="态度评分" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['AttitudeScore', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="处理人" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['ProcessingAdminId', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="UserReplyMsg" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['UserReplyMsg', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="处理时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['UserReplyDateTime', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      entity: {},
      title: ''
    }
  },
  methods: {
    openForm(id, title) {
      //参数赋值
      this.title = title || '编辑表单'
      this.loading = true

      //组件初始化
      this.init()

      //编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()

          this.$http.post('/Customer/Suggestion/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.visible = true
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())

          this.loading = true
          this.$http.post('/Customer/Suggestion/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
