<template>
  <div id="maxout">
    <div ref="maxout" style="width: 90%; margin-left: 5%">
      <h3 style="text-align: center">专课专练问卷调查</h3>
      <p>
        同学们，你们好！为了更好地了解同学们的数学学习习惯，帮助同学们更好地学习数学，掌握基础的数学知识和技能，编制了此次的问卷。问卷的统计结果没有任何负面影响，请同学们根据自己的实际情况如实填写，谢谢！
      </p>
      <p>1.在做专课专练上的计算题时，我会</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value" @change="onChange(1)">
          <a-radio :value="1"> A 逐题打草稿 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 心算不出时才打草稿 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 不打草稿 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 其他 </a-radio>
        </a-radio-group>
        <div style="margin: 24px -10px" />
        <a-input class="a-inp" v-show="value == 4" v-model="othervalue1" />
        <br /><br />
      </div>

      <p>2.在做专课专练上的题目时，我一般读题会</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value1" @change="onChange(2)">
          <a-radio :value="1"> A 粗略地读1遍 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 认真读1-2遍 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 读3遍或3遍以上 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 其他 </a-radio>
        </a-radio-group>
        <div style="margin: 24px 0" />
        <a-input class="a-inp" v-show="value1 == 4" v-model="othervalue2" />
        <br /><br />
      </div>

      <p>3.在做专课专练上的题目遇到困难时，我会</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value2" @change="onChange(3)">
          <a-radio :value="1"> A 借助线段图或画草图等方法独立思考 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 请教家长 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 空着不做 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 其他 </a-radio>
        </a-radio-group>
        <div style="margin: 24px 0" />
        <a-input class="a-inp" v-model="othervalue3" v-show="value2 == 4" />
        <br /><br />
      </div>

      <p>4. 在完成专课专练上的某张试卷后，我会</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value3" @change="onChange(4)">
          <a-radio :value="1"> A 自己检查完以后再提交 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 家长检查后再提交 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 直接提交 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 其他 </a-radio>
        </a-radio-group>
        <div style="margin: 24px 0" />
        <a-input class="a-inp" v-model="othervalue4" v-show="value3 == 4" />
        <br /><br />
      </div>

      <p>5. 在专课专练提交练习后，知道有错误的地方，我会</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value4" @change="onChange(5)">
          <a-radio :value="1"> A 及时订正错题 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 等家长辅导后再订正 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 不订正 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 其他 </a-radio>
        </a-radio-group>
        <div style="margin: 24px 0" />
        <a-input class="a-inp" v-model="othervalue5" v-show="value4 == 4" />
        <br /><br />
      </div>

      <p>6. 你喜欢“专课专练”这种手机作业形式吗？</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value5" @change="onChange(6)">
          <a-radio :value="1"> A 很喜欢 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 比较喜欢 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 一般 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 不喜欢 </a-radio>
        </a-radio-group>
        <br /><br /><br /><br />
      </div>

      <p>7. 你认为“专课专练”上的微课对你的学习是否有帮助？</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value6" @change="onChange(7)">
          <a-radio :value="1"> A 很有帮助 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 有些帮助 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 较少帮助 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 没有帮助 </a-radio>
        </a-radio-group>
        <br /><br /><br /><br />
      </div>

      <p>8.你认为“专课专练”上的评价是否对你有激励作用？</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value7" @change="onChange(8)">
          <a-radio :value="1"> A 很能激励我 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 比较能激励我 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 不太激励我 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 无法激励我 </a-radio>
        </a-radio-group>
        <br /><br /><br /><br />
      </div>

      <p>9.你会主动观看“专课专练”平台上的微课吗？</p>
      <div class="call-radio-d">
        <a-radio-group v-model="value8" @change="onChange(9)">
          <a-radio :value="1"> A 经常看 </a-radio>
          <br /><br />
          <a-radio :value="2"> B 有时看 </a-radio>
          <br /><br />
          <a-radio :value="3"> C 偶尔看 </a-radio>
          <br /><br />
          <a-radio :value="4"> D 从来不看 </a-radio>
        </a-radio-group>
        <br /><br /><br /><br />
      </div>

      <p>10.你最喜欢“专课专练”平台上哪些功能？简述理由</p>
      <div class="call-radio-d">
        <a-textarea v-model="question10" :auto-size="{ minRows: 4, maxRows: 5 }" />
        <br /><br /><br /><br />
      </div>

      <p>11.请你对专课专练提些建议</p>
      <div class="call-radio-d">
        <a-textarea v-model="question11" :auto-size="{ minRows: 4, maxRows: 5 }" />
        <br /><br />
      </div>

      <div style="width: 100%">
        <input type="button" value="提交" class="button-inp" @click="submit" />
      </div>
    </div>
    <div style="width:90%;height:100px;display:none;background-color:white;" ref="hidediv">
      <p style="margin-left:45%;margin-top:100px;font-size:25px">提交成功</p>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      value: '',
      value1: '',
      value2: '',
      value3: '',
      value4: '',
      value5: '',
      value6: '',
      value7: '',
      value8: '',
      othervalue1: '',
      othervalue2: '',
      othervalue3: '',
      othervalue4: '',
      othervalue5: '',
      question10: '',
      question11: '',
      data: [],
      advice: [],
      userid: ''
    }
  },
  created() {
    this.userid = this.$route.query.userid
  },
  methods: {
    onChange(i) {
      var questId = parseInt(event.target.value)
      var option
      var area = ''
      if (i === 1) {
        area = this.othervalue1
      } else if (i === 2) {
        area = this.othervalue2
      } else if (i === 3) {
        area = this.othervalue3
      } else if (i === 4) {
        area = this.othervalue4
      } else if (i === 5) {
        area = this.othervalue5
      }
      if (questId == 1) {
        option = 'A'
      } else if (questId == 2) {
        option = 'B'
      } else if (questId == 3) {
        option = 'C'
      } else {
        option = 'D'
      }

      var idx = -1
      for (let x = 0; x < this.data.length; x++) {
        let item = this.data[x]
        var b = item.QuestionId === i
        if (b) {
          idx = x
          break
        }
      }
      if (idx < 0) {
        this.data.push({
          QuestionId: i,
          UserId: this.userid,
          Option: option,
          Content: area,
          QuestionTypeId: questId
        })
      } else {
        this.data[idx].Option = option
        this.data[idx].Content = area
      }
    },

    submit() {
      if (this.value == '') {
        this.$message.error('请选择第一题')
        return
      }
      if (this.value1 == '') {
        this.$message.error('请选择第二题')
        return
      }
      if (this.value2 == '') {
        this.$message.error('请选择第三题')
        return
      }
      if (this.value3 == '') {
        this.$message.error('请选择第四题')
        return
      }
      if (this.value4 == '') {
        this.$message.error('请选择第五题')
        return
      }
      if (this.value5 == '') {
        this.$message.error('请选择第六题')
        return
      }
      if (this.value6 == '') {
        this.$message.error('请选择第七题')
        return
      }
      if (this.value7 == '') {
        this.$message.error('请选择第八题')
        return
      }
      if (this.value8 == '') {
        this.$message.error('请选择第九题')
        return
      }
      this.data[0].Content = this.othervalue1
      this.data[1].Content = this.othervalue2
      this.data[2].Content = this.othervalue3
      this.data[3].Content = this.othervalue4
      this.data[4].Content = this.othervalue5
      this.advice.push(
        {
          Content: this.question10,
          UserId: this.userid,
          Type: '1',
          QuestingId: '10',
          QuestionTypeId: '0'
        },
        {
          Content: this.question11,
          Type: '2',
          QuestingId: '11',
          QuestionTypeId: '0'
        }
      )
      this.$uwonhttp
        .post('/question/Question/Submit', {
          questionJson: JSON.stringify(this.data),
          suggestJson: JSON.stringify(this.advice)
        })
        .then(resJson => {
          if (resJson.data.Success) {
            this.$message.success('操作成功!')
          } else {
            this.$message.error(resJson.data.Msg)
          }
        })
      // var questinganswe10=this.question10
      // var questinganswe11=this.question11

      this.$refs.maxout.style.display = 'none'
      this.$refs.hidediv.style.display = 'block'
    }
  }
  // [{"Content":"","Type":"1","UserId":"","QuestionId":0,"QuestionTypeId":0},{"Content":"","Type":"2","UserId":"","QuestionId":0,"QuestionTypeId":0}]
}
</script>
<style lang="less" scoped>
.call-radio-d {
  margin-left: 20px;
}
.button-inp {
  width: 100px;
  margin-left: 40%;
  margin-bottom: 50px;
}
.a-inp {
  height: 100px;
}
</style>