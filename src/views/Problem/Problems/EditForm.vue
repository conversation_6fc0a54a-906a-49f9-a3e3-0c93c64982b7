﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :maskClosable="false"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form">
        <!-- <a-form-item label="问题编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['ProblemId', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item> -->
        <a-form-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Title', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="内容" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Contens', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="图片组" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Pictures', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <!-- <a-form-item label="学生编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['StudentId', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item> -->
        <!-- <a-form-item label="问题类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['ProblemType', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item> -->
         <a-form-item label="问题类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            allowClear
            showSearch
            treeNodeFilterProp="title"
            style="width: 300px"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="ProblemTypeIdTreeData"
            placeholder="请问题类型"
            treeDefaultExpandAll
            v-decorator="['ProblemType', { rules: [{ required: false }] }]"
          ></a-tree-select>
        </a-form-item>
        <a-form-item label="年级" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Grade', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <!-- <a-form-item label="加密显示" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['IsEncrypt', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item> -->
        <a-form-item label="浏览数" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['PV', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <!-- <a-form-item label="是否删除 " :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Deleted', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item> -->
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      entity: {},
      title: '',
      ProblemTypeIdTreeData:[]
    }
  },
  methods: {
    openForm(id, title) {
      //参数赋值
      this.title = title || '编辑问题信息'
      this.loading = true

      //组件初始化
      this.init()
     
      //编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()

          this.$http.post('/Problem/Problems/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.visible = true
      this.$http.post('/Base_Manage/Base_DictData/GetTreeDataList',{parentId:this.UwooEnumData.EnumList.ProblemTypeId}).then(resJson=>{ 
          if(resJson.Success)
          {
            this.ProblemTypeIdTreeData = resJson.Data              
          }
          })  
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())

          this.loading = true
          this.$http.post('/Problem/Problems/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              //this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
