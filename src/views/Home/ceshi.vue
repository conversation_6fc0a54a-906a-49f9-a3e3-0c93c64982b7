<template>
  <ul class="sort-ul">
    <div>45454</div>
    <draggable group="article" :value="sortArr" @input="handleListChange($event)">
      <li v-for="(item,index) in sortArr" :key="index">
        <h2>{{ item.title }}</h2>
      </li>
    </draggable>
  </ul>
</template>
<script>
import Draggable from 'vuedraggable'

export default {
  name: 'Test',
  props: {},
  data() {
    return {
      fileList: [],
      sortArr: [
        { title: '00' },
        { title: '01' },
        { title: '02' },
        { title: '03' },
        { title: '04' },
        { title: '05' },
        { title: '06' },
        { title: '07' },
        { title: '08' },
        { title: '09' }
      ]
    }
  },
  components: {
    Draggable
  },
  methods: {
    // 更新位置
    handleListChange(event) {
      this.sortArr = event
    }
  },
  mounted() {}
}
</script>
<style>
/* ul{
    padding: 0;
    width: 400px;
  }

  li{
    width: 100px;
    float:left;
  } */
</style>
