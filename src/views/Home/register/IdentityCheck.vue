<template>
  <div class="identity">
    <!-- 注册主要 -->
    <div class="register-info clearfix">
      <div class="register-left fl">
        <p><img @click="goBack" class="img" src="@/assets/login/编组.png" alt="" /></p>
        <p style="font-size: 18px; text-align: center">请选择您的身份</p>
        <p style="text-align: center"><img src="@/assets/login/直线 15.png" alt="" /></p>
        <div class="identity-img">
          <p @click="toStudentRegister">
            <img class="m-r" src="@/assets/login/学生头像.png" alt="" /><span>学生</span>
          </p>
          <p @click="toTeacherRegister">
            <img class="m-r" src="@/assets/login/老师头像.png" alt="" /><span>老师</span>
          </p>
          <!-- <p @click="toManageRegister"><img class="m-r m-l" src="@/assets/login/管理者头像.png" alt=""><span>管理者</span></p> -->
        </div>
      </div>
      <div class="register-right-new fg">
        <div>
          <p class="pfont">
            <span>
              <img
                v-show="this.showYpJy === 1"
                style="width: 25px; margin-right: 5px"
                src="@/assets/logo/yp-logo.png"
                alt=""
              />
            </span>
            <webName :class="{ 'f-s': this.showYpJy === 1 }" :realmName="realmName" :id="1"></webName
            ><span v-show="this.showYpJy !== 1" class="span"></span>
            <!-- <span v-show="this.showYpJy !== 1">打造一流教育</span><br /> -->
            <span v-show="this.showYpJy === 1" class="m-t">精准练习，提质增效</span>
          </p>
          <p class="fontSiz data-p">
            <img class="data-img" src="@/assets/logo/1x.png" alt="" />
            <span>大数据分析辅助</span>
          </p>
          <p class="fontSiz data-cen-p">
            <img class="data-img" src="@/assets/logo/1x (2).png" alt="" />
            <span>助力个性化学习</span>
          </p>
          <p class="fontSiz">
            <img class="data-img" src="@/assets/logo/2x2.png" alt="" />
            <span>助教研决策分析</span>
          </p>
        </div>
      </div>
      <!-- <div v-if="showAddress" class="register-right fg"></div>
      <div v-if="showDefalut" class="register-right fg"></div>
      <div v-if="showHp" class="register-right-hp fg"></div>
      <div v-if="showPt" class="register-right-pt right-defalut fg"></div>
      <div v-if="showSj" class="register-right-sj right-defalut fg"></div>
      <div v-if="showYp" class="register-right-yp right-defalut fg"></div> -->
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import webName from '@/components/WebsiteName/WebsiteName'
export default {
  components: {
    webName,
  },
  created() {
    this.realmName = window.location.href.split('/')[2]
    if (this.realmName === this.Facturl.$hrefName) {
      this.showAddress = true
    } else if (this.realmName === this.Facturl.$hrefHpName) {
      this.showHp = true
    } else if (this.realmName === this.Facturl.$hrefPtName) {
      this.showPt = true
    } else if (this.realmName === this.Facturl.$hrefSjName) {
      this.showSj = true
    } else if (this.realmName === this.Facturl.$hreYpaName) {
      this.showYp = true
      this.showYpJy = 1
    } else {
      this.showDefalut = true
      this.showYpJy = 0
    }
  },
  data() {
    return {
      showSj: false,
      showYp: false,
      showYpJy: 0,
      showPt: false,
      showDefalut: false,
      showHp: false,
      showAddress: false,
      realmName: '',
    }
  },
  methods: {
    // 教师注册
    toTeacherRegister() {
      this.$router.push({ path: '/Home/register/TeacherRegister' })
    },
    // 学生注册
    toStudentRegister() {
      this.$router.push({ path: '/Home/Register/StudentRegister' })
    },
    // 管理端注册
    toManageRegister() {
      this.$router.push({ path: '/Home/register/ManageRegister' })
    },
    // 上一页
    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="less" scoped>
.cur {
  cursor: pointer;
}
.m-r {
  margin-right: 5px;
}
.m-l {
  margin-left: 23px;
}
.f-s {
  font-size: 16px;
}

// 身份选择背景图
.identity {
  // position: fixed;
  width: 100%;
  height: 100%;
  background: url('../../../assets/login/welcome-banner.png') center center;
  background-size: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
// 注册内容
.register-info {
  width: 41%;
  height: 70%;
  // 注册-左
  .register-left {
    width: 60%;
    height: 100%;
    background-color: #fff;
    .img {
      margin: 24px 0 0 16px;
      cursor: pointer;
    }
    .identity-img {
      display: flex;
      height: 70%;
      flex-direction: column;
      /* justify-content: space-evenly; */
      justify-content: space-evenly;
      text-align: center;
      img {
        width: 88px;
        cursor: pointer;
      }
      span {
        font-size: 20px;
        cursor: pointer;
      }
    }
  }
  // 右侧
  .right-defalut {
    width: 40%;
    height: 100%;
    background-size: cover;
  }
  .register-right-new {
    position: relative;
    width: 40%;
    height: 100%;
    background: url('../../../assets/login/位图.png') center center;
    background-size: cover;
    div {
      width: 100%;
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #fff;
      .pfont {
        font-size: 18px;
        .span {
          position: relative;
          top: -3px;
          display: inline-block;
          width: 4px;
          height: 4px;
          border-radius: 4px;
          background-color: #fff;
        }
      }
      .fontSiz {
        font-size: 16px;
      }
      .data-p {
        margin-top: 100px;
      }
      .data-cen-p {
        margin: 20px 0;
      }
      .data-img {
        width: 28px;
      }
    }
  }
  // 注册-右
  .register-right {
    width: 40%;
    height: 100%;
    background: url('../../../assets/login/xh-sx.png') center center;
    background-size: cover;
  }
  .register-right-hp {
    width: 40%;
    height: 100%;
    background: url('../../../assets/login/hp-sx.png') center center;
    background-size: cover;
  }
  .register-right-pt {
    background: url('../../../assets/login/pt-sx.png') center center;
  }
  .register-right-sj {
    background: url('../../../assets/login/sj-sx.png') center center;
  }
  .register-right-yp {
    background: url('../../../assets/login/yp-sx2.png') center center;
  }
}
</style>
