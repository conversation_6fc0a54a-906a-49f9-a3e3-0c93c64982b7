<template>
  <div class="login">
    <!-- 登录内容 -->
    <div class="login-info">
      <div class="login-info-left fl">
        <p><img @click="goBack" class="img" style="cursor: pointer" src="@/assets/login/编组.png" alt="" /></p>
        <!-- 登录方式 -->
        <div class="login-mothod">
          <a-tabs default-active-key="1" @change="callback">
            <a-tab-pane key="1" tab="立即注册">
              <a-form id="components-form-demo-normal-login" :form="form" class="login-form" @submit="handleSubmit">
                <p v-show="format" class="format">{{ PromptText }}</p>
                <!-- <p v-show="handleUnregistered">手机号未注册</p> -->
                <p>手机号</p>
                <a-form-item has-feedback :validate-status="mobileSuc">
                  <a-input
                    v-decorator="['mobilephone', { rules: [{ required: true, message: '请填写手机号' }] }]"
                    placeholder=""
                    @keyup="mobilephoneValidate"
                  >
                  </a-input>
                </a-form-item>
                <p>密码</p>
                <a-form-item has-feedback :validate-status="passwordSuc">
                  <a-input
                    v-decorator="['passwordCheck', { rules: [{ required: true, message: '请输入密码' }] }]"
                    type="password"
                    placeholder=""
                    @keyup="passwordValidate"
                  >
                  </a-input>
                </a-form-item>
                <!-- 获取验证码 -->
                <a-form-item>
                  <div class="verification">
                    <input
                      @keyup="changeInput($event)"
                      v-model="Code"
                      type="tel"
                      maxlength="6"
                      v-if="canInputCode"
                      style="padding-left: 15px"
                    />
                    <input disabled="disabled" v-if="!canInputCode" />
                    <span :class="{ disabled: !canClick }" @click="countDown">{{ content }}</span>
                  </div>
                </a-form-item>
                <a-form-item>
                  <el-checkbox v-model="agree"></el-checkbox>
                  <span> 我已阅读并同意<b class="cur col" @click="serviceStatement">《服务协议及隐私声明》</b> </span>
                  <a-button type="primary" html-type="submit" :class="{ 'login-form-button': this.nowRegister }">
                    立即注册
                  </a-button>
                </a-form-item>
                <div class="register clearfix">
                  <p>
                    <span>已有账号</span> &nbsp;|&nbsp; <span style="color: #68bb97" @click="toUserLogin">去登录</span>
                  </p>
                </div>
              </a-form>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
      <div class="fg login-info-right">
        <div>
          <!-- <p>
            <span><img v-show="this.showYpJy === 1" style="width:25px;margin-right: 5px" src="@/assets/logo/yp-logo.png" alt=""></span>
            <webName :class="{'f-s': this.showYpJy === 1}" :realmName="realmName" :id="1"></webName> <span v-show="this.showYpJy !== 1" class="span"></span> 
            <span v-show="this.showYpJy !== 1">打造一流教育</span><br>
            <span v-show="this.showYpJy === 1" class="m-t">精准练习，提质增效</span>
          </p>
          <p>已有账号？</p>
          <p @click="toUserLogin">去登录</p> -->
          <XuHuiRight>
            <!-- <p class="acc-num">
               <slot >
              哈哈
            </slot>
            </p> -->
          </XuHuiRight>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import webName from '@/components/WebsiteName/WebsiteName'
import XuHuiRight from '@/components/XuHuiRight/XuHuiRight'
export default {
  components: {
    webName,
    XuHuiRight,
  },
  created() {
    this.realmName = window.location.href.split('/')[2]
    if (this.realmName === this.Facturl.$hreYpaName) {
      this.showYpJy = 1
    } else {
      this.showYpJy = 0
    }
    this.canClick = false
    this.UserName = this.$route.query.UserName
    this.SchoolId = this.$route.query.SchoolId
    this.ClassId = this.$route.query.ClassId
    this.role = this.$route.query.role
    this.Sex = this.$route.query.Sex
    this.StudyNo = this.$route.query.StudyNo
    this.Type = this.$route.query.Type
  },
  activated() {
    this.setValue()
  },
  data() {
    return {
      showYpJy: 0,
      realmName: '',
      // 名称
      UserName: '',
      // 学校id
      SchoolId: '',
      // 班级id
      ClassId: '',
      // 性别
      Sex: '',
      // 学号
      StudyNo: '',
      // 手机验证码
      Code: '',
      // Type值  0 教师注册  1 管理员注册
      Type: '',
      // 注册
      format: '',
      // 手机号状态
      mobileSuc: '',
      // 密码状态
      passwordSuc: '',
      // 提示文字
      PromptText: '',
      // 验证码
      content: '发送验证码',
      totalTime: 60,
      canClick: true, // 添加canClick,
      // 立即注册
      nowRegister: true,
      agree: '',
      canInputCode: false,
    }
  },
  watch: {},

  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'normal_login' })
  },
  methods: {
    // setValue () {
    //   this.form.setFieldsValue({
    //     mobilephone: ''
    //   })
    // },
    // tabs栏切换
    callback(key) {},
    // 服务声明
    serviceStatement() {
      // window.location.href = 'https://privacy.eduwon.cn/'
      window.open('https://privacy.eduwon.cn/')
    },
    // 提交
    handleSubmit(e) {
      e.preventDefault()
      const values = this.form.getFieldsValue()
      if (values.mobilephone === undefined) {
        this.format = true
        this.PromptText = '请先填写手机号码'
        return
      }
      if (!this.testMobilephone(values.mobilephone)) {
        this.format = true
        this.PromptText = '手机号格式不正确'
        return
      }
      if (values.passwordCheck === undefined) {
        this.format = true
        this.PromptText = '请先填写密码'
        return
      }
      if (!this.testPassWord(values.passwordCheck)) {
        this.format = true
        this.PromptText = '密码长度4到16位并且有字母或数字'
        return
      }
      if (this.Code === '') {
        this.format = true
        this.PromptText = '请输入验证码'
        return
      } else if (this.Code.length != 6) {
        this.format = true
        this.PromptText = '请输入6位手机验证码'
        return
      }
      if (!this.agree) {
        this.format = true
        this.PromptText = '请先阅读服务协议及隐私声明'
        return
      }
      this.form.validateFields((err, values) => {
        if (!err) {
          // if (this.role === '1') {
            this.$uwonhttp
              .post('/User/Login/PhoneBindUserInfo', {
                PhoneNo: values.mobilephone,
                Pwd: values.passwordCheck,
                UserName: this.UserName,
                SchoolId: this.SchoolId,
                ClassId: this.ClassId,
                Code: this.Code,
                Sex: this.Sex,
                StudyNo: this.StudyNo,
                IMEI: -1,
              })
              .then((res) => {
                if (res.data.Data.Code !== 0) {
                  this.format = true
                  this.PromptText = res.data.Data.Msg
                } else {
                  this.format = false
                  this.$message.success('注册成功')
                  this.$router.push({ path: '/Home/UserLogin' })
                  // setTimeout(() => {this.$router.push({ path: '/Home/UserLogin' })},1500)
                }
              })
          // } else if (this.role === '2') {
            // this.$uwonhttp
            //   .post('/User/Login/PhoneBindTeacherInfoTwo', {
            //     PhoneNo: values.mobilephone,
            //     Pwd: values.passwordCheck,
            //     UserName: this.UserName,
            //     SchoolId: this.SchoolId,
            //     ClassIds: this.ClassId,
            //     Code: this.Code,
            //     IMEI: -1,
            //     Type: this.Type,
            //   })
            //   .then((res) => {
            //     if (res.data.Success) {
            //       this.format = true
            //       this.PromptText = res.data.Data
            //       this.$message.success('注册成功')
            //       this.$router.push({ path: '/Home/UserLogin' })
            //     } else {
            //       this.format = true
            //       this.PromptText = res.data.Msg
            //     }
            //   })
          // }
        }
      })
    },
    onChange(e) {},
    testMobilephone(str) {
      const regex = /^1[13456789]\d{9}$/
      if (!regex.test(str)) {
        return false
      } else {
        return true
      }
    },
    testPassWord(str) {
      if(!str){
        return false
      }
      // const regex = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,16}$/
      const regex = /^[0-9A-Za-z]{4,16}$/
      if (!regex.test(str)) {
        return false
      } else {
        return true
      }
    },
    // 手机校验
    mobilephoneValidate(e) {
      // 主要就是添加一个对undefined和空串的判断
      if (e.target.value === '') {
        this.format = false
      } else {
        if (!this.testMobilephone(e.target.value)) {
          this.format = true
          this.PromptText = '手机号格式不正确'
          // 图标
          this.mobileSuc = ''
        } else {
          this.format = false
          // 图标
          this.mobileSuc = 'success'
        }
      }
      this.lightCode()
    },
    // 密码校验
    passwordValidate(e) {
      if (!this.testPassWord(e.target.value)) {
        this.format = true
        this.PromptText = '密码长度4到16位并且有字母或数字'
        this.passwordSuc = ''
        // 验证码
        // setTimeout(() => {
        //   this.format = false
        // }, 2000)
      } else {
        this.format = false
        // 图标
        this.passwordSuc = 'success'
      }
      this.lightCode()
    },
    //获取验证码是否点亮
    lightCode() {
      const values = this.form.getFieldsValue()
      let result_tel = this.testMobilephone(values.mobilephone)
      let result_pass = this.testPassWord(values.passwordCheck)
      if (result_tel && result_pass) {
        this.canClick = true
        this.canInputCode = true
      } else {
        this.canClick = false
        this.canInputCode = false
      }
    },
    // 获取手机验证码
    getPhoneCount(mobilephone) {
      this.$uwonhttp
        .get('/User/UserManager/GetPhoneNumCode', {
          params: {
            phoneNum: mobilephone,
          },
        })
        .then((res) => {})
    },
    // 验证码
    countDown() {
      if (!this.canClick && this.totalTime == 60) {
        return
      }
      if (!this.canClick && this.totalTime < 60) {
        return
      }
      this.canClick = false
      this.format = false
      const values = this.form.getFieldsValue()
      //不做验证
      this.getPhoneCount(values.mobilephone)
      this.content = `${this.totalTime}s重新获取`
      const clock = window.setInterval(() => {
        this.totalTime--
        this.content = `${this.totalTime}s重新获取`
        if (this.totalTime < 0) {
          window.clearInterval(clock)
          this.content = '重新发送验证码'
          this.totalTime = 60
          this.canClick = true
        }
      }, 1000)
      return;
      this.$uwonhttp
        .post('/TeacherUserCenter/TeacherPersonal/CheckUserExistByPhone', { phoneNum: values.mobilephone })
        .then((res) => {
          // if (res.data.Data.Exist === 1) {
          //   this.format = true
          //   this.PromptText = '手机号已注册'
          // } else {
          //   this.getPhoneCount(values.mobilephone)
          //   this.content = `${this.totalTime}s重新获取`
          //   const clock = window.setInterval(() => {
          //     this.totalTime--
          //     this.content = `${this.totalTime}s重新获取`
          //     if (this.totalTime < 0) {
          //       window.clearInterval(clock)
          //       this.content = '重新发送验证码'
          //       this.totalTime = 60
          //       this.canClick = true
          //     }
          //   }, 1000)
          // }
        })
    },
    // 验证码发生变化
    changeInput(e) {
      console.log(e.target.value)
      let val = e.target.value
      this.valiCode(val)
      if (val !== '') {
        this.nowRegister = false
      } else {
        this.nowRegister = true
      }
    },
    // 验证验证码格式
    valiCode(code) {
      if (code.length != 6) {
        this.format = true
        this.PromptText = '请输入6位手机验证码'
      } else {
        this.format = false
      }
    },
    // 跳转登录页
    toUserLogin() {
      this.$router.push({ path: '/Home/UserLogin' })
    },
    // 上一页
    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="less" scoped>
.col {
  color: #3cbb8f;
}
.cur {
  cursor: pointer;
}
.color-green {
  color: #68bb97;
}
.f-s {
  font-size: 16px;
}
.m-t {
  display: inline-block;
  margin-top: 20px;
  font-size: 24px;
}
.login {
  position: fixed;
  width: 100%;
  height: 100%;
  background: url('../../../assets/login/welcome-banner.png') center center;
  background-size: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-info {
  height: 70%;
  width: 41%;
}
// 登录左侧
.login-info-left {
  width: 60%;
  height: 100%;
  background-color: #fff;
  .img {
    margin: 24px 0 0 16px;
  }
  // 登录方式 {
  .login-mothod {
    padding: 0 50px;
    /deep/.ant-tabs-nav .ant-tabs-tab {
      font-size: 18px;
      color: #4d5753;
    }
    /deep/.ant-tabs-ink-bar {
      width: 137px;
      height: 4px;
      border-radius: 5px;
      background-color: #68bb97;
    }
    // 表单
    /deep/.ant-input {
      border: none;
      border-bottom: 1px solid #839398;
      border-radius: 0;
    }
    .ant-input:focus {
      box-shadow: none;
    }
    /deep/ .ant-checkbox-inner {
      color: #8acf7a;
      border-color: #8acf7a;
    }
    /deep/.ant-checkbox-checked .ant-checkbox-inner {
      background-color: #8acf7a;
      border-color: #8acf7a;
    }
    // 注册按钮
    /deep/.ant-btn-primary {
      width: 100%;
      height: 50px;
      background-color: #68bb97;
      border-color: #68bb97;
      border-radius: 25px;
    }
    // 手机格式 / 未注册
    .format {
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      text-align: center;
      color: #ff682c;
      border-radius: 30px;
      border: 1px solid #ff682c;
    }
    // 忘记密码，注册
    .register {
      margin-top: 10px;
      cursor: pointer;
      p {
        float: right;
      }
    }
    // 扫码
    .scan-code {
      text-align: center;
      img {
        width: 252px;
      }
    }
  }
  // 验证码
  .verification {
    input {
      width: 60%;
      height: 42px;
      background: transparent;
      border: 1px solid #ccc;
      outline: none;
      border-radius: 25px 0px 0 25px;
    }
    span {
      display: inline-block;
      width: 40%;
      height: 42px;
      font-size: 16px;
      padding: 0 10px;
      text-align: center;
      border: 1px solid #3cbb8f;
      border-radius: 0 25px 25px 0;
      background-color: #3cbb8f;
      cursor: pointer;
    }
    .disabled {
      background-color: #d6d6d6;
      border-color: #d6d6d6;
      color: #fff;
      cursor: not-allowed; // 鼠标变化
    }
  }
}
// 注册右侧
.login-info-right {
  position: relative;
  width: 40%;
  height: 100%;
  background: url('../../../assets/login/位图.png');
  background-size: cover;
  div {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    p:nth-child(1) {
      font-size: 20px;
      .span {
        position: relative;
        top: -3px;
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 4px;
        background-color: #fff;
      }
    }
    p:nth-child(2) {
      margin-top: 50px;
      margin-bottom: 30px;
      font-size: 25px;
    }
    p:nth-child(3) {
      width: 36%;
      height: 25px;
      line-height: 25px;
      margin: 0 auto;
      border-radius: 15px;
      background-color: #68bb99;
      cursor: pointer;
    }
  }
}
#components-form-demo-normal-login .login-form-button {
  width: 100%;
  height: 50px;
  background-color: #d6d6d6;
  border-color: #d6d6d6;
  border-radius: 25px;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
input[type='number'] {
  -moz-appearance: textfield;
}
</style>
