<template>
  <div class="teacher-register">
    <!-- 老师注册内容  -->
    <div class="teacher-reg-info">
      <div class="teacher-reg-info-left fl">
        <p><img @click="goBack" class="img" src="@/assets/login/编组.png" alt="" /></p>
        <p style="font-size: 18px; text-align: center; margin-bottom: 0">注册信息绑定</p>
        <p style="text-align: center"><img src="@/assets/login/直线 15.png" alt="" /></p>
        <keep-alive>
          <div class="from-info">
            <a-form id="components-form-demo-normal-login" :form="form" class="login-form" @submit="handleSubmit">
              <p v-show="format" class="format">{{ PromptText }}</p>
              <a-form-item>
                <a-input v-decorator="['name', { rules: [{ required: true, message: '' }] }]" placeholder="请输入真实姓名" @blur="userNameValidate">
                  <a-icon slot="prefix" type="user" style="color: rgba(0, 0, 0, 0.25)" />
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-input v-decorator="['area', { rules: [{ required: true, message: '请选择区域' }] }]" placeholder="">
                  <a-icon slot="prefix" type="environment" style="color: rgba(0, 0, 0, 0.25)" />
                  <a-dropdown slot="prefix" :trigger="['click']">
                    <a class="ant-dropdown-link" style="color: #68716d">
                      上海
                      <a-icon type="down" style="color: rgba(0, 0, 0, 0.25)" />
                    </a>
                  </a-dropdown>
                  <a-dropdown slot="prefix" :trigger="['click']">
                    <a class="ant-dropdown-link" style="color: #68716d">
                      上海市
                      <a-icon type="down" style="color: rgba(0, 0, 0, 0.25)" />
                    </a>
                  </a-dropdown>
                  <!-- <a-dropdown slot="prefix" :trigger="['click']">
                    <a class="ant-dropdown-link" style="color: #68716D">
                      徐汇区 <a-icon type="down" style="color: rgba(0,0,0,.25)"/>
                    </a>
                  </a-dropdown> -->
                  <a-select slot="prefix" :default-value="defaultArea" style="width: 90px; lineheight: 2.1" @change="handleChange">
                    <a-select-option v-for="(item, index) in areaData" :key="index" :value="item">
                      {{ item }}
                    </a-select-option>
                  </a-select>
                </a-input>
              </a-form-item>
              <a-form-item class="school-all">
                <img class="school" src="@/assets/login/学校.png" alt="" value="tu" />

                <a-select placeholder="学段" @change="handleSelectChange" :defaultValue="2">
                  <a-select-option v-for="item in schoolType" :key="item.SchoolTypeId" :value="item.SchoolTypeId">
                    {{ item.SchoolTypeName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item class="school-all">
                <img class="school" src="@/assets/login/学校.png" alt="" value="tu" />

                <a-select show-search v-decorator="['school', { rules: [{ required: true, message: '请选择学校' }] }]" placeholder="学校" @change="handleSelectChange1" allowClear>
                  <a-select-option v-for="item in schoolNameData" :key="item.SchoolId" :value="item.SchoolId">
                    {{ item.SchoolName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item class="school-all">
                <img class="school" src="@/assets/login/班级.png" alt="" value="tu" />

                <a-select show-search v-decorator="['classSelect', { rules: [{ required: true, message: '请选择班级' }] }]" mode="multiple" style="width: 100%" placeholder="请选择班级" @change="handleChangeClass" allowClear>
                  <a-select-option v-for="item in classData" :key="item.ClassId" :value="item.ClassId">
                    {{ item.ClassName }}
                  </a-select-option>
                </a-select>
                <!-- <div class="clearfix"> -->

                <!-- v-show="classCheck" -->
                <!-- <div class="ant-class-info"> -->

                <!-- </div> -->
                <!-- <p><span>一(1)班</span><span>一(2)班</span></p> -->
                <!-- <p class="and-class" :class="{ 'and-class-cancel': !this.classCheck}" @click="handleClass">添加班级</p> -->

                <!-- </div> -->
              </a-form-item>
            </a-form>

            <!-- <span>一(1)班</span><span>一(2)班</span><span>一(3)班</span><span>一(4)班</span><span>二(1)班</span><span>二(2)班</span><span>二(3)班</span><span>二(4)班</span><span>二(4)班</span><span>二(4)班</span><span>二(4)班</span> -->
            <p style="font-size: 12px; color: #839398">提示：请务必填写正确信息</p>
            <p class="go-down" @click="toRegister">下一步</p>
            <p class="fg" style="margin-top: 8px">
              <span>已有账号</span>&nbsp;&nbsp;&nbsp;
              <span style="color: #3cbb8f; cursor: pointer" @click="toUserLogin">去登录</span>
            </p>
          </div>
        </keep-alive>
      </div>
      <XuiHuiRight></XuiHuiRight>
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import XuiHuiRight from '@/components/XuHuiRight/XuHuiRight'
export default {
  components: {
    XuiHuiRight
  },
  beforeCreate() {
    this.form = this.$form.createForm(this)
  },
  created() {
    // 获取学段
    this.getSchoolType()
    // 获取地区
    this.getArea()
    this.replaceArea()
    this.getSchoolName()
    // 获取班级
    // this.getSchoolClass()
  },
  activated() {
    this.setValue()
  },
  data() {
    return {
      defaultArea: '徐汇区',
      // form: this.$form.createdForm(this),
      // 提示框
      format: false,
      // 提示框内容
      PromptText: '',
      // 班级选择
      classCheck: false,
      // 地区数据
      areaData: [],
      // 学段数据
      schoolType: [],
      // 学段id
      schoolTypeId: '2',
      // 地区名称
      areaName: '徐汇区',
      // 学校名称数据
      schoolNameData: [],
      // 班级数据
      classData: [],
      // 学校id
      schoolId: '',
      // 班级id
      classId: ''
    }
  },
  watch: {},
  methods: {
    replaceArea() {
      const realmName = window.location.href.split('/')[2]
      // if (realmName === this.Facturl.$hreYpaName) {
      //   this.defaultArea = '杨浦区'
      // }

      if (realmName === this.Facturl.$hrefName) {
        this.defaultArea = '徐汇区'
      } else if (realmName === this.Facturl.$hrefHpName) {
        this.defaultArea = '黄浦区'
      } else if (realmName === this.Facturl.$hrefPtName) {
        this.defaultArea = '普陀区'
      } else if (realmName === this.Facturl.$hrefSjName) {
        this.defaultArea = '松江区'
      } else if (realmName === this.Facturl.$hreYpaName) {
        this.defaultArea = '杨浦区'
      } else if (realmName === this.Facturl.$hrefCnName) {
        this.defaultArea = '长宁区'
      } else if (realmName === this.Facturl.$hrefBsName) {
        this.defaultArea = '宝山区'
      } else {
        this.defaultArea = '徐汇区'
      }
    },
    setValue() {
      this.form.setFieldsValue({
        name: ''
      })
    },
    // 获取学段
    getSchoolType() {
      this.$uwonhttp.post('/User/Login/GetSchoolType').then(res => {
        this.schoolType = res.data.Data
      })
    },
    // 获取地区
    getArea() {
      this.$uwonhttp.post('/User/Login/GetAddress').then(res => {
        this.areaData = res.data.Data[0].city[0].area
      })
    },
    // 获取学校名称
    getSchoolName() {
      this.$uwonhttp
        .post('/User/Login/GetSchoolByType', { typeid: this.schoolTypeId, city: '上海', area: this.defaultArea })
        .then(res => {
          this.schoolNameData = res.data.Data
        })
    },
    // 获取班级
    getSchoolClass(id) {
      this.$uwonhttp.post('/User/Login/GetClassInfo', { schoolId: id }).then(res => {
        this.classData = res.data.Data
      })
    },
    // 提交
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
        }
      })
    },
    // 学段选择
    handleSelectChange(value) {
      this.schoolTypeId = value
      this.getSchoolName()
      this.form.setFieldsValue({
        school: '',
        classSelect: []
      })
    },
    // 学校选择
    handleSelectChange1(value) {
      this.schoolId = value

      this.form.setFieldsValue({
        classSelect: []
      })
      // 获取班级
      this.getSchoolClass(value)
    },
    // 地区选择
    handleChange(value) {
      this.areaName = value
      this.defaultArea = value
      this.getSchoolName()
      // this.getSchoolName()
    },
    handleChangeClass(value) {
      this.classId = value.join()
    },
    // 处理选择班级
    handleClass() {
      const Name = this.form.getFieldsValue()
      if (Name.name === undefined || Name.school === undefined || Name.gender === undefined || Name.school === '') {
        this.format = true
        this.PromptText = '请先选择以上信息'
        this.classCheck = false
      } else {
        this.classCheck = true
        this.format = false
      }
    },
    // 验证姓名
    userNameValidate(e) {
      // 中文匹配
      // const reg = /^[\u4e00-\u9fa5]{2,6}$/
      const reg = /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,6}$/
      if (e.target.value === '') {
        this.format = false
      } else {
        if (!reg.test(e.target.value)) {
          this.format = true
          this.PromptText = '请输入2~6位的中英文'
        } else {
          this.format = false
          // callback()
        }
      }
    },
    // 跳转注册
    toRegister() {
      // else if (Name.gender === undefined || Name.gender === '') {
      //   this.format = true
      //   this.PromptText = '请选择学段'
      // }
      const Name = this.form.getFieldsValue()
      if (Name.name === undefined || Name.name === '') {
        this.format = true
        this.PromptText = '请填写姓名'
      } else if (Name.school === undefined || Name.school === '') {
        this.format = true
        this.PromptText = '请选择学校'
      } else if (this.classId === '' || this.classId === undefined) {
        this.format = true
        this.PromptText = '请选择班级'
      } else {
        this.format = false
        this.$router.push({
          path: '/Home/register/Register',
          query: {
            role: 2,
            UserName: Name.name,
            SchoolId: this.schoolId,
            ClassId: this.classId,
            Type: 0
          }
        })
      }

      // if (Name.name === '' || this.schoolId === '' || this.classId === '') {
      //   // this.format = true
      //   // this.PromptText = '请填写完整信息'
      // } else {
    },
    // 跳转登录
    toUserLogin() {
      this.$router.push({ path: '/Home/UserLogin' })
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.ant-select-selection--multiple .ant-select-selection__placeholder {
  margin-left: 0;
}
// 地区选择
/deep/.ant-input-affix-wrapper .ant-input-prefix :not(.anticon) {
  line-height: 2.5;
}
// 班级选择
/deep/.ant-select-selection {
  border: none;
  border-bottom: 1px solid #f1f1f1;
  border-radius: 0;
}
.teacher-register {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  background: url('../../../assets/login/welcome-banner.png') center center;
  background-size: 100%;
}
// 老师注册内容
.teacher-reg-info {
  width: 41%;
  height: 70%;
  // 左侧
  .teacher-reg-info-left {
    width: 60%;
    height: 100%;
    background-color: #fff;
    .img {
      margin: 24px 0 0 16px;
      cursor: pointer;
    }
    .from-info {
      padding: 0 40px;
    }
    // 添加班级
    .and-class {
      float: right;
      width: 100px;
      height: 40px;
      line-height: 40px;
      // margin-top: 10px;
      margin-bottom: 0;
      text-align: center;
      color: #fff;
      background-color: #68bb97;
      border-radius: 40px;
      cursor: pointer;
      .and-class-cancel {
        background-color: #d6d6d6;
        border-color: #d6d6d6;
        color: #fff;
        cursor: not-allowed;
      }
    }
    // 添加班级内容
    .ant-class-info {
      width: 100%;
      span {
        margin: 8px;
        cursor: pointer;
      }
    }
    // 手机格式 / 未注册
    .format {
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      text-align: center;
      color: #ff682c;
      border-radius: 30px;
      border: 1px solid #ff682c;
    }
    // 下一步
    .go-down {
      float: right;
      width: 100%;
      height: 40px;
      line-height: 40px;
      margin-bottom: 0;
      text-align: center;
      border-radius: 40px;
      color: #fff;
      background-color: #68bb97;
      cursor: pointer;
    }
    // 表单
    /deep/.ant-input {
      border: none;
      border-bottom: 1px solid #f1f1f1;
      border-radius: 0;
    }
    .ant-input:focus {
      border: none;
      box-shadow: none;
    }
    // 学段选择
    /deep/.ant-select-selection--single {
      border: none;
      border-bottom: 1px solid #f1f1f1;
      border-radius: 0;
    }
  }
}
// 学校的icon
.school-all {
  /deep/.ant-select-selection__rendered {
    margin-left: 35px;
  }
  .school {
    position: absolute;
    top: 0;
    width: 25px;
    left: 8px;
    z-index: 1;
  }
}
</style>
