<template>
  <div class="student-regsiter">
    <div class="student-reg-info">
      <div class="student-reg-info-left fl">
        <span><img @click="goBack" class="img" src="@/assets/login/编组.png" alt="" /></span>
        <span class="student-info pc_font_size20 font_size24 ipad_font_size28" style="text-align: center; margin-bottom: 0">学生信息绑定</span>
        <div class="form-info">
          <a-form id="components-form-demo-normal-login" :form="form" class="login-form">
            <p v-show="format" class="format">{{ PromptText }}</p>
            <a-form-item>
              <a-input
                v-decorator="['userName', { rules: [{ required: true, message: '请输入真实姓名' }] }]"
                placeholder="请输入真实姓名"
                @blur="userNameValidate"
              >
                <a-icon slot="prefix" type="user" style="color: rgba(0, 0, 0, 0.25)" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-input placeholder="">
                <img slot="prefix" src="@/assets/login/省市区.png" alt="" />
                <a-dropdown slot="prefix" :trigger="['click']">
                  <a class="ant-dropdown-link" style="color: #68716d">
                    上海 <a-icon type="down" style="color: rgba(0, 0, 0, 0.25)" />
                  </a>
                </a-dropdown>
                <a-dropdown slot="prefix" :trigger="['click']">
                  <a class="ant-dropdown-link" style="color: #68716d">
                    上海市 <a-icon type="down" style="color: rgba(0, 0, 0, 0.25)" />
                  </a>
                </a-dropdown>
                <!-- <a-dropdown slot="prefix" :trigger="['click']">
                  <a class="ant-dropdown-link" style="color: #68716D">
                    徐汇区 <a-icon type="down" style="color: rgba(0,0,0,.25)"/>
                  </a>
                </a-dropdown> -->
                <a-select
                  slot="prefix"
                  :default-value="defaultArea"
                  style="width: 90px; lineheight: 2.1"
                  @change="handleChange"
                >
                  <a-select-option v-for="(item, index) in areaData" :key="index" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-input>
            </a-form-item>
            <a-form-item class="school-all">
              <img class="school" src="@/assets/login/学校.png" alt="" value="tu" />

              <a-select placeholder="学段" @change="handleSelectChange" :defaultValue="little">
                <a-select-option v-for="item in schoolType" :key="item.SchoolTypeId" :value="item.SchoolTypeId">
                  {{ item.SchoolTypeName }}
                </a-select-option>
                <!-- <a-select-option value="2">
                  小学
                </a-select-option> -->
              </a-select>
            </a-form-item>
            <a-form-item class="school-all">
              <img class="school" src="@/assets/login/学校.png" alt="" />
              
              <a-select
                v-decorator="['school', { rules: [{ required: true, message: 'Please select your gender!' }] }]"
                placeholder="学校"
                @change="handleSelectChangeSchool"
              >
                <a-select-option v-for="item in schoolNameData" :key="item.SchoolId" :value="item.SchoolId">
                  {{ item.SchoolName }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item class="school-all">
              <a-select
                style="width: 100%"
                placeholder="请选择班级"
                @change="handleChangeClass"
                v-decorator="['classId']"
              >
                <a-select-option v-for="item in classData" :key="item.ClassId" :value="item.ClassId">
                  {{ item.ClassName }}
                </a-select-option>
              </a-select>
              <img slot="prefix" src="@/assets/login/班级.png" alt="" />
            </a-form-item>
            <!-- <a-form-item>
              <a-input
                v-decorator="[
                  'set'
                ]"
                placeholder="暂无设置">
                <img slot="prefix" src="@/assets/login/姓名.png" alt="">
              </a-input>
            </a-form-item> -->
            <a-form-item class="school-all">
              <img src="@/assets/login/学校(1).png" alt="" />
              <a-input
                v-decorator="['stuNumber']"
                @focus="handleStuNumber"
                @blur="handleStuNumberBlur"
                placeholder="学号"
              >
              </a-input>
            </a-form-item>
            <a-form-item>
              <p class="checkSex">
                <span class="font_size18 ipad_font_size22" @click="handleSex('男')"><img :src="srcMale" alt="" />男</span>
                <span class="font_size18 ipad_font_size22" @click="handleSex('女')"><img :src="srcFemale" alt="" />女</span>
              </p>
              <p class="pc_font_size16 font_size18 ipad_font_size20" style="margin: 10px 0;">提示: 请务必填写正确信息</p>
              <a-button @click="toRegister" type="primary" html-type="submit" class="login-form-button pc_font_size16 font_size18 ipad_font_size20"
                >下一步</a-button
              >
            </a-form-item>
          </a-form>
        </div>
      </div>
      <XuHuiRight></XuHuiRight>
    <!--   徐汇教育认证登录   -->
      <el-dialog
        title="提示"
        :visible.sync="dialogVisible"
        width="26%"
        :before-close="handleClose">
        <div class="divLink">
          <p>您好，一年级学生将采用徐汇教育认证登陆，登陆账户由学校统一开设，请联系班主任/校管理获取及确认账户。</p>
          <div style="display: flex;justify-content: center;margin-top: 14px">
            <el-link href="https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308&identity=2" target="_blank">徐汇教育认证登录</el-link>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import XuHuiRight from '@/components/XuHuiRight/XuHuiRight'
import action from '@/core/directives/action'
export default {
  components: {
    XuHuiRight,
  },
  beforeCreate() {
    this.form = this.$form.createForm(this)
  },
  created() {
    const realmName = window.location.href.split('/')[2]
    if (realmName === this.Facturl.$hrefName) {
      this.defaultArea = '徐汇区'
    } else if (realmName === this.Facturl.$hrefHpName) {
      this.defaultArea = '黄浦区'
    } else if (realmName === this.Facturl.$hrefPtName) {
      this.defaultArea = '普陀区'
    } else if (realmName === this.Facturl.$hrefSjName) {
      this.defaultArea = '松江区'
    } else if (realmName === this.Facturl.$hreYpaName) {
      this.defaultArea = '杨浦区'
    } else if (realmName === this.Facturl.$hrefCnName) {
      this.defaultArea = '长宁区'
    } else if (realmName === this.Facturl.$hrefBsName) {
      this.defaultArea = '宝山区'
    } else {
      this.defaultArea = '徐汇区'
    }
    // ceshishuju ceshiiqu
    // switch (realmName) {
    //   case 'this.Facturl.$hrefName':
    //     this.defaultArea = '徐汇区'
    //     break
    //   case 'this.Facturl.$hrefHpName':
    //     this.defaultArea = '黄浦区'
    //     break
    //   case 'this.Facturl.$hrefPtName':
    //     this.defaultArea = '普陀区'
    //     break
    //   case 'this.Facturl.$hrefSjName':
    //     this.defaultArea = '松江区'
    //     break
    //   case 'this.Facturl.$hreYpaName':
    //     this.defaultArea = '杨浦区'
    //     break
    //   case 'this.Facturl.$hrefCnName':
    //     this.defaultArea = '长宁区'
    //     break
    //   case 'this.Facturl.$hrefBsName':
    //     this.defaultArea = '宝山区'
    //     break
    // }
    // 获取地区
    this.getArea()
    // 获取学段
    this.getSchoolType()
    // this.getSchoolName()
    this.replaceArea()
    const ps = document.getElementsByClassName('ant-row')
    const arr = Array.from(ps)
    if (this.screenHeight < 800) {
      arr.forEach((item) => {
        item.style.marginBottom = 0
      })
    } else {
      arr.forEach((item) => {
        item.style.marginBottom = '24px'
      })
    }
  },
  mounted() {
    // 监听浏览器窗口变化
    window.onresize = () => {
      return (() => {
        this.screenWidth = document.body.clientWidth
        this.screenHeight = document.body.clientHeight
      })()
    }
  },
  watch: {
    screenHeight: (newValue, oldValue) => {
      const ps = document.getElementsByClassName('ant-row')
      const arr = Array.from(ps)
      if (newValue < 808) {
        arr.forEach((item) => {
          item.style.marginBottom = 0
        })
      } else {
        arr.forEach((item) => {
          item.style.marginBottom = '24px'
        })
      }
    },
  },
  data() {
    return {
      defaultArea: '徐汇区',
      little: 2,
      // 提示框
      format: false,
      // 提示框内容
      PromptText: '',
      // 性别选择地址
      srcMale: require('@/assets/login/男.png'),
      srcFemale: require('@/assets/login/女.png'),
      // 班级数据
      classData: [],
      // 班级id
      classId: '',
      // 地区数据
      areaData: [],
      areaName: '徐汇区',
      // 学段
      studySection: 2,
      // 学段数据
      schoolType: [],
      // 学段类型
      schoolTypeId: '',
      // 学校数据
      schoolNameData: [],
      // 性别
      Sex: '',
      // 检测浏览器窗口大小
      screenWidth: '',
      screenHeight: '',
      gradeName: '',
      dialogVisible: false
    }
  },
  methods: {
    handleClose(){
      this.dialogVisible = false
    },
    replaceArea() {},
    // 选择地区
    handleChange(value) {
      this.areaName = value
      this.defaultArea = value
      this.getSchoolName()
    },
    // 选择学段
    handleSelectChange(value) {
      this.studySection = value
      this.form.setFieldsValue({
        school: '',
        classSelect: [],
      })
      this.getSchoolName(value)
    },
    // 学校选择
    handleSelectChangeSchool(value) {
      this.schoolId = value
      this.form.setFieldsValue({
        classSelect: [],
      })
      // 获取班级
      this.getSchoolClass(value)
    },
    // 班级选择题
    handleChangeClass(value) {
      const gradeName = this.classData.find(item => item.ClassId === value).ClassName
      this.gradeName = gradeName
      this.GetClassIsLock(value)
      // this.classId = value
    },
    //检查班级是否锁定
    async GetClassIsLock(cid) {
      const res = await this.$uwonhttp.get('/Class/TeacherClassManager/GetClassIsLock?classId=' + cid)
      if (!res.data.Data) {
        this.classId = cid
      } else {
        this.$alert('该班级教师已锁定，请联系教师解锁班级！', '提示', {
          confirmButtonText: '确定',
          type: 'warning',
          callback: (action) => {
            this.classId = ''
            this.form.setFieldsValue({classId:''})
          },
        })
      }
    },
    // 获取班级
    getSchoolClass(id) {
      this.$uwonhttp.post('/User/Login/GetClassInfo', { schoolId: id }).then((res) => {
        this.classData = res.data.Data
      })
    },
    // 获取地区
    getArea() {
      this.$uwonhttp.post('/User/Login/GetAddress').then((res) => {
        this.areaData = res.data.Data[0].city[0].area
      })
      this.getSchoolName()
    },
    // 获取学段
    getSchoolType() {
      this.$uwonhttp.post('/User/Login/GetSchoolType').then((res) => {
        this.schoolType = res.data.Data
      })
    },
    // 获取学校名称
    getSchoolName(value) {
      this.$uwonhttp
        .post('/User/Login/GetSchoolByType', { typeid: this.studySection, city: '上海', area: this.defaultArea })
        .then((res) => {
          this.schoolNameData = res.data.Data
        })
    },
    // 处理性别选择
    handleSex(value) {
      this.Sex = value
      if (value === '男') {
        this.srcMale = require('@/assets/login/男中.png')
        this.srcFemale = require('@/assets/login/女.png')
      } else {
        this.srcFemale = require('@/assets/login/女中.png')
        this.srcMale = require('@/assets/login/男.png')
      }
    },
    // 处理学号
    handleStuNumber() {
      this.format = true
      document.getElementsByClassName('format')[0].style.fontSize = '12px'
      this.PromptText = '学号即小朋友平时在班级的常用学号，一般为两位数(01-99)，如遗忘，请联系任课老师。'
    },
    handleStuNumberBlur() {
      this.format = false
    },
    // 验证姓名
    userNameValidate(e) {
      const reg = /^[\u4e00-\u9fa5]{2,6}$/
      if (e.target.value === '') {
        this.format = false
      } else {
        if (!reg.test(e.target.value)) {
          this.format = true
          this.PromptText = '请输入2~6位的中文'
        } else {
          this.format = false
          // callback()
        }
      }
    },
    //验证是否同班同名
    async GetTipRegInfo(Name) {
      const res = await this.$uwonhttp.post('/User/Login/GetTipRegInfo', {
        ClassId: this.classId,
        StudentName: Name.userName,
      })
      if (res.data.Data.State == 1) {
        this.$alert(
          `
          <p style="text-align:left">该班级中<b style="color:#68bb97">${Name.userName}</b>同学已有账号，请勿重复注册！</p>
          <p style="text-align:left">如需重新注册或其他问题，请联系您的数学老师或专课专练客服（021-54486816）。</p>
        `,
          '提示',
          {
            confirmButtonText: '确定',
            confirmButtonClass: 'CBClass',
            center: true,
            dangerouslyUseHTMLString: true,
            type: 'warning',
          }
        )
      } else {
        this.$router.push({
          path: '/Home/register/Register',
          query: {
            role: 1,
            UserName: Name.userName,
            SchoolId: Name.school,
            ClassId: this.classId,
            Sex: this.Sex,
            StudyNo: Name.stuNumber,
          },
        })
      }
    },

    // 下一步
    toRegister() {
      const Name = this.form.getFieldsValue()
      if (Name.userName === undefined || Name.userName === '') {
        this.format = true
        this.PromptText = '请填写姓名'
        return
      }
      let regx = /^[\u4e00-\u9fa5]{2,6}$/
      if (!regx.test(Name.userName)) {
        this.format = true
        this.PromptText = '请输入2~6位的中文'
        return
      }
      if (Name.school === undefined || Name.school === '') {
        this.format = true
        this.PromptText = '请选择学校'
        return
      }
      if (Name.classId === undefined || Name.classId === '') {
        this.format = true
        this.PromptText = '请选择班级'
        return
      }
      if (Name.stuNumber === undefined || Name.stuNumber === '') {
        this.format = true
        this.PromptText = '请填写学号'
        return
      }
      if (Name.stuNumber <= 0 || Name.stuNumber > 99) {
        this.format = true
        this.PromptText = '学号即小朋友平时在班级的常用学号，一般为两位数(01-99)，如遗忘，请联系任课老师。'
        return
      }
      if (this.Sex === '') {
        this.format = true
        this.PromptText = '请选择性别'
        return
      }
      this.format = false
      // 处理
      if(this.areaName === '徐汇区' && this.gradeName.includes('一')){
        this.dialogVisible = true
        return
      }
      this.GetTipRegInfo(Name)
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
  },
}
</script>

<style lang="less" scoped>
.divLink{
  p{
    font-size: 16px;
    text-align: center;
  }
  .el-link{
    font-size: 18px;
    padding: 6px 20px;
    background: #68bb97;
    color: #FFFFFF;
    border-radius: 100px;
    text-indent: 2px;
    margin: 0 auto;
  }
}
// 地区选择
/deep/.ant-input-affix-wrapper .ant-input-prefix :not(.anticon) {
  line-height: 2.5;
}
.student-regsiter {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  background: url('../../../assets/login/welcome-banner.png') center center;
  background-size: 100%;
  .student-info {
    width: 80%;
    display: inline-block;
    font-size: 18px;
    text-align: center;
    margin-bottom: 0px;
  }
  p {
    margin-bottom: 0;
  }
}
// 注册内容
.student-reg-info {
  width: 41%;
  //height: 70%;
  height: 660px;
  .student-reg-info-left {
    width: 60%;
    height: 100%;
    background-color: #fff;
    // 手机格式 / 未注册
    .format {
      // height: 30px;
      // line-height: 30px;
      padding: 5px 0;
      margin-bottom: 10px;
      text-align: center;
      color: #ff682c;
      border-radius: 30px;
      border: 1px solid #ff682c;
    }
    .form-info {
      padding: 0 40px;
      img {
        width: 30px;
      }
      // 班级选择
      /deep/.ant-select-selection {
        border: none;
        border-bottom: 1px solid #f1f1f1;
        border-radius: 0;
      }
    }
    .img {
      width: 36px;
      margin: 24px 0 0 16px;
      cursor: pointer;
    }
    // 选择性别
    .checkSex {
      border-bottom: 1px solid #f1f1f1;
      display: flex;
      span {
      //  display: inline-block;
      //  cursor: pointer;
        display: flex;
        align-items: center;
      }
      img {
        width: 30px;
        //vertical-align: bottom;
      }
    }
    // 表单
    /deep/.ant-input {
      font-size: 18px;
      border: none;
      border-bottom: 1px solid #f1f1f1;
      border-radius: 0;
    }
    .ant-input:focus {
      border: none;
      box-shadow: none;
    }
    // 学区选择
    /deep/.ant-select-selection--single {
      border: none;
      border-bottom: 1px solid #f1f1f1;
      border-radius: 0;
    }
    .ant-btn-primary {
      width: 100%;
      height: 46px;
      background-color: #3cbb8f;
      border-color: #3cbb8f;
      border-radius: 30px;
    }
  }
}
// 学校的icon
.school-all {
  /deep/.ant-form-item-children{
    display: flex;
    align-items: center;
  }
  /deep/.ant-select-selection__rendered{
    font-size: 18px;
  }
  ///deep/.ant-select-selection__rendered {
  //  margin-left: 35px;
  //}
  //.school {
  //  position: absolute;
  //  top: 0;
  //  width: 25px;
  //  left: 8px;
  //  z-index: 1;
  //}
}
.el-button--primary:active {
  background: red !important;
}
.el-button--primary {
  background: red !important;
}
</style>
<style>
.CBClass {
  background: #68bb97 !important;
  border: #68bb97 !important;
}
</style>
