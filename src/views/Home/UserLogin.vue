<template>
  <div class="login">
    <!-- 登录内容 -->
    <div class="login-info">
      <div class="login-info-left fl">
        <p>
          <img @click="goBack" class="img" style="cursor: pointer" src="@/assets/login/编组.png" />
        </p>
        <!-- 登录方式 -->
        <div class="login-mothod">
          <a-tabs default-active-key="1" @change="callback">
            <a-tab-pane key="1" tab="密码登录">
              <a-form id="components-form-demo-normal-login" :form="form" class="login-form" @submit="handleSubmit">
<!--                <p v-show="format" class="format">{{ PromptText }}</p>-->
                <p class="main-col">用户名/手机号</p>
                <a-form-item has-feedback :validate-status="mobilesuc">
<!--                  <a-input-->
<!--                    v-decorator="['mobilephone', { rules: [{ required: true, message: '请填写手机号' }] }]"-->
<!--                    placeholder-->
<!--                    @blur="mobilephoneValidate"-->
<!--                  ></a-input>-->
                  <a-input
                    v-decorator="['mobilephone', { rules: [{ required: true, message: '请填写用户名/手机号' }] }]"
                    placeholder
                  ></a-input>
                </a-form-item>
                <p class="main-col">密码</p>
                <a-form-item has-feedback :validate-status="passwordSuc">
                  <a-input
                    v-decorator="['passwordCheck', { rules: [{ required: true, message: '请输入密码' }] }]"
                    type="password"
                    placeholder
                  ></a-input>
                </a-form-item>

                <a-form-item>

                  <div style="text-align: center; margin-top: 15px">
                    <a-button
                      html-type="submit"
                      v-for="(item, index) in LoginList"
                      :key="index"
                      @click="BtnLogin(item.type)"
                    >
                      <span class="container">
                        <img :src="item.img" alt="" />
                        <span>{{ item.Name }}</span>
                      </span>
                    </a-button>
                  </div>
                  <a-checkbox
                      v-decorator="[
                        'remember',
                        {
                          valuePropName: 'checked',
                          initialValue: true,
                        },
                      ]"
                      >记住我的登录信息</a-checkbox
                    >
                    <div class="register clearfix">
                    <p>
                      <span class="main-col-size" @click="toForgetPassword">忘记密码</span>
                      &nbsp;|&nbsp;
                      <span class="main-col-size" @click="toRegisterPage">立即注册</span>
                      &nbsp;|&nbsp;
                      <el-dropdown  trigger="click">
                      <span class="main-col-size">
                        其他登录方式<i class="el-icon-arrow-down el-icon--right"></i>
                      </span>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item>
                          <!-- <UnifiedUsersCertified ref="UnifiedUsersCertified"></UnifiedUsersCertified> -->
                          <el-link class="elLink" type="primary" :underline="false" href="https://rz.szjx.ai-study.net/auth/proxy?clientId=yJqCaifVc3N7tqkhec&serviceUrl=https://appapi.eduwon.cn/User/Login/StudentWebCityResult">上海教育认证</el-link>

                        </el-dropdown-item>
                        <el-dropdown-item>
                          <p class="title-h" @click="toXuHuiLogin">徐汇教育认证</p>
                          <!-- <el-link class="elLink" type="primary" :underline="false" href="https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308&identity=2">徐汇教育认证</el-link> -->
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                    </p>
                  </div>
                </a-form-item>
               
              </a-form>
            </a-tab-pane>
            <a-tab-pane key="2" tab="二维码登录" force-render>
              <div style="margin-bottom: 10px; margin-top: 20px">
                <span class="code_font_top" style="color: #839398">使用专课专练APP扫码登录</span>
                <span class="fg color-green cur help-scan code_font_top" @mouseover="mouseOver" @mouseleave="mouseLeave"
                  >扫码帮助?</span
                >
              </div>
              <!-- class="Show_box" -->
              <transition name="el-fade-in-linear">
                <div v-if="QRCode" class="transition-box">
                  <span>请核对身份后进行扫码登录</span>
                </div>
              </transition>
              <div class="scan-code" id="div1">
                <img :src="'data:image/png;base64,' + qrUrl" alt />
              </div>
              <div class="code_font_btm" style="text-align: center; margin-top: 10px">
                <span>还没有账号?</span>&nbsp;
                <span @click="toRegisterPage" class="color-green cur">立即注册</span>
              </div>
            </a-tab-pane>
          </a-tabs>
          <!-- <UnifiedUsersCertified ref="UnifiedUsersCertified"></UnifiedUsersCertified> -->
<!--          <el-link class="elLink" type="primary" href="https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308">徐汇数字基座</el-link> -->
        </div>
      </div>
      <div class="fg login-info-right">
        <div>
          <p class="pfont">
            <span
              ><img
                v-show="this.showYpJy === 1"
                style="width: 25px; margin-right: 5px"
                src="@/assets/logo/yp-logo.png"
                alt=""
            /></span>
            <webName :class="{ 'f-s': this.showYpJy === 1 }" :realmName="realmName" :id="1"></webName
            ><span v-show="this.showYpJy !== 1"></span>
            <br />
            <span v-show="this.showYpJy !== 1">打造一流教育</span>
            <br />
            <span v-show="this.showYpJy === 1" class="m-t">精准练习，提质增效</span>
          </p>
          <p>还没有账号？</p>
          <p @click="toRegisterPage">马上去注册</p>
          <img v-show="help" class="help" src="@/assets/login/弹窗／扫码帮助@2x_wps图片.png" alt />
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      append-to-body
      width="30%"
      center
     >
     <div class="dialog-content">
      <p>尊敬的用户：</p>
      <span> 您好，徐汇教育认证机房将于2025年1月17日进行搬迁，预计2025年2月5日恢复，在此期间将无法通过徐汇教育认证登录，给您带来不便请您谅解。</span>
     </div>
      <span slot="footer" class="dialog-footer" >
        <el-button v-if="isShowBtn" type="primary" @click="openXuHuiUrl">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/utils.less'
import TokenCache from '@/utils/cache/TokenCache'
import webName from '@/components/WebsiteName/WebsiteName'
import XuHuiRight from '@/components/XuHuiRight/XuHuiRight'
import UnifiedUsersCertified from './UnifiedUsersCertified.vue'
import dayjs from 'dayjs'

export default {
  components: {
    webName,
    UnifiedUsersCertified,
  },
  created() {
    this.getQrLogin()
    this.realmName = window.location.href.split('/')[2]
    if (this.realmName === this.Facturl.$hreYpaName) {
      this.showYpJy = 1
    } else {
      this.showYpJy = 0
    }
  },
  mounted() {
    this.screenWidth = document.body.clientWidth
    this.screenHeight = document.body.clientHeight
  },
  data() {
    return {
      isShowBtn:true,// 是否展示确认按钮
      dialogVisible:false,
      process:process.env.NODE_ENV,
      QRCode: false,
      showYpJy: 0,
      // 登录用户ID
      id: '',
      phone: '',
      // 注册
      format: '',
      // 手机号状态
      mobilesuc: '',
      // 密码状态
      passwordSuc: '',
      // 提示文字
      PromptText: '',
      // 扫码帮助
      help: false,
      // 登录二维码
      qrUrl: '',
      contentName: '徐汇数学网校',
      newcode: '',
      active: 0,
      LoginType: '0',
      LoginList: [
        {
          img: require('@/assets/student.png'),
          Name: '学生端登录',
          type: 0,
        },
        // {
        //   img: require('@/assets/teacher.png'),
        //   Name: '教师端登录',
        //   type: 1
        // },
        // {
        //   img: require('@/assets/leader.png'),
        //   Name: '管理端登录',
        //   type: 2
        // }
      ],
    }
  },
  watch: {},
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'normal_login' })
  },
  methods: {
    toXuHuiLogin(){
      //当前时间是否在指定的时间之前
      const beforeTimeFlag = dayjs().isBefore(dayjs('2025-01-17')) // 默认毫秒
      // 这表示 Day.js 对象是否在另一个提供的日期时间之后。
      const afterTimeFlag = dayjs().isAfter(dayjs('2025-02-05'))
      if(beforeTimeFlag || !afterTimeFlag) this.dialogVisible = true 
      else this.openXuHuiUrl()
      // 证明当前时间在两个时间段中间，不会展示确认按钮
      if(!beforeTimeFlag && !afterTimeFlag){
        this.isShowBtn = false
      }
    },
    openXuHuiUrl(){
      this.dialogVisible = false
      window.location.href = 'https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308&identity=2'
    },
    RefreshCode() {
      var alpha = 30 //透明度值变量
      var oDiv = document.getElementById('div1') //获取DOM元素对象
      oDiv.style.filter = 'alpha(opacity:' + alpha + ')' //设置IE的透明度
      oDiv.style.opacity = alpha / 100 //设置fierfox等透明度，注意透明度值是小数
      this.QRCode = true
      setTimeout(() => {
        this.goBack()
      }, 2000)
    },
    // tabs栏切换
    callback(key) {
      this.initWebSocket()
    },
    BtnLogin(type) {
      this.LoginType = type
    },
    // 提交
    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          this.login('/Base_Manage/Home/SubmitLogin', values.mobilephone, values.passwordCheck, values)
        }
      })
    },
    async login(url, mobilephone, passwordCheck, values) {
      // 去除用户名称或手机号输入时的空格
      const useName = mobilephone.replace(/\s/g, '')
      await this.$http.post(url, { userName: useName, password: passwordCheck, type: this.LoginType }).then((res) => {
        if (res.Success) {
          // 保存token
          TokenCache.deleteToken()
          localStorage.removeItem('role')
          localStorage.removeItem('UserId')
          localStorage.removeItem('passWord')
          localStorage.removeItem('isTeacher')
          localStorage.removeItem('isStudent')
          localStorage.removeItem('isSchoolHeader')
          localStorage.removeItem('IsVip')
          localStorage.removeItem('PL_newP')

          this.passwordSuc = 'success'

          TokenCache.setToken(res.Data)

          if (values && values.remember) {
            localStorage.setItem('passWord', passwordCheck)
          } else {
            localStorage.removeItem('mobilePhone')
            localStorage.removeItem('passWord')
          }

          localStorage.setItem('mobilePhone', mobilephone)

          this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: mobilephone }).then((res) => {
            // debugger
            // console.log('UserLogin.vue')
            if (res.Success) {
              if (res.Data.Roles != null) {
                localStorage.setItem('role', res.Data.Roles[0].Id)
                localStorage.setItem('SchoolId', res.Data.User.SchoolId)

                if (
                  res.Data.Roles.some((item) => {
                    return item.RoleName === '校领导'
                  })
                ) {
                  this.isSchoolHeader = true
                  localStorage.setItem('isSchoolHeader', this.isSchoolHeader)
                  localStorage.removeItem('isTeacher')
                  localStorage.removeItem('isTeachingStaff')
                  localStorage.setItem('role', res.Data.Roles[0].Id)
                }
              }
            }
            if (!res.Success) {
              this.format = true
              this.PromptText = res.Msg
            }
            if (!res.Data.Roles) {
              this.isStudent = '1'
              localStorage.setItem('isStudent', this.isStudent)
              localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
              localStorage.setItem('StClassId', res.Data.ClassId)
              localStorage.setItem('schoolName', res.Data.SchoolName)
              localStorage.setItem('userPhoto', res.Data.User.Photo)
              localStorage.setItem('userName', res.Data.User.RealName)

              localStorage.removeItem('isTeacher')
            } else {
              this.isTeacher = res.Data.Roles.some((item) => {
                return item.RoleName === '教师'
              })
              localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
              localStorage.setItem('isTeacher', this.isTeacher)
              localStorage.removeItem('isStudent')
              localStorage.setItem('role', res.Data.Roles[0].Id)
            }

            this.id = res.Data.UserType
            localStorage.setItem('Id', res.Data.UserType)
            localStorage.setItem('UserId', res.Data.User.Id)
            localStorage.setItem('IsVip', res.Data.IsVip)
            localStorage.setItem('PL_newP', '1')
            localStorage.setItem('LoginSource', res.Data.LoginSource)
            this.$router.push({
              path: '/Home/Introduce',
              query: {
                id: this.id,
              },
            })
          })
        } else {
          if (res.Msg === '还未注册！') {
            this.mobilesuc = 'warning'
          }
          this.passwordSuc = 'error'
          this.format = true
          this.PromptText = res.Msg
        }
      })
    },
    // 扫码
    mouseOver() {
      this.help = true
    },
    mouseLeave() {
      this.help = false
    },
    testMobilephone(str) {
      const regex = /^1[3456789]\d{9}$/
      if (!regex.test(str)) {
        return false
      } else {
        return true
      }
    },
    // 手机校验
    // mobilephoneValidate(e) {
    //   if (!this.testMobilephone(e.target.value)) {
    //     this.format = true
    //     this.PromptText = '手机号格式不正确'
    //   } else {
    //     this.format = false
    //     // 图标
    //     this.mobilesuc = 'success'
    //   }
    // },
    // 密码校验
    // rule, value, callback
    passwordValidate(e) {
      if (e.target.value.length < 6) {
        this.format = true
        this.PromptText = '密码长度不得少于6位'
        // 图标
        this.passwordSuc = ''
      } else {
        this.format = false
        // 图标
        this.passwordSuc = 'success'
      }
    },
    // 跳转注册页
    toRegisterPage() {
      // this.$router.push('/Home/register/IdentityCheck')
      this.$router.push('/Home/Register/StudentRegister')
    },
    // 跳转忘记密码
    toForgetPassword() {
      this.$router.push({ path: '/Home/ForgetPassword' })
    },
    // 上一页
    goBack() {
      this.$router.go(-1)
    },
    Refresh() {
      this.initWebSocket()
    },
    // 获取登录二维码
    getQrLogin() {
      this.$uwonhttp.get('/User/Login/NewLogiQR', {}).then((resJson) => {
        if (resJson.data.Success) {
          this.qrUrl = resJson.data.Data.result
          this.newcode = resJson.data.Data.code
        } else {
        }
      })
    },
    initWebSocket() {
      // 初始化weosocket
      const baseurl = this.$rootUrl1.replace('http://', '').replace('https://', '')
      var scheme = document.location.protocol === 'https:' ? 'wss' : 'ws'
      const wsuri = 'wss://appapi.eduwon.cn/' + 'ws/qrscanlogin?code=' + this.newcode
      this.websock = new WebSocket(wsuri)
      this.websock.onmessage = this.websocketonmessage
      this.websock.onopen = this.websocketonopen
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    websocketonopen() {
      // 连接建立之后执行send方法发送数据
      const actions = { scanlogin: 'conn' }
      // this.websocketsend(JSON.stringify(actions))
    },
    websocketonerror() {
      // this.initWebSocket()
    },
    websocketonmessage(e) {
      const redata = JSON.parse(e.data)
      // console.log(redata)
      // 数据接收
      if (redata.state === 1) {
        TokenCache.setToken(redata.token)
        if (redata.userType === 1) {
          // 教师登录
          // this.LoginType = 1
          // this.login('/Base_Manage/Home/TeacherScanQrLogin', redata.phone, redata.t)
          // var filter=document.getElementById("filter")
          // this.$router.push({
          //   path: '/Home/Index'
          // })
          this.RefreshCode()
        } else if (redata.userType === 2) {
          this.LoginType = 0
          this.login('/Base_Manage/Home/TeacherScanQrLogin', redata.phone, redata.t)
        } else if (redata.userType === 3) {
          this.RefreshCode()
          // 学生登录
          // this.$router.push({
          //   path: '/Home/Index'
          // })
        }
      }
    },
    websocketsend(Data) {
      // 数据发送
      this.websock.send(Data)
    },
    websocketclose(e) {},
  },
}
</script>

<style lang="less" scoped>
.title-h{
  margin-top: 0.052083rem;
    font-size: 0.09375rem;
    color: #36b8f5 !important;
    font-weight: normal;
}
.dialog-content{
  p{
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
  }
  span{
    font-size: 16px;
    line-height: 18px;
  }
}
.cur {
  cursor: pointer;
}
.f-s {
  font-size: 16px;
}
.m-t {
  display: inline-block;
  margin-top: 20px;
  font-size: 24px;
}
.color-green {
  color: #68bb97;
}
.main-col {
  font-size: 18px;
  color: #8b9a9f;
}
.main-col-size {
  color: #68bb97;
  font-size: 18px;
}
.login {
  position: fixed;
  width: 100%;
  height: 100%;
  background: url('../../assets/login/welcome-banner.png') center center;
  background-size: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  p {
    margin-bottom: 0;
  }
}
.login-info {
  //height: 70%;
  height: 672px;
  width: 42%;
}
// 手机号
.login-form {
  .ant-input {
    font-weight: 500;
    color: #4d5753;
  }
}
// 登录左侧
.login-info-left {
  width: 60%;
  height: 100%;
  background-color: #fff;
  .img {
    width: 36px;
    margin: 24px 0 0 16px;
  }
  .help-scan:hover .help {
    display: block;
  }
  // 登录方式 {
  .login-mothod {
    padding: 0 50px;
    /deep/.ant-tabs-nav .ant-tabs-tab {
      font-size: 22px;
      color: #ccc;
    }
    /deep/.ant-tabs-nav .ant-tabs-tab-active {
      color: #4d5753;
    }
    /deep/.ant-tabs-ink-bar {
      width: 137px;
      height: 4px;
      border-radius: 5px;
      background-color: #68bb97;
    }
    // 表单
    /deep/.ant-input {
      border: none;
      border-bottom: 1px solid #839398;
      border-radius: 0;
    }
    .ant-input:focus {
      box-shadow: none;
    }
    /deep/ .ant-checkbox-inner {
      color: #8acf7a;
      border-color: #8acf7a;
    }
    /deep/.ant-checkbox-checked .ant-checkbox-inner {
      background-color: #8acf7a;
      border-color: #8acf7a;
    }
    // 登录按钮
    /deep/.ant-btn-primary {
      background-color: rgba(60, 187, 143, 0.5);
      border-color: rgba(60, 187, 143, 0.5);
    }
    /deep/.ant-btn-primary:hover {
      background-color: rgba(60, 187, 143, 1);
      border-color: rgba(60, 187, 143, 1);
    }
    // 手机格式 / 未注册
    .format {
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      text-align: center;
      color: #ff682c;
      border-radius: 30px;
      border: 1px solid #ff682c;
    }
    // 忘记密码，注册
    .register {
      margin-top: 10px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      p {
        float: right;
      }
      ::v-deep{
        .el-link{
          color: #000;
        }
      }
    }
    // 扫码
    .scan-code {
      text-align: center;
      img {
        width: 252px;
      }
    }
    .transition-box {
      margin-bottom: 10px;
      width: 100%;
      padding: 10px;
      text-align: center;
      border-radius: 4px;
      background: #000;
      text-align: center;
      color: #fff;
      box-sizing: border-box;
      margin-right: 20px;
    }
    // 字体大小调整
    .code_font_top {
      font-size: 16px;
    }
    .code_font_btm {
      font-size: 20px;
    }
  }
}
// 登录右侧
.login-info-right {
  position: relative;
  width: 40%;
  height: 100%;
  background: url('../../assets/login/位图.png') center center;
  background-size: cover;
  div {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    .pfont {
      font-size: 18px;
      .span {
        position: relative;
        top: -3px;
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 4px;
        background-color: #fff;
      }
    }
    .fontSiz {
      font-size: 16px;
    }
    p:nth-child(2) {
      margin-top: 50px;
      margin-bottom: 30px;
      font-size: 25px;
    }
    p:nth-child(3) {
      width: 40%;
      //height: 30px;
      padding: 10px 0px;
      //line-height: 30px;
      font-size: 16px;
      margin: 0 auto;
      border-radius: 100px;
      background-color: #68bb99;
      cursor: pointer;
    }
  }
  .help {
    position: absolute;
    left: -42px;
    width: 300px;
    top: -197px;
  }
}
#components-form-demo-normal-login .login-form-button {
  width: 90%;
  height: 38px;
  border-radius: 5px;
  margin-bottom: 10px;
  background: #fff;
  color: #b4b4b4;
}
/deep/.ant-btn {
  width: 90%;
  height: 55px;
  color: #ffffff;
  background: #68bb97;
  border-color: #68bb97;
  border-radius: 5px;
  margin-bottom: 10px;
}
.container {
  width: 65%;
  img {
    vertical-align: middle;
  }
  span {
    font-size: 22px;
    vertical-align: middle;
    margin-left: 20px;
  }
}
.active {
  width: 90%;
  // height: 50px;
  background: #68bb97;
  color: #b4b4b4;
  border-radius: 5px;
  margin-bottom: 10px;
}
/deep/.ant-checkbox {
  font-size: 36px;
}
/deep/.ant-checkbox + span {
  font-size: 18px;
}
/deep/.ant-btn.active,
.ant-btn:active {
  color: #ffffff;
  border-color: #68bb97;
}
.elLink{
  margin-top: 10px;
  font-size: 18px;
  color:#36b8f5 !important;
  font-weight: normal;
}
</style>
