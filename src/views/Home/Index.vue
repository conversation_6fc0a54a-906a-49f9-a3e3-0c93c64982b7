<template>
  <div
    style="height: 100%;"
    v-loading="!canLogin"
    element-loading-text="正在登录中,请稍后..."
    element-loading-background="rgba(0, 0, 0, 0)">
    <!--  -->
    <div v-show="loginType">
    <div class="banner">
      <!-- 导航栏 -->
      <Nav></Nav>
      <!-- 徐汇区 -->
      <div class="download animate__animated animate__fadeInDown delay-1s">
        <p class="banner-name">
          <webName v-show="this.showYpJy !== 1" :realmName="this.realmName"></webName
          ><span v-show="this.showYpJy !== 1">&nbsp;·&nbsp;</span>
          <span v-show="this.showYpJy !== 1">打造一流教育</span>
          <span v-show="this.showYpJy === 1">精准练习 提质增效</span>
        </p>
        <!-- <img src="@/assets/user/Create First-class <EMAIL>" alt=""> -->
        <p class="banner-bottom">Create First-class Education</p>
      </div>
      <!-- 立即登录 -->
      <!-- <div class="now-login animate__animated animate__fadeInUp animate__delay-1s">
        <span @click="handleLogin" class="nowLogin">立即登录</span>
      </div> -->
      <div
        class="Login_box"
        v-loading="!canLogin"
        element-loading-text="正在登录中,请稍后..."
        element-loading-background="rgba(0, 0, 0, 0)"
        :style="{ background: !canLogin ? 'rgba(255,255,255,0)' : '' }"
      >
        <template v-if="canLogin">
          <el-button class="btn_student" @click="handleLogin(0)">学生登录</el-button>
          <el-button class="btn_teacher" @click="handleLogin(1)">教师登录</el-button>
          <el-button class="btn_admin" @click="handleLogin(2)">管理登录</el-button>
        </template>
      </div>
      <!-- 网校数据 -->
      <div class="student-data">
        <div class="student-info">
          <span><img src="@/assets/user/index-resources.png" alt="" /></span>
          <span><img src="@/assets/user/index-student.png" alt="" /></span>
          <span><img src="@/assets/user/index-teacher.png" alt="" /></span>
        </div>
      </div>
    </div>
    <!-- 教学模块 -->
    <ul class="teach-model clearfix animate__animated animate__jackInTheBox">
      <li><img src="@/assets/user/75.png" alt="" /></li>
      <li><img src="@/assets/user/精品微课.png" alt="" /></li>
      <li><img src="@/assets/user/线上辅导.png" alt="" /></li>
    </ul>
    <!-- 我们的特色 -->
    <div class="characteristic">
      <!-- <button @click="handleAnimation">发动动画</button> -->
      <p class="title animate__animated animate__fadeInDown">我们的特色</p>
      <div class="characteristic-top animate__animated" :class="{ animate__fadeInLeftBig: animate__fadeInLeftBig }">
        <img src="@/assets/user/徐汇区教育局统一管理图片.png" alt="" />
        <span class="content">
          <p>教学评估</p>
          <p class="charact-content">自动生成个性化学业分析报告</p>
          <p>老师可以根据成绩分析快速调整教</p>
          <p>学方案，标准指导教学，因材施教</p>
          <p style="margin-bottom: 48px">不再困难。</p>
          <p class="charact-content">个性化数据报告指引薄弱知识点</p>
          <p>针对薄弱各个击破，精准学习让课</p>
          <p>堂效率事半功倍。</p>
        </span>
      </div>
      <div class="characteristic-bot animate__animated animate__fadeInRightBig">
        <span class="sider-content">
          <p>错题归纳</p>
          <p class="charact-content t-r">错题整理 问题分类归纳</p>
          <p>学生做的错题归纳到错题本里，可</p>
          <p>专门进行专项训练，复习错题，巩</p>
          <p style="margin-bottom: 48px">固没有掌握好的知识点。</p>
          <p class="charact-content" style="text-align: right">错题再做 回顾拓展复习</p>
          <p>错题再次测试，检查之前没有掌握</p>
          <p>好的内容，温故知新。</p>
        </span>
        <img src="@/assets/user/兼顾学生的个性发展图片.png" alt="" />
      </div>
    </div>
    <!-- 底部 -->
    <Footer></Footer>
  </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import Footer from '@/components/XuHuiFooter/XuHuiFooter'
import Nav from '@/components/XuHuiNav/XuHuiNav'
import webName from '@/components/WebsiteName/WebsiteName'
export default {
  components: {
    Footer,
    Nav,
    webName,
  },
  computed:{
    loginType(){
      // return this.$route.query.type != 'shanghai'
      return !['shanghai','xuhui'].includes(this.$route.query.type)

    }
  },
  created() {
    if (location.href.indexOf('token=') != -1) {
      this.canLogin = false
    }
    localStorage.setItem('xh', this.xh)
    localStorage.setItem('httpList', this.httpList)
    this.realmName = window.location.href.split('/')[2]
    if (this.realmName === this.$hrefName) {
      this.conentName = '徐汇区'
    } else {
      this.conentName = '黄浦区'
    }
    if (this.realmName === this.Facturl.$hreYpaName) {
      this.showYpJy = 1
    } else {
      this.showYpJy = 0
    }
  },
  mounted() {
    if (!!window.ActiveXObject || 'ActiveXObject' in window) {
      this.$message.success('为了达到您最佳的使用效果,建议使用谷歌浏览器')
    } else {
    }
  },
  data() {
    return {
      animate__fadeInLeftBig: false,
      bannerName: '徐汇数学网校',
      realmName: '',
      conentName: '',
      showYpJy: 0,
      xh: 'http://sj.eduwon.cn/',
      canLogin: true,
    }
  },

  methods: {
    // 登录
    handleLogin(type) {
      var domain = document.domain
      // console.log(domain, 'domain')
      if (type == 0) {
        if (domain == 'zy.eduwon.cn' || domain == 'st.eduwon.cn') {
          window.location.href =
            'https://operator-api.sh-genius.cn/ucenter/auth/index?client_id=eduwon_client&sign=2dd2bbd834c9282e79b93ebb8cf72670&redirect_uri=https://adminapi.eduwon.cn/DS/Api/NewResult'
        } else {
          this.$router.push({ path: '/Home/UserLogin' })
        }
      }
      if (type == 1 || type == 2) {
        if (domain == 'zy.eduwon.cn' || domain == 'st.eduwon.cn') {
          window.location.href =
            'https://operator-api.sh-genius.cn/ucenter/auth/index?client_id=eduwon_client&sign=2dd2bbd834c9282e79b93ebb8cf72670&redirect_uri=https://adminapi.eduwon.cn/DS/Api/NewResult'
        } else {
          // console.log(type,'type')
          window.location.href = 'https://tch.eduwon.cn/Home/UserLogin?type=' + type
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
ul,
p {
  margin-bottom: 0;
}
.t-r {
  text-align: right;
}

.Login_box {
  width: 50%;
  background: rgba(255, 255, 255, 1);
  border-radius: 14px;
  opacity: 0.9;
  margin: auto;
  text-align: center;
  padding: 55px;
  margin-top: 50px;
  display: flex;
  justify-content: space-around;
  /deep/.el-button {
    width: 24%;
    height: 60px;
    font-size: 22px;
  }
  .btn_student {
    background: #0185ff;
    border-color: #0185ff;
    color: #fff;
  }
  .btn_teacher {
    background: #c7d02d;
    border-color: #c7d02d;
    color: #fff;
  }
  .btn_admin {
    background: #ff9600;
    border-color: #ff9600;
    color: #fff;
  }
}

.banner-name {
  font-size: 58px;
  color: #306a6a;
}
.banner-bottom {
  font-size: 40px;
  color: #306a6a;
}
.content {
  .charact-content {
    margin-bottom: 24px;
    font-weight: 700;
    color: #2a2d2c;
  }
  p {
    // width: 273px;
    text-align: left;
    font-size: 18px;
    color: #8f8f8f;
  }
}
.sider-content {
  .charact-content {
    font-weight: 700;
    margin-bottom: 24px;
    color: #2a2d2c;
  }
  p {
    text-align: left;
    font-size: 18px;
    color: #8f8f8f;
  }
}
.nowLogin {
  display: inline-block;
  width: 200px;
  height: 72px;
  line-height: 72px;
  background-color: #cdd309;
  color: #fff;
  font-size: 25px;
  border-radius: 5px;
  background: linear-gradient(to left, #cdd309, #9ac313);
}
// 背景图
.banner {
  height: 1132px;
  background: url('../../assets/user/首页.png') center center no-repeat;
  background-size: 100% 100%;
}
// 导航栏
.user-nav {
  // height: 93px;
  // background: url('../../assets/user/header.png') center center no-repeat;
  background-size: 100% 100%;
  ul {
    margin-top: 15px;
    li {
      float: left;
      margin-right: 73px;
      padding: 0 5px;
      color: #fff;
      cursor: pointer;
      img {
        width: 30px;
      }
    }
  }
}
.user-nav-img {
  margin-top: 10px;
  margin-left: 233px;
}
// 徐汇区下载
.download {
  // margin-top: 60px;
  text-align: center;
  img {
    height: 34px;
  }
  img:nth-child(3) {
    height: 44px;
    margin-top: 24px;
  }
}
// 立即登录
.now-login {
  height: 114px;
  margin-top: 80px;
  text-align: center;
  cursor: pointer;
}
// 网校数据
.student-data {
  // display: flex;
  // height: 185px;
  margin-top: 349px;
  text-align: center;
  // background: url('../../assets/user/banner.png') center center no-repeat;
  background-size: 100% 100%;
  .student-info {
    // width: 82.3125rem /* 1141/16 */;
    margin: 0 auto;
    display: flex;
  }
  span {
    flex: 1;
  }
}
// 教学模块
.teach-model {
  display: flex;
  // margin: 0 auto;
  text-align: center;
  margin-top: 80px;
  // width: 82.3125rem /* 1141/16 */;
  p {
    width: 100%;
    font-size: 24px;
    color: #2a2d2c;
  }
  li {
    flex: 1;
    float: left;
    // margin-right: 80px;
    margin-bottom: 80px;
    img {
      height: 245px;
    }
  }
  li:nth-of-type(3n) {
    margin-right: 0;
  }
}
// 我们的特色
.characteristic {
  padding: 0 8px;
  text-align: center;
  background-color: #f7f7f7;
  .title {
    font-size: 35px;
    text-align: center;
    padding-top: 8px;
    margin-bottom: 126px;
    // font-weight: 700;
    color: #000;
  }
  .characteristic-top {
    margin-bottom: 105px;
    img {
      vertical-align: top;
      height: 390px;
      margin-right: 101px;
    }
    span {
      display: inline-block;
      p {
        img {
          height: 164px;
        }
      }
      p:nth-child(1) {
        margin-bottom: 42px;
        text-align: left;
        font-size: 22px;
        color: #2a2d2c;
      }
    }
  }
  .characteristic-bot {
    margin-bottom: 130px;
    img {
      vertical-align: top;
      height: 390px;
      margin-left: 160px;
    }
    span {
      display: inline-block;
      // margin-right: 93px;
      p {
        img {
          height: 164px;
        }
      }
      p:nth-child(1) {
        margin-bottom: 42px;
        // padding-right: 113px;
        text-align: right;
        font-size: 22px;
        color: #2a2d2c;
      }
    }
  }
}
</style>
