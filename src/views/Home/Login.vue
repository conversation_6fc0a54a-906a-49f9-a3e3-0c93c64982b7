<template>
  <div class="main">
    <a-spin :spinning="loading">
      <a-form id="formLogin" class="user-layout-login" ref="formLogin" :form="form" @submit="handleSubmit">
        <a-tabs
          :activeKey="customActiveKey"
          :tabBarStyle="{ textAlign: 'center', borderBottom: 'unset' }"
          @change="handleTabClick"
        >
          <a-tab-pane key="tab1" tab="账号密码登录">
            <a-form-item>
              <a-input
                size="large"
                type="text"
                placeholder="请输入用户名"
                v-decorator="['userName', { rules: [{ required: true, message: '请输入用户名' }] }]"
              >
                <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>

            <a-form-item>
              <a-input
                size="large"
                type="password"
                autocomplete="false"
                placeholder="请输入密码"
                v-decorator="['password', { rules: [{ required: true, message: '请输入密码' }] }]"
              >
                <a-icon slot="prefix" type="lock" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-checkbox v-decorator="['savePwd', { valuePropName: 'checked' }]">记住密码</a-checkbox>
            </a-form-item>
            <a-form-item style="margin-top: 24px">
              <a-button size="large" type="primary" htmlType="submit" class="login-button">确定</a-button>
            </a-form-item>
          </a-tab-pane>
          <a-tab-pane key="tab2" tab="统一认证登录">
            <a-form-item style="margin-top: 24px">
              <img src="@/assets/shjyzx.png" class="logo" alt="logo" />
            </a-form-item>

            <a-form-item v-if="showRegister">
              <a-input size="large" type="text" placeholder="手机号码" v-model="BindPhone">
                <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>

            <a-form-item v-if="showRegister">
              <a-input size="large" type="password" placeholder="密码" v-model="BindPassWord">
                <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>

            <a-form-item v-if="showRegister">
              <a-input size="large" type="text" :disabled="true" placeholder="姓名" v-model="authUserInfo.realName">
                <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>

            <a-form-item v-if="showRegister">
              <a-input
                size="large"
                type="text"
                :disabled="true"
                placeholder="班级"
                v-model="authUserInfo.studentClassName"
              >
                <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>

            <a-form-item v-if="showRegister">
              <a-input
                size="large"
                type="text"
                :disabled="true"
                placeholder="学生代码"
                v-model="authUserInfo.studentCode"
              >
                <a-icon slot="prefix" type="user" :style="{ color: 'rgba(0,0,0,.25)' }" />
              </a-input>
            </a-form-item>

            <a-form-item style="margin-top: 24px" v-if="showRegister">
              <a-button size="large" type="primary" htmlType="button" @click="UnionAuthRegister" class="login-button"
                >绑定</a-button
              >
            </a-form-item>

            <a-row v-if="!showRegister">
              <a-form-item style="margin-top: 24px">
                <a-button size="large" type="primary" htmlType="button" @click="ulogin" class="login-button"
                  >统一认证登录</a-button
                >
              </a-form-item>
            </a-row>
          </a-tab-pane>
        </a-tabs>

        <a-row>
          <a-form-item style="margin-top: 100px; color: #aaa; text-align: center"
            >Copyright©上海有我科技有限公司，All RllRights Reserved</a-form-item
          >
          <a-form-item style="margin-top: 1px; color: #aaa; text-align: center">沪ICP备14028921号</a-form-item>
        </a-row>
      </a-form>
    </a-spin>
  </div>
</template>

<script>
import TokenCache from '@/utils/cache/TokenCache'

export default {
  data() {
    return {
      loading: false,
      customActiveKey: 'tab1',
      websock: null,
      visible: false,
      win: null,
      showRegister: false,
      authUserInfo: {},
      unionAuth: {},
      BindPhone: '',
      BindPassWord: '',
      form: this.$form.createForm(this),
    }
  },
  created() {
    this.initWebSocket()
  },
  destroyed() {
    this.websock.close()
  },
  mounted() {
    var userName = localStorage.getItem('userName')
    var password = localStorage.getItem('password')
    if (userName && password) {
      this.form.setFieldsValue({ userName, password, savePwd: true })
    }
  },
  methods: {
    initWebSocket() {
      // 初始化weosocket
      const baseurl = this.$rootUrl.replace('http://', '').replace('https://', '')
      var scheme = document.location.protocol === 'https:' ? 'wss' : 'ws'
      const wsuri = scheme + '://' + baseurl + '/ws/unionlogin'
      this.websock = new WebSocket(wsuri)
      this.websock.onmessage = this.websocketonmessage
      this.websock.onopen = this.websocketonopen
      this.websock.onerror = this.websocketonerror
      this.websock.onclose = this.websocketclose
    },
    websocketonopen() {
      // 连接建立之后执行send方法发送数据
      const actions = { UnionAuth: 'conn' }
      this.websocketsend(JSON.stringify(actions))
    },
    websocketonerror() {
      this.initWebSocket()
    },
    websocketonmessage(e) {
      // 数据接收
      const redata = JSON.parse(e.data)
      if (redata && redata.UnionAuth.State === false) {
        this.$message.error('未绑定平台账户，请先登录平台进行绑定')
        this.loading = false
        if (this.win) {
          this.win.close()
        }
        this.showRegister = true
        this.authUserInfo = redata.DSAuthModle.authUserInfo
        this.unionAuth = redata.UnionAuth
      } else if (redata && redata.UnionAuth.State === true) {
        if (this.win) {
          this.win.close()
        }
        this.loading = false
        this.authUserInfo = redata.DSAuthModle.authUserInfo
        this.unionAuth = redata.UnionAuth
        this.SubmitLoginByUnionId()
      }
    },
    websocketsend(Data) {
      // 数据发送
      this.websock.send(Data)
    },
    websocketclose(e) {
      this.initWebSocket()
    },
    handleTabClick(key) {
      this.customActiveKey = key
    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time))
    },
    ulogin() {
      // this.visible = true;
      var height = 800
      var width = 1200
      var winOption =
        'height=' +
        height +
        ',width=' +
        width +
        ',top=50,left=50,toolbar=no,location=no,directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,fullscreen=0'
      var url =
        'https://ds.etextbook.cn/authcenter/auth/shauth?client_id=xhzyClientId&redirect_uri=/DS/Api/Result&sign=696d91e53b0f94ce3acf30e3d898ee03'
      this.win = window.open(url, window, winOption)
    },
    UnionAuthRegister() {
      this.$http
        .post('/Base_Manage/Home/UnionAuthRegister', {
          UnionId: this.authUserInfo.id,
          Phone: this.BindPhone,
          PassWord: this.BindPassWord,
          UserType: this.authUserInfo.userType,
        })
        .then((resJson) => {
          if (resJson.Success) {
            this.customActiveKey = 'tab1'
            this.$message.success('绑定完成，可使用账号登录')
          } else {
            this.$message.error(resJson.Msg)
            this.customActiveKey = 'tab1'
          }
        })
    },
    UnionBindUser() {
      this.$http
        .post('/DS/Api/UnionBindUserId', {
          unionId: this.authUserInfo.id,
          sourceType: 3,
          userId: this.unionAuth.Id,
          url: '',
        })
        .then((resJson) => {
          if (resJson.Success) {
            this.$message.success('绑定完成')
            this.customActiveKey = 'tab1'
          } else {
            this.$message.error(resJson.Msg)
          }
        })
    },

    SubmitLoginByUnionId() {
      this.$http
        .post('/Base_Manage/Home/SubmitLoginByUnionId', {
          unionId: this.authUserInfo.id,
          userType: this.authUserInfo.userType,
        })
        .then((resJson) => {
          if (resJson.Success) {
            TokenCache.setToken(resJson.Data)
            this.$router.push({ path: '/' })
          } else {
            this.$message.error(resJson.Msg)
          }
        })
    },

    handleSubmit(e) {
      e.preventDefault()
      this.form.validateFields((errors, values) => {
        // 校验成功
        if (!errors) {
          var values = this.form.getFieldsValue()
          this.loading = true
          this.$http.post('/Base_Manage/Home/SubmitLogin', values).then((resJson) => {
            this.loading = false
            if (resJson.Success) {
              TokenCache.setToken(resJson.Data)
              this.getUserId(values['userName'])
              // 保存密码
              if (values['savePwd']) {
                localStorage.setItem('userName', values['userName'])
                localStorage.setItem('password', values['password'])
              } else {
                localStorage.removeItem('userName')
                localStorage.removeItem('password')
              }

              this.$router.push({ path: '/' })
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },

    getUserId(userName) {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName }).then((res) => {
        // console.log('Login.vue')
        const userData = JSON.stringify(res.Data.User)
        const UserType = JSON.stringify(res.Data.UserType)

        localStorage.setItem('SelectAreaId', res.Data.User.AreaId)
        localStorage.setItem('userId', userData)
        localStorage.setItem('userName', UserType)
      })
    },
  },
}
</script>

<style lang="less" scoped>
.user-layout-login {
  label {
    font-size: 14px;
  }

  .getCaptcha {
    display: block;
    width: 100%;
    height: 40px;
  }

  .forge-password {
    font-size: 14px;
  }

  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 40px;
    width: 100%;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }

    .register {
      float: right;
    }
  }
}
</style>
