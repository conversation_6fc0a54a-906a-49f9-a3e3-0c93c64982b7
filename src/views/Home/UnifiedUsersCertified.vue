<!--
 * @Author: 周恩波 z<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-14 09:30:43
 * @LastEditors: 周恩波
 * @LastEditTime: 2024-12-04 14:45:17
 * @Description: 
-->
<template>
  <div class="UnifiedUsersCertified">
    <!-- 区域名登录：链接该区的统一用户认证登录-->
    <div
      class="flex flex_space_between text_color font_size_18"
      v-if="domainName != 'stu.eduwon.cn' "
    >
      <span @click="openNewPage()" class="cursor_pointer">上海教育认证</span>
      <span v-if="targetArea"><b>|</b></span>
      <span
        class="cursor_pointer"
        v-if="targetArea"
        @click="openNewPage2(targetArea.unifiedAddress)"
        >{{ targetArea.area }}教育认证</span
      >
    </div>
    <!-- stu.eduwon.cn域名登录:链接所有统一用户认证通过的区  -->
    <div
      class="flex flex_wrap text_color font_size_18 line_height_22"
      v-if="domainName == 'stu.eduwon.cn' || domainName == 'st.dev.eduwon.cn'"
    >
      <span @click="openNewPage()" class="cursor_pointer">上海教育认证</span>
      <p
        v-for="(target, index) in targetAreaList"
        :key="index + 'target'"
        class="flex flex_align_center cursor_pointer"
      >
        <span>&nbsp;|&nbsp;</span>
        <span @click="openNewPage2(target.unifiedAddress)">{{ target.area }}教育认证</span>
      </p>
    </div>
  </div>
</template>
<script>
// 后续添加别的区，到newFacturl文件中配置一下参数即可
import { newFacturl } from '@/store/newFacturl'
export default {
  name: 'UnifiedUsersCertified',
  created() {
    this.domainName = document.domain
    if (this.domainName != 'stu.eduwon.cn' && this.domainName != 'st.dev.eduwon.cn')
      this.targetArea = newFacturl().find((item) => item.domianName == this.domainName)
    else this.targetAreaList = newFacturl().filter((item) => item.isCertified)
    //测试环境测默认赋值
    if(this.domainName == 'st.dev.eduwon.cn' && !this.targetArea) this.targetArea = newFacturl()[0]
  },
  data() {
    return {
      domainName: '',
      // shwxCertifiedAddress: 'https://rz.szjx.ai-study.net/auth/proxy?clientId=yJqCaifVc3N7tqkhec&serviceUrl=https://appapi.eduwon.cn/User/Login/StudentWebCityResult',
      // shwxCertifiedAddress: 'https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308',
      targetArea: null,
      targetAreaList: [],
    }
  },
  methods: {
    openNewPage(){
      window.open('https://rz.szjx.ai-study.net/auth/proxy?clientId=yJqCaifVc3N7tqkhec&serviceUrl=https://appapi.eduwon.cn/User/Login/StudentWebCityResult', '_blank')
    },
    openNewPage2() {
      window.open('https://platform.xhedu.sh.cn/platform/app/login.php?ep_appid=100100308&identity=2', '_blank')
    },
  },
}
</script>
<style lang="less" scoped>
.text_color {
  color: #36b8f5;
}
</style>
