<template>
  <div class="login">
    <!-- 忘记密码内容 -->
    <div class="login-info">
      <div class="login-info-left fl">

        <p><img @click="goBack" class="img" style="cursor: pointer;" src="@/assets/login/编组.png" alt=""></p>
        <!-- 忘记密码 -->
        <div class="login-mothod">
          <a-tabs default-active-key="1" @change="callback">

            <a-tab-pane key="1" tab="重置密码">
              <!-- <a-radio-group name="radioGroup" :default-value="1" @change="handleId">
                <a-radio :value="1">
                  学生
                </a-radio>
                <a-radio :value="2">
                  教师
                </a-radio>
              </a-radio-group> -->
              <a-form id="components-form-demo-normal-login" :form="form" class="login-form" @submit="handleSubmit">
                <p v-show="format" class="format">{{ PromptText }}</p>
                <a-form-item has-feedback :validate-status="mobileSuc">
                  <a-input v-decorator="[
                      'mobilephone',
                      { rules: [{ required: true, message: '请填写手机号'}] },
                    ]" placeholder="输入手机号" @input="changeInput($event)" @blur="mobilephoneValidate">
                    <img slot="prefix" src="@/assets/login/icon-重置密码／输入手机号.png" alt="">
                  </a-input>
                </a-form-item>
                <a-form-item has-feedback :validate-status="a">
                  <a-input v-decorator="[
                      'testing',
                      { rules: [{ required: true, message: '请填写验证码'}] },
                    ]" placeholder="输入验证码" @input="changeInput($event)">
                    <img slot="prefix" src="@/assets/login/icon-重置密码／输入密码.png" alt="">
                  </a-input>
                  <span class="testing" :class="{disabled: !this.canClick}" @click="countDown">{{ content }}</span>

                </a-form-item>
                <a-form-item has-feedback :validate-status="a">
                  <a-input v-decorator="[
                      'password',
                      { rules: [{ required: true, message: '请填写密码'},{validator: passwordValidate}] },
                    ]" placeholder="输入密码" type="password" @input="changeInput($event)">
                    <img slot="prefix" src="@/assets/login/icon-重置密码／输入密码1.png" alt="">
                  </a-input>
                </a-form-item>
                <a-form-item has-feedback :validate-status="a">
                  <a-input v-decorator="[
                      'againPassword',
                      { rules: [{ required: true, message: '请再次填写密码'},{validator: passwordValidate }] },
                    ]" placeholder="再次输入密码" type="password" @input="changeInput($event)">
                    <img slot="prefix" src="@/assets/login/icon-重置密码／再次输入密码.png" alt="">
                  </a-input>
                </a-form-item>
                <a-button type="primary" html-type="submit" :class="{'login-form-button': this.reset }">
                  确认重置
                </a-button>
              </a-form>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
      <XuHuiRight></XuHuiRight>
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import XuHuiRight from '@/components/XuHuiRight/XuHuiRight'
export default {
  components: {
    XuHuiRight
  },
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'normal_login' })
  },
  created() {
    this.canClick = false
  },
  data() {
    return {
      a: '',
      // 重置
      reset: true,
      // 提示状态
      format: '',
      // 手机号状态
      mobileSuc: '',
      // 提示文字
      PromptText: '',
      // 验证码
      content: '获取短信验证码',
      totalTime: 60,
      canClick: true, // 添加canClick,
      roleId: 1
    }
  },
  watch: {},
  methods: {
    handleId(e) {
      this.roleId = e.target.value
    },
    // tabs栏切换
    callback(key) {},
    // 提交
    handleSubmit(e) {
      e.preventDefault()
      const that = this
      this.form.validateFields((err, values) => {
        if (!err) {
          that.$http
            .post('/HomePageDataView/TeacherDataView/CodeIsTrue', {
              code: values.testing,
              phoneNum: values.mobilephone
            })
            .then(res => {
              if (!res.Success) {
                this.format = true
                this.PromptText = res.Msg
              } else if (values.password !== values.againPassword) {
                this.format = true
                this.PromptText = '密码不一致'
              } else {
                this.$uwonhttp
                  .post('/TeacherUserCenter/TeacherPersonal/UpdateMainPwd', {
                    phoneNum: values.mobilephone,
                    pwd: values.password,
                    Code: values.testing,
                    IsStudent: this.roleId
                  })
                  .then(res => {
                    this.format = false
                    if (res.data.Success) {
                      this.$message.success('重置密码成功')
                      this.$router.push({ path: '/Home/UserLogin' })
                    } else {
                      this.format = true
                      this.PromptText = res.data.Msg
                    }
                  })
              }
            })
          // this.$uwonhttp.post('/TeacherUserCenter/TeacherPersonal/UpdateMainPwd', { phoneNum: values.mobilephone, pwd: values.password, Code: values.testing }).then(res => {
          //   if (!res.data.Success) {
          //     this.format = true
          //     this.PromptText = res.data.Msg
          //   } else {
          //     if (values.password !== values.againPassword) {
          //       this.format = true
          //       this.PromptText = '密码不一致'
          //     } else {
          //       this.format = false
          //       this.$message.success('重置密码成功')
          //       this.$router.push('/Home/UserLogin')·
          //     }
          //   }
          // })
        }
      })
    },
    // 号码校验
    testMobilephone(str) {
      const regex = /^1[3456789]\d{9}$/
      if (!regex.test(str)) {
        return false
      } else {
        return true
      }
    },
    // 手机校验
    mobilephoneValidate(e) {
      // 主要就是添加一个对undefined和空串的判断
      if (e.target.value === '') {
        this.format = false
        this.mobileSuc = ''
        this.canClick = false
      } else {
        if (!this.testMobilephone(e.target.value)) {
          this.format = true
          this.PromptText = '手机号格式不正确'
          // 图标
          this.mobileSuc = ''
          this.canClick = false
        } else {
          this.format = false
          // 图标
          this.mobileSuc = 'success'
          if (this.content === '获取短信验证码' || this.content === '重新发送验证码') {
            this.canClick = true
          }
          // callback()
        }
      }
    },
    // 密码校验
    passwordValidate(rule, value, callback) {
      if (value.length < 6) {
        this.format = true
        this.PromptText = '密码长度不得少于6位'
        this.passwordSuc = ''
        // 验证码
      } else {
        this.format = false
        // 图标
        // this.passwordSuc = 'success'
        callback()
      }
    },
    // 获取手机验证码
    getPhoneCount(mobilephone) {
      this.$uwonhttp
        .get('/User/UserManager/GetPhoneNumCode', {
          params: {
            phoneNum: mobilephone
          }
        })
        .then(res => {})
    },
    // 校验手机号是否注册

    // 验证码
    countDown() {
      if (!this.canClick) return
      this.canClick = false
      const values = this.form.getFieldsValue()
      this.$uwonhttp
        .post('/TeacherUserCenter/TeacherPersonal/CheckUserExistByPhone', { phoneNum: values.mobilephone })
        .then(res => {
          if (res.data.Data.Exist !== 1) {
            this.format = true
            this.PromptText = '手机号未注册'
          } else {
            this.getPhoneCount(values.mobilephone)
            this.content = `${this.totalTime}s重新获取`
            const clock = window.setInterval(() => {
              this.totalTime--
              this.content = `${this.totalTime}s重新获取`
              if (this.totalTime < 0) {
                window.clearInterval(clock)
                this.content = '重新发送验证码'
                this.totalTime = 60
                this.canClick = true
              }
            }, 1000)
          }
        })
    },
    // input变化
    changeInput(e) {
      const values = this.form.getFieldsValue()
      if (
        values.againPassword !== undefined &&
        values.mobilephone !== undefined &&
        values.password !== undefined &&
        values.testing
      ) {
        this.reset = false
      } else {
        this.reset = true
      }
    },
    // 跳转登录页
    toUserLogin() {
      // this.$router.push({ path: '/Home/UserLogin' })
    },
    // 上一页
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
.cur {
  cursor: pointer;
}
.color-green {
  color: #68bb97;
}
.login {
  position: fixed;
  width: 100%;
  height: 100%;
  background: url('../../assets/login/welcome-banner.png') center center;
  background-size: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.login-info {
  height: 70%;
  width: 41%;
}
// 登录左侧
.login-info-left {
  width: 60%;
  height: 100%;
  background-color: #fff;
  .img {
    margin: 24px 0 0 16px;
  }
  // 登录方式 {
  .login-mothod {
    padding: 0 50px;
    /deep/.ant-tabs-nav .ant-tabs-tab {
      font-size: 18px;
      color: #4d5753;
    }
    /deep/.ant-tabs-ink-bar {
      width: 137px;
      height: 4px;
      border-radius: 5px;
      background-color: #68bb97;
    }
    // 表单
    /deep/.ant-input {
      border: none;
      border-bottom: 1px solid #f1f1f1;
      border-radius: 0;
    }
    .ant-input:focus {
      box-shadow: none;
    }
    /deep/ .ant-checkbox-inner {
      color: #8acf7a;
      border-color: #8acf7a;
    }
    /deep/.ant-checkbox-checked .ant-checkbox-inner {
      background-color: #8acf7a;
      border-color: #8acf7a;
    }
    // 重置按钮
    /deep/.ant-btn-primary {
      width: 100%;
      background-color: #3cbb8f;
      border-color: #3cbb8f;
    }

    // 手机格式 / 未注册
    .format {
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      text-align: center;
      color: #ff682c;
      border-radius: 30px;
      border: 1px solid #ff682c;
    }
  }
  // 验证码
  .testing {
    position: absolute;
    right: 0;
    top: -19px;
    display: inline-block;
    font-size: 12px;
    width: 114px;
    // padding: 3px 8px;
    text-align: center;
    border-radius: 30px;
    color: #fff;
    background-color: #3cbb8f;
    cursor: pointer;
  }
  .disabled {
    background-color: #d6d6d6;
    border-color: #d6d6d6;
    color: #fff;
    cursor: not-allowed; // 鼠标变化
  }
}
#components-form-demo-normal-login .login-form-button {
  width: 100%;
  height: 38px;
  background-color: #d6d6d6;
  border-color: #d6d6d6;
  border-radius: 20px;
}
</style>
