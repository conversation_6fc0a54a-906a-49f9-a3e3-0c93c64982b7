<template>
  <div>
    <!-- 1 学生 2 教师 -->
    <Student v-if="isStudent"></Student>
  </div>
</template>

<script>
// import Student from '@/components/StudentIndex/StudentIndex'
import Student from '@/views/Student/TaskCenter.vue'
import Mandishome from '@/components/MandisHome/Mandishome'
export default {
  components: {
    Student
  },
  created () {
    this.id = localStorage.getItem('Id')
    this.mobilephone = localStorage.getItem('mobilePhone')
    this.isTeacher = localStorage.getItem('isTeacher')
    // this.isStudent = localStorage.getItem('isStudent')
    this.isStudent = localStorage.getItem('isStudent') == '1'
    this.isTeachingStaff = localStorage.getItem('isTeachingStaff')
    // console.log(this.isStudent+':introduce的isStudent')
    // this.getUserInfo()
  },
  mounted () {
    // this.getRoles()
  },
  watch: {
    $route () {
      const Name = window.location.href.split('?')[1]
      this.id = localStorage.getItem('Id')
      if (this.$route.fullPath === '/Home/Introduce' + Name) {
        this.isStudent = localStorage.getItem('isStudent') == '1'
        // this.getUserInfo()
      }
    }
  },
  data () {
    return {
      id: '',
      mobilephone: '',
      isTeacher: false,
      isStudent: false,
      isSchoolHeader: false,
      isTeachingStaff: '0',
      role: ''
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo () {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.mobilephone }).then((res) => {
        console.log('introduce.vue')
        const identityId = localStorage.getItem('role')
        const roles = res.Data.Roles
        localStorage.setItem('LoginSource', res.Data.LoginSource)
        if (!res.Data.Roles) {
          this.isStudent = true
        } else {
          roles.forEach((value) => {
            if (value.Id === identityId) {
              this.role = value.RoleName
            }
          })
        }
        this.isTeacher = true
      })
    }
  }
}
</script>
<style scoped>
</style>
