<template>
  <div>
    <!--  -->
    <div class="banner">
      <!-- 导航菜单 -->
      <Nav></Nav>
      <!-- 下载 -->
      <div class="down">下载APP</div>
      <!-- app -->
      <div class="down-app-photo">
        <span class="down-and">
          <p><img class="down-android-hover" src="@/assets/down/安卓端.png" alt=""></p>
          <p><img class="down-android img-w" src="@/assets/down/anz1.png" alt=""></p>

        </span>
        <span class="down-ios">
          <p><img class="down-android-hover" src="@/assets/down/IOS端.png" alt=""></p>
          <!-- <p><img class="down-ios-hover" src="@/assets/down/iOS1.png" alt=""></p> -->
          <p><img class="down-ios-hover img-w" src="@/assets/down/iOS1.png" alt=""></p>
        </span>
      </div>
      <!-- 网校数据 -->
      <div class="student-data">
        <img class="stu-img" src="@/assets/user/index-resources.png" alt="">
        <img class="stu-img" src="@/assets/user/index-student.png" alt="">
        <img class="stu-img-only" src="@/assets/user/index-teacher.png" alt="">
      </div>
    </div>
    <!-- 底部 -->
    <Footer></Footer>
  </div>
</template>

<script>
import Nav from '@/components/XuHuiNav/XuHuiNav'
import Footer from '@/components/XuHuiFooter/XuHuiFooter'
export default {
  components: {
    Nav,
    Footer
  }
}
</script>

<style lang="less" scoped>
.img-w {
  width: 440px;
}
.banner {
  // height: 1132px;
  background: url('../../assets/down/bigbgc.png') center center no-repeat;
  background-size: 100% 100%;
  /* background: url('../../assets/down/'); */
}
.down {
  margin-top: 105px;
  font-size: 100px;
  text-align: center;
  color: #fff;
}
.down-android-hover {
  width: 300px;
  height: 100px;
}
.stu-img {
  width: 116px;
  height: 160px;
}
.stu-img-only {
  width: 80px;
  height: 160px;
}
// 下载app
.down-app-photo {
  margin-top: 133px;
  text-align: center;
  .down-and:hover .down-android {
    visibility: visible;
  }
  .down-android {
    visibility: hidden;
  }
  .down-ios:hover .down-ios-hover {
    visibility: visible;
    // display: none;
  }
  .down-ios-hover {
    visibility: hidden;
    // display: inline-block;
  }
  p {
    margin-bottom: 0;
  }
  span {
    display: inline-block;
    margin-right: 360px;
  }
  span:nth-child(2) {
    margin-right: 0;
  }
}
// 网校数据
.student-data {
  height: 185px;
  margin-top: 113px;
  text-align: center;
  // background: url('../../assets/user/banner.png') center center no-repeat;
  background-size: 100% 100%;
  img {
    margin-right: 347px;
  }
  img:nth-child(3) {
    margin-right: 0;
  }
}
.footer {
  margin-top: 0;
}
</style>
