<template>
  <div>
    <div class="Answer" id="AnswerPaper">
      <div class="Answer_left">
        <div class="left_title">
          <span @click="GoBack"><i class="el-icon-arrow-left"></i>返回</span>
          <span v-html="PaperName" v-katex></span>
          <span></span>
        </div>
        <div class="left_info" id="hello" v-katex>
          <div class="paper_item" v-for="(item, index) in PaperList" :key="index">
            <div class="paper_type" v-if="num === index">
              <div class="type_after"></div>
              <span v-if="item.TypeId == 2">单项选择题</span>
              <span v-if="item.TypeId == 5 || item.TypeId == 42">填空题</span>
              <span v-if="item.TypeId == 10">多项选择题</span>
              <span v-if="item.TypeId == 11">判断题</span>
              <span v-if="item.TypeId == 23">应用题</span>
              <span v-if="item.TypeId == 36">主观题</span>
              <span v-if="item.TypeId == 39">题型</span>
              <span v-if="item.TypeId == 40">可变行列表</span>
              <span v-if="item.TypeId == 41">可变行填空</span>
              <span v-if="item.TypeId == 43">可增加填空</span>
              <span v-if="item.TypeId == 44">可选择填空</span>
              <span v-if="item.TypeId == 45">可为空填空</span>
              <span v-if="item.TypeId == 46">可下拉选择题</span>
              <span v-if="item.TypeId == 47">思考问答题</span>
            </div>
            <div class="title_item" v-if="num === index">
              <div class="Level_box">
                <span style="font-size: 18px">第{{ index + 1 }}题</span>
              </div>
              <span v-html="item.Title" style="font-size: 18px" class="formatImg" v-katex></span>
              <!-- 单项选择题 -->
              <div v-show="item.TypeId == 10">
                <el-checkbox-group
                  v-model="Checklist"
                  @change="onCheckChange"
                  v-for="(check, ind) in item.AuditPaperItemAnswers"
                  :key="ind"
                >
                  <el-checkbox :label="check.Option + ''">{{ check.Option }}:{{ check.Content }}</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>

            <div class="Topic_type" v-if="num === index"></div>
          </div>
        </div>
      </div>
      <!--右侧 -->
      <div class="Answer_right">
        <div class="right_header">
          <img src="@/assets/student/闹钟.png" alt="" />
          <br />
          <!-- <span> 总计{{ CallinTime }}</span> -->
          <br />
          <br />
          <!-- <span>
            <span style="color: #61bb96">{{ CurrentNum }}</span
            >/ <span>{{ PaperNum }}</span> 题</span
          > -->
        </div>
        <div class="AnswerRight_list">
          <div class="List_btn">
            <div
              v-for="(item, index) in PaperList"
              :class="{ 'title-btn-whole': true, 'has-answer': item.HasAnswer, 'current-answer': item.CurrentAnswer }"
              :key="index"
              @click="targetIndex(index, item)"
            >
              <span>{{ index + 1 }}</span>
            </div>
          </div>
          <div class="submit_btn">
            <el-button @click="submit" type="primary">提交</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = '1514871130526060545'
    this.GetAuditPaperItemsList()
    this.GetTheData()
  },
  mounted() {},
  data() {
    return {
      num: 0,
      paperId: '',
      userId: '',
      PaperName: '',
      PaperList: [],
      Checklist: [],
    }
  },
  watch: {
    $route() {
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerPaper?' + ChangId) {
      }
    },
  },
  methods: {
    // 获取试卷的名称
    async GetTheData() {
      const res = await this.$http.post('/Paper/Exam_Paper/GetTheData', {
        id: this.paperId,
      })
      this.PaperName = res.Data.Title
    },

    GoBack() {
      this.$confirm('是否退出答题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$router.go(-1)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退出',
          })
        })
    },

    // 获取试卷信息
    async GetAuditPaperItemsList() {
      const res = await this.$http.get('/Paper/Exam_Item/GetAuditPaperItemsList', {
        params: {
          paperId: this.paperId,
        },
      })
      if (res.Success) {
        this.PaperList = res.Data
        this.PaperList.forEach((el) => {
          el.Answer = []
        })
        console.log(this.PaperList, 'this.PaperList')
      }
    },

    onCheckChange(val) {
      console.log(val)
    },

    onRadioChange() {},

    // 下一题
    next(index, item) {},

    // 点击跳转相对应题目
    targetIndex(index, item) {
      this.num = index
    },

    submit() {},
  },
}
</script>

<style lang="less" scoped>
@import './commonStyle/perpar.less';
.radio_text {
  /deep/.ant-radio-group {
    display: flex;
    flex-direction: column;
    .ant-radio-wrapper {
      margin-bottom: 10px;
      white-space: pre-wrap !important;
    }
  }
}
/deep/ .MJXc-display {
  text-align: left !important;
}
/deep/.el-button {
  background: #61bb96;
  border-color: #61bb96;
  color: #fff;
  margin-top: 20px;
}
/deep/.ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0px;
  display: block;
  margin-bottom: 6px;
}
.outerdiv {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2;
  width: 100%;
  height: 100%;
}
.pic {
  width: 50%;
  position: relative;
  left: 25%;
  top: 25%;
}
</style>
<style  scoped>
.el-message-box {
  width: 300px;
}
</style>
