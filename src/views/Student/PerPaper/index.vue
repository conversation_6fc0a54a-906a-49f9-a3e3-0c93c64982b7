
<template>
  <div class="task-center">
    <div class="ask-catalog">
      <span class="catalog_header">
        <p class="pc_font_size18 font_size20 ipad_font_size22">章 节 目 录</p>
      </span>
      <a-tree checkable :tree-data="treeData" :replace-fields="replaceFields" @select="onSelect" @check="onCheck" />
    </div>
    <div class="task-info">
      <div class="info_header">
        <div style="display: flex">
          <span class="info_title pc_font_size18 font_size20 ipad_font_size22" @click="GoBack"><i class="el-icon-arrow-left"></i>个性练习</span>
          <p style="flex-grow: 1; text-align: right" class="staybtn">
            <el-button class="pc_font_size14 font_size16 ipad_font_size18" style="background: #61bb96; border-color: #61bb96" type="primary" v-if="type == '2'"
              >跟进练习（{{ count.FollowCount }}道）</el-button
            >
            <el-button class="pc_font_size14 font_size16 ipad_font_size18" v-if="type == '1'" @click="changeType('2')">跟进练习（{{ count.FollowCount }}道）</el-button>
            <el-button class="pc_font_size14 font_size16 ipad_font_size18" style="background: #61bb96; border-color: #61bb96" type="primary" v-if="type == '1' && LoginSource == 1"
              >自适应练习（{{ count.AdaptiveCount }}道）</el-button
            >
            <el-button class="pc_font_size14 font_size16 ipad_font_size18" v-if="type == '2' && LoginSource == 1" @click="changeType('1')">自适应练习（{{ count.AdaptiveCount }}道）</el-button>
          </p>
        </div>
        <el-divider></el-divider>
        <span
          class="Time_span pc_font_size16 font_size18 ipad_font_size20"
          v-for="(item, index) in TitleList"
          @click="ChangeStatus(index, item)"
          :class="{ PageStatus: StatusNum == index }"
          :key="index"
          >{{ item.name }} {{ item.Num }}道</span
        >
        <span v-show="Status == 3" style="float: right">
          <span
            class="Error_span pc_font_size14 font_size16 ipad_font_size18"
            v-for="(stem, indx) in ErrorStaustList"
            @click="ChangeError(indx, stem)"
            :key="'info1-' + indx"
            :class="{ ErrorStatus: ErrorNum == indx }"
          >
            {{ stem.name }}
          </span>
        </span>
      </div>
      <!-- v-show="Status == 1" -->
      <div style="margin-top: 10px; padding-bottom: 15px; height: 60vh">
        <div style="margin: 0px auto; padding: 100px 0; text-align: center" v-if="examList.length == 0">
          <img src="@/assets/lack/暂无搜索记录.png" alt="" />
          <p>暂无记录哦</p>
        </div>
        <div class="staybox">
          <div class="item_box" v-for="(item, index) in examList" :key="index">
            <div class="staycontent">
              <!-- <span style="color: #464646">{{ item.PaperTitle }}</span> -->
              <span class="pc_font_size18 font_size20 ipad_font_size24" style="color: #464646">{{ item.ChapterText }}</span>
              <span class="pc_font_size14 font_size18 ipad_font_size20" style="float: right; color: #293bea; cursor: pointer" @click="sawOriginExam(item)"
                >查看原练习</span
              >
              <br />
              <span class="pc_font_size16 font_size18 ipad_font_size22" style="color: #61bb96">{{ item.PaperTitle }}</span>
              <br />
              <span class="pc_font_size16 font_size18 ipad_font_size22">共{{ item.ItemCount }}道练习</span>
              <span class="pc_font_size14 font_size16 ipad_font_size18" style="margin:0 20px;" v-if="[3,4,5].indexOf(item.Status) != -1">已订{{item.CorrectCount}}道</span>
              <span class="pc_font_size14 font_size16 ipad_font_size18" v-if="[3,4,5].indexOf(item.Status) != -1">未订{{item.NoCorrectCount}}道</span>
            </div>
            <!-- 待完成：去练习 -->
            <div class="staybtn" v-show="Status == 1">
              <el-button class="pc_font_size14 font_size16 ipad_font_size18" @click="GoPractice(item)" >去练习</el-button>
            </div>
            <!-- 已完成：做对的和订正正确的-查看解析和去巩固  未订正的和订正错误的-查看解析和去订正 -->
            <div class="readybtn" v-show="Status == 2" >
              <el-button class="pc_font_size14 font_size16 ipad_font_size18" style="background: #f7a422; border-color: #f7a422" @click="GoAnalysis(item)" v-show="item.Status  == 2 || item.Status == 6"
                >查看解析</el-button
              >
              <el-button class="pc_font_size14 font_size16 ipad_font_size18" style="background: #61bb96; border-color: #61bb96" @click="toSteadyExam(item)" v-show="item.Status == 6 && item.NoCorrectCount != 0"
                >去巩固</el-button
              >
            </div>
            <!-- 错题：未订正-去订正 订正正确-查看解析和去巩固 订正错误-去订正 -->
            <div class="readybtn" v-show="Status == 3">
              <template v-if="item.Status == 4">
                <!-- <el-button style="background: #f7a422; border-color: #f7a422" @click="GoAnalysis(item)" 
                  >查看解析</el-button
                > -->
                <el-button class="pc_font_size14 font_size16 ipad_font_size18" style="background: #61bb96; border-color: #61bb96" @click="toSteadyExam(item)" >去巩固</el-button>
              </template>
              <el-button
                class="pc_font_size14 font_size16 ipad_font_size18"
                style="background: #61bb96; border-color: #61bb96"
                v-if="item.Status == 3 || item.Status == 5"
                @click="toCorrect(item)"
                >去订正</el-button
              >
            </div>
          </div>
        </div>
        <div style="text-align: center" v-if="examList.length > 0">
          <el-pagination
            @size-change="handlePage"
            @current-change="handlePage"
            background
            :page-size="8"
            layout="prev, pager, next, jumper"
            :current-page.sync="Page"
            :total="TotalItems"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.chapterTree()
    this.GetStudentAdaptiveStatistics()
    this.GetTotal()
  },
  data() {
    return {
      Page: 1,
      StatusNum: 0,
      ErrorNum: 0,
      Status: 1,
      checkedKeys: '',
      selectedKeys: [],
      treeData: [],
      TitleList: [],
      Adaptive: {},
      replaceFields: {
        children: 'Second',
        title: 'ChapterName',
        key: 'ChapterId',
      },
      ErrorStaustList: [
        {
          value: 0,
          name: '全部',
        },
        {
          value: 1,
          name: '未订正',
        },
        {
          value: 2,
          name: '已订正正确',
        },
        {
          value: 3,
          name: '已订正错误',
        },
      ],
      examList: [],
      count: {
        adaptiveCount: 0, //自适应练习
        followCount: 0, // 跟进练习
      },
      type: '2', //自适应练习1 跟进练习2
      TotalItems: 0,
      LoginSource: localStorage.getItem('LoginSource')
    }
  },
  filters: {},
  watch: {},
  methods: {
    //切换分页
    handlePage(val){
      this.Page = val
      this.GetStudentAdaptiveStatistics()
    },
    //切换type
    changeType(type) {
      this.type = type
      this.examList = []
      this.GetStudentAdaptiveStatistics()
    },
    //个性练习数目统计
    async GetTotal() {
      const res = await this.$uwonhttp.get(
        '/Paper/Paper/GetStudentAdaptiveTotalStatistics?studentId=' + localStorage.getItem('UserId')
      )
      this.count = res.data.Data
    },
    GoBack() {
      this.$router.go(-1)
    },
    // 章节数
    async chapterTree() {
      const res = await this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', {
        userid: localStorage.getItem('UserId'),
        IsWeek: 1,
      })
      this.treeData = res.data.Data
    },
    // 点击复选框触发
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys.join(',')
      this.paperIndex = 1
    },
    // 点击树节点触发
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    async GetStudentAdaptiveStatistics() {
      const res = await this.$uwonhttp.post('/Paper/Paper/GetStudentAdaptiveStatistics', {
        studentId: localStorage.getItem('UserId'),
        type: this.type,
      })
      if (res.data.Success) {
        let Adaptive = res.data.Data
        let arr = [
          { name: '待完成', Num: Adaptive.NoFilshCount, Status: 1 },
          { name: '已完成', Num: Adaptive.CompletedCount, Status: 2 },
          { name: '错题', Num: Adaptive.ErrorCount, Status: 3 },
        ]
        this.TitleList = arr
        this.GetStudentAdaptivePage()
      }
    },

    ChangeStatus(index, item) {
      this.StatusNum = index
      this.Status = item.Status
      this.GetStudentAdaptivePage()
    },

    ChangeError(index, item) {
      this.ErrorNum = index
      this.GetStudentAdaptivePage()
    },

    async GetStudentAdaptivePage() {
      let params = {
        Type: this.type,
        Page: this.Page,
        UserId: localStorage.getItem('UserId'),
        Status: this.Status,
        ErrorStatus: '', //错题筛选项
        Size: 8,
      }
      if (this.Status == 3) {
        params.ErrorStatus = this.ErrorNum
      } else {
        delete params.ErrorStatus
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetStudentAdaptivePage', params)
      if (res.data.Success) {
        this.examList = res.data.Data.Items
        //总条数 分页用
        this.TotalItems = res.data.Data.TotalItems
      }
    },
    //跟进练习答题
    GoPractice(item) {
      const subject = {
        Id: item.SubjectId,
        name: item.SubjectName,
      }
      this.$router.push({
        // path: '/Student/PerPaper/PracticePage',
        path: '/Student/Exam_Student/AnswerReady',
        query: {
          paperId: item.PaperId,
          type: this.type,
          HaveSpecial: 'false',
          from:'perpaper',
          SubjectIdName:JSON.stringify(subject),
        },
      })
    },
    //跟进练习查看解析
    GoAnalysis(item) {
      const subject = {
        Id: item.SubjectId,
        name: item.SubjectName,
      }
      this.$router.push({
        path: '/Student/Exam_Student/AnswerAnalysis',
        query: {
          paperId: item.PaperId,
          type: this.type,
          SubjectIdName:JSON.stringify(subject),
        },
      })
    },
    //去订正
    toCorrect(item) {
      const subject = {
        Id: item.SubjectId,
        name: item.SubjectName,
      }
      //需要订正的题目数量为0 不需要订正 NoCorrectCount
      this.$router.push({
        path: '/Student/Exam_Paper/correctPersonalPractice',
        query: {
          paperId: item.PaperId,
          type: this.type,
          from: 'per',
          SubjectIdName:JSON.stringify(subject),
        },
      })
    },
    sawOriginExam(item) {
      const subject = {
        Id: item.SubjectId,
        name: item.SubjectName,
      }
      this.$router.push({
        path: '/Student/Exam_Student/FollowReview',
        query: {
          paperId:item.PaperId,
          SubjectIdName: JSON.stringify(subject),
        },
      })
    },
    toSteadyExam(item) {
      const subject = {
        Id: item.SubjectId,
        name: item.SubjectName,
      }
      this.$router.push({
        path: '/Student/PerPaper/steadyExam',
        query: {
          paperId:item.PaperId,
          type: this.type,
          SubjectIdName:JSON.stringify(subject),
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
@media (min-width: 1600px) {
  .ant-col-xxl-5 {
    width: 100% !important;
  }
}
.task-center {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.ask-catalog {
  width: 16%;
  //margin-right: 25px;
  .catalog_header {
    display: block;
    background: #61bb96;
    padding: 6px 0;
    border-radius: 5px;
    p {
      //font-size: 19px;
      text-align: center;
      color: #fff;
    }
  }
  /deep/.ant-tree-title {
    font-size: 20px;
    font-weight: 700;
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li {
    margin: 0;
    padding: 10px 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }
}
.task-info {
  width: 82%;

  // flex-grow: 1;
  .info_header {
    padding: 10px 15px;
    border-radius: 8px;
    background: #fff;
    .info_title {
      font-size: 19px;
      cursor: pointer;
    }
    .Time_span {
      font-size: 18px;
      margin-right: 40px;
      color: #747474;
      cursor: pointer;
    }
    .Error_span {
      margin-right: 10px;
      font-size: 18px;
      cursor: pointer;
    }
    .PageStatus {
      color: #61bb96;
      cursor: pointer;
    }
    .ErrorStatus {
      color: #ff4715;
    }
  }
  .staybox {
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
    .item_box {
      width: 23%;
      margin-right: 2%;
      background: #fff;
      border-radius: 8px;
      margin-bottom: 10px;
      span {
        font-size: 18px;
      }
      .staycontent {
        padding: 10px;
        line-height: 38px;
        .icon_p {
          width: 30px;
          height: 30px;
          text-align: center;
          line-height: 30px;
          margin-left: 5px;
          font-size: 15px;
          border-radius: 50%;
          display: inline-block;
        }
        .backp {
          background: #c2c2c2;
          color: #fff;
        }
        .backcolor {
          background: rgba(255, 109, 52, 0.2);
          color: #ff4715;
        }
      }
      .staybtn {
        text-align: center;
        border-radius: 5px;
        padding: 8px;
        /deep/.el-button {
          width: 120px;
          color: #fff;
          background: #61bb96;
          border-color: #61bb96;
        }
      }
      .readybtn {
        display: flex;
        justify-content: space-around;
        padding: 10px;
        /deep/.el-button {
          width: 120px;
          color: #fff;
        }
      }
    }
  }
  .staybox:after {
    content: '';
    width: 30%;
  }
}

// 筛选菜单
.screen-menu {
  /deep/.ant-select-selection--single {
    border: none;
    background-color: #fff;
  }
}
/deep/.el-divider--horizontal {
  margin: 10px 0;
}
/deep/.el-pagination.is-background .el-pager li:not(.disabled).active{
  background-color: rgb(97, 187, 150);
}
/deep/.el-pagination.is-background .el-pager li:not(.active):hover{
  color: rgb(97, 187, 150);
}
</style>
