.Answer {
    // width: 85%;
    width: 100%;
    height: auto;
    display: flex;
    justify-content: space-between;

    .Answer_left {
        width: 70%;
        border-radius: 5px;
        padding: 20px;
        position: relative;
        background: #ffff;

        .left_title {
            display: flex;
            justify-content: space-between;

            span:nth-child(1) {
                font-size: 16px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #858585;
                line-height: 30px;
                cursor: pointer;
            }

            span:nth-child(2) {
                font-size: 22px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #3e3e3e;
            }
        }

        .left_info {
            .paper_type {
                display: flex;
                margin-top: 20px;
                padding: 6px;

                .type_after {
                    width: 5px;
                    height: 30px;
                    background: #2751d8;
                    border-radius: 5px;
                }

                span {
                    font-size: 18px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #3e3e3e;
                    line-height: 33px;
                    margin-left: 10px;
                }
            }

            .title_item {
                span {
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #828282;
                    margin-top: 30px;
                    display: inline-block;
                }
            }

            .btn_text {
                text-align: center;
                margin-top: 20px;

                .btn_next {
                    width: 150px;
                    border-radius: 20px;
                    margin-left: 10px;
                    background: #61bb96;
                    border-color: #61bb96;
                    color: #fff;
                }
            }
        }
    }

    .Answer_right {
        width: 29%;
        padding: 20px;
        border-radius: 5px;
        background: #ffff;

        .right_header {
            text-align: center;
            line-height: 45px;
            padding: 20px;

            img {
                height: 30px;
            }

            span {
                font-size: 20px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                display: contents;
            }
        }

        .AnswerRight_list {
            padding: 0 40px;
            text-align: center;
            margin-left: 25px;

            .List_btn {
                display: flex;
                flex-wrap: wrap;
                position: relative;
                left: 10%;
            }

            .title-btn-whole {
                display: inline-block;
                width: 41px;
                height: 40px;
                line-height: 40px;
                margin: 16px 16px 0 0;
                text-align: center;
                border: 1px solid #ccc;
                border-radius: 35px;
                cursor: pointer;
            }

            .has-answer {
                color: #fff;
                background-color: #61bb96;
            }

            .current-answer {
                border-color: #da1b1b;
                color: #da1b1b;
                background-color: #fff;
            }
        }

        .submit_btn {
            margin-top: 50px;
            text-align: center;

            .el-button {
                width: 110px;
            }
        }
    }
}

.input_text {
    width: 100px;
    border-bottom: 1px solid #828282;
    text-align: center;
    font-size: 20px;
}

.Table_Btn {
    width: 77%;
    text-align: center;
    margin-top: 25px;
}

.tableTitle {
    border-collapse: collapse;
    text-align: center;
    display: flex;
    flex-wrap: wrap;
    
}

.tableTitle tr {
    border: none;
    // width: 200px;
}
.tableTitle tr:nth-child(n+1){
    border-top:none;
}
.tableTitle td {
    width: 181px;
    height: 50px;
    font-size: 17px;
    cursor: pointer;
    border: 1px inset #cccc;
    border:0;
}

.tableTitle tbody {
    margin-top: -1px;
}

.Blank_box {
    border: 1px solid #ccc;
    padding: 15px;
    margin-top: 20px;

    .blank_Answer {
        font-size: 22px;
    }

    span {
        p {
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #828282;
            white-space: pre-line;
            line-height: 40px;
        }
    }
}

.select_box {
    margin-top: 20px;

    span {
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #828282;
        line-height: 40px;
    }
}

.show_btn {
    .radio_box {
        margin-top: 15px;
        line-height: 40px;

        span {
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #828282;
        }
    }
}

.IMGkey_word {
    width: 30%;
    height: 35px;
    display: inline-block;
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    cursor: pointer;

    .backcolor_gray {
        display: inline-block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #ccc;
        display: flex;
        align-items: center;
        justify-content: space-around;

        img {
            width: 25px;
            height: 25px;
        }
    }

    .backcolor_blue {
        display: inline-block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #357bee;
        display: flex;
        align-items: center;
        justify-content: space-around;

        img {
            width: 25px;
            height: 25px;
        }
    }

    b:nth-child(n+1) {
        font-weight: normal;
        margin-left: 10px;
    }
}

.pure-table {
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #cbcbcb;
}

.pure-table caption {
    color: #000;
    font: italic 85%/1 arial, sans-serif;
    padding: 1em 0;
    text-align: center;
}

.pure-table td,
.pure-table th {
    border-left: 1px solid #cbcbcb;
    border-width: 0 0 0 1px;
    font-size: 20px;
    margin: 0;
    overflow: visible;
    padding: 15px 60px;
    text-align: center;
}

.pure-table thead {
    background-color: #e0e0e0;
    color: #000;
    text-align: left;
    vertical-align: bottom;
}

.pure-table td {
    background-color: transparent;
}

.pure-table-bordered td {
    border-bottom: 1px solid #cbcbcb;
}

.pure-table-bordered tbody>tr:last-child>td {
    border-bottom-width: 0;
}

.check_active {
    color: #2751d8;
    cursor: pointer;
}