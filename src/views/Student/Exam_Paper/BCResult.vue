<template>
  <div class="examine">
    <div class="div" style="width: 70%;">
      <p class="navigation">
        <span>复习回顾</span>
        <span class="cur" @click="toMicroClass">微课堂</span>
      </p>
      <p class="f-t title">{{ paperName }}</p>
      <div class="example" v-if="loadPaper" style="text-align: center;width:100%;">
        <a-spin />
      </div>
      <happy-scroll v-if="paperList.length !== 0">
        <div class="alone-tit" v-for="(item,index) in paperList" :key="item.ItemId" :id="item.ItemId">
          <p>
            <span style="font-size: 17px">({{ index + 1 }})</span><span v-html="item.ItemTitleWeb" style="font-size:17px;" v-katex></span>
            <span>
              <img v-if="item.IsKeyTrue === 1" src="@/assets/student/对号.png" alt="">
              <img v-if="item.IsKeyTrue === 0" src="@/assets/student/叉号.png" alt="">
            </span>
          </p>
          <p>
            <a-radio-group v-model="value" @change="onChange" v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
              <a-radio :value="item.ItemId +'|'+ k.Opt" v-for="(k, j) in item.Options" :key="j">
                <span style="fontSize: 20px;">{{ k.Opt }}</span>.&nbsp;<span v-html="k.Content" style="fontSize: 20px;" v-katex></span>
              </a-radio>
            </a-radio-group>
          </p>
          <div v-if="item.ItemTypeId === 10">
            <a-checkbox-group class="Multiple" >
              <a-row>
                <a-col :span="24">
                  <a-checkbox v-for="(i) in item.Options" :key="i.Opt" :value="item.ItemId + '|' + i.Opt">
                    {{ i.Opt }}:
                    <span v-html="i.Content" style="fontSize: 20px;" v-katex></span>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>
          <!-- <p><span>教师评语：</span><span>xxxxxxxxxxxxxxxxxxxx</span></p> -->
          <p class="line"></p>
          <p class="clearfix" style="font-size: 15px">
            <span class="fl" :class="{ 'correct': item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 0}">我的作答：{{ item.UserAnswer }}</span>
            <!-- <span class="fg tutored">已辅导</span> -->
          </p>
          <p style="font-size: 15px">正确答案：{{ item.Answer }}</p>
          <p @click="seeAnalysis(item.ItemId)" class="cur" style="font-size:15px;color:#68BB97;">查看解析</p>
          <p style="font-size: 16px" v-show="analysis === item.ItemId">解析:  <span v-html="item.Analysis" v-katex></span>  </p>
          <!-- <p class="wrong-storage"><span>放入错题收纳库</span></p> -->
        </div>
      </happy-scroll>

    </div>
    <div class="div2" style="width: 30%;">
      <p class="clearfix" style="margin-bottom:32px;">
      </p>
      <div>
        <div class="Statistics">
          <p class="p-chart">
            <a-progress
              type="circle"
              :percent="wrongNum/paperSubjectNum"
              height="100px"
              :width="50"
              :format="() => wrongNum+'/'+paperSubjectNum"
              :strokeColor="stuStatistics"
            >
            </a-progress><br>
            <span>正确题数</span>
          </p>
        </div>
        <p v-if="revision" @click="toBCRevision" class="wrong-topic cur"><span>去订正</span></p>
        <div class="List-topics">
          <p class="menu">待订正错题</p>
          <a
            v-for="(i, t) in paperList"
            :key="t"
            class="title-btn-whole"
            :class="{ 'def-color': defalutCol === i.ItemId, 'wrong-bgc': i.IsKeyTrue === 0 }"
            @click="switchTitle(t, i.ItemId, i.TypeId)"
          >
            {{ t + 1 }}
          </a>
          <p class="menu-consolidate">巩固练习推荐</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { HappyScroll } from 'vue-happy-scroll'
export default {
  components: {
    HappyScroll
  },
  created () {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.getPaperList()
    this.getPaperInfor()
  },
  watch: {
    $route () {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Paper/BCResult?' + ChangId) {
        this.getPaperList()
        this.getPaperInfor()
      }
    }
  },
  data () {
    return {
      // 加载
      loadPaper: true,
      userId: '',
      paperId: '',
      // 试卷名称
      paperName: '',
      chapter: '',
      chapterId: '',
      // 默认第一题
      defalutCol: '',
      // 切换题目数
      newSubject: 1,
      // 题目数
      paperSubjectNum: '',
      // 错题数
      wrongNum: '',
      // 真确题数
      correctNum: '',
      // 是否要订正
      revision: false,
      // 试卷信息
      paperList: [],
      // 精度
      stuStatistics: '#3CB98F',
      value: 1,
      analysis: false
      // radioStyle: {
      //   display: 'block',
      //   height: '30px',
      //   lineHeight: '30px'
      // }
    }
  },
  methods: {
    seeAnalysis (id) {
      this.analysis = id
    },
    // 获取试卷基本信息
    getPaperInfor (id) {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then(res => {
        console.log('试卷基本信息', res)
        this.paperName = res.Data.Title
        this.chapterId = res.Data.ChapterId
      })
    },
    // 复习回顾题目详情
    getPaperList () {
      this.$uwonhttp.post('/ExamItem/ExamItem/GetStudentWrongBCPaperItems', { PaperId: this.paperId, UserId: this.userId, DataType: 2 }).then(res => {
        console.log('复习回顾题目---', res)
        this.paperList = res.data.Data.Items

        this.defalutCol = res.data.Data.Items[0].ItemId
        // 题目数
        this.paperSubjectNum = res.data.Data.Items.length
        const arr = this.paperList.filter(value => {
          return value.IsKeyTrue === 2
        })
        const arr1 = this.paperList.filter(value => {
          return value.IsKeyTrue === 1
        })
        this.revision = this.paperList.some(value => {
          return value.IsKeyTrue === 2
        })
        this.paperList.forEach((item, index) => {
          const reg = /(#&\d+@)/g
          const inputele = ' '
          const stem = item.ItemTitleWeb.replace(reg, inputele)
          item.ItemTitleWeb = stem
        })
        this.wrongNum = arr.length
        this.correctNum = arr1.length
        console.log('错题', arr)
        this.loadPaper = false
      })
    },
    // 切换题目
    switchTitle (index, ItemId, typeId) {
      // console.log(index)
      document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.defalutCol = ItemId
    },
    // 选项选择
    onChange (value) {

    },
    // 微课堂
    toMicroClass () {
      this.$router.push({ path: '/Student/Exam_Student/MicroClass', query: { chapterId: this.chapterId, paperId: this.paperId, isBc: '1' } })
    },
    // bc订正
    toBCRevision () {
      this.$router.push({ path: '/Student/Exam_Paper/BCRevision', query: { paperId: this.paperId, chapterId: this.chapterId } })
    }
  }
}
</script>

<style lang="less" scoped>
  .f-t {
    font-size: 16px;
  }
  // 正确颜色
  .correct {
    color: #68BB97;
  }
  .answer-wrong {
     color: #FF682C;
  }
  .wrong-bgc {
    background-color: #FF682C;
  }
  .def-color {
    background-color: #68BB97;
  }
  .examine {
    display: flex;
    .div {
      // width: 300px;
      height: 700px;
      // background-color: #ddd;
      margin-right: 23px;
    }
  }
  .menu-consolidate {
    margin-top: 26px;
  }
  // 右边
  .div2 {
      // width: 300px;
      padding: 80px 16px 0 16px;
      background-color: #fff;
      margin-right: 23px;
      .p-chart {
        display: inline-block;
      }
      // 统计
      .Statistics {
        margin-bottom: 70px;
        text-align: center;
      }
      // 错题扫除
      .wrong-topic {
        text-align: center;
        margin-bottom: 80px;
        span {
          display: inline-block;
          width: 190px;
          height: 32px;
          line-height: 32px;
          color: #fff;
          background-color: #68BB97;
          border-radius: 30px;
        }
      }
  }
  .navigation {
    height: 38px;
    line-height: 38px;
    padding-left: 15px;
    font-size: 16px;
    background-color: #fff;
    border-radius: 5px;
    span:nth-child(1) {
      display: inline-block;
      height: 100%;
      margin-right: 25px;
      border-bottom: 2px solid #68BB97;
    }
  }
  .title {
    margin: 24px 0;
  }
  // 单题
  .alone-tit {
    width: 100%;
    padding: 10px 30px;
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 5px;
    .line {
      height: 1px;
      border-bottom: 1px solid #F6F6F6;
    }
    .wrong-storage {
      span {
         display: inline-block;
          width: 190px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          color: #fff;
          background-color: #68BB97;
          border-radius: 30px;
          cursor: pointer;
      }
    }
    // 已辅导
    .tutored {
      display: inline-block;
      width: 74px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #FF682C;
      border: 1px solid #FF682C;
      border-radius: 5px;
      cursor: pointer;
    }
  }
  // 题目目录
  .title-btn-whole {
      display: inline-block;
      width: 41px;
      height: 40px;
      line-height: 40px;
      margin: 16px 16px 0 0;
      text-align: center;
      border: 1px solid #ccc;
      border-radius: 35px;
      cursor: pointer;
    }
  /deep/.happy-scroll-content {
    width: 100%;
  }
</style>
