<template>
  <div class="revision-info">
    <div class="m-b">
      <p><img src="@/assets/teacher/编组 5.png" alt=""></p>
      <p class="paper-name">{{ paperName }}</p>
      <p><span class="sub-btn">共{{ ErrorItem + BCItem }}道题订正&巩固</span></p>
    </div>
    <p class="clearfix ">
      <span class="wait-revision">
        <p class="m-b"><img class="m-r" src="@/assets/teacher/矩形 (3).png" alt=""><span class="f-s-c">待订正错题</span></p>
        <p><span class="f-s">共{{ ErrorItem }}道</span><span @click="clearingBcWrong" class=" color cur m-t f-s-t">待订正</span></p>
      </span>
      <span style="display:inline-block">
        <p class="m-b"><img class="m-r" src="@/assets/teacher/矩形 (3).png" alt=""><span class="f-s-c">巩固练习推荐</span></p>
        <p><span class="f-s">共{{ BCItem }}道</span> <span @click="clearingBcWrong" class="color cur m-t f-s-t">去巩固</span></p>
      </span>
    </p>
    <p @click="clearingBcWrong" class="clearfix revision-btn cur "><span>订正巩固</span></p>

  </div>

</template>

<script>
export default {
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.chapterId = this.$route.query.chapterId
    this.paperName = this.$route.query.paperName
    this.getPaperWrongInfor()
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      this.chapterId = this.$route.query.chapterId
      this.paperName = this.$route.query.paperName
      if (this.$route.fullPath === '/Student/Exam_Paper/BCRevision?' + ChangId) {
        this.getPaperWrongInfor()
      }
    }
  },
  data() {
    return {
      userId: '',
      paperName: '',
      paperId: '',
      chapterId: '',
      BCItem: 0,
      ErrorItem: 0,
      paperData: {}
    }
  },
  methods: {
    getPaperWrongInfor() {
      this.$uwonhttp
        .post('/ExamItem/ExamItem/GetStudentWrongBCPaperItems', {
          UserId: this.userId,
          PaperId: this.paperId,
          DataType: 2
        })
        .then(res => {
          this.BCItem = res.data.Data.BCItemCnt
          this.ErrorItem = res.data.Data.ErrorItemCnt
        })
    },
    clearingBcWrong() {
      this.$router.push({
        path: '/Student/Exam_Paper/clearingBcWrong',
        query: {
          paperId: this.paperId,
          chapterId: this.chapterId
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.m-t {
  margin-top: 25px;
}
.m-b {
  margin-bottom: 25px;
}
.m-r {
  margin-right: 9px;
}
.f-s-t {
  font-size: 14px;
}
.f-s {
  font-size: 18px;
  margin-right: 10px;
}
.f-s-c {
  font-size: 18px;
  color: #222222;
  vertical-align: middle;
}
.paper-name {
  font-size: 20px;
  margin-top: 8px;
  margin-bottom: 10px;
}
.sub-btn {
  padding: 2px 10px;
  color: #bdd3ce;
  border: 1px solid #bdd3ce;
  border-radius: 15px;
}
.revision-info {
  width: 365px;
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -30%);
  text-align: center;
}
.revision-btn {
  height: 38px;
  line-height: 38px;
  text-align: center;
  color: #fff;
  background-color: #68bb97;
  border-radius: 25px;
}
// 待订正
.wait-revision {
  display: inline-block;
  margin-top: 25px;
  margin-bottom: 35px;
}
.bor {
  // border-bottom: 1px solid #ccc;
  margin-bottom: 15px;
}
.color {
  color: #68bb97;
}
</style>
