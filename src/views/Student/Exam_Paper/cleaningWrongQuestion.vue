<template>
  <div class="DIV_BOTTOM_DIV">
    <div class="paperheader pc_font_size20 font_size22 ipad_font_size24">
      <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
      <div style="display: flex; align-items: center">
        <span v-html="PaperName" v-katex></span>
        <div
          class="xuekeStyle"
          :style="{
            'background-color': bgColog[Multidisciplinary.SubjectId + 'bg'],
            color: bgColog[Multidisciplinary.SubjectId],
          }"
        >
          {{ Multidisciplinary.name }}
        </div>
      </div>
      <span>
        <span class="tiltcount">
          <span style="color: #61bb96">{{ CurrentNum }}</span
          >/ <span>{{ PaperNum }}</span> 题</span
        >
        <span></span><el-button @click="submit" type="primary">提交</el-button></span
      >
    </div>
    <div class="Answer" id="AnswerPaper">
      <div class="Answer_left">
        <div class="swiper_list">
          <div class="tea-analusis">
            <div class="line_border"></div>
            <swiper class="swiper" ref="mySwiper" :options="swiperOption">
              <swiper-slide style="text-align: center" v-for="(item, index) in PaperList" :key="index">
                <span
                  @click="targetIndex(index, item)"
                  :class="{
                    'title-btn-whole': true,
                    'has-answer': item.HasAnswer,
                    'current-answer': item.CurrentAnswer,
                  }"
                  >{{ index + 1 }}</span
                >
              </swiper-slide>
            </swiper>
            <div class="left-arrow">
              <div class="arrow-img" @click="prevPhoto">
                <i></i>
              </div>
            </div>
            <div class="right-arrow">
              <div class="arrow-img" @click="nextPhoto">
                <i></i>
              </div>
            </div>
          </div>
          <div class="imgbox">
            <!--            <span> 总计{{ CallinTime }}</span>-->
            <img src="@/assets/student/闹钟.png" alt="" />
            <span class="pc_font_size18 font_size20 ipad_font_size22" ref="CallinTime"></span>
          </div>
        </div>
        <div class="left_info flex" id="Img_id">
          <!-- 组合题题干 -->
          <div
            class="zuheti padding_t_20 padding_r_10 formatImg pc_font_size18 font_size28 ipad_font_size35"
            style="width: 30%; height: 60vh; overflow-y: scroll"
            v-if="zuheti.length > 0 && zuhetiTitle"
          >
            <p v-html="zuhetiTitle" v-katex style="max-width: 600px; min-width: 300px"></p>
          </div>
          <div class="paper_item" v-for="(item, index) in PaperList" :key="index">
            <div class="paper_type pc_font_size22 font_size24 ipad_font_size26" v-if="num === index">
              <div class="type_after"></div>
              <span v-if="item.ItemTypeId == 2">单项选择题</span>
              <span v-if="item.ItemTypeId == 5 || item.ItemTypeId == 42">填空题</span>
              <span v-if="item.ItemTypeId == 10">多项选择题</span>
              <span v-if="item.ItemTypeId == 11">判断题</span>
              <span v-if="item.ItemTypeId == 23">应用题</span>
              <span v-if="item.ItemTypeId == 36">主观题</span>
              <span v-if="item.ItemTypeId == 39">题型</span>
              <span v-if="item.ItemTypeId == 40">可变行列表</span>
              <span v-if="item.ItemTypeId == 41">可变行填空</span>
              <span v-if="item.ItemTypeId == 43">可增加填空</span>
              <span v-if="item.ItemTypeId == 44">可选择填空</span>
              <span v-if="item.ItemTypeId == 45">可为空填空</span>
              <span v-if="item.ItemTypeId == 46">可下拉选择题</span>
              <span v-if="item.ItemTypeId == 47">思考问答题</span>
              <span v-if="item.ItemTypeId == 52">选词填空题</span>
              <span v-if="item.ItemTypeId == 51">表格题</span>
              <span v-if="item.ItemTypeId == 50">连线题</span>
              <span v-if="item.ItemTypeId == 70">多项单选题</span>
              <span
                class="IMGkey_word"
                v-if="
                  item.ItemTypeId === 5 ||
                  item.ItemTypeId === 40 ||
                  item.ItemTypeId === 47 ||
                  item.ItemTypeId === 42 ||
                  item.ItemTypeId === 41 ||
                  item.ItemTypeId === 51
                "
              >
                <span @click="Review_Key" style="display: flex" v-if="!isMobile">
                  <b :class="showKey_color == false ? 'backcolor_gray' : 'backcolor_blue'" id="Imkey_word"
                    ><img src="@/assets/Key_word.png" alt=""
                  /></b>
                  <b>使用教学键盘</b>
                </span>
                <span id="Imkey_word" style="display: flex" v-if="isMobile">
                  <b class="backcolor_blue"><img src="@/assets/Key_word.png" alt="" /></b>
                  <b @click="Review_Key_pad('key')" :class="boardType == 'sys' ? 'check_active' : ''"> 使用教学键盘 </b>
                  <b @click="Review_Key_pad('sys')" :class="boardType == 'key' ? 'check_active' : ''">使用系统键盘</b>
                </span>
              </span>
            </div>
            <!-- <div v-if="num === index">
              <div>答案：{{item.ShowAnswer}}</div>
              <div>旧答案：{{item.ShowUserAnswer}}</div>
            </div> -->
            <div class="title_item" v-if="num === index" v-katex>
              <div class="Level_box">
                <p class="pc_font_size20 font_size24 ipad_font_size26">
                  第{{ index + 1 }}题
                  <span style="color: orange; font-size: 14px; color: #409eff" v-if="reAnswerTip"
                    >（提示：这道题有多种答案，也可以重新作答）</span
                  >
                </p>
                <el-button
                  class="btn_next pc_font_size16 font_size20 ipad_font_size26"
                  @click="next(index, item)"
                  v-show="!nextDisabled"
                  >下一题</el-button
                >
                <el-button
                  class="btn_next pc_font_size16 font_size20 ipad_font_size26"
                  @click="submit"
                  v-show="nextDisabled"
                  >提交</el-button
                >
                <el-button
                  class="btn_next pc_font_size18 font_size22 ipad_font_size24"
                  style="width: 120px"
                  v-if="
                    item.ItemTypeId == 36 &&
                    (item.ReviewContentType == 1 || item.ReviewContentType == 2 || item.ReviewContentType == 3) &&
                    isMobile
                  "
                  @click="watchFirstPhotos(item)"
                  >查看首次作答</el-button
                >
                <div class="submit_btn" style="width: 22%; display: flex" v-if="reAnswer">
                  <el-button type="primary" v-if="reAnswer" @click="hideAnswer">重新作答</el-button>
                </div>
              </div>
              <span
                v-if="item.ItemTypeId !== 70"
                v-html="item.ItemTitleWeb"
                :id="'Imgid' + index"
                style="font-size: 18px"
                ref="getFocus"
                @input="handleInput"
                @click="onkeyup"
                class="formatImg pc_font_size18 font_size28 ipad_font_size35"
                v-mathquill="{ list: item.AnswerList, showKey_color: showKey_color, input: handleInputInterceptor }"
                v-katex
              ></span>
              <!-- 多项单选题 -->
              <span
                v-if="item.ItemTypeId === 70"
                v-html="item.ItemTitleWeb"
                :id="'Imgid' + index"
                style="font-size: 18px"
                ref="getFocus"
                @click="onSelectClickBtn"
                class="formatImg pc_font_size18 font_size28 ipad_font_size35"
                v-katex
              ></span>
            </div>

            <div class="Topic_type" v-if="num === index">
              <!-- 多项单选-->
              <ul>
                <li
                  class="pc_font_size16 font_size24 ipad_font_size30"
                  v-if="arrValue.length > 0"
                  v-for="(item, index) in arrValue"
                  :key="index"
                  v-html="item"
                  v-katex
                  style="font-size: 14px; margin: 2px 0"
                ></li>
              </ul>
              <!-- 主观题-拍照上传 -->
<!--              <SubjectiveStemPhoto-->
<!--                v-if="item.ItemTypeId == 36 && item.ReviewContentType == 1 && isMobile"-->
<!--                ref="SubjectiveStemPhoto"-->
<!--                :ItemId="item.ItemId"-->
<!--                :Answer="item.Answer"-->
<!--                :SubType="1"-->
<!--                @handlePhoto="getSubjectiveData"-->
<!--              ></SubjectiveStemPhoto>-->
              <SubjectiveStemPhoto
                v-if="item.ItemTypeId == 36 && item.ReviewContentType == 1"
                ref="SubjectiveStemPhoto"
                :ItemId="item.ItemId"
                :Answer="item.Answer"
                :SubType="1"
                @handlePhoto="getSubjectiveData"
              ></SubjectiveStemPhoto>
<!--              <p-->
<!--                v-if="item.ItemTypeId == 36 && !isMobile"-->
<!--                class="pc_font_size16 font_size20 ipad_font_size22"-->
<!--                style="margin: 30px 0; color: orange"-->
<!--              >-->
<!--                此题为拍摄上传类主观题，请使用可拍摄的设备答题！-->
<!--              </p>-->
              <SubjectiveStemVideo
                v-if="item.ItemTypeId == 36 && item.ReviewContentType == 3"
                ref="SubjectiveStemVideo"
                :ItemId="item.ItemId"
                :Answer="item.Answer"
                :SubType="1"
                @handleVideo="getSubjectiveData"
              ></SubjectiveStemVideo>
              <!-- 音频题 -->
<!--              <SubjectiveStemAudio-->
<!--                ref="SubjectiveStemAudio"-->
<!--                v-if="item.ItemTypeId == 36 && item.ReviewContentType == 2 && isMobile"-->
<!--                :ItemId="item.ItemId"-->
<!--                :Answer="item.Answer"-->
<!--                :SubType="1"-->
<!--                @handleAudio="getSubjectiveData"-->
<!--              ></SubjectiveStemAudio>-->
              <SubjectiveStemAudio
                ref="SubjectiveStemAudio"
                v-if="item.ItemTypeId == 36 && item.ReviewContentType == 2"
                :ItemId="item.ItemId"
                :Answer="item.Answer"
                :isMobile="isMobile"
                :SubType="1"
                @handleAudio="getSubjectiveData"
              ></SubjectiveStemAudio>
              <!-- 连线题 -->
              <matching
                ref="matching"
                v-if="item.ItemTypeId == 50"
                :Answer="item.Answer"
                :trueAnswer="item.ShowAnswer"
                :linkExamData="item.ItemChange.LinkExamData"
                :SubType="0"
                @handleLIne="getSubjectiveData"
              ></matching>
              <!-- 表格题 -->
              <RenderTable
                v-if="item.ItemTypeId == 51"
                ref="RenderTable"
                :TableItemInfo="item.ItemChange.TableItemInfo"
                @onfocus="onfocus"
                @updatefinishStatus="item.HasAnswer = $event"
              ></RenderTable>
              <!-- 单选题 -->
              <div class="radio_text">
                <a-radio-group
                  v-model="item.Answer"
                  v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11"
                  @change="RadioCheck(item)"
                >
                  <a-radio :value="k.Opt" v-for="(k, j) in item.Options" :key="j">
                    <span class="pc_font_size16 font_size24 ipad_font_size30">{{ k.Opt }}.&nbsp;&nbsp;</span>
                    <span
                      class="p-inline pc_font_size16 font_size24 ipad_font_size30"
                      v-if="k.TypeValue === '2' || k.TypeValue === '11'"
                      v-html="k.Content"
                      v-katex
                      style="display: inline-block"
                    ></span>
                  </a-radio>
                </a-radio-group>
              </div>

              <!-- 多选题 -->
              <a-checkbox-group
                v-model="SelectionList"
                class="Multiple"
                @change="Checkbox"
                v-if="item.ItemTypeId === 10"
              >
                <a-row>
                  <a-col :span="10">
                    <a-checkbox v-for="(i, ind) in item.Options" :key="ind" :value="i.Opt">
                      <span class="pc_font_size16 font_size24 ipad_font_size30">{{ i.Opt }}.&nbsp;&nbsp;</span>
                      <p
                        class="pc_font_size16 font_size24 ipad_font_size30"
                        v-html="i.Content"
                        style="margin-left: 5px; display: inline-block"
                        v-katex
                      ></p>
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>

              <!-- 新题型 1--可变行下拉填空题  -->
              <div v-if="item.ItemTypeId == 46">
                <div class="select_box" v-for="(sel, index) in SplitSelectArr" :key="index">
                  <span v-for="(ItemSel, ind) in sel" :key="ind">
                    <span v-html="ItemSel" v-katex @change="selectSku"></span>
                  </span>
                </div>
                <div class="Table_Btn">
                  <span
                    style="font-size: 18px"
                    v-if="SplitSelectArr.length < item.ItemChange.MaxRow"
                    @click="Select_AddRow"
                    ><i class="el-icon-circle-plus-outline"></i>增加一行</span
                  >
                  <span
                    v-if="SplitSelectArr.length > 1"
                    style="font-size: 18px; margin-left: 20px"
                    @click="Select_ClearRow"
                    ><i class="el-icon-remove-outline"></i>减少一行</span
                  >
                </div>
              </div>

              <!-- 新题型 2--可变行表格  -->
              <div v-if="item.ItemTypeId == 40">
                <!-- <table class="tableTitle">
                  <tr v-for="(item, index) in tableList.Titles" :key="index">
                    <td>{{ item }}</td>
                  </tr>
                  <tbody>
                    <tr v-for="(td, ind) in tableList.Rows" :key="ind">
                      <td v-for="(td, indx) in td" :key="indx">
                        <span v-html="td" @input="handleInput" ref="getFocus" @click="onkeyup"></span>
                      </td>
                    </tr>
                  </tbody>
                </table> -->
                <table class="tableTitle" style="width: 70%">
                  <tr>
                    <td v-for="(item, index) in tableList.Titles" :key="index">{{ item }}</td>
                  </tr>
                  <tr v-for="(td, ind) in tableList.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span
                        v-html="td"
                        ref="getFocus"
                        id="hello_input"
                        @input="handleInput"
                        @click="onkeyup"
                        v-katex
                      ></span>
                    </td>
                  </tr>
                </table>
                <div class="Table_Btn">
                  <span
                    v-if="tableList.Rows.length < item.ItemChange.MaxRow"
                    style="font-size: 18px"
                    @click="Table_AddRow(item, item.ItemChange.IsSNum, tableList.Rows)"
                    ><i class="el-icon-circle-plus-outline"></i>增加一行</span
                  >
                  <span
                    v-if="tableList.Rows.length > 1"
                    style="font-size: 18px; margin-left: 20px"
                    @click="Table_ClearRow"
                    ><i class="el-icon-remove-outline"></i>减少一行</span
                  >
                </div>
              </div>
              <!-- 新题型 3--可变行填空  -->
              <div v-if="item.ItemTypeId == 41">
                <div class="Blank_box" v-for="(blank, indx) in selfList" :key="indx">
                  <span class="blank_Answer">答案{{ indx + 1 }}</span>
                  <span v-for="(i, ind) in blank" :key="ind">
                    <p v-html="i" @input="(event) => BlankInput(event, indx, ind)" @click="onkeyup" v-katex></p>
                  </span>
                </div>
                <div class="Table_Btn">
                  <span
                    v-if="selfList.length < item.ItemChange.MaxRow"
                    style="font-size: 18px"
                    @click="Blank__AddRow(selfList)"
                    ><i class="el-icon-circle-plus-outline"></i>增加一行</span
                  >
                  <span
                    v-if="selfList.length > item.ItemChange.Rows.length"
                    style="font-size: 18px; margin-left: 20px"
                    @click="Blank_ClearRow"
                    ><i class="el-icon-remove-outline"></i>减少一行</span
                  >
                </div>
              </div>
              <!-- 新题型 4--思考问答  -->
              <div v-if="item.ItemTypeId == 47">
                <div class="show_btn">
                  <el-button @click="ShowRadio">填写后点击思考</el-button>
                  <div class="radio_box" v-if="show_radiobox">
                    <span @input="handleInput" @click="onkeyup">{{ QAList.Title }}</span>
                    <br />
                    <a-radio-group v-model="splcar_radio" @change="onRadioChange">
                      <a-radio :value="i.Title" v-for="(i, indx) in QAList.ItemConfigOptions" :key="indx">
                        <span style="font-size: 19px">{{ i.Title }}:&nbsp;&nbsp;</span>
                        <span v-html="i.Name" style="font-size: 18px; display: inline-block" v-katex></span>
                      </a-radio>
                    </a-radio-group>
                    <br />
                    <span
                      v-show="options_Context"
                      id="radio_box_input"
                      v-html="QAList.ItemConfigOptions[0].Context"
                      @input="handleInput"
                      @click="onkeyup"
                      v-katex
                    ></span>
                  </div>
                </div>
              </div>
              <div v-if="item.ItemTypeId == 42">
                <table class="pure-table pure-table-bordered">
                  <thead>
                    <tr>
                      <th v-for="(item, index) in item.ItemChange.Titles" :key="index">{{ item }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                      <td v-for="(td, indx) in td" :key="indx">
                        <span
                          v-html="td"
                          ref="getFocus"
                          id="hello_input"
                          @input="handleInput"
                          @click="onkeyup"
                          v-katex
                        ></span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!-- <div class="btn_text">
                <el-button class="btn_next" @click="next(index, item)" :disabled="nextDisabled">下一题</el-button>
              </div> -->
              <!--     核心素养的拍照         -->
              <div
                class="hexinsuyangPaizhao"
                v-if="item.IsHaveSpecial && isMobile !== null && item.IsUpload != 0 && item.ItemTypeId != 36"
              >
                <p>
                  拍照上传解题思路（<span :style="{ color: item.IsUpload == 1 ? '#F56C6C' : '#909399' }">{{
                    item.IsUpload == 1 ? '必传' : '非必传'
                  }}</span
                  >）
                </p>
                <!-- 主观题-拍照上传 -->
                <SubjectiveStemPhoto
                  v-if="item.IsUpload == 1 || item.IsUpload == 2"
                  ref="SubjectiveStemPhoto"
                  :ItemId="item.ItemId"
                  :Answer="item.Answer"
                  :SubType="1"
                  @handlePhoto="getSubjectiveData"
                ></SubjectiveStemPhoto>
              </div>
              <p
                v-if="item.IsHaveSpecial && item.IsUpload != 0 && isMobile === null"
                class="pc_font_size16 font_size20 ipad_font_size22"
                style="margin: 30px 0; color: orange"
              >
                此题为拍摄上传类，请使用可拍摄的设备答题！
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 订正结果 -->
      <a-modal v-model="nextKnow" title="温馨提示" width="630px" @ok="handleKnow()" centered>
        <template slot="footer">
          <p style="text-align: center; color: #68bb97ff" class="cur" @click="handleKnow">继续订正</p>
        </template>
        <div style="text-align: center">
          <p>{{ Submission }}</p>
        </div>
      </a-modal>
    </div>
    <el-dialog title="查看首次作答" :visible.sync="photoShow" width="50%" :before-close="closePhotoShow">
      <div style="display: flex; justify-content: center; height: 60vh">
        <SubjectiveStemPhoto
          :Answer="firstPhotos"
          useType="analysis"
          v-if="ReviewContentType == 1"
        ></SubjectiveStemPhoto>
        <SubjectiveStemVideo
          :Answer="firstPhotos"
          useType="analysis"
          v-if="ReviewContentType == 3"
        ></SubjectiveStemVideo>
        <SubjectiveStemAudio
          :Answer="firstPhotos"
          useType="analysis"
          v-if="ReviewContentType == 2"
        ></SubjectiveStemAudio>
      </div>
    </el-dialog>
    <keyboard ref="keyword" @updatekey="GetKeyVal"></keyboard>
  </div>
</template>
<script>
import keyboard from '../Exam_Student/keyboard'
import eventBus from '@/components/eventBus/eventBus'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { arrlistTitle, onSelectClick } from '@/utils/bankedCloze/bankedCloze'
import SubjectiveStemPhoto from '../Exam_Student/components/subjectiveExam/answerPhoto.vue'
import SubjectiveStemVideo from '../Exam_Student/components/subjectiveExam/answerVideo.vue'
import SubjectiveStemAudio from '../Exam_Student/components/subjectiveExam/answerAudio.vue'
import ExamFormatCommon from '../../../common/ExamFormatCommon'
import paperMixins from '@/mixins/paperMixins'
import matching from '@/components/matching/index'
import RenderTable from '../Exam_Student/components/tableStem/renderTable.vue'
export default {
  mixins: [paperMixins],
  components: {
    keyboard,
    SubjectiveStemPhoto,
    SubjectiveStemVideo,
    SubjectiveStemAudio,
    matching,
    RenderTable,
  },
  created() {
    const domain = document.domain
    this.keyboardAuto = !(domain == 'st.eduwon.cn')
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.dataTypeId = this.$route.query.dataId
    this.GetStudentWrongPaperItems()
    this.GetTheData()
    //  多学科获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
    // this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)
    // this.SubjectIdName = this.$route.query.SubjectIdName
    this._isMobile()
  },
  mounted() {
    this.timeRecord(true)
    eventBus.$on('showKey_color', (showKey_color) => {
      this.showKey_color = showKey_color
      if (!showKey_color) this.closeToChangeInput()
    })
  },
  data() {
    return {
      row: null,
      column: null,
      userId: '',
      paperId: '',
      dataTypeId: '',
      PaperList: [], // 试卷信息
      PaperName: '', // 试卷名称
      CallinTime: '', // 计时时间
      timer: null,
      PaperNum: '', // 试题总数
      CurrentNum: 1, // 当前题目数
      num: 0, // 当前题目
      nextDisabled: false, // 下禁用按钮
      AnswerList: [], // 答案
      SelectionList: [], // 多选题答案,
      tableList: [], // 表格--可变行 Array
      TableRegx: [], // 表格--可变行 替换 Array
      AddRow: [], // 表格 -- 新增一行 Array
      BlanksList: [], // 特殊题型三 ---填空
      QAList: [], // 特殊题型四 ---思考问答
      splcar_radio: '', // 特殊题型四 ---radio
      show_radiobox: false, // 特殊题型四 --是否展示radio
      options_Context: false, // 特殊题型四 --是否展示第三项
      RowsList: [], // typid == 42 列表
      selfList: [],
      asList: [],
      SelectList: [],
      SplitSelectArr: [],
      TypeNumber_41: [],
      SelectNum: 0,
      TableInputs: 0,
      Getvideo: '',
      nextKnow: false,
      Submission: '',
      currentInput: '',
      showKey_color: '',
      keyboardAuto: true,
      reAnswer: false,
      reAnswerTip: false,
      isPcKey: 1,
      styleStr: `
        margin:5px 10px;
        border-bottom: 1px solid gray;
        border-radius: 4px;
        background: rgba(137, 225, 186, 0.4);
        line-height: 27px;
        font-size: 18px;
        color: #000;
        overflow: hidden;
        position: relative;
        text-align: left;
        top:8px;
        padding: 5px;
      `,
      swiperOption: {
        slidesPerView: 10, // 一行显示4个
        spaceBetween: 10, // 间隔30
        freeMode: true,
        speed: 1000, // 滑动速度
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true,
          disabledClass: 'my-button-disabled', // 前进后退按钮不可用时的类名。
          hiddenClass: 'my-button-hidden', // 按钮隐藏时的Class
        },
      },
      //  多学科部分
      bgColog: {},
      Multidisciplinary: {},
      photoShow: false,
      firstPhotos: '',
      isMobile: null,
      ReviewContentType: -1,
      // 选题填空答案
      AnswerArr: [],
      // 多项单选
      arrValue: [],
      zuheti: [],
      zuhetiTitle: '',
    }
  },
  computed: {
    itemType() {
      return this.PaperList[this.num].ItemTypeId
    },
  },
  watch: {
    $route() {
      window.clearInterval(this.timer)
      const ChangId = window.location.href.split('?')[1]
      this.dataTypeId = this.$route.query.dataId
      this.paperId = this.$route.query.paperId
      this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
      if (this.$route.fullPath === '/Student/Exam_Paper/cleaningWrongQuestion?' + ChangId) {
        this.$router.go(0)
        this.GetStudentWrongPaperItems()
        this.timeRecord(false)
        this.GetTheData()
        this._isMobile()
      }
    },
    num: {
      handler(now) {
        if (now == this.PaperList.length - 1) {
          this.nextDisabled = true
        } else {
          this.nextDisabled = false
        }
      },
      deep: true,
    },
  },
  methods: {
    onfocus({ row, column }) {
      this.row = row
      this.column = column
    },
    // 多项单选的点击
    onSelectClickBtn(e) {
      const that = this
      onSelectClick(e, that)
    },
    // 首次作答弹窗
    watchFirstPhotos(item) {
      this.$uwonhttp
        .get(
          '/ExamItem/ExamItem/GetSujectiveAnswer?paperId=' +
            this.paperId +
            '&itemId=' +
            item.ItemId +
            '&studentId=' +
            this.userId +
            '&subType=0'
        )
        .then((res) => {
          if (res.data.Success) {
            const data = res.data.Data
            this.ReviewContentType = data.ReviewContentType
            this.firstPhotos = data.AnswerUrl
            this.photoShow = true
          }
        })
    },
    closePhotoShow() {
      this.photoShow = false
      this.firstPhotos = ''
    },
    // 从主观题组件获取数据
    getSubjectiveData(data) {
      if (this.PaperList[this.CurrentNum - 1].ItemTypeId == 50) {
        let d = JSON.parse(data)
        this.PaperList[this.CurrentNum - 1].HasAnswer = d.length ? true : false
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = !!data
      }
      this.PaperList[this.CurrentNum - 1].Answer = data
    },
    // 获取试卷的名称
    async GetTheData() {
      const res = await this.$http.post('/Paper/Exam_Paper/GetTheData', {
        id: this.paperId,
      })
      this.PaperName = res.Data.Title
    },

    GoBack() {
      this.$confirm('是否退出答题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$router.go(-1)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退出',
          })
        })
    },

    // 答案隐藏
    hideAnswer() {
      const inputs_current = $(`#Imgid${this.num}`).find('input')
      const stem = this.PaperList[this.num]
      stem.AnswerList = []
      stem.Answer = ''
      for (let i = 0; i < inputs_current.length; i++) {
        inputs_current[i].value = ''
        inputs_current[i].disabled = ''
        stem.StudentCorrectItems[i].isDisabled = false
        stem.StudentCorrectItems[i].IsKeyTrue = false
        stem.StudentCorrectItems[i].Answer = ''
      }
      inputs_current[0].focus()
      this.currentInput = inputs_current[0]
      this.$refs.keyword.chengeVal('')
    },
    // 答案回显
    showAnswer() {
      const stem = this.PaperList[this.num]
      if (stem.ItemTypeId != 5) {
        this.reAnswer = false
        this.reAnswerTip = false
        return
      }
      if (stem.IsMFill == 0 || stem.ItemTypeId != 5) {
        this.reAnswer = false
        this.reAnswerTip = false
        return
      }
      // 判断是不是要显示重新作答按钮
      const stemAnswerList = stem.StudentCorrectItems
      const result = stemAnswerList.findIndex((item) => item.IsKeyTrue)
      if (result != -1) {
        this.reAnswer = true
        this.reAnswerTip = stem.IsMAnswer > 0
      } else {
        this.reAnswer = false
        this.reAnswerTip = false
      }
      const inputs_current = $(`#Imgid${this.num}`).find('input')
      stemAnswerList.forEach((item, index) => {
        inputs_current[index].value = item.Answer
        stem.AnswerList[index] = item.Answer
        if (item.isDisabled) {
          inputs_current[index].disabled = 'disabled'
        }
      })
    },
    //正确答案回显
    trueAnswerEcho(item) {
      let hasTrue = item.StudentCorrectItems.findIndex((s) => s.IsKeyTrue) != -1
      let AnswerList = []
      if (hasTrue) {
        item.StudentCorrectItems.forEach((a, i) => {
          if (a.IsKeyTrue) {
            AnswerList[i] = a.Answer
          }
        })
        return AnswerList
      }
    },

    // 获取试卷信息
    async GetStudentWrongPaperItems() {
      const res = await this.$uwonhttp.post('/ExamItem/ExamItem/GetStudentWrongPaperItems', {
        UserId: this.userId,
        paperId: this.paperId,
        DataType: this.dataTypeId,
      })
      if (res.data.Success) {
        const arrItem = res.data.Data.Items
        // 高拍题标识
        // this.IsPhotograph = res.data.Data.IsPhotograph
        if (arrItem[0].ItemTypeId === 70) {
          if (arrItem[0].ItemChange.MultitermSingle.Options[0].IsContentShow === 2) {
            this.arrValue = arrItem[0].ItemChange.MultitermSingle.Options[0].Content.split('|')
          } else {
            this.arrValue = []
          }
        }
        arrItem.forEach((item, index) => {
          // 选词填空题型
          if (
            (item.ItemTypeId === 52 && item.ItemChange.SelectFillBlank !== null) ||
            (item.ItemTypeId == 70 && item.ItemChange.MultitermSingle !== null)
          ) {
            item.ItemTitleWeb = arrlistTitle('初始化', item, item.ItemTypeId)
          }
          // 填空题处理
          if (item.ItemTypeId == 5) {
            item.ItemTitleWeb = ExamFormatCommon.format_fraction(item.ItemTitleWeb)
            item.ItemTitleWeb = ExamFormatCommon.format_input(item.ItemTitleWeb, item.ItemTypeId, item,false)
            item.AnswerList = this.trueAnswerEcho(item) || []
          }
          // 表格变行处理
          if (item.ItemTypeId == 40) {
            this.tableList = item.ItemChange
            const regx = /\(\#@\)/g
            const input = ''
            this.tableList.Rows.forEach((td, idx, ary) => {
              td.forEach((i, index, array) => {
                const TableRegx = i.match(regx)
                if (TableRegx !== null) {
                  TableRegx.forEach((item1) => {
                    const value = item1.replace(regx, '')
                    const title = i.replace(
                      item1,
                      `<input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                      id="${idx + '.' + index}"  data-type="${item.ItemTypeId}" data-index="${index}"/>`
                    )
                    array[index] = title
                  })
                }
              })
            })
          }
          // 填空变行处理
          if (item.ItemTypeId == 41) {
            this.BlanksList = item.ItemChange.Rows
            const arr = this.BlanksList.map((blank, index) => {
              return blank.map((val, ind) => {
                return val.split('\r\n').map((el, inx) => {
                  const str = el.split('(#@)')
                  return `${
                    str[0]
                  }<input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                      data-type="${item.ItemTypeId}"  id="${index + '.' + inx}" data-index="${index}"/>${str[1]}`
                })
              })
            })
            this.selfList = arr[0]
            this.asList = [[]]
          }
          // 可变行下拉填空题
          if (item.ItemTypeId == 46) {
            this.SelectList = item.ItemChange
            const SelectArr = this.forSelect(this.SelectList.Rows)
            this.SplitSelectArr = SelectArr
          }
          // 思考问答题
          if (item.ItemTypeId == 47) {
            this.QAList = item.ItemChange.ItemConfigOptionAnswer
            const regx = /\(\#\&\d+\@\)/g
            const reg = /\(\#@\)/g
            const input = ''
            const regx1 = /\(\#\&/g
            const regx2 = /\@\)/g
            const regxlist = item.ItemTitleWeb.match(regx)
            regxlist.forEach((item1) => {
              const value = item1.replace(regx1, '').replace(regx2, '')
              const title = item.ItemTitleWeb.replace(
                item1,
                `<input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                data-type="${item.ItemTypeId}"  id="${value}" data-index="${value}"/>`
              )
              item.ItemTitleWeb = title
            })
            this.QAList.ItemConfigOptions.forEach((i) => {
              const regxlist = i.Context.match(reg)
              if (regxlist !== null) {
                regxlist.forEach((item1) => {
                  const value = item1.replace(reg, '')
                  const title = i.Context.replace(
                    item1,
                    `<input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                      data-type="${item.ItemTypeId}"  id="${1}" data-index="${value}"/>`
                  )
                  i.Context = title
                })
              }
            })
          }

          if (item.ItemTypeId == 42) {
            this.RowsList = item.ItemChange
            const RowArr = this.FormatRows(item, this.RowsList.Rows)
          }

          // 添加 答案，单个做题时间， 是否已回答变量, 是否正在回答
          item.ATime = 0
          item.Answer = ''
          item.WidhList = []
          if (item.ItemTypeId != 5) {
            item.AnswerList = []
          }
          item.HasAnswer = false
          item.CurrentAnswer = index === 0
        })
        this.zuheti = ExamFormatCommon.filterZuheti(res.data.Data.Items)
        res.data.Data.Items[0].CurrentAnswer = true
        this.PaperList = res.data.Data.Items
        // 判断普通填空题是否有input
        const FristTitle = this.PaperList[0].ItemTitleWeb
        const FristType_Id = this.PaperList[0].ItemTypeId
        if (FristTitle.toString().indexOf('input') != -1) {
          const that = this
          setTimeout(() => {
            that.$nextTick((x) => {
              that.$refs.getFocus[0].getElementsByTagName('input')[0].focus()
              const inputs = $(`#Imgid${this.num}`).find('input')
              // that.currentInput = $('.virtual_input')[0]
              that.currentInput = inputs[0]
              that.currentInputlength = inputs.length
              if (this.keyboardAuto) {
                // that.$refs.keyword.KeyWrod_True()
                this.Review_Key()
              }
            })
            // this.showAnswer()
          }, 100)
        }
        if (FristType_Id == '40' || (FristType_Id == '42' && this.keyboardAuto)) {
          setTimeout(() => {
            // this.$refs.keyword.KeyWrod_True()
            this.Review_Key()
          }, 100)
        } else {
          this.$refs.keyword.KeyWrod_False()
        }
        this.PaperNum = res.data.Data.Items.length
        if (this.PaperNum == 1) {
          this.nextDisabled = true
        } else {
          this.nextDisabled = false
        }
        let ParentId_c = this.PaperList[this.CurrentNum - 1].ParentId
        this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ''
        setTimeout(() => {
          this.GetImgAll()
        }, 1000)
      } else {
        this.$message.error(res.data.Msg)
      }
    },
    GetImgAll() {
      const that = this
      that.$nextTick(() => {
        const ImgArr = document.getElementById(`Imgid${this.num}`).querySelectorAll('img')
        if (ImgArr !== null) {
          for (let i = 0; i < ImgArr.length; i++) {
            ImgArr[i].style.maxWidth = '100%'
          }
        }
      })
    },

    // 计时器
    timeRecord(bolean) {
      const _this = this
      let hour, minute, second
      hour = minute = second = 0
      if (bolean === true) {
        _this.timer = setInterval(() => {
          if (second >= 0) {
            second = second + 1
          }
          if (second >= 60) {
            second = 0
            minute = minute + 1
          }
          if (minute >= 60) {
            minute = 0
            hour = hour + 1
          }
          _this.CallinTime = hour + '时' + minute + '分' + second + '秒'
          _this.$refs['CallinTime'].innerText = '总计' + _this.CallinTime
        }, 1000)
      } else {
        window.clearInterval(_this.timer)
      }
    },

    // 单选题
    RadioCheck(e) {
      if (e.Answer) {
        e.HasAnswer = true
      }
    },

    FormatRows(item, arr) {
      const regx = /\(\#@\)/g
      arr.forEach((td, idx, ary) => {
        td.forEach((i, index, array) => {
          const TableRegx = i.match(regx)
          if (TableRegx !== null) {
            TableRegx.forEach((item1) => {
              const value = item1.replace(regx, '')
              const title = i.replace(
                item1,
                `<input class="input" autocomplete="off"  type="text" style="width: 100px;border: 0px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                      id="${idx + '.' + index}"  data-type="${item.ItemTypeId}" data-index="${index}"/>`
              )
              array[index] = title
            })
          }
        })
      })
    },

    // 下拉框，数据遍历
    forSelect(arr, flag) {
      const SelectArr = arr.map((sel, sel_index) => {
        return sel.map((val) => {
          const str1 = val.split('#&')
          let addSelctStr = ''
          str1.forEach((ele, str1_index) => {
            if (ele.length > 0) {
              addSelctStr +=
                ele +
                `<select name="age" id="${
                  flag ? this.SplitSelectArr.length : sel_index
                }.${str1_index}" class="select" style="width: 130px;margin-left: 5px;">`
              this.SelectList.SelectOptions[sel_index][str1_index].forEach((option_ele) => {
                addSelctStr += `<option value='${option_ele}'> ${option_ele}</option>`
              })
              addSelctStr += ` </select>`
            }
          })
          return addSelctStr
        })
      })
      return SelectArr
    },

    // 特殊题型 1---下拉
    selectSku(event) {
      var index = event.target.selectedIndex
      var value = event.target.options[index].value // 选中值
      const item = {
        id: event.target.id,
        value: value,
      }
      const idx = this.PaperList[this.CurrentNum - 1].AnswerList.findIndex((obj) => {
        return obj.id === event.target.id
      })
      if (idx !== -1) {
        this.PaperList[this.CurrentNum - 1].AnswerList[idx].value = value
      } else {
        this.PaperList[this.CurrentNum - 1].AnswerList.push(item)
      }
      this.getSelectAnswer()
    },

    // 特殊题型 1---下拉, 获取Answer
    getSelectAnswer() {
      const selects = document.getElementsByTagName('select')
      const list = this.PaperList[this.CurrentNum - 1].AnswerList
      this.SelectNum = selects.length

      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < list.length; i++) {
        if (i + 1 === list.length) {
          this.PaperList[this.CurrentNum - 1].Answer += list[i].value
        } else {
          if (list[i].id.split('.')[0] === list[i + 1].id.split('.')[0]) {
            this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + '|'
          } else {
            this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + ','
          }
        }
      }
      if (list.length == selects.length) {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },

    // 特殊题型41---可变行填空 获取Answer
    BlankInput(event, indx, ind) {
      let list
      this.asList[indx][ind] = event.target.value
      for (let i = 0; i < 6; i++) {
        if (i !== ind) {
          if (!this.asList[indx][i]) {
            this.asList[indx][i] = ''
          }
        }
      }
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(this.asList))
      list = JSON.parse(JSON.stringify(this.PaperList[this.CurrentNum - 1].AnswerList))
      this.PaperList[this.CurrentNum - 1].Answer = list.map((item) => item.join('|')).join(',')
      this.PaperList[this.CurrentNum - 1].HasAnswer =
        list.length > 0 &&
        list.every((val) => {
          return (
            val.length > 0 &&
            val.some((v) => {
              return v !== ''
            })
          )
        })
    },

    // 填空题---41 添加行
    Blank__AddRow(rows) {
      const alen = this.selfList.length
      var newArr = JSON.parse(JSON.stringify(rows[0]))
      newArr = newArr.map((n) => {
        return n.replace(/id=\"0./g, `id=\"${alen}.`)
      })
      this.selfList.push(newArr)
      this.asList.push([])
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(this.asList))
      this.PaperList[this.CurrentNum - 1].HasAnswer =
        this.PaperList[this.CurrentNum - 1].AnswerList.length > 0 &&
        this.PaperList[this.CurrentNum - 1].AnswerList.every((val) => {
          return (
            val.length > 0 &&
            val.some((v) => {
              return v !== ''
            })
          )
        })
    },

    // 填空题---41 删除行
    Blank_ClearRow() {
      this.selfList.splice(-1)
      this.asList.splice(-1)
      const NewAnswerList = JSON.parse(JSON.stringify(this.PaperList[this.CurrentNum - 1].AnswerList))
      const MapNewList = JSON.parse(JSON.stringify(NewAnswerList.splice(-1)))
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(NewAnswerList))
      this.PaperList[this.CurrentNum - 1].Answer = NewAnswerList.map((item) => item.join('|')).join(',')
      this.PaperList[this.CurrentNum - 1].HasAnswer =
        NewAnswerList.length > 0 &&
        NewAnswerList.every((val) => {
          return (
            val.length > 0 &&
            val.some((v) => {
              return v !== ''
            })
          )
        })
    },

    // 多选题
    Checkbox(value) {
      this.SelectionList = value.sort()
      this.PaperList[this.CurrentNum - 1].AnswerList = this.SelectionList
      const list = this.SelectionList
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < list.length; i++) {
        if (i + 1 === list.length) {
          this.PaperList[this.CurrentNum - 1].Answer += list[i]
        } else {
          this.PaperList[this.CurrentNum - 1].Answer += (list[i] ? list[i] : '') + ','
        }
      }
      if (list.length !== 0) {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },

    // 下拉选择 添加
    Select_AddRow() {
      const selects = document.getElementsByTagName('select')
      const addRow = this.forSelect(this.SelectList.Rows, true)
      this.SelectNum = selects.length + 2
      this.SplitSelectArr = this.SplitSelectArr.concat(addRow)
    },

    // 下拉选择 删除
    Select_ClearRow() {
      const _this = this
      function findDelIdx() {
        const idx = _this.PaperList[_this.CurrentNum - 1].AnswerList.findIndex((obj) => {
          return obj.id.split('.')[0] === (_this.SplitSelectArr.length - 1).toString()
        })
        if (idx !== -1) {
          _this.PaperList[_this.CurrentNum - 1].AnswerList.splice(idx, 1)
          findDelIdx()
        }
      }
      findDelIdx()
      this.getSelectAnswer()
      this.SplitSelectArr.splice(-1)
      const selects = document.getElementsByTagName('select')
      this.SelectNum = selects.length - 2
    },

    // 表格题  添加行
    Table_AddRow(item, IsSNum, rows) {
      // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
      const inputs = $(`#Imgid${this.num}`).find('input')
      this.TableInputs = inputs.length + 2
      var newArr = JSON.parse(JSON.stringify(rows[0]))
      if (IsSNum == 0) {
        for (var i = 0; i < newArr.length; i++) {
          if (newArr[i].toString().indexOf('input') != -1) {
            newArr[
              i
            ] = `<td><input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                         id="${rows.length + 1 + '.' + i}" data-type="40" data-index="${i}"/></td>`
          }
        }
        this.tableList.Rows.push(newArr)
      }
      if (IsSNum == 1) {
        newArr[0] = rows.length + 1
        for (var i = 0; i < newArr.length; i++) {
          if (newArr[i].toString().indexOf('input') != -1) {
            newArr[
              i
            ] = `<td><input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                         id="${rows.length + 1 + '.' + i}" data-type="40" data-index="${i}"/></td>`
          }
        }
        this.tableList.Rows.push(newArr)
      }
    },

    // 表格题 删除行
    Table_ClearRow() {
      // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
      const inputs = $(`#Imgid${this.num}`).find('input')
      const Table_List = JSON.parse(JSON.stringify(this.PaperList[this.CurrentNum - 1].AnswerList))
      this.TableInputs = inputs.length - 2
      this.tableList.Rows.pop()
      if (Table_List.length !== 2) {
        if (Table_List.length % 2 === 0) {
          Table_List.pop()
          Table_List.pop()
        } else {
          Table_List.pop()
        }
      }
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(Table_List))
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < Table_List.length; i++) {
        if (i + 1 === Table_List.length) {
          this.PaperList[this.CurrentNum - 1].Answer += Table_List[i].value
        } else {
          if (Table_List[i].id.split('.')[0] === Table_List[i + 1].id.split('.')[0]) {
            this.PaperList[this.CurrentNum - 1].Answer += (Table_List[i].value ? Table_List[i].value : '') + '|'
          } else {
            this.PaperList[this.CurrentNum - 1].Answer += (Table_List[i].value ? Table_List[i].value : '') + ','
          }
        }
      }
    },

    // 特殊题型四 是否是展示第二项
    ShowRadio() {
      this.show_radiobox = true
      // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
      const inputs = $(`#Imgid${this.num}`).find('input')
      const Targetvalue = inputs[0].value
      if (this.Getvideo !== '' && Targetvalue !== '') {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },

    // 特殊题型四  radio选中
    onRadioChange(event) {
      let ChangeVal = ''
      if (event.target.value == 'A') {
        this.options_Context = true
        ChangeVal = '有'
      } else {
        this.options_Context = false
        ChangeVal = '没有'
      }
      localStorage.setItem('Type_41', this.PaperList[this.CurrentNum - 1].Answer.split('|')[0] + '|' + ChangeVal)
      this.Getvideo = ChangeVal
      if (this.Getvideo !== '' && this.Getvideo !== '有') {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
        // const inputs = $(`#Imgid${this.num}`).find('input')
        const inputs = $(`#radio_box_input`).find('input')
        inputs[0].value = ''
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },

    // 下一题
    next(index, item) {
      // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
      const inputs = $(`#Imgid${this.num}`).find('input')
      this.$nextTick(() => {
        this.PaperList[this.CurrentNum - 1].ATime = this.currentTime
        item.CurrentAnswer = false
        this.PaperList[index + 1].CurrentAnswer = true
        this.$set(this.PaperList, index + 1, this.PaperList[index + 1])
        this.targetIndex(index + 1, this.PaperList[index + 1])
      })
    },
    // 点击跳转相对应题目
    targetIndex(index, item) {
      setTimeout(() => {
        let ParentId_c = this.PaperList[this.CurrentNum - 1].ParentId
        this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ''
        if (
          this.PaperList[this.CurrentNum - 1].ItemTypeId === 5 ||
          this.PaperList[this.CurrentNum - 1].ItemTypeId === 47
        ) {
          this.$nextTick((x) => {
            this.$refs.getFocus[0].getElementsByTagName('input')[0].focus()
            // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
            const inputs = $(`#Imgid${this.num}`).find('input')
            this.currentInput = $(`#Imgid${this.num}`).find('.virtual_input')[0] || inputs[0]
            let v = this.currentInput.MathQuill ? this.currentInput.MathQuill.text() : this.currentInput.value
            this.$refs.keyword.chengeVal(v)
            this.currentInputlength = inputs.length
            // if (this.keyboardAuto) {  键盘是否自动弹出
            // this.$refs.keyword.KeyWrod_True()
            // this.Review_Key()
            // }
            // this.showAnswer()
          })
        } else {
          this.$refs.keyword.KeyWrod_False()
        }
        if (
          this.PaperList[this.CurrentNum - 1].ItemTypeId === 40 ||
          (this.PaperList[this.CurrentNum - 1].ItemTypeId === 42 && this.keyboardAuto)
        ) {
          // this.$refs.keyword.KeyWrod_True()
          this.Review_Key()
        } else {
          this.$refs.keyword.KeyWrod_False()
        }
        // this.showAnswer()
        this.showBankedCloze()
      }, 100)
      // 选词填空题型清空实例暂存数据
      if (item.ItemTypeId == 52 || item.ItemTypeId == 70) {
        const MultitermSingle = item.ItemChange.MultitermSingle
        if (MultitermSingle !== null && MultitermSingle.Options[0].IsContentShow === 2) {
          this.arrValue = MultitermSingle.Options[0].Content.split('|')
        } else {
          this.arrValue = []
        }
        if (this.PaperList[index].AnswerList.length > 0) {
          this.AnswerArr = this.PaperList[index].AnswerList
        } else {
          this.AnswerArr = []
        }
      }
      if (this.PaperList[this.CurrentNum - 1].ItemTypeId == 41) {
        const Type41_Arr = this.PaperList[this.CurrentNum - 1].AnswerList
        this.PaperList[this.CurrentNum - 1].HasAnswer =
          Type41_Arr.length > 0 &&
          Type41_Arr.every((val) => {
            return (
              val.length > 0 &&
              val.some((v) => {
                return v !== ''
              })
            )
          })
      }

      if (this.PaperList[this.CurrentNum - 1].ItemTypeId == 46) {
        const SelectLength = this.SelectNum
        const CurrentLength = this.PaperList[this.CurrentNum - 1].AnswerList.length
        if (CurrentLength == 0 && SelectLength == 0) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        } else if (CurrentLength !== SelectLength) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        } else {
          this.PaperList[this.CurrentNum - 1].HasAnswer = true
        }
      }

      if (
        this.PaperList[this.CurrentNum - 1].ItemTypeId == 40 ||
        this.PaperList[this.CurrentNum - 1].ItemTypeId == 42
      ) {
        const TableLength = this.TableInputs
        const CurrentTable = this.PaperList[this.CurrentNum - 1].AnswerList.length
        const TableAnswerList = this.PaperList[this.CurrentNum - 1].AnswerList
        const Table_arr = []
        TableAnswerList.forEach((el) => {
          Table_arr.push(el.value)
        })
        if (
          Table_arr.every((v) => {
            return v !== ''
          }) &&
          CurrentTable !== 0 &&
          TableLength !== 0 &&
          CurrentTable == TableLength
        ) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = true
        } else {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        }
        this.PaperList[this.CurrentNum - 1].Answer = ''
        for (let i = 0; i < TableAnswerList.length; i++) {
          if (i + 1 === TableAnswerList.length) {
            this.PaperList[this.CurrentNum - 1].Answer += TableAnswerList[i].value
          } else {
            if (TableAnswerList[i].id.split('.')[0] === TableAnswerList[i + 1].id.split('.')[0]) {
              this.PaperList[this.CurrentNum - 1].Answer +=
                (TableAnswerList[i].value ? TableAnswerList[i].value : '') + '|'
            } else {
              this.PaperList[this.CurrentNum - 1].Answer +=
                (TableAnswerList[i].value ? TableAnswerList[i].value : '') + ','
            }
          }
        }
      }

      if (this.PaperList[this.CurrentNum - 1].ItemTypeId == 47) {
        if (this.show_radiobox == true) {
          if (this.Getvideo == '') {
            this.PaperList[this.CurrentNum - 1].HasAnswer = false
          } else if (this.Getvideo == '有') {
            if (this.PaperList[this.CurrentNum - 1].AnswerList.length !== 2) {
              this.PaperList[this.CurrentNum - 1].HasAnswer = false
            } else {
              this.PaperList[this.CurrentNum - 1].HasAnswer = true
            }
          } else if (this.Getvideo == '没有') {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          } else {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          }
        }
      }
      this.SelectionList = []
      const currentState = item.CurrentAnswer
      this.PaperList.forEach((el) => {
        el.CurrentAnswer = false
      })
      item.CurrentAnswer = true
      this.num = index
      const CurrentNum2 = JSON.parse(JSON.stringify(this.CurrentNum))
      this.CurrentNum = index + 1
      const _this = this
      this.$nextTick(() => {
        if (_this.PaperList[index].Answer !== '') {
          if (_this.PaperList[index].ItemTypeId === 5) {
            const ansList = _this.PaperList[index].AnswerList
            const widthlist = _this.PaperList[index].WidhList
            for (let i = 0; i < ansList.length; i++) {
              if (ansList[i]) {
                document.getElementById(i).value = ansList[i]
              } else {
                document.getElementById(i).value = ''
              }
            }
            for (let i = 0; i < widthlist.length; i++) {
              if (widthlist[i]) {
                document.getElementById(i).style.width = widthlist[i]
              } else {
                document.getElementById(i).style.width = 100 + 'px'
              }
            }
          } else if (_this.PaperList[index].ItemTypeId === 10) {
            _this.SelectionList = _this.PaperList[_this.CurrentNum - 1].AnswerList
          } else if (_this.PaperList[index].ItemTypeId === 40) {
            const ansList2 = _this.PaperList[index].AnswerList
            for (let i = 0; i < ansList2.length; i++) {
              document.getElementById(ansList2[i].id).value = ansList2[i].value
            }
          } else if (_this.PaperList[index].ItemTypeId === 41) {
            const ansList3 = _this.PaperList[_this.CurrentNum - 1].AnswerList
            ansList3.forEach((item, index, array) => {
              item.forEach((el, inx) => {
                document.getElementById(`${index}.${inx}`).value = el
              })
            })
          } else if (_this.PaperList[index].ItemTypeId === 46) {
            const ansList4 = _this.PaperList[_this.CurrentNum - 1].AnswerList
            for (let i = 0; i < ansList4.length; i++) {
              document.getElementById(ansList4[i].id).value = ansList4[i].value
            }
          } else if (_this.PaperList[index].ItemTypeId === 47) {
            const ansList5 = _this.PaperList[index].AnswerList
            ansList5.forEach((el, index, array) => {
              document.getElementById(`${index}`).value = el
            })
          } else if (_this.PaperList[index].ItemTypeId === 42) {
            const ansList6 = _this.PaperList[index].AnswerList
            for (let i = 0; i < ansList6.length; i++) {
              document.getElementById(ansList6[i].id).value = ansList6[i].value
            }
          }
        }
      })
      this.GetImgAll()
      this.callswiper(index)
    },
    // 选词填空的回显
    showBankedCloze() {
      const stem = this.PaperList[this.num]
      if (stem.ItemTypeId == 52 || (stem.ItemTypeId == 70 && stem.AnswerList.length > 0)) {
        stem.ItemTitleWeb = arrlistTitle('回显', stem, stem.ItemTypeId)
      }
    },
    prevPhoto() {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto() {
      this.$refs.mySwiper.$swiper.slideNext()
    },

    callswiper(index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },
    // 转utf-8
    str2utf8(str) {
      // var encoder = new TextEncoder('utf8');
      return eval("'" + encodeURI(str).replace(/%/gm, '\\x') + "'")
    },
    submit() {
      const that = this
      // return
      this.$confirm('是否确认提交?', '提交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          that.PaperList.forEach((el) => {
            if (el.ItemTypeId === 51) {
              const answerList = el.ItemChange.TableItemInfo.filter((item) => item.type === 2)
              that.AnswerList.push({
                ItemId: el.ItemId,
                Answer: answerList
                  .map((item) => {
                    const { stuAnswer } = item
                    return stuAnswer.includes('\\') || stuAnswer.includes('^')
                      ? `<span class="math-tex">\\(${stuAnswer}\\)</span>`
                      : stuAnswer
                  })
                  .join('|'),
              })
              return
            }
            that.AnswerList.push({
              ItemId: el.ItemId,
              Answer:
                el.ItemTypeId === 47 && localStorage.getItem('Type_41')
                  ? `${localStorage.getItem('Type_41')}${
                      el.Answer.split('|')[1] && !localStorage.getItem('Type_41').includes('没有')
                        ? `|${el.Answer.split('|')[1]}`
                        : ''
                    }`
                  : // 填空题中包含公式答案向后台传递数据格式处理
                  el.ItemTypeId === 2 ||
                    el.ItemTypeId === 10 ||
                    el.ItemTypeId === 11 ||
                    el.ItemTypeId === 36 ||
                    el.ItemTypeId === 46 ||
                    el.ItemTypeId === 52 ||
                    el.ItemTypeId === 50 ||
                    el.ItemTypeId === 70
                  ? el.Answer
                  : ExamFormatCommon.formula_Answer(el.IsFormula, el.AnswerList),
              // ATime: el.ATime
            })
          })
          if (that.PaperList.some((val) => val.HasAnswer === false)) {
            this.$message({
              type: 'error',
              message: '请提交完整答案',
            })
            that.AnswerList = []
          } else {
            if (that.AnswerList.some((val) => val.Answer === '')) {
              this.$message({
                type: 'error',
                message: '请提交完整答案',
              })
              that.AnswerList = []
            } else {
              that.timeRecord(false)
              if (this.zuheti.length > 0) {
                this.zuheti.forEach((zuhe) => {
                  this.AnswerList.splice(zuhe.oldIndex, 0, {
                    Answer: '组合题干',
                    ItemId: zuhe.ItemId,
                  })
                })
              }
              console.log(this.AnswerList, ':this.AnswerList')
              // return
              that.$uwonhttp
                .post('/ExamItem/ExamItem/StudentWrongItemClear', {
                  UserId: that.userId,
                  PaperId: that.paperId,
                  UserAnwser: JSON.stringify(that.AnswerList),
                })
                .then((res) => {
                  if (res.data.Success == true) {
                    localStorage.removeItem('Type_41')
                    that.Submission = res.data.Data
                    that.nextKnow = true
                    that.AnswerList = []
                  } else {
                    that.AnswerList = []
                    that.$message.error(res.data.Msg)
                  }
                })
            }
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消提交',
          })
        })
    },

    handleKnow() {
      // this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
      const paperId = this.$route.query.paperId
      const from = this.$route.query.from
      this.nextKnow = false
      if (from == 'wqb') {
        this.$router.push({
          path: '/Student/Fine_micro/WrongQuestionBank',
          // query: {
          //   SubjectIdName: JSON.stringify(this.SubjectIdName)
          // }
        })
        return
      }
      this.$router.push({
        path: '/Student/Exam_Student/AnswerDetail',
        query: {
          paperId,
          paperName: this.PaperName,
        },
      })
      // this.nextKnow = false
    },
  },
}
</script>

<style lang="less" scoped>
.zuheti {
  width: 30%;
  padding-right: 20px;
  margin-right: 20px;
  border-right: 1px solid #e6e6e6;
}
.hexinsuyangPaizhao {
  p {
    font-size: 18px;
    font-weight: bold;
  }
}
// 多学科部分
.xuekeStyle {
  margin-left: 18px;
  padding: 4px 18px;
  border-radius: 100px;
}
.paperheader {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-radius: 5px;
  background: #fff;
  font-size: 23px;
  position: relative;
  span {
    line-height: 45px;
  }
  .tiltcount {
    vertical-align: middle;
    margin-right: 20px;
  }
  /deep/.el-button {
    margin-top: 0;
    width: 100px;
    font-size: 18px;
    background: #61bb96;
    border-color: #61bb96;
    color: #fff;
  }
}
.swiper_list {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .tea-analusis {
    position: relative;
    width: 80%;
    height: auto;
    padding: 10px;
    background: #ffff;
    border-radius: 5px;
  }
  .line_border {
    width: 97%;
    height: 10px;
    position: absolute;
    border: 1px solid #e6e6e6;
    background: #e6e6e6;
    top: 50%;
  }
  .left-arrow,
  .right-arrow {
    position: absolute;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: center;
    bottom: -6%;

    z-index: 2;
    .arrow-img {
      &:hover {
        background: #68bb97;
      }
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #dadada;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      i {
        background: url('../../../assets/teacher/IntelligentCorrection/icon-arrow.png') no-repeat center center;
        width: 16px;
        height: 16px;
      }
    }
  }
  .left-arrow {
    left: -1%;
    .arrow-img {
      transform: rotate(180deg);
    }
  }
  .right-arrow {
    right: -6px;
  }
  .imgbox {
    //width: 20%;
    //font-size: 18px;
    padding: 10px;
    display: flex;
    align-items: center;
    //position: relative;
    img {
      width: 30px;
      //right: 40%;
      //top: 43%;
      //position: absolute;
    }
    span {
      margin-left: 10px;
      //right: 0;
      //top: 43%;
      //position: absolute;
    }
  }
}
.Answer {
  width: 100%;
  height: auto;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  .Answer_left {
    width: 100%;
    border-radius: 5px;
    padding: 20px;
    position: relative;
    background: #ffff;

    .Level_box {
      display: flex;
      align-items: center;
      /deep/.el-rate {
        line-height: 1;
        margin-left: 10px;
      }
      /deep/.el-rate__icon {
        font-size: 25px;
        margin-top: 33px;
      }
      /deep/.el-button {
        width: 100px;
        margin-left: 20px;
        border-radius: 30px;
        background: #61bb96;
        border-color: #61bb96;
        color: #fff;
      }
    }
    .left_info {
      .paper_type {
        display: flex;
        margin-top: 20px;
        padding: 6px;
        .type_after {
          width: 5px;
          height: 30px;
          background: #2751d8;
          border-radius: 5px;
        }
        span {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #3e3e3e;
          line-height: 33px;
          margin-left: 10px;
        }
      }
      .radio_text {
        /deep/.ant-radio-group {
          display: flex;
          flex-direction: column;
          .ant-radio-wrapper {
            margin-bottom: 10px;
            white-space: pre-wrap !important;
          }
        }
      }
      .title_item {
        span {
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #828282;
          margin-top: 30px;
          display: inline-block;
        }
      }
      .btn_text {
        text-align: center;
        margin-top: 20px;
        .btn_next {
          width: 150px;
          border-radius: 20px;
          margin-left: 10px;
          background: #61bb96;
          border-color: #61bb96;
          color: #fff;
        }
      }
    }
  }
  .title-btn-whole {
    display: inline-block;
    width: 45px;
    height: 45px;
    line-height: 45px;
    margin: 15px;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 50%;
    cursor: pointer;
    font-size: 17px;
    background: #fff;
  }
  .has-answer {
    color: #fff;
    background-color: #61bb96;
  }
  .current-answer {
    border-color: #da1b1b;
    color: #da1b1b;
    background-color: #fff;
  }
}
.input_text {
  width: 100px;
  border-bottom: 1px solid #828282;
  text-align: center;
  font-size: 20px;
}
.Table_Btn {
  width: 77%;
  text-align: center;
  margin-top: 25px;
}
.tableTitle {
  border-collapse: collapse;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
.tableTitle tr {
  border: none;
}
.tableTitle td {
  width: 180px;
  height: 50px;
  font-size: 17px;
  cursor: pointer;
  border: 1px inset #cccc;
}
.Blank_box {
  border: 1px solid #ccc;
  padding: 15px;
  margin-top: 20px;
  .blank_Answer {
    font-size: 22px;
  }
  span {
    p {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
      white-space: pre-line;
      line-height: 40px;
    }
  }
}
.select_box {
  margin-top: 20px;
  span {
    font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #828282;
    line-height: 40px;
  }
}
.IMGkey_word {
  height: 35px;
  display: inline-block;
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
  cursor: pointer;
  .backcolor_gray {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ccc;
    display: flex;
    align-items: center;
    justify-content: space-around;
    img {
      width: 25px;
      height: 25px;
    }
  }
  .backcolor_blue {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #357bee;
    display: flex;
    align-items: center;
    justify-content: space-around;
    img {
      width: 25px;
      height: 25px;
    }
  }
  b:last-child {
    font-weight: normal;
    margin-left: 10px;
  }
}
.show_btn {
  .radio_box {
    margin-top: 15px;
    line-height: 40px;
    span {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
    }
  }
  /deep/.el-button {
    background: #61bb96;
    border-color: #61bb96;
    color: #fff;
    margin-top: 20px;
  }
}
/deep/ .MJXc-display {
  text-align: left !important;
}
/deep/.ant-modal-title {
  color: #fff;
}
/deep/.ant-modal-header {
  background-color: #68bb97;
}
/deep/.ant-modal-footer button + button {
  background-color: #68bb97;
  border: none;
}
.pure-table {
  border-collapse: collapse;
  border-spacing: 0;
  empty-cells: show;
  border: 1px solid #cbcbcb;
}

.pure-table caption {
  color: #000;
  font: italic 85%/1 arial, sans-serif;
  padding: 1em 0;
  text-align: center;
}

.pure-table td,
.pure-table th {
  border-left: 1px solid #cbcbcb;
  border-width: 0 0 0 1px;
  font-size: 20px;
  margin: 0;
  overflow: visible;
  text-align: center;
  padding: 15px 60px;
}

.pure-table thead {
  background-color: #e0e0e0;
  color: #000;
  text-align: left;
  vertical-align: bottom;
}

.pure-table td {
  background-color: transparent;
}

.pure-table-bordered td {
  border-bottom: 1px solid #cbcbcb;
}

.pure-table-bordered tbody > tr:last-child > td {
  border-bottom-width: 0;
}
.ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}
/deep/.el-button {
  background: #61bb96;
  border-color: #61bb96;
  color: #fff;
  margin-top: 20px;
}
</style>
