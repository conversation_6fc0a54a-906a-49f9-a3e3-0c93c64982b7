<template>
  <div class="examine">
    <div class="div">
      <div class="f-t title">
        <div @click="GoBack" style="cursor: pointer; float: left"><i class="el-icon-arrow-left"></i>返回</div>
        <div>{{ paperName }}</div>
        <div
          class="xuekeStyle"
          :style="{ 'background-color': bgColog[SubjectIdName.Id + 'bg'], color: bgColog[SubjectIdName.Id] }"
        >
          {{ SubjectIdName.name }}
        </div>
      </div>
      <p class="navigation font_size22 ipad_font_size24">
        <span>复习回顾</span>
        <span class="cur" @click="toMicroClass">微课堂</span>
      </p>
      <div class="example" v-if="loadPaper" style="text-align: center; width: 100%">
        <a-spin />
      </div>
      <div class="swiper_list">
        <div class="tea-analusis">
          <div class="line_border"></div>
          <!-- i.IsKeyTrue === 1 -->
          <!-- 'current-answer': i.CurrentAnswer, -->
          <swiper class="swiper" ref="mySwiper" :options="swiperOption">
            <swiper-slide style="text-align: center" v-for="(i, t) in paperList" :key="t">
              <a
                class="title-btn-whole"
                :class="{
                  'def-color': defalutCol === i.ItemId,
                  'wrong-bgc': i.IsKeyTrue === 2,
                }"
                @click="switchTitle(t, i.ItemId, i.TypeId)"
              >
                {{ t + 1 }}
              </a>
            </swiper-slide>
          </swiper>
          <div class="left-arrow">
            <div class="arrow-img" @click="prevPhoto">
              <i></i>
            </div>
          </div>
          <div class="right-arrow">
            <div class="arrow-img" @click="nextPhoto">
              <i></i>
            </div>
          </div>
        </div>
        <div class="imgbox">
          <!-- <span> 答题用时{{ doPaperTime }}</span>
          <img src="@/assets/student/闹钟.png" alt="" /> -->
        </div>
      </div>
      <div class="flex" style="background:#fff;">
        <div
          class="zuheti formatImg pc_font_size18 font_size28 ipad_font_size35"
          style="width: 50%; height: 60vh; overflow-y: scroll"
          v-if="zuheti.length > 0 && zuhetiTitle"
        >
          <p v-html="zuhetiTitle" v-katex style="max-width: 600px; min-width: 300px"></p>
        </div>
        <div class="alone-tit" v-for="(item, index) in pageList_show" :key="item.ItemId" :id="item.ItemId" v-katex>
          <div class="tit_header">
            <span class="font_size22 ipad_font_size24">{{ item.PaperItemType }}</span>
            <span class="font_size22 ipad_font_size24" style="margin-left: 20px">难度 :</span>
<!--      专科专练难度展示星       -->
            <el-rate v-show="LoginSource == 1" v-model="item.Level" disabled></el-rate>
<!--      三个助手难度展示文字      -->
            <span v-show="LoginSource == 2">
              {{ item.LevelName }}
            </span>
            <span class="font_size20 ipad_font_size22" style="position: absolute; right: 5%" v-show="item.Level > 3"
              ><img style="margin-right: 5px; width: 21px" src="@/assets/拓展题.png" alt="" />拓展题</span
            >
          </div>
          <p style="margin-top: 10px">
            <span>{{ newSubject }}. &nbsp;</span
            ><span
              class="font_size24 ipad_font_size26"
              style="font-size: 18px"
              v-html="item.ItemTitleWeb"
              v-katex
            ></span>
          </p>
          <div>
            <!-- 单选题 -->
            <a-radio-group v-model="value" @change="onChange" v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
              <a-radio :style="radioStyle" :value="item.ItemId + '|' + k.Opt" v-for="(k, j) in item.Options" :key="j">
                <span class="font_size22 ipad_font_size24" style="font-size: 18px">{{ k.Opt }}</span
                >.<span v-html="k.Content" v-katex class="font_size22 ipad_font_size24" style="font-size: 18px"></span>
              </a-radio>
            </a-radio-group>

            <!-- 多选题 -->
            <a-checkbox-group class="Multiple" v-if="item.ItemTypeId === '10'">
              <a-row>
                <a-col :span="24">
                  <a-checkbox
                    v-for="i in item.Options"
                    :key="i.Opt"
                    :value="item.ItemId + '|' + i.Opt"
                    v-show="i.Type === 1"
                  >
                    {{ i.Option }}:
                    <span v-html="i.Content" class="font_size22 ipad_font_size24" v-katex style="fontsize: 20px"></span>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>

            <div v-if="item.ItemTypeId === 23">
              <img :src="item.PicUrl" alt="" style="max-width: 350px" />
            </div>

            <!-- 可变行下拉填空题 -->
            <div class="select_46" v-if="IsPhotograph !== 1 && item.ItemTypeId === 46">
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size26',
                  'ipad_font_size28',
                ]"
                >首次作答：
                <span v-for="(sel, ind) in item.ItemChange.Rows" :key="ind"
                  ><span v-for="(sel_i, indx) in sel" :key="indx">{{ sel_i }}</span></span
                >
              </span>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：
                <span v-for="(Revised, ind) in item.LastItemChange.Rows" :key="ind"
                  ><span v-for="(Revised_i, indx) in Revised" :key="indx">{{ Revised_i }}</span></span
                >
              </span>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：
                <span
                  v-for="(YesItem, ind) in item.YesItemChange.Rows"
                  :key="ind"
                  style="width: 130px; display: inline-block"
                  ><span v-for="(YesItem_i, indx) in YesItem" :key="indx">{{ YesItem_i }}</span></span
                >
              </span>
            </div>

            <!-- 可变行表格 -->
            <div class="table_40" v-if="IsPhotograph !== 1 && item.ItemTypeId === 40">
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >首次作答：</span
              >
              <table class="tableTitle">
                <tr v-for="(item, index) in item.ItemChange.Titles" :key="index">
                  <td>{{ item }}</td>
                </tr>
                <tbody>
                  <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span v-html="td" v-katex></span>
                    </td>
                  </tr>
                </tbody>
              </table>
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：</span
              >
              <table class="tableTitle">
                <tr v-for="(item, index) in item.LastItemChange.Titles" :key="index">
                  <td>{{ item }}</td>
                </tr>
                <tbody>
                  <tr v-for="(td, ind) in item.LastItemChange.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span v-html="td" v-katex></span>
                    </td>
                  </tr>
                </tbody>
              </table>
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：</span
              >
              <table class="tableTitle">
                <tr v-for="(item, index) in item.YesItemChange.Titles" :key="index">
                  <td>{{ item }}</td>
                </tr>
                <tbody>
                  <tr v-for="(td, ind) in item.YesItemChange.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span v-html="td" v-katex></span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 可变行填空 -->
            <div class="blank_41" v-if="IsPhotograph !== 1 && item.ItemTypeId === 41">
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >首次作答：</span
              >
              <div class="Blank_box" v-for="(blank, ind) in item.ItemChange.Rows" :key="ind">
                <span
                  :class="[
                    { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                    'font_size22',
                    'ipad_font_size24',
                  ]"
                  class="blank_Answer"
                  >答案{{ ind + 1 }}</span
                >
                <span
                  :class="[
                    { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                    'font_size22',
                    'ipad_font_size24',
                  ]"
                  v-for="(blank_i, index) in blank"
                  :key="index"
                >
                  <p v-html="blank_i" v-katex></p>
                </span>
              </div>
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：</span
              >
              <div class="Blank_box" v-for="(Frist, Frist_i) in item.LastItemChange.Rows">
                <span
                  :class="[
                    { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                    'font_size22',
                    'ipad_font_size24',
                  ]"
                  class="blank_Answer"
                  >答案{{ Frist_i + 1 }}</span
                >
                <span
                  :class="[
                    { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                    'font_size22',
                    'ipad_font_size24',
                  ]"
                  v-for="(blank_Frist_i, indx) in Frist"
                  :key="indx"
                >
                  <p v-html="blank_Frist_i" v-katex></p>
                </span>
              </div>
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：</span
              >
              <div class="Blank_box" v-for="(blank_Yes, indx_i) in item.YesItemChange.Rows">
                <span
                  :class="[
                    { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                    'font_size22',
                    'ipad_font_size24',
                  ]"
                  class="blank_Answer"
                  >答案{{ indx_i + 1 }}</span
                >
                <span
                  :class="[
                    { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                    'font_size22',
                    'ipad_font_size24',
                  ]"
                  v-for="(blank_Yes_i, ind) in blank_Yes"
                  :key="ind"
                >
                  <p v-html="blank_Yes_i" v-katex></p>
                </span>
              </div>
            </div>

            <p class="line"></p>

            <!-- 普通题答案 -->
            <div
              class="detail_answer"
              v-if="
                IsPhotograph !== 1 &&
                item.ItemTypeId !== 46 &&
                item.ItemTypeId !== 40 &&
                item.ItemTypeId !== 41 &&
                item.ItemTypeId !== 5 &&
                item.ItemTypeId !== 51 &&
                item.ItemTypeId !== 50
              "
            >
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >首次作答：{{ item.UserAnswer }}</span
              >
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：{{ item.LastItemChange.Conclusion }}</span
              >
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：{{ item.Answer }}</span
              >
            </div>
            <div class="detail_answer" v-if="item.ItemTypeId == 36 || IsPhotograph === 1">
              <div
                class="answer-wrong"
                style="display: flex"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
              >
                首次作答：
                <SubjectiveStemPhoto
                  v-if="item.ReviewContentType == 1 || IsPhotograph === 1"
                  :Answer="item.UserAnswer"
                  useType="analysis"
                  ref="SubjectiveStemPhoto"
                ></SubjectiveStemPhoto>
                <SubjectiveStemVideo
                  v-if="item.ReviewContentType == 3 && IsPhotograph !== 1"
                  :Answer="item.UserAnswer"
                  useType="analysis"
                  ref="SubjectiveStemVideo"
                ></SubjectiveStemVideo>
                <SubjectiveStemAudio
                  v-if="item.ReviewContentType == 2 && IsPhotograph !== 1"
                  :Answer="item.UserAnswer"
                  useType="analysis"
                  ref="SubjectiveStemAudio"
                ></SubjectiveStemAudio>
              </div>
              <div
                class="answer-wrong"
                style="display: flex"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
              >
                订正答案：
                <SubjectiveStemPhoto
                  v-if="item.ReviewContentType == 1 || IsPhotograph === 1"
                  :Answer="item.LastItemChange.Conclusion"
                  useType="analysis"
                  ref="SubjectiveStemPhoto"
                ></SubjectiveStemPhoto>
                <SubjectiveStemVideo
                  v-if="item.ReviewContentType == 3 && IsPhotograph !== 1"
                  :Answer="item.LastItemChange.Conclusion"
                  useType="analysis"
                  ref="SubjectiveStemVideo"
                ></SubjectiveStemVideo>
                <SubjectiveStemAudio
                  v-if="item.ReviewContentType == 2 && IsPhotograph !== 1"
                  :Answer="item.LastItemChange.Conclusion"
                  useType="analysis"
                  ref="SubjectiveStemAudio"
                ></SubjectiveStemAudio>
              </div>
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案： {{ item.Answer != null && item.Answer != '' ? item.Answer : '略' }}</span
              >
            </div>
            <div class="detail_answer" v-if="item.ItemTypeId == 5 && IsPhotograph !== 1">
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >首次作答：</span
              >
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                v-html="item.ShowUserAnswer"
                v-katex
              ></span>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：</span
              >
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                v-html="item.LastShowUserAnswer"
                v-katex
              ></span>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：</span
              >
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                v-html="item.ShowAnswer"
                v-katex
              ></span>
            </div>
            <!-- 表格题 -->
            <div class="detail_answer" v-if="item.ItemTypeId == 51 && IsPhotograph !== 1">
              <RenderTable :TableItemInfo="item.ItemChange.TableItemInfo" :showTitle="true"></RenderTable>
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >首次作答：</span
              >
              <RenderTable :TableItemInfo="item.ItemChange.TableItemInfo" :analysis="true"></RenderTable>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：</span
              >
              <RenderTable :TableItemInfo="item.LastItemChange.TableItemInfo" :analysis="true"></RenderTable>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：</span
              >
              <RenderTable :TableItemInfo="item.ItemChange.TableItemInfo" :showAnswer="true"></RenderTable>
            </div>
            <!-- 连线题  -->
            <div v-if="item.ItemTypeId == 50 && IsPhotograph !== 1">
              <matching
                :Answer="[]"
                :SureAnser="[]"
                :linkExamData="item.ItemChange.LinkExamData"
                :SubType="0"
              ></matching>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >首次作答：</span
              >
              <matching
                :Answer="item.ShowUserAnswer"
                :SureAnser="item.Answer"
                :linkExamData="item.ItemChange.LinkExamData"
                :SubType="3"
              ></matching>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >订正答案：</span
              >
              <matching
                :Answer="item.LastShowUserAnswer"
                :SureAnser="item.Answer"
                :linkExamData="item.ItemChange.LinkExamData"
                :SubType="1"
              ></matching>
              <br />
              <span
                class="answer-wrong"
                :class="[
                  { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                  'font_size22',
                  'ipad_font_size24',
                ]"
                >正确答案：</span
              >
              <matching
                :Answer="item.Answer"
                :SureAnser="item.Answer"
                :linkExamData="item.ItemChange.LinkExamData"
                :SubType="2"
              ></matching>
            </div>
            <p
              @click="seeAnalysis(item.ItemId)"
              class="cur font_size22 ipad_font_size24"
              style="font-size: 18px; color: #68bb97"
            >
              解析
            </p>
            <p class="font_size22 ipad_font_size24" v-show="analysis === item.ItemId">
              解析: <span v-html="item.Analysis" v-katex></span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="div2" style="width: 30%">
      <div>
        <div class="List-topics">
          <p class="menu">题目目录</p>
          <a
            v-for="(i, t) in paperList"
            :key="t"
            class="title-btn-whole"
            :class="{ 'def-color': defalutCol === i.ItemId, 'wrong-bgc': i.IsKeyTrue === 2 }"
            @click="switchTitle(t, i.ItemId, i.TypeId)"
          >
            {{ t + 1 }}
          </a>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { HappyScroll } from 'vue-happy-scroll'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { arrlistTitle } from '@/utils/bankedCloze/bankedCloze'
import RenderTable from '../Exam_Student/components/tableStem/renderTable.vue'
import matching from '@/components/matching/index'
import SubjectiveStemPhoto from '@/views/Student/Exam_Student/components/subjectiveExam/answerPhoto'
import SubjectiveStemVideo from '@/views/Student/Exam_Student/components/subjectiveExam/answerVideo'
import SubjectiveStemAudio from '@/views/Student/Exam_Student/components/subjectiveExam/answerAudio'
import ExamFormatCommon from '@/common/ExamFormatCommon'

export default {
  components: {
    SubjectiveStemPhoto,
    SubjectiveStemVideo,
    SubjectiveStemAudio,
    HappyScroll,
    RenderTable,
    matching,
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.pageList_show = []
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)
    this.getPaperList()
    this.getPaperInfor()
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Paper/onlyWrong?' + ChangId) {
        this.loadPaper = true
        // 获取试卷基本信息
        this.pageList_show = []
        this.getPaperInfor()
        // 复习回顾题目详情
        this.getPaperList()
      }
    },
  },
  data() {
    return {
      // 加载
      loadPaper: true,
      Accracy: 0,
      userId: '',
      paperId: '',
      // 试卷名称
      paperName: '',
      chapter: '',
      chapterId: '',
      // 默认第一题
      defalutCol: '',
      // 切换题目数
      newSubject: 1,
      // 题目数
      paperSubjectNum: '',
      // 试卷信息
      paperList: [],
      // 精度
      stuStatistics: '#3CB98F',
      value: 1,
      analysis: false,
      radioStyle: {
        display: 'block',
      },
      ItemChange: [], // 首次做答
      LastItemChange: [], // 订正答案
      YesItemChange: [], // 正确答案
      swiperOption: {
        slidesPerView: 10, // 一行显示4个
        spaceBetween: 10, // 间隔30
        freeMode: true,
        speed: 1000, // 滑动速度
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true,
          disabledClass: 'my-button-disabled', // 前进后退按钮不可用时的类名。
          hiddenClass: 'my-button-hidden', // 按钮隐藏时的Class
        },
      },
      pageList_show: [],
      //  多学科部分
      bgColog: {},
      SubjectIdName: {},
      IsPhotograph: 0, // 高拍截图试题标识
      // 三个助手与专科专练标识
      LoginSource: localStorage.getItem('LoginSource'),
    }
  },
  methods: {
    GoBack() {
      this.$router.push({
        path: '/Student/Fine_micro/WrongQuestionBank',
      })
    },
    prevPhoto() {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto() {
      this.$refs.mySwiper.$swiper.slideNext()
    },
    seeAnalysis(id) {
      this.analysis = id
    },
    // 获取试卷基本信息
    getPaperInfor(id) {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then((res) => {
        this.paperName = res.Data.Title
        this.chapterId = res.Data.ChapterId
      })
    },
    // 复习回顾题目详情
    getPaperList() {
      this.$uwonhttp
        .post('/ExamItem/ExamItem/GetStudentWrongPaperItems', {
          PaperId: this.paperId,
          UserId: this.userId,
          DataType: 3,
        })
        .then((res) => {
          // this.paperList.forEach((item) => {
          res.data.Data.Items.forEach((item) => {
            // 选词填空题型
            if (
              (item.ItemTypeId == 52 && item.ItemChange.SelectFillBlank !== null) ||
              (item.ItemTypeId == 70 && item.ItemChange.MultitermSingle !== null)
            ) {
              item.ItemTitleWeb = arrlistTitle('展示', item, item.ItemTypeId, 1)
            } else {
              const regx4 = /\$\$\/frac.*?\$\$/g
              const regx4List = item.ItemTitleWeb.match(regx4)
              if (regx4List && regx4List.length > 0) {
                regx4List.forEach((item_regx4) => {
                  let fenshu = item_regx4
                  fenshu = fenshu.replace(
                    /\$\$\/frac\{/g,
                    `
              <div style="display:inline-block;vertical-align:middle;margin:0 8px;">
            `
                  )
                  fenshu = fenshu.replace(
                    /\}\{/g,
                    `
              <hr style="width:100%;height:3px;background:black;position:relative;top:2px"></hr>
            `
                  )
                  fenshu = fenshu.replace(
                    /\}\$\$/g,
                    `
              </div>
            `
                  )
                  item.ItemTitleWeb = item.ItemTitleWeb.replace(item_regx4, fenshu)
                })
              }
              // console.log(item.ItemTitleWeb)
              const reg = /(#&\d+@)/g
              const stem = item.ItemTitleWeb.replace(reg, ' ')
              item.ItemTitleWeb = stem
              item.Options.forEach((el) => {
                const regex = new RegExp('<img')
                const stem = el.Content.replace(regex, `<img style="max-width: 80%; height: auto"`)
                el.Content = stem
              })
            }
          })
          this.zuheti = ExamFormatCommon.filterZuheti(res.data.Data.Items)
          this.paperList = res.data.Data.Items
          this.defalutCol = res.data.Data.Items[0].ItemId
          this.Accracy = res.data.Data.Accracy
          this.paperSubjectNum = res.data.Data.Items.length
          this.IsPhotograph = res.data.Data.IsPhotograph
          this.loadPaper = false
          this.pageList_show.push(this.paperList[0])
          // let ParentId_c = this.PaperList[this.CurrentNum - 1].ParentId
          let ParentId_c = this.paperList.find((stem) => stem.ItemId == this.defalutCol).ParentId
          this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ''
        })
    },
    // 切换题目
    switchTitle(index, ItemId, typeId) {
      // document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.pageList_show[0] = this.paperList[index]
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.defalutCol = ItemId
      this.callswiper(index)
    },
    callswiper(index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },

    // 选项选择
    onChange(value) {},
    // 微课堂
    toMicroClass() {
      this.$router.push({
        path: '/Student/Exam_Student/MicroClass',
        query: {
          chapterId: this.chapterId,
          paperId: this.paperId,
          isOnly: '1',
          SubjectIdName: JSON.stringify(this.SubjectIdName), // 学科ID
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
.zuheti {
  width: 30%;
  padding:20px 20px 0 20px;
  margin-right: 20px;
  border-right: 1px solid #e6e6e6;
}
.f-t {
  font-size: 21px;
}
// 多学科部分
.xuekeStyle {
  padding: 4px 18px;
  border-radius: 100px;
}
// 正确颜色
.correct {
  color: #68bb97;
  font-size: 18px;
}
.answer-wrong {
  color: #ff682c;
  font-size: 18px;
}
.wrong-bgc {
  background-color: #ff682c;
}
// .def-color {
//   background-color: #68bb97;
//   color:#fff;
// }
.examine {
  display: flex;
  .div {
    width: 100%;
    // margin-right: 23px;
    height: auto;
  }
}
.select_46 {
  line-height: 30px;
}
.Blank_box {
  border: 1px solid #ccc;
  padding: 15px;
  margin-top: 10px;
  .blank_Answer {
    font-size: 18px;
  }
  span {
    p {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
      white-space: pre-line;
      line-height: 40px;
    }
  }
}
.table_40 {
  line-height: 50px;
}
.tableTitle {
  border-collapse: collapse;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
.tableTitle tr {
  border: none;
}
.tableTitle td {
  width: 180px;
  height: 50px;
  font-size: 17px;
  cursor: pointer;
  border: 1px inset #cccc;
}
// 右边
.div2 {
  padding: 80px 16px 0 16px;
  background-color: #fff;
  .right_header {
    text-align: center;
    img {
      height: 50px;
    }
    span {
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      display: contents;
    }
  }
  .p-chart {
    font-size: 17px;
    display: inline-block;
  }
  // 统计
  .Statistics {
    margin-bottom: 20px;
    margin-top: 20px;
    text-align: center;
  }
  // 错题扫除
  .wrong-topic {
    text-align: center;
    margin-bottom: 80px;
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
    }
  }
}
.go_work {
  text-align: center;
  /deep/.el-button {
    width: 100px;
    color: #fff;
    background-color: #68bb97;
  }
}
.navigation {
  //line-height: 38px;
  //padding-left: 15px;
  padding: 10px 14px;
  font-size: 22px;
  background-color: #fff;
  border-radius: 5px;
  margin-bottom: 20px;
  //height: 50px;
  //line-height: 50px;
  span:nth-child(1) {
    display: inline-block;
    height: 100%;
    margin-right: 25px;
    border-bottom: 2px solid #68bb97;
  }
}
.title {
  margin: 0px 5px 20px;
  //height: 50px;
  padding: 10px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}
.List-topics {
  margin-top: 20px;
  text-align: center;
  .menu {
    font-size: 18px;
  }
}
// 单题
.alone-tit {
  width: 100%;
  padding: 10px 10px;
  margin-bottom: 10px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  .line {
    height: 1px;
    margin-top: 10px;
    border-bottom: 1px solid #f6f6f6;
  }
  .wrong-storage {
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
      cursor: pointer;
    }
  }
  // 已辅导
  .tutored {
    display: inline-block;
    width: 74px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    color: #ff682c;
    border: 1px solid #ff682c;
    border-radius: 5px;
    cursor: pointer;
  }
  .tit_header {
    display: flex;
    /deep/.el-rate {
      // line-height: 1;
      font-size: 27px;
      margin-left: 10px;
    }
    /deep/.el-rate__icon {
      font-size: 25px;
    }
    span {
      font-size: 21px;
    }
  }
}
.detail_answer {
  margin-top: 5px;
  line-height: 30px;
}
// 题目目录
.title-btn-whole {
  display: inline-block;
  width: 41px;
  height: 40px;
  line-height: 40px;
  margin: 16px 16px 0 8px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 35px;
  cursor: pointer;
  background: #fff;
  font-size: 16px;
}
/deep/.happy-scroll-content {
  width: 100%;
}
/deep/.ant-radio-wrapper {
  line-height: 2.5;
  white-space: pre-wrap !important;
}
.swiper_list {
  width: 100%;
  display: flex;
  .tea-analusis {
    position: relative;
    width: 100%;
    height: auto;
    padding: 10px;
    background: #ffff;
    border-radius: 5px;
  }
  .line_border {
    width: 97%;
    height: 10px;
    position: absolute;
    border: 1px solid #e6e6e6;
    background: #e6e6e6;
    top: 55%;
  }
  .left-arrow,
  .right-arrow {
    position: absolute;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: center;
    bottom: -25%;

    z-index: 2;
    .arrow-img {
      &:hover {
        background: #68bb97;
      }
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #dadada;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      i {
        background: url('../../../assets/teacher/IntelligentCorrection/icon-arrow.png') no-repeat center center;
        width: 16px;
        height: 16px;
      }
    }
  }
  .left-arrow {
    left: 8px;
    .arrow-img {
      transform: rotate(180deg);
    }
  }
  .right-arrow {
    right: 6px;
  }
  .imgbox {
    width: 20%;
    font-size: 18px;
    padding: 10px;
    position: relative;
    background: #fff;
    img {
      width: 30px;
      right: 70%;
      top: 43%;
      position: absolute;
    }
    span {
      margin-left: 10px;
      right: 5%;
      top: 43%;
      position: absolute;
    }
  }
}
.wrong-bgc {
  border: 2px solid #da1b1b;
  color: #da1b1b;
}
.def-color {
  border: 2px solid #61bb96;
  color: #61bb96;
}

.current-answer {
  border-color: #357bee;
  color: #357bee;
}
</style>
