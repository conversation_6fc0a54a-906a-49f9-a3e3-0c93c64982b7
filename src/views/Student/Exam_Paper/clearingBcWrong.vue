<template>
  <div>
    <div id="practice-preview" class="clearfix" style="display: flex;">
      <div class="subject">
        <div class="example" v-if="loadPaper" style="text-align: center;width:100%;">
          <a-spin />
        </div>
        <p class="sub-title">{{ paperName }}</p>
        <div class="sub-info" v-for="(item, index) in paperData" :key="item.ItemId" :id="item.ItemId">
          <span style="font-size: 18px">({{ index + 1 }}) </span>
          <span v-html="item.Title" style="font-Size: 20px;" v-katex></span>
          <!-- 试题选项 -->
          <div style="padding-left: 35px" v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
            <a-radio-group @change="SelectOptions">
              <a-radio :style="radioStyle" :value="item.ItemId + '|' + k.Opt" v-for="(k, j) in item.Options" :key="j">
                <span v-if="k.TypeValue === '2'" style="font-Size: 20px;">{{ k.Opt }}:&nbsp;&nbsp;</span>
                <span class="p-inline" v-if="k.TypeValue === '2' ||　k.TypeValue === '11'" v-html="k.Content" style="fontSize: 18px;display:inline-block" v-katex></span>
              </a-radio>
            </a-radio-group>

          </div>
          <div v-if="item.ItemTypeId === 10">
            <a-checkbox-group class="Multiple" @change="SelectOptions1">
              <a-row>
                <a-col :span="24">
                  <a-checkbox v-for="(i,ind) in item.Options" :key="ind" :value="item.ItemId + '|' + i.Opt">
                    {{ i.Opt }}:
                    <span v-html="i.Content" style="font-Size: 20px;" v-katex></span>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </div>

          <!-- 主观题订正 -->
          <div v-if="item.ItemTypeId === 23" style="margin-top:15px;">
            <p><img style="width: 650px;" :src="item.TeacherPicUrl" alt=""></p>
            <p><img style="width: 650px;" :src="item.PicUrl" alt="" v-if="item.TeacherPicUrl === ''"></p>
          </div>
          <div v-if="item.ItemTypeId === 23" @click="subjectUpload(item.ItemId)">
            <a-upload name="file" list-type="picture-card" class="avatar-uploader" :show-upload-list="false" :action="defaultAddress" @change="handleChange">
              <img :src="imageUrl" alt="avatar" style="max-width:400px;" :id="item.ItemId + '@'" />
              <div>
                <a-icon :type="loading ? 'loading' : 'plus'" />
                <div class="ant-upload-text">
                  注：*可用相机拍摄作答过程，形成图片，导入本地进行上传
                </div>
              </div>
            </a-upload>
          </div>
        </div>
      </div>
      <div class="sub-btn">
        <p class="clearfix title-info">
          <span class="fl">{{ newSubject }}/{{ paperNum }}题</span>
          <span class="fg">
            <!-- <img style="width:14px;" src="@/assets/user/编组计时.png" alt=""> -->
            <!-- {{ callinTime }} -->
          </span>
        </p>
        <div>
          <!-- <p>数学符号</p> -->
        </div>
        <div class="title-btn" @scroll="onScroll">
          <p>题目目录</p>
          <a v-for="(i, t) in paperData" :key="t" class="title-btn-whole" :class="{ 'color': color === i.ItemId }" @click="switchTitle(t, i.ItemId, i.TypeId)">
            {{ t + 1 }}
          </a>
        </div>
        <p class="submit">
          <a-button @click="viewsSubject">
            提交
          </a-button>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  created() {
    this.id = localStorage.getItem('UserId')
    this.dataTypeId = this.$route.query.dataId
    this.paperId = this.$route.query.paperId
    this.chapterId = this.$route.query.chapterId
    // 获取试卷基本信息
    this.getTitleList()
    // 获取试卷的名称
    this.getTestPaperName()
    this.getAnswer()
    // this.getSubjectInfo()
  },
  mounted() {
    window.addEventListener('scroll', this.getScrollPosition, false)
  },
  updated() {},
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.id = localStorage.getItem('UserId')
      this.dataTypeId = this.$route.query.dataId
      this.paperId = this.$route.query.paperId
      this.chapterId = this.$route.query.chapterId
      if (this.$route.fullPath === '/Student/Exam_Paper/clearingBcWrong?' + ChangId) {
        this.loadPaper = true
        this.paperData = []
        // 获取试卷基本信息
        this.getTitleList()
        // 获取试卷的名称
        this.getTestPaperName()
        this.eachAnswer = []
        this.testChoiceAnswer = []
      }
    }
  },
  data() {
    return {
      loading: false,
      imageUrl: require('@/assets/student/upload.png'),
      // 主观上传地址
      defaultAddress: '',
      loadPaper: true,
      id: '',
      dataTypeId: 1,
      heightPage: 0,
      nextKnow: false,
      // 试卷id
      paperId: '',
      chapterId: '',
      // 试卷信息
      paperData: [],
      // 试卷名称
      paperName: '',
      // 试卷题数
      paperNum: '',
      // 当前题目标号
      newSubject: '1',
      // 切换题目颜色
      color: '',
      //  // 题目切换
      key: 1,
      // 填空题答案
      testAnswer: [],
      // 单选题答案
      testChoiceAnswer: [],
      testChoiceAnswer1: [],
      testChoiceAnswer2: [],
      radioStyle: {
        display: 'block',
        lineHeight: '30px'
      }
    }
  },
  methods: {
    onScroll() {},
    // 获取试卷基本信息
    getTitleList() {
      this.$uwonhttp
        .post('/ExamItem/ExamItem/GetStudentWrongBCPaperItems', {
          UserId: this.id,
          PaperId: this.paperId,
          DataType: 2
        })
        .then(res => {
          this.paperData = res.data.Data.Items
          this.paperNum = res.data.Data.Items.length
          this.color = res.data.Data.Items[0].ItemId
          this.paperData.forEach((item, index) => {
            // const reg = /(#@)/g
            const reg = /(#&\d+@)/g
            // const id = item.ItemId
            const inputele =
              '<input autocomplete="off"  type="text" itemId= "' +
              item.ItemId +
              '" style="width: 100px;border: 0px; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;" name="' +
              'input' +
              '"/>'
            const stem = item.ItemTitleWeb.replace(reg, inputele)
            item.Title = stem
            this.loadPaper = false
          })
        })
    },
    // 获取试卷的名称
    getTestPaperName() {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then(res => {
        this.paperName = res.Data.Title
      })
    },
    // 切换题目
    switchTitle(index, ItemId, typeId) {
      document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.newItemId = ItemId
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.color = ItemId
      // 切换题目时的类型ID
      this.newTextType = typeId
      // 处理试卷切换题目选项
      this.switchTitleOption(ItemId)
      // this.updateFormat(this.paperData)
    },
    // 处理切换题目时选项
    switchTitleOption(ItemId) {
      this.$http
        .post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', {
          itemId: ItemId
        })
        .then(res => {
          this.option = res.Data
        })
    },
    // 选择题的选项变化
    SelectOptions(e) {
      const choiceAnswer = e.target.value
      const choiceArray = choiceAnswer.split('|')
      const itemid = choiceArray[0]
      const selectOption = choiceArray[1]

      const exist = this.testChoiceAnswer.some(item => {
        if (item.ItemId === itemid) {
          return true
        }
      })

      if (!exist) {
        this.testChoiceAnswer.push({
          ItemId: itemid,
          Answer: selectOption
        })
        this.selectOption = selectOption
      } else {
        this.testChoiceAnswer.forEach(item => {
          if (item.ItemId === itemid) {
            item.Answer = selectOption
            this.selectOption = selectOption
          }
        })
      }
    },
    // 多选题的选项
    SelectOptions1(checkedValues) {
      checkedValues.forEach(item => {
        const items = item.split('|')
        const itemid = items[0]
        const selectOption = items[1]
        const exist = this.testChoiceAnswer1.some(item => {
          if (item.ItemId === itemid) {
            return true
          }
        })

        if (!exist) {
          this.testChoiceAnswer1.push({
            ItemId: itemid,
            AnswerArray: [selectOption],
            Answer: selectOption
          })
        } else {
          this.testChoiceAnswer1.forEach(item => {
            if (item.ItemId === itemid) {
              item.AnswerArray.push(selectOption)
            }
          })
        }
        this.testChoiceAnswer1.forEach(item => {
          const b = checkedValues.map(res => {
            return res.split('|')[1]
          })
          const TestAnswer = [...new Set(item.AnswerArray)].filter(i => {
            if (b.includes(i)) {
              return i
            }
          })
          TestAnswer.sort()
          item.Answer = TestAnswer.join('|')
        })
        const abc = []
        this.testChoiceAnswer1.forEach(value => {
          value.Answer.split(',')
            .sort()
            .join('|')
          abc.push({
            ItemId: value.ItemId,
            Answer: value.Answer
          })

          this.testChoiceAnswer2 = abc
        })
      })
    },
    // 提交试卷
    viewsSubject() {
      const that = this
      // this.updateFormat()
      const modal = this.$confirm({
        title: '温馨提示',
        content: '是否确认提交练习',
        okText: '确认',
        cancelText: '取消',
        // 确定按钮回调
        onOk(e) {
          // 停止计时
          // that.timeRecord(false)
          // 清除提示框
          modal.destroy()
          const inputs = document.getElementsByTagName('input')

          for (var i = 0; i < inputs.length; i++) {
            const itemId = inputs[i].getAttribute('itemid')
            const each = inputs[i].value

            const exist = that.testAnswer.some(item => {
              if (item.ItemId === itemId) {
                return true
              }
            })

            if (!exist) {
              that.testAnswer.push({
                ItemId: itemId,
                AnswerArray: [each],
                Answer: each
              })
            } else {
              that.testAnswer.forEach(item => {
                if (item.ItemId === itemId) {
                  item.AnswerArray.push(each)
                }
              })
            }
          }

          for (var x = 0; x < that.testAnswer.length; x++) {
            that.testAnswer[x].Answer = that.testAnswer[x].AnswerArray.join('|')
          }
          // that.eachAnswer = [...that.testChoiceAnswer, ...that.testAnswer, ...that.testChoiceAnswer2]
          that.eachAnswer = [...that.testChoiceAnswer, ...that.testAnswer]
          const EachAnswer = that.eachAnswer.filter(item => item.ItemId !== null)
          that.eachAnswer = EachAnswer
          let arr = 0
          that.eachAnswer.forEach(el => {
            if (el.Answer === '' || el.Answer.replace(/\|/g, '').replace(' ', '') === '') {
              arr = 1
            }
          })
          if (arr == 1) {
            return that.$message.error('请提交完整的答案')
          }
          if (that.eachAnswer.length !== that.paperData.length) {
            return that.$message.error('请提交完整的答案')
          } else {
            that.$message.success('提交成功')
            that.$uwonhttp
              .post('/ExamItem/ExamItem/StudentCorrect', {
                UserId: that.id,
                UserAnwser: JSON.stringify(that.eachAnswer),
                PaperId: that.paperId
              })
              .then(res => {
                if (res.data.Success) {
                  that.nextKnow = true
                  that.$router.push({
                    path: '/Student/TaskCenter'
                    // query: {
                    //   paperId: that.paperId,
                    //   chapterId: that.chapterId
                    // }
                  })
                }
              })
          }
        }
      })
    },
    // 提交主观题图片
    subjectUpload(id) {
      this.itemImgId = id
      this.defaultAddress = `http://uwoomobile.doggod.xyz/ExamItem/ExamItem/StuItemPic?userId=${this.userId}&paperId=${this.paperId}&itemId=${id}`
    },
    // 上传图片
    handleChange(info, label, extra) {
      if (info.file.status === 'uploading') {
        this.loading = true
        return
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj, imageUrl => {
          // this.imageUrl = imageUrl
          document.getElementById(this.itemImgId + '@').src = imageUrl
          this.loading = false
        })
      }
    },
    handleKnow() {
      this.nextKnow = false
      this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
    },
    getAnswer(id) {
      this.$http.post('/Paper/Exam_ItemOptiAnswer/GetAnswerDataByItemId', { itemId: '11177' }).then(res => {
        this.answer = res.Data.Content
        this.completion = this.answer.split('|')
        this.mulAnswer = res.Data.Content.split('')
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 题目
.subject {
  float: left;
  width: 71%;
  height: 800px;
  overflow: scroll;
  margin-right: 10px;
  padding: 50px;
  background-color: #fff;
  border-radius: 5px;
  // 试卷名称
  .sub-title {
    font-size: 20px;
    color: #49534e;
    text-align: center;
  }
  // 试卷主体
  .sub-info {
    margin-bottom: 50px;
    border-bottom: 1px dotted #ddd;
    padding-top: 30px;
    padding-bottom: 50px;
    p {
      display: inline-block;
    }
    /deep/.ant-radio-wrapper {
      height: 100%;
    }
  }
}
// 题目对应按钮
.sub-btn {
  // position: fixed;
  right: 0;
  // top: 130px;
  // height: 100%;
  width: 25%;
  // height: 300px;
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;

  .title-info {
    padding-bottom: 32px;
    border-bottom: 1px dotted #ccc;
  }
  .title-btn {
    padding: 0 40px;
    p {
      margin-bottom: 0;
    }
    .color {
      color: #fff;
      background-color: #66bc9a;
    }
  }
  // 对应按钮
  .title-btn-whole {
    display: inline-block;
    width: 41px;
    height: 40px;
    line-height: 40px;
    margin: 16px 16px 0 0;
    text-align: center;
    color: #d6d6d6;
    border: 1px solid #ccc;
    border-radius: 35px;
    cursor: pointer;
  }
}
// 提交
.submit {
  margin-top: 75px;
  text-align: center;
  button {
    display: inline-block;
    width: 114px;
    height: 50px;
    line-height: 50px;
    color: #fff;
    background-color: #61bb96;
    border-radius: 25px;
  }
}

.p-inline {
  p {
    display: inline-block;
  }
}
// 模态框
/deep/.ant-modal-title {
  color: #fff;
}
/deep/.ant-modal-header {
  background-color: #68bb97;
}
/deep/.ant-modal-footer button + button {
  background-color: #68bb97;
  border: none;
}
// 上传图的
/deep/.ant-upload.ant-upload-select-picture-card {
  width: 400px;
  height: 220px;
}
</style>
