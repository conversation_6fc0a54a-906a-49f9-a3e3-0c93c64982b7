<template>
  <div class="whole-mr clearfix" style="margin: 0 auto">
    <p class="histroy-menu font_size22 ipad_font_size24">历史周报</p>
    <div class="histroy_content">
      <div class="item_history" v-for="(item, index) in weeklyArray" :key="index">
        <span class="font_size20 ipad_font_size22">{{ item.Title }}</span>
        <br />
        <span class="font_size20 ipad_font_size22">{{ item.WeeklyTime }}</span>
        <br />
        <el-button @click="ViewDetail(item)" class="font_size16 ipad_font_size18">查看</el-button>
      </div>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNo"
        :page-size="pageNum"
        background
        layout="prev, pager, next"
        :total="totalNum"
      >
      </el-pagination>
    </div>
    <!-- <div v-if="lackHistroy" class="lack-history">
      <img src="@/assets/lack/暂无搜索记录.png" alt="">
      <p>暂无历史周报</p>
    </div> -->
  </div>
</template>

<script>
import eventBus from '@/components/eventBus/eventBus'

export default {
  created() {
    this.GetHistoryStudentWeekly()
  },
  data() {
    return {
      // 缺省
      lackHistroy: false,
      histroyList: [],
      pageNo: 1,
      pageNum: 15,
      totalNum: 0,
      weeklyArray: [],
    }
  },
  watch: {
    $route() {
      if (this.$route.fullPath === '/Student/StudentWeekly/HistroyWeekly') {
        this.GetHistoryStudentWeekly()
      }
    },
  },
  methods: {
    //历史周报
    GetHistoryStudentWeekly() {
      this.$uwonhttp
        .post('/Weekly/StudentWeekly/GetHistoryStudentWeekly', { pageNo: this.pageNo, pageIndex: this.pageNum })
        .then((res) => {
          this.weeklyArray = res.data.Data.Items
          this.totalNum = res.data.Data.TotalItems
        })
    },
    handleSizeChange(val) {
      this.pageNum = val
      this.GetHistoryStudentWeekly()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.GetHistoryStudentWeekly()
    },
    ViewDetail(item) {
      this.$router.push({
        path: '/Student/StudentWeekly/newStudyWeekly',
        query: {
          id: item.Id,
        },
      })
      // this.$router.push({ path: '/Student/StudentWeekly/newStudyWeekly' })
      // eventBus.$emit('ItemId', item.Id)
    },
  },
}
</script>

<style lang="less" scoped>
.m-r {
  margin-right: 25px;
}
// 缺省
.lack-history {
  text-align: center;
  color: #ccc;
}
.histroy-menu {
  margin-bottom: 24px;
  padding: 12px;
  background-color: #fff;
  border-radius: 5px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #4d5753;
}
.histroy-info {
  padding: 24px 16px;
  background-color: #fff;
  border-radius: 5px;
  .histroy-p {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
  }
}
.histroy_content {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .item_history {
    width: 19%;
    background: #fff;
    border-radius: 5px;
    text-align: center;
    padding: 10px;
    line-height: 50px;
    margin-bottom: 20px;
    span:nth-of-type(1) {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4d5753;
    }
    span:nth-of-type(2) {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #8e8e8e;
    }
    /deep/ .el-button {
      width: 40%;
      color: #fff;
      background: #68bb97;
    }
  }
}
.histroy_content:after {
  content: '';
  width: 59.5%;
}
.pagination {
  text-align: center;
  /deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #68bb97;
    color: #fff;
  }
}
</style>
