<template>
  <div>
    <div class="study_page" v-loading="loading" style="margin: 0 auto">
      <div class="bgimg">
        <div class="get_time">
          <span>{{ weekly.CurrWeek }}</span>
        </div>
        <div class="weekly" @click="goHisroyWeekly">
          <span>历史周报</span>
          <img src="@/assets/student/zhoubao.png" alt="" />
        </div>
        <div class="bg_content">
          <span>本周关键词</span>
          <br />
          <span>{{ weekly.Keyword }}</span>
          <br />
          <span v-if="weekly.Keyword == '激流涌进'">{{ weekly.NarrationSum }}</span>
          <span v-if="weekly.Keyword != '激流涌进'"
            >过去的一周你很勤奋，有<span style="color: #ffffff">{{ weekly.StudyTime }}</span
            >沉浸在知识的海洋里。不仅按时完成练习任务，你还获得了<span style="color: #ffffff"
              >“{{ weekly.NarrationSum }}”</span
            >
            荣誉称号，但是完成练习后也不要忘记勤加复习。</span
          >
          <br />
          <span
            v-if="weekly.Keyword != '激流涌进'"
            style="font-size: 18px; font-family: PingFangSC-Regular, PingFang SC; font-weight: 400; color: #474725"
            >继续一路披荆斩棘，勇往直前吧！</span
          >
        </div>
        <div class="GoPractice" v-if="weekly.Keyword == '激流涌进'" @click="goPractice">
          <img src="@/assets/student/btn.png" alt="" />
        </div>
        <img src="@/assets/student/backg2.png" alt="" />
        <div class="bg_right">
          <div class="bg_first">
            <img src="@/assets/student/backg.png" alt="" />
          </div>
        </div>
        <div class="bg_btn">
          <div class="btn_item" v-if="weekly.SummaryPractice != null">
            <span>{{ weekly.SummaryPractice }}</span>
          </div>
          <div class="btn_item" v-if="weekly.Evaluate != null">
            <span>{{ weekly.Evaluate }}</span>
          </div>
          <div class="btn_item" v-if="weekly.ExcellentPractice != null">
            <span>{{ weekly.ExcellentPractice }}</span>
          </div>
          <div class="btn_item" v-if="weekly.SpeedPractice != null">
            <span>{{ weekly.SpeedPractice }}</span>
          </div>
        </div>
        <div
          class="next_page"
          @click="ViewWeekly"
          v-if="
            weekly.SummaryPractice != null &&
            weekly.Evaluate != null &&
            weekly.SpeedPractice != null &&
            weekly.ExcellentPractice != null
          "
        >
          <span class="font_size20 ipad_font_size22">下一页</span>
          <img src="@/assets/student/nextPage.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import eventBus from '@/components/eventBus/eventBus'

export default {
  created() {
    // this.userId = localStorage.getItem('UserId')
    // this.GetPlateListV2()
    this.init()
  },
  data() {
    return {
      userId: '',
      ItemId: '',
      weekly: {},
      loading: false,
    }
  },
  mounted() {
    // eventBus.$on('ItemId', ItemId => {
    //   this.ItemId = ItemId
    // })
  },
  watch: {
    $route() {
      if (this.$route.fullPath === '/Student/StudentWeekly/newStudyWeekly') {
        // this.GetPlateListV2()
        this.init()
      }
    },
  },
  components: {},
  methods: {
    init() {
      this.userId = localStorage.getItem('UserId')
      this.ItemId = this.$route.query.id || ''
      this.GetPlateListV2()
    },
    //学生端 学习周报
    async GetPlateListV2() {
      this.loading = true
      let params = {
        userId: this.userId,
        Id: this.ItemId,
      }
      const res = await this.$uwonhttp.post('/Weekly/StudentWeekly/GetPlateListV2', params)
      if (res.status == 200) {
        this.loading = false
        this.weekly = res.data.Data
        this.loading = false
      } else {
        this.$message.error('请求失败')
        this.loading = false
      }
      // this.ItemId = ''
    },
    // 历史周报
    goHisroyWeekly() {
      this.$router.push({ path: '/Student/StudentWeekly/HistroyWeekly' })
    },
    //练习中心
    goPractice() {
      this.$router.push({ path: '/Student/TaskCenter' })
    },
    //下一页
    ViewWeekly() {
      this.$router.push({ path: '/Student/StudentWeekly/WeeklyPage?id='+ this.ItemId})
    },
  },
}
</script>

<style lang="less" scoped>
.study_page {
  width: 85%;
  height: 700px;
  position: relative;
  background: #ffff;
  left: 3%;
}
.bgimg {
  width: 100%;
  height: 100%;
  background: url('../../../assets/student/backg1.png');
  background-repeat: no-repeat;
  background-size: auto;
  background-size: contain;
  img {
    width: 250px;
    position: absolute;
    bottom: 12%;
    left: 3%;
  }
  .get_time {
    width: 25%;
    height: 50px;
    background: url('../../../assets/student/backg4.png');
    background-repeat: no-repeat;
    background-size: auto;
    background-size: contain;
    position: relative;
    left: 5%;
    top: 5%;
    span {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #474725;
      line-height: 60px;
      margin-left: 11px;
    }
  }
  .bg_content {
    width: 25%;
    position: relative;
    top: 9%;
    left: 1%;
    line-height: 35px;
    span:nth-of-type(1) {
      font-size: 17px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #474725;
    }
    span:nth-of-type(2) {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #474725;
    }
    span:nth-of-type(3) {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #474725;
    }
    span:nth-of-type(4) {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #474725;
    }
  }
  .GoPractice {
    width: 10%;
    position: absolute;
    bottom: 4%;
    left: 5%;
    img {
      width: 150px;
      cursor: pointer;
    }
  }
  .weekly {
    width: 10%;
    position: absolute;
    right: 0;
    top: 0%;
    cursor: pointer;
    img {
      width: 20px;
      height: 20px;
      position: absolute;
      bottom: 33%;
      left: 55%;
    }
    span {
      font-size: 18px;
      line-height: 50px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #474725;
      vertical-align: middle;
    }
  }
  .bg_right {
    position: relative;
    left: 30%;
    img {
      position: absolute;
      top: 50%;
      width: 500px;
    }
  }
  .bg_btn {
    position: absolute;
    right: 5%;
    top: 20%;
    .btn_item {
      margin-bottom: 50px;
      height: 55px;
      padding: 5px;
      text-align: center;
      border-radius: 10px;
      background: rgba(153, 188, 68, 0.4);
      span {
        font-size: 18px;
        line-height: 50px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #474725;
      }
    }
  }
  .next_page {
    width: 10%;
    position: absolute;
    right: 0;
    bottom: 2%;
    cursor: pointer;
    img {
      width: 25px;
      height: 25px;
      position: absolute;
      bottom: 25%;
      left: 45%;
    }
    span {
      font-size: 18px;
      line-height: 50px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #474725;
      vertical-align: middle;
    }
  }
}
</style>
