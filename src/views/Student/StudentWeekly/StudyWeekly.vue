<template>
  <div class="whole-mr">
    <p class="clearfix micro-catalog">
      <span class="fl m-r study">学习周报</span><span class="fl week-time">{{ weekDetails.BeginTime }} -- {{ weekDetails.EndTime }}</span>
      <span class="fg his-bank cur" @click="toHisroyWeekly"><img src="@/assets/student/histroy.png" alt="" />历史周报</span>
    </p>
    <div v-if="IsNoVip !== '1'" class="week-lack">
      <img src="@/assets/lack/暂无历史通知bai.png" alt="" />
      <p>您的学校暂未开放该功能</p>
    </div>
    <div v-if="weeklyLack" class="week-lack">
      <img src="@/assets/lack/暂无练习记录.png" alt="" />
      <p>本周学习报告将在下周呈现哦</p>
    </div>
    <div class="week-situation" v-show="!weeklyLack && IsNoVip === '1'">
      <p class="practice-sit">
        <span class="complete-sub">练习情况</span>
        <span>本周共完成<span class="complete-sub" style="font-size: 14px; margin-right: 0">{{
            weekDetails.DoPaperCount
          }}</span>份练习</span>
        <span class="complete-sub">学习评价</span>
        <span>{{ weekDetails.Rank }}</span>
        <span class="complete-sub">平均用时</span>
        <span>{{ weekDetails.DoTime }}</span>
      </p>
      <p class="title-col">本周总结</p>
      <p class="title-mb">
        你经常做题的时间段是<span><span class="color">{{ weekDetails.DoItemInterval }}</span>, 你的正确率<span class="color">{{ weekDetails.StudentAccuracy }}%</span>。共发现了你的优秀知识点<span
            class="color">{{ weekDetails.GoodKnowledge }}</span>个,薄弱知识点<span style="color: red">{{ weekDetails.WeakKnowledge }}</span>个,要继续努力哦！
        </span>
      </p>
      <p class="title-col">学情分析</p>
      <div class="clearfix study-ana">
        <div class="specific-block fl">
          <p class="f-s flex first1-p">
            <span class="block-m-r flex1">练习名称</span><span class="block-m-r flex1">学习评价</span><span>平均用时</span>
          </p>
          <happy-scroll>
            <div>
              <p v-for="(item, index) in LearningList" :key="index" class="flex first-p">
                <span class="flex1">{{ item.PaperTitle }}</span>
                <span class="flex1">{{ item.Rank }}</span>
                <span>{{ item.DoTime }}</span>
              </p>
            </div>
          </happy-scroll>
        </div>
        <div class="specific-block-chart fl" ref="specificChart"></div>
      </div>
      <p class="title-col">学科能力分析</p>
      <div class="clearfix">
        <div class="fl subject-info">
          <p>
            <span>知识点掌握情况</span>
            <span class="fg" style="display: inline-block; font-size: 9px"><img src="@/assets/student/mark.png" alt="" />薄弱知识点：个人正确率小于参考值的知识点</span>
          </p>
          <div class="fl sub-wid">
            <p class="f-s flex first1-p Knowle-p">
              <span class="Knowledge-mr flex1">知识点</span><span class="flex1">学习评价</span><span class="flex1">&nbsp;&nbsp;</span>
            </p>
            <!-- 学科能力分析 知识点掌握情况 -->
            <happy-scroll>
              <div>
                <p class="flex first-p Knowle-bor" v-for="(item, index) in KnowledgeList" :key="index">
                  <span class="flex1">{{ item.ChapterName }}<span v-if="item.IsIsWeak === 1" style="color: #f69598">【薄弱】</span></span>
                  <span class="flex1">{{ item.Rank }}</span>
                  <span class="cur" @click="toStudy(item.ChapterId)">去学习<img src="@/assets/student/trunRight.png" alt="" /></span>
                </p>
              </div>
            </happy-scroll>
          </div>
        </div>
        <div class="fl subject-info">
          <p>
            <span>知识版块分析</span><span class="fg" style="display: inline-block"><img src="@/assets/student/mark.png" alt="" />薄弱知识版块：个人正确率小于参考值的知识版块</span>
          </p>
          <div class="fl sub-wid">
            <p class="f-s flex first1-p Knowle-p">
              <span class="flex1">知识版块</span><span class="flex1">学习评价</span><span>错题情况</span>
            </p>
            <!-- 学科能力分析 题型版块分析 -->
            <happy-scroll>
              <div>
                <p class="flex first-p Knowle-bor" v-for="item in subjectPlate" :key="item.Id">
                  <span class="flex1 Knowle-span1">{{ item.Name }}</span>
                  <span class="flex1">{{ item.Rank }}</span>
                  <span class="cur">
                    <span class="total-item">共{{ item.TotalItemCount }}道错题</span>
                    <span class="color" @click="handleWrongSub(item.Id, item.Name)">{{ item.RevisedCount }}道待订正</span>
                    <img src="@/assets/student/trunRight.png" alt="" />
                  </span>
                </p>
              </div>
            </happy-scroll>
            <!-- 待订正错题 -->
            <a-modal :title="plateName" centered width="720" class="wrong-con" v-model="visible" :footer="null">
              <div>
                <span class="cur" :class="{ 'bor-down': this.type === 0 }" @click="switchWrong(0)">待订正错题</span>
                <span class="cur" :class="{ 'bor-down': this.type === 1 }" @click="switchWrong(1)">已订正错题</span>
                <!-- 待订正错题 错题扫除-->
                <ul class="clearfix wrong-list" v-if="this.type === 0">
                  <li class="fl" v-for="item in wrongBankList" :key="item.PaperId">
                    <div>
                      <p>{{ item.ChapterName }}</p>
                      <p class="title-width">{{ item.PaperTitle }}</p>
                      <p>
                        共收纳<span>{{ item.WrongCount }}</span>道错题
                        <!-- <span v-if="item.PaperType === 1">区</span> -->
                        <!-- <span v-if="item.PaperType === 2">班</span> -->
                      </p>
                      <p style="font-size: 10px">{{ item.CreateTime }}</p>
                    </div>
                    <p class="review" style="background-color: #68bb97" @click="toCleaningWrong(item.PaperId)">
                      开始订正
                    </p>
                  </li>
                </ul>
                <!-- 已订正 错题收纳 -->
                <ul class="clearfix wrong-list" v-if="this.type === 1">
                  <li class="fl" v-for="item in wrongBankList" :key="item.PaperId">
                    <div>
                      <p>{{ item.ChapterName }}</p>
                      <p class="title-width">{{ item.PaperTitle }}</p>
                      <p>
                        共收纳<span>{{ item.WrongCount }}</span>道错题
                        <!-- <span v-if="item.PaperType === 1">区</span> -->
                        <!-- <span v-if="item.PaperType === 2">班</span> -->
                      </p>
                      <p style="font-size: 10px">{{ item.CreateTime }}</p>
                    </div>
                    <p class="review" @click="toReviewWrong(item.PaperId)">复习回顾</p>
                  </li>
                </ul>
              </div>
              <!-- 缺省 -->
              <div v-if="wrongLack" class="wrong-lack">
                <img src="@/assets/lack/暂无订正错题.png" alt="" />
                <p>暂无错题内容</p>
              </div>
            </a-modal>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { HappyScroll } from 'vue-happy-scroll'
import echarts from 'echarts'
export default {
  components: {
    HappyScroll
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.weeklyId = this.$route.query.weeklyId
    this.IsNoVip = localStorage.getItem('IsVip')
    if (this.IsNoVip === '1') {
      this.getWeeklyChart()
    }
  },
  mounted() {
    this.init()
  },
  watch: {
    $route() {
      this.weeklyId = this.$route.query.weeklyId
      const ChangId = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Student/StudentWeekly/StudyWeekly?' + ChangId) {
        this.getWeeklyChart()
      }
    }
  },
  data() {
    return {
      // vip
      IsNoVip: '0',
      // 缺省
      weeklyLack: false,
      // 错题缺省
      wrongLack: false,
      // 周报id
      weeklyId: '',
      userId: '',
      weekDetails: {},
      // 学情分析
      LearningList: [],
      // 知识点掌握
      KnowledgeList: [],
      // 题型板块分析
      subjectPlate: [],
      // 待订正
      visible: false,
      // 待订正试卷
      wrongBankList: [],
      // 待订正0 / 已订正1
      type: 0,
      // 题型板块id
      plateId: '',
      // 题型板块名称
      plateName: '数与运算'
    }
  },
  methods: {
    init() {
      const specificChart = echarts.init(this.$refs.specificChart)
      specificChart.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            // console.log(params)
            return ''
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'line' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        dataZoom: [
          {
            start: 0, // 默认为0
            end: 30, // 默认为100
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            handleSize: 0, // 滑动条的 左右2个滑动条的大小
            height: 10, // 组件高度
            // left: '10%', // 左边的距离
            // right: '10%', // 右边的距离
            bottom: 0, // 右边的距离
            border: 'none',
            borderColor: '#fff',
            fillerColor: '#CCD0DB',
            borderRadius: 2,
            backgroundColor: '#ededed', // 两边未选中的滑动条区域的颜色
            showDataShadow: false, // 是否显示数据阴影 默认auto
            showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
            realtime: true, // 是否实时更新
            filterMode: 'filter'
          }
        ],
        grid: {
          x: 50,
          y: 20,
          x2: 10,
          y2: 18,
          bottom: 30,
          borderWidth: 10
        },
        color: ['#479AEB', '#F87175'],
        legend: {
          data: [
            { name: '参考值', icon: 'roundRect' },
            { name: '我', icon: 'roundRect' }
          ]
        },
        xAxis: {
          type: 'category',
          data: ['加载中', '加载中', '加载中', '加载中', '加载中']
        },
        yAxis: {
          type: 'value',
          interval: 10,
          // 网格线
          splitLine: {
            show: true
          },
          axisLabel: {
            formatter: function(value) {
              // console.log(value)
              const texts = []
              if (value === 10) {
                texts.push('须努力')
              } else if (value === 50) {
                texts.push('合格')
              } else if (value === 70) {
                texts.push('良好')
              } else if (value === 90) {
                texts.push('优秀')
              }
              return texts
            },
            color: function(value) {
              // console.log('颜色---', value)
              if (value === '10') {
                return '#F87175'
              } else if (value === '50') {
                return '#68BB97'
              } else if (value === '70') {
                return '#32C5FF'
              } else if (value === '90') {
                return '#F7B500'
              } else {
                return '#000'
              }
            }
            // color: '#32C5FF'
          },
          axisLine: {
            lineStyle: {
              width: 3,
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#F7B500' // 0% 处的颜色
                  },
                  {
                    offset: 0.3,
                    color: '#32C5FF' //
                  },
                  {
                    offset: 0.5,
                    color: '#68BB97'
                  },
                  {
                    offset: 1,
                    color: 'red'
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          // Y 轴长出的线条
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '参考值',
            data: [80, 92, 90, 93, 29, 33, 32],
            type: 'line',
            itemStyle: {
              color: '#479AEB'
            }
          },
          {
            name: '我',
            data: [52, 73, 30, 84, 59, 63, 92],
            type: 'line',
            itemStyle: {
              color: '#F87175'
            }
          }
        ]
      })
      window.addEventListener('resize', function() {
        specificChart.resize()
      })
    },
    // 周报折线图
    getWeeklyChart() {
      this.$uwonhttp.post('/Weekly/StudentWeekly/GetStudentClassAccuracy', { userId: this.userId }).then(res => {
        if (res.data.Data.length === 0) {
          this.weeklyLack = true
        }
        const brokenLine = res.data.Data
        const nameX = brokenLine.map(value => {
          return value.WeeklyTime
        })
        const classLine = brokenLine.map(value => {
          return value.ClassAccuracy
        })
        const myLine = brokenLine.map(value => {
          return value.StudentAccuracy
        })
        const specificChart = echarts.init(this.$refs.specificChart)
        specificChart.setOption({
          xAxis: {
            type: 'category',
            data: nameX
          },
          series: [
            {
              name: '参考值',
              type: 'line',
              data: classLine
            },
            {
              name: '我',
              type: 'line',
              data: myLine
            }
          ]
        })
        this.getStudyWeekly()
        this.getSubjectPlate()
      })
    },
    // 学习周报
    getStudyWeekly() {
      this.$uwonhttp
        .post('/Weekly/StudentWeekly/GetStudentWeeklyInfo', { userId: this.userId, Id: this.weeklyId })
        .then(res => {
          this.weekDetails = res.data.Data
          this.LearningList = res.data.Data.StudyAnalysis
          this.KnowledgeList = res.data.Data.ChapterAnalysis
        })
    },
    // 题型板块分析
    getSubjectPlate() {
      this.$uwonhttp.post('/Weekly/StudentWeekly/GetPlateList', { userId: this.userId }).then(res => {
        this.subjectPlate = res.data.Data
      })
    },
    // 处理错题订正
    handleWrongSub(plateId, name) {
      this.plateName = name
      this.visible = true
      this.plateId = plateId
      this.getWrongInfor()
    },
    // 获取错题订正内容
    getWrongInfor() {
      this.$uwonhttp
        .post('/Weekly/StudentWeekly/GetWeeklyWrongQuestions', {
          userId: this.userId,
          plateTypeId: this.plateId,
          beginTime: this.weekDetails.BeginTime,
          endTime: this.weekDetails.EndTime,
          type: this.type
        })
        .then(res => {
          if (res.data.Data.length === 0) {
            this.wrongLack = true
            this.wrongBankList = res.data.Data
          } else {
            this.wrongLack = false
            this.wrongBankList = res.data.Data
          }
        })
    },
    // 切换待订正/已订正
    switchWrong(id) {
      this.type = id
      this.getWrongInfor()
    },
    // 开始订正
    toCleaningWrong(id) {
      this.visible = false
      this.$router.push({
        path: '/Student/Exam_Paper/cleaningWrongQuestion',
        query: {
          paperId: id,
          dataId: 1
        }
      })
    },
    // 复习回顾
    toReviewWrong(id) {
      this.$router.push({
        path: '/Student/Exam_Paper/onlyWrong',
        query: {
          paperId: id,
          dataId: 2
        }
      })
    },
    // 历史周报
    toHisroyWeekly() {
      this.$router.push({ path: '/Student/StudentWeekly/HistroyWeekly' })
    },
    // 去学习（微课视频）
    toStudy(id) {
      this.$router.push({
        path: '/Student/Fine_micro/BoutiqueMicro',
        query: {
          chapterId: id
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@color: #ff0ab7ef;
.m-r {
  margin-right: 24px;
}
.study {
  font-size: 14px;
}
.f-s {
  font-size: 16px;
  color: #000;
}
.bor-down {
  margin: 0 20px;
  border-bottom: 2px solid #0ab7ef;
}
.complete-sub {
  color: red;
}
// 本周报 时间
.week-time {
  margin-top: 2px;
}
// 缺省
.week-lack {
  // text-align: center;
  color: #ccc;
  position: absolute;
  top: 37%;
  left: 45%;
}
// 学科能力分析
.Knowledge-mr {
  margin-right: 103px;
}
.Knowle-p {
  margin-bottom: 25px;
}
.Knowle-bor {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ddd;
}
.title-mb {
  margin-bottom: 40px;
  font-size: 14px;
}
.color {
  color: #14baf0;
}
.happy-scroll {
  height: 84%;
}
// 滚动条
/deep/.happy-scroll-content {
  width: 97%;
}
// flex
.flex {
  display: flex;
}
.flex1 {
  flex: 1;
}
// 具体的右侧块
.block-m-r {
  margin-right: 60px;
}
// 历史周报
.his-bank:hover {
  color: #65b391;
}
// 标题颜色
.title-col {
  margin-bottom: 16px;
  font-size: 16px;
  color: #14baf0;
}
// 目录
.micro-catalog {
  margin-bottom: 24px;
  padding: 12px;
  background-color: #fff;
  border-radius: 5px;
}
// 本周情况
.week-situation {
  padding-top: 32px;
  padding-left: 16px;
  padding-bottom: 60px;
  background-color: #fff;
  border-radius: 5px;
}
// 练习情况
.practice-sit {
  margin-bottom: 40px;
  span:nth-child(odd) {
    margin-right: 24px;
    font-size: 16px;
  }
  span:nth-child(even) {
    margin-right: 121px;
  }
}
// 学情分析
.study-ana {
  margin-bottom: 40px;
}
// 学科分析
.subject-info {
  width: 48%;
  margin-right: 25px;
  .Knowle-span1 {
    margin-right: 98px;
  }
}
// 具体四块
.sub-wid {
  width: 100%;
  height: 183px;
  margin-right: 25px;
  padding: 20px 13px 22px 13px;
  background-color: #f1f5f6;
  border-radius: 3px;
}
// 总错题数
.total-item {
  margin-right: 3px;
}
.specific-block {
  // display: flex;
  width: 48%;
  height: 183px;
  margin-right: 25px;
  padding: 20px 13px 24px 13px;
  background-color: #f1f5f6;
  border-radius: 3px;
  .first-p {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #d6dcde;
  }
  .first1-p {
    margin-bottom: 25px;
    // padding-bottom: 5px;
    // border-bottom: 1px solid #D6DCDE;
  }
  p:nth-child(even) {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #d6dcde;
    // span:nth-child(1) {
    // margin-right: 46px;
    // }
    // span:nth-child(2) {
    // margin-right: 80px;
    // }
  }
  p:nth-child(3) {
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #d6dcde;
    // span:nth-child(1) {
    // margin-right: 46px;
    // }
    // span:nth-child(2) {
    // margin-right: 80px;
    // }
  }
}
.specific-block-chart {
  width: 48%;
  height: 183px;
  margin-right: 25px;
  padding: 20px 13px 24px 13px;
  background-color: #f1f5f6;
  border-radius: 3px;
}
// 错题内容
.wrong-con {
  width: 720px;
}
.wrong-list {
  li {
    min-width: 235px;
    // width: 235px;
    // height: 186px;
    min-height: 186px;
    margin: 18px 0px 20px 20px;
    border-radius: 5px;
    background-color: #f2f2f2;
    // 错题描述
    div {
      padding: 15px 0 0 38px;
      p:nth-child(1) {
        font-size: 13px;
        margin-bottom: 13px;
        color: grey;
      }
      p:nth-child(2) {
        margin-bottom: 5px;
        color: #000;
      }
      p:nth-child(3) {
        margin-bottom: 5px;
        color: #000;
        span:nth-child(1) {
          font-size: 19px;
          color: red;
        }
        span:nth-child(2) {
          display: inline-block;
          width: 24px;
          height: 24px;
          line-height: 24px;
          margin-left: 20px;
          text-align: center;
          font-size: 13px;
          color: #fff;
          background-color: #839398;
          border-radius: 15px;
        }
      }
    }
    // 复习按钮
    .review {
      width: 155px;
      height: 34px;
      line-height: 34px;
      margin: 0 auto;
      margin-top: 10px;
      text-align: center;
      color: #fff;
      border-radius: 20px;
      background-color: #bdd3ce;
      cursor: pointer;
    }
  }
}
// 错题缺省
.wrong-lack {
  margin-top: 20px;
  text-align: center;
  color: #ccc;
}
</style>
