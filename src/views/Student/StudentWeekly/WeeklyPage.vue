<template>
  <div >
    <div class="Weekly_page" v-loading="loading">
      <div class="page_left">
        <div class="charts_header" ref="myCharts"></div>
        <div class="charts_footer">
          <span class="font_size22 ipad_font_size24">{{ weekly.AccuracyDescribe }}</span>
          <br />
          <span class="font_size22 ipad_font_size24">“个人之最”</span>
          <div class="item_Practice" v-for="(item, index) in weekly.MaxPractice" :key="index">
            <span class="font_size20 ipad_font_size22">{{ item }}</span>
          </div>
        </div>
      </div>
      <div class="page_right">
        <div class="right_header">
          <el-tooltip content="点击查看历史周报" placement="top" effect="light">
            <img @click="goHisroyWeekly" src="@/assets/student/zhoubao.png" alt="" />
          </el-tooltip>
          <div class="right_content">
            <div class="card_Frist">
              <span class="font_size20 ipad_font_size22">学习情况</span>
              <br />
              <span class="font_size18 ipad_font_size20">共完成<span style="color: red">{{ weekly.StudyInfo }}</span
                >份</span
              >
            </div>
            <div class="card_second">
              <span class="font_size20 ipad_font_size22">周评价</span>
              <br />
              <span class="font_size18 ipad_font_size20">{{ weekly.AccuracyLevelWord }}</span>
            </div>
            <div class="card_third">
              <span class="font_size20 ipad_font_size22">学习时长</span>
              <br />
              <span class="font_size18 ipad_font_size20">{{ weekly.StudyTime }}</span>
            </div>
            <div class="card_fourth">
              <span class="font_size20 ipad_font_size22">练习速度</span>
              <br />
              <span class="font_size18 ipad_font_size20">{{ weekly.AvgSpeed }}</span>
            </div>
            <div class="card_fifth">
              <span class="font_size20 ipad_font_size22">必须努力练习</span>
              <br />
              <span class="font_size18 ipad_font_size20">{{ weekly.AccuracyLowAvg }}</span>
            </div>
            <div class="card_sixth">
              <span class="font_size20 ipad_font_size22">错误未订正练习</span>
              <br />
              <span class="font_size18 ipad_font_size20">{{ weekly.WrongNotRevised }}</span>
            </div>
          </div>
        </div>
        <div class="right_footer">
          <div class="line_charts" ref="lineCharts"></div>
        </div>
      </div>
    </div>
    <div class="next_page" @click="GoPage" style="background:#fff;border-radius:5px;width:100%;margin:10px auto;text-align:right;padding-right:20px;">
      <img src="@/assets/student/nextPage.png" alt="" style="transform:rotate(180deg);margin-right:5px;"/>
      <span class="font_size16 ipad_font_size18">上一页</span>
    </div>
  </div>
</template>

<script>
import eventBus from '@/components/eventBus/eventBus'
import echarts from 'echarts'
import 'echarts-liquidfill'

export default {
  created() {
    // this.userId = localStorage.getItem('UserId')
    // this.GetPlateListV2()
    this.initPage()
  },
  data() {
    return {
      userId: '',
      ItemId: '',
      weekly: {},
      loading: false,
    }
  },
  mounted() {
    // eventBus.$on('ItemId', (ItemId) => {
    //   this.ItemId = ItemId
    // })
  },
  watch: {
    $route() {
      if (this.$route.fullPath === '/Student/StudentWeekly/WeeklyPage') {
        // this.GetPlateListV2()
        this.initPage()
      }
    },
  },
  components: {},
  methods: {
    initPage() {
      this.userId = localStorage.getItem('UserId')
      this.ItemId = this.$route.query.id || ''
      this.GetPlateListV2()
    },
    //查询接口
    async GetPlateListV2() {
      this.loading = true
      let params = {
        userId: this.userId,
        Id: this.ItemId,
      }
      const res = await this.$uwonhttp.post('/Weekly/StudentWeekly/GetPlateListV2', params)
      if (res.status == 200) {
        this.loading = false
        this.weekly = res.data.Data
        const AvgAccuracy = res.data.Data.AvgAccuracy
        const WeekPractices = res.data.Data.WeekPractices
        await this.init(AvgAccuracy)
        await this.myChartsLine(WeekPractices)
      } else {
        this.$message.error('请求失败')
      }
      this.ItemId = ''
    },
    //水晶图
    init(AvgAccuracy) {
      const value = AvgAccuracy
      const data = [value, value, value]
      const myChart = echarts.init(this.$refs.myCharts)
      myChart.setOption({
        title: {
          text: (value * 1).toFixed(1) + '{a|%}',
          textStyle: {
            fontSize: 40,
            fontWeight: 'normal',
            color: '#81AEFF',
            rich: {
              a: {
                fontSize: 28,
              },
            },
          },
          x: 'center',
          y: '35%',
        },
        graphic: [
          {
            type: 'group',
            left: 'center',
            top: '60%',
            children: [
              {
                type: 'text',
                z: 100,
                left: '10',
                top: 'middle',
                style: {
                  fill: '#81AEFF',
                  text: '平均正确率',
                  font: '20px Microsoft YaHei',
                },
              },
            ],
          },
        ],
        series: [
          {
            type: 'liquidFill',
            radius: '85%',
            center: ['50%', '50%'],
            data: data,
            backgroundStyle: {
              color: {
                type: 'linear',
                colorStops: [],
                globalCoord: false,
              },
            },
            outline: {
              borderDistance: 0,
              itemStyle: {
                borderWidth: 10,
                borderColor: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#FFFFFF',
                    },
                    {
                      offset: 0.5,
                      color: '#F2F2F2',
                    },
                    {
                      offset: 1,
                      color: '#81AEFF',
                    },
                  ],
                  globalCoord: false,
                },
                shadowBlur: 10,
                shadowColor: '#FFFFFF',
              },
            },
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 1,
                  color: '#C6D807',
                },
                {
                  offset: 0.5,
                  color: '#C6D807',
                },
                {
                  offset: 0,
                  color: '#C6D807',
                },
              ],
              globalCoord: false,
            },
            label: {
              normal: {
                formatter: '',
              },
            },
          },
        ],
      })
    },
    //折线图
    myChartsLine(WeekPractices) {
      const Data = WeekPractices
      const PreStudent = Data.map((val) => {
        return val.PreStudent
      })
      const PreClass = Data.map((val) => {
        return val.PreClass
      })
      const Title = Data.map((val) => {
        return val.Title
      })
      const LineCharts = echarts.init(this.$refs.lineCharts)
      LineCharts.setOption({
        title: {
          text: '我的本周练习',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: {
          trigger: 'axis',
        },
        color: ['#81AEFF', '#B3C3BC'],

        legend: {
          data: ['自己', '班平均'],
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          show: false,
          type: 'category',
          data: Title,
          boundaryGap: false,
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '自己',
            type: 'line',
            data: PreStudent,
            symbolSize: 14, //设定实心点的大小
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 5, // 0.1的线条是非常细的了
                },
              },
            },
          },
          {
            name: '班平均',
            type: 'line',
            data: PreClass,
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 2, // 0.1的线条是非常细的了
                },
              },
            },
          },
        ],
      })
    },
    //跳转历史周报
    goHisroyWeekly() {
      this.$router.push({ path: '/Student/StudentWeekly/HistroyWeekly' })
    },
    // //回上一页
    GoPage() {
      this.$router.go(-1)
    }
  },
}
</script>

<style lang="less" scoped>
.Weekly_page {
  width: 100%;
  height: 664px;
  // position: relative;
  display: flex;
  justify-content: space-between;
  left: 3%;
  .page_left {
    width: 35%;
    border-radius: 5px;
    background: #ffff;
    .charts_header {
      width: 100%;
      height: 50%;
    }
  }
  .charts_footer {
    text-align: center;
    padding: 20px;
    line-height: 40px;
    span:nth-of-type(1) {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #65b391;
    }
    span:nth-of-type(2) {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #727272;
    }
    .item_Practice {
      span {
        font-size: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #727272;
      }
    }
  }
  .page_right {
    width: 63%;
    border-radius: 5px;
    position:relative;
    .right_header {
      width: 100%;
      height: auto;
      padding: 5px;
      border-radius: 5px;
      background: #ffff;
      img {
        width: 25px;
        height: 25px;
        float: right;
        cursor: pointer;
      }
      .right_content {
        width: 100%;
        height: auto;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        text-align: center;
        .card_Frist {
          width: 30%;
          padding: 20px;
          background: url('../../../assets/student/card1.png') no-repeat ;
          background-size:100% 100%;
        }
        .card_second {
          width: 30%;
          padding: 20px;
          background: url('../../../assets/student/card2.png') no-repeat;
          background-size:100% 100%;
        }
        .card_third {
          width: 30%;
          padding: 20px;
          background: url('../../../assets/student/card3.png') no-repeat;
          background-size:100% 100%;
        }
        .card_fourth {
          width: 30%;
          padding: 20px;
          background: url('../../../assets/student/card4.png') no-repeat;
          background-size:100% 100%;
        }
        .card_fifth {
          width: 30%;
          padding: 20px;
          background: url('../../../assets/student/card5.png') no-repeat;
          background-size:100% 100%;
        }
        .card_sixth {
          width: 30%;
          padding: 20px;
          background: url('../../../assets/student/card6.png') no-repeat;
          background-size:100% 100%;
        }
        span {
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          line-height: 45px;
        }
      }
    }

    .right_footer {
      width: 100%;
      height: auto;
      margin-top: 10px;
      background: #fff;
      padding: 5px;
      .line_charts {
        width: 100%;
        height: 350px;
      }
    }
  }
}
.next_page {
  width: 17%;
  float: right;
  cursor: pointer;
  img {
    width: 25px;
    height: 25px;
    margin-left: 10px;
  }
  span {
    font-size: 18px;
    line-height: 50px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #474725;
    vertical-align: middle;
  }
}
</style>
