<template>
  <div>
    <!-- <el-input v-model="money" type="textarea" placeholder="请输入内容"></el-input> -->
    <div class="keyboard">
      <div class="keyboard_key">
        <!-- <div class="keyboard_character" @click="_chraClick">
          <button type="button" class="pad-num1 specialkey" data-num="!">!</button>
          <button type="button" class="pad-num1 specialkey" data-num="@">@</button>
          <button type="button" class="pad-num1 specialkey" data-num="#">#</button>
          <button type="button" class="pad-num1 specialkey" data-num="$">$</button>
          <button type="button" class="pad-num1 specialkey" data-num="%">%</button>
          <button type="button" class="pad-num1 specialkey" data-num="^">^</button>
          <button
            type="button"
            class="pad-num1 specialkey"
            data-num="&'>&</button>
            <button type=&quot;button&quot; class=&quot;pad-num1 specialkey&quot; data-num="
            *'>*</button>
          <button type="button" class="pad-num1 specialkey" data-num="(">(</button>
          <button type="button" class="pad-num1 specialkey" data-num=")">)</button>
          <button type="button" class="pad-num1 specialkey" data-num="{">{</button>
          <button type="button" class="pad-num1 specialkey" data-num="}">}</button>
          <button type="button" class="pad-num1 specialkey" data-num=":">:</button>
          <button type="button" class="pad-num1 specialkey" data-num="&quot;">"</button>
          <button type="button" class="pad-num1 specialkey" data-num=",">,</button>
          <button type="button" class="pad-num1 specialkey" data-num="/">/</button>
        </div>

        <div class="centerdiv"></div>

        <div class="keyboard_Letter">
          <div class="group1">
            <button v-for="(item,index) in keylist" :key="index" type="button" class="pad-num1" @click="upClick1(item)">{{ item }}</button>
          </div>

          <div class="group2">
            <button v-for="(item,index) in keylist2" :key="index" type="button" class="pad-num1" @click="upClick2(item)">{{ item }}</button>
          </div>

          <div class="group3">
            <button v-for="(item,index) in keylist3" :key="index" type="button" class="pad-num1" @click="upClick3(item)" >{{ item }}</button>
          </div>
          <div class="group4" @click="_letterClick">
            <button type="button" class="pad-num1 specialkey" data-num="delete">delete</button>
            <button type="button" class="pad-num2 specialkey" data-num="space">space</button>
            <button type="button" class="pad-num1 specialkey" data-num="enter">
              <i class="fa fa-mail-reply" data-num="enter"></i>
            </button>
          </div>

        </div> -->

        <div class="centerdiv"></div>

        <div class="keyboard_number" @click="_handleKeyPress">
          <div class="pos-right-pad-num">
            <button type="button" class="pad-num specialkey" data-num="÷">÷</button>
            <button type="button" class="pad-num specialkey" data-num="8">8</button>
            <button type="button" class="pad-num specialkey" data-num="9">9</button>
            <button type="button" class="pad-num specialkey" data-num="4">4</button>
            <button type="button" class="pad-num specialkey" data-num="5">5</button>
            <button type="button" class="pad-num specialkey" data-num="6">6</button>
            <button type="button" class="pad-num specialkey" data-num="1">1</button>
            <button type="button" class="pad-num specialkey" data-num="2">2</button>
            <button type="button" class="pad-num specialkey" data-num="3">3</button>
            <button type="button" class="pad-num specialkey" data-num="0">0</button>
            <button type="button" class="pad-num specialkey" data-num=".">.</button>
            <button type="button" class="pad-num specialkey" data-num="D">X</button>
          </div>
          <div class="pos-right-pad-act">
            <button type="button" class="pad-num specialkey border-right" style="width:100%" data-num="+">+</button>
            <button type="button" class="pad-num specialkey border-right" style="width:100%" data-num="-">-<br></button>
            <button type="button" class="pad-num2 specialkey border-right" data-num="enter"><i class="fa fa-mail-reply" aria-hidden="true"></i></button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      money: '',
      keylist: [],
      keylist2: [],
      keylist3: [],
      bigkeys1: [],
      bigkeys2: [],
      bigkeys3: [],
      smallkeys1: [],
      smallkeys2: [],
      smallkeys3: [],
      selected: false
    }
  },
  created() {
    this.readle()
  },
  methods: {
    readle() {
      const smallkey1 = ['q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p']
      const bigkey1 = ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P']
      const smallkey2 = ['a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l']
      const bigkey2 = ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L']
      const smallkey3 = ['shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', '<', '>']
      const bigkey3 = ['SHIFT', 'Z', 'X', 'C', 'V', 'B', 'N', 'M', '<', '>']
      this.keylist = smallkey1
      this.keylist2 = smallkey2
      this.keylist3 = smallkey3
      this.bigkeys1 = bigkey1
      this.bigkeys2 = bigkey2
      this.bigkeys3 = bigkey3
      this.smallkeys1 = smallkey1
      this.smallkeys2 = smallkey2
      this.smallkeys3 = smallkey3
    },
    _chraClick(e) {
      const num = e.target.dataset.num
      this.money += num
      this.$emit('updatekey', this.money)
    },
    _handleKeyPress(e) {
      const num = e.target.dataset.num
      switch (String(num)) {
        case 'D':
          this.deletekey()
          break
        case 'enter':
          this.money += '\n'
          break
        default:
          this.Addnum(num)
          break
      }
      this.$emit('updatekey', this.money)
    },
    deletekey() {
      const values = this.money
      if (!values.length) {
        return false
      } else {
        this.money = values.substring(0, values.length - 1)
        this.$emit('updatekey', this.money)
      }
    },
    Addnum(num) {
      const value = this.money
      this.money = value + num
    },
    upClick1(key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    upClick2(key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    upClick3(key) {
      switch (String(key)) {
        case 'shift':
          this.keylist = this.bigkeys1
          this.keylist2 = this.bigkeys2
          this.keylist3 = this.bigkeys3
          break
        case 'SHIFT':
          this.keylist = this.smallkeys1
          this.keylist2 = this.smallkeys2
          this.keylist3 = this.smallkeys3
          break
        default:
          this.money += key
          break
      }
      this.$emit('updatekey', this.money)
    },
    _letterClick(e) {
      const num = e.target.dataset.num
      switch (String(num)) {
        case 'delete':
          this.deletekey()
          break
        case 'space':
          this.money += ' '
          break
        case 'enter':
          this.money += '\n'
          break
      }
    }
  }
}
</script>
<style lang="less" scoped>
.keyboard {
  width: 100%;
  height: 215px;
  background: #000;
  opacity: 0.7;
  overflow: hidden;
}
.keyboard_key {
  padding-top: 10px;
  width: 95%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
button {
  background-color: aliceblue;
}
// ----数字键盘-----
.keyboard_number {
  display: flex;
  width: 20%;
  margin: 0;
  padding: 0;
  vertical-align: bottom;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}
.pos-right-pad-num {
  display: flex;
  width: 75%;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}
.pos-right-pad-act {
  width: 25%;
}
.pad-num {
  margin: 0 !important;
  width: 33.3333333%;
  height: 48px;
  border: 0;
  border: 1px solid #000000;
  font-size: 15px;
  cursor: pointer;
  &:hover {
    position: relative;
    cursor: pointer;
  }
  &:active {
    top: 1px;
    left: 1px;
    background-color: #201a1a;
  }
}
.pad-num2 {
  margin: 0 !important;
  width: 100%;
  height: 96px;
  border: 0;
  border: 1px solid #000000;
  font-size: 15px;
  cursor: pointer;
  &:hover {
    position: relative;
    cursor: pointer;
  }
  &:active {
    top: 1px;
    left: 1px;
    background-color: #201a1a;
  }
}

// 字符键盘
.keyboard_character {
  width: 20%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 5%;
  .pad-num1 {
    width: 25%;
    height: 48px;
    border: 1px solid #000000;
    font-size: 15px;
    cursor: pointer;
    &:hover {
      position: relative;
      cursor: pointer;
    }
    &:active {
      top: 1px;
      left: 1px;
      background-color: #201a1a;
    }
  }
}

//字母键盘
.keyboard_Letter {
  width: 40%;
  margin: 0;
  padding: 0;
  .group1 {
    display: flex;
    flex-direction: row;
    .pad-num1 {
      width: 10%;
      height: 48px;
      border: 1px solid #000000;
      font-size: 15px;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
  .group2 {
    display: flex;
    flex-direction: row;
    width: 100%;
    margin-left: 26px;
    .pad-num1 {
      width: 10%;
      height: 48px;
      border: 1px solid #000000;
      font-size: 15px;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }

  .group3 {
    display: flex;
    flex-direction: row;
    .pad-num1 {
      width: 10%;
      height: 48px;
      border: 1px solid #000000;
      font-size: 16px;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
  .group4 {
    display: flex;
    flex-direction: row;
    .pad-num1 {
      width: 15%;
      height: 48px;
      font-size: 16px;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
    .pad-num2 {
      width: 70%;
      height: 48px;
      border: 1px solid #000000;
      font-size: 16px;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
}
.centerdiv {
  border-right: 2px solid rgb(243, 239, 239);
  padding-bottom: 1800px; /*关键*/
  margin-bottom: -1800px; /*关键*/
  padding-top: 1800px; /*关键*/
  margin-top: -1800px; /*关键*/
}
.input-box {
  font-size: 35px;
  font-weight: bold;
  height: 40px;
  border-bottom: 1px solid #aaa;
  padding: 10px 15px;
  text-align: right;
  width: 90%;
}
</style>
