<template>
  <div>
    <p class="news-center">消息中心</p>
    <div class="newsData" id="newsData">
      <span @click="wholeRead" class="fg mr cur all-read">全部标记为已读</span>
      <a-tabs default-active-key="2" @change="callback">
        <a-tab-pane key="1" tab="消息">
          <!-- <span class="tick" ><a-badge status="error" /></span> -->
          <!-- <span v-if="allNotice" class="system" ><a-badge status="error" /></span> -->
          <!--  -->
          <div v-if="lackIng" class="lack-sty">
            <img src="@/assets/lack/暂无搜索记录.png" alt="">
            <p>暂无作业列表</p>
          </div>
          <div v-for="(item,index) in newsList" :key="index" class="news-notice">
            <div class="clearfix">
              <span v-if="item.IsRead === 0">
                <a-badge status="error" /></span>
              <span style="color: #000;font-weight: 700;margin-right: 8px;">{{ userName }}</span>
              <span>{{ item.Content }}</span>
              <p class="fg">
                <span v-if="item.Type === 0 && item.Type === 0 && item.FriendType !== 1" class="operation gruy cur">忽略</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <span @click="adoptFriend(item.CreatorId,item.Id)" v-if="item.Type === 0 && item.FriendType !== 1" class="operation sel cur">通过</span>
                <span v-if="item.FriendType === 1 && item.Type === 0" class="operation gruy fg">已通过</span>
                <span v-if="item.Type !== 0 && item.Type !== 1" class="operation sel cur">查看详情</span>
              </p>
            </div>
            <p class="col">{{ item.CreateTime }}</p>
          </div>
        </a-tab-pane>
        <!-- force-render 隐藏是否展示DOM结构-->
        <a-tab-pane key="2" tab="系统推送" force-render>
          <!-- <span class="tick"><a-badge status="error" /></span> -->
          <!-- 系统推送 -->
          <div v-for="(item,index) in SystemList" :key="index" class="system-push">
            <span v-if="item.IsRead === 0" class="system">
              <a-badge status="error" /></span>
            <p> {{ item.Content }}<span @click="toDetails(item.KeyId,item.ClassId,item.ChapterNum)" class="operation sel fg cur">查看详情</span></p>
            <p class="col">{{ item.CreateTime }}</p>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <!-- 分页 -->
    <div class="paging">
      <a-pagination show-quick-jumper :default-current="1" :total="TotalItems" @change="onPaging" hideOnSinglePage :defaultPageSize="15" />
    </div>
    <!-- <p class="footer">Copyright©上海有我科技有限公司，All Rights Reserved</p> -->
    <!-- <footerLogo :heightPage="heightPage"></footerLogo> -->
  </div>
</template>

<script>
import footerLogo from '@/components/newFooterLogo/newFooterLogo'
export default {
  components: {
    footerLogo
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.getNewsList()
    // 系统
    this.getSystemList()
    this.getTeacherInfo()
  },
  updated() {
    this.heightPage = document.getElementById('newsData').offsetHeight
  },
  data() {
    return {
      heightPage: 0,
      userId: '',
      userName: '',
      // 消息通知数据
      newsList: [],
      // 总体通知
      allNotice: '',
      // 系统推送数据
      SystemList: [],
      pageNo: 1,
      TotalItems: 1,
      totalNews: 1,
      totlalSystem: 1,
      // tab切换需要的id
      switchKey: '2',
      // 缺省
      lackIng: false
    }
  },
  methods: {
    callback(key) {
      this.switchKey = key
      this.pageNo = 1
      if (key === '1') {
        this.TotalItems = this.totalNews
      } else {
        this.TotalItems = this.totlalSystem
      }
      this.getNewsList()
    },
    // 获取消息通知  0 未读  1 已读 Type 0-添加好友通知 1-好友通过通知 2-学生提问通知 3-微课审核通知 4-关注通知 5-学生二次修订完成通知 6关注的老师的微课上线
    // FriendType
    getNewsList() {
      this.$uwonhttp
        .post('/PersonalController/Personal/GetStudentPushMsg', {
          userId: this.userId,
          pageNo: this.pageNo,
          pageSize: 10,
          type: 0
        })
        .then(res => {
          if (res.data.Data.Items.length === 0) {
            this.lackIng = true
          } else {
            this.lackIng = false
            this.newsList = res.data.Data.Items
            this.totalNews = res.Data.TotalItems
            this.allNotice = res.Data.Items.some(item => {
              return item.Type === 0
            })
          }
        })
    },
    // 获取系统推送
    getSystemList() {
      this.$uwonhttp
        .post('/PersonalController/Personal/GetStudentPushMsg', {
          userId: this.userId,
          pageNo: this.pageNo,
          pageSize: 10,
          type: 1
        })
        .then(res => {
          this.SystemList = res.Data.Items
          this.totalNum = res.Data.TotalItems
          this.totlalSystem = res.Data.TotalItems
          this.TotalItems = res.Data.TotalItems
        })
    },
    // 通过好友     1  同意
    adoptFriend(friendId, newsId) {
      this.$http
        .post('/Friend/FriendRelationShip/UpdateFriend', {
          userId: this.userId,
          friendsId: friendId,
          deleted: 1,
          informationId: newsId
        })
        .then(res => {
          this.getNewsList()
        })
    },
    // 全部已读
    wholeRead() {
      this.$http.post('/Information/TeacherInformation/UpdateRead', { userId: this.userId, id: null }).then(res => {
        this.getNewsList()
        this.getSystemList()
      })
    },
    // 系统查看详情
    toDetails(id, classId, chapterNum) {
      this.$router.push({
        path: '/Teacher/My_Task/InspectionWorkData',
        query: {
          paperId: id,
          classId,
          chapterNum
        }
      })
    },
    // 获取教师信息
    getTeacherInfo() {
      this.$uwonhttp
        .post('/ManagerPersonalsController/ManagerPersonals/GetTeacherInfo', { userId: this.userId })
        .then(res => {
          this.userName = res.data.Data.TrueName
        })
    },
    onPaging(pageNum) {
      this.pageNo = pageNum
      if (this.switchKey === '1') {
        this.getNewsList()
      } else {
        this.getSystemList()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.col {
  color: #b5b5b5;
}
.mr {
  margin-right: 30px;
}
.sel {
  background-color: #68bb97;
}
.gruy {
  background-color: #d6d6d6;
}
.lack-sty {
  margin-top: 50px;
  margin-bottom: 50px;
  text-align: center;
  color: #ccc;
}
.tick {
  position: absolute;
  top: 8px;
  left: 47px;
}
.news-center {
  font-size: 18px;
  color: #49534e;
}
.newsData {
  padding: 10px 5px 10px 30px;
  background-color: #fff;
  border-radius: 5px;
}
.all-read {
  color: #b5b5b5;
}
// 消息
.news-notice {
  border-bottom: 1px solid rgba(232, 232, 232);
  margin-top: 20px;
}
// 系统
.system {
  position: absolute;
  top: 8px;
  left: 167px;
}
// 系统推送
.system-push {
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(232, 232, 232);
}
// 操作
.operation {
  display: inline-block;
  width: 90px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 5px;
  color: #fff;
  margin-top: 14px;
}
</style>
