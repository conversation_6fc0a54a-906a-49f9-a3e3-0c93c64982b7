<template>
  <div class="personal-settings">
    <!-- 个人设置内容 -->
    <div class="personal-setting-info">
      <span style="text-align:center">
        <a-avatar :size="64" icon="user" :src="userHead" />
        <p>
          <a-upload name="file" :multiple="true" :action="uploadPictureUrl" :headers="headers" @change="handleChange">
            <a-button>
              <a-icon type="upload" />修改头像
            </a-button>
          </a-upload>
        </p>
      </span>
      <!-- 基本信息 -->
      <div class="basic-info">
        <a-form id="components-form-demo-normal-login" :form="form" class="login-form" @submit="handleSubmit">
          <a-form-item>
            <span>手机号码:</span><span>{{ userName }}</span><span class="cur" @click="handleMobilePhone" style="color: #68BB97">修改</span>
            <a-modal v-model="visible" title="修改手机号" @ok="handleOk" ok-text="保存" centered>
              <a-form-item>
                <a-input v-decorator="[
                    'oldmobilephone',
                    { rules: [{validator: mobilephoneValidate}] },
                  ]" placeholder="原手机号">
                  <a-icon slot="prefix" type="mobile" />
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-input v-decorator="[
                    'newmobilephone',
                    { rules: [{validator: mobilephoneValidate}] },
                  ]" placeholder="新手机号">
                  <a-icon slot="prefix" type="mobile" />
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-input style="width: 50%" v-decorator="[
                    'testing',
                    { rules: [] },
                  ]" placeholder="验证码">
                  <a-icon slot="prefix" type="safety-certificate" />
                </a-input>
                <span class="testing-btn" @click="countDown" :class="{disabled: !this.canClick}">{{ content }}</span>
              </a-form-item>
            </a-modal>
          </a-form-item>
          <a-form-item>
            <span>密码设置:</span><span @click="handlePassWord" class="cur" style="color: #68BB97">修改密码</span>
            <a-modal v-model="visiblePAW" title="修改密码" ok-text="确定" @ok="handleOkPassWord" centered>
              <a-form-item>
                <a-input v-decorator="[
                    'oldPassword',
                    { rules:[{ validator: passwordValidate }]}
                  ]" type="password" placeholder="输入原密码">
                </a-input>
                <p v-show="testEqually" style="textIndent: 5px;color: red;height: 25px;marginBottom: 0">{{ testContent }}</p>
              </a-form-item>
              <a-form-item>
                <a-input v-decorator="[
                    'newPassword',
                    { rules:[{ validator: passwordValidate }]}
                  ]" type="password" placeholder="输入新密码">
                </a-input>
              </a-form-item>
              <a-form-item>
                <a-input v-decorator="[
                    'againNewPassword',
                    { rules:[{ validator: passwordValidate }]}
                  ]" type="password" placeholder="再次输入密码">
                </a-input>
                <p v-show="equally" style="textIndent: 5px;color: red;">密码不一致</p>
              </a-form-item>
            </a-modal>
          </a-form-item>
          <a-form-item>
            <span>选择学生:</span>
            <a-select v-if="fristStu" class="seles" style="width: 150px" placeholder="选择学生" :default-value="fristStu" @change="handleSwitch">
              <a-select-option v-for="item in userInfo" :key="item.UserId" :value="item.UserId">{{ item.TrueName }}</a-select-option>
            </a-select>
            <span class="add-btn" @click="addStudent">增加学生</span>
            <a-modal v-model="addStudentData" title="增加学生" ok-text="确定" @ok="handleOkAdd" centered>
              <p class="m-b">
                <img class="input-img" src="@/assets/index/icon／60pt-首页备份 4.png" alt="">
                <input v-model="studentName" class="input-w " type="text" placeholder="请输入真实姓名"></p>
              <p class="m-b">
                <img class="input-img" src="@/assets/index/icon／60pt-首页备份 3.png" alt="">
                <input v-model="studyNo" class="input-w" type="text" placeholder="请输入学号（1-99）">
              </p>
              <p class="m-b">
                <img class="input-img" src="@/assets/index/icon／60pt-首页备份 3.png" alt="">
                <a-select show-search placeholder="选择学校" style="width: 230px" :filter-option="filterOption" @change="handleSchool">
                  <a-select-option v-for="item in UserTreeData" :key="item.SchoolID" :value="item.SchoolID">
                    {{ item.SchoolName }}
                  </a-select-option>
                </a-select>
              </p>
              <!-- 选择班级 -->
              <p class="m-b">
                <img class="input-img" src="@/assets/index/icon／60pt-首页备份 3.png" alt="">
                <a-select placeholder="选择班级" style="width: 230px" @change="handleClass">
                  <a-select-option v-for="item in classList" :key="item.ClassId" :value="item.ClassId">
                    {{ item.className }}
                  </a-select-option>
                </a-select>
              </p>
              <p>
                <img class="input-img" src="@/assets/index/icon／60pt-首页备份 3.png" alt="">
                <input class="input-w" type="text" v-model="teacherName" placeholder="请输入教师名称">
              </p>
            </a-modal>
          </a-form-item>
          <a-form-item>
            <a-radio-group @change="SelectOptions" v-model="sexValue">
              <a-radio :value="1">
                男
              </a-radio>
              <a-radio :value="0">
                女
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item>
            <span>所在班级: </span><span>{{ this.className }}</span>

          </a-form-item>
          <a-form-item>
            <span>所属学校:</span><span>{{ this.schoolName }}</span>
          </a-form-item>
          <a-form-item>
            <span>入学年份:</span><span>{{ this.entranceYear }}</span>
          </a-form-item>
          <a-form-item>
            <span>学号:</span><span>{{ this.studentNum }}</span>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit" class="login-form-button">
              保存个人信息
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  beforeCreate() {
    this.form = this.$form.createForm(this, { name: 'normal_login' })
  },
  created() {
    this.userName = localStorage.getItem('mobilePhone')
    this.userId = localStorage.getItem('UserId')
    this.jwtToken = localStorage.getItem('jwtToken')

    // 获取用户基本信息
    this.getUserInfor()
    this.getStudentInfor()
    // 获取教师对应班级
    this.getTeacherClass()
    // 获取账号的其他角色
    this.getAccountRole()
  },
  data() {
    return {
      fristStu: '', // 默认学生姓名
      studentName: '',
      teacherName: '',
      schoolId: '',
      addClassId: '',
      studyNo: '', // 学号
      selUserId: '', // 选择id
      classList: [], // 班级
      UserTreeData: [], // 选择学生
      addStudentData: false, // 新增学生
      userInfo: [],
      // 默认性别
      sexValue: 0,
      // 年级
      genderId: '',
      userName: '',
      // 用户头像
      userHead: '',
      // 用户信息
      userData: {},
      jwtToken: '',
      // 用户ID
      userId: '',
      trueName: '',
      // 教师性别
      sex: '',
      // 教师班级信息
      TeacherClass: [],
      // 修改手机号
      visible: false,
      // 修改密码
      visiblePAW: false,
      // 验证密码一致
      equally: false,
      // 验证原密码是否正确
      testContent: '',
      testEqually: false,
      teacherList: [],
      // 验证码
      content: '发送验证码',
      totalTime: 60,
      canClick: false, // 验证码显示
      uploadPictureUrl: '', // 修改头像地址
      headers: {
        authorization: '' // 请求头携带
      },
      schoolName: '',
      className: '',
      entranceYear: '', // 入学年份
      studentNum: '' // 学号
    }
  },
  methods: {
    // 切换学生
    handleSwitch(vlaue) {
      this.selUserId = vlaue
      window.localStorage.removeItem('UserId')
      localStorage.setItem('UserId', vlaue)
      this.$router.go(0)
    },
    // 获取班级
    getClassList(value) {
      this.$uwonhttp
        .get('/Class/ClassManager/GetClassBySchoolId', {
          params: {
            schoolId: value
          }
        })
        .then(res => {
          this.classList = res.data.Data
        })
    },
    // 获取教师名称
    getTeacherName(classId) {
      this.$uwonhttp
        .get('/User/UserManager/GetTeacherName', {
          params: {
            classId: classId
          }
        })
        .then(res => {
          this.teacherName = res.data.Data
        })
    },
    // 选择班级
    handleClass(value) {
      this.getTeacherName(value)
      this.addClassId = value
    },
    // 获取学校
    getSchoolName() {
      this.$uwonhttp.post('/Report/School/GetSchools').then(res => {
        this.UserTreeData = res.data.Data
      })
    },
    handleSchool(value) {
      this.getClassList(value)
      this.schoolId = value
    },
    filterOption(input, option) {
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    // 账号其他角色
    getAccountRole() {
      this.$uwonhttp
        .get('/User/UserManager/GetStudentListByPhone', {
          params: {
            phoneNo: this.userName
          }
        })
        .then(res => {
          this.userInfo = res.data.Data
          this.fristStu = res.data.Data[0].UserId
          if (this.fristStu === '') {
            this.fristStu = res.data.Data[0].UserId
          } else {
            const firstUserId = localStorage.getItem('UserId')
            const defalutUser = this.userInfo.findIndex(value => {
              return value.UserId === firstUserId
            })
            this.fristStu = res.data.Data[defalutUser].UserId
          }
        })
    },
    // 增加学生
    addStudent() {
      this.addStudentData = true
      this.getSchoolName()
    },
    // 增加学生框
    handleOkAdd() {
      this.$uwonhttp
        .post('/User/UserManager/BindStudent', {
          UserName: this.studentName,
          SchoolId: this.schoolId,
          ClassId: this.addClassId,
          StudyNo: this.studyNo,
          PhoneNo: this.userName
        })
        .then(res => {
          this.getAccountRole()
          this.$message.success('添加成功')
          this.addStudentData = false
        })
    },
    // 获取用户基本信息
    getUserInfor() {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.userId }).then(res => {
        console.log('css.vue')
        this.userData = res.Data.User
        this.trueName = res.Data.User.RealName
        this.userHead = res.Data.User.Photo
        this.genderId = res.Data.GradeId
        // 性别
        this.sex = res.Data.User.Sex
        this.headers = {
          authorization: `Bearer ${this.jwtToken}`
        }
      })
      this.uploadPictureUrl = `http://uwoomobile.doggod.xyz/User/UserManager/SetUserPhoto?UserId=${this.userId}`
    },
    // 获取学生个人信息
    getStudentInfor() {
      this.$uwonhttp.post('/PersonalController/Personal/GetUserInfo', { userID: this.userId }).then(res => {
        this.className = res.data.Data.className
        this.entranceYear = res.data.Data.InSchoolYear
        this.studentNum = res.data.Data.StudentNo
        this.sexValue = res.data.Data.Gender
        this.schoolName = res.data.Data.SchoolName
      })
    },
    // 修改头像
    handleChange(arg1, label, extra) {
      if (arg1.file.status === 'done') {
        if (arg1.file.response.Success) {
          this.userHead = arg1.file.response.Data
          this.$message.success('修改头像成功')
          this.$router.go(0)
        } else {
          this.$message.error(arg1.file.response.Msg, 2)
        }
      }
    },
    // 修改男女
    SelectOptions(e) {
      let gender = ''
      if (e.target.value === 1) {
        gender = '男'
      } else {
        gender = '女'
      }
      this.$uwonhttp
        .post('/User/UserManager/SetUserSex', { UserId: this.userId, TrueName: this.trueName, Gender: gender })
        .then(res => {})
    },
    // 获取教师对应班级
    getTeacherClass() {
      this.$http.post('/ClassManage/Exam_Class/GetListByUserId', { userId: this.userId }).then(res => {
        this.teacherList = res.Data
      })
    },
    // 修改手机号
    handleMobilePhone() {
      this.visible = true
    },
    // 修改手机号模态框确认
    handleOk() {
      this.visible = false
      const values = this.form.getFieldsValue()
      this.$uwonhttp
        .post('/User/UserManager/UpdatePhoneNum', {
          Userid: this.userId,
          PhoneNum: values.newmobilephone,
          Code: values.testing
        })
        .then(res => {})
    },
    // 修改密码
    handlePassWord() {
      this.visiblePAW = true
    },
    // 修改密码模态框确认
    handleOkPassWord() {
      const values = this.form.getFieldsValue()
      if (values.newPassword !== values.againNewPassword) {
        this.equally = true
        this.testContent = '密码不一致'
      } else if (values.oldPassword.length < 6) {
        return false
      } else {
        this.equally = false
        this.$uwonhttp
          .post('/User/UserManager/UpdatePwdByOld', {
            userId: this.userId,
            oldPwd: values.oldPassword,
            newPwd: values.againNewPassword
          })
          .then(res => {
            if (res.data.Success) {
              this.$message.success(res.data.Data)
              this.visiblePAW = false
            } else {
              this.testEqually = true
              this.testContent = res.data.Msg
            }
          })
      }
    },
    // 获取手机验证码
    getPhoneCount(mobilephone) {
      this.$uwonhttp
        .get('/User/UserManager/GetPhoneNumCode', {
          params: {
            phoneNum: mobilephone
          }
        })
        .then(res => {})
    },
    // 验证码 265071
    countDown() {
      if (!this.canClick) return
      this.canClick = false
      const values = this.form.getFieldsValue()

      // 获取手机验证码
      this.getPhoneCount(values.oldmobilephone)
      this.content = `${this.totalTime}s重新获取`
      const clock = window.setInterval(() => {
        this.totalTime--
        this.content = `${this.totalTime}s重新获取`
        if (this.totalTime < 0) {
          window.clearInterval(clock)
          this.content = '重新发送验证码'
          this.totalTime = 60
          this.canClick = true
        }
      }, 1000)
    },
    testMobilephone(str) {
      const regex = /^1[3456789]\d{9}$/
      if (!regex.test(str)) {
        return false
      } else {
        return true
      }
    },
    // 手机校验
    mobilephoneValidate(rule, value, callback) {
      if (!this.testMobilephone(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        // 验证码
        this.canClick = true
        callback()
      }
    },
    // 密码校验
    passwordValidate(rule, value, callback) {
      if (value === undefined || value === '') {
        this.equally = false
        this.testEqually = false
        callback(new Error('密码不能为空'))
      } else if (value.length < 6) {
        this.equally = false
        this.testEqually = false
        callback(new Error('密码不少于6位'))
      } else {
        callback()
      }
    },
    // 保存个人设置
    handleSubmit(e) {
      e.preventDefault()
      if (this.sex === 1) {
        this.sex = '男'
      } else {
        this.sex = '女'
      }
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$uwonhttp
            .post('/User/UserManager/SetUserTrueName', {
              UserId: this.userId,
              TrueName: values.mobilephone,
              Gender: this.sex
            })
            .then(res => {
              if (res.data.Success) {
                this.$message.success('修改姓名成功')
                this.$router.go(0)
              }
              this.getUserInfor()
            })
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.m-b {
  margin-bottom: 20px;
}
.input-img {
  width: 28px;
}
.m-r {
  margin-right: 30px;
}
.input-w {
  width: 260px;
  border-bottom: 1px solid #ccc;
}
.cur {
  cursor: pointer;
}
/deep/.ant-radio-inner::after {
  background-color: #68bb97;
}
.add-btn {
  display: inline-block;
  width: 100px;
  height: 35px;
  line-height: 35px;
  margin-left: 30px;
  text-align: center;
  color: #fff;
  background-color: #68bb97;
  border-radius: 8px;
  cursor: pointer;
}
// 上传预览设置
/deep/.ant-upload-list {
  display: none;
}
// 设置内容
.personal-setting-info {
  display: flex;
  width: 1108px;
  height: 500px;
  margin: 0 auto;
  margin-top: 106px;
  // background-color: #fff;
  // 表单内容
  .basic-info {
    margin: 20px 0 0 150px;
    span {
      margin-right: 30px;
    }
  }
}
/deep/.ant-input-affix-wrapper {
  margin-bottom: 15px;
  height: 45px;
}
// 验证码按钮
.testing-btn {
  display: inline-block;
  background-color: #ccc;
  height: 36px;
  padding: 0 10px;
  line-height: 36px;
  margin-left: 30px;
  cursor: pointer;
}
// 鼠标变化
.disabled {
  background-color: #d6d6d6;
  border-color: #d6d6d6;
  color: #fff;
  cursor: not-allowed; // 鼠标变化
}
</style>
