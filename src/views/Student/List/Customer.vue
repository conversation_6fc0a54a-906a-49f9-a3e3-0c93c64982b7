<template>
  <div class="customer-info font_size26 ipad_font_size28">
    <p>联系我们</p>
    <p class="clearfix bor"><span class="fl">客服QQ</span><span class="fg">{{ customerList.CustomerQQ }}</span></p>
    <p class="clearfix bor"><span class="fl">客服电话</span><span class="fg">{{ customerList.PhoneNum }}</span></p>
    <p class="clearfix bor"><span class="fl">微信公众号</span><span class="fg">{{ customerList.OfficialAccount }}</span></p>
    <div>
      <p><img class="cus-img" :src="customerList.QRCode" alt=""></p>
      <p>{{ customerList.Name }}</p>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.schoolId = localStorage.getItem('SchoolId')
    this.getSchoolId()
  },
  data() {
    return {
      schoolId: '',
      customerList: {}
    }
  },
  methods: {
    getSchoolId() {
      this.$uwonhttp.post('/Customer/Suggestion/GetTheDataBySchoolId', { schoolId: this.schoolId }).then(res => {
        console.log('学校对应二维码', res)
        this.customerList = res.data.Data
      })
    }
  }
}
</script>

<style lang="less" scoped>
.cus-img {
  width: 180px;
}
.customer-info {
  width: 365px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 16px;
}
.bor {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}
</style>
