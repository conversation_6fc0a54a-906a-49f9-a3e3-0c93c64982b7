<template>
  <div class="help-mr" style="margin:0 auto;padding:0 40px;">
    <p class="help-nav pc_font_size18 font_size20 ipad_font_size22">
      <span>客服中心</span>
      <span class="cur" @click="toProposal">投诉与建议</span>
    </p>
    <div class="clearfix">
      <div class="fl que-left font_size18 ipad_font_size20">
        <h3 class="m-b">常见问题</h3>
        <!-- <p class="cur m-b" @click="show1(1)" :class="{'now-color': this.show === 1}"><span class="pointer"></span>练习已做，显示未完成？</p>
        <p class="cur m-b" @click="show1(2)" :class="{'now-color': this.show === 2}"><span class="pointer"></span>近期登录异常？</p>
        <p class="cur m-b" @click="show1(3)" :class="{'now-color': this.show === 3}"><span class="pointer"></span>登录后首页无练习显示</p> -->
        <p @click="show1(item.Id)" class="cur m-b" :class="{'now-color': show === item.Id}" v-for="item in commonQues" :key="item.Id" v-html="item.ItemContent" v-katex>
          <span class="pointer"></span>
        </p>
        <h3 class="m-b">问题分类</h3>
        <ul>
          <li v-for="item in quesSort" :key="item.Id">
            <div>
              <p @click="showQuestionType(item.Id)" class="cur  m-b" :class="{'now-color': show === item.Id}">
                <span class="m-r">{{ item.Name }}</span>
                <span>
                  <img src="@/assets/student/icon-down.png" alt="">
                  <!-- <img src="@/assets/student/iconUp.png" alt=""> -->
                </span>
              </p>
              <div v-show="showType === item.Id" class="ques-type">
                <p class="cur" v-for="ite in item.questionItemsVOs" :key="ite.Id" @click="handleShowType(ite.Id)" :class="{'now-color': show === ite.Id}">
                  <span class="pointer"></span>
                  {{ ite.ItemContent }}
                </p>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="fl que-right font_size24 ipad_font_size26">
        <div v-for="item in commonQues" :key="item.Id" v-show="show === item.Id">
          <p>{{ item.ItemAnswer }}</p>
        </div>
        <div v-if=" this.secondType.length !== 0">
          <div v-for="item in secondType[0].questionItemsVOs" :key="item.Id" v-show="show === item.Id">
            <p>{{ item.ItemAnswer }}</p>
          </div>
        </div>
        <div v-for="item in quesSort" :key="item.Id" v-show="show === item.Id">
          <p>{{ item.Name }}</p>
          <!-- <p>请选择以下信息</p> -->
        </div>
        <!-- <div v-show="this.show === 1">
          <p>1.试卷提交后，提示提交成功并弹出成绩报告页面，则表示试卷数据上传成功，等待几分钟后改试卷状态会切换成已完成。</p>
          <p>2.试卷提交后可以点击首页的筛选,选择已完成,可以即时查看刚提交的试卷是否完成。</p>
        </div>
        <div v-show="this.show === 2">
          <p>1.请先检查网络环境是否正常</p>
          <p>2.</p>
        </div>
        <div v-show="this.show === 3">
          <p>1.请先检查网络环境是否正常,确认正常后,重新刷新首页</p>
          <p>2.</p>
        </div>
        <div v-show="this.show === '2'">
          <p>1.注册时任课老师有误</p>
        </div> -->
        <div class="question-link">
          <span style="margin-right:35px;"><img class="cur" @click="toContactService" src="@/assets/student/问题未解决.png" alt=""></span>
          <span><img class="cur" @click="showOk" src="@/assets/student/问题已解决.png" alt=""></span>
          <p v-show="this.showok">非常感谢您的反馈，我们会努力为您提供更好的服务和帮助</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.getCustomerList()
  },
  data() {
    return {
      // 常见问题
      commonQues: [],
      // 问题分类
      quesSort: [],
      // 二层问题分类
      secondType: [],
      secondTypeOne: [],
      showType: '',
      show: '1247426401145982976',
      showok: false
    }
  },
  methods: {
    show1(id) {
      this.show = id
    },
    showOk() {
      this.showok = true
      setTimeout(() => {
        this.showok = false
      }, 800)
    },
    handleChange(value) {
      this.show = value
    },
    // 获取客服常见问题
    getCustomerList() {
      this.$uwonhttp.post('/Customer/QuestionItems/GetQuestions').then(res => {
        this.commonQues = res.data.Data.CommonQuesionSubjects.questionItemsVOs
        this.quesSort = res.data.Data.OtherQuestionSubjects
      })
    },
    // 处理问题分类
    showQuestionType(id) {
      this.showType = id
      this.show = id
      this.secondType = this.quesSort.filter(value => {
        if (value.Id === id) {
          return value
        }
      })
      this.secondTypeOne = this.secondType[0]
    },
    // 问题分类显示
    handleShowType(id) {
      this.show = id
    },
    toProposal() {
      this.$router.push('/Student/List/Proposal')
    },
    toContactService() {
      // console.log('客服')
      this.$router.push('/Student/List/Customer')
    }
  }
}
</script>

<style lang="less" scoped>
.help-mr {
  margin-right: 190px;
  .clearfix{
    width: 100%;
    display: flex;
    .que-left{
      width: 22%;
    }
    .que-right{
      width: 76%;
    }
  }
}
.m-r {
  margin-right: 25px;
}
.m-b {
  margin-bottom: 15px;
}
.pointer {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 6px;
  border-radius: 10px;
  background-color: #ddd;
}
.now-color {
  padding: 3px 5px;
  color: #fff;
  background-color: #65b391;
  border-radius: 3px;
}
.help-nav {
  //height: 38px;
  //line-height: 38px;
  //text-indent: 15px;
  font-weight: 500;
  //font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  padding: 10px 20px;
  span {
    margin-right: 35px;
  }
  span:nth-child(1) {
    color: #000;
  }
  span:nth-child(2) {
    color: #b5b5b5;
  }
}
.que-left {
  //margin-right: 100px;
  padding: 30px;
  font-size: 16px;
}
.que-right {
  //width: 70%;
  padding: 30px;
  font-size: 15px;
  margin-top: 30px;
  border: 1px solid #ccc;
  border-radius: 5px;
  //float:right;
}
.question-link {
  margin-top: 200px;
}
/deep/.ant-select-selection {
  border: none;
  background-color: rgba(240, 242, 245);
}
// 问题分类
.ques-type {
  margin-top: -8px;
}
</style>
