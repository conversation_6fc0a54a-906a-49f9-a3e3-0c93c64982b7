<template>
  <div class="proposal-mr clearfix" style="margin: 0 auto;">
    <p class="help-nav pc_font_size18 font_size20 ipad_font_size22">
      <span @click="toCustomerCenter" class="cur">客服中心</span>
      <span>投诉与建议</span>
    </p>
    <div>
      <p class="pc_font_size18 font_size24 ipad_font_size28">
        <img src="@/assets/student/proposal.png" alt="" /><span class="f-c">请选择投诉/建议类型</span
        ><span @click="toMyFeedBack" class="my-feedback fg cur">我的反馈</span>
      </p>
      <a-radio-group @change="SelectOptions" :defalut-value="1" style="width: 100%; ">
        <a-radio :value="1" class="font_size22 ipad_font_size26" style="width: 100%;height:40px;line-height:40px;font-size: 16px">
          性能故障:
          <span style="margin: 10px 0">白屏、闪退、卡顿、图片出不来等</span>
        </a-radio>
        <a-radio :value="2" class="font_size22 ipad_font_size26" style="width: 100%;height:40px;line-height:40px;font-size: 16px">
          功能异常:
          <span>部分功能异常</span> </a-radio
        ><br />
        <a-radio :value="3" class="font_size22 ipad_font_size26" style="width: 100%;height:40px;line-height:40px;font-size: 16px">
          产品建议:
          <span>我要吐槽</span>
        </a-radio>
        <a-radio :value="4" class="font_size22 ipad_font_size26" style="width: 100%;height:40px;line-height:40px;font-size: 16px">
          其他反馈:
          <span>请在下面描述</span>
        </a-radio>
      </a-radio-group>
    </div>
    <p class="pc_font_size18 font_size24 ipad_font_size28"><img src="@/assets/student/proposal.png" alt="" /><span class="f-c">请输入您的投诉/建议</span></p>
    <textarea
      class="font_size22 ipad_font_size26"
      style="width: 100%; font-size: 16px;margin:10px 0;padding:5px;"
      id="text"
      v-model="textInfo"
      placeholder="为了方便尽快解决您的问题，请详细描述核心问题

        如例:

        首页显示问题

        什么时候，做了哪些操作，系统反馈了什么"
    >
    </textarea>
    <!-- <div>
      <p><img src="@/assets/student/proposal.png" alt=""><span class="f-c">添加相关图片或视频（选填）</span></p>
      <p>
        <a-upload
          name="avatar"
          list-type="picture-card"
          class="avatar-uploader"
          :show-upload-list="false"
          action="https://www.mocky.io/v2/5cc8019d300000980a055e76"

          @change="handleChange"
        >
          <img v-if="imageUrl" :src="imageUrl" alt="avatar" />
          <div v-else>
            <a-icon :type="loading ? 'loading' : 'plus'" />
            <div class="ant-upload-text">
              Upload
            </div>
          </div>
        </a-upload>
      </p>
    </div> -->
    <div>
      <p class="pc_font_size18 font_size24 ipad_font_size28"><img src="@/assets/student/proposal.png" alt="" /><span class="f-c">联系方式</span></p>
      <p class="m-r m-b font_size22 ipad_font_size26">
        <img style="width: 10px" src="@/assets/student/proposal.png" alt="" />手机号（必填）<input
          v-model="Phone"
          class="info-input"
          type="text"
        />
      </p>
      <p class="m-r m-b font_size22 ipad_font_size26">微信号（选填）<input v-model="weChat" class="info-input" type="text" /></p>
      <p class="m-r m-b font_size22 ipad_font_size26">QQ号（选填）<input v-model="qq" class="info-input" type="text" /></p>
    </div>
    <p>
      <span class="proposal-btn font_size18 ipad_font_size22" style="background-color: #a2c240">取消</span>
      <span @click="submitPro" class="proposal-btn font_size18 ipad_font_size22" style="background-color: #68bb97">提交</span>
    </p>
  </div>
</template>

<script>
function getBase64(img, callback) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}
export default {
  created() {
    this.userId = localStorage.getItem('UserId')
  },
  data() {
    return {
      userId: '',
      nowTime: '',
      Phone: '',
      qq: '',
      weChat: '',
      // 默认问题类型
      falutType: '',
      // 其他内容
      textInfo: '',
      radioStyle: {
        display: 'block',
      },
      loading: false,
      imageUrl: '',
    }
  },
  methods: {
    getPhone() {
      // this.
    },
    // 上传图片
    handleChange(info) {
      if (info.file.status === 'uploading') {
        this.loading = true
        return
      }
      if (info.file.status === 'done') {
        // Get this url from response in real world.
        getBase64(info.file.originFileObj, (imageUrl) => {
          this.imageUrl = imageUrl
          this.loading = false
        })
      }
    },
    SelectOptions(e) {
      this.falutType = e.target.value
    },
    submitPro() {
      this.nowTime = new Date()
      if (!this.falutType) {
        return this.$message.warning('请选择反馈类型')
      } else if (!this.textInfo) {
        return this.$message.warning('请输入反馈内容')
      } else if (!this.Phone) {
        return this.$message.warning('请输入手机号')
      } else {
        this.$uwonhttp
          .post('/Customer/Suggestion/SaveData', {
            CreateTime: this.nowTime,
            CreatorId: this.userId,
            FaultType: this.falutType,
            Phone: this.Phone,
            QQ: this.qq,
            WeChat: this.weChat,
            SourcePlatform: 1,
            UserReplyMsg: this.textInfo,
          })
          .then((res) => {
            this.$router.push({ path: '/Student/List/FeedBackResult' })
          })
      }
    },
    // 客服中心
    toCustomerCenter() {
      this.$router.push('/Student/List/HelpFeedbackList')
    },
    // 我的反馈
    toMyFeedBack() {
      this.$router.push('/Student/List/MyFeedBack')
    },
  },
}
</script>

<style lang="less" scoped>
.proposal-mr {
  padding:0 40px 10px 40px;
  //margin-right: 190px;
}
.m-b {
  margin-bottom: 20px;
  font-size:16px;
}
.t-in {
  text-indent: 20px;
}
.f-c {
  //font-size: 18px;
  vertical-align: middle;
  color: #4d5753;
}
.m-r {
  margin-left: 30px;
}
// 我的反馈
.my-feedback {
  padding: 7px 15px;
  color: #fff;
  background-color: #68bb97;
  border-radius: 15px;
}
// 操作
.proposal-btn {
  //display: inline-block;
  //width: 90px;
  //height: 35px;
  margin-right: 25px;
  line-height: 35px;
  //text-align: center;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
  padding: 10px 30px;
}
.info-input {
  width: 300px;
  height: 30px;
  border: 1px solid #ddd;
  border-radius: 5px;
}
#text {
  height: 200px;
  width: 450px;
  margin-bottom: 15px;
  border-color: #d6d6d6;
  border-radius: 5px;
  resize: none;
}
.help-nav {
  //height: 38px;
  //line-height: 38px;
  //text-indent: 15px;
  font-weight: 500;
  //font-size: 16px;
  margin-bottom: 25px;
  background-color: #fff;
  border-radius: 5px;
  padding: 10px 20px;
  span {
    margin-right: 35px;
  }
  span:nth-child(1) {
    color: #b5b5b5;
  }
  span:nth-child(2) {
    color: #000;
  }
}
</style>
