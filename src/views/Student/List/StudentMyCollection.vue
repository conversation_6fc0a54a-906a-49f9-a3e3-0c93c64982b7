<template>
  <div>
    <!-- 暂无 -->
    <div v-if="friends" class="not-yet">
      <img src="@/assets/lack/暂无搜索记录.png" alt="">
      <p style="color:#ccc;text-align:center;">暂无收藏的视频</p>
    </div>

    <!-- 视频信息 -->

    <a-modal v-model="visible" title="提醒" @ok="handleOk" width="630px" centered>
      <p>确定移除微课吗？</p>
    </a-modal>
    <p v-if="!friends" class="my-collection-name font_size20 ipad_font_size22">我的收藏</p>
    <p v-if="!friends" class="clearfix pc_font_size18 font_size20 ipad_font_size22" style="margin: 10px 0;">
      <span v-if="showCancel" @click="cancel" class="fg manage-collection">取消</span>
      <span @click="manageMicro" class="fg manage-collection">{{ manageBtn }}</span>
    </p>
    <div id="info" class="info clearfix">
      <div class="fl my-collection-data" v-for="item in collectionList" :key="item.Id">
        <img @click="removeMicro(item.Id,$event)" v-show="checkStudent" class="img" :src="checkIcon" alt="">
        <div class="main" :style="{ backgroundImage:'url(' + item.CoverUrl + ')',backgroundRepeat:'no-repeat',backgroundSize:'100% 100%' }">
          <a-icon @click="toVideoDetails(item.Id)" type="play-circle" :style="{ fontSize: '42px' , position: 'relative', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', cursor: 'pointer', color: '#fff'}" />
        </div>
        <div class="play-info">
          <div class="main-info clearfix pc_font_size18 font_size20 ipad_font_size22">
            <span class="fl">{{ item.ChapterName }}</span>

          </div>
          <div class="main-info-down clearfix pc_font_size18 font_size20 ipad_font_size22">
            <span class="fl">{{ item.Name }}</span>
          </div>
          <div class="main-info-time pc_font_size16 font_size18 ipad_font_size20">
            <span class="main-info-time"><img src="@/assets/user/时长.png" alt="">{{ item.Duration }}</span>
            <span class="main-info-time">
              <a-icon type="play-circle" />
              {{ item.ClickNum }}次
            </span>

          </div>
        </div>
      </div>
    </div>
    <!-- 分页 -->
    <div class="paging">
      <a-pagination :defaultCurrent="1" :total="total" @change="handleChange" :defaultPageSize="pageSize" hideOnSinglePage />
    </div>
    <!-- <p class="footer">Copyright©上海有我科技有限公司，All Rights Reserved</p> -->
  </div>
</template>

<script>
import '@/utils/utils.less'
export default {
  components: {},
  created() {
    this.userId = localStorage.getItem('UserId')
    // 收藏数据列表
    this.getCollectionList()
  },
  watch: {
    $route() {
      // const ChangId = window.location.href.split('?')[1]
      this.userId = localStorage.getItem('UserId')
      if (this.$route.fullPath === '/Student/List/StudentMyCollection') {
        // 收藏数据列表
        this.getCollectionList()
      }
    }
  },
  updated() {
    this.heightPage = document.getElementById('info').offsetHeight
  },
  data() {
    return {
      heightPage: 0,
      friends: false,
      count: 1,
      // 当前页码
      pageIndex: 1,
      // 每页条数
      pageSize: 8,
      // 用户ID
      userId: '',
      // 收藏数据列表
      collectionList: [],
      // 收藏总数
      total: 1,
      manageBtn: '管理',
      // 勾选图标
      checkIcon: require('@/assets/teacher/未勾选.png'),
      visible: false,
      checkStudent: false,
      arr: [],
      arr1: '',
      showCancel: false
    }
  },
  methods: {
    // 获取页码，每页条数
    handleChange(page, pageSize) {
      this.pageIndex = page
      this.getCollectionList()
    },
    // 跳转视频详情
    toVideoDetails(videoId) {
      this.$router.push({
        path: '/Paper/Exam_MicroLesson/VideoDetails',
        query: {
          videoId
        }
      })
    },
    // 收藏数据列表
    getCollectionList() {
      this.$uwonhttp
        .post('/PersonalController/Personal/GetAllVideoByCollection', {
          pageNo: this.pageIndex,
          pageSize: this.pageSize,
          key: '',
          userid: this.userId
        })
        .then(res => {
          this.collectionList = res.data.Data.Items
          this.total = res.data.Data.TotalItems
          if (res.data.Data.TotalItems < 1) {
            this.friends = true
          }
        })
    },
    // 管理微课
    manageMicro() {
      this.checkStudent = true

      if (this.manageBtn === '管理') {
        this.manageBtn = '移除微课'
        this.showCancel = true
        this.removeMicro(null, null, 1)
      } else {
        this.visible = true
      }
    },
    cancel() {
      this.checkStudent = false
      this.showCancel = false
      this.manageBtn = '管理'
    },
    removeMicro(id, e, show) {
      if (show === 1) {
        this.arr1 = ''
        const info = document.getElementsByClassName('img')
        for (const key of info) {
          key.src = require('@/assets/teacher/未勾选.png')
        }
      } else {
        if (this.arr.indexOf(id) === -1) {
          this.arr.push(id)
        }
        if (e.target.src === require('@/assets/teacher/未勾选.png')) {
          e.target.src = require('@/assets/teacher/勾选.png')
        } else {
          e.target.src = require('@/assets/teacher/未勾选.png')
          const idx = this.arr.indexOf(id)
          if (idx !== -1) {
            this.arr.splice(idx, 1)
          }
        }

        this.arr1 = this.arr.join(',')
      }
    },
    handleOk() {
      this.checkStudent = false
      this.showCancel = false
      this.manageBtn = '管理'
      this.visible = false
      this.$uwonhttp
        .post('/Exam_MicroLesson/MicroLesson/RemoveCollectionVideo', { userid: this.userId, vids: this.arr1 })
        .then(res => {
          if (res.Success) {
            this.$message.success('移除微课视频成功')
          }
          this.getCollectionList()
        })
    }
  }
}
</script>

<style lang="less" scoped>
.not-yet {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.my-collection-name {
  font-size: 20px;
  color: #4d5753;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 5px;
}
.play-info {
  padding: 5px 0 10px 15px;
}
.my-collection-data {
  position: relative;
  width: 300px;
  margin-bottom: 25px;
  margin-right: 15px;
  background-color: #fff;
  border-radius: 5px;
}
.img {
  position: absolute;
  right: 0px;
  z-index: 12;
  background-color: green;
}
.manage-collection {
  display: inline-block;
  margin-right: 15px;
  padding: 5px 26px;
  text-align: center;
  color: #fff;
  background-color: #68bb97;
  border-radius: 40px;
  cursor: pointer;
}
// 视频信息
.info {
  .main-info-time {
    margin-right: 15px;
    //font-size: 12px;
    color: #b5b5b5;
    display: flex;
    align-items: center;
    img{
      width: 18px;
      height: 18px;
    }
  }
  margin-right: -20px;
  div {
    // margin-right: 10px;
  }
}
.info:nth-child(4n) {
  margin: 0;
}
.main {
  position: relative;
  // width: 300px;
  height: 200px;
  // margin-top: 15px;
  // margin-right: 17px;
  // border: 1px solid #ddd;
  border-radius: 5px 5px 0 0;
}
.main-info {
  width: 300px;
  margin-right: 10px;
  font-size: 10px;
}
.main-info-down {
  width: 300px;
  margin-right: 10px;
  margin-bottom: 22px;
  span:nth-child(1) {
    font-weight: 700;
  }
  span:nth-child(2) {
    font-size: 10px;
  }
}
// 分页
.paging {
  text-align: center;
  /deep/.ant-pagination-item:focus,
  .ant-pagination-item:hover {
    border-color: #68bb97;
    border-color: #68bb97;
  }
  /deep/.ant-pagination-item-active a {
    color: #000;
    background-color: #68bb97;
  }
}
</style>
