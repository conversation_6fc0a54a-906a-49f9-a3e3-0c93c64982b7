<template>
  <div class="student_token">
    <div class="token_left">
      <div class="left_haeder">
        <span>个人设置</span>
      </div>
      <div class="left_content">
        <span>
          <a-avatar :size="64" icon="user" :src="userHead" />
          <p>
            <a-upload
              name="file"
              :multiple="true"
              :show-upload-list="false"
              :action="uploadPictureUrl"
              :headers="headers"
              @change="handleChange"
            >
              <el-button class="font_size20 ipad_font_size22"> <a-icon type="upload" />修改头像 </el-button>
            </a-upload>
          </p>
        </span>
        <div class="tabs_list">
          <span>选择头像以切换学生</span>
          <div class="changeRole">
            <el-radio-group v-model="AuthoCheck" @change="agreeChange">
              <el-radio
                v-for="item in StudentList"
                :key="item.UserId"
                :label="item.UserId"
                :value="item.UserId"
                border
              >
                {{ item.UserName }}
              </el-radio>
            </el-radio-group>
          </div>
          <el-button class="font_size18 ipad_font_size20" icon="el-icon-plus" @click="Added">增加学生</el-button>
        </div>
      </div>
    </div>
    <div class="token_right">
      <span>个人信息</span>
      <el-form ref="form" :model="TeacherInfo" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="真实姓名">
              <el-input v-model="TeacherInfo.UserName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属角色">
              <el-input v-model="studentRole" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属学校">
              <el-input v-model="TeacherInfo.SchoolName" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属班级">
              <el-input v-model="TeacherInfo.className" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="我的学号">
              <el-input v-model="TeacherInfo.StudentNo" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="我的身份">
              <el-radio-group v-model="TeacherInfo.Gender" @change="getGender">
                <el-radio :label="1">男生</el-radio>
                <el-radio :label="2">女生</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="班主任">
              <el-input
                disabled
                :placeholder="TeacherInfo.TeacherName ? TeacherInfo.TeacherName : '暂无班主任'"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入学年份">
              <el-input v-model="TeacherInfo.InSchoolYear" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="学科老师">
              <el-input
                disabled
                :placeholder="TeacherInfo.TeacherCount ? TeacherInfo.TeacherCount + '位' : ''"
              ></el-input>
              <span class="see_reset font_size16 ipad_font_size18" @click="viewTeachers">查看</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span>账号设置</span>
      <div class="item__label">
        <el-form :label-position="labelPosition" label-width="80px" style="line-height: 50px" :model="TeacherInfo">
          <el-form-item label="手机号码">
            <el-input v-model="TeacherInfo.PhoneNum" disabled></el-input>
            <span class="Reset font_size16 ipad_font_size18" @click="ResetPhone">修改</span>
          </el-form-item>
          <el-form-item label="账号密码">
            <el-input v-model="TeacherNumber" disabled></el-input>
            <span class="Reset font_size16 ipad_font_size18" @click="ResetPassWord">修改密码</span>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!-- 查看学科老师 -->
    <div>
      <el-dialog :visible.sync="teacherDialogShow" title="学科老师" width="65%">
        <ul style="display: flex; flex-wrap: wrap">
          <li v-for="(teacher, index) in teacherList" :key="index + 'teacher'" class="teacherList">
            <span
              >{{ teacher.SubjectName
              }}<b :class="teacher.IsAssociate ? 'isAssociate_true' : ''" class="isAssociate">{{
                teacher.IsAssociate ? '已关联' : '未关联'
              }}</b></span
            >
            <span>{{ teacher.TeacherName }}</span>
          </li>
        </ul>
      </el-dialog>
    </div>
    <ResetPhone ref="refsPhone"></ResetPhone>
    <ResetPassWord ref="refsPassWord"></ResetPassWord>
    <AddedStudent ref="Added"></AddedStudent>
  </div>
</template>

<script>
import ResetPassWord from '@/components/ReastDialong/ResetPassWord'
import ResetPhone from '@/components/ReastDialong/ResetPhone'
import AddedStudent from '@/components/ReastDialong/AddedStudent'
import TokenCache from '@/utils/cache/TokenCache'
export default {
  data() {
    return {
      userName: '',
      userId: '',
      userData: {},
      jwtToken: '',
      userHead: '', // 用户头像
      trueName: '',
      sex: '', // 教师性别
      uploadPictureUrl: '', // 修改头像地址
      headers: {
        authorization: '', // 请求头携带
      },
      genderId: '',
      TeacherInfo: {},
      labelPosition: 'right',
      TeacherNumber: '已设置', //教师端账号信息
      studentRole: '学生',
      schoolName: '', //所属学校
      AuthoCheck: '',
      StudentList: [],
      addCagLabel: '',
      Gender: '',
      teacherDialogShow: false,
      teacherList: [],
    }
  },
  components: {
    ResetPassWord,
    ResetPhone,
    AddedStudent,
  },
  created() {
    this.userName = localStorage.getItem('mobilePhone')
    this.userId = localStorage.getItem('UserId')
    this.jwtToken = localStorage.getItem('jwtToken')
    this.GetUserInfoByUserName() // 获取用户基本信息
    this.GetUserInfo() //获取学生信息
  },
  mounted() {},
  methods: {
    //查看学科老师
    viewTeachers() {
      this.teacherDialogShow = true
      this.GetStudentTeacherList()
    },
    //获取学科老师列表
    async GetStudentTeacherList() {
      const res = await this.$uwonhttp.get(
        '/PersonalController/Personal/GetStudentTeacherList?studentId=' + this.userId
      )
      this.teacherList = res.data.Data
    },
    // 获取用户基本信息
    GetUserInfoByUserName() {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.userId }).then((res) => {
        // console.log('ss.vue')
        this.userHead = res.Data.User.Photo
        this.headers = {
          authorization: `Bearer ${this.jwtToken}`,
        }
      })
      this.uploadPictureUrl = `http://uwoomobile.doggod.xyz/User/UserManager/SetUserPhoto?UserId=${this.userId}`
    },
    // 获取学生个人信息
    async GetUserInfo() {
      const res = await this.$uwonhttp.post('/PersonalController/Personal/GetUserInfo', { userID: this.userId })
      this.TeacherInfo = res.data.Data
      this.AuthoCheck = res.data.Data.UserId
      localStorage.setItem('PhoneNum', res.data.Data.PhoneNum)
      await this.GetStudentListByPhone() //获取绑定账号列表
    },
    //绑定账号
    async GetStudentListByPhone() {
      const res = await this.$uwonhttp.get('/User/UserManager/GetStudentListByPhone', {
        params: {
          phoneNo: this.TeacherInfo.PhoneNum,
        },
      })
      this.StudentList = res.data.Data
    },
    async agreeChange(value) {
      let obj = {}
      obj = this.StudentList.find((item) => {
        return item.UserId === value
      })
      this.addCagLabel = obj.UserName
      this.userId = obj.UserId
      window.localStorage.removeItem('UserId')
      localStorage.setItem('mobilePhone', this.addCagLabel)
      TokenCache.setToken(obj.Token)
      localStorage.setItem('UserId', this.userId)
      location.reload()
      // this.$router.go(0)
      await this.GetUserInfo(this.userId)
    },
    //修改性别
    async getGender(val) {
      switch (val) {
        case 1:
          this.Gender = '男'
          break
        case 2:
          this.Gender = '女'
          break
      }
      let params = {
        UserId: this.userId,
        TrueName: this.TeacherInfo.UserName,
        Gender: this.Gender,
      }
      const res = await this.$uwonhttp.post('/User/UserManager/SetUserSex', params)
      if (res.data.Success == true) {
        this.$message.success('修改成功')
      }
    },
    // 修改头像
    handleChange(arg1, label, extra) {
      if (arg1.file.status === 'done') {
        if (arg1.file.response.Success) {
          this.userHead = arg1.file.response.Data
          this.$message.success('修改头像成功')
          this.$router.go(0)
        } else {
          this.$message.error(arg1.file.response.Msg, 2)
        }
      }
    },
    //修改手机号
    ResetPhone() {
      this.$refs.refsPhone.openDialog()
      this.$refs.refsPhone.clearVal()
    },
    //修改密码
    ResetPassWord() {
      this.$refs.refsPassWord.openDialog()
      this.$refs.refsPassWord.clearRuleForm()
    },
    //增加学生
    Added() {
      this.$refs.Added.openDialog()
      this.$refs.Added.GetSchools()
    },
  },
}
</script>
<style lang="less" scoped>
.student_token {
  display: flex;
  border-radius: 5px;
  justify-content: space-around;
  .token_left {
    width: 30%;
    background: #ffff;
    border-radius: 5px;
    .left_haeder {
      width: 100%;
      height: 50px;
      background: #f7f8f8;
      text-align: center;
      span {
        font-size: 22px;
        color: #49534e;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        line-height: 50px;
      }
    }
    .left_content {
      padding: 30px;
      text-align: center;
      /deep/ .ant-upload.ant-upload-select {
        display: inline-block;
        margin-top: 10px;
      }
      .tabs_list {
        padding: 20px;
        span {
          font-size: 22px;
          color: #49534e;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          line-height: 50px;
        }
        .changeRole {
          /deep/ .el-radio {
            display: block;
            margin-bottom: 20px;
            white-space: normal;
            margin-right: 0;
            .el-radio__label{
              font-size: 16px;
            }
          }
          /deep/ .el-radio.is-bordered + .el-radio.is-bordered {
            margin-left: 0px;
          }
        }
        /deep/ .el-button {
          width: 147px;
        }
      }
    }
  }
  .token_right {
    width: 68%;
    background: #ffff;
    border-radius: 5px;
    padding: 30px;
    @media screen and (min-device-width: 1030px) and (max-device-width: 1360px){
      /deep/.el-radio{
        .el-radio__label{
          font-size: 16px;
        }
      }
      /deep/.el-form-item__label{
        font-size: 16px;
      }
    }
    @media screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape){
      /deep/.el-radio{
        .el-radio__label{
          font-size: 18px;
        }
      }
      /deep/.el-form-item__label{
        font-size: 18px;
      }
      /deep/.el-input__inner{
        font-size: 16px;
        color: #000000;
      }
    }
    span {
      font-size: 22px;
      color: #49534e;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      line-height: 50px;
    }
    /deep/ .el-input {
      position: relative;
      font-size: 0.072917rem;
      display: inline-block;
      width: 75%;
    }
    .item__label {
      /deep/.el-form-item__label {
        width: 80px;
        line-height: 50px;
      }
      /deep/ .el-input {
        position: relative;
        font-size: 0.072917rem;
        display: inline-block;
        width: 30%;
      }
      .Reset {
        font-size: 15px;
        color: #409eff;
        cursor: pointer;
        margin-left: 15px;
      }
    }
    span.see_reset {
      font-size: 15px;
      color: #409eff;
      cursor: pointer;
      margin-left: 15px;
    }
    .from_save {
      margin-top: 50px;
      /deep/ .el-button {
        width: 200px;
        position: relative;
        left: 10%;
        background: #68bb97;
        color: #ffff;
      }
    }
  }
}
.teacherList {
  width: 25%;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  span:nth-child(1) {
    border: 1px solid #e4e7ed;
    width: 50%;
    text-align: center;
    line-height: 40px;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    .isAssociate {
      position: absolute;
      right: -10px;
      top: -15px;
      color: #ffff;
      background:#EC808D;
      font-size:12px;
      padding:0 5px;
      line-height:24px;
      border-radius: 4px;
    }
    .isAssociate_true {
      background: #16D585;
    }
  }
}
</style>
