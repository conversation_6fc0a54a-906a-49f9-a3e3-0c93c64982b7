<template>
  <div class="concern">
    <!-- 暂无关注 -->
    <div v-if="friends" class="noConcern">
      <img src="@/assets/lack/暂无关注好友.png" alt="">
      <p>暂无关注</p>
    </div>
    <!-- 我的关注 -->
    <div id="my-concern">
      <p v-if="!friends" class="my-concern-name font_size20 ipad_font_size22">我的关注</p>
      <div class="concern-content fl" v-for="item in concernList" :key="item.TeacherID">
        <div style="display: flex;align-items: center">
          <a-avatar :src="item.Photo" :size="newSize" icon="user" />
          <!-- 个人信息 -->
          <div class="infor">
            <p class="font_size22 ipad_font_size24" @click="goTutorMicro(item.TeacherID)">{{ item.RealName }}</p>
            <p class="pc_font_size16 font_size18 ipad_font_size20">{{item.SchoolName.substring(0,7)+'...' }}</p>
          </div>
        </div>
        <!--  -->
        <div class="infor-num pc_font_size16 font_size18 ipad_font_size20">
          <span class="span">粉丝 <span class="black">{{ item.FocusCount }}</span>人</span>
          <span>课程 <span class="black">{{ item.CurriculumCount }}</span>个</span>
        </div>
        <!-- 关注 -->
        <div class="btn cur pc_font_size16 font_size16 ipad_font_size18">
          <!-- @click="confirm(item.TeacherID)" -->
          <span>{{ follow }}</span>
        </div>
      </div>
    </div>

    <!-- <div class="concern-content fl">
      <a-avatar :size="64" icon="user" style=" backgroundColor: #ddd; position: absolute; left: 50px; top: 36px; " />
      <div class="infor">
        <p>李国明</p>
        <p>高小一中</p>
      </div>
      <div class="infor-num">
        <span>粉丝  56人</span>
        <span>课程   6个</span>
      </div>
      <div class="btn">
        <a-button @click="confirm" :style="{ width: '74px', height: '32px', backgroundColor: '#ddd', borderRadius: '20px' }">{{ follow }}</a-button>
      </div>
    </div> -->
  </div>
</template>

<script>
import '@/utils/utils.less'
import isMobile from '@/utils/isMobile/isMobile'
export default {
  components: {},
  created() {
    this.userId = localStorage.getItem('UserId')
    // 关注数据列表
    this.getFollowList()
    this.roundSize()
  },
  updated() {
    this.heightPage = document.getElementById('my-concern').offsetHeight
  },
  data() {
    return {
      heightPage: 0,
      follow: '已关注',
      userId: '',
      // 关注数据列表
      concernList: [],
      friends: false,
      newSize: null
    }
  },
  watch: {
    $route() {
      if (this.$route.fullPath === '/Paper/Exam_MyConcern/MyConcern') {
        this.getFollowList()
      }
    }
  },
  methods: {
    roundSize () {
      if (isMobile()) {
        this.newSize = 38
      } else {
        this.newSize = 65
      }
    },
    // 切换关注状态
    switch() {},
    // 关注提示框
    confirm(e) {
      const that = this
      const modal = this.$confirm({
        title: '温馨提示',
        content: '你确定取消关注吗？',
        okText: '确认',
        cancelText: '取消',
        // 确定按钮回调
        onOk() {
          // 取消关注
          that.$http
            .post('/Focus/Focus_Collection/AddDataColletion', {
              CreatorId: e
            })
            .then(res => {
              console.log('确认取消', res)
              that.getFollowList()
            })
          modal.destroy()
        }
      })
    },
    // 跳转老师的微课
    goTutorMicro(teacherId) {
      this.$router.push({
        path: '/Student/Fine_micro/FamousTeacherPage',
        query: {
          teacherId,
          userId: this.userId
        }
      })
    },
    // 关注数据列表
    getFollowList() {
      this.$uwonhttp.post('/PersonalController/Personal/GetMyFollow', { userid: this.userId }).then(res => {
        this.concernList = res.data.Data.Items
        if (res.data.Data.Items.length < 1) {
          this.friends = true
        } else {
          this.friends = false
        }
      })
    }
  }
}
</script>

<style lang="less">
// .concern div:nth-child(3n) {
//   margin-right: 0;
// }
.noConcern {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(165, 166, 167);
  img {
    margin-bottom: 20px;
  }
}
.my-concern-name {
  font-size: 20px;
  margin-bottom: 25px;
  padding: 10px 20px;
  color: #4d5753;
  background-color: #fff;
  border-radius: 5px;
}
.black {
  font-weight: 700;
  color: #000;
}
.concern-content {
  //position: relative;
  padding: 20px;
  width: 300px;
  height: 184px;
  margin-right: 20px;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .infor {
    margin-left: 10px;
    p:nth-child(1) {
      font-size: 18px;
    }
    p:nth-child(1):hover {
      color: #68bb97;
      cursor: pointer;
    }
  }
  .infor-num {
    text-align: center;
    //margin-top: 113px;
    .span {
      margin-right: 20px;
    }
  }
  .btn {
    text-align: center;
    margin-top: 8px;
    span {
      display: inline-block;
      width: 74px;
      height: 32px;
      line-height: 32px;
      color: #fff;
      background-color: #d6d6d6;
      border-radius: 5px;
    }
  }
}
.concern-footer {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #b5b5b5;
}
</style>
