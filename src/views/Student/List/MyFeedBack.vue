<template>
  <div class="myback-mr clearfix"  style="margin:0 auto;">
    <p class="my-back font_size22 ipad_font_size24"><img @click="returnGo" class="cur" src="@/assets/teacher/左边箭头.png" alt=""><span class="my-back-name">我的反馈:</span></p>
    <div v-if="this.myBack.length === 0" style="text-align:center">
      <img src="@/assets/lack/暂无搜索记录.png" alt="">
      <p style="color:#ccc;">暂无我的反馈</p>
    </div>
    <div class="back-list" v-for="(item, index) in myBack" :key="index">
      <p class="back-info">{{ item.UserReplyMsg }}</p>
      <p>{{ item.CreateTime }}</p>
    </div>
    <!-- 分页 -->
    <div class="paging">
      <a-pagination :default-current="pageNo" :total="totalNum" @change="onPaging" hideOnSinglePage />
    </div>
  </div>
</template>

<script>
export default {
  watch: {
    $route() {
      if (this.$route.fullPath === '/Student/List/MyFeedBack') {
        this.getMyBack()
      }
    }
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.getMyBack()
  },
  data() {
    return {
      userId: '',
      myBack: [],
      pageNo: 1,
      totalNum: 1
    }
  },
  methods: {
    getMyBack() {
      this.$http
        .post('/Customer/Suggestion/GetDataList', {
          PageIndex: this.pageNo,
          PageRows: 10,
          condition: 'CreatorId',
          keyword: this.userId
        })
        .then(res => {
          this.myBack = res.Data
          this.totalNum = res.Total
        })
    },
    //
    onPaging(pageNumber) {
      this.pageNo = pageNumber
      this.getMyBack()
    },
    returnGo() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
.myback-mr {
  margin-right: 190px;
}
.my-back {
  padding-bottom: 15px;
  font-size: 16px;
  color: #737976;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  img{
    width: 30px;
    height: 30px;
  }
}
.my-back-name {
  margin-left: 8px;
}
.back-list {
  padding-bottom: 10px;
  font-size: 14px;
  color: #737976;
  border-bottom: 1px solid #ccc;
}
.back-info {
  margin-top: 15px;
}
</style>
