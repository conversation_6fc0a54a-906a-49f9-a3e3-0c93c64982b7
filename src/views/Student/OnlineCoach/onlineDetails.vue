<template>
  <div class="online-box">
    <p class="online-banner">辅导中心</p>
    <div class="ask-answer-info">
      <p class="clearfix m-b">{{ subjectInfo.ItemTitleWeb }}<span class="fg">{{ anaInfo.CreateTime }}</span></p>
      <!-- 单选 -->
      <p v-if="subjectInfo.ItemType === 2" class="m-b">A. 7 B. 8 C.9 D. 10</p>
      <p class="m-b">
        <span class="chapter-img"><img src="@/assets/finemicro/link.png" alt="">{{ subjectInfo.ChapterName }}</span>
      </p>
      <p class="m-b">我的作答: <span class="wrong-col" :class="{ 'wrong-col': subjectInfo.IsKeyTrue === 2, 'correct': subjectInfo.IsKeyTrue === 1}">{{ subjectInfo.UserAnswer }}(错误)</span></p>
      <p class="clearfix"><span>正确答案: {{ subjectInfo.Answer }}</span></p>
    </div>
    <div class="coach-explain">
      <p class="cocach-p"><img src="@/assets/student/rectangle.png" alt="">&nbsp;&nbsp;辅导讲解</p>
      <div>
        <p class="m-b">这次作业的关键点:</p>
        <p class="m-b">{{ anaInfo.CruxText }}</p>
        <!-- <p class="m-b">2.xxxxxxxxxxxxxx</p> -->
        <p class="m-b">
          <span>图片</span>
          <!-- <img :src="subjectInfo.PicUrl" alt=""> -->
          <img class="change-big" style="width: 200px;" src="@/assets/student/welcomeBanner.png" alt="" preview="1">
          <img style="width: 200px;" src="@/assets/student/welcomeBanner.png" alt="" preview="2">
        </p>
        <!-- <p
          @click="playAdiuoed"
          class="sound-show"
        >
        </p> -->
        <!-- 播放音频 -->
        <!-- <div class="play-adiuo"> -->
        <div class="play-adiuo" ref="playAdiuo" @click="playAdiuoed" :class="{ 'active': activePlay === '1' }">
          <audio ref="mp3" @ended="overAudio" src="https://uwoo.obs.cn-east-2.myhuaweicloud.com:443/uploadFile4739176f4f4a48898613bf8bcff9b52d.20201020184024.mp3">
            <!-- <source src="" type="audio/ogg"> -->
            <!-- <source src="horse.mp3" type="audio/mpeg"> -->
            <!-- <source src=""> -->
            您的浏览器不支持 audio 元素。
          </audio>
          <span style="color: #fff">{{ playTime }}</span>
        </div>
        <!-- </div> -->

        <div class="video">
          <video-player class="video-player vjs-custom-skin" ref="videoPlayer" :playsinline="true" :options="playerOptions">
          </video-player>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Recorder from 'js-audio-recorder'
const recorder = new Recorder()
export default {
  created() {
    this.getOnlineDetails()
    setTimeout(() => {
      this.playSound()
    }, 800)
  },
  mounted() {},
  watch: {
    $route() {
      const audio = this.$refs.mp3
      this.playTime = parseInt(audio.duration)
    }
  },
  data() {
    return {
      activePlay: '',
      // 试题详情
      subjectInfo: {},
      // 辅导详情
      anaInfo: {},
      playerOptions: {
        playbackRates: '', // 播放速度'[0.5, 1, 1.5, 2]'
        autoplay: false, // 如果true,浏览器准备好时开始回放。
        controls: true, // 控制条
        preload: 'auto', // 视频预加载
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        language: 'zh-CN',
        aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [
          {
            // type: 'video/mp4',
            // type: 'video/ogg',
            type: 'video/webm',
            src:
              'https://video2020.obs.cn-east-2.myhuaweicloud.com:443/uploadFile29bd43d836a14af2813d021b793b3a81.2016050419295949.mp4' // 你所放置的视频的地址，最好是放在服务器上
          }
        ],
        poster:
          'https://itemimage.obs.cn-east-2.myhuaweicloud.com/uploadFile271a4c71b69d433eb0ad147f2b523172.20190228143954.jpg', // 你的封面地址（覆盖在视频上面的图片）
        width: document.documentElement.clientWidth,
        notSupportedMessage: '此视频暂无法播放，请稍后再试' // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
      },
      playTime: 1
    }
  },
  methods: {
    playSound() {
      const audio = this.$refs.mp3
      this.playTime = parseInt(audio.duration)
      // this.activePlay = '1'
      // recorder.play()
      // recorder.onplayend = this.playend()
    },
    playAdiuoed() {
      this.activePlay = '1'
      const audio = this.$refs.mp3
      audio.play()
    },
    overAudio() {
      this.activePlay = ''
    },
    // 获取辅导中心的详情
    getOnlineDetails() {
      this.$http.post('/ExtendedAnalysis/Coach/GetStudentCoachInfo', { Id: '1' }).then(res => {
        const reg = /(#&\d+@)/
        // replace 返回新的字符串
        res.Data.ItemTitleWeb = res.Data.ItemTitleWeb.replace(reg, ' ')
        this.subjectInfo = res.Data
        this.anaInfo = res.Data.CoachsVo
        // this.playerOptions.sources[0].src = res.Data.CoachsVo.VideoUrl
        // this.playerOptions.poster = res.Data.CoachsVo.PictureUrl
      })
    }
  }
}
</script>

<style lang="less" scoped>
.online-box {
  margin-right: 190px;
}
.wrong-col {
  color: #ff682c;
}
// 正确作答
.correct {
  color: #68bb97;
}
.m-b {
  margin-bottom: 15px;
}
// 图片
.change-big {
  margin-right: 25px;
  cursor: url('../../../assets/student/bbig.png'), auto;
}
// 音频
.play-adiuo {
  width: 201px;
  height: 39px;
  line-height: 39px;
  margin-top: 15px;
  margin-bottom: 15px;
  text-align: center;
  background: url('../../../assets/correcting/录音show.png') no-repeat center bottom;
  cursor: pointer;
}
.online-banner {
  height: 38px;
  line-height: 38px;
  text-indent: 15px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
}
.ask-answer-info {
  margin-bottom: 35px;
  padding: 24px;
  padding-bottom: 10px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  .chapter-img {
    padding: 3px 5px;
    background-color: #f2f2f2;
    border-radius: 15px;
  }
  img {
    width: 20px;
  }
}
// 辅导讲解
.coach-explain {
  padding: 24px;
  padding-bottom: 10px;
  font-size: 16px;
  border-radius: 5px;
  background-color: #fff;
  // 讲解
  .cocach-p {
    margin-bottom: 15px;
  }
  // 语音
  .sound-show {
    width: 201px;
    height: 39px;
    line-height: 39px;
    margin-top: 15px;
    text-align: center;
    background: url('../../../assets/correcting/录音show.png');
    cursor: pointer;
  }
  @keyframes soundShow {
    0% {
      background: url('../../../assets/correcting/播放1.png');
    }
    25% {
      background: url('../../../assets/correcting/播放2.png');
    }
    50% {
      background: url('../../../assets/correcting/播放3.png');
    }
    100% {
      background: url('../../../assets/correcting/播放3.png');
    }
  }
  .active {
    animation: soundShow 1.6s linear infinite;
  }
}
// 视频
.video {
  display: inline-block;
  width: 450px;
  height: 259px;
  text-align: center;
  line-height: 100px;
  border: 1px solid transparent;
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-right: 25px;
}
</style>
