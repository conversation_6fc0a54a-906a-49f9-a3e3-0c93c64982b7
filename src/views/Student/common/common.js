//分数填空格式化处理
function format_fraction(regxList, item) {
  //regxList-匹配到的分数填空列表   
  //item-要替换的题目
  const regx1 = /\$\$\/frac\{/g
  const regx2 = /\}\{/g
  const regx3 = /\}\$\$/g
  if (regxList && regxList.length > 0) {
    regxList.forEach((regx_item) => {
      let fenshu = regx_item;
      fenshu = fenshu.replace(
        regx1,
        `
            <div style="display:inline-block;vertical-align:middle;margin:0 8px;" >
          `
      )
      fenshu = fenshu.replace(
        regx2,
        `
            <hr style="width:100%;height:3px;background:black;position:relative;top:5px;"></hr>
          `
      )
      fenshu = fenshu.replace(
        regx3,
        `</div>
          `
      )
      item = item.replace(regx_item, fenshu)
    })
  }
  return item;
}



export default{
    format_fraction
}

