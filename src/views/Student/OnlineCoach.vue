<template>
  <div>
    <!-- 缺省页 -->
    <!-- <div v-if="onlineLack" class="online-lack">
      <img src="@/assets/lack/暂无搜索记录.png" alt="">
      <p>暂无我的微课数据</p>
    </div> -->
    <div class="online-coach">
      <p class="online-banner">辅导中心</p>
      <!-- 微课基本信息 -->
      <!-- <div class="coach-micro">
      <div class="info">
        <a-avatar :size="64" icon="user" :style="{ position: 'absolute', left: '54px', top: '45px'}" />
        <div class="info-content">
          <p>李国平</p>
          <p>高一小学</p>
        </div>
      </div>
      <div class="info-details">
        <ul>
          <li>
            <p>156</p>
            <p>提问数</p>
          </li>
          <li>
            <p>100</p>
            <p>老师回答数</p>
          </li>
        </ul>
      </div>
    </div> -->
      <!-- 提问辅导 -->
      <!-- <div class="ask-coach clearfix">
      <div class="fl ask-text">提问题目</div>
      <div class="fg coach-center">
        <span>辅导中心 &nbsp;&nbsp;&nbsp;</span>
        <span>更多名师解答&nbsp;&nbsp;&nbsp;</span>
        <span> > > </span>
      </div>
    </div> -->
      <!-- 搜索内容 -->
      <div class="clearfix search-info">
        <div class="fl">
          <a-cascader class="cascader-sel" :field-names="{ label: 'ChapterName', value: 'ChapterId', children: 'Second' }" :options="options" change-on-select allowClear @change="onChangeKnowledge" placeholder="全部题目" />
          <a-select class="sel-date" placeholder="所有日期" show-search @change="handleChange" default-value="all">
            <a-select-option value="all">
              所有日期
            </a-select-option>
            <a-select-option value="week">
              上一周
            </a-select-option>
            <a-select-option value="month">
              上一月
            </a-select-option>
          </a-select>
        </div>
        <!-- <div class="fg">
        <a-input-search placeholder="搜索提问内容" style="width: 250px" @search="onSearch" />
      </div> -->
      </div>
      <!-- 缺省 -->
      <div v-if="onlineLack" class="online-lack">
        <img src="@/assets/lack/暂无搜索记录.png" alt="">
        <p>暂无搜索记录哦</p>
      </div>
      <!-- 辅导题目 -->
      <div class="ask-answer">
        <h3 v-if="!onlineLack">辅导题目</h3>
        <div class="ask-answer-info" v-for="item in paperList" :key="item.Id">
          <div>
            <p class="clearfix m-b" v-html="item.ItemTitleWeb" v-katex></p>
            <span class="fg">{{ item.CreateTime }}</span>
          </div>

          <p v-show="item.ItemType === 2 || item.ItemType === 10 || item.ItemType === 11" class="m-b" v-for="(ite,index) in item.Options " :key="index">
            <span>{{ ite.Opt }}</span><span>{{ ite.Content }}</span>
          </p>
          <p class="m-b">
            <span class="chapter-img"><img src="@/assets/finemicro/link.png" alt="">{{ item.ChapterName }}</span>
          </p>
          <p class="m-b">我的作答: <span :class="{ 'correct': item.IsKeyTrue !== 2, 'wrong-col': item.IsKeyTrue === 2}">{{ item.UserAnswer }} (错误)</span></p>
          <p class="clearfix"><span>正确答案: {{ item.Answer }}</span><span class="fg see-all cur" @click="toOnlineDetails">查看全部</span></p>
        </div>
      </div>
      <!-- 分页 -->
      <div class="paging">
        <a-pagination :default-current="pageNo" :total="this.total" @change="onPaging" hideOnSinglePage />
      </div>
    </div>
  </div>

</template>

<script>
import '@/utils/utils.less'
export default {
  components: {},
  created() {
    this.stuId = localStorage.getItem('UserId')
    this.userPhone = localStorage.getItem('mobilePhone')
    this.getClassId()
    // 辅导列表
    this.getCoachList()
  },
  data() {
    return {
      // 缺省
      onlineLack: false,
      // 学生id
      stuId: '',
      options: [],
      // 评论显示
      showcomment: false,
      // 评论
      comment: '评论',
      pageNo: 1,
      chapterId: '',
      timeSlot: '',
      // 分页总数
      total: 1,
      // 辅导列表
      paperList: []
    }
  },
  methods: {
    // 时间段选择
    handleChange(value) {
      console.log(`selected ${value}`)
      if (value === 'all') {
        this.timeSlot = ''
      } else {
        this.timeSlot = value
      }
    },
    // 获取班级id
    getClassId() {
      this.$http.post('/Base_Manage/Base_User/GetUserInfoByUserName', { userName: this.userPhone }).then(res => {
        this.classId = res.Data.ClassId
        console.log('OnlineCoach.vue')
        this.getChapterList()
      })
    },
    // 获取章节的选择
    getChapterList() {
      this.$uwonhttp.post('/Chapter/TeacherChapter/GetChapterByClass', { ClassId: this.classId }).then(res => {
        this.options = res.data.Data
      })
    },
    // 获取辅导中心内容
    getCoachList() {
      this.$http
        .post('/ExtendedAnalysis/Coach/GetStudentCoachList', {
          studentId: this.stuId,
          pageNo: this.pageNo,
          pageIndex: 10,
          weekTime: this.timeSlot,
          chapterId: this.chapterId
        })
        .then(res => {
          if (res.Data.Items.length === 0) {
            this.onlineLack = true
          } else {
            this.onlineLack = false
            this.paperList = res.Data.Items
            this.total = res.Total
            this.paperList.forEach(value => {
              const reg = /(#&\d+@)/g
              const stem = value.ItemTitleWeb.replace(reg, ' ')
              value.ItemTitleWeb = stem
            })
          }
        })
    },
    // 获取当前分页
    onPaging(pageNum) {
      this.pageNo = pageNum
      // this.getCoachList()
    },
    // 章节选择
    onChangeKnowledge(value) {
      if (value.length === 1) {
        this.chapterId = ''
      } else {
        this.chapterId = value[1]
      }
    },
    // 搜索框
    onSearch(value, event) {
      // event.target.value = ''
    },
    // 辅导详情页
    toOnlineDetails() {
      this.$router.push({ path: '/Student/OnlineCoach/onlineDetails' })
    }
  }
}
</script>

<style lang="less" scoped>
.online-coach {
  padding-right: 190px;
}
.online-lack {
  text-align: center;
  color: #ccc;
}
// 错误作答
.wrong-col {
  color: #ff682c;
}
// 正确作答
.correct {
  color: #68bb97;
}
.m-b {
  margin-bottom: 15px;
}
h3 {
  margin-bottom: 15px;
  font-size: 16px;
}
.online-banner {
  height: 38px;
  line-height: 38px;
  text-indent: 15px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
}
// 微课基本信息
.coach-micro {
  position: relative;
  height: 150px;
  margin-bottom: 20px;
  border-radius: 5px;
  background-color: #ddd;
  // 个人信息
  .info {
    position: relative;
    .info-content {
      position: absolute;
      left: 140px;
      top: 50px;
      p:nth-child(1) {
        font-weight: 700;
        font-size: 20px;
      }
    }
  }
  // 信息详情
  .info-details {
    position: relative;
    ul {
      position: absolute;
      right: 80px;
      top: 46px;
      li {
        float: left;
        margin-right: 70px;
        text-align: center;
        p:nth-child(1) {
          font-size: 26px;
        }
      }
    }
  }
}
// 提问 辅导中心
.ask-coach {
  height: 50px;
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
  .ask-text {
    width: 98px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #000;
    background: url('../../assets/finemicro/Pointer.png');
  }
  .coach-center {
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    span:nth-child(2) {
      font-size: 12px;
    }
    span:nth-child(odd) {
      font-size: 18px;
      color: blue;
    }
  }
}
// 搜索内容
.search-info {
  height: 50px;
  margin-top: 20px;
  margin-bottom: 10px;
  // border-bottom: 2px solid #ccc;
}
// 提问 解答
.ask-answer {
  margin-bottom: 15px;
  font-size: 16px;
  .ask-answer-info {
    margin-bottom: 35px;
    padding: 24px;
    padding-bottom: 10px;
    background-color: #fff;
    border-radius: 5px;
    .chapter-img {
      padding: 3px 5px;
      background-color: #f2f2f2;
      border-radius: 15px;
    }
    // 查看全部
    .see-all {
      display: inline-block;
      // width: 80px;
      // height: 32px;
      // line-height: 32px;
      padding: 5px 8px;
      text-align: center;
      color: #fff;
      background-color: #68bb97;
      border-radius: 5px;
    }
    img {
      width: 20px;
    }
  }
}
// 选择框
.select-sty {
  margin-right: 40px;
}
.cascader-sel {
  width: 349px;
  margin-right: 20px;
}
.sel-date {
  width: 150px;
}
//   /deep/.ant-select-selection--single {
//     height: 50px;
//     width: 150px;
//     border-radius: 35px;
//     padding-top:10px;
//     margin-left:10% ;
// }
</style>
