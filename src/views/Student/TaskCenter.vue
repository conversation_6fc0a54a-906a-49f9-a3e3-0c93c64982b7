<template>
  <div class="clearfix task-center">
    <div class="switch_top">
      <switch-Multidisciplinary
        :sortShow="sortShow"
        widthNum="100%"
        @handleClick="newHandleClick"
      ></switch-Multidisciplinary>
    </div>
    <div style="display: flex">
      <div
        class="flex_item_shrink "
        style="transition: all ease 0.3s"
        :class="{ openInDialogBorder: openInDialog }"
        :style="{ width: leftWidth + '%' }"
      >
        <left-screening ref="leftScreening" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 16 ? 2.3 : 16" @getAllList="getLeftScreening"></left-screening>
      </div>
      <!-- 作业详情 -->
      <div class="fl task-info">
        <div class="right-header clearfix ant-col-xl-24 ant-col-xxl-24">
          <p class="screen-menu fl pc_font_size20 font_size20 ipad_font_size24">
            <!-- <span class="m-r cur" @click="changeArea(0)" :class="{ color: this.PaperType === 0 }">全部</span> -->
            <span class="m-r cur" @click="changeArea(1)" :class="{ color: this.PaperType === 1 }">待完成</span>
            <span class="m-r cur" @click="changeArea(3)" :class="{ color: this.PaperType === 3 }">待订正</span>
            <span class="m-r cur" @click="changeArea(2)" :class="{ color: this.PaperType === 2 }">已完成</span>
            <span class="m-r cur" @click="changeArea(4)" :class="{ color: this.PaperType === 4 }">待批阅</span>
          </p>
        </div>
        <div class="example" v-if="loadPaper" style="text-align: center; width: 100%">
          <a-spin />
        </div>
        <div v-if="loadDefault" style="text-align: center; color: #ccc">
          <img src="@/assets/lack/暂无搜索记录.png" alt="" />
          <p>暂无作业列表</p>
        </div>
        <div :class="[paperData.length%4  != 0 ? 'new_content_item' : 'content_item']">
          <div
            :class="[paperData.length%4  != 0  ? 'new_li_item' : '', 'li_item']"
            v-for="(item, index) in paperData"
            :key="index"
          >
            <div class="item-header">
              <div>
                <div style="display: flex;justify-content: space-between;">
                  <div>
                    <span
                      @mouseenter="showChapterName(item.PaperId)"
                      @mouseleave="hideChapterName"
                      class="chapter-width ch-m-r pc_font_size18 font_size20 ipad_font_size22"
                      style="color: #4d5753"
                    >{{ item.ChapterName.substring(0,8) }}</span>
                  </div>
                  <div
                    class="xuekeStyle pc_font_size14 font_size16 ipad_font_size18"
                    :style="{ 'background-color': bgColog[item.SubjectId + 'bg'], color: bgColog[item.SubjectId] }">
                    {{ item.SubjectName }}
                  </div>
                </div>
                <el-tooltip class="item" effect="dark" placement="top-start">
                  <div slot="content">
                    <p class="til-top pc_font_size16 font_size18 ipad_font_size20">
                      {{ item.Title }}
                    </p>
                  </div>
                </el-tooltip>
                <div class="subjectTitle">
                  <p class="til-top pc_font_size18 font_size18 ipad_font_size20">
                    {{ item.Title.substring(0, 10) }}
                    <img class="micro-icon" v-if="item.IsVideo === 1" src="@/assets/student/icon-micro.png" alt="" />
                    <img class="imgIsdzb" v-if="item.IsPhotograph == 1" src="@/assets/index/bianZu.png" />
                  </p>
                  <span class="Coach_span pc_font_size14 font_size14 ipad_font_size16" v-show="item.PaperType === -2">辅导助手</span>
                </div>
                <div class="item-content">
                  <div class="correct-chart" v-if="item.DoPaperType === 1">
                    <p class="p-chart">
                      <a-progress
                        type="circle"
                        :percent="item.Accuracy"
                        :width="newWidth"
                        :strokeColor="accuracyColor"
                        :format="
                        () => {
                          if (item.Accuracy === 100) {
                            return '100%'
                          } else if(item.Accuracy === 0) {
                            return '--'
                          } else {
                            return item.Accuracy + '%'
                          }
                        }
                      "
                      />
                      <span class="pc_font_size16 font_size18 ipad_font_size20">正确率</span>
                    </p>
                    <p class="p-chart" v-if="item.RevisionCount !== 0">
                      <a-progress
                        type="circle"
                        :percent="(item.RevisionCount / item.ItemCount) * 100"
                        :width="newWidth"
                        :format="() => item.RevisionCount + '/' + item.ItemCount"
                        :strokeColor="stuStatistics"
                      />
                      <span class="pc_font_size16 font_size18 ipad_font_size20">待订正错题数</span>
                    </p>
                      <div class="r-info" v-if="item.DoPaperType === 1">
                        <div v-if="item.Rank === 2 || item.Rank === 1">
                          <img src="@/assets/student/excellent.png" alt="" />
                          <p class="pc_font_size16 font_size18 ipad_font_size20">优秀</p>
                        </div>
                        <div v-if="item.Rank === 3">
                          <img src="@/assets/student/good.png" alt="" />
                          <p class="pc_font_size16 font_size18 ipad_font_size20">良好</p>
                        </div>
                        <div v-if="item.Rank === 4">
                          <img src="@/assets/student/qualified.png" alt="" />
                          <p class="pc_font_size16 font_size18 ipad_font_size20">合格</p>
                        </div>
                        <div v-if="item.Rank === 5">
                          <img src="@/assets/student/strive.png" alt="" />
                          <p class="pc_font_size16 font_size18 ipad_font_size20">需努力</p>
                        </div>
                      </div>
                  </div>

                  <div
                    class="item_num"
                    v-if="item.DoPaperType === 0 || item.DoPaperType === 2"
                  >
                    <p class="pc_font_size14 font_size16 ipad_font_size18">共{{ item.ItemCount }}道题</p>
                    <!--  -->
                    <!-- <p v-show="isTime">预计完成时长15分钟</p> -->
                  </div>
                </div>
              </div>
              <div>
                <!-- style="text-align: center; margin-top: 8px" -->
                <div class="item-footer" :style="{'justify-content': item.DoPaperType === 1 && item.SubjectId == '1' && item.IsOpen || item.EvaluationStatus === 2 ? 'space-between': 'center'}">
                  <!-- <span v-if="item.DoPaperType === 1" class="operation"><span @click="toReviewWrong(item.PaperId, item.ChapteId)">复习回顾</span></span> -->
                  <!-- <span v-if="item.DoPaperType === 1" class="operation" @click="AnsweReport(item)">成绩报告</span> -->
                  <span v-if="item.DoPaperType === 1 && item.IsDZB === 0">
                    <span @click="AnsweReport(item)" class="operation pc_font_size16 font_size18 ipad_font_size20">成绩报告</span>
                  </span>
                  <!--       已完成作答，并且是点阵笔，内部包含作文题         -->
                  <span v-if="item.DoPaperType === 1 && item.IsDZB === 1 && item.PaperTag == 26">
                    <span @click="goZuoWenAnalysis(item)" class="operation pc_font_size16 font_size18 ipad_font_size20">成绩报告</span>
                  </span>
                  <span v-if="item.DoPaperType === 3">
                    <span style="background-color: #1DBFFF;" class="operation pc_font_size16 font_size18 ipad_font_size20" @click="submissionTips(item)">已提交</span>
                  </span>
                  <span v-if="item.DoPaperType === 0 || item.DoPaperType === 2">
                    <!--                <span-->
                    <!--                @click="toAnswerPaper(item.PaperId, item.HaveSpecial, item.SubjectId)"-->
                    <!--                :class="item.ColorTime == true ? 'TodyColor' : 'YesterColor'">-->
                    <!--                  开始答题-->
                    <!--                </span>-->
                    <span
                      @click="
                        toAnswerPaper(
                          item.PaperId,
                          item.HaveSpecial,
                          item.IsExistSubjectItem,
                          item.IsDZB,
                          item
                        )
                      "
                      :class="[item.ColorTime == true ? 'TodyColor' : 'YesterColor', 'pc_font_size16', 'font_size18', 'ipad_font_size20']"
                    >
                      开始答题
                    </span>
                  </span>
                  <span v-if="item.DoPaperType === 1 && item.SubjectId == '1' && item.IsOpen" class="huping" @click="evaluateBtn(item.PaperId,1)">互评</span>
                  <span v-if="item.DoPaperType === 1 && item.SubjectId == '1' && item.EvaluationStatus == 2 && !item.IsOpen" class="huping" @click="evaluateBtn(item.PaperId,2)">互评详情</span>
                </div>
              </div>
            </div>
            <div class="item_empty"></div>
            <div class="item_empty"></div>
            <div class="item_empty"></div>
          </div>
        </div>
        <div>
          <!-- 分页 -->
          <a-pagination
            class="paging"
            show-quick-jumper
            :default-current="1"
            :total="PageCounts"
            hideOnSinglePage
            @change="onChange"
            :defaultPageSize="pageRows"
          />
        </div>
        <!-- 提示框 -->
        <el-dialog title="提示" :visible.sync="dialogVisible" width="40%">
          <div class="title_tips">
            <h6>{{ TeacherName }}说：</h6>
            <p>你还有{{ NoFinishCount }}份习题未完成，请到”练习中心“完成练习吧！</p>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="warning" round @click="dialogVisible = false">前去练习</el-button>
          </span>
        </el-dialog>
        <!--    所有练习完成弹窗-->
        <div class="global_bg" v-show="globalShow">
          <div class="global_bg_position">
            <div class="new_icon" @click="closeBtn">
              <i class="el-icon-error" style="color: #dcdfe6"></i>
            </div>
            <div class="botm_div">
              <p>真了不起！</p>
              <p>{{ StudentName }}</p>
              <div class="el_btn" type="warning" round @click="globalShow = false">我知道了</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="smallbellVisible"
      width="24%">
      <div class="smallbell">
        <img src="@/assets/icon/smallbell.png" />
        <h1>练习名称</h1>
        <p>请小朋友使用点阵笔，在纸质上进行作答此份练习噢！</p>
      </div>
      <div style="display: flex;justify-content: center;margin-top: 26px;">
        <el-button type="primary" size="medium" @click="smallbellVisible = false">知 道 啦</el-button>
      </div>
    </el-dialog>
    <mutualEvaluate ref="mutualEvaluate"></mutualEvaluate>
<!--    <details ref="details"></details>-->
  </div>
</template>

<script>
// import '@/utils/utils.less'
import switchMultidisciplinary from '@/components/switchMultidisciplinary/switchMultidisciplinary'
import mutualEvaluate from '@/views/Student/components/mutualEvaluate'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { debounce } from 'lodash'
import isMobile from '@/utils/isMobile/isMobile'
import myMixIn from '@/mixIn'
import leftScreening from '@/components/leftScreening/index.vue'

export default {
  components: {
    leftScreening,
    switchMultidisciplinary,
    mutualEvaluate
  },
  mixins: [myMixIn],
  created() {
    this.userId = localStorage.getItem('UserId')
    this.$store.commit('setHeaderIndex', 1)
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.SubjectId = localStorage.getItem('SubjectId')
    this.GetNewDay()
    // 获取往日练习
    this.getPastPaper(this.userId)
    // 获取当前时间戳
    // this.getNewTime()
    // this.chapterTree()
    // 获取学生未完成习题套数
    this.getStudentRemindMessage()
    this.roundSize()
  },
  data() {
    return {
      isTime: true,
      loadPaper: true,
      loadDefault: false,
      // 正确率
      accuracyColor: '#68BB97',
      // 待订正
      stuStatistics: '#F87175',
      // 学生ID
      userId: '',
      // 默认展开指定的树菜单
      expandedKeys: ['一 10以内的数', '二 10以内数的加减法'],
      // 展开父节点
      autoExpandParent: true,
      // 默认选中的子节点
      checkedKeys: '',
      // 设置选中的数节点
      selectedKeys: [],
      treeData: [],
      replaceFields: {
        children: 'Second',
        title: 'ChapterName',
        key: 'ChapterId',
      },
      // 当前页码
      paperIndex: 1,
      // 每页多少条数据
      pageRows: 8,
      // 往日练习数据
      paperData: [],
      // 练习总量
      PageCounts: 1,
      // 当前时间 (时间戳)
      newTime: '',
      // 班 / 区
      PaperType: 0,
      // 隐藏已做
      HideDo: 0,
      showChName: '0',
      isToday: '',
      // 未完成联系套数
      NoFinishCount: 0,
      StudentName: '',
      TeacherName: '',
      // 提示框
      dialogVisible: false,
      // 完成练习的提示窗
      globalShow: false,
      // 多学科部分
      sortShow: false,
      bgColog: {},
      SubjectId: '',
      // 是否为点阵笔作业出现弹窗
      smallbellVisible: false,
      newWidth: null,
      // 章节树使用
      openInDialog: false,
      leftWidth: 16,
    }
  },
  filters: {
    // ellipsis(_val) {
    //   if (!_val) return ''
    //   if (_val.length > 6) {
    //     return _val.slice(0, 6) + '...'
    //   }
    //   return _val
    // }
  },
  watch: {
    $route() {
      if (this.$route.fullPath === '/Student/TaskCenter') {
        this.SubjectId = localStorage.getItem('SubjectId')
        this.loadPaper = true
        this.GetNewDay()
        // 获取往日练习
        this.getPastPaper()
        // this.chapterTree()
        // 获取当前时间戳
        // this.getNewTime()
        this.$store.commit('setHeaderIndex', 1)
        this.$refs.leftScreening.getChild()
      }
    },
  },
  methods: {
    // 章节树组件筛查
    getLeftScreening(opt){
      this.loadPaper = true
      this.paperIndex = 1
      this.getPastPaper()
    },
    roundSize (){
      if (isMobile()) {
        this.newWidth = 44
      } else {
        this.newWidth = 52
      }
    },
    // 多学科选择学科点击事件
    newHandleClick: debounce(function (val) {
      if (val === '-1') {
        // this.treeData = []
        this.$refs.leftScreening.empty()
      } else {
        // this.chapterTree()
        this.$refs.leftScreening.getChild()
      }
      this.SubjectId = val
      this.paperIndex = 1
      this.getPastPaper()
    }, 800),
    // 关闭弹窗
    closeBtn() {
      this.globalShow = false
    },
    showChapterName(id) {
      this.showChName = id
    },
    hideChapterName() {
      this.showChName = '1'
    },
    // 获取学生未完成习题套数
    getStudentRemindMessage() {
      this.$uwonhttp.post('/Paper/Paper/GetStudentRemindMessage', { UserId: this.userId }).then((res) => {
        const data = res.data.Data
        this.NoFinishCount = data.NoFinishCount
        this.TeacherName = data.TeacherName
        if (data.NoFinishCount > 0 && data.IsTeacherTip === 1) {
          this.dialogVisible = true
          this.globalShow = false
        } else {
          this.getWeekPracticePush()
        }
      })
    },
    // 获取学生完成的提示
    getWeekPracticePush() {
      this.$uwonhttp
        .post('/Paper/Paper/GetStudentPaperTip', {
          UserId: this.userId,
        })
        .then((res) => {
          this.StudentName = res.data.Data.TipContent
          if (res.data.Data.State !== 0) {
            this.dialogVisible = false
            this.globalShow = true
          }
        })
    },
    // 章节数
    chapterTree() {
      this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', { userid: this.userId, IsWeek: 1 }).then((res) => {
        const data = res.data.Data
        data.forEach((item_1) => {
          if (item_1.ChapterName.lenght > 14) {
            item_1.ChapterName = item_1.ChapterName.slice(0, 14) + '...'
          }
          item_1.Second.forEach((item_2) => {
            if (item_2.ChapterName.lenght > 14) {
              item_2.ChapterName = item_2.ChapterName.slice(0, 14) + '...'
            }
          })
        })
        this.treeData = data
      })
    },
    // 点击复选框触发
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys.join(',')
      this.loadPaper = true
      this.paperIndex = 1
      this.getPastPaper()
    },
    // 点击树节点触发
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    // 获取页码
    onChange(pageNumber) {
      this.paperIndex = pageNumber
      this.loadPaper = true
      this.getPastPaper()
    },
    // 隐藏已做
    handleShow() {
      this.paperIndex = 1
      if (this.HideDo) {
        this.HideDo = false
      } else {
        this.HideDo = true
      }
      this.loadPaper = true
      this.getPastPaper()
    },
    // 切换班 / 区
    changeArea(id) {
      this.PaperType = id
      this.loadPaper = true
      if (id === 0) {
        this.HideDo = 0
      } else if (id === 1) {
        this.HideDo = 1
        this.paperIndex = 1
      } else {
        this.HideDo = 2
        this.paperIndex = 1
      }

      this.getPastPaper()
    },
    // 切换等级
    handleGrade(value) {
      this.rankNum = value
      this.loadPaper = true
      this.getPastPaper()
    },
    // 获取往日练习的数据
    getPastPaper(id) {
      const { Ids } = this.$store.state.chapterStorage.ChapterObj
      var domain = document.domain
      if (domain == 'st.eduwon.cn') {
        this.isTime = false
      } else {
        this.isTime = true
      }
      const Platform = parseInt(localStorage.getItem('PL_newP'))
      this.$http
        .post('/Student/Exam_Student/GetStudentPaper', {
          UserId: this.userId,
          PageNo: this.paperIndex,
          PageIndex: this.pageRows,
          // ChapterId: this.checkedKeys,
          ChapterId: Ids.join(','),
          PaperType: this.PaperType,
          Rank: this.rankNum,
          IsDo: this.HideDo,
          SubjectId: this.SubjectId,
          Platform,
        })
        .then((res) => {
          this.paperData = res.Data.Items
          this.paperData.forEach((item) => {
            item.ColorTime = false
            item.DateTime = item.CreateTime.substring(0, 10)
            if (item.DateTime == this.isToday) {
              item.ColorTime = true
            }
          })
          this.PageCounts = res.Data.TotalItems
          if (res.Data.Items.length !== 0) {
            this.loadPaper = false
            this.loadDefault = false
          } else {
            this.loadPaper = false
            this.loadDefault = true
          }
        })
    },
    GetNewDay() {
      var d = new Date()
      var year = d.getFullYear()
      var month = change(d.getMonth() + 1)
      var day = change(d.getDate())
      var hour = change(d.getHours())
      var minute = change(d.getMinutes())
      var second = change(d.getSeconds())
      function change(t) {
        if (t < 10) {
          return '0' + t
        } else {
          return t
        }
      }
      var time = year + '/' + month + '/' + day
      this.isToday = time
    },
    // 跳转到我的错题库
    toMyWrongBank() {
      this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
    },
    // 新跳转至答题页面
    toAnswerPaper(id, HaveSpecial, IsExistSubjectItem,IsDZB,item) {
      const Subject = {name:item.SubjectName,SubjectId: item.SubjectId}
      if(IsDZB == 1){
        this.smallbellVisible = true
        return false
      }else {
        const LoginSource = localStorage.getItem('LoginSource')
        localStorage.setItem('Multidisciplinary',JSON.stringify(Subject))
        if (LoginSource == '1'){
          if(this.$store.state.limitingJobPermissions == 0){
            this.popupPrompt()
            return false
          }
          this.$router.push({
            path: '/Student/Exam_Student/AnswerReady',
            query: {
              paperId: id,
              HaveSpecial: HaveSpecial,
              // SubjectIdName: JSON.stringify(SubjectIdName), // 学科ID
              type: 10,
              IsExistSubjectItem
            },
          })
        } else {
          this.$router.push({
            path:'/Student/Exam_Student/AnswerPaper',
            query:{
              paperId: id
            }
          })
        }
      }
    },
    // 跳转到复习回顾
    toReviewWrong(id, chapterId) {
      this.$router.push({ path: '/Student/Exam_Student/ReviewWrong', query: { paperId: id, chapterId: chapterId } })
    },

    AnsweReport(item) {
      const LoginSource = localStorage.getItem('LoginSource')
      if(this.$store.state.limitingJobPermissions == 0 && LoginSource == 1){
        this.popupPrompt()
        return false
      }
      const Subject = { name:item.SubjectName, SubjectId: item.SubjectId }
      localStorage.setItem('Multidisciplinary',JSON.stringify(Subject))
      this.$router.push({
        path: '/Student/Exam_Student/AnswerDetail',
        query: {
          paperId: item.PaperId,
          paperName: item.Title
        },
      })
    },
    // 成绩报告
    goZuoWenAnalysis(item){
      const { Title, PaperId, SubjectId, SubjectName } = item
      this.$router.push({
        path: '/Student/zuoWenAnalysis/index',
        query: {
          Title,
          PaperId,
          SubjectId,
          SubjectName
        },
      })
    },
    // 互评价
    evaluateBtn (paperId,num) {
      const studentId = localStorage.getItem('UserId')
      this.$refs.mutualEvaluate.init(paperId, studentId, num)
      // this.$refs.details.init()
    },
    // 互评详情
    // evaluate (paperId) {
    //   const studentId = localStorage.getItem('UserId')
    //   this.$refs.mutualEvaluate.newInit(paperId,studentId)
    // },
    // 获取当前时间戳
    // getNewTime() {
    //   this.newTime = new Date().getTime()
    // },
    // 已提交事件提示
    submissionTips (item) {
      const data = {
        paperId: item.PaperId,
        userId: localStorage.getItem('UserId')
      }
      this.$uwonhttp.post('Paper/Paper/GetPaperSubmitQueueState', data).then((res) => {
        if (res.data.Success) {
          this.$message({ message: res.data.Data, duration: 5000 })
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.flex_item_shrink{
  height: 70vh;
}
@media (min-width: 1600px) {
  .ant-col-xxl-5 {
    width: 100% !important;
  }
}
.item-header{
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.xuekeStyle {
  padding: 4px 14px;
  border-radius: 100px;
}
.switch_top {
  margin-bottom: 18px;
  padding: 18px 25px;
  background-color: #ffffff;
  border-radius: 10px;
}
.subjectTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  div {
    padding: 4px 18px;
    border-radius: 100px;
  }
}
// 提示弹窗
.title_tips {
  h6 {
    font-size: 26px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #353535;
    line-height: 37px;
  }
  p {
    margin-top: 10px;
    font-size: 24px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #797979;
    line-height: 33px;
    padding-left: 40px;
  }
}
// 完成练习弹窗
.global_bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.4);
  .global_bg_position {
    width: 500px;
    height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    margin: 0 auto;
    background-image: url('../../assets/student/completeBg.png');
    background-size: 100% 100%;
    position: relative;
    .new_icon {
      font-size: 32px;
      position: absolute;
      right: -30px;
      top: -30px;
    }
    .botm_div {
      width: 100%;
      text-align: center;
      padding-bottom: 43px;
      p {
        font-size: 22px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #7e7e7e;
        line-height: 37px;
      }
      .el_btn {
        width: 180px;
        height: 56px;
        background: #ffffff;
        border-radius: 29px;
        border: 2px solid #fa6400;
        text-align: center;
        line-height: 56px;
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #fa6400;
        margin: 50px auto 0 auto;
      }
    }
  }
}
.TodyColor {
  display: inline-block;
  width: 136px;
  padding: 6px 0;
  //height: 30px;
  margin-right: 28px;
  //line-height: 30px;
  color: #fff;
  text-align: center;
  border-radius: 25px;
  cursor: pointer;
  background-color: #519efc;
}
.YesterColor {
  display: inline-block;
  width: 136px;
  padding: 6px 0;
  //height: 30px;
  margin-right: 28px;
  //line-height: 30px;
  color: #fff;
  text-align: center;
  border-radius: 25px;
  cursor: pointer;
  background-color: #68bb97;
}
.new_content_item {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  .item_empty {
    width: 24%;
    height: 0;
  }
}
.content_item {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .item_empty {
    width: 24%;
    height: 0;
  }
}
.ch-m-r {
  margin-right: 5px;
}
.til-top {
  //font-size: 19px;
  color: #68bb97;
  margin-top: 5px;
  .imgIsdzb{
    width: 28px;
    height: 28px;
    margin-left: 8px;
  }
}
.ec-hi {
  margin: 10px 0 34px 0;
}
@color: #15335135;
.m-r {
  margin-right: 20px;
}
.color {
  color: #68bb97;
}
.chapter-width {
  width: 110px;
  //font-size: 20px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chapter-width:hover {
  overflow: visible;
  z-index: 2;
}
// 微课图标
.micro-icon {
  width: 20px;
}
// 隐藏已做
.hide {
  /deep/.ant-switch-checked {
    background-color: #ccc;
  }
  /deep/.ant-switch-checked:after {
    background-color: #68bb97;
  }
}
// 筛选菜单
.screen-menu {
  // margin-top: 10px;
  /deep/.ant-select-selection--single {
    border: none;
    background-color: #fff;
  }
}
.create-time {
  margin-top: 10px;
  font-size: 12px;
  color: #ccc;
}
// 正确图表
.correct-chart {
  margin-top: 10px;
  display: flex;
  //flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;

  .p-chart {
    display: inline-block;
    //font-size: 12px;
    text-align: center;
    color: #9ba6a9;
    display: flex;
    flex-direction: column;
    margin-right: 4px;
    //span{
    //  font-size: 16px
    //}
    @media screen and (min-width: 1024px) and (max-width: 1440px) {
      /deep/.ant-progress-circle .ant-progress-text {
        font-size: 18px;
      }
    }
    @media screen and (min-width: 1440px) {
      /deep/.ant-progress-circle .ant-progress-text {
        font-size: 12px;
      }
    }
  }
}

.task-catalog {
  // width: 280px;
  margin-right: 25px;
  font-size: 16px;
  margin-top: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: #fff;
  padding:0 10px;
  /deep/.ant-tree-title {
    font-size: 20px;
    font-weight: 700;
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li {
    margin: 0;
    padding: 10px 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }
  p {
    //height: 40px;
    //line-height: 40px;
    //font-size: 18px;
    padding: 6px;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    background-color: #68bb97;
  }
}
// 作业内容
.task-info {
  flex-grow: 1;
  // float: right;
  width: 80%;
  padding: 10px;
  border-radius: 5px;
  padding-top: 0;
  .right-header {
    margin-right: 35px;
    padding: 15px;
    background-color: #fff;
    border-radius: 5px;
  }
  .wrong-bank {
    font-size: 14px;
    cursor: pointer;
  }
  .wrong-bank:hover {
    color: #68bb97;
  }
  .new_li_item {
    margin-right: 15px;
  }
  .li_item {
    &:hover {
      box-shadow: 0px 0px 4px #000;
    }
    overflow: hidden;
    position: relative;
    width: 24%;
    min-height: 220px;
    padding: 20px;
    padding-bottom: 10px;
    margin-top: 18px;
    // margin-bottom: 20px;
    border-radius: 5px;
    background-color: #fff;
    .operation {
    //  margin-bottom: 0;
    //  position: absolute;
    //  bottom: 4%;
    //  left: 26%;
      display: inline-block;
      width: 136px;
      padding: 6px 0;
      margin-top: 10px;
      //height: 30px;
      //line-height: 30px;
      color: #fff;
      text-align: center;
      background-color: sandyBrown;
      border-radius: 25px;
      cursor: pointer;
    }
    .huping{
      //display: inline-block;
      //width: 136px;
      //height: 30px;
      font-size: 16px;
      padding: 4px 10px;
      color: #fff;
      background-color: #68bb97;
      border-radius: 25px;
    }
  }
}
.item_num {
  margin: 15px 0 34px 0;
  //line-height: 30px;
  p {
    //font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #b5b5b5;
  }
}
li:hover {
  box-shadow: 8px 8px 7px grey;
}
.l-details {
  .chapter-show {
    display: none;
  }
}
.r-info {
  height: 100%;
  //position: relative;
  text-align: center;
  //right: 44px;
  img {
    width: 40px;
  }
  .span {
    position: absolute;
    top: 20px;
    right: 25px;
  }
  p {
    text-align: center;
    width: 75px;
  }
}

.equal-rank {
  display: inline-block;
  margin-left: 40px;
  position: relative;
}
.ant-pagination {
  text-align: center;
}
.paging {
  text-align: center;
  margin-top: 20px;
  /deep/.ant-pagination-item:focus,
  .ant-pagination-item:hover {
    border-color: #68bb97;
    border-color: #68bb97;
  }
  /deep/.ant-pagination-item-active a {
    color: #000;
    background-color: #68bb97;
  }
}
.ant-row-flex {
  flex-flow: row wrap;
  display: flexbox;
}
.item-footer {
  display: flex;
  //position: absolute;
  //bottom: 25px;
  //left: 25%;
}
.Coach_span {
  padding: 4px 10px;
  border-radius: 25px;
  color: #31c0ce;
  background-color: rgba(49, 192, 206, 0.2);
  //text-align: center;
  //display: inline-block;
  margin-top: 10px;
}
// 是否为点阵笔试卷弹窗提示
.smallbell{
  position: relative;
  img{
    width: 100px;
    position: absolute;
    right: 10px;
    top: -50px;
  }
  h1{
    padding-top: 20px;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
  }
  p{
    padding-top: 10px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
  }
}
</style>