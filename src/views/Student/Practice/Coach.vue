<template>
  <div class="page clearfix" style="margin: 15px auto">
    <div class="page_left">
      <div class="Answer_header">
        <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
        <span>{{ this.$route.query.PaperTitle }}</span>
        <span></span>
      </div>
      <div class="Answer" id="Imgid">
        <div class="swiper_list">
          <div class="tea-analusis">
            <div class="line_border"></div>
            <swiper class="swiper" ref="mySwiper" :options="swiperOption">
              <!-- 'def-color': i.IsKeyTrue === 1, 'wrong-bgc': i.IsKeyTrue === 2, -->
              <swiper-slide style="text-align: center" v-for="(i, t) in paperList" :key="t">
                <a
                  class="title-btn-whole"
                  :class="{
                    'current-answer': i.Current<PERSON>ns<PERSON>,
                  }"
                  @click="switchTitle(i, t)"
                >
                  {{ t + 1 }}
                </a>
              </swiper-slide>
            </swiper>
            <div class="left-arrow">
              <div class="arrow-img" @click="prevPhoto">
                <i></i>
              </div>
            </div>
            <div class="right-arrow">
              <div class="arrow-img" @click="nextPhoto">
                <i></i>
              </div>
            </div>
          </div>
        </div>
        <div v-for="(item, index) in paperList" :key="index">
          <span v-if="Index_num === index" class="ItemType">{{ item.PaperItemType }}</span>
          <div class="content" v-if="Index_num === index">
            <span class="size">{{ index + 1 }}、</span><span class="size" v-html="item.ItemTitle" v-katex> </span>
            <div class="Radio" v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11" v-katex>
              <span v-for="(k, j) in item.Options" :key="j" style="display: block">
                <span style="vertical-align: middle">
                  {{ k.Opt }}.&nbsp;&nbsp;<span v-html="k.Content" v-katex></span
                ></span>
              </span>
            </div>
            <br />
            <br />
            <p class="size">
              我的作答:
              <span v-html="item.ShowUserAnswer" v-katex></span>
            </p>
            <br />
            <br />
            <span class="size">解析: <span v-katex v-html="item.Analysis"></span> </span>
            <br />
            <div class="Tabsbox">
              <ul>
                <li
                  v-for="(title, idex) in CoachTitle"
                  @click="changes(idex)"
                  :key="idex"
                  :class="{ active: idex == CochNum }"
                >
                  {{ title }}
                </li>
              </ul>
              <div v-for="(coch, ind) in CoachList" :key="ind" v-show="ind == CochNum">
                <span class="size" style="color: #efb61c">辅导老师:{{ coch.TeacherName }}</span>
                <br />
                <span class="size">{{ coch.CruxText }}</span>
                <br />
                <div class="Urlbox">
                  <div class="itemurl" v-for="(ImgItem, indx) in coch.PictureUrl" :key="indx">
                    <el-image :src="ImgItem" :preview-src-list="coch.PictureUrl"> </el-image>
                  </div>
                </div>
                <div>
                  <video
                    :preload="preload"
                    :height="height"
                    :width="width"
                    :controls="controls"
                    :autoplay="autoplay"
                    ref="vueRef"
                    v-if="coch.VideoUrl != ''"
                  >
                    <source :src="coch.VideoUrl" type="video/mp4" />
                  </video>
                </div>
              </div>
            </div>
            <div class="btn_text" v-if="false">
              <el-button class="btn_Prex" @click="prex(index, item)" :disabled="preDisabled">上一题</el-button>
              <el-button class="btn_next" @click="next(index, item)" :disabled="nextDisabled">下一题</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="page_right">
      <div style="padding: 10px">
        <div class="List-topics">
          <p class="menu">题目目录</p>
          <span
            v-for="(i, t) in paperList"
            :key="t"
            class="title-btn-whole"
            :class="{
              'current-answer': i.CurrentAnswer,
            }"
            @click="switchTitle(i, t)"
          >
            {{ t + 1 }}
          </span>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
export default {
  data() {
    return {
      preDisabled: true, //上禁用按钮
      nextDisabled: false, //下禁用按钮
      isShowVideo: false,
      Index_num: 0, // 当前题目
      CochNum: 0,
      paperList: [],
      CoachTitle: [],
      CoachList: [],
      VideoUrl: '',
      width: '50%', // 设置视频播放器的显示宽度（以像素为单位）
      height: '50%', // 设置视频播放器的显示高度（以像素为单位）
      preload: 'auto', //  建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
      autoplay: '',
      controls: true, // 确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
      swiperOption: {
        slidesPerView: 10, //一行显示4个
        spaceBetween: 10, //间隔30
        freeMode: true,
        speed: 1000, //滑动速度
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true,
          disabledClass: 'my-button-disabled', //前进后退按钮不可用时的类名。
          hiddenClass: 'my-button-hidden', //按钮隐藏时的Class
        },
      },
    }
  },
  created() {
    this.GetStudentCoachPapaerInfo()
  },
  components: {},
  watch: {
    Index_num(now, old) {
      if (now == this.paperList.length - 1) {
        this.nextDisabled = true
      } else {
        this.nextDisabled = false
      }
      if (now < 1) {
        this.preDisabled = true
      } else {
        this.preDisabled = false
      }
    },
  },
  methods: {
    GoBack() {
      this.$router.go(-1)
    },
    prevPhoto() {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto() {
      this.$refs.mySwiper.$swiper.slideNext()
    },

    async GetStudentCoachPapaerInfo() {
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetStudentCoachPapaerInfo', {
        UserId: localStorage.getItem('UserId'),
        PaperId: this.$route.query.PaperId,
      })
      if (res.data.Success) {
        this.paperList = res.data.Data
        this.paperList.length <= 1 ? (this.nextDisabled = true) : (this.nextDisabled = false)
        this.paperList.forEach((el, index) => {
          el.CurrentAnswer = index === 0
          if (el.ItemTypeId == 5) {
            var Newtype_5 = el.Answer.replace(/\|/g, ' , ')
            el.Answer = Newtype_5
            this.Blanks(el)
          }
        })
        const ItemId = this.paperList[0].ItemId
        this.GetStudentCoachItemInfo(ItemId)
        setTimeout(() => {
          this.GetImgAll()
        }, 1000)
      }
    },

    async GetStudentCoachItemInfo(ItemId) {
      let params = {
        ItemId: ItemId,
        UserId: localStorage.getItem('UserId'),
      }
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetStudentCoachItemInfo', params)
      if (res.data.Success) {
        const list = res.data.Data
        for (let i = 0; i < list.length; i++) {
          list[i].key = i + 1
          list[i].name = '辅导' + list[i].key
          this.CoachTitle.push(list[i].name)
        }
        this.CoachList = list
        this.CochNum = 0
      }
    },

    changes(key) {
      this.CochNum = key
    },

    Blanks(el) {
      const regx4 = /\$\$\/frac.*?\$\$/g
      const regx4List = el.ItemTitle.match(regx4)
      const reg1 = /\$\$\/frac\{/g
      const reg2 = /\}\{/g
      const reg3 = /\}\$\$/g
      if (regx4List && regx4List.length > 0) {
        regx4List.forEach((item_regx4) => {
          let fenshu = item_regx4
          fenshu = fenshu.replace(
            reg1,
            `
                <div style="display:inline-block;vertical-align:middle;margin:0 8px;" >
              `
          )
          fenshu = fenshu.replace(
            reg2,
            `
                <hr style="width:100%;height:3px;background:black;position:relative;top:2px;"></hr>
              `
          )
          fenshu = fenshu.replace(
            reg3,
            `</div>
              `
          )
          el.ItemTitle = el.ItemTitle.replace(item_regx4, fenshu)
        })
      }
      el.ItemTitle = el.ItemTitle.replace(/\#\#/g, '&nbsp;&nbsp;&nbsp;&nbsp;')
      const regx = /\(\#@\)/g
      const regxlist = el.ItemTitle.match(regx)
      console.log(regxlist)
      if (regxlist && regxlist.length > 0) {
        regxlist.forEach((item1) => {
          const value = item1.replace(regx, '')
          const title = el.ItemTitle.replace(
            item1,
            `<input class="input" autocomplete="off" disabled='disabled' type="text" style="width: 100px;height:30px;border: 0px; border-radius:4px; color:#000;border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin: 0 2px 9px 2px;"
          />`
          )
          el.ItemTitle = title
        })
      }
    },

    GetImgAll() {
      const that = this
      that.$nextTick(() => {
        const MyImg = document.getElementById('Imgid').querySelector('img')
        if (MyImg !== null) {
          MyImg.style.maxWidth = '90%'
        }
      })
    },

    next(index, item) {
      this.preDisabled = false
      item.CurrentAnswer = false
      this.paperList[index + 1].CurrentAnswer = true
      this.$set(this.paperList, index + 1, this.paperList[index + 1])
      if (this.Index_num < this.paperList.length - 1) {
        this.Index_num += 1
      }
      this.CoachTitle = []
      this.GetStudentCoachItemInfo(this.paperList[index + 1].ItemId)
      this.GetImgAll()
      this.callswiper(index)
    },
    callswiper(index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },
    prex(index, item) {
      item.CurrentAnswer = false
      this.paperList[index - 1].CurrentAnswer = true
      this.$set(this.paperList, index - 1, this.paperList[index - 1])
      if (this.Index_num === 0) {
        this.Index_num = 0
      } else {
        this.Index_num -= 1
      }
      this.CoachTitle = []
      this.GetStudentCoachItemInfo(this.paperList[index - 1].ItemId)
      this.GetImgAll()
    },

    // 切换题目
    switchTitle(i, index) {
      this.Index_num = index
      this.paperList.forEach((el) => {
        el.CurrentAnswer = false
      })
      i.CurrentAnswer = true
      this.CoachTitle = []
      this.GetStudentCoachItemInfo(i.ItemId)
      this.GetImgAll()
    },
  },
}
</script>

<style lang="less" scoped>
.page {
  width: 90%;
  display: flex;
  justify-content: space-between;
  .page_left {
    width: 100%;
    background: #ffff;
    .Answer_header {
      display: flex;
      justify-content: space-between;
      font-size: 22px;
      padding: 15px;
    }
    .ItemType {
      font-size: 20px;
      margin-left: 1.5%;
    }
    .ItemType:before {
      content: '';
      display: inline-block;
      width: 5px;
      height: 25px;
      vertical-align: middle;
      background: #68bb97;
      margin: 0 8px 1px;
    }
    .Radio {
      position: relative;
      top: 13px;
      font-size: 18px;
      left: 6px;
      line-height: 34px;
    }
    .content {
      padding: 25px;
    }
    .btn_text {
      margin-top: 5%;
      text-align: center;
      .btn_Prex {
        border-color: #68bb97;
        color: #61bb96;
      }
      .btn_next {
        background-color: #68bb97;
        border-color: #68bb97;
        color: #fff;
      }
    }
    .coachsize {
      font-size: 20px;
    }
    .coachsize:before {
      content: '';
      display: inline-block;
      width: 5px;
      height: 25px;
      vertical-align: middle;
      background: #68bb97;
      margin: 0 5px;
    }
    .Urlbox {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      .itemurl {
        width: 30%;
        margin-bottom: 10px;

        img {
          width: 100%;
        }
      }
    }
    .Urlbox:after {
      content: '';
      width: 30%;
    }
    .title-btn-whole {
      display: inline-block;
      width: 41px;
      height: 40px;
      line-height: 40px;
      margin: 16px 0px 0 8px;
      text-align: center;
      border-radius: 35px;
      cursor: pointer;
      border: 1px solid #ccc;
      background: #fff;
      font-size: 16px;
    }
    .current-answer {
      border-color: #357bee;
      color: #ffff;
      background-color: #357bee;
    }
  }
  .page_right {
    width: 30%;
    background: #fff;
    .List-topics {
      margin-top: 20px;
      text-align: center;
      .menu {
        font-size: 18px;
      }
    }
  }
}

.size {
  font-size: 18px;
}
.Tabsbox {
  font-size: 18px;
  line-height: 40px;
  ul {
    display: flex;
  }

  ul li {
    width: 100px;
    font-size: 18px;
    margin-right: 30px;
    cursor: pointer;
    text-align: center;
  }

  .active {
    width: 100px;
    color: #fff;
    background: #68bb97;
    text-align: center;
    border-radius: 20px;
  }
}
.swiper_list {
  width: 100%;
  display: flex;
  margin: 0 15px 15px 15px;
  .tea-analusis {
    position: relative;
    width: 85%;
    height: auto;
    padding: 10px;
    background: #ffff;
    border-radius: 5px;
  }
  .line_border {
    width: 97%;
    height: 10px;
    position: absolute;
    border: 1px solid #e6e6e6;
    background: #e6e6e6;
    top: 55%;
  }
  .left-arrow,
  .right-arrow {
    position: absolute;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: center;
    bottom: -25%;

    z-index: 2;
    .arrow-img {
      &:hover {
        background: #68bb97;
      }
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #dadada;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      i {
        background: url('../../../assets/teacher/IntelligentCorrection/icon-arrow.png') no-repeat center center;
        width: 16px;
        height: 16px;
      }
    }
  }
  .left-arrow {
    left: 8px;
    .arrow-img {
      transform: rotate(180deg);
    }
  }
  .right-arrow {
    right: 6px;
  }
  .imgbox {
    width: 20%;
    font-size: 18px;
    padding: 10px;
    position: relative;
    img {
      width: 30px;
      right: 70%;
      top: 43%;
      position: absolute;
    }
    span {
      margin-left: 10px;
      right: 5%;
      top: 43%;
      position: absolute;
    }
  }
}
</style>
