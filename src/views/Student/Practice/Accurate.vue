<template>
  <div class="task-center clearfix" style="margin:0 auto;">
    <div class="ask-catalog">
      <span class="catalog_header">
        <p class="pc_font_size18 font_size20 ipad_font_size22">章节目录</p>
      </span>
      <a-tree checkable :tree-data="treeData" :replace-fields="replaceFields" @select="onSelect" @check="onCheck" />
    </div>
    <div class="task-info">
      <div class="info_header">
        <!-- <span class="info_title">辅导统计</span> -->
        <span class="info_title pc_font_size18 font_size20 ipad_font_size22">精准解析（教师辅导统计）</span>
        <el-divider></el-divider>
        <span
          class="Time_span"
          v-for="(item, index) in TimeList"
          @click="ChangeStatus(item, index)"
          :class="{ Status: Page_num == index }"
          :key="index"
          >{{ item.label }}</span
        >
      </div>
      <div class="info_content" v-if="PaperList.length != 0">
        <div class="info_item" v-for="(item, index) in PaperList" :key="index">
          <span style="color: #464646">{{ item.ChapterName }}</span>
          <br />
          <span>{{ item.PaperTitle }}</span>
          <br />
          <span>已辅导{{ item.CoachCount }}题</span>
          <br />
          <span>辅导时间:{{ item.CoachTime }}</span>
          <br />
          <span>发布时间:{{ item.CreateTime }}</span>
          <br />
          <el-button @click="RewiveCoach(item)">查看辅导</el-button>
        </div>
      </div>
      <div class="block">
        <a-pagination :default-current="paperIndex" :total="this.total" @change="onPaging" hideOnSinglePage />
      </div>
      <div class="info_data" v-if="PaperList.length == 0">
        <img src="@/assets/lack/暂无搜索记录.png" alt="" />
        <br />
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.chapterTree()
    this.GetStudentCoachPaperList()
  },
  data() {
    return {
      Page_num: 0,
      paperIndex: 1,
      total: 0,
      Time: '',
      checkedKeys: '',
      selectedKeys: [],
      treeData: [],
      PaperList: [],
      replaceFields: {
        children: 'Second',
        title: 'ChapterName',
        key: 'ChapterId',
      },
      TimeList: [
        {
          value: '',
          label: '全部',
        },
        {
          value: '7',
          label: '近一周',
        },
        {
          value: '14',
          label: '近两周',
        },
        {
          value: '30',
          label: '近一个月',
        },
      ],
    }
  },
  filters: {},
  watch: {},
  methods: {
    // 章节数
    async chapterTree() {
      const res = await this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', {
        userid: localStorage.getItem('UserId'),
        IsWeek: 1,
      })
      this.treeData = res.data.Data
    },
    // 点击复选框触发
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys.join(',')
      this.paperIndex = 1
      this.GetStudentCoachPaperList()
    },
    // 点击树节点触发
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    ChangeStatus(item, index) {
      this.Page_num = index
      this.Time = item.value
      this.GetStudentCoachPaperList()
    },

    onPaging(pageNum) {
      this.paperIndex = pageNum
      this.GetStudentCoachPaperList()
    },
    async GetStudentCoachPaperList() {
      let Platform = localStorage.getItem('PL_newP');
      const params = {
        UserId: localStorage.getItem('UserId'),
        PageNo: this.paperIndex,
        ChapterId: this.checkedKeys,
        Time: this.Time,
        Platform
      }
      const res = await this.$uwonhttp.post('/Report/StudentReport/GetStudentCoachPaperList', params)
      if (res.data.Success) {
        this.PaperList = res.data.Data.Items
        this.total = this.PaperList.length
      }
    },

    RewiveCoach(item) {
      this.$router.push({
        path: '/Student/Practice/Coach',
        query: {
          PaperId: item.PaperId,
          PaperTitle: item.PaperTitle,
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
@media (min-width: 1600px) {
  .ant-col-xxl-5 {
    width: 100% !important;
  }
}
.task-center {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.ask-catalog {
  width: 16%;
  .catalog_header {
    display: block;
    background: #61bb96;
    padding: 6px 0;
    border-radius: 5px;
    p {
      //font-size: 19px;
      text-align: center;
      color: #fff;
    }
  }
  /deep/.ant-tree-title {
    font-size: 20px;
    font-weight: 700;
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li {
    margin: 0;
    padding: 10px 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }
}
.task-info {
  width: 82%;
  .info_header {
    padding: 10px;
    border-radius: 5px;
    background: #fff;
    .info_title {
      font-size: 19px;
    }
    .Time_span {
      font-size: 18px;
      margin-right: 40px;
      color: #747474;
      cursor: pointer;
    }
    .Status {
      color: #61bb96;
      cursor: pointer;
    }
  }
  .info_content {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .info_item {
      width: 32%;
      padding: 9px 15px;
      background: #fff;
      line-height: 30px;
      font-size: 17px;
      border-radius: 5px;
      margin-bottom: 10px;
      /deep/.el-button {
        margin-left: 28%;
        width: 120px;
        background: #61bb96;
        border-collapse: #61bb96;
        color: #fff;
      }
    }
  }
  .info_content:after {
    content: '';
    width: 32%;
  }
  .info_data {
    width: 100%;
    height: 70vh;
    margin-top: 10px;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }
}

// 筛选菜单
.screen-menu {
  /deep/.ant-select-selection--single {
    border: none;
    background-color: #fff;
  }
}
/deep/.el-divider--horizontal {
  margin: 10px 0;
}
</style>
