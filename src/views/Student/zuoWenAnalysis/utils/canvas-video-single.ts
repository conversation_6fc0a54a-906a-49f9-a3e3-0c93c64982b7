import dayjs from 'dayjs';
import { PEColorMap } from './colorMap';
/**
 * 播放速度
 */
export const PLAY_SPEED_MAP = new Map([
  ['1', 15],
  ['2', 20],
  ['4', 40],
  ['8', 60]
])
/**单题模式坐标偏移量 */
const OFFSET = 60
interface Context {
  context: CanvasRenderingContext2D
  ImageInfo: ImageInfo
  Image: HTMLImageElement
  baseImage: HTMLImageElement
  cutY: number
  cutHeight: number
  offset: number
  cutX: number
  cutWidth: number
  drawTarget: string
}
class Track {
  width: number = 0
  height: number = 0
  cutX: number = 0
  cutY: number = 0
  cutWidth: number = 0
  cutHeight: number = 0
  context: CanvasRenderingContext2D
  Image: HTMLImageElement
  baseImage: HTMLImageElement
  pointList: Point[] = []
  offset: number
  drawTarget: string = ''
  constructor(contextInfo: Context) {
    const { context, ImageInfo, Image, baseImage, cutY, cutHeight, offset, cutX, cutWidth, drawTarget } = contextInfo
    this.context = context
    this.width = ImageInfo.width
    this.height = ImageInfo.height
    this.Image = Image
    this.baseImage = baseImage
    this.cutY = cutY
    this.cutX = cutX
    this.cutHeight = cutHeight
    this.cutWidth = cutWidth
    this.offset = offset
    this.drawTarget = drawTarget
  }
  //初始化
  init(beforepointList?: Point[]) {
    this.pointList = beforepointList || []
    this.clear()
    if (this.drawTarget != 'comments')
      this.context.drawImage(
        this.baseImage,
        this.cutX,
        this.cutY,
        this.cutWidth,
        this.cutHeight,
        this.cutX,
        this.offset,
        this.cutWidth,
        this.cutHeight
      )
  }
  /**
   * 带笔的动画步骤
   * @param start
   * @param end
   */
  drawPoint(start: Point, end: Point) {
    this.context.clearRect(0, 0, this.width, this.height)
    this.context.drawImage(
      this.baseImage,
      this.cutX,
      this.cutY,
      this.cutWidth,
      this.cutHeight,
      this.cutX,
      this.offset,
      this.cutWidth,
      this.cutHeight
    )
    if (this.pointList.length) this.pointList.push(end)
    else this.pointList.push(start, end)

    this.context.beginPath()
    this.context.moveTo(this.pointList[0].X, this.pointList[0].Y)
    this.pointList.forEach(({ X, Y }, i) => {
      const { Type } = this.pointList[i - 1] || {} //上一个点为结束点
      if (Type === 2) {
        this.context.moveTo(X, Y)
      }
      this.context.lineTo(X, Y)
    })
    const length = this.pointList.length
    const { X, Y } = this.pointList[length - 1]
    this.context.drawImage(this.Image, X, Y - 23, 22, 23)
    this.context.stroke()
  }

  /**
   * 最后清理笔
   */
  clearEditIcon() {
    this.context.clearRect(0, 0, this.width, this.height)
    this.context.drawImage(
      this.baseImage,
      this.cutX,
      this.cutY,
      this.cutWidth,
      this.cutHeight,
      this.cutX,
      this.offset,
      this.cutWidth,
      this.cutHeight
    )
    if(!this.pointList.length) return false
    this.context.beginPath()
    this.context.moveTo(this.pointList[0].X, this.pointList[0].Y)
    this.pointList.forEach(({ X, Y }, i) => {
      const { Type } = this.pointList[i - 1] || {} //上一个点为结束点
      if (Type === 2) this.context.moveTo(X, Y)
      // const last_p = i > 1 ? this.pointList[i - 1] : { X: 0, Y: 0 }
      // let g = Math.abs(last_p.X + last_p.Y - (X + Y))
      // if (g > 30) this.context.moveTo(X, Y)
      this.context.lineTo(X, Y)
    })
    this.context.stroke()
  }

  clear() {
    this.context.clearRect(0, 0, this.width, this.height)
  }
}

/**
 * 自定义视频播放器
 */
export default class CanvasVideo {
  canvas: HTMLCanvasElement
  context: CanvasRenderingContext2D
  imageInfo: ImageInfo
  baseImage: HTMLImageElement | null = null
  editImage: HTMLImageElement | null = null
  studentPenLog: Point[]
  teacherPenLog: Point[]
  fps: number = 60 //设置动画帧率
  track: Track | null = null
  currentPoint: number = 0
  animate: number = 0
  cutX: number = 0 //源图像的裁剪区原点横坐标
  cutY: number = 0 //源图像的裁剪区原点纵坐标
  cutWidth: number = 0 //源图像的裁剪区宽度
  cutHeight: number = 0 //源图像的裁剪区高度
  playing: boolean = false //播放状态
  startTime: number = new Date().getTime()
  paperImage: PaperImage = {}
  offset: number = 0
  $store: any;
  updateTimeCallBack: (progress: number) => void = () => {} //时时更新进度
  endedCallBack: () => void = () => {} //播放结束回调
  constructor(
    imageInfo: ImageInfo,
    studentPenLog: Point[],
    teacherPenLog: Point[],
    canvasDom: HTMLCanvasElement,
    fps?: number
  ) {
    this.canvas = canvasDom
    this.context = this.canvas.getContext('2d') as CanvasRenderingContext2D
    //优化去除锯齿状
    const devicePixelRatio = window.devicePixelRatio || 1
    // 设置高清画布
    this.canvas.width = imageInfo.width * devicePixelRatio
    this.canvas.height = imageInfo.height * devicePixelRatio
    // 修改缩放比，在高分辨率屏幕上仍然能够以预期的尺寸绘制图形，且在保留清晰度的同时防止模糊
    this.context.scale(devicePixelRatio, devicePixelRatio)
    this.context.imageSmoothingEnabled = true
    //以上
    this.imageInfo = imageInfo
    this.cutWidth = imageInfo.width
    this.studentPenLog = studentPenLog
    this.teacherPenLog = teacherPenLog || []
    this.fps = fps || 60
  }
  //初始化
  init(options: Options) {
    return new Promise(async (resove, reject) => {
      try {
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height) //清理上一次
        this.pause()
        this.currentPoint = 0
        const { 
          ImageInfo, 
          studentPenLog, 
          TchPenLogList = [], 
          DZPenLogList = [], 
          cutPoints = [], 
          StudentComments = [], 
          drawTarget = '', 
          cutDown = true ,
          arcList = [],
          illFormedLineList = [],
          beautifulLineList = []
        } = options || {}
        if (ImageInfo) this.imageInfo = ImageInfo
        const { ImgUrl } = this.imageInfo
        this.canvas.width = this.imageInfo.width
        this.canvas.height = this.imageInfo.height
        this.baseImage = this.paperImage[ImgUrl] || (await imageLoad(ImgUrl))
        this.paperImage[ImgUrl] = this.baseImage
        /** 设置剪裁 */
        const [leftTop, rightTop, rightDown, leftDown] = cutPoints as Point[]
        this.cutY = leftTop?.Y || 0
        this.cutX = leftTop?.X || 0
        this.cutWidth = (rightTop?.X || 0) - (leftTop?.X || 0) || this.imageInfo.width
        this.cutHeight = (rightDown?.Y || 0) - (rightTop?.Y || 0) || this.imageInfo.height
        if (options) this.studentPenLog = JSON.parse(JSON.stringify(studentPenLog))
        this.offset = options ? OFFSET : 0
        this.studentPenLog.forEach(item => {
          item.Y = item.Y - this.cutY + this.offset
          // item.Y = item.Y - this.cutY 

        })
       
        //绘制互评数据时候不需要背景
          if (!cutDown) {
            // 实时绘制笔迹不进行裁减
            this.context.drawImage(this.baseImage, this.cutX, this.cutY, this.cutWidth, this.cutHeight)
          } else {
            // 剪裁区域
            if (cutPoints.length) this.canvas.height = this.cutHeight + 2 * OFFSET
            this.context.drawImage(
              this.baseImage,
              this.cutX,
              this.cutY,
              this.cutWidth,
              this.cutHeight,
              this.cutX,
              this.offset,
              this.cutWidth,
              this.cutHeight
            )
          }
        this.context.strokeStyle = '#0063D7' //设置笔的颜色
        this.context.lineWidth = 4 //设置笔迹线条的宽度
        this.context.lineCap = 'round' // 绘制笔迹圆角 属性设置或返回线条末端线帽的样式
        this.context.lineJoin = 'round' // 设定了两线段连接处所显示的模样
        if (options) {
          // await this.drawPint(this.context, this.studentPenLog) //单题模式 初始展示
          await this.drawPint(this.context, this.studentPenLog) //单题模式 初始展示
          this.drawTeacherPint(
            this.context,
            JSON.parse(JSON.stringify(TchPenLogList)).map((item: { Y: number }) => {
              item.Y = item.Y - this.cutY + this.offset
              return item
            })
          )
          // 绘制批改圆
          this.drawCorrectionMarkPint(this.context,arcList)
          // 绘制批改线 病句
          this.drawCorrectIllFormedPint(this.context,illFormedLineList)
          // 绘制批改线 佳句
          this.drawCorrectBeautifulPint(this.context,beautifulLineList)
          this.drawCorrectPint(
            this.context,
            JSON.parse(JSON.stringify(DZPenLogList)).map((item: { Y: number,X:number }) => {
              item.Y = item.Y - this.cutY + this.offset 
              return item
            })
          )
          if (StudentComments.length > 0) {
            StudentComments.forEach((S: StudentComments) => {
              let PenLogList = JSON.parse(JSON.stringify(S.PenLogList)) || []
              this.drawStudentComments(this.context, PenLogList!.map((item: { Y: number })  => {
                item.Y = item.Y - this.cutY + this.offset
                return item
              }), S.StudentNo ?? '')
            })
          }
        }
        const src = require('@/assets/equipment/edit.png')
        const editImage = this.editImage || (await imageLoad(src))
        this.editImage = editImage
        let contextInfo: Context = {
          context: this.context,
          ImageInfo: this.imageInfo,
          Image: this.editImage,
          baseImage: this.baseImage,
          cutY: this.cutY,
          cutHeight: this.cutHeight,
          offset: this.offset,
          cutX: this.cutX,
          cutWidth: this.cutWidth,
          drawTarget: drawTarget
        }
        this.track = new Track(contextInfo)
        const base64URL = this.canvas.toDataURL('image/png')
        resove(base64URL)
      } catch (err) {
        console.log(err)
        reject('error')
      }
    })
  }
  // 单题裁剪
  singleClipping(options: Options) {
    return new Promise(async (resolve, reject) => {
      const { ImageInfo, cutPoints = [] } = options || {}
      if (!ImageInfo!.ImgUrl || !cutPoints.length) {
        reject(new Error('opts params Error!'))
      }
      const src = ImageInfo!.ImgUrl
      const img: HTMLImageElement = new Image()
      img.setAttribute('crossOrigin', 'Anonymous')
      img.src = src
      const _this = this
      img.onload = function () {
        const canvas: HTMLCanvasElement = document.createElement('canvas')
        const ctx: CanvasRenderingContext2D = canvas.getContext('2d') as CanvasRenderingContext2D
        canvas.width = _this.imageInfo.width
        canvas.height = _this.imageInfo.height
        const [leftTop, rightTop, rightDown, leftDown] = cutPoints as Point[]
        const cutY = leftTop?.Y || 0
        const cutX = leftTop?.X || 0
        const cutWidth = (rightTop?.X || 0) - (leftTop?.X || 0) || _this.imageInfo.width
        const cutHeight = (rightDown?.Y || 0) - (rightTop?.Y || 0) || _this.imageInfo.height
        canvas.height = cutHeight + 2 * OFFSET
        const newCutY = cutY + 1 * OFFSET
        ctx.drawImage(img, cutX, newCutY, cutWidth, cutHeight, cutX, _this.offset, cutWidth, cutHeight)
        const url = canvas.toDataURL('image/png')
        resolve(url)
      }
      img.onerror = function (err) {
        reject(err)
      }
    })
  }

  //设置当前播放位置
  setCurrentPoint(beforepointList: Point[], currentIndex: number) {
    this.currentPoint = currentIndex - 1
    const list: Point[] = JSON.parse(JSON.stringify(beforepointList))
    list.forEach(item => {
      item.Y = item.Y - this.cutY + this.offset
    }) //移除掉剪裁高度
    this.track?.init(list)
    const start = { ...this.studentPenLog[this.currentPoint] }
    const end = { ...this.studentPenLog[this.currentPoint + 1] }
    this.track?.drawPoint(start, end)
    if (this.currentPoint >= this.studentPenLog.length - 1) this.track?.clearEditIcon()
  }
  //设置播放速度
  setPlaySpeed(val: string) {
    this.fps = PLAY_SPEED_MAP.get(val) || 15
  }
  play() {
    if (!this.currentPoint) this.track?.init()
    if (this.currentPoint >= this.studentPenLog.length - 1) {
      cancelAnimationFrame(this.animate)
      this.track?.clearEditIcon()
      this.endedCallBack()
      this.updateTimeCallBack(this.studentPenLog.length)
      this.currentPoint = 0
      this.playing = false
    } else {
      this.playing = true
      this.updateTimeCallBack(this.currentPoint)
      this.animate = requestAnimationFrame(this.play.bind(this))
      let fpsInterval = 1000 / this.fps
      let now = new Date().getTime()
      let elapsed = now - this.startTime
      if (elapsed > fpsInterval) {
        this.startTime = now - (elapsed % fpsInterval)
        const start = { ...this.studentPenLog[this.currentPoint] }
        const end = { ...this.studentPenLog[this.currentPoint + 1] }
        this.track?.drawPoint(start, end)
        this.currentPoint++
      }
    }
  }
  payReset(){
    cancelAnimationFrame(this.animate)
    this.track?.clearEditIcon()
    this.endedCallBack()
    this.updateTimeCallBack(0)
    this.currentPoint = 0
    this.playing = false
  }

  pause() {
    this.playing = false
    cancelAnimationFrame(this.animate)
  }
  //时时更新播放进度
  timeupdate(callBack: (progress: number) => void) {
    this.updateTimeCallBack = callBack
  }
  //播放结束的回调
  ended(callBack: () => void) {
    this.endedCallBack = callBack
  }

  clearBoard() {
    this.track?.init()
  }
  /**
   * @description: 绘制学生笔迹
   * @description: 不能使用贝塞尔曲线，对于中文或者字母A和数学公式就得不偿失，字迹还原度不高取决于点阵笔采集点的坐标决定
   * @param {CanvasRenderingContext2D} context
   * @param {Point} studentPenLog
   */
  private drawPint(context: CanvasRenderingContext2D, studentPenLog: Point[]) {
    const splitStudentPenLog: Point[][] = splitByTwo(studentPenLog)
    drawLinesWithDistinction(context, splitStudentPenLog, '#0063D7')
  }

  /**
   * @description: 绘制教师笔迹
   * @param {CanvasRenderingContext2D} context
   * @param {Point} teacherPenLog
   */
  private drawTeacherPint(context: CanvasRenderingContext2D, teacherPenLog: Point[]) {
    const splitTchPenLog: Point[][] = splitByTwo(teacherPenLog)
    drawLinesWithDistinction(context, splitTchPenLog, '#ff0000')
    context.strokeStyle = '#0063D7'
  }
  /**
   * @description: 绘制订正笔记
   * @param {CanvasRenderingContext2D} context
   * @param {Point} dzPenLog
   */
  private drawCorrectPint(context: CanvasRenderingContext2D, dzPenLog: Point[]) {
    const splitTchPenLog: Point[][] = splitByTwo(dzPenLog)
    drawLinesWithDistinction(context, splitTchPenLog, '#f4a460')
    context.strokeStyle = '#0063D7'
  }
  /**
   * @description: 绘制圆// 多个圈
   * @param {CanvasRenderingContext2D} context
   * @return {*}
   */  
  private drawCorrectionMarkPint(context: CanvasRenderingContext2D,arcList:Arc[]) {
    if(!arcList.length) return false
    console.log(arcList)
    arcList.forEach(item=>{
      context.beginPath()
      context.arc(item.x,item.y,50,0,2*Math.PI);
      context.strokeStyle = '#ff0000'
      context.stroke()
    })
  }

    /**
   * @description: 绘制批改线 病句
   * @param {CanvasRenderingContext2D} context
   * @return {*}
   */  
  private drawCorrectIllFormedPint(context: CanvasRenderingContext2D,lineList:Line[][]) {
   
    if(!lineList.length) return false
    context.strokeStyle = 'red'
    context.lineWidth = 6 //设置笔迹线条的宽度
    for (let i = 0; i < lineList.length; i++) {
      const pointGroup = lineList[i]
      context.beginPath()
      if (pointGroup.length === 0) return
      context.moveTo(pointGroup[0].x, pointGroup[0].y)
      for (let j = 1; j < pointGroup.length; j++) {
        const currentPoint = pointGroup[j]
        context.lineTo(currentPoint.x, currentPoint.y)
      }
      context.stroke()
    }
  }
      /**
   * @description: 绘制批改线 佳句
   * @param {CanvasRenderingContext2D} context
   * @return {*}
   */  
      private drawCorrectBeautifulPint(context: CanvasRenderingContext2D,lineList:Line[][]) {
        // return false
        if(!lineList.length) return false
        context.strokeStyle = '#FFA500'
        context.lineWidth = 6 //设置笔迹线条的宽度
        // 得到x轴最大和最小的差值
        const minMaxX = findMinMaxX(lineList)
        const newLineList:Line[][] =[]
        minMaxX.map((item, index) => {
          const interval = 5;
          newLineList[index] = [];
          const points = lineList[index];
          for (let x = item.minX; x <= item.maxX; x += interval) {
            // 使用线性插值计算 y 坐标
            let y = 0;
            for (let i = 0; i < points.length - 1; i++) {
              const p1 = points[i];
              const p2 = points[i + 1];
              if (x >= p1.x && x <= p2.x) {
                y = p1.y + ((x - p1.x) / (p2.x - p1.x)) * (p2.y - p1.y);
                break;
              }
            }
            newLineList[index].push({ x, y });
          }
        });
        const amplitude = 10; // 波浪高度
        const period = 50;  // 波浪周期

        for (let i = 0; i < newLineList.length; i++) {
          const pointGroup = newLineList[i]
          context.beginPath()
          if (pointGroup.length === 0) return
          context.moveTo(pointGroup[0].x, pointGroup[0].y)
          for (let j = 1; j < pointGroup.length; j++) {
            const currentPoint = pointGroup[j]
            // context.lineTo(currentPoint.x, currentPoint.y)
            // 波浪线
            currentPoint.y = currentPoint.y + amplitude * Math.sin(currentPoint.x / period * 2 * Math.PI);
            context.lineTo(currentPoint.x, Math.round(currentPoint.y))
          }
          context.stroke()
        }
      }

  /**
 * @description: 绘制生生互评笔迹
 * @param {CanvasRenderingContext2D} context
 * @param {Point} PenLogList
 */
  private drawStudentComments(context: CanvasRenderingContext2D, PenLogList: Point[], StudentNo: string) {
    const splitTchPenLog: Point[][] = splitByTwo(PenLogList)
    drawLinesWithDistinction(context, splitTchPenLog, PEColorMap.get(StudentNo) ?? '')
    context.strokeStyle = '#0063D7'
  }
}
/**
 * @description: 绘制点与点之间的连线，并根据Type属性进行区分
 * @param {CanvasRenderingContext2D} context - HTML5画布的绘图上下文
 * @param {Array<Array<Point>>} points - 点的二维数组，每个内部数组代表一组点
 * @param {string} color - 线条的颜色
 * @return {void}
 */
function drawLinesWithDistinction(context: CanvasRenderingContext2D, points: Point[][], color: string): void {
  context.lineWidth = 4 //设置笔迹线条的宽度
  context.strokeStyle = color
  for (let i = 0; i < points.length; i++) {
    const pointGroup = points[i]
    context.beginPath()
    if (pointGroup.length === 0) return
    context.moveTo(pointGroup[0].X, pointGroup[0].Y)
    for (let j = 1; j < pointGroup.length; j++) {
      const currentPoint = pointGroup[j]
      context.lineTo(currentPoint.X, currentPoint.Y)
    }
    context.stroke()
  }
}

/**
 * 图片加载
 * @param src
 * @returns
 */

function imageLoad(src: string): Promise<HTMLImageElement> {
  const img: HTMLImageElement = new Image()
  img.setAttribute('crossOrigin', 'Anonymous')
  // img.src = `${src}?time=${dayjs().valueOf()}` //防止缓存
  img.src = src
  return new Promise((resove, reject) => {
    img.onload = () => {
      resove(img)
    }
    img.onerror = err => {
      reject(err)
    }
  })
}
/**
 * @description: 加工一维数组以Type为2转换为2维数组
 * @param {Point} arr
 * @return {*}
 */

function splitByTwo(arr: Point[]) {
  if (arr.length === 0) return []
  if (arr.some(item => item.Type != 2)) {
    const lastItem = arr.pop()!
    arr.push({ ...lastItem, Type: 2 })
  }
  let temp: Point[] = []
  return arr.reduce((accumulator: Point[][], currentValue: Point) => {
    temp.push(currentValue)
    if (currentValue.Type === 2) {
      accumulator.push(temp)
      temp = []
    }
    return accumulator
  }, [])
}

/**
 * 寻找每组线段中的最小和最大X坐标及其首个Y坐标
 * 该函数主要用于处理一组线段，找出每组线段中的最小和最大X坐标，以及它们共有的首个Y坐标
 * 这对于在图形处理或数据分析中确定线段的水平范围特别有用
 * 
 * @param lineList 一个二维数组，包含多组线段，每组线段由多个点组成
 * @returns 返回一个对象数组，每个对象包含每组线段的最小X坐标（minX）、最大X坐标（maxX）和首个Y坐标（y）
 */
function findMinMaxX(lineList:Line[][]) {
  return lineList.map(points => {
    let minX = points[0].x;
    let maxX = points[0].x;
    let y = points[0].y;
    points.forEach(point => {
      if (point.x < minX) {
        minX = point.x;
      }
      if (point.x > maxX) {
        maxX = point.x;
      }
    });

    return { minX, maxX, y };
  });
}

// 原始图片信息
interface ImageInfo {
  width: number /**图片宽度 */
  height: number /**图片高度 */
  ImgUrl: string /**图片URL */
}
// 学生笔迹
interface Point {
  Type?: number
  X: number
  Y: number
}
interface PaperImage {
  [ImgUrl: string]: HTMLImageElement
}

interface Arc {
  x: number,//圆的中心的 x 坐标。
  y: number,//圆的中心的 y 坐标。
  r: number//圆的半径。
}
interface Line {
  x:number,
  y:number,
}
interface Options {
  DZPenLogList?: Point[]
  ImageInfo?: ImageInfo
  studentPenLog?: Point[]
  cutPoints?: Point[]
  TchPenLogList?: Point[]
  cutDown?: Boolean //是否裁减
  StudentComments?: StudentComments[],
  arcList?:Arc[],// 画圈
  illFormedLineList?:Line[][],//病句
  beautifulLineList?:Line[][],// 佳句
  drawTarget?: string,
}

interface StudentComments {
  StudentName?: string
  StudentNo?: string
  StudentId?: string
  PenLogList?: Point[]
}


