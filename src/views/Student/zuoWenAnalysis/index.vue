<template>
  <!--
  v-loading="loading" 魏世飞
  -->
  <div class="zuoWenAnalysis" >
    <div class="goReturn">
      <p @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>返回</p>
      <span>{{ paperTitle }}</span>
      <div class="xuekeStyle" :style="{ 'background-color': bgColog[SubjectIdName.Id + 'bg'], color: bgColog[SubjectIdName.Id] }">
        {{ SubjectIdName.name }}
      </div>
    </div>
    <div class="itemContent">
      <div class="headline">作文</div>
      <div class="questionStem" v-html="paperItem.Title" v-katex></div>
      <div class="IMAnswer">
        <p>我的作答：</p>
        <el-image ref="Image" :src="dialogImageUrl" :preview-src-list="[dialogImageUrl]"></el-image>
      </div>
      <!--        跳转AI会话页面       -->
      <div class="goAIhuihua">
        <img src="@/assets/AIImg/import.png"/>
        <div class="_text">
          <p>HI,{{ userName }}同学，我是你的AI智能老师</p>
          <p>你可以向我们咨询这道题的相关解题思路哦！</p>
          <el-button @click="goAIPagesAndComponents">立即提问</el-button>
        </div>
      </div>
      <!--        跳转AI会话页面       -->
    </div>
    <div class="AIshiBie">
      <zuowen-a-i></zuowen-a-i>
    </div>
    <!--  前端整卷笔迹使用canvas元素  -->
    <canvas v-show="false" id="canvas-video" ref="canvasVideo" style="width: 100%"></canvas>
  </div>
</template>

<script>
import ExamFormatCommon from '@/common/ExamFormatCommon'
// AI分析
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import zuowenAI from '@/views/Student/Exam_Student/components/zuowenAI'
import CanvasVideoSingle, { PLAY_SPEED_MAP } from '@/views/Student/zuoWenAnalysis/utils/canvas-video-single'
import { Session } from '@/utils/storage'
export default {
  name: 'Index',
  data () {
    return {
      dialogImageUrl: '',
      bgColog: {},
      SubjectIdName: {},
      paperTitle: '',
      paperItem: {},
      userName: localStorage.getItem('userName'),
      // 整卷实例
      canvasVnode: null, // canvas 实例
      loading: true
    }
  },
  components: {
    zuowenAI
  },
  created () {
    this.bgColog = CorrespondingColor()
    const { Title, PaperId, SubjectId, SubjectName } = this.$route.query
    this.SubjectIdName = {
      Id: SubjectId,
      name: SubjectName
    }
    this.paperTitle = Title
    this.GetAuditPaperItemsList(PaperId)
  },
  methods: {
    // 获取试卷详情
    async GetAuditPaperItemsList (PaperId) {
      const res = await this.$http.get(`/Paper/Exam_Item/GetAuditPaperItemsList?paperId=${PaperId}`)
      if (res.Success) {
        this.paperItem = res.Data.find((ite) => { return ite.TypeId === '26' })
        this.paperItem.Title = ExamFormatCommon.format_space(this.paperItem.Title)
        this.GetMDPaperSectionInfos({ ItemId: this.paperItem.ItemId, PaperId })
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 获取笔迹点位
    async GetMDPaperSectionInfos ({ ItemId, PaperId }) {
      const reqData = {
        ItemId,
        PaperId,
        StudentIds: localStorage.getItem('UserId')
      }
      const { data: res } = await this.$uwonhttp.post('/MDPen/MDPen/GetMDPaperSectionInfos', reqData)
      // console.log(res)
      if (res.Success) {
        this.GetHaoweilaiX({ PaperId, PenOfPoint: res.Data[0] })
      } else {
        this.$message.error(res.Msg)
      }
    },
    // 作文批注点位
    // 好未来批改
    async GetHaoweilaiX ({ PaperId, PenOfPoint }) {
      const params = {
        paperId: PaperId,
        studentId: localStorage.getItem('UserId')
      }
      this.$uwonhttp.get('/MDPen/MDPen/GetHaoweilaiX', { params }).then(res => {
        if (res.data.Success && res.data.Data) {
          console.log(res)
          const { writing_highlight, gec_result } = res.data.Data
          const arc = gec_result.grammar_mistake_info?.info
          const actions = writing_highlight.description?.info?.action
          const action = writing_highlight.rhetorical?.info?.analogy
          // 病句
          const missing_component = gec_result.grammar_sentence_mistake_info?.missing_component
          console.log(missing_component)
          let annotationCoords = []
          let annotationCoord = []
          const arcList = []
          let missing_componentList = []
          if (actions) {
            annotationCoords = actions.flatMap(item => item.location_info.map(sub => sub.location.map(k => {
              return { x: k.x, y: k.y + 60 }
            })))
          }
          if (missing_component) {
            missing_componentList = missing_component.flatMap(item => item.location_info.map(sub => sub.location.map(k => {
              return { x: k.x, y: k.y + 60 }
            })))
            console.log(missing_componentList)
          }
          if (action) {
            annotationCoord = action.flatMap(item => item.location_info.map(sub => sub.location.map(k => {
              return { x: k.x, y: k.y + 60 }
            })))
          }

          if (arc) {
            arc.forEach(item => {
              item &&
              item.details.forEach(sub => {
                arcList.push(sub.location[0])
              })
            })
          }
          const haoweilai = {
            beautifulLineList: [...annotationCoord, ...annotationCoords],
            illFormedLineList: missing_componentList,
            arcList: arcList.map(k => { return { x: k.x, y: k.y + 60 } })
          }
          // console.log(arcList);
          // this.$emit('annotationCoord',{
          //   beautifulLineList:[...annotationCoord,...annotationCoords],
          //   illFormedLineList:missing_componentList,
          //   arcList:arcList.map(k=>{ return { x: k.x, y: k.y +60 } })
          // })
          this.createCanvasInit({ item: PenOfPoint, haoweilai })
        } else {
          // this.$message.error('批改无数据')
          const haoweilai = {
            beautifulLineList: [],
            illFormedLineList: [],
            arcList: []
          }
          this.createCanvasInit({ item: PenOfPoint, haoweilai })
          // this.$emit('annotationCoord',{beautifulLineList:[],arcList:[],illFormedLineList:[]})
        }
      })
    },
    // 初始canvas
    async createCanvasInit ({ item, haoweilai }) {
      const { ImgUrl, ItemSection, width, height } = item
      const { beautifulLineList, illFormedLineList, arcList } = haoweilai
      const imageInfo = {
        ImgUrl,
        width,
        height
      }
      this.canvasVnode?.pause()
      if (!this.canvasVnode) {
        this.canvasVnode = new CanvasVideoSingle(
          imageInfo,
          ItemSection[0].ItemPoints,
          [],
          this.$refs.canvasVideo,
          15
        )
      }
      const canvasImg = await this.canvasVnode.init({ // 获取的整卷笔迹绘制
        DZPenLogList: [],
        ImageInfo: imageInfo,
        cutPoints: [],
        TchPenLogList: [],
        studentPenLog: ItemSection[0].ItemPoints,

        cutDown: true,
        StudentComments: [],
        // 画圈
        arcList,
        // 佳句
        beautifulLineList,
        // 病句
        illFormedLineList
      })
      this.dialogImageUrl = canvasImg
      this.loading = false
      // console.log(this.canvasImg)
    },
    // 跳转AI会话页
    goAIPagesAndComponents () {
      const { GradeId } = Session.get('userInfo')
      const { ItemId } = this.paperItem
      const text = `你将扮演学生的问答助手，帮助学生在作文方面答疑解惑。在解答问题时，需要关联学生的基础信息（如：所在的年级、使用的教材、评分细则）；
        以下是学生的基础信息：
          上海市${GradeId}年级；使用的教材是人教版
          以下是学生的评分细则
          【中心:A:精准把握“我的好朋友”主题核心，立意新颖独特，挖掘出朋友与众不同的品质或两人间独特的情感价值；典型事件紧扣朋友特点和相处细节，生动展现朋友形象，细节真实感人，能深度体现两人真挚友谊；
          情感表达真挚自然，对友谊的感悟深刻，与前文叙事紧密融合，升华主题。人物的外在特点和内在特点紧密结合，可构成一致或反差的逻辑关系。。B:主题符合“我的好朋友”题意，有一定价值导向，如展现朋友的善良、勇敢等常见品质；选材能体现朋友特点，但细节不够丰富，存在部分常见事例；情感表达较自然，有对友谊的感悟，不过与叙事衔接稍显生硬，感悟深度一般。人物的外在特点和内在特点结合一般。。C:基本围绕“我的好朋友”展开，主题偶有偏离，如重点写其他无关内容过多；选材零散，多件事简单罗列或事件描述模糊，难以突出朋友形象；情感表达不清晰，感悟与叙事关联不紧密，可能只是简单提及友谊。人物的外在特点和内在特点结合并不紧密。。D:严重偏离“我的好朋友”主题，选材与朋友无关或关系不大；情感虚假或无情感表达，内容空洞无物。。E:完全脱离主题，不知所云，没有任何与朋友相关的有效内容。。】【结构:A:开头迅速引入朋友，方式巧妙新颖，如用两人的一个有趣约定开篇；结尾有力回应开头，深化友谊主题，不重复前文内容；段落布局合理，重点突出朋友相关内容，过渡自然简洁，详略安排得当，对能突出朋友特点的关键场景进行多角度细致描写。。B:结构符合基本叙事逻辑，有开头、中间叙述和结尾；开头结尾较为常规，段落主次分明，但存在少量冗余信息；详略基本合理。。C:结构较松散，开头结尾不够清晰明确，段落划分不太合理，内容碎片化，重点不突出；详略不当，对朋友的描写和关键事件描述过于简略，背景铺垫冗长。。D:结构混乱，没有清晰的叙事顺序，段落之间缺乏逻辑联系，难以理清文章脉络；几乎没有对重点内容的描写，文章整体较为混乱。。E:完全没有结构可言，内容杂乱无章。。】【语言:A:对朋友的描写充满画面感，运用细节描写，抓住人物的细微之处；修辞手法运用巧妙，比喻、拟人等贴切新颖，为文章增添文采。。B:语句通顺连贯，有对朋友的描写，不过比较笼统，缺乏细节；运用常见修辞手法，虽不新颖但使用恰当，能起到一定表达效果。。C:存在少量语病，但不影响对文章的整体理解；语言平淡，多为简单陈述性语句，对朋友的描写很少，几乎没有运用修辞手法。。D:语病较多，影响语句通顺和表意；语言表达混乱，词不达意，难以通过语言展现朋友形象。。E:语言表述极差，无法正常表意，满篇语病或错别字，严重影响阅读。。】
          在解答时，请遵循以下规则：
          1、充分考虑学生的年级、教材特点以及老师的评分体系来给出有针对性的解答；
          2、回答要丰富、全面，涵盖所有相关要点。
          3、确保解答符合学生的知识水平和认知能力。
          4、当接收到作文图片时，识别图片中的作文内容，仔细阅读作文内容，理解其中心、结构、语言表达等方面，将作文内容与评分体系中的各项标准逐一对照。对作文的各个方面进行分析和评估。`
      Session.set('AITEXT', text)
      const que = {
        PaperId: this.$route.query.PaperId,
        ItemId: ItemId
      }
      this.$router.push({
        path: '/AIPagesAndComponents/index',
        // query: item.AIDialogueCount === 0 ? { text , ...que } : que
        query: { ...que }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import "../../../assets/font/font.css";
.zuoWenAnalysis{
  .goReturn{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background-color: #FFFFFF;
    border-radius: 4px;
    p{
      font-size: 18px;
      cursor: pointer;
    }
    span{
      font-size: 20px;
    }
    .xuekeStyle{
      font-size: 18px;
      padding: 4px 18px;
      border-radius: 4px;
    }
  }
  .itemContent{
    margin-top: 10px;
    background-color: #ffffff;
    padding: 20px;
    .headline{
      font-size: 22px;
    }
    .questionStem{
      font-size: 20px;
      margin: 6px 0;
    }
    .IMAnswer{
      display: flex;
      p{
        font-size: 18px;
        color: #68bb97;
      }
      .el-image{
        width: 120px;
        border: 1px solid #DCDCDCFF;
      }
    }
    // AI会话样式
    .goAIhuihua{
      width: 38.5%;
      position: relative;
      img{
        width: 100%;
      }
      ._text{
        position: absolute;
        right: 60px;
        bottom: 18px;
        p{
          position: relative;
          top: -18px;
          font-size: 22px;
          color: #8DB79CFF;
          font-family: '优设标题黑';
          margin: 0;
          letter-spacing: 1px;
        }
        p:nth-child(2){
          position: relative;
          top: -18px;
        }
        .el-button{
          font-size: 18px;
          background-color: #8DB79CFF;
          color: #FFFFFF;
          font-family: '优设标题黑';
          position: relative;
          top: -16px;
          border: none;
          letter-spacing: 1px;
          padding: 10px 16px;
        }
      }
    }
  }
  .AIshiBie{
    margin-top: 10px;
  }
}
</style>
