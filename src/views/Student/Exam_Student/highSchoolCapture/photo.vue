<template>
  <div class="photoDemo" v-loading="photoDemo" element-loading-spinner="el-icon-loading" element-loading-text="图片上传中...">
    <div class="photoHeader">
      <i class="el-icon-arrow-left" @click="goHuitui"></i>
      <el-button round :disabled="disable" size="small" :style="{background: disable ? '#909399':'#61BB96',color: '#FFFFFF',border: 'none'}" @click="goSubmit">单题校对</el-button>
    </div>
    <div ref="mengban" class="cont">
      <div class="mengban" :style="{width: captureWidth + 'px', height: captureHeight + 'px'}">
        <img class="lftp" src="@/assets/index/lftp.png" />
        <img class="rgtp" src="@/assets/index/rgtp.png" />
        <img class="lfbm" src="@/assets/index/lfbm.png" />
        <img class="rgbm" src="@/assets/index/rgbm.png" />
<!--        <Camera-->
<!--          v-if="canvasOut"-->
<!--          ref="canvasOut"-->
<!--          class="canvasOut"-->
<!--          :videoW='captureWidth'-->
<!--          :videoH="captureHeight"-->
<!--          @refreshDataList="UploadDoPaperPicture">-->
<!--        </Camera>-->
        <Camera
          v-if="canvasOut"
          ref="canvasOut"
          class="canvasOut"
          :style="{width:captureWidth+'px',height:captureHeight+'px'}"
          @refreshDataList="UploadDoPaperPicture">
        </Camera>
        <canvas-masking
          v-if="canvasXY.length > 0"
          :style="{ zIndex: imgTrueFale ? 120 : 100 }"
          ref="canvasMasking"
          class="canvasMasking"
          :origin-width="captureWidth"
          :origin-height="captureHeight"
          :canvasXY="canvasXY">
        </canvas-masking>
        <div class="round" @click="setImage" :style="{ zIndex : imgTrueFale ? 100 : 120}">
          <div class="rou_div"></div>
        </div>
        <div class="img_bg_camera" v-show="imgTrueFale">
          <img :src="photoList[tiveIndex-1]" ref="tximg" class="tx_img" />
        </div>
        <div class="delet" v-show="imgTrueFale" @click="deleteRemove">
          <i class="el-icon-error"></i>
        </div>
      </div>
    </div>
    <div class="footer">
      <ul>
        <li v-for="(item,index) in canvasList" :key="item.Page" :class="{'active':tiveIndex == item.Page}" @click="liClick(item.Page,index)">{{item.Page}}</li>
      </ul>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="46%"
      :before-close="handleClose">
      <div>
        <p>请将题目与作答内容放在框内进行拍照，分割更准确哦！</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-checkbox v-model="checked" @change="checkedBtn">不再提示</el-checkbox>
        <el-button type="primary" @click="dialogVisible = false">知道了</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Camera from '@/views/Student/Exam_Student/highSchoolCapture/Camera'
import canvasMasking from '@/views/Student/Exam_Student/highSchoolCapture/canvasMasking'
export default {
  name: 'Photo',
  data () {
    return {
      captureWidth: 0,
      captureHeight: 0,
      canvasOut: false, // 拍照组件
      photoDemo: false, // 上传加载
      canvasXY: [], // 拍照页上层遮罩XY坐标数据
      canvasList: [], // 底部试卷页码
      photoList: [], // 拍照完成后的图片展示数据
      imgTrueFale: false, // 拍照完成后图片显示
      tiveIndex: 1, // 试卷页码数据
      disable: true, // 单题校对按钮禁用开启状态
      dialogVisible: true, // 刚入拍照页面弹窗提示
      checked: false // 不再提示选择
    }
  },
  watch: {
    $route () {
      console.log('路由')
    },
    photoList (newObl, valObl) {
      this.disable = newObl.some(res => typeof res === 'number')
    }
  },
  created () {
    // 等比a4纸宽高
    this.GetPaperPictureRange()
    if (localStorage.getItem('photoCheck') === undefined) {
      this.dialogVisible = true
    } else {
      this.dialogVisible = !localStorage.getItem('photoCheck')
    }
  },
  mounted () {
    this.captureWH()
    window.addEventListener('resize', this.captureWH, false)
  },
  components: {
    Camera,
    canvasMasking
  },
  methods: {
    huoquyingcun () {
      for (var i = 90; i < 400; i++) {
        if (matchMedia('(resolution: ' + i + 'dpi)').matches) {
          return i
        }
      }
    },
    handleClose () {
      this.dialogVisible = false
    },
    checkedBtn (e) {
      localStorage.setItem('photoCheck', e)
    },
    // 拍照按钮
    setImage () {
      this.$refs.canvasOut.setImage()
    },
    liClick (page, i) {
      if (typeof this.photoList[i] !== 'number') {
        this.imgTrueFale = true
      } else {
        this.imgTrueFale = false
      }
      this.tiveIndex = page
      this.canvasXY = this.canvasList[i].Ranges
    },
    deleteRemove () {
      this.photoList.splice(this.tiveIndex - 1, 1, this.tiveIndex)
      this.imgTrueFale = false
    },
    goHuitui () {
      // this.$confirm('返回后，现有扫描试卷将被清空，确认返回?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   this.$router.go(-1)
      //   this.$refs.canvasOut.CloseCamera()
      // }).catch(() => {
      //   return false
      // })
      this.$router.go(-1)
      this.$refs.canvasOut.CloseCamera()
    },
    captureWH () {
      const width = document.documentElement.clientWidth
      const height = document.documentElement.clientHeight
      if (height < width) {
        this.canvasXY = []
        this.canvasOut = false
      } else {
        this.canvasOut = true
        const time = setTimeout(() => {
          const W = this.$refs.mengban.clientWidth
          const H = this.$refs.mengban.clientHeight
          const domW = W - 100
          const domH = domW * 1.4
          if (domH > H) {
            this.captureWidth = (H - 20) / 1.4
            this.captureHeight = (H - 20)
          } else {
            this.captureWidth = domW
            this.captureHeight = domH
          }
          this.canvasXY = this.canvasList[this.tiveIndex - 1].Ranges
          clearTimeout(time)
        }, 500)
        // this.canvasXY = this.canvasList[this.tiveIndex - 1].Ranges
      }
    },
    // 获取笔迹
    GetPaperPictureRange () {
      this.$uwonhttp.post('Paper/Paper/GetPaperPictureRange', { paperId: this.$route.query.paperId }).then(res => {
        if (res.data.Success) {
          this.canvasList = res.data.Data
          this.canvasXY = res.data.Data[0].Ranges
          const array = []
          res.data.Data.forEach(item => {
            array.push(item.Page)
          })
          this.GetStudentDoPaperPictureInfos(array)
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    },
    // 图片上传
    UploadDoPaperPicture (file) {
      // this.photoTxt = file
      this.photoDemo = true
      const formData = new FormData()
      formData.append('file', file)
      formData.append('PaperId', this.$route.query.paperId)
      formData.append('StudentId', localStorage.getItem('UserId'))
      formData.append('Page', this.tiveIndex)
      this.$uwonhttp.post('Paper/Paper/UploadDoPaperPicture', formData).then(res => {
        this.photoDemo = false
        if (res.data.Success) {
          console.log(res.data.Data.Url)
          this.photoList.splice(this.tiveIndex - 1, 1, res.data.Data.Url)
          this.imgTrueFale = true
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    },
    // 获取整张图片
    GetStudentDoPaperPictureInfos (array) {
      const data = {
        paperId: this.$route.query.paperId,
        studentId: localStorage.getItem('UserId'),
        ImgType: 1
      }
      this.$uwonhttp.post('Paper/Paper/GetStudentDoPaperPictureInfos', data).then(res => {
        if (res.data.Success) {
          res.data.Data.forEach(item => {
            array.splice(array.indexOf(item.Page), 1, item.ImgUrl)
          })
          this.photoList = array
          this.imgTrueFale = array.some(item => typeof item === 'string')
        }
      })
    },
    // 单题校对
    goSubmit () {
      this.$router.push({
        path: '/Student/Exam_Student/highSchoolCapture/photoAnswer',
        query: {
          paperId: this.$route.query.paperId
        }
      })
      this.$refs.canvasOut.CloseCamera()
    }
  }
}
</script>

<style lang="less" scoped>
.photoDemo{
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .photoHeader,.footer{
    background-color: #828282;
  }
  .photoHeader{
    width: 100%;
    //height: 120px;
    height: 5%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 46px;
    i{
      font-size: 50px;
      color: #FFFFFF;
    }
    /deep/.el-button{
      height: 80px;
      font-size: 36px;
    }
  }
  .cont{
    width: 100%;
    height: 88%;
    //background-color: #EBEBEB;
    //padding: 36px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .mengban{
      //width: 100%;
      //height: 100%;
      //width: 941px;
      //height: 1233px;
      position: relative;
      .lftp,.rgtp,.lfbm,.rgbm{
        width: 60px;
        height: 60px;
        position: absolute;
        z-index: 999;
      }
      .lftp{
        left: -6px;
        top: -6px;
      }
      .rgtp{
        right: -6px;
        top: -6px;
      }
      .lfbm{
        left: -6px;
        bottom: -6px;
      }
      .rgbm{
        right: -6px;
        bottom: -6px;
      }
      .canvasMasking{
        position: absolute;
        left: 0;
        top: 0;
        //z-index: 99;
        //z-index: 100;
      }
      //.canvasOut{
      //  position: absolute;
      //  left: 0;
      //  top: 0;
      //  z-index: 88;
      //}
      .round{
        position: absolute;
        left: 46%;
        bottom: 30px;
        border: 4px solid rgba(255, 255, 255, 0.9);
        width: 160px;
        height: 160px;
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        //z-index: 100;
        //z-index: 120;
        .rou_div{
          width: 146px;
          height: 146px;
          background-color: rgba(255, 255, 255, 0.8);
          border-radius: 50%;
        }
      }
      .img_bg_camera{
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        //z-index: 120;
        z-index: 99;
        img{
          width: 100%;
          height: 100%;
        }
      }
      .delet{
        border-radius: 50%;
        overflow: hidden;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 130;
        i{
          //color: #61BB96;
          color: #F56C6C;
          opacity: 0.5;
          font-size: 100px;
        }
      }
    }
  }
  .footer{
    width: 100%;
    //height: 270px;
    height: 8%;
    display: flex;
    justify-content: center;
    ul{
      display: flex;
      align-items: center;
      li{
        width: 100px;
        height: 100px;
        line-height: 100px;
        text-align: center;
        background-color: #FFFFFF;
        margin: 0 10px;
        font-size: 60px;
        border-radius: 20px;
        color: #000000;
      }
      .active{
        background-color: #61bb96 !important;
        color: #FFFFFF !important;
      }
    }
  }
  /deep/.el-loading-mask{
    .el-loading-spinner{
      .el-icon-loading{
        font-size: 100px;
      }
      .el-loading-text{
        font-size: 70px;
      }
    }
  }
  /deep/.el-dialog__wrapper{
    .el-dialog{
      margin-top: 40vh !important;
    }
    .el-dialog__title{
      font-size: 40px;
    }
    .el-dialog__headerbtn{
      font-size: 40px;
    }
    .el-dialog__body{
      font-size: 30px;
    }
    .dialog-footer{
      display: flex;
      justify-content: space-between;
      .el-button{
        font-size: 32px;
      }
      .el-checkbox{
        display: flex;
        align-items: center;
        .el-checkbox__label{
          font-size: 30px;
        }
        .el-checkbox__input{
          zoom: 200%;
        }
      }

    }
  }
}
</style>
