<template>
<div class="photoAnswer">
  <div class="heade pc_font_size20 font_size22 ipad_font_size24">
    <div class="goRetur" @click="goReturn">
      <i class="el-icon-arrow-left"></i>
      <span>返回</span>
    </div>
    <div class="titleContent">
      <span>
        {{ paperName }}
      </span>
      <div
        class="xuekeStyle"
        :style="{
            background: bgColog[SubjectId + 'bg'],
            color: bgColog[SubjectId],
          }"
      >
        {{ Multidisciplinary.name }}
      </div>
    </div>
    <div class="rgBtn">
      <p><span style="color: #61bb96">{{itemIndex + 1}}</span>/{{ itemListLength }}题</p>
      <el-button size="medium" @click="StudentDoPaper">提交</el-button>
    </div>
  </div>
  <div class="conced">
    <div class="swiper_list">
      <p><i class="el-icon-arrow-left"></i></p>
      <div class="cneterdiv">
        <ul>
          <li
            v-for="i in itemListLength"
            :key="i"
            :style="{borderColor: itemIndex + 1 === i ? 'red' : '',color: itemIndex + 1 === i ? 'red' : '', backgroundColor: itemIndex + 1 === i ? '#FFFFFF' : ''}"
            @click="itemClick(i)">
            {{i}}
          </li>
        </ul>
        <div></div>
      </div>
      <p><i class="el-icon-arrow-right"></i></p>
    </div>
    <div>
      <div class="itemSt" v-if="itemList.length > 0">
        <div class="tixing">
          <span class="pc_font_size22 font_size24 ipad_font_size26">{{ returnType() }}</span>
        </div>
        <div class="itemInd">
          <p class="pc_font_size20 font_size24 ipad_font_size26">第{{ itemIndex + 1 }}题</p>
          <el-button class="pc_font_size16 font_size20 ipad_font_size26" size="medium" @click="next">{{ nextTitle }}</el-button>
        </div>
        <div class="tigan pc_font_size18 font_size28 ipad_font_size35" v-html="itemList[itemIndex].ItemTitle" v-katex></div>
        <div class="tupian">
          <el-image
            v-for="(item, ind) in itemList[itemIndex].ImgUrl.split('|')"
            :key="ind"
            style="width: 30%;"
            :src="item"
            :preview-src-list="itemList[itemIndex].ImgUrl.split('|')"
            fit="contain"></el-image>
<!--          </br>-->
<!--          <canvas id="canvasImg"></canvas>-->
        </div>
      </div>
    </div>
  </div>

</div>
</template>

<script>
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
export default {
  name: 'photoAnswer',
  data () {
    return {
      itemList: [],
      itemListLength: 0,
      itemIndex: 0,
      nextTitle: '下一题',
      paperName: '',
      SubjectId: '',
      // 学科
      bgColog: {},
      Multidisciplinary: {},
      answerJson: null
    }
  },
  created () {
    this.bgColog = CorrespondingColor()
    this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
    this.GetStudentDoPaperPictureInfos()
  },
  // mounted () {
  //   const time = setTimeout(()=>{
  //     this.jiazai()
  //     clearTimeout(time)
  //   },1000)
  // },
  methods: {
    // jiazai(){
    //   let canvas = document.getElementById('canvasImg')
    //   let ctx = canvas.getContext('2d')
    //   let image = new Image()
    //   // image.src = this.itemList[this.itemIndex].ImgUrl
    //   image.src = this.itemList[2].ImgUrl
    //   image.onload = () => {
    //     canvas.style.width = image.width + 'px'
    //     canvas.style.height = image.height + 'px'
    //     var scale = window.devicePixelRatio // Change to 1 on retina screens to see blurry canvas.
    //     canvas.width = Math.floor(image.width * scale)
    //     canvas.height = Math.floor(image.height * scale)
    //     ctx.scale(scale, scale)
    //     ctx.fillStyle = ctx.createPattern(image, 'repeat') // 性质相当于css中的background-image
    //     ctx.fillRect(0, 0, canvas.width, canvas.height) // 最终渲染的位置
    //   }
    // },
    goReturn () {
      this.$router.go(-1)
    },
    next () {
      // console.log(this.itemIndex)
      // if (this.itemIndex++ >= this.itemListLength - 2) {
      //   this.nextTitle = '提交'
      // } else {
      //   this.nextTitle = '下一题'
      // }
      if (this.itemIndex++ > this.itemListLength - 2) this.itemIndex = 0
    },
    itemClick (e) {
      // this.nextTitle = this.itemListLength === e ? '提交' : '下一题'
      this.itemIndex = e - 1
    },
    // 试题类型
    returnType () {
      let title = ''
      const typeList = [
        { '2': '单项选择题' },
        { '5': '填空题' },
        { '42': '填空题' },
        { '10': '多项选择题' },
        { '11': '判断题' },
        { '23': '应用题' },
        { '36': '主观题' },
        { '39': '题型' },
        { '40': '可变行列表' },
        { '41': '可变行填空' },
        { '43': '可增加填空' },
        { '44': '可选择填空' },
        { '45': '可为空填空' },
        { '46': '可下拉选择题' },
        { '47': '思考问答题' },
        { '50': '连线题' },
        { '51': '表格题' },
        { '52': '选词填空题' },
        { '70': '多项单选题' }
      ]
      if (this.itemList.length > 0) {
        const id = this.itemList[this.itemIndex].TypeId
        title = typeList.filter(res => res[id])[0][id]
      }
      return title
    },
    // 获取切割试题
    GetStudentDoPaperPictureInfos () {
      const data = {
        paperId: this.$route.query.paperId,
        studentId: localStorage.getItem('UserId'),
        ImgType: 2
      }
      this.$uwonhttp.post('Paper/Paper/GetStudentDoPaperPictureInfos', data).then(res => {
        if (res.data.Success) {
          this.itemList = res.data.Data
          this.itemListLength = res.data.Data.length
          this.paperName = res.data.Data[0].PaperName
          this.SubjectId = res.data.Data[0].SubjectId
          const arr = []
          res.data.Data.forEach(item => {
            arr.push({ ItemId: item.ItemId, Answer: item.ImgUrl })
          })
          this.answerJson = JSON.stringify(arr)
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    },
    // 提交
    StudentDoPaper () {
      const data = {
        PaperId: this.$route.query.paperId,
        UserId: localStorage.getItem('UserId'),
        answerJson: this.answerJson
      }
      this.$uwonhttp.post('Paper/Paper/StudentDoPaper', data).then(res => {
        if (res.data.Success) {
          this.SaveSubjectiveItemAnswer(data, res.data.Data.IsThereLevelItem)
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    },
    SaveSubjectiveItemAnswer (data, IsThereLevelItem) {
      data.SubType = 0
      this.$uwonhttp.post('Paper/Paper/SaveSubjectiveItemAnswer', data).then(res => {
        if (res.data.Success) {
          this.$router.push({
            path: '/Student/Exam_Student/jobReport',
            query: {
              paperId: this.$route.query.paperId,
              IsThereLevelItem: IsThereLevelItem
            }
          })
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.photoAnswer{
  .heade{
    height: 60px;
    background-color: #FFFFFF;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 6px;
    .rgBtn{
      display: flex;
      align-items: center;
      p{
        margin-right: 20px;
      }
      .el-button{
        font-size: 18px;
        background-color: #68bb97;
        color: #FFFFFF;
      }
    }
    .titleContent{
      display: flex;
      align-items: center;
      // 学科
      .xuekeStyle {
        margin-left: 18px;
        padding: 4px 18px;
        border-radius: 100px;
      }
    }
  }
  .conced{
    width: 100%;
    margin-top: 10px;
    border-radius: 6px;
    background-color: #FFFFFF;
    padding: 20px;
    .swiper_list {
      width: 100%;
      padding: 0 16px;
      display: flex;
      justify-content: start;
      p{
        width: 50px;
        height: 50px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background-color: rgba(128, 128, 128, 0.6);
        color: #FFFFFF;
        i{
          font-size: 20px;
          color: #FFFFFF;
        }
      }
      .cneterdiv{
        width: 100%;
        position: relative;
        div{
          width: 100%;
          height: 10px;
          background-color: rgba(128, 128, 128, 0.2);
          position: absolute;
          top: 20px;
          left: 0;
        }
        ul{
          display: flex;
          list-style: none;
          li{
            width: 50px;
            height: 50px;
            border: 1px solid #FFFFFF;
            color: #FFFFFF;
            border-radius: 50%;
            font-size: 20px;
            line-height: 50px;
            text-align: center;
            margin: 0 24px;
            background-color: #68bb97;
            z-index: 99;
          }
        }
      }
    }
  }
  .itemSt{
    margin-top: 20px;
    .itemInd{
      display: flex;
      align-items: center;
      p{
        margin-right: 20px
      }
      .el-button{
        background-color: #68bb97;
        font-size: 16px;
        border-radius: 100px;
        color: #FFFFFF;
      }
    }
    .tigan{
      margin: 10px 0;
    }
    .tupian{
      display: flex;
      align-items: center;
      img{
        width: 30%;
        object-fit: contain;
      }
    }
  }
}
</style>