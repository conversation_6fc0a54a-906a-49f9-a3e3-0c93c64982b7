<template>
  <div class="captureAnswer">
    <div class="top" :style="{'margin-bottom':!_isMobile() ? '10px' : 0}">
      <div class="lf">
        <i class="el-icon-arrow-left" @click="goHuitui"></i>
      </div>
      <div class="cont">练习名称</div>
      <div></div>
    </div>
    <div class="content">
      <div class="itemShiTi">
        <img v-for="(item,index) in imgList" :key="index" :src="item.Url" />
      </div>
      <div class="itemCont">
        <div class="title">
          <span></span>
          拍照上传答案
        </div>
        <div class="cont">
          <div>
            <div class="contImg" @click="goPhoto">
              <img src="@/assets/index/拍照.png" />
            </div>
            <p class="txt">*请水平拍摄练习上的题目答案，系统会自动分割题目</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CaptureAnswer',
  data () {
    return {
      imgList: []
    }
  },
  created () {
    this.GetPaperPictureRange(this.$route.query.paperId)
  },
  methods: {
    GetPaperPictureRange (paperId) {
      this.$uwonhttp.post('Paper/Paper/GetPaperPictureRange', { paperId: paperId }).then(res => {
        if (res.data.Success) {
          this.imgList = res.data.Data
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    },
    goPhoto () {
      this.$router.push({
        path: '/Student/Exam_Student/highSchoolCapture/photo',
        query: {
          paperId: this.$route.query.paperId,
          isDZB: this.$route.query.isDZB
        }
      })
      // this.$router.push({
      //   path: '/Student/Exam_Student/highSchoolCapture/photoAnswer',
      //   query: {
      //     paperId: this.$route.query.paperId
      //   }
      // })
    },
    _isMobile () {
      const flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|Macintosh)/i
      )
      return flag
    },
    goHuitui() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="less" scoped>
.captureAnswer{
  .top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #FFFFFF;
    border-radius: 4px;
    .lf{
      i{
        font-size: 22px;
      }
    }
    .cont{
      font-size: 24px;
      font-weight: 500;
      color: #535353;
    }
  }
  .content{
    display: flex;
    justify-content: space-between;
    height: 80vh;
    border-radius: 4px;
    .itemShiTi{
      width: 49.8%;
      height: 100%;
      overflow: auto;
      img{
        width: 100%;
        margin-bottom: 12px;
      }
    }
    .itemCont{
      width: 49.8%;
      height: 100%;
      padding: 10px 20px;
      background-color: #FFFFFF;
      .title{
        height: 10%;
        font-size: 22px;
        //margin-bottom: 60px;
        span{
          padding: 0 4px;
          background-color: #537566;
          margin-right: 14px;
          border-radius: 50px;
        }
      }
      .cont{
        height: 90%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .contImg{
          width: 400px;
          height: 400px;
          border: 2px dashed #D5D5D5;
          border-radius: 40px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin: 0 auto;
          img{
            width: 140px;
            height: 120px;
          }
        }
        .txt{
          font-size: 20px;
          font-weight: 500;
          color: #B8B8B8;
          text-align: center;
          margin-top: 20px;
        }
      }
    }
  }
}
</style>
