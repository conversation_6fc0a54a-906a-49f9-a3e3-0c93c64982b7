<template>
<!--  <div class="publish" :style="{width:videoW+'px',height:videoH+'px'}">-->
  <div class="publish">
    <video ref="video" id="videoDom" style="object-fit: cover;"></video>
    <canvas style="display: none;" id="canvasCamera" width="1654" height="2339"></canvas>
<!--    <div v-if="imgSrc" class="img_bg_camera">-->
<!--      <img :src="imgSrc" ref="tximg" class="tx_img" />-->
<!--    </div>-->
<!--    <button @click="OpenCamera">打开摄像头</button>-->
<!--    <button @click="CloseCamera">关闭摄像头</button>-->
<!--    <button @click="setImage">拍照</button>-->
  </div>
</template>

<script>
// import html2canvas from 'html2canvas'
import { base64ConvertFile } from '@/utils/fileBase64Blob'
export default {
  name: 'CanvasMaskingOut',
  props: ['videoW', 'videoH'],
  data () {
    return {
      mediaStreamTrack: {},
      video_stream: '', // 视频stream
      imgSrc: '', // 拍照图片
      canvas: null,
      context: null
    }
  },
  mounted () {
    // 进入页面 自动调用摄像头
    this.getCamera()
  },
  methods: {
    // 调用打开摄像头功能
    getCamera () {
      // 获取 canvas 画布
      this.canvas = document.getElementById('canvasCamera')
      this.context = this.canvas.getContext('2d')
      // 旧版本浏览器可能根本不支持mediaDevices，我们首先设置一个空对象
      const constraints = {
        video: {
          // minAspectRatio: 1.333,
          // minFrameRate: 60,
          width: 2339,
          height: 1658,
          facingMode: {
            exact: "environment"
          }
        }
      }
      navigator.mediaDevices
        .getUserMedia(constraints)
        .then(function(stream) {
          // console.log(stream, '：stream')
          var video = document.querySelector('#videoDom')
          if ('srcObject' in video) {
            video.srcObject = stream
          } else {
            video.src = window.URL.createObjectURL(stream)
          }
          video.onloadedmetadata = function (e) {
            video.play()
          }
        })
        .catch(function(err) {
          alert(err.name + ': ' + err.message)
        })
    },
    // 拍照 绘制图片
    setImage () {
      // 点击canvas画图
      this.context.drawImage(
        this.$refs.video,
        0,
        0,
        1654,
        2339
      )
      // this.context.drawImage(
      //   this.$refs.video,
      //   0,
      //   0,
      //   1026,
      //   1369
      // )
      // 获取图片base64链接
      const image = this.canvas.toDataURL('image/jpg')
      // console.log(image)
      this.$emit('refreshDataList', base64ConvertFile(image))
    },
    // 打开摄像头
    OpenCamera () {
      this.getCamera()
    },
    // 关闭摄像头
    CloseCamera () {
      this.$refs.video.srcObject.getTracks()[0].stop()
    }
  }
}
</script>

<style lang="less" scoped>
.publish{
  position: absolute;
  left: 0;
  top: 0;
  z-index: 88;
  video{
    width: 100%;
    height: 100%;
  }
  //width: 100%;
  //height: 100%;
  //canvas {
  //  width: 100%;
  //  height: 500px;
  //}
  //button {
  //  width: 100px;
  //  height: 40px;
  //  position: relative;
  //  bottom: 0;
  //  left: 0;
  //  background-color: rgb(22, 204, 195);
  //}
  //.img_bg_camera {
  //  img {
  //    width: 300px;
  //    height: 500px;
  //  }
  //}
}
</style>
