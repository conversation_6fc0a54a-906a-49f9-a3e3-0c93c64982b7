<template>
  <canvas ref="canvasObj" id="canvasObj" :width="originWidth" :height="originHeight"></canvas>
</template>

<script>
export default {
  name: 'CanvasMasking',
  props: {
    originWidth: {
      type: Number
    },
    originHeight: {
      type: Number
    },
    canvasXY: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  computed: {
    witchFun: function () {
      return this.originWidth
    }
  },
  watch: {
    canvasXY: {
      handler (val) {
        this.calcRate(val)
      },
      immediate: true
      // deep: true
    },
    witchFun: function (newVal) {
      if (newVal) {
        this.calcRate(this.canvasXY)
      } else {
        return false
      }
    }
  },
  data () {
    return {
      rate: 1,
      dots: []
    }
  },
  mounted () {
    // this.calcRate(this.canvasXY)
  },
  methods: {
    getDots (Arrlist) {
      const that = this
      const dots = JSON.parse(JSON.stringify(Arrlist))
      dots.forEach(item => {
        item.forEach(it => {
          it.X = it.X * that.rate
          it.Y = it.Y * that.rate
        })
      })
      this.dots = dots
      this.$nextTick(() => {
        that.initCanvas()
      })
    },
    calcRate (Arrlist) {
      const containerWith = this.originWidth
      const rate = containerWith / 1654
      this.rate = rate
      this.getDots(Arrlist)
    },
    initCanvas () {
      const canvasDom = this.$refs['canvasObj']
      const ctx = canvasDom.getContext('2d')
      ctx.clearRect(0, 0, this.originWidth, this.originHeight)
      ctx.lineWidth = 1
      ctx.strokeStyle = 'red'
      this.dots.forEach(item => {
        for (let i = 0; i < item.length; i++) {
          ctx.beginPath()
          ctx.moveTo(item[i].X, item[i].Y)
          if (i === item.length - 1) {
            ctx.lineTo(item[0].X, item[0].Y)
          } else {
            ctx.lineTo(item[i + 1].X, item[i + 1].Y)
          }
          ctx.stroke()
        }
      })
    }
  }
}
</script>

<style scoped>
#canvasObj{
  background-color: rgba(0, 145, 255, 0.12);
}
</style>
