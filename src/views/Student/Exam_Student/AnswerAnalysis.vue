<template>
  <div class="whole_page" style="margin: 0 auto">
    <div class="whole_title">
      <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
      <span>跟进练习--{{ PaperObj.PaperTitle }}</span>
      <span></span>
    </div>
    <div class="whole_content">
      <div class="div" style="width: 100%" id="Imgid">
        <div class="swiper_list">
          <div class="tea-analusis">
            <div class="line_border"></div>
            <swiper class="swiper" ref="mySwiper" :options="swiperOption">
              <!-- 'current-answer': i.CurrentAnswer, -->
              <swiper-slide style="text-align: center" v-for="(i, t) in AssociationItems" :key="t">
                <a
                  class="title-btn-whole"
                  :class="{
                    'def-color': i.IsKeyTrue === 1,
                    'wrong-bgc': i.IsKeyTrue === 2,
                  }"
                  @click="switchTitle(t, i.ItemId, i.TypeId)"
                >
                  {{ t + 1 }}
                </a>
              </swiper-slide>
            </swiper>
            <div class="left-arrow">
              <div class="arrow-img" @click="prevPhoto">
                <i></i>
              </div>
            </div>
            <div class="right-arrow">
              <div class="arrow-img" @click="nextPhoto">
                <i></i>
              </div>
            </div>
          </div>
          <div class="imgbox">
            <img src="@/assets/student/闹钟.png" alt="" />
            <span class="pc_font_size18 font_size20 ipad_font_size22"> 答题用时{{ PaperObj.UseTime }}</span>
          </div>
        </div>
        <div
          class="alone-tit"
          v-for="(item, index) in AssociationItems_show"
          :key="item.ItemId"
          :id="item.ItemId"
          v-katex
        >
          <div class="tit_header ">
            <span class=" font_size24 ipad_font_size26">{{ item.PaperItemType }}</span>
          </div>
          <div class="titleindex">
            <p class="divleft">
              <span style="width: 18px; vertical-align: middle" class="font_size24 ipad_font_size26"> {{ newSubject }}.&nbsp; </span
              ><span class="font_size24 ipad_font_size26" v-html="item.ItemTitle"></span>
            </p>
            <div class="divright" v-if="index == 0">
              <span style="margin-right: 32px">
                <a-progress
                  type="circle"
                  :percent="PaperObj.Accuracy"
                  :width="newWidth"
                  :strokeColor="stuStatistics"
                  :format="
                    () => {
                      if (PaperObj.Accuracy === 100) {
                        return '100%'
                      } else {
                        return PaperObj.Accuracy + '%'
                      }
                    }
                  "
                /><br />
                <span class="font_size18 ipad_font_size20">正确率</span></span>
              <span>
                <a-progress
                  type="circle"
                  :percent="(PaperObj.RightCount / PaperObj.ItemCount) * 100"
                  :width="newWidth"
                  :format="() => PaperObj.RightCount + '/' + PaperObj.ItemCount"
                  :strokeColor="stuStatistics"
                >
                </a-progress
                ><br />
                <span class="font_size18 ipad_font_size20">正确题数</span>
              </span>
            </div>
          </div>
          <div>
            <!-- 选择题 -->
            <div v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
              <span v-for="(k, j) in item.Options" :key="j" style="display: block" class="font_size22 ipad_font_size26">
                <img
                  v-if="item.IsKeyTrue === 1 && item.UserAnswer === k.Opt"
                  style="width: 10%; vertical-align: middle"
                  src="@/assets/student/对号.png"
                  alt=""
                />
                <img
                  v-else-if="item.IsKeyTrue === 2 && item.UserAnswer === k.Opt"
                  style="width: 10%; vertical-align: middle"
                  src="@/assets/student/叉号.png"
                  alt=""
                />
                <span v-else class="Radio_raduis"></span>
                <span style="vertical-align: middle"> {{ k.Opt }}{{PaperFormat.period()}}<span v-html="k.Content"></span></span>
              </span>
            </div>
            <!-- 表格题 -->
            <RenderTable v-if="item.ItemTypeId === 51" :TableItemInfo="item.ItemChange.TableItemInfo" :showTitle="true"></RenderTable>
            <!-- 多选题 -->
            <div v-if="item.ItemTypeId === 10">
              <div v-for="k in item.Options" :key="k.Opt" class="font_size22 ipad_font_size26">
                <img
                  v-if="k.IsTrue === 1 && item.UserAnswer.indexOf(k.Opt) != -1"
                  style="width: 18px; vertical-align: middle"
                  src="@/assets/student/对号.png"
                  alt=""
                />
                <img
                  v-else-if="k.IsTrue === 0 && item.UserAnswer.indexOf(k.Opt) != -1"
                  style="width: 18px; vertical-align: middle"
                  src="@/assets/student/叉号.png"
                  alt=""
                />
                <span v-else class="check_box"></span>
                <span style="vertical-align: middle"> {{ k.Opt }}{{PaperFormat.period()}}<span v-html="k.Content"></span></span>
              </div>
            </div>
            <div class="table_40" v-if="item.ItemTypeId === 42">
              <table class="pure-table pure-table-bordered">
                <thead>
                  <tr>
                    <th v-for="(item, index) in item.ItemChange.Titles" :key="index">{{ item }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span
                        v-html="td"
                        ref="getFocus"
                        id="hello_input"
                        @input="handleInput"
                        @click="onkeyup"
                      ></span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <p class="line"></p>
          <p class="clearfix" v-if="item.ItemTypeId !== 46 && item.ItemTypeId !== 40 && item.ItemTypeId !== 41">
            <span class="fl" :class="[{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },'font_size22', 'ipad_font_size24']">
              我的作答：
            </span>
            <span
              v-if="item.ItemTypeId != 36 && item.ItemTypeId != 51 && item.ItemTypeId != 50"
              class="fl"
              :class="[{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },'font_size22', 'ipad_font_size24']"
              v-html="item.UserAnswer"
              v-katex
            ></span>
            <SubjectiveStemPhoto
              v-if="item.ItemTypeId == 36 && item.ReviewContentType == 1"
              :Answer="item.PicUrl"
              useType="analysis"
              ref="SubjectiveStemPhoto"
            ></SubjectiveStemPhoto>
            <SubjectiveStemVideo
              v-if="item.ItemTypeId == 36 && item.ReviewContentType == 3"
              :Answer="item.PicUrl"
              useType="analysis"
              ref="SubjectiveStemVideo"
            ></SubjectiveStemVideo>
            <SubjectiveStemAudio
              v-if="item.ItemTypeId == 36 && item.ReviewContentType == 2"
              :Answer="item.PicUrl"
              useType="analysis"
              ref="SubjectiveStemAudio"></SubjectiveStemAudio>
            <RenderTable v-if="item.ItemTypeId === 51" :TableItemInfo="item.ItemChange.TableItemInfo" :analysis="true"></RenderTable>
            <!-- 连线题 -->
            <matching
              v-if="item.ItemTypeId == 50"
              :Answer="item.UserAnswer"
              :SureAnser="item.Answer"
              :linkExamData="item.ItemChange.LinkExamData"
              :SubType="1"
            ></matching>
          </p>
          <div v-show="item.Evaluation !== null">
            <span style="color: #00aaff;">老师首次评价:</span>
            <el-tag effect="dark">
              {{ item.Evaluation == 0 ? '答对1/4':item.Evaluation==1?'答对1/2':'答对3/4' }}
            </el-tag>
            <div class="Comments" v-if="item.Comments !== null">
              <!--              <el-input-->
              <!--                style="width: 28%;"-->
              <!--                type="textarea"-->
              <!--                autosize-->
              <!--                disabled-->
              <!--                v-model="item.Comments"-->
              <!--              >-->
              <!--              </el-input>-->
              {{ item.Comments }}
            </div>
          </div>
          <span class="cur font_size24 ipad_font_size26" style="color: #68bb97">解析:</span>
          <span v-show="item.Analysis !== ''" class="font_size24 ipad_font_size26"><span v-html="item.Analysis"></span></span>
          <span v-show="item.Analysis === ''" class="font_size24 ipad_font_size26"><span>暂无解析</span></span>
          <div class="Line_span">
            <span class="font_size22 ipad_font_size24">对应原题</span>
          </div>
          <div class="original-tit" v-for="item_i in item.TitleArr" :key="item_i.ItemId" :id="item_i.ItemId" v-katex>
            <div class="tit_header">
              <span class="font_size22 ipad_font_size24">{{ item_i.PaperItemType }}</span>
            </div>
            <p>
              <span class="font_size24 ipad_font_size26" v-html="item_i.ItemTitleWeb"></span>
            </p>
            <div>
              <div v-if="item_i.ItemTypeId === 2 || item_i.ItemTypeId === 11">
                <span v-for="(k, j) in item_i.Options" :key="j" style="display: block" class="font_size22 ipad_font_size26">
                  <img
                    v-if="item_i.IsKeyTrue === 1 && item_i.UserAnswer === k.Opt"
                    style="width: 18px; vertical-align: middle"
                    src="@/assets/student/对号.png"
                    alt=""
                  />
                  <img
                    v-else-if="item_i.IsKeyTrue === 2 && item_i.UserAnswer === k.Opt"
                    style="width: 18px; vertical-align: middle"
                    src="@/assets/student/叉号.png"
                    alt=""
                  />
                  <span v-else class="Radio_raduis"></span>
                  <span style="vertical-align: middle"> {{ k.Opt }}{{PaperFormat.period()}}<span v-html="k.Content"></span></span>
                </span>
              </div>
              <!-- 表格题 -->
              <RenderTable v-if="item_i.ItemTypeId === 51" :TableItemInfo="item_i.ItemChange.TableItemInfo" :showTitle="true"></RenderTable>
              <div v-if="item_i.ItemTypeId === 10">
                <div v-for="k in item_i.Options" :key="k.Opt">
                  <img
                    v-if="k.IsTrue === 1 && item_i.UserAnswer.indexOf(k.Opt) != -1"
                    style="width: 18px; vertical-align: middle"
                    src="@/assets/student/对号.png"
                    alt=""
                  />
                  <img
                    v-else-if="k.IsTrue === 0 && item_i.UserAnswer.indexOf(k.Opt) != -1"
                    style="width: 18px; vertical-align: middle"
                    src="@/assets/student/叉号.png"
                    alt=""
                  />
                  <span v-else class="check_box"></span>
                  <span style="vertical-align: middle"> {{ k.Opt }}{{PaperFormat.period()}}<span v-html="k.Content"></span></span>
                </div>
              </div>

              <div v-if="item_i.ItemTypeId === 42">
                <table class="pure-table pure-table-bordered">
                  <thead>
                    <tr>
                      <th v-for="(item, index) in item_i.ItemChange.Titles" :key="index">{{ item }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(td, ind) in item_i.ItemChange.Rows" :key="ind">
                      <td v-for="(td, indx) in td" :key="indx">
                        <span
                          v-html="td"
                          ref="getFocus"
                          id="hello_input"
                          @input="handleInput"
                          @click="onkeyup"
                        ></span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <p class="line"></p>
            <p class="clearfix" v-if="item_i.ItemTypeId !== 46 && item_i.ItemTypeId !== 40 && item_i.ItemTypeId !== 41">
              <span class="fl" :class="[{ correct: item_i.IsKeyTrue === 1, 'answer-wrong': item_i.IsKeyTrue === 2 },'font_size22', 'ipad_font_size24']">
                我的作答：
              </span>
              <span
                v-if="item_i.ItemTypeId !== 36 && item_i.ItemTypeId !== 51 && item_i.ItemTypeId !== 50"
                :class="[{ correct: item_i.IsKeyTrue === 1, 'answer-wrong': item_i.IsKeyTrue === 2 },'font_size22', 'ipad_font_size24']"
                v-html="item_i.UserAnswer"
                v-katex>
              </span>
              <SubjectiveStemPhoto
                v-if="item_i.ItemTypeId == 36 && item_i.ReviewContentType == 1"
                :Answer="item_i.PicUrl"
                useType="analysis"
                ref="SubjectiveStemPhoto"
              ></SubjectiveStemPhoto>
              <SubjectiveStemVideo
                v-if="item_i.ItemTypeId == 36 && item_i.ReviewContentType == 3"
                :Answer="item_i.PicUrl"
                useType="analysis"
                ref="SubjectiveStemVideo"
              ></SubjectiveStemVideo>
              <SubjectiveStemAudio
                v-if="item_i.ItemTypeId == 36 && item_i.ReviewContentType == 2"
                :Answer="item_i.PicUrl"
                useType="analysis"
                ref="SubjectiveStemAudio"></SubjectiveStemAudio>
              <RenderTable v-if="item_i.ItemTypeId === 51" :TableItemInfo="item_i.ItemChange.TableItemInfo" :analysis="true"></RenderTable>
              <!-- 连线题 -->
              <matching
                v-if="item_i.ItemTypeId == 50"
                :Answer="item_i.ShowUserAnswer"
                :SureAnser="item_i.Answer"
                :linkExamData="item_i.ItemChange.LinkExamData"
                :SubType="4"
              ></matching>
            </p>
            <span class="cur font_size24 ipad_font_size26" style="color: #68bb97">解析:</span>
            <span v-show="item_i.Analysis !== ''" class="font_size24 ipad_font_size26"><span v-html="item_i.Analysis"></span></span>
            <span v-show="item_i.Analysis === ''" class="font_size24 ipad_font_size26"><span>暂无解析</span></span>
          </div>
        </div>
      </div>
      <!-- <div class="div2" style="width: 30%">
        <div class="right_header">
          <img src="@/assets/student/闹钟.png" alt="" />
          <br />
          <br />
          <span>答题用时:{{ PaperObj.UseTime }}</span>
          <br />
        </div>
        <div>
          <div class="Statistics">
            <p class="p-chart" style="margin-right: 32px">
              <a-progress
                type="circle"
                :percent="PaperObj.Accuracy"
                height="100px"
                :width="65"
                :strokeColor="stuStatistics"
                :format="
                  () => {
                    if (PaperObj.Accuracy === 100) {
                      return '100%'
                    } else {
                      return PaperObj.Accuracy + '%'
                    }
                  }
                "
              /><br />
              <span>正确率</span>
            </p>
            <p class="p-chart">
              <a-progress
                type="circle"
                :percent="(PaperObj.RightCount / PaperObj.ItemCount) * 100"
                height="100px"
                :width="65"
                :format="() => PaperObj.RightCount + '/' + PaperObj.ItemCount"
                :strokeColor="stuStatistics"
              >
              </a-progress
              ><br />
              <span>正确题数</span>
            </p>
          </div>

          <div class="List-topics">
            <p class="menu">答题卡</p>
            <a
              v-for="(i, t) in AssociationItems"
              :key="t"
              class="title-btn-whole"
              :class="{ 'def-color': i.IsKeyTrue === 1, 'wrong-bgc': i.IsKeyTrue === 2 }"
              @click="switchTitle(t, i.ItemId, i.TypeId)"
            >
              {{ t + 1 }}
            </a>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import ExamFormatCommon from '../../../common/ExamFormatCommon'
import SubjectiveStemPhoto from '@/views/Student/Exam_Student/components/subjectiveExam/answerPhoto'
import SubjectiveStemVideo from '@/views/Student/Exam_Student/components/subjectiveExam/answerVideo'
import SubjectiveStemAudio from '@/views/Student/Exam_Student/components/subjectiveExam/answerAudio'
import RenderTable from './components/tableStem/renderTable.vue'
import { arrlistTitle } from '@/utils/bankedCloze/bankedCloze'
import isMobile from '@/utils/isMobile/isMobile'
import matching from '@/components/matching/index'
export default {
  components: {
    SubjectiveStemPhoto,
    SubjectiveStemVideo,
    SubjectiveStemAudio,
    RenderTable,
    matching
  },
  created () {
    this.AssociationItems_show = []
    this.GetAdaptivePaperFinish()
    this.GetAdaptiveShare()
    this.roundSize()
  },
  data () {
    return {
      AssociationItems: [],
      AssociationItems_show: [],
      TitleItems: [],
      PaperObj: {},
      doPaperTime: '',
      Accracy: 0,
      userId: '',
      paperId: '',
      // 试卷名称
      paperName: '',
      chapter: '',
      chapterId: '',
      // 默认第一题
      defalutCol: '',
      // 切换题目数
      newSubject: 1,
      // 题目数
      paperSubjectNum: '',
      // 错题数
      wrongNum: '',
      // 真确题数
      correctNum: '',
      // 是否要订正
      revision: false,
      // 试卷信息
      paperList: [],
      // 精度
      stuStatistics: '#3CB98F',
      value: 1,
      analysis: false,
      radioStyle: {
        display: 'block'
      },
      swiperOption: {
        slidesPerView: 10, // 一行显示4个
        spaceBetween: 10, // 间隔30
        freeMode: true,
        speed: 1000, // 滑动速度
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true,
          disabledClass: 'my-button-disabled', // 前进后退按钮不可用时的类名。
          hiddenClass: 'my-button-hidden' // 按钮隐藏时的Class
        }
      },
      newWidth: null
    }
  },
  watch: {
    $route () {
      const ChangData = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerAnalysis?' + ChangData) {
        this.AssociationItems_show = []
        this.GetAdaptivePaperFinish()
        this.GetAdaptiveShare()
      }
    }
  },
  methods: {
    roundSize () {
      if (isMobile()) {
        this.newWidth = 50
      } else {
        this.newWidth = 65
      }
    },
    GoBack () {
      history.go(-1)
    },
    prevPhoto () {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto () {
      this.$refs.mySwiper.$swiper.slideNext()
    },
    seeAnalysis (id) {
      this.analysis = id
    },
    // 获取试卷基本信息
    getPaperInfor (id) {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then((res) => {
        this.paperName = res.Data.Title
        this.chapterId = res.Data.ChapterId
      })
    },
    // 复习回顾题目详情
    async GetAdaptivePaperFinish () {
      const params = {
        userId: localStorage.getItem('UserId'),
        paperId: this.$route.query.paperId,
        type: this.$route.query.type
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetAdaptivePaperFinish', params)
      if (res.data.Success) {
        // Items
        this.AssociationItems = res.data.Data.Items
        this.defalutCol = this.AssociationItems[0].ItemId
        this.TitleItems = res.data.Data.AssociationItems

        this.AssociationItems.forEach((item, index) => {
          item.TitleArr = []
          this.TitleItems.forEach((el, indx) => {
            el.ItemTitleWeb = ExamFormatCommon.format_fraction(el.ItemTitleWeb)
            el.ItemTitleWeb = ExamFormatCommon.format_space(el.ItemTitleWeb)
            // 选词填空题
            if (el.ItemTypeId == 52 && el.ItemChange.SelectFillBlank !== null || el.ItemTypeId == 70 && el.ItemChange.MultitermSingle !== null) {
              el.ItemTitleWeb = arrlistTitle('展示', el, el.ItemTypeId)
            }
            if (index == indx) {
              item.TitleArr.push(el)
            }
          })
        })
        this.AssociationItems.forEach((item) => {
          // 选词填空题
          if (item.ItemTypeId == 52 && item.ItemChange.SelectFillBlank !== null || item.ItemTypeId == 70 && item.ItemChange.MultitermSingle !== null) {
            item.ItemTitle = arrlistTitle('展示', item, item.ItemTypeId)
          }
          if (item.ItemTypeId == 5) {
            var Newtype_5 = item.UserAnswer.replace(/\||<.*?>.*?<\/.*?>/g, function (e) {
              if (e === '|') {
                return ' , '
              }
              return e
            })
            item.UserAnswer = Newtype_5
            item.ItemTitle = ExamFormatCommon.format_fraction(item.ItemTitle)
            item.ItemTitle = ExamFormatCommon.format_space(item.ItemTitle)

            // this.filterRows_5(item)
          }
          if (item.ItemTypeId == 10) {
            var Newtype_10 = item.UserAnswer.replace(/\|/g, ',')
            item.UserAnswer = Newtype_10
          }
          if (item.ItemTypeId == 42) {
            var Newtype_42 = item.UserAnswer.replace(/\|/g, ',')
            item.UserAnswer = Newtype_42
            this.FormatRows(item.ItemChange.Rows)
          }
          item.TitleArr.forEach((el) => {
            const reg = /(#@)/g
            const rep = ''
            const stem = el.ItemTitle.replace(reg, rep)
            el.ItemTitle = stem
            if (el.ItemTypeId == 5) {
              var Newtype_5 = el.UserAnswer.replace(/\||<.*?>.*?<\/.*?>/g, function (e) {
                if (e === '|') {
                  return ' , '
                }
                return e
              })
              el.UserAnswer = Newtype_5
              // this.filterData_5(el)
            }
            if (el.ItemTypeId == 10) {
              var Newtype_10 = el.UserAnswer.replace(/\|/g, ',')
              el.UserAnswer = Newtype_10
            }
            if (el.ItemTypeId == 42) {
              var Newtype_42 = el.UserAnswer.replace(/\|/g, ',')
              el.UserAnswer = Newtype_42
              this.FormatRows(el.ItemChange.Rows)
            }
          })
        })
        this.AssociationItems_show.push(this.AssociationItems[0])
        setTimeout(() => {
          this.GetImgAll()
        }, 1000)
      }
    },

    GetImgAll () {
      const that = this
      that.$nextTick(() => {
        const MyImg = document.getElementById('Imgid').querySelectorAll('img')
        if (MyImg !== null) {
          const Img_arr = []
          const Img_http = []
          for (let i = 0; i < MyImg.length; i++) {
            if (MyImg[i].src.indexOf('http') != -1) {
              Img_http.push(MyImg[i])
            } else {
              Img_arr.push(MyImg[i])
            }
          }
          for (let i = 0; i < Img_http.length; i++) {
            Img_http[i].style.maxWidth = '90%'
          }
          for (let i = 0; i < Img_arr.length; i++) {
            Img_arr[i].style.width = '2%'
          }
        }
      })
    },

    FormatRows (arr) {
      const regx = /\(\#@\)/g
      arr.forEach((td, idx, ary) => {
        td.forEach((i, index, array) => {
          const TableRegx = i.match(regx)
          if (TableRegx !== null) {
            TableRegx.forEach((item1) => {
              const value = item1.replace(regx, '')
              const title = i.replace(
                item1,
                `<input class="input" autocomplete="off" disabled='disabled' type="text" style="width: 100px;height:30px;border: 0px; border-radius:4px; color:#000;border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin: 0 2px 9px 2px;"
                      id="${idx + '.' + index}"   data-index="${index}"/>`
              )
              array[index] = title
            })
          }
        })
      })
    },

    filterRows_5 (data) {
      const regx = /\(#@\)/g
      const TableRegx = data.ItemTitle.match(regx)
      TableRegx.forEach((item1) => {
        const value = item1.replace(regx, '')
        const title = data.ItemTitle.replace(
          item1,
          `<input class="input" disabled='disabled' style="width: 120px;height:34px;border: 0px; border-radius:4px; color:#000;border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"/>`
        )
        data.ItemTitle = title
      })
    },

    filterData_5 (data) {
      const regx = /\(\#\&\d+\@\)/g
      const regx1 = /\(\#\&/g
      const regx2 = /\@\)/g
      const regxlist = data.ItemTitleWeb.match(regx)
      regxlist.forEach((item1) => {
        const value = item1.replace(regx1, '').replace(regx2, '')
        const title = data.ItemTitleWeb.replace(
          item1,
          `<input class="input" disabled='disabled' style="width: 120px;height:34px;border: 0px; border-radius:4px; color:#000;border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"/>`
        )
        data.ItemTitleWeb = title
      })
    },

    // 做卷信息
    async GetAdaptiveShare () {
      const params = {
        userId: localStorage.getItem('UserId'),
        paperId: this.$route.query.paperId,
        type: this.$route.query.type
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetAdaptiveShare', params)
      if (res.data.Success) {
        this.PaperObj = res.data.Data[0]
      }
    },

    // 切换题目
    switchTitle (index, ItemId, typeId) {
      // document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      // this.AssociationItems_show[0] = this.AssociationItems[index]
      // console.log(this.AssociationItems)
      this.AssociationItems_show = []
      this.AssociationItems_show.push(this.AssociationItems[index])
      // console.log(this.AssociationItems_show)
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.defalutCol = ItemId
      this.GetImgAll()
      this.callswiper(index)
    },
    callswiper (index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },
    // 选项选择
    onChange (value) {}
  }
}
</script>

<style lang="less" scoped>
.Comments{
  width: 22%;
  padding: 2px 10px;
  border-radius: 4px;
  color: #232323;
  border: 1px solid #808080;
  background-color: rgba(250, 250, 250, 60);
  opacity: 0.8;
  font-size: 15px;
  ///deep/.el-textarea__inner {
  //  background-color: #fafafa !important;
  //  border-color: #c7c7c7 !important;
  //  color: #595959 !important;
  //  cursor: not-allowed;
  //}
}
.titleindex {
  display: flex;
  justify-content: space-between;
  .divleft {
    display: flex;
    align-items: flex-start;
    width: 50%;
  }
  .divright {
    width: 50%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: -50px;
    text-align: center;
    bottom: 40px;
    @media screen and (min-width: 1024px) and (max-width: 1440px) {
      /deep/.ant-progress-circle .ant-progress-text {
        font-size: 18px;
      }
    }
    @media screen and (min-width: 1440px) {
      /deep/.ant-progress-circle .ant-progress-text {
        font-size: 12px;
      }
    }
  }
  /deep/.el-button {
    margin-left: 20px;
  }
}
.whole_page {
  width: 100%;
}
.f-t {
  font-size: 21px;
}
// 正确颜色
.correct {
  color: #68bb97;
  // font-size: 18px;
}
.answer-wrong {
  color: #ff682c;
  // font-size: 18px;
}
.wrong-bgc {
  border: 2px solid #da1b1b;
  color: #da1b1b;
}
.def-color {
  border: 2px solid #61bb96;
  color: #61bb96;
}
.current-answer {
  border-color: #357bee;
  color: #357bee;
}
.whole_content {
  margin-top: 10px;
  display: flex;
  .div {
    height: 700px;
    height: 80vh;
    overflow: auto;
  }
}
.select_46 {
  line-height: 30px;
}
.Blank_box {
  border: 1px solid #ccc;
  padding: 15px;
  margin-top: 10px;
  .blank_Answer {
    font-size: 18px;
  }
  span {
    p {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
      white-space: pre-line;
      line-height: 40px;
    }
  }
}
.table_40 {
  line-height: 40px;
}
.tableTitle {
  border-collapse: collapse;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
.tableTitle tr {
  border: none;
}
.tableTitle td {
  width: 180px;
  height: 50px;
  font-size: 17px;
  cursor: pointer;
  border: 1px inset #cccc;
}
.check_box {
  width: 15px;
  height: 15px;
  border: 1px solid #ccc;
  display: inline-block;
  vertical-align: middle;
}
// 右边
.div2 {
  padding: 80px 16px 0 16px;
  margin-left: 10px;
  background-color: #fff;
  .right_header {
    text-align: center;
    img {
      height: 50px;
    }
    span {
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      display: contents;
    }
  }
  .p-chart {
    font-size: 17px;
    display: inline-block;
  }
  // 统计
  .Statistics {
    margin-bottom: 20px;
    margin-top: 20px;
    text-align: center;
  }
  // 错题扫除
  .wrong-topic {
    text-align: center;
    margin-bottom: 80px;
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
    }
  }
}
.List-topics {
  margin-top: 20px;
  text-align: center;
  .menu {
    font-size: 18px;
  }
}
// 单题
.alone-tit {
  width: 100%;
  padding: 10px 30px;
  margin-bottom: 30px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  line-height: 45px;
  .line {
    height: 1px;
    border-bottom: 1px solid #f6f6f6;
  }
  .wrong-storage {
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
      cursor: pointer;
    }
  }
  .tit_header {
    display: flex;
    span {
      font-size: 21px;
    }
  }
}

.original-tit {
  width: 100%;
  margin-bottom: 30px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  line-height: 45px;
  .line {
    height: 1px;
    border-bottom: 1px solid #f6f6f6;
  }
  .wrong-storage {
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
      cursor: pointer;
    }
  }
  .tit_header {
    display: flex;
    span {
      font-size: 21px;
    }
  }
}

// 题目目录
.title-btn-whole {
  display: inline-block;
  width: 41px;
  height: 40px;
  line-height: 40px;
  margin: 16px 0px 0 8px;
  text-align: center;
  // border: 1px solid #ccc;
  border-radius: 35px;
  cursor: pointer;
  background: #fff;
}
.Radio_raduis {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 1px solid #ccc;
  vertical-align: middle;
  display: inline-block;
}

.Line_span {
  position: relative;
}
.Line_span:before {
  content: '';
  background-color: #3796ec;
  width: 5px;
  height: 25px;
  position: absolute;
  left: -10px;
  top: 40%;
  margin-top: -8px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.whole_title {
  display: flex;
  justify-content: space-between;
  font-size: 22px;
  padding: 15px;
  background: #ffffff;
  border-radius: 10px;
}
.pure-table {
  border-collapse: collapse;
  border-spacing: 0;
  empty-cells: show;
  border: 1px solid #cbcbcb;
}

.pure-table caption {
  color: #000;
  font: italic 85%/1 arial, sans-serif;
  padding: 1em 0;
  text-align: center;
}

.pure-table td,
.pure-table th {
  border-left: 1px solid #cbcbcb;
  border-width: 0 0 0 1px;
  font-size: 20px;
  margin: 0;
  overflow: visible;
  text-align: center;
  padding: 15px 60px;
}

.pure-table thead {
  background-color: #e0e0e0;
  color: #000;
  text-align: left;
  vertical-align: bottom;
}

.pure-table td {
  background-color: transparent;
}

.pure-table-bordered td {
  border-bottom: 1px solid #cbcbcb;
}

.pure-table-bordered tbody > tr:last-child > td {
  border-bottom-width: 0;
}
.swiper_list {
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: #FFFFFF;
  .tea-analusis {
    position: relative;
    width: 85%;
    height: auto;
    padding: 10px;
    background: #ffff;
    border-radius: 5px;
  }
  .line_border {
    width: 97%;
    height: 10px;
    position: absolute;
    border: 1px solid #e6e6e6;
    background: #e6e6e6;
    top: 55%;
  }
  .left-arrow,
  .right-arrow {
    position: absolute;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: center;
    bottom: -25%;
    z-index: 2;
    .arrow-img {
      &:hover {
        background: #68bb97;
      }
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #dadada;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      i {
        background: url('../../../assets/teacher/IntelligentCorrection/icon-arrow.png') no-repeat center center;
        width: 16px;
        height: 16px;
      }
    }
  }
  .left-arrow {
    left: 8px;
    .arrow-img {
      transform: rotate(180deg);
    }
  }
  .right-arrow {
    right: 6px;
  }
  .imgbox {
    //width: 20%;
    //font-size: 18px;
    padding: 10px;
    display: flex;
    align-items: center;
    //position: relative;
    background: #fff;
    img {
      width: 30px;
      //right: 70%;
      //top: 43%;
      //position: absolute;
    }
    span {
      margin-left: 10px;
      //right: 5%;
      //top: 43%;
      //position: absolute;
    }
  }
}
</style>
  // const reg = /(#@)/g
  // const rep = ''
  // const stem = item.ItemTitle.replace(reg, rep)
  // item.ItemTitle = stem
