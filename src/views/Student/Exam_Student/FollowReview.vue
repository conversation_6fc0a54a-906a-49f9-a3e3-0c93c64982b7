<template>
  <div class="examine whole-mr" style="margin: 0 auto">
    <div class="example" v-if="loadPaper" style="text-align: center; width: 100%">
      <a-spin />
    </div>
    <div class="Answer_header">
      <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
      <span>{{ paperName }}</span>
      <div
        class="xuekeStyle"
        :style="{ 'background-color': bgColog[SubjectIdName.Id + 'bg'], color: bgColog[SubjectIdName.Id] }"
      >
        {{ SubjectIdName.name }}
      </div>
    </div>
    <div class="Answer_content">
      <div class="div" style="width: 70%">
        <div class="flex">
          <div
            class="zuheti padding_t_20 padding_lr_10 formatImg pc_font_size18 font_size28 ipad_font_size35"
            style="width: 30%"
            v-if="zuheti.length > 0 && zuhetiTitle"
          >
            <p v-html="zuhetiTitle" v-katex style="max-width: 600px; min-width: 300px"></p>
          </div>
          <div class="flex_item_grow">
            <div v-for="(item, index) in paperList" :key="index" v-katex>
              <div class="alone-tit" v-if="Index_num === index">
                <div class="tit_header">
                  <span class="spantype font_size24 ipad_font_size26">{{ item.PaperItemType }}</span>
                </div>
                <div class="titleindex">
                  <div class="divleft">
                    <span style="width: 18px; vertical-align: middle" class="font_size24 ipad_font_size26"
                      >第{{ index + 1 }}题</span
                    >
                    <el-button
                      class="btn_Prex font_size16 ipad_font_size20"
                      @click="prex(index, item)"
                      :disabled="preDisabled"
                      >上一题</el-button
                    >
                    <el-button
                      class="btn_next font_size16 ipad_font_size20"
                      @click="next(index, item)"
                      :disabled="nextDisabled"
                      >下一题</el-button
                    >
                  </div>
                  <div class="divright">
                    <span style="margin-right: 32px">
                      <a-progress
                        type="circle"
                        :percent="Accracy"
                        :width="newWidth"
                        :strokeColor="stuStatistics"
                        :format="
                          () => {
                            if (Accracy === 100) {
                              return '100%'
                            } else {
                              return Accracy + '%'
                            }
                          }
                        "
                      /><br />
                      <span class="font_size18 ipad_font_size20">正确率</span></span
                    >
                    <span>
                      <a-progress
                        type="circle"
                        :percent="(correctNum / paperSubjectNum) * 100"
                        :width="newWidth"
                        :format="() => correctNum + '/' + paperSubjectNum"
                        :strokeColor="stuStatistics"
                      >
                      </a-progress
                      ><br />
                      <span class="font_size18 ipad_font_size20">正确题数</span>
                    </span>
                  </div>
                </div>
                <span
                  class="font_size26 ipad_font_size30"
                  style="width: 18px"
                  id="Imgid"
                  v-html="item.ItemTitleWeb"
                  v-katex
                ></span>
                <div>
                  <!-- 选择题 -->
                  <div v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11" v-katex>
                    <span
                      v-for="(k, j) in item.Options"
                      :key="j"
                      style="display: block"
                      class="font_size22 ipad_font_size26"
                    >
<!--        这里注释的历史代码不能删除     -->
<!--                      <img-->
<!--                        v-if="item.IsKeyTrue === 1 && item.UserAnswer === k.Opt"-->
<!--                        style="width: 18px; vertical-align: middle"-->
<!--                        src="@/assets/student/对号.png"-->
<!--                        alt=""-->
<!--                      />-->
<!--                      <img-->
<!--                        v-else-if="item.IsKeyTrue === 2 && item.UserAnswer === k.Opt"-->
<!--                        style="width: 18px; vertical-align: middle"-->
<!--                        src="@/assets/student/叉号.png"-->
<!--                        alt=""-->
<!--                      />-->
<!--                      <span style="vertical-align: middle">-->
<!--                        {{ k.Opt }}.&nbsp;&nbsp;<span v-html="k.Content" v-katex></span-->
<!--                      ></span>-->
<!--                      <div v-else style="display: flex;align-items: baseline;">-->
                      <div style="display: flex;align-items: baseline;">
                        <img
                          v-if="item.IsKeyTrue === 1 && item.UserAnswer === k.Opt"
                          style="width: 18px; vertical-align: middle"
                          src="@/assets/student/对号.png"
                          alt=""
                        />
                      <img
                        v-else-if="item.IsKeyTrue === 2 && item.UserAnswer === k.Opt"
                        style="width: 18px; vertical-align: middle"
                        src="@/assets/student/叉号.png"
                        alt=""
                      />
                        <span v-else class="Radio_raduis"></span>
                        <span style="vertical-align: middle">{{ k.Opt }}.</span>
                        <p v-html="k.Content" v-katex></p>
                      </div>
                    </span>
                  </div>

                  <!-- 多选题 -->
                  <div v-if="item.ItemTypeId === 10" v-katex>
                    <div v-for="k in item.Options" :key="k.Opt" class="font_size22 ipad_font_size26">
                      <img
                        v-if="k.IsTrue === 1 && item.UserAnswer.indexOf(k.Opt) != -1"
                        style="width: 18px; vertical-align: middle"
                        src="@/assets/student/对号.png"
                        alt=""
                      />
                      <img
                        v-else-if="k.IsTrue === 0 && item.UserAnswer.indexOf(k.Opt) != -1"
                        style="width: 18px; vertical-align: middle"
                        src="@/assets/student/叉号.png"
                        alt=""
                      />
                      <span v-else class="check_box"></span>
                      <span style="vertical-align: middle">
                        {{ k.Opt }}.&nbsp;&nbsp;<span v-html="k.Content" v-katex></span
                      ></span>
                    </div>
                  </div>

                  <!-- 普通填空题 -->
                  <!-- <div v-if="item.ItemTypeId === 5">
                <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答：</span>
                <span style="margin-left: 10px;" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" v-for="(blank, ind) in item.UserAnswer">{{blank}}</span>
              </div> -->

                  <!-- 可变行下拉填空题 -->
                  <div class="select_46" v-if="item.ItemTypeId === 46 && IsPhotograph === 0">
                    <span
                      :class="[
                        { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                        'font_size22',
                        'ipad_font_size24',
                      ]"
                      >我的作答</span
                    >
                    <div v-for="(sel, ind) in item.ItemChange.Rows" :key="ind">
                      <span
                        class="font_size22 ipad_font_size24"
                        v-for="(sel, indx) in sel"
                        :key="indx"
                        v-html="sel"
                        v-katex
                        :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }"
                      ></span>
                    </div>
                  </div>
                  <!-- 表格题 -->
                  <RenderTable
                    v-if="item.ItemTypeId === 51 && IsPhotograph === 0"
                    :TableItemInfo="item.ItemChange.TableItemInfo"
                    :showTitle="true"
                  ></RenderTable>
                  <!-- 可变行表格 -->
                  <div class="table_40" v-if="item.ItemTypeId === 40 && IsPhotograph === 0">
                    <span
                      :class="[
                        { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                        'font_size22',
                        'ipad_font_size24',
                      ]"
                      >我的作答</span
                    >
                    <table class="tableTitle">
                      <tr v-for="(item, index) in item.ItemChange.Titles" :key="index">
                        <td>{{ item }}</td>
                      </tr>
                      <tbody>
                        <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                          <td v-for="(td, indx) in td" :key="indx">
                            <span v-html="td" v-katex></span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <div v-if="item.ItemTypeId === 42 && IsPhotograph === 0">
                    <table class="pure-table pure-table-bordered">
                      <thead>
                        <tr>
                          <th v-for="(item, index) in item.ItemChange.Titles" :key="index">{{ item }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                          <td v-for="(td, indx) in td" :key="indx">
                            <span v-html="td" v-katex></span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- 可变行填空 -->
                  <div class="blank_41" v-if="item.ItemTypeId === 41 && IsPhotograph === 0">
                    <span
                      :class="[
                        { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                        'font_size22',
                        'ipad_font_size24',
                      ]"
                      >我的作答</span
                    >
                    <div class="Blank_box" v-for="(blank, indx) in item.ItemChange.Rows" :key="indx">
                      <span
                        :class="[
                          { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                          'font_size22',
                          'ipad_font_size24',
                        ]"
                        class="blank_Answer"
                        >答案{{ indx + 1 }}</span
                      >
                      <span
                        :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }"
                        v-for="(i, ind) in blank"
                        :key="ind"
                      >
                        <p class="font_size22 ipad_font_size23" v-html="i" v-katex></p>
                      </span>
                    </div>
                  </div>

                  <div v-if="item.ItemTypeId === 23 && IsPhotograph === 0">
                    <img :src="item.PicUrl" alt="" style="max-width: 350px" />
                  </div>

                  <span
                    class="fl"
                    v-if="item.ItemTypeId == 50 && IsPhotograph === 0"
                    :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }"
                    >正确答案：</span
                  >
                  <!-- 正确答案-连线题 -->
                  <matching
                    v-if="item.ItemTypeId == 50 && IsPhotograph === 0"
                    :Answer="item.Answer"
                    :SureAnser="item.Answer"
                    :linkExamData="item.ItemChange.LinkExamData"
                    :SubType="2"
                  ></matching>
                </div>
                <p class="line"></p>
                <p class="clearfix" v-if="item.ItemTypeId !== 46 && item.ItemTypeId !== 40 && item.ItemTypeId !== 41">
                  <span
                    class="fl"
                    :class="[
                      { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                      'font_size22',
                      'ipad_font_size24',
                    ]"
                    >我的作答：</span
                  >
                  <span
                    v-if="item.ItemTypeId != 36 && item.ItemTypeId != 50 && item.ItemTypeId != 51 && IsPhotograph === 0"
                    class="fl"
                    :class="[
                      { correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 },
                      'font_size22',
                      'ipad_font_size24',
                    ]"
                    v-html="item.ShowUserAnswer"
                    v-katex
                  ></span>
                  <SubjectiveStemPhoto
                    v-if="(item.ItemTypeId == 36 && item.ReviewContentType == 1) || IsPhotograph === 1"
                    :Answer="item.PicUrl"
                    useType="analysis"
                    ref="SubjectiveStemPhoto"
                  ></SubjectiveStemPhoto>
                  <SubjectiveStemVideo
                    v-if="item.ItemTypeId == 36 && item.ReviewContentType == 3 && IsPhotograph === 0"
                    :Answer="item.PicUrl"
                    useType="analysis"
                    ref="SubjectiveStemVideo"
                  ></SubjectiveStemVideo>
                  <SubjectiveStemAudio
                    v-if="item.ItemTypeId == 36 && item.ReviewContentType == 2 && IsPhotograph === 0"
                    :Answer="item.PicUrl"
                    useType="analysis"
                    ref="SubjectiveStemAudio"
                  ></SubjectiveStemAudio>
                  <!-- 学生的作答-连线题 -->
                  <matching
                    v-if="item.ItemTypeId == 50 && IsPhotograph === 0"
                    :Answer="item.ShowUserAnswer"
                    :SureAnser="item.Answer"
                    :linkExamData="item.ItemChange.LinkExamData"
                    :SubType="1"
                  ></matching>
                  <br v-if="item.ItemTypeId === 51 && IsPhotograph === 0" />
                  <RenderTable
                    v-if="item.ItemTypeId === 51 && IsPhotograph === 0"
                    :TableItemInfo="item.ItemChange.TableItemInfo"
                    :analysis="true"
                  ></RenderTable>
                </p>
                <div v-show="item.Evaluation !== null">
                  <p style="color: #00aaff">老师首次评价:</p>
                  <el-tag effect="dark">
                    {{ item.Evaluation == 0 ? "答对1/4" : item.Evaluation == 1 ? "答对1/2" : "答对3/4" }}
                  </el-tag>
                  <div class="Comments" v-if="item.Comments !== null">
                    <!--                <el-input-->
                    <!--                  style="width: 28%;"-->
                    <!--                  type="textarea"-->
                    <!--                  autosize-->
                    <!--                  disabled-->
                    <!--                  v-model="item.Comments"-->
                    <!--                >-->
                    <!--                </el-input>-->
                    {{ item.Comments }}
                  </div>
                </div>
                <span class="cur font_size24 ipad_font_size26" style="color: #68bb97">解析:</span>
                <span v-show="item.Analysis !== ''" class="font_size24 ipad_font_size26"
                  ><span v-html="item.Analysis" v-katex></span
                ></span>
                <span v-show="item.Analysis === ''" class="font_size24 ipad_font_size26"><span>暂无解析</span></span>
                <div v-if="item.ItemAnalysisFile !== null">
                  <span>解析文件：</span>
                  <VideoPlay
                    v-if="item.ItemAnalysisFile.FileType == 1"
                    ref="VideoPlay"
                    width="240px"
                    height="180px"
                    :url="item.ItemAnalysisFile.VideoUrl"
                    :posterUrl="item.ItemAnalysisFile.CoverUrl"
                    style="text-align: left"
                  ></VideoPlay>
                  <!-- v-if="item.FileType == 2" 音频 -->
                  <SubjectiveStemAudio
                    v-if="item.ItemAnalysisFile.FileType == 2"
                    :Answer="item.ItemAnalysisFile.VideoUrl"
                    useType="analysis"
                    ref="SubjectiveStemAudio2"
                  ></SubjectiveStemAudio>
                </div>
                <div v-if="item.VideoCoverUrl !== '' && item.VideoUrl !== ''">
                  <div class="paper_type">
                    <div class="type_after"></div>
                    <span>视频讲解</span>
                  </div>
                  <div>
                    <video-player
                      class="video-player vjs-custom-skin"
                      ref="videoPlayer"
                      :playsinline="true"
                      :options="playerOptions"
                    >
                    </video-player>
                  </div>
                </div>
                <br />
                <!--        跳转AI会话页面       -->
                <div class="goAIhuihua">
                  <img src="@/assets/AIImg/import.png"/>
                  <div class="_text">
                    <p>HI,{{UserName}}同学，我是你的AI智能老师</p>
                    <p>你可以向我们咨询这道题的相关解题思路哦！</p>
                    <el-button @click="goAIPagesAndComponents(item)">立即提问</el-button>
                  </div>
                </div>
                <!--        跳转AI会话页面       -->
                <el-button
                  v-if="item.IsKeyTrue === 2"
                  type="primary"
                  class="font_size18 ipad_font_size20"
                  @click="ItemHelp(item)"
                  >求助老师辅导</el-button
                >
                <!-- 教师辅导  -->
                <div >
                  <TeacherGuide ref="TeacherGuide" :ItemId ="item.ItemId" ></TeacherGuide>
                </div>
                <div v-for="(st, idx) in StsquareList" :key="idx">
                  <div v-if="item.ItemId == st.ItemId">
                    <div class="paper_type">
                      <div class="type_after"></div>
                      <span class="font_size20 ipad_font_size22">互助学习广场:可以问问这些同学噢~~</span>
                    </div>
                    <div class="paper_photo" v-if="st.ItemQuestions.length != 0">
                      <span v-for="st_i in st.ItemQuestions" class="Image_s">
                        <img style="width: 50px; height: 50px; border-radius: 100%" :src="st_i.ImageUrl" alt="" />
                        <br />
                        <span class="font_size18 ipad_font_size20">{{ st_i.RealName }}</span>
                      </span>
                    </div>
                    <div class="paper_photo" v-if="st.ItemQuestions.length == 0">
                      <span class="font_size20 ipad_font_size22">暂无</span>
                    </div>
                  </div>
                </div>
                <!--
            <div class="btn_text">
              <el-button class="btn_Prex" @click="prex(index, item)" :disabled="preDisabled">上一题</el-button>
              <el-button class="btn_next" @click="next(index, item)" :disabled="nextDisabled">下一题</el-button>
            </div> -->

                <!-- 本题辅导 -->
                <div v-if="item.ItemVideoBinding || item.LearningGuidance ">
                  <div class="paper_type">
                    <div class="type_after"></div>
                    <span class="font_size20 ipad_font_size22" v-if="LoginSource == 2">学习指导</span>
                    <span class="font_size20 ipad_font_size22" v-else>本题辅导</span>
                  </div>
                  <div>
                    <p v-html="item.LearningGuidance" v-katex></p>
                    <VideoPlay
                      v-if="item.ItemVideoBinding?.FileType == 1"
                      ref="VideoPlay"
                      width="240px"
                      height="180px"
                      :url="item.ItemVideoBinding?.VideoUrl"
                      :posterUrl="item.ItemVideoBinding?.CoverUrl"
                      style="text-align: left"
                    ></VideoPlay>
                    <!-- v-if="item.FileType == 2" 音频 -->
                    <SubjectiveStemAudio
                      v-if="item.ItemVideoBinding?.FileType == 2"
                      :Answer="item.ItemVideoBinding?.VideoUrl"
                      useType="analysis"
                      ref="SubjectiveStemAudio2"
                    ></SubjectiveStemAudio>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <div class="swiper_list">
          <div class="line_border"></div>
          <swiper class="swiper" ref="mySwiper" :options="swiperOption">
            <swiper-slide style="text-align: center" v-for="(i, t) in paperList" :key="t">
              <a
                class="title-btn-whole"
                :class="{
                  'current-answer': i.CurrentAnswer,
                  'def-color': i.IsKeyTrue === 1,
                  'wrong-bgc': i.IsKeyTrue === 2,
                }"
                @click="switchTitle(t, i)"
              >
                {{ t + 1 }}
              </a>
            </swiper-slide>
          </swiper>
          <div class="left-arrow">
            <div class="arrow-img" @click="prevPhoto">
              <i></i>
            </div>
          </div>
          <div class="right-arrow">
            <div class="arrow-img" @click="nextPhoto">
              <i></i>
            </div>
          </div>
        </div>
        <div class="imgbox">
          <img src="@/assets/student/闹钟.png" alt="" />
          <span class="pc_font_size18 font_size20 ipad_font_size22"> 答题用时{{ doPaperTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ExamFormatCommon from "@/common/ExamFormatCommon"
import { CorrespondingColor } from "@/utils/subjectCorrespondingColor"
import { arrlistTitle } from "@/utils/bankedCloze/bankedCloze"
import SubjectiveStemPhoto from "./components/subjectiveExam/answerPhoto.vue"
import SubjectiveStemVideo from "./components/subjectiveExam/answerVideo.vue"
import SubjectiveStemAudio from "./components/subjectiveExam/answerAudio.vue"
import matching from "@/components/matching/index"
import RenderTable from "./components/tableStem/renderTable.vue"
import isMobile from "@/utils/isMobile/isMobile"
import VideoPlay from "@/components/SingleVideoCoach/VideoPlay.vue"
import TeacherGuide from './components/TeacherGuide'
import { Session } from "@/utils/storage"

export default {
  components: {
    SubjectiveStemPhoto,
    SubjectiveStemVideo,
    SubjectiveStemAudio,
    matching,
    RenderTable,
    VideoPlay,
    TeacherGuide,
  },
  created() {
    this.userId = localStorage.getItem("UserId")
    this.StClassId = localStorage.getItem("StClassId")
    this.paperId = this.$route.query.paperId
    //  多学科获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)
    this.getPaperList()
    this.getPaperInfor()
    this.roundSize()
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split("?")[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === "/Student/Exam_Student/FollowReview?" + ChangId) {
        this.loadPaper = true
        this.getPaperInfor()
        this.getPaperList()
      }
    },
    Index_num: {
      handler(now) {
        if (now < 1) {
          this.preDisabled = true
          this.nextDisabled = false
        }

        if (now >= this.paperList.length - 1) {
          this.nextDisabled = true
        }

        if (now > 0 && now < this.paperList.length - 1) {
          this.nextDisabled = false
          this.preDisabled = false
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      preDisabled: true, // 上禁用按钮
      nextDisabled: false, // 下禁用按钮
      Index_num: 0, // 当前题目
      // 加载
      loadPaper: true,
      doPaperTime: "",
      Accracy: 0,
      userId: "",
      paperId: "",
      // 试卷名称
      paperName: "",
      chapter: "",
      chapterId: "",
      // 默认第一题
      defalutCol: "",
      // 切换题目数
      newSubject: 1,
      // 题目数
      paperSubjectNum: "",
      // 错题数
      wrongNum: "",
      // 真确题数
      correctNum: "",
      // 是否要订正
      revision: false,
      // 试卷信息
      paperList: [],
      Arr_options: [],
      // 精度
      stuStatistics: "#3CB98F",
      analysis: false,
      StClassId: "",
      playStatus: "",
      muteStatus: "",
      isMute: true,
      isPlay: false,
      width: "820", // 设置视频播放器的显示宽度（以像素为单位）
      height: "500", // 设置视频播放器的显示高度（以像素为单位）
      autoplay: "",
      preload: "auto", //  建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
      controls: true, // 确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
      StsquareList: [],
      radioStyle: {
        display: "block",
      },
      playerOptions: {
        playbackRates: "",
        autoplay: false,
        controls: true,
        preload: "auto",
        muted: false,
        loop: false,
        language: "zh-CN",
        aspectRatio: "16:9",
        fluid: true,
        sources: [
          {
            type: "video/webm",
            src: "",
          },
        ],
        poster: "",
        width: document.documentElement.clientWidth,
      },
      swiperOption: {
        slidesPerView: 'auto', // 自动计算显示数量
        spaceBetween: 10,
        freeMode: true,
        speed: 1000,
        breakpoints: {
          // 当宽度 <= 480px
          480: {
            slidesPerView: 5,
            spaceBetween: 5
          },
          // 当宽度 <= 768px
          768: {
            slidesPerView: 8,
            spaceBetween: 8
          },
          // 当宽度 <= 1024px
          1024: {
            slidesPerView: 12,
            spaceBetween: 10
          },
          // 当宽度 > 1024px
          1200: {
            slidesPerView: 20,
            spaceBetween: 10
          }
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
          hideOnClick: true,
          disabledClass: "my-button-disabled",
          hiddenClass: "my-button-hidden",
        },
      },
      //  多学科部分
      bgColog: {},
      SubjectIdName: {},
      // 正确率和题数图形宽度
      newWidth: null,
      IsPhotograph: null,
      zuheti: [],
      zuhetiTitle: "",
      // 用户名称
      UserName: localStorage.getItem('userName'),
      AitheAnswer: {}
    }
  },
  computed:{
    LoginSource(){
      return localStorage.getItem('LoginSource')
    }
  },
  methods: {
    roundSize() {
      if (isMobile()) {
        this.newWidth = 50
      } else {
        this.newWidth = 65
      }
    },
    GoBack() {
      this.$router.go(-1)
    },
    prevPhoto() {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto() {
      this.$refs.mySwiper.$swiper.slideNext()
    },
    // 跳转AI会话页
    goAIPagesAndComponents(item){
      // console.log(item)
      // console.log(this.AitheAnswer)
      // return false
      const { Options } = item
      let options = []
      if(Options.length > 0){
        Options.forEach(item => {
          options.push(item.Opt+'：'+item.Content)
        })
      }
      options = options.join('|')
      const { GradeName, SemesterName, TextbookName, SubjectName, UnitName, ChapterName } = this.AitheAnswer
      const text = `你是一个专业的【${SubjectName}学科】老师，能为学生提供个性化、极具引导性的解题思路，助力学生高效掌握知识。同时，以精准简洁的方式进行教学辅导。这是一个【${GradeName}】、【${SemesterName}】、【${UnitName}】、【${ChapterName}】的题，题目信息为【${item.ItemTitle}】${options !== '' ? '选项：' + options : ''}`
      Session.set('AITEXT', text)
      // if(item.AIDialogueCount < 5){
        let que = {
          PaperId: this.$route.query.paperId,
          ItemId: item.ItemId,
          // Count: item.AIDialogueCount
        }
        this.$router.push({
          path: '/AIPagesAndComponents/index',
          // query: item.AIDialogueCount === 0 ? { text , ...que } : que
          query: { ...que }
        })
      // } else {
      //   this.$confirm('如果还没有明白，咨询下你的任课老师哦', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }).then(() => {
      //     return false
      //   }).catch(() => {
      //     return false
      //   });
      // }
    },
    // 获取试卷基本信息
    getPaperInfor(id) {
      this.$http.post("/Paper/Exam_Paper/GetTheData", { id: this.$route.query.paperId }).then((res) => {
        this.paperName = res.Data.Title
        this.chapterId = res.Data.ChapterId
        setTimeout(() => {
          this.GetImgAll()
        }, 1000)
      })
    },

    GetImgAll() {
      const that = this
      that.$nextTick(() => {
        const MyImg = document.getElementById("Imgid").querySelector("img")
        if (MyImg !== null) {
          MyImg.style.maxWidth = "90%"
        }
      })
    },

    next(index, item) {
      this.preDisabled = false
      item.CurrentAnswer = false
      this.paperList[index + 1].CurrentAnswer = true
      this.$set(this.paperList, index + 1, this.paperList[index + 1])
      if (this.Index_num < this.paperList.length - 1) {
        this.Index_num += 1
      }
      if (this.paperList[index + 1].VideoCoverUrl != "" && this.paperList[index + 1].VideoUrl != "") {
        this.playerOptions.sources[0].src = this.paperList[index + 1].VideoUrl
        this.playerOptions.poster = this.paperList[index + 1].VideoCoverUrl
      }
      this.PaperStudentEvent(this.paperList[index + 1].ItemId)
      let ParentId_c = this.paperList[this.Index_num].ParentId
      this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ""
      this.GetImgAll()
      this.callswiper(index)
    },

    callswiper(index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },

    prex(index, item) {
      item.CurrentAnswer = false
      this.paperList[index - 1].CurrentAnswer = true
      this.$set(this.paperList, index - 1, this.paperList[index - 1])
      if (this.Index_num === 0) {
        this.Index_num = 0
      } else {
        this.Index_num -= 1
      }
      if (this.paperList[index - 1].VideoCoverUrl != "" && this.paperList[index - 1].VideoUrl != "") {
        this.playerOptions.sources[0].src = this.paperList[index - 1].VideoUrl
        this.playerOptions.poster = this.paperList[index - 1].VideoCoverUrl
      }
      this.PaperStudentEvent(this.paperList[index - 1].ItemId)
      let ParentId_c = this.paperList[this.Index_num].ParentId
      this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ""
      this.GetImgAll()
    },

    // 切换题目
    switchTitle(index, i) {
      this.Index_num = index
      this.paperList.forEach((el) => {
        el.CurrentAnswer = false
      })
      i.CurrentAnswer = true
      if (i.VideoCoverUrl != "" && i.VideoUrl != "") {
        this.playerOptions.sources[0].src = i.VideoUrl
        this.playerOptions.poster = i.VideoCoverUrl
      }
      this.PaperStudentEvent(this.paperList[index].ItemId)
      let ParentId_c = this.paperList[this.Index_num].ParentId
      this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ""
      this.GetImgAll()
    },

    // 学生触发事件
    async PaperStudentEvent(ItemId) {
      const params = {
        userId: localStorage.getItem("UserId"),
        paperId: this.$route.query.paperId,
        itemId: ItemId,
        type: 1,
        videoId: "",
      }
      const res = await this.$uwonhttp.post("/Paper/Paper/PaperStudentEvent", params)
    },

    // 复习回顾题目详情
    async getPaperList() {
      const res = await this.$uwonhttp.post("/Paper/Paper/GetSudentFinishPaperContent", {
        paperId: this.$route.query.paperId,
        userId: this.userId,
      })
      if (res.data.Success) {
        // AI首次会话值
        this.AitheAnswer = res.data.Data
        // 请误触碰
        this.zuheti = ExamFormatCommon.filterZuheti(res.data.Data.Items)
        res.data.Data.Items[0].CurrentAnswer = true
        this.paperList = res.data.Data.Items
        this.IsPhotograph = res.data.Data.IsPhotograph
        const obj = this.paperList[0]
        if (obj.VideoCoverUrl != "" && obj.VideoUrl != "") {
          this.playerOptions.sources[0].src = obj.VideoUrl
          this.playerOptions.poster = obj.VideoCoverUrl
        }
        this.defalutCol = res.data.Data.Items[0].ItemId
        this.doPaperTime = res.data.Data.DoTime
        this.Accracy = res.data.Data.Accracy
        // 题目数
        this.paperSubjectNum = res.data.Data.Items.length
        this.paperList.forEach((item, index) => {
          // 选词填空题
          if (
            (item.ItemTypeId == 52 && item.ItemChange.SelectFillBlank !== null && this.IsPhotograph !== 1) ||
            (item.ItemTypeId == 70 && item.ItemChange.MultitermSingle !== null && this.IsPhotograph !== 1)
          ) {
            item.ItemTitleWeb = arrlistTitle("展示", item, item.ItemTypeId)
          }
          if (item.ItemTypeId == 5 && this.IsPhotograph !== 1) {
            var Newtype_5 = item.ShowUserAnswer.replace(/\||<.*?>.*?<\/.*?>/g, function (e) {
              if (e === "|") {
                return " , "
              }
              return e
            })
            // Newtype_5 = Newtype_5.replace(/\\\\/g, '\\')
            item.ShowUserAnswer = Newtype_5
            const regx4 = /\$\$\/frac.*?\$\$/g
            const regx4List = item.ItemTitleWeb.match(regx4)
            const reg1 = /\$\$\/frac\{/g
            const reg2 = /\}\{/g
            const reg3 = /\}\$\$/g
            if (regx4List && regx4List.length > 0) {
              regx4List.forEach((item_regx4) => {
                let fenshu = item_regx4
                fenshu = fenshu.replace(
                  reg1,
                  `
                <div style="display:inline-block;vertical-align:middle;margin:0 8px;" >
              `
                )
                fenshu = fenshu.replace(
                  reg2,
                  `
                <hr style="width:100%;height:3px;background:black;position:relative;top:2px;"></hr>
              `
                )
                fenshu = fenshu.replace(
                  reg3,
                  `</div>
              `
                )
                item.ItemTitleWeb = item.ItemTitleWeb.replace(item_regx4, fenshu)
              })
            }

            const regx = /\(\#\&\d+\@\)/g
            const regx1 = /\(\#\&/g
            const regx2 = /\@\)/g
            const regxlist = item.ItemTitleWeb.match(regx)
            regxlist.forEach((item1) => {
              const value = item1.replace(regx1, "").replace(regx2, "")
              const title = item.ItemTitleWeb.replace(
                item1,
                `<input class="input" autocomplete="off" disabled='disabled' type="text" style="width: 100px;height:30px;border: 0px; border-radius:4px; color:#000;border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin:5px 10px;"
                  id="${value}" data-index="${value}"/>`
              )
              item.ItemTitleWeb = title
            })
          }
          if (item.ItemTypeId == 10 && this.IsPhotograph !== 1) {
            var Newtype_10 = item.UserAnswer.replace(/\|/g, ",")
            item.UserAnswer = Newtype_10
          }
          if (item.ItemTypeId == 42 && this.IsPhotograph !== 1) {
            var Newtype_42 = item.UserAnswer.replace(/\|/g, ",")
            item.UserAnswer = Newtype_42
            this.FormatRows(item.ItemChange.Rows)
          }
          item.CurrentAnswer = index === 0
          item.Options.forEach((el) => {
            const regex = new RegExp("<img")
            const reg = new RegExp("\r\n\r\n")
            const ImgContent = el.Content.replace(regex, `<img style="max-width: 90%"`)
            el.Content = ImgContent
          })
        })
        const arr1 = this.paperList.filter((value) => {
          return value.IsKeyTrue === 1
        })
        this.revision = this.paperList.some((value) => {
          return value.IsKeyTrue === 2
        })
        this.correctNum = arr1.length
        this.loadPaper = false
        let sIndex = this.$route.query.sIndex || 1
        if (sIndex) {
          this.switchTitle(sIndex - 1, this.paperList[sIndex - 1])
        }
        this.GetWebItemQuestionStudentList()
        let ParentId_c = this.paperList[this.Index_num].ParentId
        this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).ItemTitleWeb : ""
      }
    },
    FormatRows(arr) {
      const regx = /\(\#@\)/g
      arr.forEach((td, idx, ary) => {
        td.forEach((i, index, array) => {
          const TableRegx = i.match(regx)
          if (TableRegx !== null) {
            TableRegx.forEach((item1) => {
              const value = item1.replace(regx, "")
              const title = i.replace(
                item1,
                `<input class="input" autocomplete="off" disabled='disabled' type="text" style="width: 100px;height:30px;border: 0px; border-radius:4px; color:#000;border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin: 0 2px 9px 2px;"
                      id="${idx + "." + index}"   data-index="${index}"/>`
              )
              array[index] = title
            })
          }
        })
      })
    },

    // 选项选择
    onChange(value) {},

    // bc订正
    toBCRevision(paperName) {
      // this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
      const paperId = this.$route.query.paperId
      this.$router.push({
        path: "/Student/Exam_Student/AnswerDetail",
        query: {
          paperId,
          paperName: this.paperName,
        },
      })
    },

    // Web获取互助广场
    async GetWebItemQuestionStudentList() {
      const params = {
        classId: this.StClassId,
        paperId: this.$route.query.paperId,
        userId: this.userId,
        // itemId:
      }
      const res = await this.$uwonhttp.post("/ExamItem/ExamItem/GetWebItemQuestionStudentList", params)
      if (res.data.Success) {
        this.StsquareList = res.data.Data
      }
    },

    async ItemHelp(item) {
      const params = {
        paperId: this.$route.query.paperId,
        itemId: item.ItemId,
        userId: this.userId,
      }
      const res = await this.$uwonhttp.post("/ExamItem/ExamItem/StudentItemMutualHelp", params)
      if (res.data.Success) {
        this.$notify({
          title: "求助成功",
          message: "老师，我需要您辅导我这道题目！",
          type: "success",
        })
      } else {
        this.$notify({
          title: "提示",
          message: res.data.Msg,
          type: "warning",
        })
      }
    },
  },
}
</script>

<style lang="less" scoped>
@import "../../../styles/common";
@import "../../../assets/font/font.css";
// AI会话样式
.goAIhuihua{
  width: 38.5%;
  position: relative;
  img{
    width: 100%;
  }
  ._text{
    position: absolute;
    right: 60px;
    bottom: 18px;
    p{
      font-size: 22px;
      color: #8DB79CFF;
      font-family: '优设标题黑';
      margin: 0;
      letter-spacing: 1px;
    }
    p:nth-child(2){
      position: relative;
      top: -18px;
    }
    .el-button{
      font-size: 18px;
      background-color: #8DB79CFF;
      color: #FFFFFF;
      font-family: '优设标题黑';
      position: relative;
      top: -16px;
      border: none;
      letter-spacing: 1px;
      padding: 10px 16px;
    }
  }
}
.zuheti {
  width: 30%;
  padding-right: 20px;
  margin-right: 20px;
  border-right: 1px solid #e6e6e6;
}
.Comments {
  width: 22%;
  padding: 2px 10px;
  border-radius: 4px;
  color: #232323;
  border: 1px solid #808080;
  background-color: rgba(250, 250, 250, 60);
  opacity: 0.8;
  font-size: 15px;
  ///deep/.el-textarea__inner {
  //  background-color: #fafafa !important;
  //  border-color: #c7c7c7 !important;
  //  color: #595959 !important;
  //  cursor: not-allowed;
  //}
}
.f-t {
  font-size: 21px;
}
// 多学科部分
.xuekeStyle {
  padding: 4px 18px;
  border-radius: 100px;
}
// 正确颜色
.correct {
  color: #68bb97;
}
.answer-wrong {
  color: #ff682c;
}
.wrong-bgc {
  border: 2px solid #da1b1b;
  color: #da1b1b;
}
.def-color {
  border: 2px solid #61bb96;
  color: #61bb96;
}

.current-answer {
  border-color: #357bee;
  color: #357bee;
}

.swiper_list {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  // width: 85%;
  height: auto;
  padding: 10px;
  background: #ffff;
  .line_border {
    width: 97%;
    height: 10px;
    position: absolute;
    border: 1px solid #e6e6e6;
    background: #e6e6e6;
    top: 55%;
  }
  .left-arrow,
  .right-arrow {
    position: absolute;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: center;
    bottom: -25%;

    z-index: 2;
    .arrow-img {
      &:hover {
        background: #68bb97;
      }
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #dadada;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      i {
        background: url("../../../assets/teacher/IntelligentCorrection/icon-arrow.png") no-repeat center center;
        width: 16px;
        height: 16px;
      }
    }
  }
  .left-arrow {
    left: 8px;
    .arrow-img {
      transform: rotate(180deg);
    }
  }
  .right-arrow {
    right: 6px;
  }
}
.imgbox {
  //width: 20%;
  //font-size: 18px;
  padding: 10px;
  display: flex;
  align-items: center;
  //position: relative;
  img {
    width: 30px;
    //right: 70%;
    //top: 43%;
    //position: absolute;
  }
  span {
    margin-left: 10px;
    //right: 5%;
    //top: 43%;
    //position: absolute;
  }
}
.titleindex {
  display: flex;
  justify-content: space-between;
  .divleft {
    width: 50%;
  }
  .divright {
    width: 50%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: -50px;
    text-align: center;
    position: relative;
    bottom: 40px;
    @media screen and (min-width: 1024px) and (max-width: 1440px) {
      /deep/.ant-progress-circle .ant-progress-text {
        font-size: 18px;
      }
    }
    @media screen and (min-width: 1440px) {
      /deep/.ant-progress-circle .ant-progress-text {
        font-size: 12px;
      }
    }
  }
  /deep/.el-button {
    margin-left: 20px;
  }
}
.select_46 {
  line-height: 30px;
}
.Blank_box {
  border: 1px solid #ccc;
  padding: 15px;
  margin-top: 10px;
  .blank_Answer {
    font-size: 18px;
  }
  span {
    p {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
      white-space: pre-line;
      line-height: 40px;
    }
  }
}
.paper_type {
  display: flex;
  margin-top: 20px;
  padding: 6px;
  .type_after {
    width: 5px;
    height: 30px;
    background: #537566;
    border-radius: 5px;
  }
  span {
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #3e3e3e;
    line-height: 33px;
    margin-left: 10px;
  }
}
.paper_photo {
  display: flex;
  .Image_s {
    text-align: center;
    margin-left: 16px;
  }
}
.Answer_header {
  display: flex;
  justify-content: space-between;
  font-size: 22px;
  padding: 15px;
  background: #ffffff;
  border-radius: 10px;
}
.Answer_content {
  display: flex;
  .div {
    background: #fff;
  }
  .right-content{
    flex: 1;
    margin-left: 15px;
    background-color: white;
  }
  // 单题
  .alone-tit {
    width: 100%;
    padding: 20px;
    font-size: 16px;
    background-color: #fff;
    border-radius: 5px;
    line-height: 45px;
    .line {
      height: 1px;
      border-bottom: 1px solid #f6f6f6;
    }

    .tit_header {
      display: flex;
      position: relative;
      .spantype {
        //font-size: 18px;
        margin-left: 15px;
        color: #3a3a3a;
      }
    }
    .tit_header:before {
      content: "";
      background-color: #537566;
      width: 5px;
      height: 25px;
      position: absolute;
      left: 0;
      top: 20%;
      border-radius: 3px;
    }
    .btn_text {
      text-align: center;
    }
  }
  // 右边
  .div2 {
    padding: 80px 16px 0 16px;
    background-color: #fff;
    .right_header {
      text-align: center;
      img {
        height: 50px;
      }
      span {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: contents;
      }
    }
    .p-chart {
      font-size: 17px;
      display: inline-block;
    }
    // 统计
    .Statistics {
      margin-bottom: 20px;
      margin-top: 20px;
      text-align: center;
    }
  }
}

.table_40 {
  line-height: 40px;
}
.tableTitle {
  border-collapse: collapse;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
.tableTitle tr {
  border: none;
}
.tableTitle td {
  width: 180px;
  height: 50px;
  font-size: 17px;
  cursor: pointer;
  border: 1px inset #cccc;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

.pure-table {
  border-collapse: collapse;
  border-spacing: 0;
  empty-cells: show;
  border: 1px solid #cbcbcb;
}

.pure-table caption {
  color: #000;
  font: italic 85%/1 arial, sans-serif;
  padding: 1em 0;
  text-align: center;
}

.pure-table td,
.pure-table th {
  border-left: 1px solid #cbcbcb;
  border-width: 0 0 0 1px;
  font-size: inherit;
  margin: 0;
  overflow: visible;
  // padding: 0.5em 1em;
  text-align: center;
  padding: 15px 60px;
}

.pure-table thead {
  background-color: #e0e0e0;
  color: #000;
  text-align: left;
  vertical-align: bottom;
}

.pure-table td {
  background-color: transparent;
}

.pure-table-bordered td {
  border-bottom: 1px solid #cbcbcb;
}

.pure-table-bordered tbody > tr:last-child > td {
  border-bottom-width: 0;
}

.Radio_raduis {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 1px solid #ccc;
  vertical-align: middle;
  display: inline-block;
  margin-right: 10px;
}

.check_box {
  width: 15px;
  height: 15px;
  border: 1px solid #ccc;
  display: inline-block;
  vertical-align: middle;
}

.go_work {
  text-align: center;
  /deep/.el-button {
    width: 100px;
    color: #fff;
    background-color: #68bb97;
  }
}

.List-topics {
  margin-top: 20px;
  text-align: center;
  .menu {
    font-size: 18px;
  }
}

// 题目目录
.title-btn-whole {
  display: inline-block;
  width: 41px;
  height: 40px;
  line-height: 40px;
  margin: 15px 0px 0px 8px;
  text-align: center;
  border-radius: 35px;
  cursor: pointer;
  background: #fff;
}
/deep/ .ant-radio-wrapper {
  margin-bottom: 10px;
  white-space: pre-wrap !important;
}
.btn_Prex {
  border-color: #68bb97;
  color: #61bb96;
}
.btn_next {
  background-color: #68bb97;
  border-color: #68bb97;
  color: #fff;
}
</style>
