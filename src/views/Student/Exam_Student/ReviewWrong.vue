<template>
  <div class="examine whole-mr">
    <div class="div" style="width: 70%">
      <p class="navigation">
        <span>复习回顾</span>
        <span class="cur" @click="toMicroClass">微课堂</span>
      </p>
      <p class="f-t title">{{ paperName }}</p>
      <div class="example" v-if="loadPaper" style="text-align: center; width: 100%">
        <a-spin />
      </div>
      <happy-scroll v-if="paperList.length !== 0">
        <div class="alone-tit" v-for="(item, index) in paperList" :key="item.ItemId" :id="item.ItemId" v-katex>
          <div class="tit_header">
            <span>{{item.PaperItemType}}</span>
            <!-- <span style="margin-left: 20px;">难度 :</span>
            <el-rate v-model="item.Level" disabled></el-rate>
            <span style="position:absolute;right: 5%;" v-show="item.Level > 3"><img style="margin-right: 5px;width: 21px;" src="@/assets/拓展题.png" alt="" />拓展题</span> -->
          </div>
          <p>
            <span style="font-size: 18px">({{ index + 1 }}) </span><span style="font-size: 20px" v-html="item.ItemTitleWeb"></span>
            <span>
              <img v-if="item.IsKeyTrue === 1" src="@/assets/student/对号.png" alt="" />
              <img v-if="item.IsKeyTrue === 2" src="@/assets/student/叉号.png" alt="" />
            </span>
          </p>

          <div>
            <!-- 选择题 -->
            <a-radio-group v-model="value" @change="onChange" v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
              <a-radio :style="radioStyle" :value="item.ItemId + '|' + k.Opt" v-for="(k, j) in item.Options" :key="j">
                <span style="font-size: 20px">{{ k.Opt }}</span>.<span v-html="k.Content" style="font-size: 20px"></span>
              </a-radio>
            </a-radio-group>

            <!-- 多选题 -->
            <a-checkbox-group class="Multiple" v-if="item.ItemTypeId === 10">
              <a-row>
                <a-col :span="24">
                  <a-checkbox v-for="i in item.Options" :key="i.Opt" :value="item.ItemId + '|' + i.Opt">
                    {{ i.Option }}:
                    <span v-html="i.Content" style="font-size: 20px"></span>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>

            <!-- 普通填空题 -->
            <div v-if="item.ItemTypeId === 5">
              <span style="margin-left: 10px;" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" v-for="(blank, ind) in item.UserAnswer">{{blank}}</span>
            </div>

            <!-- 可变行下拉填空题 -->
            <div class="select_46" v-if="item.ItemTypeId === 46">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答</span>
              <div v-for="(sel,ind) in item.ItemChange.Rows" :key="ind">
                <span v-for="(sel,indx) in sel" :key="indx" v-html="sel" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }"></span>
              </div>
            </div>

            <!-- 可变行表格 -->
            <div class="table_40" v-if="item.ItemTypeId === 40">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答</span>
              <table class="tableTitle">
                <tr v-for="(item, index) in item.ItemChange.Titles" :key="index">
                  <td>{{ item }}</td>
                </tr>
                <tbody>
                  <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span v-html="td"></span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 可变行填空 -->
            <div class="blank_41" v-if="item.ItemTypeId === 41">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答</span>
              <div class="Blank_box" v-for="(blank, indx) in item.ItemChange.Rows" :key="indx">
                <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" class="blank_Answer">答案{{ indx + 1 }}</span>
                <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" v-for="(i, ind) in blank" :key="ind">
                  <p v-html="i"></p>
                </span>
              </div>
            </div>

            <div v-if="item.ItemTypeId === 23">
              <img :src="item.PicUrl" alt="" style="max-width: 350px" />
            </div>
          </div>
          <p class="line"></p>
          <p class="clearfix" v-if="item.ItemTypeId !== 46  && item.ItemTypeId !== 40 && item.ItemTypeId !== 41 && item.ItemTypeId !== 5">
            <span class="fl" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答：{{ item.UserAnswer }}</span>
          </p>
          <p @click="seeAnalysis(item.ItemId)" class="cur" style="font-size: 18px; color: #68bb97">查看解析</p>
          <p v-show="analysis === item.ItemId && item.Analysis!==''">解析: <span v-html="item.Analysis"></span></p>
          <p v-show="analysis === item.ItemId && item.Analysis===''">解析: <span>暂无解析</span></p>
        </div>
      </happy-scroll>
    </div>
    <div class="div2" style="width: 30%">
      <div class="right_header">
        <img src="@/assets/student/闹钟.png" alt="" />
        <br />
        <br />
        <span>答题用时:{{ doPaperTime }}</span>
        <br />
      </div>
      <div>
        <div class="Statistics">
          <p class="p-chart" style="margin-right: 32px">
            <a-progress type="circle" :percent="Accracy" height="100px" :width="65" :strokeColor="stuStatistics" :format="
                () => {
                  if (Accracy === 100) {
                    return '100%'
                  } else {
                    return Accracy + '%'
                  }
                }
              " /><br />
            <span>正确率</span>
          </p>
          <p class="p-chart">
            <a-progress type="circle" :percent="(correctNum / paperSubjectNum) * 100" height="100px" :width="65" :format="() => correctNum + '/' + paperSubjectNum" :strokeColor="stuStatistics">
            </a-progress><br />
            <span>正确题数</span>
          </p>
        </div>

        <!-- <div class="go_work">
          <el-button v-if="revision" @click="toBCRevision(paperName)">去订正</el-button>
        </div> -->

        <div class="List-topics">
          <p class="menu">答题卡</p>
          <a v-for="(i, t) in paperList" :key="t" class="title-btn-whole" :class="{ 'def-color': defalutCol === i.ItemId, 'wrong-bgc': i.IsKeyTrue === 2 }" @click="switchTitle(t, i.ItemId, i.TypeId)">
            {{ t + 1 }}
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { HappyScroll } from 'vue-happy-scroll'
export default {
  components: {
    HappyScroll
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.getPaperList()
    this.getPaperInfor()
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Student/ReviewWrong?' + ChangId) {
        this.loadPaper = true
        // 获取试卷基本信息
        this.getPaperInfor()
        // 复习回顾题目详情
        this.getPaperList()
      }
    }
  },
  data() {
    return {
      // 加载
      loadPaper: true,
      //
      doPaperTime: '',
      Accracy: 0,
      userId: '',
      paperId: '',
      // 试卷名称
      paperName: '',
      chapter: '',
      chapterId: '',
      // 默认第一题
      defalutCol: '',
      // 切换题目数
      newSubject: 1,
      // 题目数
      paperSubjectNum: '',
      // 错题数
      wrongNum: '',
      // 真确题数
      correctNum: '',
      // 是否要订正
      revision: false,
      // 试卷信息
      paperList: [],
      // 精度
      stuStatistics: '#3CB98F',
      value: 1,
      analysis: false,
      radioStyle: {
        display: 'block'
      }
    }
  },
  methods: {
    seeAnalysis(id) {
      this.analysis = id
    },
    // 获取试卷基本信息
    getPaperInfor(id) {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then(res => {
        this.paperName = res.Data.Title
        this.chapterId = res.Data.ChapterId
      })
    },
    // 复习回顾题目详情
    getPaperList() {
      this.$uwonhttp
        .post('/Paper/Paper/GetSudentFinishPaperContent', { paperId: this.paperId, userId: this.userId })
        .then(res => {
          this.paperList = res.data.Data.Items
          this.defalutCol = res.data.Data.Items[0].ItemId
          this.doPaperTime = res.data.Data.DoTime
          this.Accracy = res.data.Data.Accracy
          // 题目数
          this.paperSubjectNum = res.data.Data.Items.length
          this.paperList.forEach(item => {
            console.log(item, 'item')
            const regex = new RegExp('<img')
            const reg = /(#&\d+@)/g
            const ImgContent = `<img style="max-width: 80%" `
            const rep = ''
            const stem = item.ItemTitleWeb.replace(reg, rep, ImgContent)
            item.ItemTitleWeb = stem
            if (item.ItemTypeId == 5) {
              var Newtype_5 = item.UserAnswer.split('|')
              item.UserAnswer = Newtype_5
            }
          })
          const arr1 = this.paperList.filter(value => {
            return value.IsKeyTrue === 1
          })
          this.revision = this.paperList.some(value => {
            return value.IsKeyTrue === 2
          })
          this.correctNum = arr1.length
          this.loadPaper = false
        })
    },
    // 切换题目
    switchTitle(index, ItemId, typeId) {
      document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.defalutCol = ItemId
    },
    // 选项选择
    onChange(value) {},
    // 微课堂
    toMicroClass() {
      this.$router.push({
        path: '/Student/Exam_Student/MicroClass',
        query: { chapterId: this.chapterId, paperId: this.paperId }
      })
    },
    // bc订正
    toBCRevision(paperName) {
      this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
    }
  }
}
</script>

<style lang="less" scoped>
.f-t {
  font-size: 21px;
}
// 正确颜色
.correct {
  color: #68bb97;
  font-size: 18px;
}
.answer-wrong {
  color: #ff682c;
  font-size: 18px;
}
.wrong-bgc {
  background-color: #ff682c;
}
.def-color {
  background-color: #68bb97;
}
.examine {
  display: flex;
  .div {
    height: 700px;
    margin-right: 23px;
  }
}
.select_46 {
  line-height: 30px;
}
.Blank_box {
  border: 1px solid #ccc;
  padding: 15px;
  margin-top: 10px;
  .blank_Answer {
    font-size: 18px;
  }
  span {
    p {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
      white-space: pre-line;
      line-height: 40px;
    }
  }
}
.table_40 {
  line-height: 40px;
}
.tableTitle {
  border-collapse: collapse;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
.tableTitle tr {
  border: none;
}
.tableTitle td {
  width: 180px;
  height: 50px;
  font-size: 17px;
  cursor: pointer;
  border: 1px inset #cccc;
}
// 右边
.div2 {
  padding: 80px 16px 0 16px;
  background-color: #fff;
  margin-right: 23px;
  .right_header {
    text-align: center;
    img {
      height: 50px;
    }
    span {
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      display: contents;
    }
  }
  .p-chart {
    font-size: 17px;
    display: inline-block;
  }
  // 统计
  .Statistics {
    margin-bottom: 20px;
    margin-top: 20px;
    text-align: center;
  }
  // 错题扫除
  .wrong-topic {
    text-align: center;
    margin-bottom: 80px;
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
    }
  }
}
.go_work {
  text-align: center;
  /deep/.el-button {
    width: 100px;
    color: #fff;
    background-color: #68bb97;
  }
}
.navigation {
  line-height: 38px;
  padding-left: 15px;
  font-size: 22px;
  background-color: #fff;
  border-radius: 5px;
  span:nth-child(1) {
    display: inline-block;
    height: 100%;
    margin-right: 25px;
    border-bottom: 2px solid #68bb97;
  }
}
.title {
  margin: 20px 5px;
}
.List-topics {
  margin-top: 20px;
  text-align: center;
  .menu {
    font-size: 18px;
  }
}
// 单题
.alone-tit {
  width: 100%;
  padding: 10px 30px;
  margin-bottom: 30px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  line-height: 45px;
  .line {
    height: 1px;
    border-bottom: 1px solid #f6f6f6;
  }
  .wrong-storage {
    span {
      display: inline-block;
      width: 190px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      color: #fff;
      background-color: #68bb97;
      border-radius: 30px;
      cursor: pointer;
    }
  }
  // 已辅导
  .tutored {
    display: inline-block;
    width: 74px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    color: #ff682c;
    border: 1px solid #ff682c;
    border-radius: 5px;
    cursor: pointer;
  }
  .tit_header {
    display: flex;
    /deep/.el-rate {
      line-height: 1;
      font-size: 35px;

      margin-left: 10px;
    }
    /deep/.el-rate__icon {
      font-size: 25px;
    }
    span {
      font-size: 21px;
    }
  }
}
// 题目目录
.title-btn-whole {
  display: inline-block;
  width: 41px;
  height: 40px;
  line-height: 40px;
  margin: 16px 16px 0 0;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 35px;
  cursor: pointer;
}
/deep/.happy-scroll-content {
  width: 100%;
}
.happy-scroll {
  height: 84.3%;
}
/deep/ .ant-radio-wrapper {
  margin-bottom: 10px;
  white-space: pre-wrap !important;
}
</style>
