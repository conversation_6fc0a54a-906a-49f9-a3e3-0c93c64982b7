/*
 * @file mindmap.js
 * @brief markdown-it plugin for rendering markmap (mind maps)
 * <AUTHOR> Assistant
 * @date 2025-01-16
 *
 * @description
 * 这个插件允许在 markdown 中使用 ```mindmap 代码块来渲染思维导图
 * 支持流式更新和增量渲染，避免闪烁问题
 *
 * 使用方法:
 * ```mindmap
 * # 中心主题
 * ## 分支1
 * ### 子分支1.1
 * ### 子分支1.2
 * ## 分支2
 * ### 子分支2.1
 * ```
 */

/**
 * markdown-it mindmap 插件
 */
const mindmapPlugin = (md, options = {}) => {
  const defaultOptions = {
    marker: 'mindmap'
  }

  // 保存原始的 fence 规则
  const originalFence = md.renderer.rules.fence || function(tokens, idx, options, env, slf) {
    return slf.renderToken(tokens, idx, options)
  }

  // 重写 fence 规则
  md.renderer.rules.fence = function(tokens, idx, options, env, slf) {
    const token = tokens[idx]
    const info = token.info ? token.info.trim() : ''

    const content = (token.content ?? '').trim()

    // mindmap
    if (info === defaultOptions.marker) {
      return `
      <div class="markmap-container" style="position: relative; width: 100%; border: 1px solid #e8e8e8; border-radius: 8px; overflow: hidden; background: #fff; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <div data-mindmap="1" data-mindmap-content="${encodeURIComponent(content)}" style="width: 100%; height: auto; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
        </div>
      </div>
      `
    }

    return originalFence(tokens, idx, options, env, slf)
  }
}

export default mindmapPlugin
