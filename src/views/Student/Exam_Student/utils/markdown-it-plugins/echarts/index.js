const echartsPlugin = (md) => {
  const defaultRenderer = md.renderer.rules.fence;

  md.renderer.rules.fence = function(tokens, idx, options, env, self) {
    const token = tokens[idx];
    if (token.info === 'echarts_json') {
      const content = token.content;
      return `<div class="echarts-container"  data-echarts-json="${encodeURIComponent(content)}"></div>`;
    }
    return defaultRenderer(tokens, idx, options, env, self);
  };
}


export default echartsPlugin;
