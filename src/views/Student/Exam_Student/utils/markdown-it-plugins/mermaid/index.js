/**
 * @file mermaid/index.js
 * @brief markdown-it plugin for rendering Mermaid diagrams
 * <AUTHOR> Assistant
 * @date 2025-01-16
 *
 * @description
 * 这个插件允许在 markdown 中使用 ```mermaid 代码块来渲染各种图表
 * 支持流程图、甘特图、时序图、类图等多种Mermaid图表类型
 *
 * 使用方法:
 * ```mermaid
 * graph TD
 *     A[开始] --> B{判断条件}
 *     B -->|是| C[执行操作]
 *     B -->|否| D[结束]
 *     C --> D
 * ```
 *
 * ```mermaid
 * gantt
 *     title 项目甘特图
 *     dateFormat  YYYY-MM-DD
 *     section 阶段1
 *     任务1           :a1, 2024-01-01, 30d
 *     任务2           :after a1, 20d
 * ```
 */

const mermaidPlugin = (md, options = {}) => {
  const defaultOptions = {
    marker: 'mermaid'
  }

  const opts = { ...defaultOptions, ...options }

  // 保存原始的 fence 规则
  const originalFence = md.renderer.rules.fence || function(tokens, idx, options, env, slf) {
    return slf.renderToken(tokens, idx, options)
  }

  // 重写 fence 规则
  md.renderer.rules.fence = function(tokens, idx, options, env, slf) {
    const token = tokens[idx]
    const info = token.info ? token.info.trim() : ''
    const content = (token.content ?? '').trim()

    // 检查是否是 mermaid 代码块
    if (info === opts.marker) {
      // 返回包含 Mermaid 代码的 HTML 结构
      return `<div class="mermaid-container" data-mermaid-code="${encodeURIComponent(content)}"></div>`
    }

    // 不是 mermaid 代码块，使用原始渲染器
    return originalFence(tokens, idx, options, env, slf)
  }
}

export default mermaidPlugin
