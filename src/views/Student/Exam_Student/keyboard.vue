<template>
  <div class="key_box" ref="returnTop" v-if="Key_Visible" v-drag>
    <div class="nav">
      <div class="left">
        <div
          class="item"
          :class="{ active: item.active }"
          v-for="item in navList"
          :key="item.value"
          @click="item.active = !item.active"
        >
          {{ item.text }}
        </div>
        <div class="item" @click="changeAll" :class="{ active: isAll }">全部键盘</div>
      </div>
      <span @click="KeyWrod_False" class="keyWord_False">×</span>
    </div>
    <div class="screen">
      <template></template>
      <!-- id="screenBox" -->
      <div class="math-quill-content" @mousemove.stop="() => {}">
        <math-quill ref="mathquill"></math-quill>
      </div>
      <div class="btn" @click="handleSubmit">确认</div>
      <div class="chineseBox" v-show="isPY && allPYList.length">
        <div class="top">{{ searchValue }}</div>
        <div class="bottom">
          <div class="fonts">
            <div class="font" @click="handleEnterPy(item)" v-for="(item, index) in PYList" :key="item">
              {{ index + 1 }}、{{ item }}
            </div>
          </div>
          <div class="func" v-show="PYList.length > 0">
            <i class="el-icon-caret-left" :class="{ disabled: activePYIndex === 0 }" @click="handleNext(false)"></i>
            <i
              class="el-icon-caret-right"
              :class="{ disabled: allPYList.length && (activePYIndex + 1) * 5 >= allPYList.length }"
              @click="handleNext(true)"
            ></i>
          </div>
        </div>
      </div>
    </div>
    <div class="content1">
      <div class="left" id="scrollKeyboard">
        <div v-show="navList[0].active">
          <div class="keybord">
            <div class="name">数字</div>
            <div class="detail">
              <div class="item" v-for="item in defaultList" :key="item" @click="handleClick(item)">
                {{ item }}
              </div>
            </div>
          </div>
          <div class="keybord">
            <div class="name">符号</div>
            <div class="detail">
              <div
                class="item"
                :class="{
                  fs18: [`正确✓`, `错误×`].indexOf(item.text) !== -1,
                }"
                v-for="(item, index) in symbol"
                :key="index"
                @click="handleSymbol(item)"
              >
                <img class="symbolImg" :src="item.imgSrc" v-if="item.imgSrc" alt="" />
                <template v-else> {{ item.text }}</template>
              </div>
            </div>
          </div>
        </div>
        <div class="keybord" v-show="navList[1].active && isHighMode">
          <div class="name">字母<br />符号</div>
          <div class="detail">
            <div class="item" v-for="item in letter" :key="item.text" @click="handleValue(item)">
              {{ item.text }}
            </div>
          </div>
        </div>
        <!-- <div class="keybord" v-show="navList[2].active && isHighMode"> -->
        <!-- <div class="keybord" v-show="navList[2].active && this.type == 'key'"> -->
        <div class="keybord" v-show="navList[2].active">
          <div class="name">公式<br />格式</div>
          <div class="detail" style="width: 1110px">
            <div
              class="item"
              style="font-size: 18px"
              v-for="(item, index) in formula"
              :key="index + 'l'"
              @click="handleFormula(item, index)"
            >
              {{ item.text }}
              <img style="height: 85%" :src="item.imgUrl" v-if="item.imgUrl" alt="" />
            </div>
          </div>
        </div>
        <div
          class="keybord"
          :class="{ chinese: checktop() }"
          v-show="(!isHighMode && navList[1].active) || (isHighMode && navList[3].active)"
        >
          <div class="name">中英文输入</div>
          <div class="detail">
            <div
              class="item"
              @click="enterEnglish(item)"
              :class="{
                pybtn: item === '拼音',
                unActive: item === '拼音' && !isPY,
                isBigFont,
              }"
              v-for="item in english"
              :key="item"
            >
              {{ item }}
            </div>
            <div class="btn btn_big" @click="isBigFont = !isBigFont" :class="{ unActive: !isBigFont }">大小写</div>
            <div class="btn" @click="closePy" :class="{ unActive: isPY }">abc</div>
          </div>
        </div>
        <div class="keybord" v-show="navList[2].active && !isHighMode">
          <div class="name">单位</div>
          <div class="detail">
            <div class="item" v-for="item in unit" @click="handleClick(item)" :key="item">
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="btn" @click="changePosition(0)">→</div>
        <div class="btn" @click="changePosition(1)">←</div>
        <div class="btn" @click="handleClearAll">清空</div>
        <div class="btn" @click="handleDelete">删除</div>
      </div>
    </div>
  </div>
</template>
<script>
import drag from '@/utils/drag'
import katex from 'katex'
// import debounce from '@/utils/debounce'
import eventBus from '@/components/eventBus/eventBus'
import keybordInfo from './keyboardInfo.js'
import Vue from 'vue'
import bus from '@/components/formula/bus'
import MathQuill from '@/components/Mathquill'
export default {
  props: {
    content: {
      type: String,
      default: '',
    },
  },
  components: {
    MathQuill,
  },
  data() {
    return {
      isHighMode: false,
      rootInputId: 'screenBox',
      Key_Visible: false,
      isPY: false,
      // navList: keybordInfo.navList,
      navList: keybordInfo.middleNavList,
      money: '',
      searchValue: '',
      dialogVisible: false,
      info: [],
      activeIndex: -1,
      defaultList: keybordInfo.defaultList,
      symbol: keybordInfo.smallSymbol,
      // symbol: keybordInfo.middleSymbol,
      english: keybordInfo.english,
      unit: keybordInfo.unit,
      letter: keybordInfo.letter,
      formula: keybordInfo.formula,
      keylist5: [],
      smallkeys1: [],
      smallkeys2: [],
      isBigFont: true,
      smallkeys3: [],
      PYList: [],
      allPYList: [],
      activePYIndex: 0,
      selected: false,
      Frac_val: '',
      type: 'sys',
    }
  },
  mounted() {
    bus.parentCompMap = {}
    bus.childCompMap = {}

    bus.parentCompMap[this.rootInputId] = this
    bus.childCompMap[this.rootInputId] = this.info

    bus.getKatexStr = function (elId) {
      var itemList = document.getElementById(elId)?.children || []
      var infoList = bus.childCompMap[elId] || []

      let str = '{'
      infoList.forEach((info, idx) => {
        if (typeof info === 'object') {
          str += itemList[idx].children[0].__vue__?.getKatex() || ''
        } else {
          str += info
        }
      })

      return str + '}'
    }

    bus.removeActive = function () {
      document.querySelectorAll('.screen .active')?.forEach((item) => {
        item.classList.remove('active')
      })
    }

    bus.delete = () => {
      // 获取光标所指向的组件，如果获取不到，说明没有光标，默认不执行删除
      let pItemEl = document.querySelectorAll('.screen .active')
      if (!pItemEl || !pItemEl.length) {
        return
      }

      // 获取光标所指向的输入框
      pItemEl = pItemEl[0]
      if (pItemEl.classList.contains('box')) {
        pItemEl = pItemEl.closest('.item')
      }

      const boxEl = pItemEl.parentNode
      const index = Array.prototype.indexOf.call(boxEl.children, pItemEl)
      // 删除前重新指定当前操作对象
      bus.currentEl = boxEl

      // 删除前去掉光标
      bus.removeActive()

      // 执行删除
      bus.childCompMap[boxEl.id].splice(index, 1)

      this.$nextTick(() => {
        // 删除执行完成后，等待dom更新后进行光标指定
        if (index === 0) {
          if (boxEl.children.length) {
            boxEl.children[boxEl.children.length - 1].classList.add('active')
          } else {
            boxEl.classList.add('active')
            boxEl.classList.add('empty')
          }
        } else {
          boxEl.children[index - 1].classList.add('active')
        }
      })
    }
  },
  computed: {
    isAll() {
      return !this.navList.some((e) => e.active === false)
    },
    isRootActive() {
      if (!bus.currentEl) {
        return true
      }
      return bus.currentEl.id === this.rootInputId
    },
  },
  watch: {
    content(value) {
      const arr = value.split('|')
      this.checkType(arr, this.isHighMode)
    },
    Key_Visible(value) {
      if (!value) {
        this.isPY = false
        this.clearPY()
      } else {
        const arr = this.content.split('|')
        this.checkType(arr, this.isHighMode)
        this.$nextTick(() => {
          this.handleClearAll()
        })
      }
    },
  },
  created() {
    this.getStudentType()
  },

  methods: {
    checktop() {
      if (this.isHighMode) {
        return !(this.navList[0].active || this.navList[1].active || this.navList[2].active) && this.PYList.length > 0
      } else {
        return !this.navList[0].active && this.PYList.length > 0
      }
    },
    clearArr() {
      this.allPYList = []
      this.PYList = []
      this.info = []
      this.searchValue = ''
    },
    async getStudentType() {
      const { data } = await this.$uwonhttp.get('/GooglePinYin/Keyboard/student/year', {
        params: {
          studentId: localStorage.getItem('UserId'),
        },
      })
      const { Data, ErrorCode } = data
      if (ErrorCode === 0) {
        this.isHighMode = Data !== 1
        if (!this.isHighMode) {
          this.symbol = keybordInfo.smallSymbol
          let t = keybordInfo.formula.find((item) => item.comp == 'Frac')
          this.formula = [t]
          // this.navList = keybordInfo.middleNavList
        } else {
          this.symbol = keybordInfo.middleSymbol
        }
      }
    },
    handleSubmit() {
      var value = this.$refs.mathquill.getValue().replace(/\\ \\frac/g,'\\frac')
      this.$emit('updatekey', { value: value, type: this.type })
      this.Key_Visible = false
      eventBus.$emit('showKey_color', this.Key_Visible)
    },
    getKatex(item) {
      const first = item[0]
      if ([`\\`, '{'].indexOf(first) !== -1) {
        return katex.renderToString(item)
      } else {
        return item
      }
    },
    handleValue(item) {
      if (this.type === 'sys') {
        this.handleClick(item.text)
        return
      }
      if (item.tv) {
        this.handleFormula(item)
      } else {
        this.handleClick(item.text)
      }
    },
    checkType(arr, isHighMode) {
      const reg = /[a-zA-Z\u4e00-\u9fa5]/
      const isEnglish = arr.some((e) => {
        if (reg.test(e)) {
          return true
        }
      })
      this.navList.forEach((e) => {
        if (e.value === 'CAE') {
          e.active = !!isEnglish
        }
      })

      if (isHighMode) {
        const letter = []
        this.letter.forEach((e) => {
          letter.push(e.text)
        })
        const isLetter = arr.some((e) => {
          if (letter.indexOf(e) !== -1) {
            return true
          }
        })

        this.navList.forEach((e) => {
          if (e.value === 'letter') {
            e.active = !!isLetter
          }
        })
      } else {
        const str = arr.join('')
        const isUnit = this.unit.some((e) => {
          if (str.indexOf(e) !== -1) {
            return true
          }
        })
        this.navList.forEach((e) => {
          if (e.value === 'unit') {
            e.active = !!isUnit
          }
        })
      }
      const numReg = /[0-9]/
      let numFlag = false
      arr.forEach((e) => {
        if (numReg.test(e)) {
          numFlag = true
          return true
        }
      })

      arr.some((e1) => {
        const isSys = this.symbol.some((e) => {
          let str = e.text
          if (str === '正确✓') {
            str = '✓'
          }
          if (str === '错误×') {
            str = '×'
          }
          if (str === e1) {
            numFlag = true
            return true
          }
        })
        if (isSys) return true
      })
      if (!numFlag) {
        this.navList[0].active = false
      } else {
        this.navList[0].active = true
      }

      const hasAcrive = this.navList.some((e) => e.active === true)
      if (!hasAcrive) {
        this.navList[0].active = true
      }
    },
    createEl() {
      var el = document.createElement('span')
      el.classList.add('item')
      return el
    },
    // 公式
    handleFormula(item) {
      if (this.type === 'sys') {
        this.$refs.mathquill.append(item.text)
        return
      }
      if (item.v) {
        if (Array.isArray(item.v)) {
          item.v.forEach((v) => {
            this.$refs.mathquill.moveMouseRight()
            this.$refs.mathquill.cmd(v)
          })
          this.$refs.mathquill.moveMouseLeft(item.v.length)
        } else {
          // 目前只处理带分数
          if (item.v == '/') this.$refs.mathquill.cmd(' ')
          this.$refs.mathquill.cmd(item.v)
        }
      } else {
        this.$refs.mathquill.append(item.tv, item.index)
      }
      // this.$refs.mathquill.append(value)
      // if (!bus.currentEl) {
      //   bus.currentEl = document.getElementById(this.rootInputId)
      // }
      // bus.currentEl.classList.remove('empty')
      // this.handleClick(item)
    },
    // 删除
    handleDelete() {
      this.$refs.mathquill.backspace()
    },
    // 清空
    handleClearAll() {
      this.$refs.mathquill.clearValue()
    },
    // 光标移动
    changePosition(type) {
      // bus.removeActive()
      if (type) {
        this.$refs.mathquill.moveMouseLeft()
      } else {
        this.$refs.mathquill.moveMouseRight()
      }
    },
    // 符号
    handleSymbol(item) {
      let html = item.text
      if (item.text === '正确✓') {
        html = '✓'
      }
      if (item.text === '错误×') {
        html = '×'
      }

      if (this.type === 'sys') {
        this.handleClick(html)
        return
      }
      if (item.tv) {
        this.handleFormula(item)
      } else {
        this.handleClick(html)
      }
    },
    handleClickInput(event) {
      let target = event.target
      let activeIndex = -1

      bus.removeActive()

      if (target.classList.contains('box')) {
        // 如果当前点击的节点就是某个输入框，保存输入框
        bus.currentEl = target
        if (target.classList.contains('empty')) {
          activeIndex = -1
          target.classList.add('active')
        } else {
          activeIndex = bus.childCompMap[target.id].length - 1
          target.children[target.children.length - 1].classList.add('active')
        }
      } else if (target.classList.contains('item')) {
        // 如果当前点击的节点就是输入框里某一输入项
        bus.currentEl = target.parentNode
        activeIndex = Array.prototype.indexOf.call(bus.currentEl.children, target)
        target.classList.add('active')
      } else {
        // 如果当前点击的不是输入框某一项，或者输入框本身，就向上查找父元素中的输入项和输入框
        target = target.closest('.item')
        bus.currentEl = target.parentNode
        activeIndex = Array.prototype.indexOf.call(bus.currentEl.children, target)
        target.classList.add('active')
      }

      this.activeIndex = activeIndex

      // console.log(bus.getKatexStr(bus.currentEl.id))
    },
    // 插入
    handleClick(value) {
      // console.log(value, 'handleClick')
      // console.log(value)
      // console.log(bus.currentEl,':bus.currentEl')  //初始化mounted时候并没有给bus.currentEl赋值
      this.$refs.mathquill.append(value)
    },
    // 英文输入
    enterEnglish(item) {
      if (item === '拼音') {
        this.isPY = true
      } else {
        if (this.isPY) {
          this.searchValue += item
          this.getPYResult()
        } else {
          let value = item
          if (this.isBigFont) {
            value = value.toUpperCase()
          }
          this.handleClick(value)
        }
      }
    },
    closePy() {
      this.isPY = false
      this.clearPY()
    },
    handleEnterPy(item) {
      for (let i = 0; i < item.length; i++) {
        setTimeout(() => {
          this.handleClick(item[i])
        }, 100 * i)
      }
      this.clearPY()
    },
    clearPY() {
      this.searchValue = ''
      this.activePYIndex = 0
      this.allPYList = []
      this.PYList = []
    },
    async getPYResult() {
      if (!this.searchValue) {
        this.clearPY()
        return
      }
      const res = await this.$uwonhttp.get('/GooglePinYin/Keyboard', {
        params: {
          Text: this.searchValue,
          Num: 20,
        },
      })
      const { Data, ErrorCode } = res.data
      if (ErrorCode === 0) {
        this.PYList = Data.slice(0, 5)
        this.activePYIndex = 0
        this.allPYList = Data
      }
    },
    handleNext(type) {
      const allPYList = JSON.parse(JSON.stringify(this.allPYList))
      if (type && (this.activePYIndex + 1) * 5 < allPYList.length) {
        this.activePYIndex += 1
        const startIndex = this.activePYIndex * 5
        this.PYList = allPYList.splice(startIndex, 5)
      }
      if (!type && this.activePYIndex > 0) {
        this.activePYIndex -= 1
        const startIndex = this.activePYIndex * 5
        this.PYList = allPYList.splice(startIndex, 5)
      }
    },
    // 中小学
    changeType() {
      this.isHighMode = !this.isHighMode
      if (this.isHighMode) {
        this.symbol = keybordInfo.middleSymbol
        this.navList = keybordInfo.middleNavList
      } else {
        this.symbol = keybordInfo.smallSymbol
        this.navList = keybordInfo.navList
      }
    },
    // 全选
    changeAll() {
      this.navList.forEach((e) => {
        e.active = true
      })
    },
    chengeVal(val) {
      this.money = val
      // console.log(val,':chengeVal keyboard')
      this.$refs?.mathquill?.initValue(val)
    },
    KeyWrod_True(info) {
      // if(info.key == 'sys'){
      //   this.symbol = keybordInfo.smallSymbol
      //   // this.navList = keybordInfo.navList
      // }
      // if(info.key == 'key'){
      //   this.symbol = keybordInfo.middleSymbol
      //   // this.navList = keybordInfo.middleNavList
      // }
      this.Key_Visible = true
      this.activeIndex = -1
      this.type = info.key
      if (!info.infoList) {
        setTimeout(async () => {
          // info.key = 'key'  //测试用代码
          this.$refs.mathquill.checkType(info.key)
          this.$nextTick(() => {
            this.$refs.mathquill.initValue(info.value)
          })
          // for (let i = 0; i < info.value.length; i++) {
          //   setTimeout(() => {
          //     // 代码赋值
          //     this.handleClick(info.value[i])
          //     this.activeIndex++
          //   }, i * 60)
          // }
        }, 20)
      }

      eventBus.$emit('showKey_color', this.Key_Visible)
    },
    KeyWrod_False() {
      this.Key_Visible = false
      eventBus.$emit('showKey_color', this.Key_Visible)
    },
    _chraClick(e) {
      const num = e.target.dataset.num
      this.money += num
      this.$emit('updatekey', this.money)
    },
    _handleKeyPress(e) {
      const num = e.target.dataset.num
      switch (String(num)) {
        case 'D':
          this.deletekey()
          break
        case 'enter':
          this.money += '\n'
          break
        default:
          this.Addnum(num)
          break
      }
      this.$emit('updatekey', this.money)
    },
    deletekey() {
      const values = this.money
      if (!values.length) {
        return false
      } else {
        this.money = values.substring(0, values.length - 1)
        this.$emit('updatekey', this.money)
      }
    },
    Addnum(num) {
      const value = this.money
      this.money = value + num
    },
    upClick(key) {
      switch (String(key)) {
        case '正确✓':
          this.Markkey()
          break
        case '错误×':
          this.ErrorKey()
          break
        case '、':
          this.SignKey()
          break
        case 'shift':
          this.keylist = this.bigkeys1
          this.keylist2 = this.bigkeys2
          this.keylist3 = this.bigkeys3
          break
        case 'SHIFT':
          this.keylist = this.smallkeys1
          this.keylist2 = this.smallkeys2
          this.keylist3 = this.smallkeys3
          break
        default:
          this.money += key
          this.$emit('updatekey', this.money)
          break
      }
    },
    upClick2(key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    upClick3(key) {
      switch (String(key)) {
        case 'shift':
          this.keylist = this.bigkeys1
          this.keylist2 = this.bigkeys2
          this.keylist3 = this.bigkeys3
          break
        case 'SHIFT':
          this.keylist = this.smallkeys1
          this.keylist2 = this.smallkeys2
          this.keylist3 = this.smallkeys3
          break
        default:
          this.money += key
          break
      }
      this.$emit('updatekey', this.money)
    },
    upClick4(key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    upClick5(key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    _letterClick(e) {
      const num = e.target.dataset.num
      switch (String(num)) {
        case 'delete':
          this.deletekey()
          break
        case 'space':
          this.spaceKey()
          break
        case 'Mark':
          this.Markkey()
          break
        case 'Error':
          this.ErrorKey()
          break
        case 'Sign':
          this.SignKey()
          break
        case 'Frac':
          this.FracKey()
          break
        case 'enter':
          this.TargetIndexKey()
          break
      }
    },
    Markkey() {
      const key = '✓'
      this.money += key
      this.$emit('updatekey', this.money)
    },
    ErrorKey() {
      const key = '×'
      this.money += key
      this.$emit('updatekey', this.money)
    },
    SignKey() {
      const key = '、'
      this.money += key
      this.$emit('updatekey', this.money)
    },
    spaceKey() {
      this.money = ''
      this.$emit('updatekey', this.money)
    },
    TargetIndexKey() {
      this.money = 'target'
      this.$emit('updatekey', this.money)
    },
  },
}
</script>
<style lang="less" scoped>
.key_box {
  font-family: PingFangSC-Regular, PingFang SC;
  width: 1425px;
  position: fixed;
  top: 20%;
  left: 50%;
  padding: 24px 15px;
  transform: translateX(-50%);
  // height: 60vh;
  z-index: 999;
  border-radius: 4px;
  background-color: #47485c;
  color: #fff;
  font-size: 18px;

  .chineseBox {
    position: absolute;
    min-width: 572px;
    height: 80px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #68bb97;
    bottom: -80px;
    left: 50%;
    z-index: 9;
    transform: translateX(-50%);
    padding: 10px 30px;
    font-size: 18px;
    color: #333;

    .top {
      padding-bottom: 10px;
    }

    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      white-space: nowrap;
    }

    .fonts {
      display: flex;
      align-items: center;

      .font {
        cursor: pointer;
        margin-right: 40px;
        flex-shrink: 0;

        &:nth-of-type(1) {
          color: #68bb97;
        }
      }
    }

    .func {
      width: 40px;
      flex-shrink: 0;

      i {
        cursor: pointer;
        color: #68bb97;
      }

      .disabled {
        color: #999;
      }
    }
  }

  .nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20px;
    border-bottom: 1px solid #565865;

    .left {
      display: flex;

      .item {
        cursor: pointer;
        width: 110px;
        height: 35px;
        background: #91919d;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        margin-right: 22px;
      }

      .active {
        background: #ffffff;
        color: #47485c;
      }
    }
  }

  .screen {
    display: flex;
    justify-content: space-between;
    position: relative;
    color: #4d4b57;
    margin: 10px 0;
    width: 100%;
    min-height: 80px;

    .math-quill-content {
      width: 100%;
      min-height: 80px;
      background: #fffeff;
      margin-right: 16px;
      overflow-x: auto;
      ::v-deep .mathquill > .mq-root-block {
        padding-top: 24px;
        padding-bottom: 24px;
      }
    }

    ::v-deep .active {
      &::after {
        width: 2px;
        height: 1.08em;
        content: ' ';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -2px;
        background: #61bb96;
        animation: showLine 1s infinite;
      }

      @keyframes showLine {
        from {
          opacity: 0;
        }

        to {
          opacity: 1;
        }
      }
    }

    ::v-deep .box.active {
      &::after {
        left: 2px;
      }
    }

    ::v-deep .box {
      color: #333;
      border-radius: 2px;
      display: flex;
      align-items: center;
      position: relative;

      .item {
        margin-right: 3px;
        position: relative;
      }
    }

    .btn {
      width: 64px;
      background: #68bb97;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      min-height: 80px;
      height: 100%;
    }
  }
}

.symbolImg {
  width: 25px;
}

.content1 {
  display: flex;
  justify-content: space-between;

  .left {
    width: 1314px;
    height: 290px;
    overflow-y: auto;

    .keybord {
      display: flex;
    }

    .chinese {
      // position: relative;
      margin-top: 60px;
    }

    .name {
      width: 64px;
      flex-shrink: 0;
      background: #91919d;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 13px 16px 0;
      text-align: center;
    }

    .detail {
      display: flex;
      flex-wrap: wrap;

      .item {
        cursor: pointer;
        width: 117px;
        height: 56px;
        border-radius: 4px;
        border: 1px solid #9596b3;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22px;
        margin: 0 5px 13px 0;

        &:nth-of-type(10n) {
          margin-right: 0;
        }

        &:active {
          position: relative;
          top: 1px;
          left: 1px;
          background-color: #201a1a;
        }
      }

      .fs18 {
        font-size: 18px;
      }

      .fs40 {
        font-size: 40px;
      }

      .btn {
        width: 117px;
        height: 56px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #9596b3;
        margin-left: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #47485c;
        font-size: 20px;

        &:active {
          position: relative;
          top: 1px;
          left: 1px;
          background-color: #201a1a;
        }
      }

      .btn_big {
        margin-left: 0;
        width: 241px;
      }

      .pybtn {
        background: #fff;
        color: #47485c;
        font-size: 18px;
      }

      .unActive {
        background: #91919d;
        color: #fff;
      }
    }
  }

  .right {
    min-height: 290px;
    width: 64px;
    display: flex;
    flex-direction: column;

    .btn {
      cursor: pointer;
      width: 64px;
      height: 56px;
      margin-bottom: 16px;
      background: #91919d;
      border-radius: 4px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      &:nth-of-type(3) {
        flex: 1;
        background: #9596b3;
      }

      &:nth-of-type(4) {
        flex: 1;
        background: #ff5d5d;
        margin-bottom: 0;
      }

      &:active {
        position: relative;
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
}

.isBigFont {
  text-transform: uppercase;
}

.fs14 {
  font-size: 14px !important;
}

.back {
  transform: rotateY(190deg);
}

.keyWord_False {
  cursor: pointer;
  height: 100%;
  font-size: 30px;
  color: #fff;
  text-align: center;
}

::v-deep .screen .box.empty {
  background-color: rgba(200, 200, 200, 0.5);
  border-radius: 1px;
}

::v-deep .screen .box .item {
  display: inline-block;
}

.screen #screenBox {
  background-color: #fff;
  width: 1314px;
  height: 100px;
  color: #333;
  overflow: auto;
  padding: 10px 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
</style>
