<template>
  <div>
    <h2>错题扫除</h2>
    <div class="title">
      {{ paperName }}
    </div>
  </div>
</template>

<script>
export default {
  created() {
    this.paperId = this.$route.query.paperId
    this.getTestPaperName()
  },
  data() {
    return {
      // 试卷id
      paperId: '',
      // 试卷名称
      paperName: ''
    }
  },
  watch: {},
  methods: {
    // 获取试卷的名称
    getTestPaperName() {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then(res => {
        this.paperName = res.Data.Title
      })
      // this.$set(this.current, this.paperId, this.current[this.paperId] ? this.current[this.paperId] : 0)
      // this.$set(this.activeKey, this.paperId, this.activeKey[this.paperId] ? this.activeKey[this.paperId] : 0)
    }
  }
}
</script>

<style lang="less" scoped>
h2 {
  height: 45px;
  border-bottom: 2px solid #ccc;
}
.title {
  text-align: center;
  font-size: 23px;
}
</style>
