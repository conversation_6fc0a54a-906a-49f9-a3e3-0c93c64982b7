<template>
  <div>
    <div class="key_box" ref="returnTop" v-if="Key_Visible" v-drag>
      <p>
        <span>键盘</span>
        <span v-html="Frac_val"></span>
        <span @click="KeyWrod_False" class="keyWord_False">×</span>
      </p>
      <div class="keyboard">
        <div class="keyboard_key">
          <div class="keyboard_Letter">
            <div class="group1">
              <button
                v-for="(item, index) in keylist"
                :key="index"
                type="button"
                class="pad-num1"
                @click="upClick(item)"
              >
                {{ item }}
              </button>
            </div>

            <div class="group2">
              <button
                v-for="(item, index) in keylist2"
                :key="index"
                type="button"
                class="pad-num1"
                @click="upClick(item)"
              >
                {{ item }}
              </button>
            </div>

            <div class="group3">
              <button
                v-for="(item, index) in keylist3"
                :key="index"
                type="button"
                class="pad-num1"
                @click="upClick(item)"
              >
                {{ item }}
              </button>
            </div>

            <div class="group4">
              <button
                v-for="(item, index) in keylist4"
                :key="index"
                type="button"
                class="pad-num1"
                @click="upClick(item)"
              >
                {{ item }}
              </button>
            </div>

            <div class="group4">
              <button
                v-for="(item, index) in keylist5"
                :key="index"
                type="button"
                class="pad-num1"
                @click="upClick(item)"
              >
                {{ item }}
              </button>
            </div>
          </div>
          <div class="pos-right-pad-act" style="width: 10%" @click="_letterClick">
            <!-- <button type="button" class="pad-num specialkey border-right" specialkey data-num="Mark" style="width: 90%">
              正确✓
            </button>
            <button
              type="button"
              class="pad-num specialkey border-right"
              specialkey
              data-num="Error"
              style="width: 90%"
            >
              错误×
            </button>
            <button type="button" class="pad-num specialkey border-right" specialkey data-num="Sign" style="width: 90%">
              、
            </button> -->
            <button
              type="button"
              class="pad-num specialkey border-right"
              specialkey
              data-num="space"
              style="width: 90%; background: #9596b3; border: 0; height: 46%"
            >
              清空
            </button>
            <button
              type="button"
              class="pad-num specialkey border-right"
              data-num="delete"
              style="width: 90%; background: #9596b3; border: 0; height: 46%"
            >
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import drag from '@/utils/drag'
import debounce from '@/utils/debounce'
import eventBus from '@/components/eventBus/eventBus'
export default {
  data () {
    return {
      title: '键盘',
      Key_Visible: false,
      money: '',
      keylist: [],
      keylist2: [],
      keylist3: [],
      keylist4: [],
      keylist5: [],
      smallkeys1: [],
      smallkeys2: [],
      smallkeys3: [],
      selected: false,
      Frac_val: ''
    }
  },
  mounted () {},
  created () {
    this.readle()
  },
  watch: {},
  methods: {
    chengeVal (val) {
      this.money = val
    },
    KeyWrod_True () {
      this.Key_Visible = true
      eventBus.$emit('showKey_color', this.Key_Visible)
    },
    KeyWrod_False () {
      this.Key_Visible = false
      eventBus.$emit('showKey_color', this.Key_Visible)
    },
    _chraClick (e) {
      const num = e.target.dataset.num
      this.money += num
      this.$emit('updatekey', this.money)
    },
    _handleKeyPress (e) {
      const num = e.target.dataset.num
      switch (String(num)) {
        case 'D':
          this.deletekey()
          break
        case 'enter':
          this.money += '\n'
          break
        default:
          this.Addnum(num)
          break
      }
      this.$emit('updatekey', this.money)
    },
    deletekey () {
      const values = this.money
      if (!values.length) {
        return false
      } else {
        this.money = values.substring(0, values.length - 1)
        this.$emit('updatekey', this.money)
      }
    },
    Addnum (num) {
      const value = this.money
      this.money = value + num
    },
    upClick (key) {
      console.log(key == String(key))
      switch (String(key)) {
        case '正确✓':
          this.Markkey()
          break
        case '错误×':
          this.ErrorKey()
          break
        case '、':
          this.SignKey()
          break
        case 'shift':
          this.keylist = this.bigkeys1
          this.keylist2 = this.bigkeys2
          this.keylist3 = this.bigkeys3
          break
        case 'SHIFT':
          this.keylist = this.smallkeys1
          this.keylist2 = this.smallkeys2
          this.keylist3 = this.smallkeys3
          break
        default:
          this.money += key
          this.$emit('updatekey', this.money)
          break
      }
    },
    upClick2 (key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    upClick3 (key) {
      switch (String(key)) {
        case 'shift':
          this.keylist = this.bigkeys1
          this.keylist2 = this.bigkeys2
          this.keylist3 = this.bigkeys3
          break
        case 'SHIFT':
          this.keylist = this.smallkeys1
          this.keylist2 = this.smallkeys2
          this.keylist3 = this.smallkeys3
          break
        default:
          this.money += key
          break
      }
      this.$emit('updatekey', this.money)
    },
    upClick4 (key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    upClick5 (key) {
      this.money += key
      this.$emit('updatekey', this.money)
    },
    _letterClick (e) {
      const num = e.target.dataset.num
      switch (String(num)) {
        case 'delete':
          this.deletekey()
          break
        case 'space':
          this.spaceKey()
          break
        case 'Mark':
          this.Markkey()
          break
        case 'Error':
          this.ErrorKey()
          break
        case 'Sign':
          this.SignKey()
          break
        case 'Frac':
          this.FracKey()
          break
        case 'enter':
          this.TargetIndexKey()
          break
      }
    },
    Markkey () {
      const key = '✓'
      this.money += key
      this.$emit('updatekey', this.money)
    },
    ErrorKey () {
      const key = '×'
      this.money += key
      this.$emit('updatekey', this.money)
    },
    SignKey () {
      const key = '、'
      this.money += key
      this.$emit('updatekey', this.money)
    },
    spaceKey () {
      this.money = ''
      this.$emit('updatekey', this.money)
    },
    TargetIndexKey () {
      this.money = 'target'
      this.$emit('updatekey', this.money)
    },
    readle () {
      const smallkey1 = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '正确✓']
      const smallkey2 = ['+', '-', '×', '÷', '=', '≠', '≈', '≤', '≥', '/', '错误×']
      const smallkey3 = ['(', ')', '[', ']', '{', '}', '<', '>', '.', '……', '、']
      const smallkey4 = ['mm', 'cm', 'dm', 'm', 'km', 'mm²', 'cm²', 'dm²', '㎡', 'km²', '⊥']
      const smallkey5 = ['mm³', 'dm³', 'cm³', 'm³', 'mg', 'g', 'kg', 't', 'ml', 'l', '//']
      this.keylist = smallkey1
      this.keylist2 = smallkey2
      this.keylist3 = smallkey3
      this.keylist4 = smallkey4
      this.keylist5 = smallkey5
    }
  }
}
</script>
<style lang="less" scoped>
.key_box {
  width: 70%;
  margin: 15px;
  height: 400px;
  position: fixed;
  bottom: 5%;
  left: 15%;
  z-index: 100;
  p {
    height: 40px;
    line-height: 40px;
    padding: 0 2%;
    font-size: 21px;
    background-color: #47485c;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span:last-child {
      font-size: 30px;
    }
  }
}
.keyboard {
  width: 100%;
  height: 320px;
  background: #000;
  overflow: hidden;
  background-color: #47485c;
}
.keyboard_key {
  width: 100%;
  padding: 5px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}
// ----数字键盘-----
.keyboard_number {
  display: flex;
  width: 15%;
  margin: 0;
  padding: 0;
  vertical-align: bottom;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}
.pos-right-pad-num {
  display: flex;
  width: 75%;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}
.pad-num {
  width: 10%;
  height: 16.8%;
  margin: 5px;
  border-radius: 5px;
  background-color: transparent;
  border: 1px solid #cccccc;
  font-size: 20px;
  color: #ffffff;
  cursor: pointer;
  &:hover {
    position: relative;
    cursor: pointer;
  }
  &:active {
    top: 1px;
    left: 1px;
    background-color: #201a1a;
  }
}
.pad-num2 {
  width: 100%;
  height: 180px;
  margin: 5px;
  border-radius: 5px;
  background-color: transparent;
  border: 1px solid #cccccc;
  font-size: 20px;
  color: #ffffff;
  font-size: 15px;
  cursor: pointer;
  &:hover {
    position: relative;
    cursor: pointer;
  }
  &:active {
    top: 1px;
    left: 1px;
    background-color: #201a1a;
  }
}
//字母键盘
.keyboard_Letter {
  width: 100%;
  padding: 0;
  .group1 {
    display: flex;
    flex-direction: row;
    .pad-num1 {
      width: 10%;
      height: 52px;
      margin: 5px;
      border-radius: 5px;
      background-color: transparent;
      border: 1px solid #cccccc;
      font-size: 20px;
      color: #ffffff;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
  .group2 {
    display: flex;
    flex-direction: row;
    width: 100%;
    .pad-num1 {
      width: 10%;
      height: 52px;
      margin: 5px;
      border-radius: 5px;
      background-color: transparent;
      border: 1px solid #cccccc;
      font-size: 20px;
      color: #ffffff;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
  .group3 {
    display: flex;
    flex-direction: row;
    .pad-num1 {
      width: 10%;
      height: 52px;
      margin: 5px;
      border-radius: 5px;
      background-color: transparent;
      border: 1px solid #cccccc;
      font-size: 20px;
      color: #ffffff;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
  .group4 {
    display: flex;
    flex-direction: row;
    .pad-num1 {
      width: 10%;
      height: 52px;
      margin: 5px;
      border-radius: 5px;
      background-color: transparent;
      border: 1px solid #cccccc;
      font-size: 20px;
      color: #ffffff;
      cursor: pointer;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
    .pad-num2 {
      width: 10%;
      height: 68px;
      margin: 5px;
      border-radius: 5px;
      background-color: transparent;
      border: 1px solid #cccccc;
      font-size: 20px;
      color: #ffffff;
      &:hover {
        position: relative;
        cursor: pointer;
      }
      &:active {
        top: 1px;
        left: 1px;
        background-color: #201a1a;
      }
    }
  }
}
/deep/.el-dialog__header {
  background: #47485c;
  color: #fff;
}
/deep/.el-dialog__body {
  background: #47485c;
  padding: 0px;
}
/deep/ .el-dialog__title {
  color: #fff;
}
/deep/ .el-dialog__headerbtn .el-dialog__close {
  font-size: 20px;
  color: #fff;
}
.keyWord_False {
  cursor: pointer;
  height:100%;
  width:70px;
  text-align: center;
}
</style>
