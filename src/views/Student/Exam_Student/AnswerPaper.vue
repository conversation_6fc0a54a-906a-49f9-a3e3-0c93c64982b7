<!--
 * @Author: 周恩波 <EMAIL>
 * @Date: 2024-09-03 11:45:54
 * @LastEditors: 周恩波
 * @LastEditTime: 2025-01-13 14:48:44
 * @Description:
-->
<template>
  <div class="DIV_BOTTOM_DIV">
    <div class="paperheader pc_font_size20 font_size22 ipad_font_size24">
      <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
      <div style="display: flex; align-items: center">
        <span v-html="PaperName" v-katex></span>
        <div
          class="xuekeStyle"
          :style="{
            'background-color': bgColog[Multidisciplinary.SubjectId + 'bg'],
            color: bgColog[Multidisciplinary.SubjectId],
          }"
        >
          {{ Multidisciplinary.name }}
        </div>
      </div>
      <span>
        <span class="tiltcount">
          <span style="color: #61bb96">{{ CurrentNum }}</span
          >/ <span>{{ PaperNum }}</span> 题</span
        >
        <span></span><el-button @click="submit" type="primary">提交</el-button></span
      >
    </div>
    <div class="Answer" id="AnswerPaper">
      <div class="Answer_left">
        <div class="swiper_list">
          <div class="tea-analusis">
            <div class="line_border"></div>
            <swiper class="swiper" ref="mySwiper" :options="swiperOption">
              <swiper-slide style="text-align: center" v-for="(item, index) in PaperList" :key="index">
                <span
                  @click="targetIndex(index, item)"
                  :class="{
                    'title-btn-whole': true,
                    'has-answer': item.HasAnswer,
                    'current-answer': item.CurrentAnswer,
                  }"
                  >{{ index + 1 }}</span
                >
              </swiper-slide>
            </swiper>
            <div class="left-arrow">
              <div class="arrow-img" @click="prevPhoto">
                <i></i>
              </div>
            </div>
            <div class="right-arrow">
              <div class="arrow-img" @click="nextPhoto">
                <i></i>
              </div>
            </div>
          </div>
          <div class="imgbox">
            <img src="@/assets/student/闹钟.png" alt="" />
            <!--            <span class="pc_font_size18 font_size20 ipad_font_size22"> 总计{{ CallinTime }}</span>-->
            <span class="pc_font_size18 font_size20 ipad_font_size22" ref="CallinTime"></span>
          </div>
        </div>
        <div class="left_info flex" id="hello" v-katex>
          <!-- 组合题题干 -->
          <div
            class="zuheti padding_t_20 padding_r_10 formatImg pc_font_size18 font_size28 ipad_font_size35"
            style="width: 30%; height: 60vh; overflow-y: scroll"
            v-if="zuheti.length > 0 && zuhetiTitle"
          >
            <p v-html="zuhetiTitle" v-katex style="max-width: 600px; min-width: 300px"></p>
          </div>
          <div class="paper_item flex_item_shrink" v-for="(item, index) in PaperList" :key="index">
            <div class="paper_type pc_font_size22 font_size24 ipad_font_size26" v-show="num === index">
              <div class="type_after"></div>
              <span v-if="item.TypeId == 2">单项选择题</span>
              <span v-if="item.TypeId == 5 || item.TypeId == 42">填空题</span>
              <span v-if="item.TypeId == 10">多项选择题</span>
              <span v-if="item.TypeId == 11">判断题</span>
              <span v-if="item.TypeId == 23">应用题</span>
              <span v-if="item.TypeId == 36">主观题</span>
              <span v-if="item.TypeId == 39">题型</span>
              <span v-if="item.TypeId == 40">可变行列表</span>
              <span v-if="item.TypeId == 41">可变行填空</span>
              <span v-if="item.TypeId == 43">可增加填空</span>
              <span v-if="item.TypeId == 44">可选择填空</span>
              <span v-if="item.TypeId == 45">可为空填空</span>
              <span v-if="item.TypeId == 46">可下拉选择题</span>
              <span v-if="item.TypeId == 47">思考问答题</span>
              <span v-if="item.TypeId == 50">连线题</span>
              <span v-if="item.TypeId == 51">表格题</span>
              <span v-if="item.TypeId == 52">选词填空题</span>
              <span v-if="item.TypeId == 70">多项单选题</span>
              <span
                class="IMGkey_word"
                v-if="
                  item.TypeId === '5' ||
                  item.TypeId === '40' ||
                  item.TypeId === '47' ||
                  item.TypeId === '42' ||
                  item.TypeId === '41' ||
                  item.TypeId === '51'
                "
              >
                <span @click="Review_Key" style="display: flex" v-if="!isMobile">
                  <b :class="showKey_color == false ? 'backcolor_gray' : 'backcolor_blue'" id="Imkey_word"
                    ><img src="@/assets/Key_word.png" alt=""
                  /></b>
                  <b>使用教学键盘</b>
                </span>
                <span id="Imkey_word" style="display: flex" v-if="isMobile">
                  <b class="backcolor_blue"><img src="@/assets/Key_word.png" alt="" /></b>
                  <b @click="Review_Key_pad('key')" :class="boardType == 'sys' ? 'check_active' : ''"> 使用教学键盘 </b>
                  <b @click="Review_Key_pad('sys')" :class="boardType == 'key' ? 'check_active' : ''">使用系统键盘</b>
                </span>
              </span>
            </div>

            <div class="title_item" v-show="num === index">
              <div class="Level_box">
                <span style="vertical-align: sub" class="pc_font_size20 font_size24 ipad_font_size26"
                  >第{{ index + 1 }}题</span
                >
                <el-button
                  class="pc_font_size16 font_size20 ipad_font_size26"
                  @click="next(index, item)"
                  v-show="nextDisabled"
                  >下一题</el-button
                >
                <!--
                :disabled="isDisabled" -->
                <el-button :disabled="isDisabled" :style="{background:item.UserAnswer?.length >0 ? '#EBEEF5' :'#61bb96'}" class="pc_font_size16 font_size20 ipad_font_size26" @click="submitItemHandel(item,index)">提交该题目</el-button>
                <el-button class="pc_font_size16 font_size20 ipad_font_size26" @click="submit" v-show="SubmitBtn"
                  >提交</el-button
                >
                <a
                  class="pc_font_size18 font_size22 ipad_font_size24"
                  v-show="item.Tips != null && item.Tips != ''"
                  type="text"
                  style="margin-left: 30px"
                  @click="exerciseGuidance(item)"
                  >查看练习指导</a
                >
              </div>
              <span
                v-if="item.TypeId !== '70'"
                v-html="item.Title"
                :id="'Imgid' + index"
                class="formatImg pc_font_size18 font_size28 ipad_font_size35"
                @input="handleInput"
                ref="getFocus"
                @click="onkeyup"
                v-mathquill="{ list: item.AnswerList, showKey_color: showKey_color, input: handleInputInterceptor }"
                v-katex
              ></span>
              <!-- 多项单选题 -->
              <span
                v-if="item.TypeId === '70'"
                v-html="item.Title"
                :id="'Imgid' + index"
                ref="getFocus"
                @click="onSelectClickBtn"
                class="formatImg pc_font_size18 font_size28 ipad_font_size35"
                v-katex
              ></span>
            </div>
            <div class="Topic_type" v-if="num === index">
              <!-- 多项单选仅显示选择项状态时展示答案区-->
              <ul>
                <li
                  class="pc_font_size16 font_size24 ipad_font_size30"
                  v-if="arrValue.length > 0"
                  v-for="(item, index) in arrValue"
                  :key="index"
                  v-html="item"
                  v-katex
                  style="margin: 2px 0"
                ></li>
              </ul>
              <!-- 主观题-拍照上传 -->
              <!-- && isMobile -->
<!--              <SubjectiveStemPhoto-->
<!--                v-if="item.TypeId == 36 && item.ReviewContentType == 1 && isMobile"-->
<!--                ref="SubjectiveStemPhoto"-->
<!--                :ItemId="item.ItemId"-->
<!--                :Answer="item.Answer"-->
<!--                :SubType="0"-->
<!--                @handlePhoto="getSubjectiveData"-->
<!--              ></SubjectiveStemPhoto>-->
              <SubjectiveStemPhoto
                v-if="item.TypeId == 36 && item.ReviewContentType == 1"
                ref="SubjectiveStemPhoto"
                :ItemId="item.ItemId"
                :Answer="item.Answer"
                :UserAnswer="item.UserAnswer"
                :SubType="0"
                @handlePhoto="getSubjectiveData"
              ></SubjectiveStemPhoto>
<!--              <p-->
<!--                v-if="item.TypeId == 36 && !isMobile"-->
<!--                class="pc_font_size16 font_size20 ipad_font_size22"-->
<!--                style="margin: 30px 0; color: orange"-->
<!--              >-->
<!--                此题为拍摄上传类主观题，请使用可拍摄的设备答题！-->
<!--              </p>-->
              <!-- 主观题-拍摄视频 -->
<!--              <SubjectiveStemVideo-->
<!--                ref="SubjectiveStemVideo"-->
<!--                v-if="item.TypeId == 36 && item.ReviewContentType == 3 && isMobile"-->
<!--                :ItemId="item.ItemId"-->
<!--                :Answer="item.Answer"-->
<!--                :SubType="0"-->
<!--                @handleVideo="getSubjectiveData"-->
<!--              ></SubjectiveStemVideo>-->
              <SubjectiveStemVideo
                ref="SubjectiveStemVideo"
                v-if="item.TypeId == 36 && item.ReviewContentType == 3"
                :ItemId="item.ItemId"
                :Answer="item.Answer"
                :UserAnswer="item.UserAnswer"
                :SubType="0"
                @handleVideo="getSubjectiveData"
              ></SubjectiveStemVideo>
              <!-- 主观题-录制语音 -->
              <!-- && isMobile -->
<!--              <SubjectiveStemAudio-->
<!--                ref="SubjectiveStemAudio"-->
<!--                v-if="item.TypeId == 36 && item.ReviewContentType == 2 && isMobile"-->
<!--                :ItemId="item.ItemId"-->
<!--                :Answer="item.Answer"-->
<!--                :SubType="0"-->
<!--                @handleAudio="getSubjectiveData"-->
<!--              ></SubjectiveStemAudio>-->
              <SubjectiveStemAudio
                ref="SubjectiveStemAudio"
                v-if="item.TypeId == 36 && item.ReviewContentType == 2"
                :isMobile="isMobile"
                :ItemId="item.ItemId"
                :Answer="item.url"
                :UserAnswer="item.UserAnswer"
                :SubType="0"
                @handleAudio="getSubjectiveData"
              ></SubjectiveStemAudio>
              <!-- 表格题 -->
              <RenderTable
                v-if="item.TypeId == 51"
                ref="RenderTable"
                :TableItemInfo="item.ItemChange.TableItemInfo"
                :isUserAnswer="item.isUserAnswer"
                @onfocus="onfocus"
                @updatefinishStatus="item.HasAnswer = $event"
              ></RenderTable>
              <!-- 连线题 -->
              <matching
                ref="matching"
                v-if="item.TypeId == 50"
                :Answer="item.Answer"
                :trueAnswer="item.AuditPaperItemAnswers.Content"
                :linkExamData="item.ItemChange.LinkExamData"
                :SubType="isDisabled ? 2 :0"
                @handleLIne="getSubjectiveData"
              ></matching>
              <!-- 单选题 -->
              <div class="radio_text" id="Psdi">
                  <!-- v-model="item.Answer" -->
                <a-radio-group
                  v-model="item.Answer"
                  v-if="item.TypeId === '2' || item.TypeId === '11'"
                  @change="RadioCheck(item)"
                >
                  <a-radio
                    :value="i.Option"
                    v-for="(i, indx) in item.AuditPaperItemAnswers"
                    :disabled="isDisabled"
                    :key="indx"
                    v-show="i.Type === 1"
                  >
                    <div style="display: flex;">
                      <span class="pc_font_size16 font_size24 ipad_font_size30" v-if="i.Type === 1"
                      >{{ i.Option }}{{ PaperFormat.period() }}</span
                      >
                      <p
                        class="pc_font_size16 font_size24 ipad_font_size30"
                        v-if="i.Type === 1"
                        v-html="i.Content"
                        v-katex
                      ></p>
                    </div>
                  </a-radio>
                </a-radio-group>
              </div>

              <!-- 多选题 -->
              <a-checkbox-group v-model="SelectionList" class="Multiple" @change="Checkbox" v-if="item.TypeId === '10'">
                <a-checkbox
                  v-for="(i, ind) in item.AuditPaperItemAnswers"
                  :key="ind"
                  :value="i.Option"
                  :disabled="isDisabled"
                  v-show="i.Type === 1"
                >
                  <span class="pc_font_size16 font_size24 ipad_font_size30"
                    >{{ i.Option }}{{ PaperFormat.period() }}</span
                  >
                  <p
                    v-html="i.Content"
                    class="pc_font_size16 font_size24 ipad_font_size30"
                    style="margin-left: 5px; display: inline-block"
                    v-katex
                  ></p>
                </a-checkbox>
              </a-checkbox-group>

              <!-- 新题型 1--可变行下拉填空题  -->
              <div v-if="item.TypeId == 46">
                <div class="select_box" v-for="(sel, index) in SplitSelectArr" :key="index">
                  <span v-for="(ItemSel, ind) in sel" :key="ind">
                    <span v-html="ItemSel" @change="selectSku" v-katex></span>
                  </span>
                </div>
                <div class="Table_Btn">
                  <span
                    style="font-size: 18px"
                    v-if="SplitSelectArr.length < item.ItemChange.MaxRow && !isDisabled"
                    @click="Select_AddRow"
                    ><i class="el-icon-circle-plus-outline"></i>增加一行</span
                  >
                  <span
                    v-if="SplitSelectArr.length > 1 && !isDisabled"
                    style="font-size: 18px; margin-left: 20px"
                    @click="Select_ClearRow"
                    ><i class="el-icon-remove-outline"></i>减少一行</span
                  >
                </div>
              </div>

              <!-- 新题型 2--可变行表格  -->
              <div v-if="item.TypeId == 40">
                <!-- <table class="tableTitle" style="width: 70%">
                  <tr v-for="(item, index) in tableList.Titles" :key="index">
                    <td>{{ item }}</td>
                  </tr>
                  <tbody>
                    <tr v-for="(td, ind) in tableList.Rows" :key="ind">
                      <td v-for="(td, indx) in td" :key="indx">
                        <span v-html="td" ref="getFocus" id="hello_input" @input="handleInput" @click="onkeyup"></span>
                      </td>
                    </tr>
                  </tbody>
                </table> -->
                <table class="tableTitle" style="width: 70%">
                  <tr>
                    <td v-for="(item, index) in tableList.Titles" :key="index">{{ item }}</td>
                  </tr>
                  <tr v-for="(td, ind) in tableList.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span
                        v-html="td"
                        ref="getFocus"
                        id="hello_input"
                        @input="handleInput"
                        @click="onkeyup"
                        v-katex
                      ></span>
                    </td>
                  </tr>
                </table>

                <div class="Table_Btn">
                  <span
                    v-if="tableList.Rows.length < item.ItemChange.MaxRow"
                    style="font-size: 18px"
                    @click="Table_AddRow(item, item.ItemChange.IsSNum, tableList.Rows)"
                    ><i class="el-icon-circle-plus-outline"></i>增加一行</span
                  >
                  <span
                    v-if="tableList.Rows.length > 1"
                    style="font-size: 18px; margin-left: 20px"
                    @click="Table_ClearRow(item.ItemChange.IsSNum)"
                    ><i class="el-icon-remove-outline"></i>减少一行</span
                  >
                </div>
              </div>
              <!-- 新题型 3--可变行填空  -->
              <div v-if="item.TypeId == 41">
                <div class="Blank_box" v-for="(blank, indx) in selfList" :key="indx">
                  <span class="blank_Answer">答案{{ indx + 1 }}</span>
                  <span v-for="(i, ind) in blank" :key="ind">
                    <p v-html="i" @input="(event) => BlankInput(event, indx, ind)" @click="onkeyup" v-katex></p>
                  </span>
                </div>
                <div class="Table_Btn">
                  <span
                    v-if="selfList.length < item.ItemChange.MaxRow"
                    style="font-size: 18px"
                    @click="Blank__AddRow(selfList)"
                    ><i class="el-icon-circle-plus-outline"></i>增加一行</span
                  >
                  <span
                    v-if="selfList.length > item.ItemChange.Rows.length"
                    style="font-size: 18px; margin-left: 20px"
                    @click="Blank_ClearRow"
                    ><i class="el-icon-remove-outline"></i>减少一行</span
                  >
                </div>
              </div>

              <!-- 新题型 4--思考问答  -->
              <div v-if="item.TypeId == 47">
                <div class="show_btn">
                  <el-button @click="ShowRadio">填写后点击思考</el-button>
                  <div class="radio_box" v-if="show_radiobox">
                    <span ref="getFocus" @input="handleInput" @click="onkeyup">{{ QAList.Title }}</span>
                    <br />
                    <a-radio-group v-model="splcar_radio" @change="onRadioChange">
                      <a-radio :value="i.Title" v-for="(i, indx) in QAList.ItemConfigOptions" :key="indx">
                        <span style="font-size: 19px">{{ i.Title }}:&nbsp;&nbsp;</span>
                        <span v-html="i.Name" style="font-size: 18px; display: inline-block" v-katex></span>
                      </a-radio>
                    </a-radio-group>
                    <br />
                    <span
                      v-show="options_Context"
                      id="radio_box_input"
                      ref="get_input"
                      v-html="QAList.ItemConfigOptions[0].Context"
                      v-katex
                      @input="handleInput"
                      @click="onkeyup"
                    ></span>
                  </div>
                </div>
              </div>

              <div v-if="item.TypeId == 42">
                <table class="pure-table pure-table-bordered">
                  <thead>
                    <tr>
                      <th v-for="(item, index) in item.ItemChange.Titles" :key="index">{{ item }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                      <td v-for="(td, indx) in td" :key="indx">
                        <span
                          v-html="td"
                          ref="getFocus"
                          id="hello_input"
                          @input="handleInput"
                          @click="onkeyup"
                          v-katex
                        ></span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!--     核心素养的拍照参数：IsHaveSpecial:是否为核心素养题，IsUpload: 0:不上传，1:必传，2:非必传/暂时添加解题思路上传需求字段：IsSolutions         -->
              <div class="hexinsuyangPaizhao" v-if="item.IsHaveSpecial && isMobile !== null && item.IsUpload != 0 && item.TypeId != 36 || item.IsSolutions && isMobile !== null">
                <p v-if="!item.IsSolutions">拍照上传解题思路（<span :style="{'color':item.IsUpload == 1 ? '#F56C6C' : '#909399'}">{{ item.IsUpload == 1?'必传':'非必传' }}</span>）</p>
                <p v-if="item.IsSolutions">上传解题思路</p>
                <!-- 主观题-拍照上传 -->
                <SubjectiveStemPhoto
                  v-if="item.IsUpload == 1 || item.IsUpload == 2 || item.IsSolutions"
                  ref="SubjectiveStemPhoto"
                  :ItemId="item.ItemId"
                  :Answer="item.url"
                  :SubType="0"
                  @handlePhoto="getSubjectiveData"
                ></SubjectiveStemPhoto>
              </div>
              <p
                v-if="item.IsHaveSpecial && item.IsUpload != 0 && isMobile === null || item.IsSolutions && isMobile === null"
                class="pc_font_size16 font_size20 ipad_font_size22"
                style="margin: 30px 0; color: orange"
              >
                此题为拍摄上传类，请使用可拍摄的设备答题！
              </p>
            </div>
            <div v-if="isDisabled && num === index && !['36'].includes(item.TypeId)">
              <span>我的作答：</span>
              <span v-html="item.UserAnswer" v-katex></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-image v-show="Show_Img" ref="Image" :src="dialogImageUrl" :preview-src-list="[dialogImageUrl]"></el-image>
    <keyboard ref="keyword" @updatekey="GetKeyVal" :content="content"></keyboard>
    <!--    作答提示弹窗-->
    <el-dialog title="练习指导" :visible.sync="CloseGuidanceVisible" width="30%" :before-close="CloseGuidance">
      <span v-html="Guidance" v-katex></span>
    </el-dialog>
  </div>
</template>
<script>
import keyboard from './keyboard'
import ExamFormatCommon from '@/common/ExamFormatCommon'
import katex from 'katex'
import eventBus from '@/components/eventBus/eventBus'
import { colorList } from '../../../components/SettingDrawer/settingConfig'
import Common from '../common/common'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { arrlistTitle, onSelectClick } from '@/utils/bankedCloze/bankedCloze'
import SubjectiveStemPhoto from './components/subjectiveExam/answerPhoto.vue'
import SubjectiveStemVideo from './components/subjectiveExam/answerVideo.vue'
import SubjectiveStemAudio from './components/subjectiveExam/answerAudio.vue'
import matching from '@/components/matching/index'
import paperMixins from '@/mixins/paperMixins'
import answerPaperMixin from './answerPaper.mixin'
import RenderTable from './components/tableStem/renderTable.vue'
// import BankedClozeModule from './components/bankedCloze/bankedClozeModule'
export default {
  mixins: [paperMixins,answerPaperMixin],
  components: {
    keyboard,
    SubjectiveStemPhoto,
    SubjectiveStemVideo,
    SubjectiveStemAudio,
    matching,
    RenderTable,
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.PaperList = []
    this.Init()
    this.GetAuditPaperItemsList()
    this.GetTheData()
    this.submit_lock = false
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
  },
  mounted() {
    this.timeRecord(true)
    this.setTimmer()
    eventBus.$on('showKey_color', (showKey_color) => {
      this.showKey_color = showKey_color
      if (!showKey_color) this.closeToChangeInput()
    })
  },
  data() {
    return {
      row: null,
      column: null,
      input_val: '',
      userId: '',
      content: '',
      paperId: '',
      PaperList: [], // 试卷信息
      PaperName: '', // 试卷名称
      CallinTime: '', // 计时时间
      timer: null,
      timmer: null, // 初始化计时器
      currentTime: 0, // 当前题目的时间
      PaperNum: '', // 试题总数
      CurrentNum: 1, // 当前题目数
      num: 0, // 当前题目
      nextDisabled: true, // 下禁用按钮
      SubmitBtn: false,
      AnswerList: [], // 答案
      SelectionList: [], // 多选题答案,
      tableList: [], // 表格--可变行 Array
      TableRegx: [], // 表格--可变行 替换 Array
      AddRow: [], // 表格 -- 新增一行 Array
      BlanksList: [], // 特殊题型三 ---填空
      QAList: [], // 特殊题型四 ---思考问答
      splcar_radio: '', // 特殊题型四 ---radio
      show_radiobox: false, // 特殊题型四 --是否展示radio
      options_Context: false, // 特殊题型四 --是否展示第三项
      RowsList: [], // typid == 42 列表
      selfList: [],
      asList: [],
      SelectList: [],
      SplitSelectArr: [],
      SelectNum: 0,
      TableInputs: 0,
      Getvideo: '',
      currentInput: '',
      showKey_color: '',
      keyboardAuto: true, // 教学键盘自动弹出
      dialogVisible: false,
      dialogImageUrl: '',
      Show_Img: false,
      isMobile: null,
      boardType: '', // key-数字键盘  sys-系统键盘
      blinkTimer: null,
      debounceTimer: null,
      submit_lock: false,
      isPcKey: 0,
      swiperOption: {
        slidesPerView: 10, // 一行显示4个
        spaceBetween: 10, // 间隔30
        freeMode: true,
        speed: 1000, // 滑动速度
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true,
          disabledClass: 'my-button-disabled', // 前进后退按钮不可用时的类名。
          hiddenClass: 'my-button-hidden', // 按钮隐藏时的Class
        },
      },
      styleStr: `
        margin:5px 10px;
        height: 27px;
        border-bottom: 1px solid gray;
        border-radius: 4px;
        background: rgba(137, 225, 186, 0.4);
        height: 27px;
        line-height: 27px;
        font-size: 18px;
        color: #000;
        overflow: hidden;
        position: relative;
        text-align: left;
        top:8px;
        padding-top: 10px
      `,
      //  多学科部分
      bgColog: {},
      Multidisciplinary: {},
      // 选词填空答案
      AnswerArr: [],
      // 作答提示
      CloseGuidanceVisible: false,
      Guidance: null,
      // 多项单选
      arrValue: [],
      solveProblemsKernelImgList: [] ,// 解题思路/核心素养数据暂存
      zuheti: [],
      zuhetiTitle: '',
    }
  },
  watch: {
    $route() {
      window.clearInterval(this.timer)
      const ChangId = window.location.href.split('?')[1]
      this.PaperList = []
      this.paperId = this.$route.query.paperId
      this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerPaper?' + ChangId) {
        this.$router.go(0)
        this.Init()
        this.GetTheData()
        this.GetAuditPaperItemsList()
        this.timeRecord(false)
        this.submit_lock = false
      }
    },
    num: {
      handler(now) {
        if (now == this.PaperList.length - 1) {
          this.nextDisabled = false
          this.SubmitBtn = true
        } else {
          this.nextDisabled = true
          this.SubmitBtn = false
        }
      },
      deep: true,
    },
  },
  computed: {
    swiper() {
      return this.$refs.mySwiper.$swiper
    },
    itemType() {
      return this.PaperList[this.num].TypeId
    },
    isDisabled(){
      return this.PaperList[this.num].UserAnswer?.length > 0
    }
  },
  methods: {
    onfocus({ row, column }) {
      this.row = row
      this.column = column
    },
    // 多项单选的点击
    onSelectClickBtn(e) {
      const that = this
      onSelectClick(e, that)
    },
    // 关闭作答提示
    CloseGuidance() {
      this.Guidance = null
      this.CloseGuidanceVisible = false
    },
    Init() {
      const domain = document.domain
      this.keyboardAuto = !(domain == 'st.eduwon.cn')
      this.boardType = this.keyboardAuto ? 'key' : 'sys'
      this._isMobile()
    },
    // 从主观题组件获取数据
    getSubjectiveData (data) {
      // console.log(data)
      // console.log(this.PaperList[this.CurrentNum - 1])
      // return
      if (this.PaperList[this.CurrentNum - 1].TypeId == 50) {
        const d = JSON.parse(data)
        this.PaperList[this.CurrentNum - 1].HasAnswer = !!d.length
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = !!data
      }
      if (this.PaperList[this.CurrentNum - 1].TypeId === '36') {
        this.PaperList[this.CurrentNum - 1].url = data
      } else {
        if (
          this.PaperList[this.CurrentNum - 1].IsUpload === 1 ||
          this.PaperList[this.CurrentNum - 1].IsUpload === 2 ||
          this.PaperList[this.CurrentNum - 1].IsSolutions
        ) {
          this.PaperList[this.CurrentNum - 1].url = data
        }
      }
      this.PaperList[this.CurrentNum - 1].Answer = data
    },
    // 测试使用
    tabInit() {
      const naver = $(`#Imgid${this.num}`).find('button')
      for (let i = 0; i < naver.length; i++) {
        naver[i].style.background = 'red'
        naver[i].style.color = '#000000'
      }
    },
    // 模拟光标闪烁
    addBlink() {
      // 使用内置光标闪烁
      // if (this.debounceTimer) {
      //   clearTimeout(this.debounceTimer)
      //   clearInterval(this.blinkTimer)
      // }
      // this.debounceTimer = setTimeout(() => {
      //   this.blinkTimer = setInterval(() => {
      //     const span = document.getElementById('blink')
      //     if (!span) {
      //       const span = document.createElement('b')
      //       span.setAttribute('id', 'blink')
      //       span.innerHTML = '|'
      //       if (this.currentInput) this.currentInput.appendChild(span)
      //     } else {
      //       span.remove()
      //     }
      //   }, 600)
      // }, 100)
    },

    // 获取试卷的名称
    async GetTheData() {
      const res = await this.$http.post('/Paper/Exam_Paper/GetTheData', {
        id: this.paperId,
      })
      this.PaperName = res.Data.Title
    },

    GoBack() {
      this.$confirm('是否退出答题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$router.push('/Student/TaskCenter')
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退出',
          })
        })
    },
    // 分数处理
    // 获取试卷信息
    async GetAuditPaperItemsList() {
      const res = await this.$http.get('/Paper/Exam_Item/GetAuditPaperItemsList', {
        params: {
          paperId: this.paperId,
        },
      })
      if (res.Success == true) {
        const arrItem = res.Data
        if (arrItem[0].TypeId == 70) {
          if (arrItem[0].ItemChange.MultitermSingle.Options[0].IsContentShow === 2) {
            this.arrValue = arrItem[0].ItemChange.MultitermSingle.Options[0].Content.split('|')
          } else {
            this.arrValue = []
          }
        }
        res.Data.forEach((item, index) => {
          // 选词填空题型
          if (
            (item.TypeId == 52 && item.ItemChange.SelectFillBlank !== null) ||
            (item.TypeId == 70 && item.ItemChange.MultitermSingle !== null)
          ) {
            item.Title = arrlistTitle('初始化', item, item.TypeId)
          }
          // 填空题处理
          if (item.TypeId == 5) {
            item.Title = ExamFormatCommon.format_fraction(item.Title)
            // item.Title = ExamFormatCommon.format_input(item.Title, item.TypeId, item)
            item.Title = ExamFormatCommon.SingleFormatInput(item.Title, item.TypeId, item)

          }
          item.Title = ExamFormatCommon.format_space(item.Title)
          // 表格变行处理
          if (item.TypeId == 40) {
            this.tableList = item.ItemChange
            const regx = /\(\#@\)/g
            const input = ''
            this.tableList.Rows.forEach((td, idx, ary) => {
              td.forEach((i, index, array) => {
                const TableRegx = i.match(regx)
                if (TableRegx !== null) {
                  TableRegx.forEach((item1) => {
                    const value = item1.replace(regx, '')
                    const title = i.replace(
                      item1,
                      `<input class="input" autocomplete="off"  type="text" style="width: 100px;border: 0px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                      id="${idx + '.' + index}"  data-type="${item.TypeId}" data-index="${index}"/>`
                    )
                    array[index] = title
                  })
                }
              })
            })
          }
          // 填空变行处理
          if (item.TypeId == 41) {
            this.BlanksList = item.ItemChange.Rows
            const arr = this.BlanksList.map((blank, index) => {
              return blank.map((val, ind) => {
                return val.split('\r\n').map((el, inx) => {
                  const str = el.split('(#@)')
                  return `${
                    str[0]
                  }<input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                      data-type="${item.TypeId}"  id="${index + '.' + inx}" data-index="${index}"/>${str[1]}`
                })
              })
            })
            this.selfList = arr[0]
            this.asList = [[]]
          }
          // 可变行下拉填空题
          if (item.TypeId == 46) {
            this.SelectList = item.ItemChange
            const SelectArr = this.forSelect(this.SelectList.Rows)
            this.SplitSelectArr = SelectArr
          }
          // 思考问答题
          if (item.TypeId == 47) {
            this.QAList = item.ItemChange.ItemConfigOptionAnswer
            const regx = /\(\#\&\d+\@\)/g
            const reg = /\(\#@\)/g
            const input = ''
            const regx1 = /\(\#\&/g
            const regx2 = /\@\)/g
            const regxlist = item.Title.match(regx)
            if (regxlist > 0) {
              regxlist.forEach((item1) => {
                const value = item1.replace(regx1, '').replace(regx2, '')
                const title = item.Title.replace(
                  item1,
                  `<input class="input" autocomplete="off"  type="text" style="width: 100px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                data-type="${item.TypeId}"  id="${value}" data-index="${value}"/>`
                )
                item.Title = title
              })
            }
            this.QAList.ItemConfigOptions.forEach((i) => {
              const regxlist = i.Context.match(reg)
              if (regxlist !== null) {
                regxlist.forEach((item1) => {
                  const value = item1.replace(reg, '')
                  const title = i.Context.replace(
                    item1,
                    `<input class="input" autocomplete="off"  type="text" style="width: 100px;;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px"
                      data-type="${item.TypeId}"  id="${1}" data-index="${value}"/>`
                  )
                  i.Context = title
                })
              }
            })
          }
          if (item.TypeId == 42) {
            this.RowsList = item.ItemChange
            const RowArr = this.FormatRows(item, this.RowsList.Rows, this.styleStr)
          }
          // 连线题
          if (item.TypeId == 50) {
          }
          // 添加 答案，单个做题时间， 是否已回答变量, 是否正在回答
          // if(item.UserAnswer?.length && item.TypeId == 36){
          //   item.UserAnswer = item.url
          // }
          item.ATime = item.UserATime || 0
          item.Answer = ''
          item.AnswerList = []
          item.WidhList = []
          item.HasAnswer = item.UserAnswer?.length ? true : false
          item.isUserAnswer=false // 是否单题提交
          item.CurrentAnswer = index === 0
          item.AuditPaperItemAnswers.forEach((el) => {
            const regex = new RegExp('<img')
            const reg = new RegExp('\r\n\r\n')
            const ImgContent = el.Content.replace(
              regex,
              `<img style="max-width: 100%;max-height:100%;width:auto;height:auto;"`
            )
            el.Content = ImgContent
          })
        })
        //需要把组合题题干过滤出来
        this.zuheti = ExamFormatCommon.filterZuheti(res.Data)
        res.Data[0].CurrentAnswer = true
        this.PaperList = res.Data
        const FristTitle = this.PaperList[0].Title
        const FristType_Id = this.PaperList[0].TypeId
        if (FristTitle.toString().indexOf('input') != -1) {
          const that = this
          setTimeout(() => {
            that.$nextTick((x) => {
              // const inputs = document.getElementById('AnswerPaper').getElementsByTagName('input')
              const inputs = $(`#Imgid${this.num}`).find('input')
              that.currentInputlength = inputs.length
              // console.log(that.currentInputlength, ':that.currentInputlength')
              // console.log(that.boardType)

              that.currentInput = inputs[0]
              that.Review_Key_pad(that.boardType)
            })
          }, 100)
        }
        if (FristType_Id == '40' || (FristType_Id == '42' && this.keyboardAuto)) {
          setTimeout(() => {
            this.$refs.keyword.KeyWrod_True()
          }, 100)
        } else {
          this.$refs.keyword.KeyWrod_False()
        }
        this.PaperNum = res.Data.length
        if (this.PaperNum == 1) {
          this.nextDisabled = false
          this.SubmitBtn = true
        } else {
          this.nextDisabled = true
          this.SubmitBtn = false
        }
        let ParentId_c = this.PaperList[this.CurrentNum - 1].ParentId
        this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).Title : ''
        this.GetImgAll()
        this.PaperList[0].AuditPaperItemAnswers.forEach((e) => {
          if (e.Type == 8) {
            this.content = e.Content
          }
        })
      }
    },
    bigit() {
      var image = document.getElementsByClassName('pic')[0]
      image.style.height = image.height * 1.1 + 'px'
      image.style.width = image.width * 1.1 + 'px'
    },

    littleit() {
      var image = document.getElementsByClassName('pic')[0]
      image.style.height = image.height / 1.1 + 'px'
      image.style.width = image.width / 1.1 + 'px'
    },

    Clear_Img() {
      this.dialogVisible = false
    },

    GetImgAll() {
      const that = this
      that.$nextTick(() => {
        // const MyImg = document.getElementById('Imgid').querySelector('img')
        const MyImg = document.getElementById(`Imgid${this.num}`).querySelector('img')
        if (MyImg !== null) {
          MyImg.style.cursor = 'pointer'
          MyImg.style.maxWidth = '90%'
          MyImg.onclick = function () {
            that.$refs.Image.showViewer = true
            that.dialogImageUrl = MyImg.src
          }
        }
      })
    },

    // 计时器
    timeRecord(bolean) {
      const _this = this
      let hour, minute, second
      hour = minute = second = 0
      if (bolean === true) {
        _this.timer = setInterval(() => {
          if (second >= 0) {
            second = second + 1
          }
          if (second >= 60) {
            second = 0
            minute = minute + 1
          }
          if (minute >= 60) {
            minute = 0
            hour = hour + 1
          }
          _this.CallinTime = hour + '时' + minute + '分' + second + '秒'
          _this.$refs['CallinTime'].innerText = '总计' + _this.CallinTime
        }, 1000)
      } else {
        window.clearInterval(_this.timer)
      }
    },

    // 单题计时
    setTimmer() {
      // if (this.timer) {
      //   clearInterval(this.timer)
      //   this.timer = null
      // }
      this.timmer = setInterval(() => {
        this.currentTime++
      }, 1000)
    },

    // 单选题
    RadioCheck(e) {
      if (e.Answer) {
        e.HasAnswer = true
      }
    },

    FormatRows(item, arr, style) {
      const regx = /\(\#@\)/g
      arr.forEach((td, idx, ary) => {
        td.forEach((i, index, array) => {
          const TableRegx = i.match(regx)
          if (TableRegx !== null) {
            TableRegx.forEach((item1) => {
              const value = item1.replace(regx, '')
              const title = i.replace(
                item1,
                // `<input class="input" autocomplete="off"  type="text" style="width: 100px;border: 0px;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                //       id="${idx + '.' + index}"  data-type="${item.TypeId}" data-index="${index}"/>`
                `
                  <div
                    class="input_box"
                    style='${style}'
                    data-type="${item.TypeId}"
                    data-index="${idx + '.' + index}"
                  >
                    <input  data-type="${item.TypeId}"  id="${idx + '.' + index}" data-index="${
                  idx + '.' + index
                }" type="text" autocomplete="off" style="width: 100px; background: transparent;text-align:center;"  />
                    <div
                      style="
                          width: 100px;
                          position:absolute;
                          top:0;
                          left:0;
                          text-align:center;
                        "
                      data-type="${item.TypeId}"
                      data-index="${idx + '.' + index}"
                      class="virtual_input"
                    >
                    <p data-type="${item.TypeId}" data-index="${
                  idx + '.' + index
                }" class="virtual_input_p" style="white-space: nowrap"></p>
                    </div>
                  </div>
                `
              )
              array[index] = title
            })
          }
        })
      })
    },

    // 下拉框，数据遍历
    forSelect(arr, flag,isDisabled) {
      const SelectArr = arr.map((sel, sel_index) => {
        return sel.map((val) => {
          const str1 = val.split('#&')
          let addSelctStr = ''
          str1.forEach((ele, str1_index) => {
            if (ele.length > 0) {
              addSelctStr +=
                ele +
                `<select name="age" id="${
                  flag ? this.SplitSelectArr.length : sel_index
                }.${str1_index}" class="select" style="width: 130px;margin-left: 5px;">`
              this.SelectList.SelectOptions[sel_index][str1_index].forEach((option_ele) => {
                addSelctStr += `<option value='${option_ele}' ${isDisabled ? 'disabled' :''} > ${option_ele}</option>`
              })
              addSelctStr += ` </select>`
            }
          })
          return addSelctStr
        })
      })
      return SelectArr
    },

    // 特殊题型 1---下拉
    selectSku(event) {
      var index = event.target.selectedIndex
      var value = event.target.options[index].value // 选中值
      const item = {
        id: event.target.id,
        value: value,
      }
      const idx = this.PaperList[this.CurrentNum - 1].AnswerList.findIndex((obj) => {
        return obj.id === event.target.id
      })
      if (idx !== -1) {
        this.PaperList[this.CurrentNum - 1].AnswerList[idx].value = value
      } else {
        this.PaperList[this.CurrentNum - 1].AnswerList.push(item)
      }
      this.getSelectAnswer()
    },

    // 特殊题型 1---下拉, 获取Answer
    getSelectAnswer() {
      const selects = document.getElementsByTagName('select')
      const list = this.PaperList[this.CurrentNum - 1].AnswerList
      this.SelectNum = selects.length
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < list.length; i++) {
        if (i + 1 === list.length) {
          this.PaperList[this.CurrentNum - 1].Answer += list[i].value
        } else {
          if (list[i].id.split('.')[0] === list[i + 1].id.split('.')[0]) {
            this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + '|'
          } else {
            this.PaperList[this.CurrentNum - 1].Answer += (list[i].value ? list[i].value : '') + ','
          }
        }
      }
      if (list.length == selects.length) {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },
    // 下拉选择 添加
    Select_AddRow() {
      const selects = document.getElementsByTagName('select')
      const addRow = this.forSelect(this.SelectList.Rows, true)
      this.SelectNum = selects.length + 2
      this.SplitSelectArr = this.SplitSelectArr.concat(addRow)
    },

    // 下拉选择 删除
    Select_ClearRow() {
      const _this = this
      function findDelIdx() {
        const idx = _this.PaperList[_this.CurrentNum - 1].AnswerList.findIndex((obj) => {
          return obj.id.split('.')[0] === (_this.SplitSelectArr.length - 1).toString()
        })
        if (idx !== -1) {
          _this.PaperList[_this.CurrentNum - 1].AnswerList.splice(idx, 1)
          findDelIdx()
        }
      }
      findDelIdx()
      this.getSelectAnswer()
      this.SplitSelectArr.splice(-1)
      const selects = document.getElementsByTagName('select')
      this.SelectNum = selects.length - 2
    },

    // 特殊题型41---可变行填空 获取Answer
    BlankInput(event, indx, ind) {
      let list
      this.asList[indx][ind] = event.target.value
      for (let i = 0; i < 6; i++) {
        if (i !== ind) {
          if (!this.asList[indx][i]) {
            this.asList[indx][i] = ''
          }
        }
      }
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(this.asList))
      list = JSON.parse(JSON.stringify(this.PaperList[this.CurrentNum - 1].AnswerList))
      this.PaperList[this.CurrentNum - 1].Answer = list.map((item) => item.join('|')).join(',')
      this.PaperList[this.CurrentNum - 1].HasAnswer =
        list.length > 0 &&
        list.every((val) => {
          return (
            val.length > 0 &&
            val.some((v) => {
              return v !== ''
            })
          )
        })
    },

    // 填空题---41 添加行
    Blank__AddRow(rows) {
      const alen = this.selfList.length
      var newArr = JSON.parse(JSON.stringify(rows[0]))
      newArr = newArr.map((n) => {
        return n.replace(/id=\"0./g, `id=\"${alen}.`)
      })
      this.selfList.push(newArr)
      this.asList.push([])
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(this.asList))
      this.PaperList[this.CurrentNum - 1].HasAnswer =
        this.PaperList[this.CurrentNum - 1].AnswerList.length > 0 &&
        this.PaperList[this.CurrentNum - 1].AnswerList.every((val) => {
          return (
            val.length > 0 &&
            val.some((v) => {
              return v !== ''
            })
          )
        })
    },

    // 填空题---41 删除行
    Blank_ClearRow() {
      this.selfList.splice(-1)
      this.asList.splice(-1)
      const NewAnswerList = JSON.parse(JSON.stringify(this.PaperList[this.CurrentNum - 1].AnswerList))
      const MapNewList = JSON.parse(JSON.stringify(NewAnswerList.splice(-1)))
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(NewAnswerList))
      this.PaperList[this.CurrentNum - 1].Answer = NewAnswerList.map((item) => item.join('|')).join(',')
      this.PaperList[this.CurrentNum - 1].HasAnswer =
        NewAnswerList.length > 0 &&
        NewAnswerList.every((val) => {
          return (
            val.length > 0 &&
            val.some((v) => {
              return v !== ''
            })
          )
        })
    },

    // 表格题  添加行
    Table_AddRow(item, IsSNum, rows) {
      const inputs = $(`#Imgid${this.num}`).find('input')
      this.TableInputs = inputs.length + 2
      var newArr = JSON.parse(JSON.stringify(rows[0]))
      if (IsSNum == 0) {
        for (var i = 0; i < newArr.length; i++) {
          if (newArr[i].toString().indexOf('input') != -1) {
            newArr[
              i
            ] = `<input class="input" autocomplete="off"  type="text" style="width: 100px;;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                         id="${rows.length + 1 + '.' + i}" data-type="40" data-index="${i}"/>`
          }
        }
        this.tableList.Rows.push(newArr)
      }
      if (IsSNum == 1) {
        newArr[0] = rows.length + 1
        for (var i = 0; i < newArr.length; i++) {
          if (newArr[i].toString().indexOf('input') != -1) {
            newArr[
              i
            ] = `<input class="input" autocomplete="off"  type="text" style="width: 100px;;border-radius:4px; color:#000; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4);margin-bottom: 10px;margin:0 2px 0 2px;"
                         id="${rows.length + 1 + '.' + i}" data-type="40" data-index="${i}"/>`
          }
        }
        this.tableList.Rows.push(newArr)
      }
    },

    // 表格题 删除行
    Table_ClearRow(IsSNum) {
      const inputs = $(`#Imgid${this.num}`).find('input')
      const Table_List = JSON.parse(JSON.stringify(this.PaperList[this.CurrentNum - 1].AnswerList))
      this.TableInputs = inputs.length - 2
      this.tableList.Rows.pop()
      if (Table_List.length !== 2) {
        if (Table_List.length % 2 === 0) {
          Table_List.pop()
          Table_List.pop()
        } else {
          Table_List.pop()
        }
      }
      this.PaperList[this.CurrentNum - 1].AnswerList = JSON.parse(JSON.stringify(Table_List))
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < Table_List.length; i++) {
        if (i + 1 === Table_List.length) {
          this.PaperList[this.CurrentNum - 1].Answer += Table_List[i].value
        } else {
          if (Table_List[i].id.split('.')[0] === Table_List[i + 1].id.split('.')[0]) {
            this.PaperList[this.CurrentNum - 1].Answer += (Table_List[i].value ? Table_List[i].value : '') + '|'
          } else {
            this.PaperList[this.CurrentNum - 1].Answer += (Table_List[i].value ? Table_List[i].value : '') + ','
          }
        }
      }
    },

    // 多选题
    Checkbox(value) {
      this.SelectionList = value.sort()
      this.PaperList[this.CurrentNum - 1].AnswerList = this.SelectionList
      const list = this.SelectionList
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < list.length; i++) {
        if (i + 1 === list.length) {
          this.PaperList[this.CurrentNum - 1].Answer += list[i]
        } else {
          this.PaperList[this.CurrentNum - 1].Answer += (list[i] ? list[i] : '') + ','
        }
      }
      if (list.length !== 0) {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },

    // 特殊题型四 是否是展示第二项
    ShowRadio() {
      this.show_radiobox = true
      const inputs = $(`#Imgid${this.num}`).find('input')
      const Targetvalue = inputs[0].value
      if (this.Getvideo !== '' && Targetvalue !== '') {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
      }
    },
    // 特殊题型四  radio选中
    onRadioChange(event) {
      let ChangeVal = ''
      if (event.target.value == 'A') {
        this.options_Context = true
        ChangeVal = '有'
      } else {
        this.options_Context = false
        ChangeVal = '没有'
      }
      localStorage.setItem('Type_41', this.PaperList[this.CurrentNum - 1].Answer.split('|')[0] + '|' + ChangeVal)
      this.Getvideo = ChangeVal
      if (this.Getvideo !== '' && this.Getvideo !== '有') {
        this.PaperList[this.CurrentNum - 1].HasAnswer = true
      } else {
        // const inputs = $(`#Imgid${this.num}`).find('input')
        const inputs = $(`#radio_box_input`).find('input')
        inputs[0].value = ''
        this.PaperList[this.CurrentNum - 1].HasAnswer = false
        // if (!inputs[0].value) {
        // } else {
        //   this.PaperList[this.CurrentNum - 1].HasAnswer = true
        // }
      }
    },
    // 下一题
    next(index, item) {
      this.$nextTick(() => {
        this.PaperList[this.CurrentNum - 1].ATime = this.currentTime
        item.CurrentAnswer = false
        this.PaperList[index + 1].CurrentAnswer = true
        this.$set(this.PaperList, index + 1, this.PaperList[index + 1])
        this.targetIndex(index + 1, this.PaperList[index + 1])
      })
    },
    // 练习指导
    exerciseGuidance(item) {
      this.Guidance = item.Tips
      this.CloseGuidanceVisible = true
      const data = {
        paperId: this.paperId,
        itemId: item.ItemId,
        studentId: localStorage.getItem('UserId'),
        subjectId: this.Multidisciplinary.SubjectId,
      }
      this.$uwonhttp.post('/Paper/Paper/StudentItemTipsView', data).then((res) => {
        if (res.data.Success) {
          return false
        }
      })
    },
    prevPhoto() {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto() {
      this.$refs.mySwiper.$swiper.slideNext()
    },
    // 点击跳转相对应题目
    targetIndex(index, item) {
      setTimeout(() => {
        //判断是否要显示组合题题干
        let ParentId_c = this.PaperList[this.CurrentNum - 1].ParentId
        this.zuhetiTitle = ParentId_c ? this.zuheti.find((zh) => zh.ItemId == ParentId_c).Title : ''
        const arr = ['5', '40', '47', '42', '41']
        const CurrentNum_TypeId = this.PaperList[this.CurrentNum - 1].TypeId
        if (arr.indexOf(CurrentNum_TypeId) != -1) {
          this.$nextTick((x) => {
            const input_box = $(`#Imgid${this.num}`).find('.input_box')
            this.currentInputlength = input_box.length
            this.currentInput =
              $(`#Imgid${this.num}`).find('.virtual_input')[0] || $(`#Imgid${this.num}`).find('input')[0]
            const v =
              this.currentInput && this.currentInput.MathQuill
                ? this.currentInput.MathQuill.text()
                : this.currentInput
                ? this.currentInput.value
                : ''
            this.$refs.keyword.chengeVal(v)
            this.PaperList[this.num].AuditPaperItemAnswers.forEach((e) => {
              if (e.Type == 8) {
                this.content = e.Content
              }
            })
            this.Review_Key_pad(this.boardType)
          })
        } else {
          this.$refs.keyword.KeyWrod_False()
        }
      }, 100)
      // 选词填空题型清空实例暂存数据
      if (item.TypeId == '52' || item.TypeId == '70') {
        const MultitermSingle = item.ItemChange.MultitermSingle
        if (MultitermSingle !== null && MultitermSingle.Options[0].IsContentShow === 2) {
          this.arrValue = MultitermSingle.Options[0].Content.split('|')
        } else {
          this.arrValue = []
        }
        if (this.PaperList[index].AnswerList.length > 0) {
          this.AnswerArr = this.PaperList[index].AnswerList
        } else {
          this.AnswerArr = []
        }
        // this.AnswerArr = this.PaperList[this.CurrentNum - 1].AnswerList
      } else {
        this.arrValue = []
      }
      if (this.PaperList[this.CurrentNum - 1].TypeId == '41') {
        const Type41_Arr = this.PaperList[this.CurrentNum - 1].AnswerList
        this.PaperList[this.CurrentNum - 1].HasAnswer =
          Type41_Arr.length > 0 &&
          Type41_Arr.every((val) => {
            return (
              val.length > 0 &&
              val.some((v) => {
                return v !== ''
              })
            )
          })
      }

      if (this.PaperList[this.CurrentNum - 1].TypeId == '46') {
        const SelectLength = this.SelectNum
        const CurrentLength = this.PaperList[this.CurrentNum - 1].AnswerList.length
        if (CurrentLength == 0 && SelectLength == 0) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        } else if (CurrentLength !== SelectLength) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        } else {
          this.PaperList[this.CurrentNum - 1].HasAnswer = true
        }
      }

      if (this.PaperList[this.CurrentNum - 1].TypeId == '40' || this.PaperList[this.CurrentNum - 1].TypeId == '42') {
        const TableLength = this.TableInputs
        const CurrentTable = this.PaperList[this.CurrentNum - 1].AnswerList.length
        const TableAnswerList = this.PaperList[this.CurrentNum - 1].AnswerList
        const Table_arr = []
        TableAnswerList.forEach((el) => {
          Table_arr.push(el.value)
        })
        if (
          Table_arr.every((v) => {
            return v !== ''
          }) &&
          CurrentTable !== 0 &&
          TableLength !== 0 &&
          CurrentTable == TableLength
        ) {
          this.PaperList[this.CurrentNum - 1].HasAnswer = true
        } else {
          this.PaperList[this.CurrentNum - 1].HasAnswer = false
        }
        this.PaperList[this.CurrentNum - 1].Answer = ''
        for (let i = 0; i < TableAnswerList.length; i++) {
          if (i + 1 === TableAnswerList.length) {
            this.PaperList[this.CurrentNum - 1].Answer += TableAnswerList[i].value
          } else {
            if (TableAnswerList[i].id.split('.')[0] === TableAnswerList[i + 1].id.split('.')[0]) {
              this.PaperList[this.CurrentNum - 1].Answer +=
                (TableAnswerList[i].value ? TableAnswerList[i].value : '') + '|'
            } else {
              this.PaperList[this.CurrentNum - 1].Answer +=
                (TableAnswerList[i].value ? TableAnswerList[i].value : '') + ','
            }
          }
        }
      }

      if (this.PaperList[this.CurrentNum - 1].TypeId == '47') {
        if (this.show_radiobox == true) {
          if (this.Getvideo == '') {
            this.PaperList[this.CurrentNum - 1].HasAnswer = false
          } else if (this.Getvideo == '有') {
            if (this.PaperList[this.CurrentNum - 1].AnswerList.length !== 2) {
              this.PaperList[this.CurrentNum - 1].HasAnswer = false
            } else {
              this.PaperList[this.CurrentNum - 1].HasAnswer = true
            }
          } else if (this.Getvideo == '没有') {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          } else {
            this.PaperList[this.CurrentNum - 1].HasAnswer = true
          }
        }
      }
      this.SelectionList = []
      this.PaperList[this.CurrentNum - 1].ATime = this.currentTime
      if (this.timmer) {
        clearInterval(this.timmer)
        this.timmer = null
        this.currentTime = this.PaperList[index].ATime
      }
      const currentState = item.CurrentAnswer
      this.PaperList.forEach((el) => {
        el.CurrentAnswer = false
      })
      item.CurrentAnswer = true
      this.setTimmer()
      this.num = index
      const CurrentNum2 = JSON.parse(JSON.stringify(this.CurrentNum))
      this.CurrentNum = index + 1
      const _this = this
      this.$nextTick(() => {
        if (_this.PaperList[index].Answer !== '') {
          if (_this.PaperList[index].TypeId === '5') {
            const ansList = _this.PaperList[index].AnswerList
            const widthlist = _this.PaperList[index].WidhList
            // for (let i = 0; i < ansList.length; i++) {
            //   if (ansList[i]) {
            //     document.getElementById(i).value = ansList[i]
            //   } else {
            //     document.getElementById(i).value = ''
            //   }
            // }
            for (let i = 0; i < widthlist.length; i++) {
              if (widthlist[i]) {
                document.getElementById(i).style.width = widthlist[i]
              } else {
                document.getElementById(i).style.width = 100 + 'px'
              }
            }
          } else if (_this.PaperList[index].TypeId === '10') {
            _this.SelectionList = _this.PaperList[_this.CurrentNum - 1].AnswerList
          } else if (_this.PaperList[index].TypeId === '40') {
            const ansList2 = _this.PaperList[index].AnswerList
            for (let i = 0; i < ansList2.length; i++) {
              document.getElementById(ansList2[i].id).value = ansList2[i].value
            }
          } else if (_this.PaperList[index].TypeId === '41') {
            const ansList3 = _this.PaperList[_this.CurrentNum - 1].AnswerList
            ansList3.forEach((item, index, array) => {
              item.forEach((el, inx) => {
                document.getElementById(`${index}.${inx}`).value = el
              })
            })
          } else if (_this.PaperList[index].TypeId === '46') {
            const ansList4 = _this.PaperList[_this.CurrentNum - 1].AnswerList
            for (let i = 0; i < ansList4.length; i++) {
              document.getElementById(ansList4[i].id).value = ansList4[i].value
            }
          } else if (_this.PaperList[index].TypeId === '47') {
            const ansList5 = _this.PaperList[index].AnswerList
            ansList5.forEach((el, index, array) => {
              document.getElementById(`${index}`).value = el
            })
          } else if (_this.PaperList[index].TypeId === '42') {
            const ansList6 = _this.PaperList[index].AnswerList
            for (let i = 0; i < ansList6.length; i++) {
              document.getElementById(ansList6[i].id).value = ansList6[i].value
            }
          }
          // else if(_this.PaperList[index].TypeId === '52') {
          //   const ansList7 = _this.PaperList[index].AnswerList
          //   console.log('52')
          //   console.log(ansList7)
          // }
        }
      })
      this.targetImg(index)
      this.callswiper(index)
    },

    callswiper(index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },

    targetImg(index) {
      const that = this
      that.$nextTick(() => {
        // const MyImg = document.getElementById('Imgid').querySelector('img')
        const MyImg = document.getElementById(`Imgid${index}`).querySelector('img')
        if (MyImg !== null) {
          MyImg.style.cursor = 'pointer'
          MyImg.style.maxWidth = '100%'
          MyImg.onclick = function () {
            that.$refs.Image.showViewer = true
            that.dialogImageUrl = MyImg.src
          }
        }
      })
    },
    // 转utf-8
    str2utf8(str) {
      // var encoder = new TextEncoder('utf8');
      return eval("'" + encodeURI(str).replace(/%/gm, '\\x') + "'")
    },
    submit() {
      if (this.submit_lock) {
        this.$message({
          message: '不能重复提交！',
          type: 'warning',
        })
        return
      }
      const that = this
      this.$confirm('是否确认提交?', '提交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
      })
        .then(() => {
          that.submit_lock = true
          that.PaperList.forEach((el, index) => {
            // 如果有用户答案，但是没有答案 进行赋值
            if (el.UserAnswer?.length && !el.Answer || el.TypeId == 36) {
              el.Answer = el.Answer || el.UserAnswer
              el.ATime = el.ATime || 0
            }
            if (el.TypeId === '51') {
              if(el.UserAnswer?.length){
                that.AnswerList.push({
                  ItemId: el.ItemId,
                  Answer: el.UserAnswer,
                  ATime: el.ATime,
                })
              }else{
                const answerList = el.ItemChange.TableItemInfo.filter((item) => item.type === 2)
                that.AnswerList.push({
                  ItemId: el.ItemId,
                  Answer: answerList
                    .map((item) => {
                      const { stuAnswer } = item
                      return stuAnswer.includes('\\') || stuAnswer.includes('^')
                        ? `<span class="math-tex">\\(${stuAnswer}\\)</span>`
                        : stuAnswer
                    })
                    .join('|'),
                  ATime: el.ATime,
                })
              }
              return
            }
            that.AnswerList.push({
              ItemId: el.ItemId,
              Answer:
                el.TypeId === '47' && localStorage.getItem('Type_41')
                  ? `${localStorage.getItem('Type_41')}${
                      el.Answer.split('|')[1] && !localStorage.getItem('Type_41').includes('没有')
                        ? `|${el.Answer.split('|')[1]}`
                        : ''
                    }`
                  : // 填空题中包含公式答案向后台传递数据格式处理
                  el.TypeId === '2' ||
                    el.TypeId === '10' ||
                    el.TypeId === '11' ||
                    el.TypeId === '36' ||
                    el.TypeId === '46' ||
                    el.TypeId === '52' ||
                    el.TypeId === '50' ||
                    el.TypeId === '70' ||
                    el.TypeId === '41' ||
                    el.TypeId === '40'
                  ? el.Answer
                  : ExamFormatCommon.formula_Answer(el.IsFormula, el.AnswerList.length > 0 ? el.AnswerList : el.UserAnswer?.split('|') || []),
              ATime: el.ATime,
            })
            if (el.url !== undefined || el.TypeId == 36) {
              this.solveProblemsKernelImgList.push({
                ItemId: el.ItemId,
                Answer: el.url || el.Answer
              })
            }
          })
          if(this.PaperList.every(item=>item.UserAnswer?.length>0)){
            return that.StudentDoPaper()
          }
          if (that.PaperList.some((val) => val.HasAnswer === false) ) {
            this.$message({
              type: 'error',
              message: '请检查是否已经答完',
            })
            that.AnswerList = []
            that.submit_lock = false
          } else {
            if (that.AnswerList.some((val) => val.Answer === '')) {
              this.$message({
                type: 'error',
                message: '请提交完整答案',
              })
              that.AnswerList = []
              that.submit_lock = false
            } else {
              if (that.PaperList.some((val) => val.IsUpload === 1 && !val.url)) {
                this.$message({
                  type: 'error',
                  message: '请检查试题是否完成拍照',
                })
                that.AnswerList = []
                that.submit_lock = false
                return false
              } else {
                that.StudentDoPaper()
              }
            }
          }
          // 三个助手执行
          if(localStorage.getItem('LoginSource') == 2){
            this.PushStudentVerb()
          }
        })
        .catch((error) => {
          console.log(error,'======>error')
          this.$message({
            type: 'info',
            message: '已取消提交',
          })
          that.AnswerList = []
          that.submit_lock = false
        })
    },
    // 三个助手退出登陆需要调用接口
    async PushStudentVerb () {
      await this.$uwonhttp.post('/XAPI/XAPI/PushStudentVerb', { studentId: localStorage.getItem('UserId'), type: '2' })
    },
    // 试题提交主体部分
    StudentDoPaper() {
      const that = this
      that.timeRecord(false)
      this.PaperList[this.CurrentNum - 1].ATime = this.currentTime
      const hourNum = that.CallinTime.indexOf('时')
      const minuteNum = that.CallinTime.indexOf('分')
      const secondNum = that.CallinTime.indexOf('秒')
      const hour = that.CallinTime.slice(0, hourNum)
      const minute = that.CallinTime.slice(hourNum + 1, minuteNum)
      const second = that.CallinTime.slice(minuteNum + 1, secondNum)
      const time = hour * 360 + minute * 60 + second * 1
      that.AnswerList[that.AnswerList.length - 1].ATime = this.PaperList[this.CurrentNum - 1].ATime
      if (this.zuheti.length > 0) {
        this.zuheti.forEach((zuhe) => {
          this.AnswerList.splice(zuhe.oldIndex, 0, {
            ATime: 0,
            Answer: '组合题干',
            ItemId: zuhe.ItemId,
          })
        })
      }
      // console.log(that.AnswerList)
      // console.log(this.solveProblemsKernelImgList)
      // return
      that.$uwonhttp
        .post('/Paper/Paper/StudentDoPaper', {
          DoTime: time,
          UserId: that.userId,
          PaperId: that.paperId,
          answerJson: JSON.stringify(that.AnswerList),
        })
        .then((res) => {
          if (res.data.Success) {
            that.SaveSubjectiveItemAnswer(res.data.Data)
            // ######################################下面部分暂时先注释，后续接口调整完再重新整。陈少波：解题思路
            // that.$message.success('提交成功')
            // localStorage.removeItem('Type_41')
            // that.$router.push({
            //   path: '/Student/Exam_Student/jobReport',
            //   query: {
            //     paperId: that.paperId,
            //     IsThereLevelItem: res.data.Data.IsThereLevelItem,
            //     IsQueue: res.data.Data.IsQueue // 该值针对成绩报告是否可立即展示做页面展现区分
            //   }
            // })
          } else {
            that.$message.error('错误：' + res.data.Msg)
            that.AnswerList = []
          }
          that.AnswerList = []
          that.submit_lock = false
        })
    },
    // 提交副接口
    SaveSubjectiveItemAnswer (odd) {
      const data = {
        UserId: this.userId,
        PaperId: this.paperId,
        answerJson: JSON.stringify(this.solveProblemsKernelImgList)
      }
      this.$uwonhttp.post('/Paper/Paper/SaveSubjectiveItemAnswer', data).then(res => {
        if (res.data.Success) {
          this.$message.success('提交成功')
          localStorage.removeItem('Type_41')
          this.$router.push({
            path: '/Student/Exam_Student/jobReport',
            query: {
              paperId: this.paperId,
              IsThereLevelItem: odd.IsThereLevelItem,
              IsQueue: odd.IsQueue // 该值针对成绩报告是否可立即展示做页面展现区分
            }
          })
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    }
  }
}
</script>
<style lang="less">
.el-image-viewer__wrapper {
  .el-image-viewer__actions {
    width: 300px;
    .el-image-viewer__actions__inner {
      i {
        font-size: 36px;
      }
    }
  }
}
</style>
<style lang="less" scoped>
@import './commonStyle/Paper.less';

.zuheti {
  width: 30%;
  padding-right: 20px;
  margin-right: 20px;
  border-right: 1px solid #e6e6e6;
}
.formatImg {
  white-space: normal;
  //width: auto !important;
  width: 1500px;
}
#AnswerPaper {
  font-family: PingFangSC-Regular, PingFang SC;
}
.hexinsuyangPaizhao {
  p {
    font-size: 18px;
    font-weight: bold;
  }
}
.paperheader {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  border-radius: 5px;
  background: #fff;
  //font-size: 23px;
  position: relative;
  span {
    line-height: 45px;
  }
  .tiltcount {
    vertical-align: middle;
    margin-right: 20px;
  }
  /deep/.el-button {
    margin-top: 0;
    width: 100px;
    font-size: 18px;
  }
}
// 多学科部分
.xuekeStyle {
  margin-left: 18px;
  padding: 4px 18px;
  border-radius: 100px;
}
.radio_text {
  /deep/.ant-radio-group {
    display: flex;
    flex-direction: column;
    .ant-radio-wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      white-space: pre-wrap !important;
      .ant-radio {
        transform: scale(1.1);
      }
    }
  }
}
/deep/.MJXc-display {
  text-align: left !important;
}
/deep/.el-button {
  background: #61bb96;
  border-color: #61bb96;
  color: #fff;
  margin-top: 20px;
}
.Multiple {
  display: flex;
  flex-direction: column;
  /deep/.ant-checkbox-wrapper {
    margin-left: 0px;
    display: flex;
    align-items: center;
    margin-bottom: 14px;
    .ant-checkbox {
      transform: scale(1.1);
    }
  }
}
///deep/.ant-checkbox-wrapper + .ant-checkbox-wrapper {
//  margin-left: 0px;
//  display: block;
//  margin-bottom: 14px;
//}
.outerdiv {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 2;
  width: 100%;
  height: 100%;
}
.pic {
  width: 50%;
  position: relative;
  left: 25%;
  top: 25%;
}

.swiper_list {
  width: 100%;
  display: flex;
  justify-content: space-between;
  .tea-analusis {
    position: relative;
    width: 80%;
    height: auto;
    padding: 10px;
    background: #ffff;
    border-radius: 5px;
  }
  .line_border {
    width: 97%;
    height: 10px;
    position: absolute;
    border: 1px solid #e6e6e6;
    background: #e6e6e6;
    top: 50%;
  }
  .left-arrow,
  .right-arrow {
    position: absolute;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: center;
    bottom: -6%;

    z-index: 2;
    .arrow-img {
      &:hover {
        background: #68bb97;
      }
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #dadada;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      i {
        background: url('../../../assets/teacher/IntelligentCorrection/icon-arrow.png') no-repeat center center;
        width: 16px;
        height: 16px;
      }
    }
  }
  .left-arrow {
    left: 8px;
    .arrow-img {
      transform: rotate(180deg);
    }
  }
  .right-arrow {
    right: 6px;
  }
  .imgbox {
    //width: 20%;
    //font-size: 18px;
    padding: 10px;
    display: flex;
    align-items: center;
    //position: relative;
    img {
      width: 30px;
      //right: 40%;
      //top: 43%;
      //position: absolute;
    }
    span {
      margin-left: 10px;
      //right: 0;
      //top: 43%;
      //position: absolute;
    }
  }
}
</style>
<style scoped>
.el-message-box {
  width: 300px;
}
</style>

<style lang="less">
.input_box {
  padding: 5px 10px;
  min-width: 120px;
  height: auto !important;
  max-width: 600px;
  display: inline-block;
  text-align: center !important;
}
</style>
