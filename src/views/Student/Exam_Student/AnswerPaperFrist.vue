<template>
  <div class="Answer">
    <div class="Answer_left">
      <div class="left_title">
        <span @click="GoBack"><i class="el-icon-arrow-left"></i>返回</span>
        <span>{{ PaperName }}</span>
        <span></span>
      </div>
      <div class="left_info">
        <div class="paper_item" v-for="(item,index) in PaperList" :key="index">
          <div class="paper_type" v-if="num===index">
            <div class="type_after"></div>
            <span v-if="item.TypeId==2">单项选择题</span>
            <span v-if="item.TypeId==5">填空题</span>
            <span v-if="item.TypeId==10">多项选择题</span>
            <span v-if="item.TypeId==11">判断题</span>
            <span v-if="item.TypeId==23">应用题</span>
            <span v-if="item.TypeId==36">主观题</span>
            <span v-if="item.TypeId==39">题型</span>
          </div>
          <div class="title_item" v-if="num===index">
            <span>第{{ index+1 }}题</span>
            <br />
            <span v-html="item.Title" @input="handleInput" class="formatImg"></span>
          </div>
          <div class="Topic_type" v-if="num===index">
            <!-- 单选题 -->
            <a-radio-group v-model="item.Answer" v-if="item.TypeId === '2' || item.TypeId === '11'" @change="RadioCheck(item)">
              <a-radio :value="i.Option" v-for="(i, indx) in item.AuditPaperItemAnswers" :key="indx" v-show="i.Type === 1">
                <span style="font-size: 19px" v-if="i.Type === 1">{{ i.Option }}:&nbsp;&nbsp;</span>
                <span v-if="i.Type === 1" v-html="i.Content" style="font-size: 18px; display: inline-block"></span>
              </a-radio>
            </a-radio-group>
            <!-- 多选题 -->
            <a-checkbox-group v-model="SelectionList" class="Multiple" @change="Checkbox" v-if="item.TypeId === '10'">
              <a-row>
                <a-col :span="24">
                  <a-checkbox v-for="(i, ind) in item.AuditPaperItemAnswers" :key="ind" :value="i.Option" v-show="i.Type === 1">
                    {{ i.Option }}:
                    <span v-html="i.Content" style="font-size: 20px"></span>
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
            <div class="btn_text">
              <el-button class="btn_next" @click="next(index,item)">下一题</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="Answer_right">
      <div class="right_header">
        <img src="@/assets/student/闹钟.png" alt="">
        <br />
        <span> 单个{{ currentTime }}</span>
        <br />
        <span> 总计{{ CallinTime }}</span>
        <br />
        <br />
        <span> <span style="color:#61BB96">{{ CurrentNum }}</span>/ <span>{{ PaperNum }}</span> 题</span>
      </div>
      <div class="AnswerRight_list">
        <div v-for="(item,index) in PaperList" :class="{'title-btn-whole': true, 'has-answer': item.HasAnswer, 'current-answer': item.CurrentAnswer}" :key="index" @click="targetIndex(index,item)">
          <span>{{ index+1 }}</span>
        </div>
        <div class="submit_btn">
          <el-button @click="submit" type="primary">提交</el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// title-btn-whole -未做颜色  has-answer -已做  current-answer -当前
import ExamFormatCommon from '../../../common/ExamFormatCommon'
export default {
  components: {},
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    this.GetAuditPaperItemsList()
    this.GetTheData()
  },
  mounted() {
    this.timeRecord(true)
    this.setTimmer()
  },
  data() {
    return {
      input_val: '',
      userId: '',
      paperId: '',
      PaperList: [], // 试卷信息
      PaperName: '', // 试卷名称
      CallinTime: '', // 计时时间
      timmer: null,
      currentTime: 0, // 当前题目的时间
      PaperNum: '', // 试题总数
      CurrentNum: 1, // 当前题目数
      num: 0, // 当前题目
      nextDisabled: false, // 下禁用按钮
      AnswerList: [], // 答案
      SelectionList: [] // 多选题答案
    }
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerPaper?' + ChangId) {
        this.$router.go(0)
        this.GetAuditPaperItemsList()
        this.GetTheData()
        this.timeRecord(false)
      }
    },
    num: {
      handler(now) {
        if (now == this.PaperList.length - 1) {
          this.nextDisabled = true
        } else {
          this.nextDisabled = false
        }
      },
      deep: true
    }
  },
  methods: {
    GoBack() {
      this.$router.go(-1)
    },
    // 获取试卷的名称
    async GetTheData() {
      const res = await this.$http.post('/Paper/Exam_Paper/GetTheData', {
        id: this.paperId
      })
      this.PaperName = res.Data.Title
    },
    // 获取试卷信息
    async GetAuditPaperItemsList() {
      const res = await this.$http.get('/Paper/Exam_Item/GetAuditPaperItemsList', {
        params: {
          paperId: this.paperId
        }
      })
      if (res.Success == true) {
        res.Data.forEach((item, index) => {
          if (item.TypeId == 5) {
            const regx = /\(\#\&\d+\@\)/g
            const regx1 = /\(\#\&/g
            const regx2 = /\@\)/g
            const regxlist = item.Title.match(regx)
            regxlist.forEach(item1 => {
              const value = item1.replace(regx1, '').replace(regx2, '')
              const title = item.Title.replace(
                item1,
                `<input class="input" autocomplete="off"  type="text" style="width: 100px;border: 0px; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;" id="${value}" data-index="${value}"/>`
              )
              item.Title = title
            })
          }
          // 添加 答案，单个做题时间， 是否已回答变量, 是否正在回答
          item.ATime = 0
          item.Answer = ''
          item.AnswerList = []
          item.HasAnswer = false
          item.CurrentAnswer = index === 0
          item.AuditPaperItemAnswers.forEach(el => {
            const regex = new RegExp('<img')
            const ImgContent = el.Content.replace(regex, `<img style="max-width: 80%"`)
            el.Content = ImgContent
          })
        })
        this.PaperList = res.Data
        this.PaperNum = res.Data.length
      }
    },
    // 计时器
    timeRecord(bolean) {
      const _this = this
      let hour, minute, second
      hour = minute = second = 0
      if (bolean === true) {
        _this.timer = setInterval(() => {
          if (second >= 0) {
            second = second + 1
          }
          if (second >= 60) {
            second = 0
            minute = minute + 1
          }
          if (minute >= 60) {
            minute = 0
            hour = hour + 1
          }
          _this.CallinTime = hour + '时' + minute + '分' + second + '秒'
        }, 1000)
      } else {
        window.clearInterval(_this.timer)
      }
    },
    // 单个计时
    setTimmer() {
      this.timmer = setInterval(() => {
        this.currentTime++
      }, 1000)
    },
    // 单选题
    RadioCheck(e) {
      if (e.Answer) {
        e.HasAnswer = true
      }
    },
    // 获取填空题
    handleInput(event) {
      // const value = event.target.value.trim()
      const value = ExamFormatCommon.str2utf8(event.target.value).replace(/\xe2\x80\x8b/g,"")
      this.PaperList[this.CurrentNum - 1].AnswerList[parseInt(event.target.id)] = value
      const list = this.PaperList[this.CurrentNum - 1].AnswerList
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < list.length; i++) {
        if (i + 1 === list.length) {
          this.PaperList[this.CurrentNum - 1].Answer += list[i]
        } else {
          this.PaperList[this.CurrentNum - 1].Answer += (list[i] ? list[i] : '') + '|'
        }
      }
    },
    // 多选题
    Checkbox(value) {
      this.SelectionList = value
      this.PaperList[this.CurrentNum - 1].AnswerList = this.SelectionList
      const list = this.SelectionList
      this.PaperList[this.CurrentNum - 1].Answer = ''
      for (let i = 0; i < list.length; i++) {
        if (i + 1 === list.length) {
          this.PaperList[this.CurrentNum - 1].Answer += list[i]
        } else {
          this.PaperList[this.CurrentNum - 1].Answer += (list[i] ? list[i] : '') + ','
        }
      }
    },
    // 下一题
    next(index, item) {
      if (item.Answer) {
        item.HasAnswer = true
      }
      this.PaperList[this.CurrentNum - 1].ATime = this.currentTime
      item.CurrentAnswer = false // 右边面板圆圈切换下一道题
      this.PaperList[index + 1].CurrentAnswer = true
      this.$set(this.PaperList, index + 1, this.PaperList[index + 1])
      this.targetIndex(index + 1) // 切换下一道题
      const _this = this
      this.$nextTick(() => {
        if (_this.PaperList[index].Answer !== '') {
          if (_this.PaperList[index].TypeId === '5') {
            const ansList = _this.PaperList[index].AnswerList
            for (let i = 0; i < ansList.length; i++) {
              if (ansList[i]) {
                document.getElementById(i).value = ansList[i]
              } else {
                document.getElementById(i).value = ''
              }
            }
          } else if (_this.PaperList[index].TypeId === '10') {
            _this.SelectionList = _this.PaperList[_this.CurrentNum - 1].AnswerList
          }
        }
      })
    },
    // 点击跳转相对应题目
    targetIndex(index, item) {
      if (this.timmer) {
        clearInterval(this.timmer)
        this.timmer = null
        this.currentTime = this.PaperList[index].ATime
      }
      this.setTimmer()
      this.num = index
      this.CurrentNum = index + 1
      let currentState = item.CurrentAnswer
      this.PaperList.forEach(el => {
        el.CurrentAnswer = false
      })
      item.CurrentAnswer = !currentState
      const _this = this
      this.$nextTick(() => {
        if (_this.PaperList[index].Answer !== '') {
          if (_this.PaperList[index].TypeId === '5') {
            const ansList = _this.PaperList[index].AnswerList
            for (let i = 0; i < ansList.length; i++) {
              if (ansList[i]) {
                document.getElementById(i).value = ansList[i]
              } else {
                document.getElementById(i).value = ''
              }
            }
          } else if (_this.PaperList[index].TypeId === '10') {
            _this.SelectionList = _this.PaperList[_this.CurrentNum - 1].AnswerList
          }
        }
      })
    },
    submit() {
      const that = this
      this.$confirm('是否确认提交?', '提交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          that.timeRecord(false)
          const hourNum = that.CallinTime.indexOf('时')
          const minuteNum = that.CallinTime.indexOf('分')
          const secondNum = that.CallinTime.indexOf('秒')
          const hour = that.CallinTime.slice(0, hourNum)
          const minute = that.CallinTime.slice(hourNum + 1, minuteNum)
          const second = that.CallinTime.slice(minuteNum + 1, secondNum)
          const time = hour * 360 + minute * 60 + second * 1
          that.PaperList.forEach(el => {
            that.AnswerList.push({
              ItemId: el.ItemId,
              Answer: el.Answer,
              ATime: el.ATime
            })
          })
          if (that.AnswerList.some(val => val.Answer === '')) {
            this.$message({
              type: 'error',
              message: '请提交完整答案'
            })
            that.AnswerList = []
          } else {
            that.$uwonhttp
              .post('/Paper/Paper/StudentDoPaper', {
                DoTime: time,
                UserId: that.userId,
                PaperId: that.paperId,
                answerJson: JSON.stringify(that.AnswerList)
              })
              .then(res => {
                if (res.data.Success == true) {
                  that.$message.success('提交成功')
                  that.$router.push({
                    path: '/Student/Exam_Student/jobReport',
                    query: {
                      paperId: that.paperId
                    }
                  })
                } else {
                  that.$message.error(res.data.Msg)
                }
              })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消提交'
          })
        })
    }
  }
}
</script>

<style lang="less" scoped>
.Answer {
  width: 85%;
  // height: 640px;
  height: auto;
  display: flex;
  justify-content: space-between;
  .Answer_left {
    width: 70%;
    // height: 100%;
    border-radius: 5px;
    padding: 20px;
    position: relative;
    background: #ffff;
    .left_title {
      display: flex;
      justify-content: space-between;
      span:nth-child(1) {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #858585;
        line-height: 30px;
        cursor: pointer;
      }
      span:nth-child(2) {
        font-size: 22px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #3e3e3e;
      }
    }
    .left_info {
      .paper_type {
        display: flex;
        margin-top: 20px;
        padding: 6px;
        .type_after {
          width: 5px;
          height: 30px;
          background: #2751d8;
          border-radius: 5px;
        }
        span {
          font-size: 18px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #3e3e3e;
          line-height: 33px;
          margin-left: 10px;
        }
      }
      .title_item {
        // display: flex;
        span {
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #828282;
          margin-top: 30px;
          display: inline-block;
        }
      }
      .btn_text {
        text-align: center;
        margin-top: 20px;
        .btn_next {
          width: 150px;
          border-radius: 20px;
          margin-left: 10px;
          background: #61bb96;
          color: #fff;
        }
      }
    }
  }
  .Answer_right {
    width: 29%;
    padding: 20px;
    border-radius: 5px;
    background: #ffff;
    .right_header {
      text-align: center;
      line-height: 45px;
      padding: 20px;
      img {
        height: 30px;
      }
      span {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: contents;
      }
    }
    .AnswerRight_list {
      padding: 0 40px;
      text-align: center;
      margin-left: 25px;
      .title-btn-whole {
        display: inline-block;
        width: 41px;
        height: 40px;
        line-height: 40px;
        margin: 16px 16px 0 0;
        text-align: center;
        border: 1px solid #ccc;
        border-radius: 35px;
        cursor: pointer;
      }
      .has-answer {
        color: #fff;
        background-color: #61bb96;
      }
      .current-answer {
        border-color: #da1b1b;
        color: #da1b1b;
        background-color: #fff;
      }
    }
    .submit_btn {
      margin-top: 50px;
      text-align: center;
      .el-button {
        width: 110px;
      }
    }
  }
}
.input_text {
  width: 100px;
  border-bottom: 1px solid #828282;
  text-align: center;
  font-size: 20px;
}
</style>
