<template>
  <div class="whole-mr" style="margin: 0 auto;width:100%;">
    <div class="whole_title">
      <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
      <span>成绩报告</span>
      <span></span>
    </div>
    <!-- 完成详情 -->
    <div class="Content">
      <div class="Content_left">
        <p class="jobreport-tip f-t" style="">{{ paperName }}</p>
        <div style="margin-bottom: 21px">
          <img style="width: 100px; height: 100px; border-radius: 50px" :src="userPhoto" alt="" />
          <span class="stu-info" style="">
            <p>{{ studentName }}</p>
            <p>{{ className }}</p>
          </span>
        </div>
      </div>
      <div class="Content_right">
        <div>
          <img src="@/assets/student/引号.png" alt="" />
          <p class="continue-come">{{ daPaperData.Tip }}<img src="@/assets/student/鱼.png" alt="" /></p>
          <div class="fg_Name" v-show="IsThereLevelItem == 'true'">
            <img src="@/assets/灯泡.png" alt="" />
            <span class="IsTrue">拓展题不计入学习评价统计哦！</span>
          </div>

          <div class="fg_Name">
            <span
            >你的学习评价是 <span style="color: #639da5">{{ daPaperData.ScoreLevel }}</span></span
            >
          </div>

          <div class="ping_lun">
            <!-- <span class="dis">
              <p><img src="@/assets/student/矩形.png" alt="" /> 你的学习评价是</p>
              <br />
              <p class="f-t">{{ daPaperData.ScoreLevel }}</p>
            </span> -->
            <span class="dis">
              <p><img src="@/assets/student/矩形.png" alt="" /> 正确率</p>
              <br />
              <p class="f-t" v-if="daPaperData.IsExistSubjective && !daPaperData.Subjective.IsReview">-%</p>
              <p class="f-t" v-else>{{ daPaperData.Accuracy }}%</p>
            </span>
            <span class="dis">
              <p><img src="@/assets/student/矩形.png" alt="" /> 正确题数</p>
              <br />
              <p class="f-t">
                <span v-if="daPaperData.IsExistSubjective && !daPaperData.Subjective.IsReview">-</span
                ><span v-else>{{ daPaperData.RightCount }}</span
              >/{{ daPaperData.ItemCount }}
              </p>
            </span>
            <!-- UseTime -->
            <span class="dis">
              <p><img src="@/assets/student/矩形.png" alt="" /> 答题用时</p>
              <br />
              <p class="f-t">{{ daPaperData.UseTime }}</p>
            </span>
            <span v-if="daPaperData.IsExistSubjective && !daPaperData.Subjective.IsReview">
              <p style="color: orange">提示：总计正确率，将在教师批阅主观题后给出哦~</p>
            </span>
          </div>
          <div style="margin: 25px 0" v-if="daPaperData.IsExistSubjective">
            <p style="font-size: 16px">
              共{{ daPaperData.ItemCount }}道，客观题{{ daPaperData.Objective.Count }}道，主观题{{
                daPaperData.Subjective.Count
              }}道
            </p>
            <ul style="display: flex; font-size: 14px">
              <li style="width: 40%; text-align: center">
                <p><b>客观题</b></p>
                <p style="width: 100%">
                  <el-progress type="circle" :percentage="daPaperData.Objective.Accuracy" :width="50"></el-progress>
                </p>
                <p style="width: 100%">
                  共{{ daPaperData.Objective.Count }}道，正确{{
                    daPaperData.Objective.Count - daPaperData.Objective.ErrorCount
                  }}道，错误{{ daPaperData.Objective.ErrorCount }}道
                </p>
              </li>
              <li style="width: 40%; text-align: center">
                <p><b>主观题</b></p>
                <p style="width: 100%">
                  <el-progress
                    type="circle"
                    :percentage="daPaperData.Subjective.Accuracy"
                    :width="50"
                    :color="!daPaperData.Subjective.IsReview ? '#f7f7f7;' : ''"
                    :format="() => (daPaperData.Subjective.IsReview ? daPaperData.Subjective.Accuracy : '-%')"
                  ></el-progress>
                </p>
                <p style="width: 100%">
                  <span>共{{ daPaperData.Subjective.Count }}道，</span>
                  <span v-if="daPaperData.Subjective.IsReview">
                    正确{{ daPaperData.Subjective.Count - daPaperData.Subjective.ErrorCount }}道，错误{{
                      daPaperData.Subjective.ErrorCount
                    }}道
                  </span>
                  <span v-else>待教师批阅</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="task-btn clearfix">
      <!-- <span @click="toCorrect">订正错题</span>
      <span @click="toKeepPractice">继续其他练习</span> -->
      <!-- <span>答题情况</span> -->
      <el-button type="primary" @click="PaperAnalysis()">题目解析</el-button>
<!--      <el-button v-if="daPaperData.RightCount !== daPaperData.ItemCount && !daPaperData.IsExistSubjective " type="danger"-->
<!--      >去订正（{{ daPaperData.ItemCount - daPaperData.RightCount }}道）</el-button-->
<!--      >-->
<!--      <el-button>答题情况</el-button>-->
    </div>
    <!--    <div class="complete-analysis clearfix">-->
    <!--      <div class="User_box">-->
    <!--        <p style="font-size: 20px; margin-top: 10px">跟进练习—{{ paperName }}</p>-->
    <!--        <div style="margin-top: 10px; position: relative">-->
    <!--          <img style="width: 35%" :src="userPhoto" alt="" />-->
    <!--          <span style="font-size: 19px; position: absolute; top: 10%; left: 50%">-->
    <!--            <p>{{ studentName }}</p>-->
    <!--            <p>{{ className }}</p>-->
    <!--          </span>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div class="Report_box">-->
    <!--        <div>-->
    <!--          <img src="@/assets/student/引号.png" alt="" />-->
    <!--          <img style="margin-left: 50px; margin-top: 50px" src="@/assets/student/鱼.png" alt="" />-->
    <!--        </div>-->

    <!--&lt;!&ndash;        <div class="User_message">&ndash;&gt;-->
    <!--&lt;!&ndash;          <span class="dis">&ndash;&gt;-->
    <!--&lt;!&ndash;            <p><img src="@/assets/student/矩形.png" alt="" /> 正确题数</p>&ndash;&gt;-->
    <!--&lt;!&ndash;            <p class="f-t">{{ daPaperData.RightCount }}/{{ daPaperData.ItemCount }}</p>&ndash;&gt;-->
    <!--&lt;!&ndash;          </span>&ndash;&gt;-->
    <!--&lt;!&ndash;          <span class="dis">&ndash;&gt;-->
    <!--&lt;!&ndash;            <p><img src="@/assets/student/矩形.png" alt="" /> 正确率</p>&ndash;&gt;-->
    <!--&lt;!&ndash;            <p class="f-t">{{ daPaperData.Accuracy }}%</p>&ndash;&gt;-->
    <!--&lt;!&ndash;          </span>&ndash;&gt;-->
    <!--&lt;!&ndash;          <span class="dis">&ndash;&gt;-->
    <!--&lt;!&ndash;            <p><img src="@/assets/student/矩形.png" alt="" /> 答题用时</p>&ndash;&gt;-->
    <!--&lt;!&ndash;            <p class="f-t">{{ daPaperData.UseTime }}</p>&ndash;&gt;-->
    <!--&lt;!&ndash;          </span>&ndash;&gt;-->
    <!--&lt;!&ndash;        </div>&ndash;&gt;-->
    <!--      </div>-->
    <!--    </div>-->

  </div>
</template>

<script>
import '@/utils/utils.less'
export default {
  created() {
    this.paperId = this.$route.query.paperId
    this.Type = this.$route.query.Type
    this.userId = localStorage.getItem('UserId')
    this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
    this.getPaperInfor(this.paperId)
    this.getTeacherList()
    // 做卷信息
    this.doPaperData()
  },
  data() {
    return {
      userId: '',
      // 评分星星数量
      value: 3,
      // 评分总数
      count: 4,
      // 试卷基本信息
      paperData: [],
      // 试卷名称
      paperName: '',
      // 学生姓名
      studentName: '',
      userPhoto: '',
      className: '',
      // 试卷ID
      paperId: '',
      daPaperData: {},
      Type: '',
      IsThereLevelItem: '',
      Multidisciplinary: {}
    }
  },
  watch: {
    $route() {
      this.paperId = this.$route.query.paperId
      const ChangData = window.location.href.split('?')[1]
      this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
      if (this.$route.fullPath === '/Student/Exam_Student/jobReport?' + ChangData) {
        this.Type = this.$route.query.Type
        this.getTeacherList()
        this.getPaperInfor(this.paperId)
        this.doPaperData()
      }
    },
  },
  methods: {
    GoBack() {
      if (this.$route.query.from == 'perpaper') {
        this.$router.push({
          path: '/Student/PerPaper/index',
        })
      } else {
        const paperId = this.$route.query.paperId
        this.$router.push({
          path: '/Student/Exam_Student/AnswerDetail',
          query: {
            paperId,
            paperName: this.paperName,
          },
        })
      }
      const paperId = this.$route.query.paperId
      // this.$router.push({
      //   path:'/Student/PerPaper/index'
      // })
      // return
      this.$router.push({
        path: '/Student/Exam_Student/AnswerDetail',
        query: {
          paperId,
          paperName: this.paperName
        }
      })
    },
    PaperAnalysis() {
      const data = {
        Id:this.Multidisciplinary.SubjectId,
        name:this.Multidisciplinary.name
      }
      this.$router.push({
        path: '/Student/Exam_Student/AnswerAnalysis',
        query: {
          SubjectIdName: JSON.stringify(data),
          paperId: this.paperId,
          type: this.Type
        },
      })
    },
    // 用户信息
    async getTeacherList() {
      const res = await this.$uwonhttp.post('/PersonalController/Personal/GetUserInfo', { userID: this.userId })
      this.studentName = res.data.Data.RealName
      this.className = res.data.Data.ClassName
    },
    // 做卷信息
    async doPaperData() {
      let params = {
        userId: this.userId,
        paperId: this.paperId,
        type: this.Type,
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetAdaptiveShare', params)
      if (res.data.Success) {
        this.userPhoto = res.data.Data[0].Photo
        this.daPaperData = res.data.Data[0]
      }
    },

    // 获取试卷基本信息
    async getPaperInfor(id) {
      const res = await this.$http.post('/Paper/Exam_Paper/GetTheData', { id })
      this.paperData = res.Data
      this.paperName = res.Data.Title
    },
  },
}
</script>

<style lang="less" scoped>
//.stu-info {
//  display: inline-block;
//  vertical-align: middle;
//  margin-left: 20px;
//  font-size: 14px;
//}
//.whole_title {
//  display: flex;
//  justify-content: space-between;
//  font-size: 22px;
//  padding: 15px;
//  background: #ffffff;
//  border-radius: 10px;
//}
.botm_Btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.stu-info {
  display: inline-block;
  vertical-align: middle;
  margin-left: 20px;
  font-size: 14px;
}
// 多学科部分
.xuekeStyle {
  padding: 4px 18px;
  border-radius: 100px;
}
.whole_title {
  display: flex;
  justify-content: space-between;
  font-size: 22px;
  padding: 15px;
  background: #ffffff;
  border-radius: 10px;
}
.fg {
  line-height: 50px;
}
.dis {
  display: inline-block;
  margin-right: 25px;
  text-align: center;
  p {
    font-size: 17px;
    margin-bottom: -20px;
  }
}
.f-t {
  font-size: 20px;
  color: #639da5;
}
.jobreport-tip {
  margin-bottom: 11px;
}
.fg_Name {
  img {
    width: 22px;
  }
  span {
    font-size: 17px;
    // vertical-align: middle;
  }
}
.continue-come {
  font-size: 20px;
  color: #639da5;
}
.job-title {
  height: 38px;
  line-height: 38px;
  padding-left: 15px;
  font-size: 20px;
  background-color: #fff;
  border-radius: 5px;
}
// 完成解析
.Content {
  // position: relative;
  // width: 800px;
  width: 80%;
  height: 600px;
  margin: 0 auto;
  margin-top: 60px;
  padding: 35px 45px;
  border-radius: 5px;
  background: url(../../../assets/student/报告背景.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: space-between;
}
.Content_left {
  width: 35%;
}
.Content_right {
  width: 60%;
  line-height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.ping_lun {
  display: flex;
  line-height: 20px;
}
// 按钮
.task-btn {
  width: 62%;
  margin: 40px auto 30px;
  // text-align:right;
  // margin-top: 30px;
  /deep/.el-button {
    width: 15%;
    font-size: 17px;
    float: right;
    margin-left: 10px;
    background: rgba(73, 173, 237, 1);
    border-color: rgba(73, 173, 237, 1);
    color: #fff;

  }
}
// 完成解析
//.complete-analysis {
//  position: relative;
//  width: 58%;
//  height: 70vh;
//  margin: 0 auto;
//  margin-top: 60px;
//  border-radius: 5px;
//  background: url(../../../assets/student/报告背景.png) no-repeat;
//  display: flex;
//}
//.User_box {
//  width: 40%;
//  padding: 60px;
//}
//.Report_box {
//  width: 50%;
//  padding: 30px;
//}
//.User_message {
//  margin-top: 30px;
//  display: flex;
//  justify-content: space-around;
//}
//.dis {
//  font-size: 19px;
//  text-align: center;
//}
</style>
