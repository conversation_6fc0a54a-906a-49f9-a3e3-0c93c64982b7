﻿<template>
  <a-modal :title="title" width="40%" :maskClosable="false" :visible="visible" :confirmLoading="loading" @ok="handleSubmit" @cancel="handleCancel">
    <a-spin :spinning="loading">
      <a-form :form="form">
        <a-form-item label="用户名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['UserName', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['PhoneNum', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="密码" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input type="password" autocomplete="false" placeholder="请输入密码" v-decorator="['newPwd', { rules: [{ required: false, message: '请输入密码' }] }]"></a-input>
        </a-form-item>
        <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['RealName', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-radio-group v-decorator="['Gender', { rules: [{ required: true, message: '必填' }] }]">
            <a-radio :value="1">男</a-radio>
            <a-radio :value="2">女</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- <a-form-item label="出生日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 355px"
            format="YYYY-MM-DD"
            v-decorator="['Birthday', { rules: [{ required: false , message: '必填' }] }]"
          />
        </a-form-item>

        <a-form-item label="注册日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-date-picker
            style="width: 355px"
            format="YYYY-MM-DD"
            v-decorator="['RegistTime', { rules: [{ required: false , message: '必填' }] }]"
          />
        </a-form-item> -->
        <a-form-item label="学号" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['StudentNo', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>

        <!--<a-form-item label="学校id" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['SchoolId', { rules: [{ required: true }] }]" />
        </a-form-item>-->

        <a-form-item label="学校" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select allowClear showSearch treeNodeFilterProp="title" style="width: 355px" :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }" :treeData="SchoolTreeData" @change="SchoolChange"
            placeholder="请选所属学校" treeDefaultExpandAll v-decorator="['SchoolId', { rules: [{ required: true, message: '必填' }] }]"></a-tree-select>
        </a-form-item>

        <a-form-item label="班级" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select allowClear showSearch treeNodeFilterProp="title" style="width: 355px" :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }" :treeData="ClassTreeData" placeholder="请选所属班级" treeDefaultExpandAll
            v-decorator="['ClassId', { rules: [{ required: true, message: '必填' }] }]"></a-tree-select>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      entity: {},
      title: '',
      SchoolTreeData: [],
      ClassTreeData: []
    }
  },
  methods: {
    openForm(id, title) {
      //参数赋值
      this.title = title || '编辑表单'
      this.loading = true

      //组件初始化
      this.init()

      //编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()

          this.$http.post('/Student/Exam_Student/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            this.BySchoolId(resJson.Data.SchoolId)
            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.visible = true
      this.$http.post('/School/Exam_School/GetTreeDataList').then(resJson => {
        if (resJson.Success) {
          this.SchoolTreeData = resJson.Data
        }
      })
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())

          this.loading = true
          this.$http.post('/Student/Exam_Student/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    BySchoolId(schoolId) {
      this.$http.post('/ClassManage/Exam_Class/GetTreeDataListBySchoolId', { schoolId: schoolId }).then(resJson => {
        if (resJson.Success) {
          this.ClassTreeData = resJson.Data
        }
      })
    },
    SchoolChange(value, label, extra) {
      this.BySchoolId(value)
    }
  }
}
</script>
