<template>
  <div>
    <div v-if="isShowAudioView">
      <audio v-show="UserAnswer !== ''" :src="UserAnswer" type="audio/mpeg" controls>
        您的浏览器不支持 audio 标签。
      </audio>
    </div>
    <div v-else class="AnswerAudio" v-loading="audioLoding">
      <!--  音频播放标签  -->
      <audio v-show="audioUrl !== ''" :src="audioUrl" type="audio/mpeg" controls>
        您的浏览器不支持 audio 标签。
      </audio>
      <!-- 录制按钮      -->
      <div v-if="isMobile" class="pick_box flex">
        <div v-show="audioUrl === ''" class="pick pickAudio flex flex_align_center">
          <span class="microphone">
            <i class="el-icon-microphone" v-if="recStatus === 'beforeStart'" @click="recStart"></i>
            <i class="el-icon-video-pause" v-if="recStatus === 'recording' " @click="recPause"></i>
            <i class="el-icon-video-play" v-if="recStatus === 'pause'" @click="recResume"></i>
          </span>
          <span class="microline" v-show="recStatus === 'beforeStart'"></span>
          <span ref="spanTitle" v-show="recStatus === 'recording'">录制中...</span>
          <span v-show="recStatus === 'pause'">录制暂停</span>
        </div>
        <!--  <i v-show="playStatus==='play'" class="el-icon-headset newPlay" style="color: #00aaff;margin: 0 14px;font-size: 20px;" @click="play"></i>-->
        <!--  <i v-show="playStatus==='pausePlay'" class="el-icon-video-pause" style="color: #00aaff;margin: 0 14px;font-size: 20px;" @click="suspend"></i>-->
        <!--  <i v-show="playStatus==='play2'" class="el-icon-video-play" style="color: #00aaff;margin: 0 14px;font-size: 20px;" @click="recovery "></i>-->
        <div v-if="useType != 'analysis' && !isShowAudioView" ref="audioTimer" style="margin-left: 10px;"></div>
        <div class="record_btns" v-if="useType != 'analysis' && !isShowAudioView">
          <!-- 开始录音后展示  点击后弹窗提示保存或删除再消失 -->
          <el-button
            size="small"
            v-show="recStatus !== 'beforeStart'"
            @click="recStop"
          >结束录制</el-button>
  <!--        <el-button-->
  <!--          size="small"-->
  <!--          v-show="audioUrl !== ''"-->
  <!--          @click="recStart"-->
  <!--        >重新录制</el-button>-->
          <el-button
            size="small"
            v-show="audioUrl !== ''"
            @click="recSet"
          >重新录制</el-button>
        </div>
        <!-- 操作按钮  暂停录制 恢复录制  结束录制  回放 重新录制-->
      </div>
      <div v-else>
        <el-upload
          class="upload-demo"
          ref="elUploadList"
          action="#"
          accept=".mp3,.wav"
          :show-file-list="false"
          :http-request="UploadItemVoice"
          :limit="1"
          multiple>
          <el-button class="upButton" size="small" type="primary">上传音频</el-button>
        </el-upload>
      </div>
    </div>
  </div>
</template>
<script>
import Recorder from 'js-audio-recorder'
const recorder = new Recorder({
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1 // 声道，支持 1 或 2， 默认是1
})
export default {
  name: 'AnswerAudio',
  props:{
    ItemId: '',
    Answer: '',
    SubType: '',
    useType: '',
    UserAnswer:'',
    isMobile: {
      type: Boolean,
      default: true
    }
  },
  // props: ['ItemId', 'Answer', 'SubType', 'useType','isMobile'],
  data () {
    return {
      recorder: null,
      recStatus: 'beforeStart',
      audioTimer: 0, // 录制时间
      playStatus: '',
      timer: null,
      playTime: 0,
      audioUrl: '',
      audioLoding: false
    }
  },
  watch: {
    Answer (val) {
      console.log(val)
      this.audioUrl = val
    }
  },
  created () {
    console.log( this.audioUrl)
    if (this.Answer) {
      this.audioUrl = this.Answer
    } else {
      if(this.isMobile){
        this.init()
      }
    }
  },
  computed: {
    // 音频时长
    isShowAudioView () {
      return this.UserAnswer?.length > 0
    }
  },
  mounted () {
    if (this.Answer) {
      this.$nextTick(()=>{
        if(this.$refs.audioTimer) this.$refs.audioTimer.innerText = recorder.duration.toFixed(2)
      })
    }
  },
  methods: {
    // 初始化获取麦克风权限
    init () {
      Recorder.getPermission().then(
        () => {
          this.recorder = recorder
        }, (err) => {
          this.$message.error(err)
        }
      )
    },
    // // 播放
    // play () {
    //   this.playStatus = 'pausePlay'
    //   this.recorder.play()
    // },
    // // 暂停
    // suspend () {
    //   this.playStatus = 'play2'
    //   this.recorder.pausePlay()
    // },
    // // 恢复播放
    // recovery () {
    //   this.playStatus = 'pausePlay'
    //   this.recorder.resumePlay()
    // },
    recSet () {
      this.reset()
      // this.recorder = recorder
    },
    // 开始录制
    recStart () {
      // console.log(this.recorder.start())
      const that = this
      // that.reset()
      that.recorder.start().then(
        () => {
          this.recStatus = 'recording'
          // 绑定事件-打印的是当前录音数据
          that.recorder.onprogress = function (params) {
            that.audioTimer = params.duration
            that.$refs.audioTimer.innerText = params.duration.toFixed(2)
          }
        },
        (err) => {
          that.$confirm(err + ',请更换设备或浏览器后再试！', '提示', {
            confirmButtonText: '确定',
            type: 'warning'
          }).then(() => {
            return false
          }).catch(() => {
            return false
          })
        }
      )
    },
    // 暂停录制
    recPause () {
      this.recStatus = 'pause'
      this.recorder.pause()
    },
    // 恢复录制
    recResume () {
      this.recStatus = 'recording'
      this.recorder.resume()
    },
    // 结束录制
    async  recStop () {
      this.recStatus = 'beforeStart'
      // this.playStatus = 'play'
      const blob = this.recorder.getWAVBlob()// 获取wav格式音频数据
      // 此处获取到blob对象后需要设置fileName满足当前项目上传需求，其它项目可直接传把blob作为file塞入formData
      const newbolb = new Blob([blob], { type: 'audio/wav' })
      const fileOfBlob = new File([newbolb], new Date().getTime() + '.wav')
      // const blob = this.recorder.getWAVBlob()// 获取wav格式音频数据
      this.UpAudio(fileOfBlob)
    },
    // 重置回归
    reset () {
      this.recStatus = 'beforeStart'
      this.audioTimer = 0 // 录制时间
      this.$refs.audioTimer.innerText = ' '
      // this.audioUrl = ''
      this.init()
    },
    // 上传
    UploadItemVoice(File){
      this.UpAudio(File.file)
    },
    // 上传音频
    UpAudio (blob) {
      this.audioUrl = ''
      const num = 3000
      const valNum = Math.round(this.audioTimer / 60)
      const ime = num + (valNum * 1000)
      const params = new FormData()
      params.append('UserId', localStorage.getItem('UserId'))
      params.append('PaperId', this.$route.query.paperId)
      params.append('ItemId', this.ItemId)
      params.append('SubType', this.SubType)
      params.append('TypeId', 2) // 主观题
      params.append('oriContent', '')
      params.append('file', blob)
      this.$uwonhttp.post('/Paper/Paper/SetUserDraftBook', params).then((res) => {
        if (res.data.Success) {
          const data = res.data.Data
          const that = this
          this.fileList = []
          that.audioLoding = true
          const setTime = setTimeout(function () {
            that.audioUrl = data
            that.audioLoding = false
            clearTimeout(setTime)
          }, ime)
          this.AnswerList = data.split('|')
          this.$emit('handleAudio', data)
          if(this.isMobile){
            that.recorder.destroy().then(function () {
              that.recorder = null
            })
          } else {
            // 清除上传列表
            this.$refs.elUploadList.clearFiles()
          }
        } else {
          this.$message.success('上传失败')
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
@import './subjectiveComm';
.AnswerAudio{
  display: flex;
  align-items: center;
}
.pick_box {
  width: 50%;
  height: 80px;
  align-items: center;
  .pickAudio {
    background: #fff5eb;
    border-radius: 35px;
    padding: 0 8%;
    .microphone {
      width: 50px;
      height: 50px;
      background: #ed6a39;
      border-radius: 50%;
      color: #fff;
      font-size: 26px;
      line-height: 50px;
      text-align: center;
      margin-right: 3%;
    }
    .microline {
      width: 90%;
      height: 2px;
      background: #fb9567;
    }
  }
}
.record_btns {
  /deep/.el-button{
    margin-left: 20px;
    margin-top: 0 !important;
  }
}
.upButton{
  margin-left: 20px;
  margin-top: 0 !important;
}
</style>
