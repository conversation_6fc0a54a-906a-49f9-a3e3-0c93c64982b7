<template>
  <div>
    <div   v-if="isShowUploadView">
      <video class="show_box" style="margin-right: 10px;" :src="item" v-for="(item,index) in videoList" :key="index" controls>您的浏览器不支持 video 标签。</video>
    </div>
    <div
      class="AnswerVideo flex flex_align_center"
      v-loading="loading"
      element-loading-text="正在上传"
      style="margin: 20px 0"
      v-else
    >
      <!-- 回显的列表 -->
      <div class="show_box" v-for="(video, index) in AnswerList" :key="index">
        <span class="close" @click="deleteAnswerVideo(index)" v-if="useType != 'analysis' && !isShowUploadView"
          ><i class="el-icon-circle-close"></i
        ></span>
        <video :src="video" controls>您的浏览器不支持 video 标签。</video>
      </div>
      <template v-if="useType != 'analysis' && !isShowUploadView">
        <div class="pick_box" @change="fetchFile" v-show="AnswerList.length < 3 ">
          <input type="file" accept="video/*" capture="camcorder" id="video" />
          <div class="pick pickVideo" >
            <p class="pickIcon white_color">
              <i class="el-icon-video-camera"></i>
            </p>
          </div>
          <div style="color: #999; margin-top: 5px">* 点击拍摄视频</div>
        </div>
        <br />
      </template>
    </div>
  </div>
</template>
<script>
import obsClient from '@/utils/obsClient'
const obsUrl = 'https://studentitemdraft2024.obs.cn-east-2.myhuaweicloud.com/'
export default {
  name: 'AnswerVideo',
  props: ['ItemId', 'Answer', 'SubType', 'useType','UserAnswer'],
  data() {
    return {
      AnswerList: [],
      fileList: [],
      loading: false,
    }
  },
  created() {
    if (this.Answer) {
      this.AnswerList = this.Answer.split('|')
    }
  },
  computed:{
    isShowUploadView(){
      return this.UserAnswer?.length > 0
    },
    videoList(){
      return this.UserAnswer?.split('|') || []
    }
  },
  methods: {
    async uploadVideo() {
      //  UserId:学生Id， PaperId：试卷Id ItemId:试题Id  SubType:0|1 答题|订正
      let params = new FormData()
      params.append('UserId', localStorage.getItem('UserId'))
      params.append('PaperId', this.$route.query.paperId)
      params.append('ItemId', this.ItemId)
      params.append('SubType', this.SubType)
      params.append('TypeId', 2) //主观题
      params.append('oriContent', this.AnswerList.length > 0 ? this.AnswerList.join('|') : 'noOriContent')
      params.append('fileUrl', this.fileList.map(item=>`${obsUrl}${item.name}`).join('|'))
      // this.fileList.forEach((item, index) => {
      //   params.append('file' + index,  item)
      // })
      
      const res = await this.$uwonhttp.post('/Paper/Paper/SetUserDraftBook', params)
      if (res.data.Success) {
        // 提交答案 更改答题状态
        let data = res.data.Data
        if (data !== '' && data != 'noOriContent') {
          this.AnswerList = data.split('|')
          this.$emit('handleVideo', data)
        } else {
          this.$emit('handleVideo', '')
          this.AnswerList = []
        }
        this.loading = false
        this.fileList = []
      } else {
        this.$message.error('请求错误，请联系客服！')
        this.loading = false
      }
    },
    //删除答案中图片
    deleteAnswerVideo(index) {
      this.AnswerList.splice(index, 1)
      this.uploadVideo()
    },
    async fetchFile() {
      let f = document.getElementById('video')
      console.log(f.files)
      this.loading = true
      await obsClient.putObject({
        Bucket : 'studentitemdraft2024',
        Key : f.files[0].name, 
        SourceFile : f.files[0]
      }, (err, result)=> {
        if(!err){
          this.loading = false
          console.log(result)
          this.fileList.push(f.files[0])
          f.value = ''
          this.uploadVideo()
        }
      });
      
    },
  },
}
</script>
<style lang="less" scoped>
@import './subjectiveComm';
.pick_box {
  width: 200px;
  height: 150px;
  margin-right: 15px;
}
.pickVideo {
  background: #fff5eb;
}
.show_box {
  width: 200px;
  height: 150px;
  margin-right: 25px;
  position: relative;
}
video {
  width: 100%;
  height: 100%;
  object-fit: fill;
}
</style>