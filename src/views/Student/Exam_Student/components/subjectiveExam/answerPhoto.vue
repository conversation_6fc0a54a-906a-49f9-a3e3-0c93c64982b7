<template>
  <div>
    <!-- <div v-if="isShowUploadView">
      <img :src="UserAnswer">
    </div> -->
    <div class="demo-image__preview" v-if="isShowUploadView">
      <el-image 
        v-for="(img,index) in srcList"
        :key="index"
        style="width: 100px; height: 100px;margin-right: 10px;"
        :src="img" 
        :preview-src-list="srcList">
      </el-image>
    </div>
    <div
      v-else
      class="AnswerPhoto flex flex_align_center"
      v-loading="loading"
      element-loading-text="正在上传"
      style="margin: 20px 0"
    >
      <!-- 已保存的图片列表 -->
      <div class="show_box" v-for="(img, index) in AnswerList" :key="index">
        <span
          class="close"
          @click="deleteAnswerImg(index)"
          v-if="useType != 'analysis'"
        ><i class="el-icon-circle-close"></i
        ></span>
        <el-image :src="img" style="width: 100%; height: 100%" :preview-src-list="AnswerList"></el-image>
      </div>
      <template v-if="useType != 'analysis'">
        <div class="pick_box" @change="fetchFile" v-if="AnswerList.length < 3">
          <input type="file" accept="image/*" capture="camera" id="img" />
          <div class="pick pickPhoto">
            <p class="pickIcon white_color">
              <i class="el-icon-camera"></i>
            </p>
          </div>
          <div class="font_size18 ipad_font_size20" style="color: #999; margin-top: 5px">* 点击拍照</div>
        </div>
      </template>
    </div>
  </div>

</template>
<script>
export default {
  name: 'AnswerPhoto',
  props: ['ItemId', 'Answer', 'SubType', 'useType','UserAnswer'],
  data () {
    return {
      fileList: [],
      AnswerList: [],
      loading: false,
    }
  },
  watch: {
    Answer: function (newVal, oldVal) {
      // 添加以下该判断是避免客观题与主观题融合的核心素养试题，作答时会出现空白图片问题
      const reg = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/
      if (reg.test(newVal)) {
        this.AnswerList = []
        this.AnswerList = newVal.split('|')
      } else {
        this.AnswerList = []
      }
    }
  },
  created () {
    if (this.Answer) {
      this.AnswerList = this.Answer.split('|')
    }
  },
  computed:{
    isShowUploadView(){
      return this.UserAnswer?.length > 0
    },
    srcList(){
      return this.UserAnswer.split('|') || []
    },
  },
  beforeDestroy () {},
  methods: {
    // 上传图片
    async uploadPhoto () {
      //  UserId:学生Id， PaperId：试卷Id ItemId:试题Id  SubType:0|1 答题|订正
      const params = new FormData()
      // params.append('UserId', localStorage.getItem('UserId'))
      // params.append('PaperId', this.$route.query.paperId)
      // params.append('ItemId', this.ItemId)
      // params.append('SubType', this.SubType)
      // params.append('TypeId', 2) // 主观题
      // params.append('oriContent', this.AnswerList.length > 0 ? this.AnswerList.join('|') : 'noOriContent')
      this.fileList.forEach((item, index) => {
        params.append('file' + index, item)
      })
      this.loading = true
      // const res = await this.$uwonhttp.post('/Paper/Paper/SetUserDraftBook', params)
      const res = await this.$uwonhttp.post('/file/Upload/UploadFileByForm', params)
      if (res.data.thumbUrl) {
        this.AnswerList.push(res.data.thumbUrl)
        this.$emit('handlePhoto', this.AnswerList.join('|'))
        this.fileList = []
        this.loading = false
      }
      // return
      // if (res.data.Success) {
      //   // 提交答案 更改答题状态
      //   const data = res.data.Data
      //   if (data !== '' && data != 'noOriContent') {
      //     this.AnswerList = data.split('|')
      //     this.$emit('handlePhoto', data)
      //   } else {
      //     this.$emit('handlePhoto', '')
      //     this.AnswerList = []
      //   }
      //   this.fileList = []
      //   this.loading = false
      // } else {
      //   this.$message.error('请求错误，请联系客服！')
      //   this.loading = false
      // }
    },
    // 删除答案中图片
    deleteAnswerImg (index) {
      this.AnswerList.splice(index, 1)
      this.$emit('handlePhoto', this.AnswerList.join('|'))
      // this.uploadPhoto()
    },
    // 图片转换成base64数据流
    fetchFile () {
      const f = document.getElementById('img')
      this.fileList.push(f.files[0])
      f.value = ''
      this.uploadPhoto()
    }
  }
}
</script>
<style lang="less" scoped>
@import './subjectiveComm';
.pick_box {
  width: 150px;
  height: 150px;
  margin-right: 15px;
}
.pickPhoto {
  background: #fff5eb;
}
.pickIcon {
  width: 60px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  border-radius: 50%;
  background: #ed6a39;
  font-size: 28px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
.show_box {
  width: 150px;
  height: 150px;
  margin-right: 25px;
  position: relative;
}
</style>
