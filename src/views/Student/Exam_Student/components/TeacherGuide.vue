<template>
  <div class="TeacherGuide" v-if="guideList.length > 0">
    <!-- 切换 -->
    <div class="listBar flex flex_align_center">
      <el-button
        :type="currentI == i ? 'primary' : 'info'"
        round
        v-for="i in listLen"
        :key="i"
        size="small"
        @click="currentI = i"
      >
        辅导{{ i }}</el-button
      >
    </div>
    <!-- 内容  文字+视频+图片 -->
    <div>
      <p style="color: #e5b845">辅导老师：{{ guideList[currentI - 1].TeacherName }}</p>
      <p>辅导文字：{{ guideList[currentI - 1].CruxText }}</p>
      <div>
        <img
          style="max-width: 100%"
          :src="img"
          alt=""
          v-for="(img, index) in guideList[currentI - 1].PictureUrl"
          :key="index"
        />
      </div>
      <div>
        <video-play
          :url="guideList[currentI - 1].VideoUrl"
          ref="VideoPlay"
          width="240px"
          height="180px"
          style="text-align: left"
        ></video-play>
      </div>
    </div>
  </div>
</template>
<script>
import VideoPlay from "@/components/SingleVideoCoach/VideoPlay.vue"
export default {
  name: "TeacherGuide",
  components: {
    VideoPlay,
  },
  data() {
    return {
      currentI: 1,
      guideList: [],
    }
  },
  props: {
    ItemId: {
      type: String,
      default: "",
    },
  },
  computed: {
    listLen() {
      return this.guideList.length
    },
  },
  created() {
    this.GetStudentCoachItemInfo()
  },
  methods: {
    //获取指导列表
    async GetStudentCoachItemInfo() {
      const res = await this.$uwonhttp.post(`/Report/StudentReport/GetStudentCoachItemInfo`, {
        ItemId: this.ItemId,
        UserId: localStorage.getItem("UserId"),
      })
      if (!res.data.Success) return this.$message.error(res.data.Msg)
      this.guideList = res.data.Data
    },
  },
}
</script>
<style lang="less" scoped>
/deep/.el-button--primary {
  background-color: #61bb96;
  border-color: #61bb96;
}
</style>