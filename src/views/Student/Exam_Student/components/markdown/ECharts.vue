<script>
import { defineComponent } from 'vue'
import * as echarts from 'echarts'

export default defineComponent({
  name: 'ECharts',
  props: {
    options: {
      type: [String, Object],
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: '加载中...'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  data() {
    return {
      chart: null,
      resizeObserver: null,
      isLoading: false,
      error: null,
    }
  },
  emits: ['click', 'error', 'ready'],
  watch: {
    options: {
      immediate: true,
      deep: true,
      handler(newOptions) {
        this.updateChart(newOptions)
      }
    },
    loading: {
      immediate: true,
      handler(isLoading) {
        this.isLoading = isLoading
        if (this.chart) {
          if (isLoading) {
            this.chart.showLoading('default', {
              text: this.loadingText,
              color: '#007bff',
              textColor: '#6c757d',
              maskColor: 'rgba(248, 249, 250, 0.8)',
              zlevel: 0
            })
          } else {
            this.chart.hideLoading()
          }
        }
      }
    }
  },
  methods: {
    /**
     * 检查 JSON 字符串是否可能是完整的
     * @param {String} jsonStr - JSON 字符串
     * @returns {Object} { isComplete: boolean, isValid: boolean, error: string }
     */
    checkJsonStatus(jsonStr) {
      if (!jsonStr || typeof jsonStr !== 'string') {
        return { isComplete: false, isValid: false, error: '空数据' }
      }

      const trimmed = jsonStr.trim()
      if (!trimmed) {
        return { isComplete: false, isValid: false, error: '空数据' }
      }

      // 检查是否以 { 开头
      if (!trimmed.startsWith('{')) {
        return { isComplete: false, isValid: false, error: '不是有效的 JSON 对象' }
      }

      // 检查是否以 } 结尾
      if (!trimmed.endsWith('}')) {
        return { isComplete: false, isValid: true, error: '数据加载中...' }
      }

      // 简单的括号匹配检查
      let braceCount = 0
      let inString = false
      let escaped = false

      for (let i = 0; i < trimmed.length; i++) {
        const char = trimmed[i]

        if (escaped) {
          escaped = false
          continue
        }

        if (char === '\\') {
          escaped = true
          continue
        }

        if (char === '"' && !escaped) {
          inString = !inString
          continue
        }

        if (!inString) {
          if (char === '{') braceCount++
          else if (char === '}') braceCount--
        }
      }

      const isComplete = braceCount === 0
      return {
        isComplete,
        isValid: true,
        error: isComplete ? null : '数据加载中...'
      }
    },

    /**
     * 解析配置选项
     * @param {String|Object} options - 配置选项
     * @returns {Object|null} 解析后的配置对象，如果解析失败返回 null
     */
    parseOptions(options) {
      if (typeof options === 'string') {
        try {
          let decoded = decodeURIComponent(options)

          // 处理 markdown 代码块格式
          if (decoded.includes('```echarts_json')) {
            // 提取代码块中的 JSON 内容
            const match = decoded.match(/```echarts_json\s*\n([\s\S]*?)\n```/)
            if (match && match[1]) {
              decoded = match[1].trim()
            }
          }

          // 尝试 JSON.parse 解析
          try {
            return JSON.parse(decoded)
          } catch (jsonError) {
            // JSON.parse 失败，可能包含 JavaScript 表达式，尝试使用 Function 构造函数
            console.log('JSON.parse failed, trying JavaScript evaluation:', jsonError.message)
            return this.parseJavaScriptObject(decoded)
          }
        } catch (error) {
          console.log('Error parsing options:', error)
          // 不抛出错误，返回 null 让调用者处理
          return null
        }
      }
      return options || {}
    },

    /**
     * 解析包含 JavaScript 表达式的对象字符串
     * @param {String} jsObjectStr - JavaScript 对象字符串
     * @returns {Object|null} 解析后的对象，如果解析失败返回 null
     */
    parseJavaScriptObject(jsObjectStr) {
      try {
        // 创建一个安全的执行环境
        const safeEval = new Function('echarts', `
          "use strict";
          return ${jsObjectStr};
        `)

        // 传入 echarts 对象并执行
        const result = safeEval(echarts)
        return result
      } catch (error) {
        console.warn('JavaScript 对象解析失败:', error)
        return null
      }
    },

    /**
     * 验证 ECharts 配置是否有效
     * @param {Object} options - ECharts 配置对象
     * @returns {Boolean} 是否有效
     */
    validateOptions(options) {
      if (!options || typeof options !== 'object') {
        return false
      }

      // 对于流式数据，放宽验证条件
      // 只要是对象就认为可能是有效的配置
      return true
    },

    /**
     * 更新图表
     * @param {String|Object} newOptions - 新的配置选项
     */
    async updateChart(newOptions) {

      if (!newOptions) {
        // 没有数据，显示加载状态
        this.isLoading = true
        this.error = null
        return
      }

      // 如果是字符串，检查 JSON 状态
      if (typeof newOptions === 'string') {
        const jsonStatus = this.checkJsonStatus(decodeURIComponent(newOptions))

        if (!jsonStatus.isValid) {
          // 无效数据，显示错误
          this.error = jsonStatus.error
          this.isLoading = false
          this.$emit('error', new Error(jsonStatus.error))
          return
        }

        if (!jsonStatus.isComplete) {
          // 数据不完整，显示加载状态
          this.isLoading = true
          this.error = null
          return
        }
      }

      // 尝试解析配置
      const parsedOptions = this.parseOptions(newOptions)
     
      if (!parsedOptions) {
        // 解析失败，可能是数据不完整
        this.isLoading = true
        this.error = null
        return
      }

      if (!this.validateOptions(parsedOptions)) {
        // 配置无效，但不一定是错误，可能还在加载中
        this.isLoading = true
        this.error = null
        return
      }

      // 配置有效，更新图表
      try {
        this.error = null

        if (this.chart) {
          this.chart.setOption(parsedOptions, true)
          // 图表更新成功后立即清除 loading 状态
          this.isLoading = false
        } else {
          // 如果图表还没初始化，等待 mounted 后再设置
          this.pendingOptions = parsedOptions
          // 暂时不清除 loading，等待图表初始化完成
        }
      } catch (error) {
        this.error = error.message
        this.isLoading = false
        this.$emit('error', error)
      }
    },

    /**
     * 初始化图表
     */
    initChart() {
      if (!this.$refs.chartContainer || this.chart) return

      try {
        this.chart = echarts.init(this.$refs.chartContainer)
        
        // 设置点击事件
        this.chart.on('click', (params) => {
          this.$emit('click', params)
        })

        // 如果有待处理的配置，立即应用
        if (this.pendingOptions) {
          this.chart.setOption(this.pendingOptions)
          this.pendingOptions = null
          // 确保清除 loading 状态
          this.isLoading = false
        }

        // 设置加载状态
        if (this.loading) {
          this.chart.showLoading('default', {
            text: this.loadingText,
            color: '#007bff',
            textColor: '#6c757d',
            maskColor: 'rgba(248, 249, 250, 0.8)',
            zlevel: 0
          })
        }

        this.$emit('ready', this.chart)
      } catch (error) {
        console.error('ECharts 初始化失败:', error)
        this.error = error.message
        this.$emit('error', error)
      }
    },

    /**
     * 设置响应式监听
     */
    setupResize() {
      if (!this.$refs.chartContainer || !this.chart) return

      // 使用 ResizeObserver 监听容器尺寸变化
      this.resizeObserver = new ResizeObserver(() => {
        if (this.chart) {
          this.chart.resize()
        }
      })
      this.resizeObserver.observe(this.$refs.chartContainer)

      // 监听窗口大小变化
      this.windowResizeHandler = () => {
        if (this.chart) {
          this.chart.resize()
        }
      }
      window.addEventListener('resize', this.windowResizeHandler)
    },

    /**
     * 清理资源
     */
    cleanup() {
      // 清理 ResizeObserver
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }

      // 清理窗口事件监听
      if (this.windowResizeHandler) {
        window.removeEventListener('resize', this.windowResizeHandler)
        this.windowResizeHandler = null
      }

      // 销毁图表实例
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      this.setupResize()
    })
  },
  beforeUnmount() {
    this.cleanup()
  }
})
</script>

<template>
  <div
    class="echarts-wrapper"
    :style="{ width, height }"
    v-loading="isLoading || loading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(248, 249, 250, 0.8)"
  >
    <!-- 错误状态 -->
    <div
      v-if="error"
      class="echarts-error"
    >
      <div class="error-icon">⚠️</div>
      <div class="error-message">{{ error }}</div>
    </div>

    <!-- 图表容器 -->
    <div
      v-else
      ref="chartContainer"
      class="echarts-container"
      :style="{ width: '100%', height: '100%' }"
    />
  </div>
</template>

<style lang="less" scoped>
.echarts-wrapper {
  position: relative;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 400px; /* 确保有最小高度 */
}

.echarts-container {
  width: 100%;
  height: 100%;
  min-height: 400px; /* 确保容器有最小高度 */
}



.echarts-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  text-align: center;
  z-index: 1;

  .error-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .error-message {
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
  }
}


</style>
