<script>
import { defineComponent } from 'vue'
import screenfull from 'screenfull'

export default defineComponent({
  name: 'Mermaid',
  props: {
    code: {
      type: String,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: '图表加载中...'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: 'auto'
    },
    minHeight: {
      type: String,
      default: '300px'
    },
    maxHeight: {
      type: String,
      default: null  // 不限制最大高度
    },
    // 流式数据防抖延迟时间（毫秒）
    debounceDelay: {
      type: Number,
      default: 1200
    }
  },
  data() {
    return {
      mermaid: null,
      isLoading: false,
      error: null,
      chartId: null,
      resizeObserver: null,
      // 自适应高度相关
      autoHeight: 'auto',
      // 防抖和流式数据处理相关
      debounceTimer: null,
      lastValidCode: null,
      renderAttempts: 0,
      maxRenderAttempts: 3,
      // 流式数据检测
      lastCodeLength: 0,
      codeGrowthHistory: [],
      isStreamingDetected: false,
      // 交互控制相关
      isFullScreen: false,
      currentScale: 1,
      minScale: 0.1,
      maxScale: 5,
      // 拖拽相关
      isDragging: false,
      dragStartX: 0,
      dragStartY: 0,
      translateX: 0,
      translateY: 0,
      lastTranslateX: 0,
      lastTranslateY: 0,
      // 触摸相关
      isTouching: false,
      touchStartTime: 0,
      touchMoveThreshold: 15, // 触摸移动阈值，提高阈值减少误触
      initialTouchDistance: 0,
      lastTouchDistance: 0,
      touchStartX: 0,
      touchStartY: 0,
      touchMoveX: 0,
      touchMoveY: 0,
      touchCount: 0,
      preventScroll: false
    }
  },
  emits: ['click', 'error', 'ready'],
  watch: {
    code: {
      immediate: true,
      handler(newCode) {
        this.debouncedUpdateChart(newCode)
      }
    },
    loading: {
      immediate: true,
      handler(isLoading) {
        this.isLoading = isLoading
      }
    }
  },
  computed: {
    containerStyle() {
      const style = {
        width: this.width,
        minHeight: this.minHeight
      }

      if (this.height === 'auto') {
        if (this.autoHeight !== 'auto') {
          style.height = this.autoHeight
        }
        // 只有当明确设置了 maxHeight 且不为 null 时才应用最大高度限制
        if (this.maxHeight && this.maxHeight !== 'null' && this.maxHeight !== 'none') {
          style.maxHeight = this.maxHeight
        }
      } else {
        style.height = this.height
      }

      return style
    }
  },
  methods: {
    /**
     * 缩放功能
     * @param {number} scale - 缩放比例
     */
    zoom(scale) {
      const newScale = Math.max(this.minScale, Math.min(this.maxScale, this.currentScale * scale))
      this.currentScale = newScale
      this.updateTransform()
    },

    /**
     * 重置缩放和位置
     */
    resetTransform() {
      this.currentScale = 1
      this.translateX = 0
      this.translateY = 0
      this.lastTranslateX = 0
      this.lastTranslateY = 0
      this.updateTransform()
    },

    /**
     * 更新SVG变换
     */
    updateTransform() {
      const svgElement = this.$refs.chartContainer?.querySelector('svg')
      if (svgElement) {
        const transform = `translate(${this.translateX}px, ${this.translateY}px) scale(${this.currentScale})`
        svgElement.style.transform = transform
        svgElement.style.transformOrigin = 'center center'
        svgElement.style.transition = this.isDragging ? 'none' : 'transform 0.2s ease'
      }
    },

    /**
     * 全屏切换
     */
    toggleFullscreen() {
      if (screenfull.isFullscreen) {
        screenfull.exit()
        return
      }
      this.$refs.mermaidWrapper.requestFullscreen()
    },

    /**
     * 全屏状态变化处理
     */
    handleFullscreenChange(e) {
      if (!(e.target === this.$refs.mermaidWrapper)) {
        return
      }
      this.isFullScreen = screenfull.isFullscreen
    },

    /**
     * 开始拖拽 - 支持鼠标和触摸操作
     */
    startDrag(event) {
      if (event.target.closest('.control-buttons')) {
        return // 不在控制按钮上拖拽
      }

      // 处理鼠标事件
      if (event.type === 'mousedown') {
        event.preventDefault()
        this.isDragging = true

        this.dragStartX = event.clientX - this.translateX
        this.dragStartY = event.clientY - this.translateY

        document.addEventListener('mousemove', this.handleDrag)
        document.addEventListener('mouseup', this.stopDrag)
      }
    },

    /**
     * 处理拖拽 - 支持鼠标操作
     */
    handleDrag(event) {
      if (!this.isDragging) return

      // 只处理鼠标移动事件
      if (event.type !== 'mousemove') return

      event.preventDefault()

      this.translateX = event.clientX - this.dragStartX
      this.translateY = event.clientY - this.dragStartY

      this.updateTransform()
    },

    /**
     * 停止拖拽 - 支持鼠标操作
     */
    stopDrag() {
      this.isDragging = false
      this.lastTranslateX = this.translateX
      this.lastTranslateY = this.translateY

      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    },

    /**
     * 处理鼠标滚轮缩放 - 只在按住Ctrl键时生效
     */
    handleWheel(event) {
      // 只有在按住Ctrl键（或Mac的Cmd键）时才进行缩放
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        const delta = event.deltaY > 0 ? 0.9 : 1.1
        this.zoom(delta)
      }
      // 否则让事件正常冒泡，允许页面滚动
    },

    /**
     * 触摸开始事件处理
     * 支持单指拖拽和双指缩放，优先保证页面滚动不受影响
     */
    handleTouchStart(event) {
      if (event.target.closest('.control-buttons')) {
        return // 不在控制按钮上处理触摸
      }

      this.touchCount = event.touches.length
      this.touchStartTime = Date.now()
      this.isTouching = true
      this.preventScroll = false // 默认不阻止滚动

      if (this.touchCount === 1) {
        // 单指触摸 - 不立即阻止默认行为，等待用户意图明确
        const touch = event.touches[0]
        this.touchStartX = touch.clientX
        this.touchStartY = touch.clientY
        this.touchMoveX = touch.clientX
        this.touchMoveY = touch.clientY
        // 不在这里调用 preventDefault()，让滚动优先
      } else if (this.touchCount === 2) {
        // 双指触摸 - 明确的缩放意图，可以阻止默认行为
        const touch1 = event.touches[0]
        const touch2 = event.touches[1]
        this.initialTouchDistance = this.getTouchDistance(touch1, touch2)
        this.lastTouchDistance = this.initialTouchDistance

        // 双指操作明确是缩放，可以阻止默认行为
        event.preventDefault()
        this.preventScroll = true
      }
    },

    /**
     * 触摸移动事件处理
     * 智能区分拖拽和滚动操作，优先保证页面滚动不受影响
     */
    handleTouchMove(event) {
      if (!this.isTouching) return

      const currentTime = Date.now()
      const touchDuration = currentTime - this.touchStartTime

      if (this.touchCount === 1 && event.touches.length === 1) {
        // 单指操作 - 需要仔细区分拖拽和滚动
        const touch = event.touches[0]
        const deltaX = touch.clientX - this.touchMoveX
        const deltaY = touch.clientY - this.touchMoveY

        // 计算总移动距离和方向
        const totalMoveX = Math.abs(touch.clientX - this.touchStartX)
        const totalMoveY = Math.abs(touch.clientY - this.touchStartY)
        const totalMove = Math.sqrt(totalMoveX * totalMoveX + totalMoveY * totalMoveY)

        // 只有在非常明确的拖拽意图下才阻止滚动
        if (totalMove > this.touchMoveThreshold) {
          // 计算移动方向比例
          const verticalRatio = totalMoveY / Math.max(totalMoveX, 1) // 避免除零
          const horizontalRatio = totalMoveX / Math.max(totalMoveY, 1)

          // 判断是否为明显的垂直滚动（优先级最高）
          const isVerticalScroll = verticalRatio > 1.8 && totalMoveY > 20

          // 判断是否为明确的水平拖拽（需要更严格的条件）
          const isHorizontalDrag = horizontalRatio > 2.5 && totalMoveX > 35 && touchDuration > 200

          // 判断是否为对角线拖拽（需要非常明确的意图）
          const isDiagonalDrag = touchDuration > 500 && totalMove > 60 &&
                                Math.abs(horizontalRatio - verticalRatio) < 0.5

          // 如果是垂直滚动，立即停止处理，让滚动正常进行
          if (isVerticalScroll) {
            this.preventScroll = false
            return
          }

          // 只有在非常明确的拖拽意图下才阻止默认行为
          if ((isHorizontalDrag || isDiagonalDrag) && !isVerticalScroll) {
            // 确认为拖拽操作
            if (!this.preventScroll) {
              this.preventScroll = true
            }
            event.preventDefault()

            this.translateX += deltaX
            this.translateY += deltaY
            this.updateTransform()
          }
          // 其他情况（不明确的移动）不处理，优先保证滚动
        }

        this.touchMoveX = touch.clientX
        this.touchMoveY = touch.clientY

      } else if (this.touchCount === 2 && event.touches.length === 2) {
        // 双指缩放 - 明确的缩放意图，可以阻止默认行为
        event.preventDefault()
        this.preventScroll = true

        const touch1 = event.touches[0]
        const touch2 = event.touches[1]
        const currentDistance = this.getTouchDistance(touch1, touch2)

        if (this.lastTouchDistance > 0) {
          const scale = currentDistance / this.lastTouchDistance
          // 限制缩放速度，避免过于敏感
          const limitedScale = Math.max(0.98, Math.min(1.02, scale))
          this.zoom(limitedScale)
        }

        this.lastTouchDistance = currentDistance
      }
    },

    /**
     * 触摸结束事件处理
     */
    handleTouchEnd() {
      if (!this.isTouching) return

      const touchDuration = Date.now() - this.touchStartTime

      // 如果是短时间的轻触且没有阻止滚动，可能是点击或滚动
      if (touchDuration < 200 && !this.preventScroll) {
        // 允许点击事件和滚动正常处理
        this.resetTouchState()
        return
      }

      // 更新最终位置（只有在确实进行了拖拽操作时）
      if (this.preventScroll) {
        this.lastTranslateX = this.translateX
        this.lastTranslateY = this.translateY
      }

      // 重置触摸状态
      this.resetTouchState()
    },

    /**
     * 重置触摸状态
     */
    resetTouchState() {
      this.isTouching = false
      this.touchCount = 0
      this.preventScroll = false
      this.initialTouchDistance = 0
      this.lastTouchDistance = 0
      this.touchStartX = 0
      this.touchStartY = 0
      this.touchMoveX = 0
      this.touchMoveY = 0
      this.touchStartTime = 0
    },

    /**
     * 计算两个触摸点之间的距离
     */
    getTouchDistance(touch1, touch2) {
      const dx = touch1.clientX - touch2.clientX
      const dy = touch1.clientY - touch2.clientY
      return Math.sqrt(dx * dx + dy * dy)
    },

    /**
     * 按需加载Mermaid库
     */
    async loadMermaid() {
      if (this.mermaid) {
        return this.mermaid
      }

      try {
        // 动态导入Mermaid
        const mermaidModule = await import('mermaid')
        this.mermaid = mermaidModule.default || mermaidModule

        // 初始化Mermaid配置 - 优化的v8.14.0版本配置
        const v8Config = {
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          // 关键：禁用严格模式，减少语法错误
          suppressErrorRendering: false,
          // v8版本的flowchart配置
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'linear',
            padding: 15
          },
          // v8版本的gantt配置
          gantt: {
            useMaxWidth: true,
            fontSize: 12,
            sectionFontSize: 16,
            numberSectionStyles: 4,
            axisFormat: '%m-%d'
          },
          // v8版本的sequence配置
          sequence: {
            useMaxWidth: true,
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35
          },
          // v8版本特定配置
          logLevel: 1, // 1=error, 2=warn, 3=info, 4=debug
          arrowMarkerAbsolute: false,
          // 确保兼容性的额外配置
          themeCSS: '',
          maxTextSize: 50000,
          // 错误处理配置
          errorLevel: 'fatal',
          // 添加解析器配置
          parseError: function(err) {
            console.error('Mermaid parse error:', err)
          }
        }

        this.mermaid.initialize(v8Config)

        return this.mermaid
      } catch (error) {
        console.error('Failed to load Mermaid:', error)
        throw new Error('Mermaid库加载失败')
      }
    },

    /**
     * 生成唯一的图表ID
     */
    generateChartId() {
      return `mermaid-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
    },

    /**
     * 验证Mermaid代码是否有效 - 流式数据友好的验证
     */
    validateCode(code) {
      if (!code || typeof code !== 'string') {
        return { isValid: false, error: '代码为空' }
      }

      const trimmed = code.trim()
      if (!trimmed) {
        return { isValid: false, error: '代码为空' }
      }

      // 检查是否使用了 v8.14.0 不支持的图表类型
      if (trimmed.startsWith('classDiagram')) {
        return {
          isValid: false,
          error: `类图 (classDiagram) 不支持：\n\nMermaid v8.14.0 不支持类图功能。\n\n解决方案：\n1. 升级到 Mermaid v10+ 版本\n2. 使用流程图 (graph) 替代\n3. 使用时序图 (sequenceDiagram) 表示交互\n\n支持的图表类型：\n• graph (流程图)\n• sequenceDiagram (时序图)\n• gantt (甘特图)\n• stateDiagram (状态图)\n• pie (饼图)\n• journey (用户旅程图)`
        }
      }

      // 检查其他 v8 不支持的图表类型
      const unsupportedTypes = ['erDiagram', 'gitgraph', 'requirement', 'c4Context']
      for (const type of unsupportedTypes) {
        if (trimmed.startsWith(type)) {
          return {
            isValid: false,
            error: `图表类型 "${type}" 不支持：\n\nMermaid v8.14.0 不支持此图表类型。\n\n请使用支持的图表类型：\n• graph (流程图)\n• sequenceDiagram (时序图)\n• gantt (甘特图)\n• stateDiagram (状态图)\n• pie (饼图)\n• journey (用户旅程图)`
          }
        }
      }

      // 默认流式模式：对不完整的代码更加宽容
      if (!this.isCompleteCode(trimmed)) {
        // 对于流式数据，即使代码看起来不完整，也认为是"有效的"
        // 这样可以避免在数据传输过程中显示错误
        return { isValid: true, error: null, isIncomplete: true }
      }

      // 非常宽松的验证：只检查基本结构，让Mermaid处理具体语法
      return { isValid: true, error: null, isIncomplete: false }
    },

    /**
     * 解析代码内容 - 简化解析
     */
    parseCode(code) {
      if (typeof code === 'string') {
        try {
          return decodeURIComponent(code)
        } catch (error) {
          console.warn('Failed to decode code:', error)
          return code
        }
      }
      return code || ''
    },

    /**
     * 检测是否为流式数据
     */
    detectStreamingData(newCode) {
      const currentLength = newCode ? newCode.length : 0

      // 记录代码长度变化历史
      this.codeGrowthHistory.push({
        length: currentLength,
        timestamp: Date.now()
      })

      // 只保留最近5次的历史记录
      if (this.codeGrowthHistory.length > 5) {
        this.codeGrowthHistory.shift()
      }

      // 如果代码长度在持续增长，很可能是流式数据
      if (this.codeGrowthHistory.length >= 3) {
        const isGrowing = this.codeGrowthHistory.every((record, index) => {
          if (index === 0) return true
          return record.length >= this.codeGrowthHistory[index - 1].length
        })

        // 检查增长频率（短时间内多次更新）
        const timeSpan = this.codeGrowthHistory[this.codeGrowthHistory.length - 1].timestamp -
                        this.codeGrowthHistory[0].timestamp
        const isFrequentUpdates = timeSpan < 2000 && this.codeGrowthHistory.length >= 3

        this.isStreamingDetected = isGrowing && isFrequentUpdates
      }

      this.lastCodeLength = currentLength
      return this.isStreamingDetected
    },

    /**
     * 防抖更新图表 - 处理流式数据
     */
    debouncedUpdateChart(newCode) {
      // 检测流式数据
      const isStreaming = this.detectStreamingData(newCode)

      // 清除之前的防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
        this.debounceTimer = null
      }

      // 如果代码为空，立即显示加载状态
      if (!newCode || !newCode.trim()) {
        this.isLoading = true
        this.error = null
        return
      }

      // 检查是否是完整的 Mermaid 代码
      const isComplete = this.isCompleteCode(newCode)

      // 如果检测到流式数据且代码不完整，使用更长的防抖延迟
      if (isStreaming && !isComplete) {
        console.log('Streaming data detected, using extended debounce')
        this.isLoading = true
        this.error = null
        this.debounceTimer = setTimeout(() => {
          this.updateChart(newCode)
        }, this.debounceDelay * 2) // 流式数据使用更长的延迟
        return
      }

      // 如果有上一个有效代码且当前代码不完整，保持当前状态并设置较长的防抖
      if (!isComplete && this.lastValidCode) {
        console.log('Code incomplete, extending debounce delay')
        this.isLoading = true
        this.error = null
        this.debounceTimer = setTimeout(() => {
          this.updateChart(newCode)
        }, this.debounceDelay * 1.5) // 延长防抖时间
        return
      }

      if (isComplete) {
        // 如果代码看起来完整，稍微延迟一下再渲染（避免过于频繁）
        this.debounceTimer = setTimeout(() => {
          this.updateChart(newCode)
        }, isStreaming ? 500 : 200) // 流式数据即使完整也稍微延迟一下
      } else {
        // 如果代码不完整，使用更长的防抖延迟
        this.isLoading = true
        this.error = null
        this.debounceTimer = setTimeout(() => {
          this.updateChart(newCode)
        }, this.debounceDelay)
      }
    },

    /**
     * 检查代码是否完整
     */
    isCompleteCode(code) {
      if (!code || typeof code !== 'string') {
        return false
      }

      const trimmedCode = code.trim()

      // 更严格的完整性检查，减少误判
      const checks = [
        // 1. 检查是否有图表类型声明 (只检查 v8.14.0 支持的类型)
        /^(graph|flowchart|sequenceDiagram|stateDiagram|gantt|pie|journey)/m.test(trimmedCode),

        // 2. 检查括号是否匹配（简单检查）
        this.areBracketsBalanced(trimmedCode),

        // 3. 检查是否有基本的节点或连接定义
        /[A-Za-z0-9_]+(\[.*?\]|\(.*?\)|\{.*?\}|-->|---)/m.test(trimmedCode),

        // 4. 检查代码长度是否合理（太短可能不完整）
        trimmedCode.length > 20,

        // 5. 检查是否以完整的语句结束（不是在单词中间截断）
        this.endsWithCompleteStatement(trimmedCode),

        // 6. 检查是否包含常见的流式数据不完整标志
        !this.hasIncompleteMarkers(trimmedCode)
      ]

      // 需要满足更多条件才认为是完整的（至少5个）
      const passedChecks = checks.filter(Boolean).length
      return passedChecks >= 5
    },

    /**
     * 检查代码是否以完整语句结束
     */
    endsWithCompleteStatement(code) {
      const trimmed = code.trim()
      if (!trimmed) return false

      // 检查最后一行是否看起来完整
      const lines = trimmed.split('\n')
      const lastLine = lines[lines.length - 1].trim()

      // 如果最后一行为空，检查倒数第二行
      const effectiveLastLine = lastLine || (lines.length > 1 ? lines[lines.length - 2].trim() : '')

      if (!effectiveLastLine) return false

      // 完整语句的特征 (只检查 v8.14.0 支持的类型)
      const completePatterns = [
        /^(graph|flowchart|sequenceDiagram|stateDiagram|gantt|pie|journey)/,  // 图表类型声明
        /[A-Za-z0-9_]+(\[.*?\]|\(.*?\)|\{.*?\})$/,  // 完整的节点定义
        /[A-Za-z0-9_]+\s*(-->|---)\s*[A-Za-z0-9_]+/,  // 完整的连接
        /^end$/,  // subgraph 结束
        /^[A-Za-z0-9_]+\s*(-->|---)\s*[A-Za-z0-9_]+(\[.*?\]|\(.*?\)|\{.*?\})$/  // 连接到节点
      ]

      return completePatterns.some(pattern => pattern.test(effectiveLastLine))
    },

    /**
     * 检查是否包含不完整的标志
     */
    hasIncompleteMarkers(code) {
      const incompleteMarkers = [
        /\[\s*$/,  // 未闭合的方括号
        /\(\s*$/,  // 未闭合的圆括号
        /\{\s*$/,  // 未闭合的花括号
        /-->\s*$/,  // 未完成的箭头
        /---\s*$/,  // 未完成的连线
        /[A-Za-z0-9_]+\s*$/,  // 可能未完成的节点名
        /^[^a-zA-Z]*$/  // 只包含非字母字符（可能是截断的开始）
      ]

      return incompleteMarkers.some(pattern => pattern.test(code.trim()))
    },

    /**
     * 简单的括号平衡检查
     */
    areBracketsBalanced(code) {
      const brackets = { '[': ']', '(': ')', '{': '}' }
      const stack = []

      for (const char of code) {
        if (brackets[char]) {
          stack.push(brackets[char])
        } else if (Object.values(brackets).includes(char)) {
          if (stack.pop() !== char) {
            return false
          }
        }
      }

      return stack.length === 0
    },

    /**
     * 更新图表
     */
    async updateChart(newCode) {
      if (!newCode) {
        this.isLoading = true
        this.error = null
        return
      }

      // 解析代码
      const parsedCode = this.parseCode(newCode)

      // 验证代码
      const validation = this.validateCode(parsedCode)
      if (!validation.isValid) {
        this.error = validation.error
        this.isLoading = false
        this.$emit('error', new Error(validation.error))
        return
      }

      // 如果代码被标记为不完整，且我们有上一个有效的代码，继续显示上一个
      if (validation.isIncomplete && this.lastValidCode) {
        console.log('Code appears incomplete, keeping last valid render')
        this.isLoading = true  // 保持加载状态，表示还在接收数据
        return
      }

      try {
        this.error = null
        this.isLoading = true

        // 按需加载Mermaid
        await this.loadMermaid()

        // 如果容器还没准备好，等待mounted
        if (!this.$refs.chartContainer) {
          this.pendingCode = parsedCode
          return
        }

        await this.renderChart(parsedCode)

        // 渲染成功，保存为最后一个有效代码
        this.lastValidCode = parsedCode
        this.renderAttempts = 0

      } catch (error) {
        console.error('Mermaid render error:', error)

        // 增加渲染尝试次数
        this.renderAttempts++

        // 如果代码不完整，不显示错误（默认流式数据模式）
        if (validation.isIncomplete) {
          console.log(`Render failed for incomplete code, attempt ${this.renderAttempts}/${this.maxRenderAttempts}`)

          // 如果有最后一个有效的代码，继续显示它，否则保持加载状态
          if (this.lastValidCode) {
            this.isLoading = true  // 保持加载状态
            return
          } else {
            this.isLoading = true
            this.error = null
            return
          }
        }

        // 如果尝试次数未超限且代码看起来不完整，继续等待
        if (this.renderAttempts < this.maxRenderAttempts && !this.isCompleteCode(parsedCode)) {
          console.log(`Render attempt ${this.renderAttempts}/${this.maxRenderAttempts} failed, waiting for more data...`)
          this.isLoading = true
          this.error = null
          return
        }

        // 如果有最后一个有效的代码，尝试回退到它
        if (this.lastValidCode && this.lastValidCode !== parsedCode) {
          console.log('Falling back to last valid code')
          try {
            await this.renderChart(this.lastValidCode)
            // 回退成功，但保持加载状态，表示还在等待新数据
            this.isLoading = true
            return
          } catch (fallbackError) {
            console.error('Fallback render also failed:', fallbackError)
          }
        }

        // 只有在确实无法恢复时才显示错误（默认流式数据模式）
        if (this.renderAttempts >= this.maxRenderAttempts) {
          this.error = error.message || '图表渲染失败'
          this.isLoading = false
          this.$emit('error', error)
        } else {
          // 默认流式模式下保持加载状态
          this.isLoading = true
          this.error = null
        }
      }
    },

    /**
     * 渲染图表 - 优化版本，减少闪烁和错误
     */
    async renderChart(code) {
      if (!this.mermaid || !this.$refs.chartContainer) {
        return
      }

      let processedCode = null // 在外部定义，以便在catch块中访问

      try {
        // 生成新的图表ID
        this.chartId = this.generateChartId()

        // 预处理代码，确保兼容v8语法
        processedCode = this.preprocessCode(code)

        // 移除严格的语法验证，让Mermaid自己处理
        // 这样可以减少误报和不必要的错误

        console.log('Processing mermaid code for v8.14.0:', processedCode)

        // 使用最稳定的渲染方法
        await this.stableRender(processedCode)

        // 添加点击事件监听
        this.addEventListeners()

        // 更新自适应高度
        this.$nextTick(() => {
          this.updateAutoHeight()
        })

        this.isLoading = false
        this.$emit('ready', this.chartId)

      } catch (error) {
        console.error('Mermaid render error:', error)
        console.error('Failed code:', processedCode || code)

        // 提供更详细的错误信息和解决建议
        const errorMessage = error.message || error.toString()
        let userFriendlyMessage = '图表渲染失败'

        if (errorMessage.includes('Syntax error in graph')) {
          userFriendlyMessage = `图表语法错误：代码中包含不支持的语法。\n常见问题：\n1. 节点标签中包含换行符\n2. 使用了不支持的字符\n3. 缩进不正确\n\n原始错误：${errorMessage}`
        } else if (errorMessage.includes('Parse error') && errorMessage.includes('class')) {
          userFriendlyMessage = `Mermaid v8不支持class语法：\n当前版本(8.14.0)不支持class、classDef、style等高级语法。\n\n建议：\n1. 移除所有class定义\n2. 使用基础的节点和连接语法\n3. 避免使用样式定义\n\n如需样式，请升级到更新版本的Mermaid`
        } else if (errorMessage.includes('Parse error')) {
          userFriendlyMessage = `代码解析错误：${errorMessage}\n\n建议：\n1. 检查节点标签是否包含特殊字符\n2. 确保所有括号正确匹配\n3. 检查连接符号是否正确\n4. 移除v8不支持的语法(class, style等)`
        } else if (errorMessage.includes('Expecting')) {
          userFriendlyMessage = `语法期望错误：${errorMessage}\n\n这通常是由于：\n1. 标签中包含不支持的换行符\n2. 特殊字符使用不当\n3. 语法结构不完整\n4. 使用了v8不支持的关键字`
        } else {
          userFriendlyMessage = `渲染失败: ${errorMessage}`
        }

        this.error = userFriendlyMessage
        this.isLoading = false
        this.$emit('error', new Error(userFriendlyMessage))
      }
    },



    /**
     * 稳定的渲染方法 - 减少闪烁
     */
    async stableRender(code) {
      try {
        // 确保容器存在
        if (!this.$refs.chartContainer) {
          throw new Error('图表容器不存在')
        }

        // 使用最稳定的渲染方法
        if (this.mermaid.mermaidAPI) {
          // 方法1：使用mermaidAPI.render
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('渲染超时'))
            }, 10000) // 10秒超时

            try {
              this.mermaid.mermaidAPI.render(this.chartId, code, (svgCode, bindFunctions) => {
                clearTimeout(timeout)

                if (svgCode) {
                  // 平滑更新内容
                  this.$refs.chartContainer.innerHTML = svgCode

                  // 绑定交互功能（如果有）
                  if (bindFunctions && typeof bindFunctions === 'function') {
                    try {
                      bindFunctions(this.$refs.chartContainer)
                    } catch (bindError) {
                      console.warn('绑定交互功能失败:', bindError)
                    }
                  }

                  resolve()
                } else {
                  reject(new Error('渲染失败：未生成SVG内容'))
                }
              })
            } catch (error) {
              clearTimeout(timeout)
              reject(error)
            }
          })
        } else if (this.mermaid.render) {
          // 方法2：使用新版本的render方法
          const result = await this.mermaid.render(this.chartId, code)
          if (result && (typeof result === 'string' || result.svg)) {
            this.$refs.chartContainer.innerHTML = result.svg || result
          } else {
            throw new Error('render方法未返回有效内容')
          }
        } else {
          throw new Error('没有可用的渲染方法')
        }
      } catch (error) {
        console.error('稳定渲染失败:', error)
        throw error
      }
    },

    /**
     * 预处理代码以适配Mermaid v8语法
     */
    preprocessCode(code) {
      const originalCode = code.trim()
      let processedCode = originalCode

      console.log('Original Mermaid code:', originalCode)

      // 处理flowchart语法，v8版本使用graph而不是flowchart
      if (processedCode.startsWith('flowchart')) {
        processedCode = processedCode.replace(/^flowchart\s+/, 'graph ')
        console.log('Converted flowchart to graph for v8 compatibility')
      }

      // 移除可能的HTML实体编码
      processedCode = processedCode
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")

      // v8版本关键修复：处理节点标签中的换行符
      // Mermaid v8不支持节点标签内的真实换行符，需要转换为HTML换行或简化文本
      processedCode = this.fixNodeLabelsForV8(processedCode)

      // v8版本特殊处理
      // 确保图表类型声明正确
      if (processedCode.startsWith('graph')) {
        // 检查方向声明是否正确
        const graphMatch = processedCode.match(/^graph\s+([A-Z]{1,2})/)
        if (graphMatch) {
          const direction = graphMatch[1]
          // v8支持的方向：TB, TD, BT, RL, LR
          const validDirections = ['TB', 'TD', 'BT', 'RL', 'LR']
          if (!validDirections.includes(direction)) {
            processedCode = processedCode.replace(/^graph\s+[A-Z]{1,2}/, 'graph TD')
            console.log('Fixed graph direction for v8 compatibility')
          }
        } else {
          // 如果没有方向声明，添加默认方向
          processedCode = processedCode.replace(/^graph\s*/, 'graph TD\n    ')
        }
      }

      // 处理v8不支持的语法
      processedCode = this.removeUnsupportedV8Syntax(processedCode)

      // 处理可能的语法问题
      // 移除多余的空行
      processedCode = processedCode.replace(/\n\s*\n\s*\n/g, '\n\n')

      // 确保缩进一致
      const lines = processedCode.split('\n')
      const processedLines = lines.map((line, index) => {
        if (index === 0) return line // 保持第一行不变
        if (line.trim() === '') return line // 保持空行不变
        // 确保非空行有适当的缩进
        if (!line.startsWith('    ') && line.trim() !== '') {
          return '    ' + line.trim()
        }
        return line
      })

      processedCode = processedLines.join('\n')

      // 输出处理结果用于调试
      if (originalCode !== processedCode) {
        console.log('Code was modified during preprocessing')
        console.log('Processed code for v8:', processedCode)
      } else {
        console.log('Code unchanged during preprocessing')
      }

      return processedCode
    },

    /**
     * 移除Mermaid v8不支持的语法
     */
    removeUnsupportedV8Syntax(code) {
      let cleanedCode = code

      // 1. 移除class定义（v8不支持）
      // 匹配 class 关键字开头的行
      cleanedCode = cleanedCode.replace(/^\s*class\s+.*$/gm, '')

      // 2. 移除classDef定义（v8版本语法不同）
      cleanedCode = cleanedCode.replace(/^\s*classDef\s+.*$/gm, '')

      // 3. 移除click事件定义（v8可能不支持）
      cleanedCode = cleanedCode.replace(/^\s*click\s+.*$/gm, '')

      // 4. 移除style定义（v8语法不同）
      cleanedCode = cleanedCode.replace(/^\s*style\s+.*$/gm, '')

      // 5. 处理subgraph语法（确保v8兼容）
      cleanedCode = cleanedCode.replace(/subgraph\s+([^[\n]*)\s*\[([^\]]*)\]/g, 'subgraph $2')

      // 6. 移除linkStyle定义（v8可能不支持）
      cleanedCode = cleanedCode.replace(/^\s*linkStyle\s+.*$/gm, '')

      // 7. 移除其他可能导致解析错误的内容
      // 移除单独的关键字行（可能是不完整的语法）
      cleanedCode = cleanedCode.replace(/^\s*(class|style|classDef|click|linkStyle)\s*$/gm, '')

      // 8. 确保每行都有完整的语法结构
      const lines = cleanedCode.split('\n')
      const validLines = lines.filter(line => {
        const trimmed = line.trim()
        // 保留空行和注释
        if (!trimmed || trimmed.startsWith('%') || trimmed.startsWith('//')) {
          return true
        }
        // 保留图表类型声明 (只保留 v8.14.0 支持的类型)
        if (trimmed.match(/^(graph|flowchart|sequenceDiagram|stateDiagram|gantt|pie|journey)/)) {
          return true
        }
        // 保留节点定义和连接
        if (trimmed.match(/^[A-Za-z0-9_]+(\[.*\]|\(.*\)|\{.*\}|-->|---)/)) {
          return true
        }
        // 保留subgraph
        if (trimmed.match(/^(subgraph|end)/)) {
          return true
        }
        // 其他可能有效的行
        return trimmed.length > 0
      })

      cleanedCode = validLines.join('\n')

      // 9. 清理多余空行
      cleanedCode = cleanedCode.replace(/\n\s*\n\s*\n/g, '\n\n')

      console.log('Removed unsupported v8 syntax')
      return cleanedCode
    },

    /**
     * 修复节点标签以兼容Mermaid v8
     * v8版本不支持节点标签内的真实换行符和某些特殊字符
     */
    fixNodeLabelsForV8(code) {
      let fixedCode = code

      // 1. 修复方括号标签 [文本内容]
      const nodePattern = /(\w+)\[([^\]]*)\]/g
      fixedCode = fixedCode.replace(nodePattern, (_, nodeId, labelContent) => {
        let fixedLabel = this.sanitizeLabelForV8(labelContent)
        return `${nodeId}[${fixedLabel}]`
      })

      // 2. 修复圆括号标签 (文本内容)
      const roundPattern = /(\w+)\(([^)]*)\)/g
      fixedCode = fixedCode.replace(roundPattern, (_, nodeId, labelContent) => {
        let fixedLabel = this.sanitizeLabelForV8(labelContent)
        return `${nodeId}(${fixedLabel})`
      })

      // 3. 修复花括号标签 {文本内容}
      const bracePattern = /(\w+)\{([^}]*)\}/g
      fixedCode = fixedCode.replace(bracePattern, (_, nodeId, labelContent) => {
        let fixedLabel = this.sanitizeLabelForV8(labelContent)
        return `${nodeId}{${fixedLabel}}`
      })

      // 4. 修复连接线上的标签
      const linkPattern = /-->\|([^|]*)\|/g
      fixedCode = fixedCode.replace(linkPattern, (_, labelContent) => {
        let fixedLabel = this.sanitizeLabelForV8(labelContent)
        return `-->|${fixedLabel}|`
      })

      return fixedCode
    },

    /**
     * 清理标签内容以兼容v8
     */
    sanitizeLabelForV8(labelContent) {
      if (!labelContent) return labelContent

      let fixedLabel = labelContent
        // 移除真实换行符
        .replace(/\n/g, ' ')
        // 移除转义换行符
        .replace(/\\n/g, ' ')
        // 移除制表符
        .replace(/\t/g, ' ')
        // 合并多个空格
        .replace(/\s+/g, ' ')
        // 移除可能导致解析错误的特殊字符
        .replace(/[^\w\s\u4e00-\u9fff.,!?()（）【】-]/g, '')
        .trim()

      // 如果标签太长，进行截断
      if (fixedLabel.length > 50) {
        fixedLabel = fixedLabel.substring(0, 47) + '...'
      }

      // 如果标签为空，提供默认值
      if (!fixedLabel) {
        fixedLabel = '节点'
      }

      if (fixedLabel !== labelContent) {
        console.log(`Sanitized label: "${labelContent}" -> "${fixedLabel}"`)
      }

      return fixedLabel
    },



    /**
     * 添加事件监听器
     */
    addEventListeners() {
      if (!this.$refs.chartContainer) return

      const svgElement = this.$refs.chartContainer.querySelector('svg')
      if (svgElement) {
        svgElement.addEventListener('click', (event) => {
          this.$emit('click', {
            event,
            target: event.target,
            chartId: this.chartId
          })
        })
      }
    },

    /**
     * 设置响应式监听
     */
    setupResize() {
      if (!this.$refs.chartContainer) return

      // 清理之前的监听器
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
      }

      // 创建新的 ResizeObserver
      this.resizeObserver = new ResizeObserver(() => {
        this.handleResize()
      })

      // 开始监听容器大小变化
      this.resizeObserver.observe(this.$refs.chartContainer)
    },

    /**
     * 设置触摸事件监听器
     */
    setupTouchEvents() {
      if (!this.$refs.chartContainer) return

      // 添加触摸事件监听器
      // touchstart 使用 passive: true，不立即阻止默认行为
      this.$refs.chartContainer.addEventListener('touchstart', this.handleTouchStart, { passive: true })
      // touchmove 使用 passive: false，以便在需要时可以调用 preventDefault
      this.$refs.chartContainer.addEventListener('touchmove', this.handleTouchMove, { passive: false })
      // touchend 使用 passive: true
      this.$refs.chartContainer.addEventListener('touchend', this.handleTouchEnd, { passive: true })
    },

    /**
     * 处理容器大小变化
     */
    handleResize() {
      // 防抖处理
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
      }

      this.resizeTimer = setTimeout(() => {
        this.updateAutoHeight()
      }, 100)
    },

    /**
     * 更新自适应高度
     */
    updateAutoHeight() {
      if (this.height !== 'auto' || !this.$refs.chartContainer) {
        return
      }

      const svgElement = this.$refs.chartContainer.querySelector('svg')
      if (!svgElement) return

      try {
        // 获取SVG的实际尺寸
        const bbox = svgElement.getBBox()
        const viewBox = svgElement.viewBox.baseVal

        let naturalHeight = bbox.height

        // 如果有viewBox，使用viewBox的高度
        if (viewBox && viewBox.height > 0) {
          naturalHeight = viewBox.height
        }

        // 添加一些padding
        const padding = 40
        const calculatedHeight = Math.max(naturalHeight + padding, parseInt(this.minHeight))
        const maxHeightValue = parseInt(this.maxHeight)

        // 限制最大高度
        const finalHeight = Math.min(calculatedHeight, maxHeightValue)
        this.autoHeight = `${finalHeight}px`

        console.log('Auto height updated:', {
          bbox: bbox,
          viewBox: { width: viewBox?.width, height: viewBox?.height },
          naturalHeight,
          calculatedHeight,
          finalHeight: this.autoHeight
        })
      } catch (error) {
        console.warn('Failed to calculate auto height:', error)
        this.autoHeight = this.minHeight
      }
    },

    /**
     * 清理资源
     */
    cleanup() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }

      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer)
        this.resizeTimer = null
      }

      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
        this.debounceTimer = null
      }

      if (this.$refs.chartContainer) {
        this.$refs.chartContainer.innerHTML = ''
      }

      // 清理拖拽事件监听器 - 鼠标事件
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.stopDrag)

      // 清理触摸事件监听器
      if (this.$refs.chartContainer) {
        this.$refs.chartContainer.removeEventListener('touchstart', this.handleTouchStart)
        this.$refs.chartContainer.removeEventListener('touchmove', this.handleTouchMove)
        this.$refs.chartContainer.removeEventListener('touchend', this.handleTouchEnd)
      }

      // 清理全屏事件监听器
      if (screenfull.isEnabled) {
        screenfull.off('change', this.handleFullscreenChange)
      }

      this.chartId = null
      this.lastValidCode = null
      this.renderAttempts = 0
      this.lastCodeLength = 0
      this.codeGrowthHistory = []
      this.isStreamingDetected = false
    }
  },

  async mounted() {
    await this.$nextTick()

    // 如果有待处理的代码，立即渲染
    if (this.pendingCode) {
      await this.renderChart(this.pendingCode)
      this.pendingCode = null
    }

    this.setupResize()
    this.setupTouchEvents()

    // 设置全屏事件监听器
    if (screenfull.isEnabled) {
      screenfull.on('change', this.handleFullscreenChange)
    }
  },

  beforeUnmount() {
    this.cleanup()
  }
})
</script>

<template>
  <div
    ref="mermaidWrapper"
    class="mermaid-wrapper"
    :style="containerStyle"
    v-loading="isLoading || loading"
    element-loading-text="图表渲染中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(248, 249, 250, 0.8)"
    :class="{ 'fullscreen': isFullScreen }"
  >
    <!-- 控制按钮组 -->
    <div class="control-buttons">
      <el-button size="small" @click="() => zoom(1.25)" title="放大">
        <i class="el-icon-zoom-in"></i>
        放大
      </el-button>
      <el-button size="small" @click="() => zoom(0.8)" title="缩小">
        <i class="el-icon-zoom-out"></i>
        缩小
      </el-button>
      <el-button size="small" @click="resetTransform" title="重置">
        <i class="el-icon-refresh"></i>
        重置
      </el-button>
      <el-button size="small" @click="toggleFullscreen" title="全屏">
        <i :class="isFullScreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
        {{ isFullScreen ? '退出全屏' : '全屏' }}
      </el-button>
    </div>

    <!-- 错误状态 -->
    <div
      v-if="error"
      class="mermaid-error"
    >
      <div class="error-icon">⚠️</div>
      <div class="error-message">{{ error }}</div>
    </div>

    <!-- 图表容器 -->
    <div
      v-else
      ref="chartContainer"
      class="mermaid-container"
      :style="{ width: '100%', height: '100%' }"
      @mousedown="startDrag"
      @wheel="handleWheel"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    />
  </div>
</template>

<style lang="less" scoped>
.mermaid-wrapper {
  position: relative;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 300px;

  &.fullscreen {
    background: #ffffff;
  }
}

.control-buttons {
  position: absolute;
  left: 8px;
  top: 8px;
  z-index: 10;
  display: flex;
  gap: 4px;

  .el-button {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dcdfe6;
    color: #606266;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
      border-color: #409eff;
      color: #409eff;
    }

    i {
      margin-right: 2px;
    }
  }
}

.mermaid-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: grab;
  overflow: hidden;
  // 优化触摸体验 - 允许垂直滚动，只禁用水平滚动和缩放
  touch-action: pan-y pinch-zoom; // 允许垂直滚动和双指缩放
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  &:active {
    cursor: grabbing;
  }

  :deep(svg) {
    max-width: none;
    max-height: none;
    width: auto;
    height: auto;
    display: block;
    user-select: none;
    pointer-events: none;
    // SVG 不阻止触摸操作，让容器处理
    touch-action: inherit;
  }
}

.mermaid-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  text-align: center;
  z-index: 1;

  .error-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .error-message {
    font-size: 14px;
    line-height: 1.4;
    font-weight: 500;
    white-space: pre-line;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-buttons {
    .el-button {
      padding: 2px 4px;
      font-size: 11px;

      span {
        display: none; // 在小屏幕上只显示图标
      }
    }
  }
}
</style>
