<script>
import { defineComponent } from 'vue'
import { Markmap } from 'markmap-view'
import { transformer } from '../../utils/markdown-it-plugins/markmap/markmap'
import screenfull from 'screenfull'

export default defineComponent({
  name: 'MindMap',
  props: {
    code: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      isFullScreen: false
    }
  },
  emits: ['click'],
  watch: {
    code: {
      immediate: true,
      async handler(val) {
        if (!val) {
          return
        }
        if (this.mm) {
          const { root } = await transformer.transform(val)
          this.mm.setData(root)
        }
      }
    }
  },
  methods: {
    zomm(val) {
      this.mm?.rescale(val)
    },
    toggle() {
      if (screenfull.isFullscreen) {
        screenfull.exit()
        return
      }
      this.$refs.svgWrapRef.requestFullscreen()
    },

    fullScreenHandel(e) {
      if (!(e.target === this.$refs.svgWrapRef)) {
        return
      }
      this.isFullScreen = screenfull.isFullscreen;
    },
    onClick (e) {
      e.preventDefault()
      if (e.target.tagName === 'A' && /^\/\d+$/.test(e.target.pathname)) {
        // 点击学生
        this.$emit('click', {
          target: {
            tagName: e.target.tagName,
            dataset: {
              uid: e.target.pathname.slice(1)
            }
          }
        })
      }
    },
    resize () {
      if (!this.$refs.svgRef || !this.$refs.svgWrapRef) {
        return
      }
      this.$refs.svgRef.style.width = `${this.$refs.svgWrapRef.offsetWidth}px`
      this.$refs.svgRef.style.height = `${this.$refs.svgWrapRef.offsetHeight}px`
      this.$refs.svgRef.setAttribute('width', this.$refs.svgWrapRef.offsetWidth)
      this.$refs.svgRef.setAttribute('height', this.$refs.svgWrapRef.offsetHeight)
    }
  },
  async mounted() {
    const { root } = await transformer.transform(this.code)
    this.mm = Markmap.create(this.$refs.svgRef, {
      duration: 0, // 流式更新时使用更短的动画时间
      spacingVertical: 5,
      spacingHorizontal: 80,
      autoFit: true,
      pan: false, // 禁用平移功能
      zoom: true, // 启用缩放
      colorFreezeLevel: 6, // 颜色冻结级别
      maxWidth: 0, // 最大宽度，0表示无限制
      initialExpandLevel: -1 // 初始展开级别：-1表示全部展开
    }, root)

    console.log('mounted')

    this.$nextTick(() => {
      this.resize()
      this.mm?.fit() // 重新调整大小
    })

    const observer = new ResizeObserver(() => {
      this.resize()
      // 设置宽高
      this.mm?.fit() // 重新调整大小
    })
    this.observer = observer
    observer.observe(this.$refs.svgWrapRef)

    screenfull.on('change', this.fullScreenHandel)
  },
  async beforeUnmount() {
    screenfull.off('change', this.fullScreenHandel)
    this.observer?.unobserve(this.$refs.svgWrapRef)
    try {
      this.mm?.destroy()
    } catch (e) {
      console.warn(e)
    }
  }
})
</script>

<template>
  <div ref="svgWrapRef" style="position: relative;width: 100%;height: 400px" :style="{ background: isFullScreen ? '#ffffff' : 'transparent' }">
    <div style="position: absolute;left: 8px;top: 8px">
      <el-button size="small" @click="() => zomm(1.25)">放大</el-button>
      <el-button size="small" @click="() => zomm(0.8)">缩小</el-button>
      <el-button size="small" @click="() => toggle()">{{ isFullScreen ? '退出全屏' : '全屏' }}</el-button>
    </div>
    <svg ref="svgRef" @click="onClick" class="markmap-wrapper">
    </svg>
  </div>
</template>

<style lang="less">
.markmap-container {
  position: relative;
  overflow: hidden;
  /* 禁用平移，只允许缩放交互 */
  touch-action: pinch-zoom;
}

.markmap-foreign {
  pre {
    padding: 0!important;
  }
}

.markmap-container svg {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
  height: 100%;
  display: block;
}
.markmap-container .markmap-node {
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.markmap-container .markmap-node:hover {
  opacity: 0.8;
}
.markmap-container .markmap-link {
  fill: none;
  stroke: #999;
  stroke-width: 2px;
  transition: stroke 0.2s ease;
}
.markmap-container .markmap-link:hover {
  stroke: #666;
}
/* 加载动画 */
@keyframes pulse {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}
.markmap-container .loading {
  animation: pulse 1.5s ease-in-out infinite;
}
/* 流式更新指示器 */
.streaming-indicator {
  animation: slideInRight 0.3s ease-out;
}
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.streaming-dot {
  animation: pulse 1.5s ease-in-out infinite !important;
}
/* 防闪烁样式 */
.markmap-container svg {
  opacity: 1;
  transition: opacity 0.1s ease;
}
.markmap-container.updating svg {
  opacity: 0.9;
}
</style>

<style lang="less" scoped>
</style>
