<script>
import MindMap from './MindMap.vue'
import ECharts from './ECharts.vue'
import Mermaid from './Mermaid.vue'
import * as htmlparser2 from 'htmlparser2'
// import './github.markdown.css'

export default {
  name: 'Mark<PERSON><PERSON><PERSON><PERSON>',
  props: {
    html: {
      type: String,
      required: true
    }
  },
  emits: ['click'],
  data() {
    return {
      vnodeChildren: null,
      // 安全配置选项
      allowInlineStyles: false, // 是否允许内联样式
      allowComments: true, // 是否保留注释
      enableDebugLogging: false // 是否启用调试日志
    }
  },
  watch: {
    html: {
      immediate: true,
      handler(val) {
        this.updateVNode(val)
      }
    }
  },
  methods: {
    handleClick(e) {
      this.$emit('click', e)
    },
    updateVNode(html) {
      const dom = htmlparser2.parseDocument(html)
      const children = htmlparser2.DomUtils.getChildren(dom)
      this.vnodeChildren = children.map(this.domToVNode)
    },

    /**
     * 将 DOM 节点转换为 Vue VNode
     * @param {Object} node - DOM 节点对象
     * @returns {VNode|string|null} Vue VNode、文本内容或null
     */
    domToVNode(node) {
      if (!node || !node.type) {
        console.warn('Invalid node:', node)
        return null
      }

      const h = this.$createElement

      switch (node.type) {
        case 'text':
          return this.handleTextNode(h, node)

        case 'tag':
          return this.handleTagNode(h, node)

        case 'comment':
          return this.handleCommentNode(h, node)

        case 'directive':
          return this.handleDirectiveNode(h, node)

        case 'cdata':
          return this.handleCdataNode(h, node)

        case 'script':
          return this.handleScriptNode(h, node)

        case 'style':
          return this.handleStyleNode(h, node)

        default:
          console.warn(`Unknown node type: ${node.type}`, node)
          return null
      }
    },

    /**
     * 处理文本节点
     * @param {Function} h - createElement 函数
     * @param {Object} node - 文本节点
     * @returns {string} 文本内容
     */
    handleTextNode(h, node) {
      // LaTeX 公式的正则表达式
      const latexRegex = /\$\$([\s\S]+?)\$\$|\$([^$\n]+?)\$|\\\[((?:.|\n)+?)\\\]|\\\(((?:.|\n)+?)\\\)/g
      // 检查是否包含 LaTeX 公式
      if (!latexRegex.test(node.data || '')) {
        // 纯文本，直接返回
        return node.data || ''
      }
      return h('span', {
        style: { display: 'contents', pointerEvents: 'none' },
        directives: [{
          name: 'katex',
          value: {
            delimiters: [
              { left: '$$', right: '$$', display: true },
              { left: '$', right: '$', display: false },
              { left: '\\(', right: '\\)', display: false },
              { left: '\\[', right: '\\]', display: true }
            ],
            throwOnError: false
          }
        }],
        domProps: {
          innerHTML: node.data || ''
        }
      })
    },

    /**
     * 处理标签节点
     * @param {Function} h - createElement 函数
     * @param {Object} node - 标签节点
     * @returns {VNode|null} Vue VNode
     */
    handleTagNode(h, node) {
      try {
        const tag = node.name
        const attrs = this.convertAttrs(node.attribs)

        // 特殊处理：思维导图节点
        if (this.isMindmapNode(node)) {
          return this.createMindmapVNode(h, tag, attrs, node.attribs)
        }

        // 特殊处理：echarts图表节点
        if (this.isEchartsNode(node)) {
          return this.createEchartsVNode(h, tag, attrs, node.attribs)
        }

        // 特殊处理：mermaid图表节点
        if (this.isMermaidNode(node)) {
          return this.createMermaidVNode(h, tag, attrs, node.attribs)
        }

        // 安全检查：过滤危险标签
        if (this.isDangerousTag(tag)) {
          console.warn(`Dangerous tag filtered: ${tag}`)
          return null
        }

        // 混合内容，正常处理子节点
        const children = this.getNodeChildren(node)
        return h(tag, { attrs }, children)
      } catch (error) {
        console.error('Failed to handle tag node:', error)
        return null
      }
    },

    /**
     * 处理注释节点
     * @param {Function} h - createElement 函数
     * @param {Object} node - 注释节点
     * @returns {VNode|null} Vue VNode 或 null
     */
    handleCommentNode(h, node) {
      // 根据配置决定是否保留注释
      if (this.allowComments) {
        return h('template', {}, [`<!-- ${node.data || ''} -->`])
      }
      return null
    },

    /**
     * 处理指令节点（如 DOCTYPE）
     * @param {Function} h - createElement 函数
     * @param {Object} node - 指令节点
     * @returns {null} 总是返回 null
     */
    handleDirectiveNode(h, node) {
      // 指令节点在 Vue 组件中通常不需要处理
      if (this.enableDebugLogging) {
        console.log(`Directive node ignored: ${node.name}`, node.data)
      }
      return null
    },

    /**
     * 处理 CDATA 节点
     * @param {Function} h - createElement 函数
     * @param {Object} node - CDATA 节点
     * @returns {VNode|null} Vue VNode 或 null
     */
    handleCdataNode(h, node) {
      try {
        // CDATA 节点通常包含原始文本数据
        if (node.children && node.children.length > 0) {
          const children = this.getNodeChildren(node)
          return h('div', { attrs: { class: 'cdata-content' } }, children)
        }
        return null
      } catch (error) {
        console.error('Failed to handle CDATA node:', error)
        return null
      }
    },

    /**
     * 处理脚本节点
     * @param {Function} h - createElement 函数
     * @param {Object} node - 脚本节点
     * @returns {null} 出于安全考虑总是返回 null
     */
    handleScriptNode(h, node) {
      // 出于安全考虑，完全忽略脚本节点
      console.warn('Script nodes are ignored for security reasons', node)
      return null
    },

    /**
     * 处理样式节点
     * @param {Function} h - createElement 函数
     * @param {Object} node - 样式节点
     * @returns {VNode|null} Vue VNode 或 null
     */
    handleStyleNode(h, node) {
      const styleText = (node.children || []).map(child => child.data || '').join('')
      return h('style', styleText)
    },

    /**
     * 检查是否为危险标签
     * @param {string} tagName - 标签名
     * @returns {boolean} 是否为危险标签
     */
    isDangerousTag(tagName) {
      const dangerousTags = []
      return dangerousTags.includes(tagName.toLowerCase())
    },

    /**
     * 判断是否为思维导图节点
     * @param {Object} node - DOM 节点对象
     * @returns {boolean} 是否为思维导图节点
     */
    isMindmapNode(node) {
      return node.attribs && node.attribs['data-mindmap'] === '1'
    },
    /**
     * 判断是否为echarts图表节点
     * @param {Object} node - DOM 节点对象
     * @returns {boolean} 是否为echarts图表节点
     */
    isEchartsNode(node) {
      return node.attribs && (node.attribs['data-echarts-id'] || node.attribs['data-echarts-json'])
    },

    /**
     * 判断是否为mermaid图表节点
     * @param {Object} node - DOM 节点对象
     * @returns {boolean} 是否为mermaid图表节点
     */
    isMermaidNode(node) {
      return node.attribs && node.attribs['data-mermaid-code']
    },

    /**
     * 创建思维导图 VNode
     * @param {Function} h - createElement 函数
     * @param {string} tag - 标签名
     * @param {Object} attrs - 属性对象
     * @param {Object} rawAttribs - 原始属性对象
     * @returns {VNode} 思维导图 VNode
     */
    createMindmapVNode(h, tag, attrs, rawAttribs) {
      try {
        const encodedContent = rawAttribs['data-mindmap-content'] || ''
        const code = encodedContent ? decodeURIComponent(encodedContent) : ''

        return h(tag, {
          attrs: {
            ...attrs,
            'data-mindmap-content': undefined
          }
        }, [
          h(MindMap, {
            props: { code },
            on: {
              click: (e) => {
                this.$emit('click', e)
              }
            }
          })
        ])
      } catch (error) {
        console.error('Failed to create mindmap VNode:', error)
        return h('div', { attrs: { class: 'mindmap-error' } }, ['思维导图渲染失败'])
      }
    },

    /**
     * 创建echarts图表 VNode
     * @param {Function} h - createElement 函数
     * @param {string} tag - 标签名
     * @param {Object} attrs - 属性对象
     * @param {Object} rawAttribs - 原始属性对象
     * @returns {VNode} echarts图表 VNode
     */
    createEchartsVNode(h, tag, attrs, rawAttribs) {
      try {
        // 从不同的属性中获取 JSON 数据
        const jsonStr = rawAttribs['data-echarts-json'] || '';

        // 使用 ECharts 组件，让组件自己处理所有状态
        return h(tag, {
          attrs: {
            ...attrs,
            'data-echarts-json': undefined
          }
        }, [
          h(ECharts, {
            props: {
              options: jsonStr,
              loadingText: '图表数据加载中...'
            },
            on: {
              click: (params) => {
                this.$emit('echarts-click', params)
              },
              error: (error) => {
                console.error('ECharts error:', error)
                this.$emit('echarts-error', error)
              },
              ready: (chart) => {
                console.log('ECharts ready:', chart)
                this.$emit('echarts-ready', chart)
              }
            }
          })
        ])
      } catch (error) {
        console.error('Failed to create ECharts VNode:', error)
        return h('div', { attrs: { class: 'echarts-error' } }, ['图表渲染失败: ' + error.message])
      }
    },

    /**
     * 创建mermaid图表 VNode
     * @param {Function} h - createElement 函数
     * @param {string} tag - 标签名
     * @param {Object} attrs - 属性对象
     * @param {Object} rawAttribs - 原始属性对象
     * @returns {VNode} mermaid图表 VNode
     */
    createMermaidVNode(h, tag, attrs, rawAttribs) {
      try {
        const encodedCode = rawAttribs['data-mermaid-code'] || ''
        const code = encodedCode ? decodeURIComponent(encodedCode) : ''

        return h(tag, {
          attrs: {
            ...attrs,
            'data-mermaid-code': undefined
          }
        }, [
          h(Mermaid, {
            props: {
              code,
              loadingText: 'Mermaid图表渲染中...'
            },
            on: {
              click: (params) => {
                this.$emit('mermaid-click', params)
              },
              error: (error) => {
                console.error('Mermaid error:', error)
                this.$emit('mermaid-error', error)
              },
              ready: (chartId) => {
                console.log('Mermaid ready:', chartId)
                this.$emit('mermaid-ready', chartId)
              }
            }
          })
        ])
      } catch (error) {
        console.error('Failed to create Mermaid VNode:', error)
        return h('div', { attrs: { class: 'mermaid-error' } }, ['Mermaid图表渲染失败: ' + error.message])
      }
    },





    /**
     * 获取节点的子节点并转换为 VNode
     * @param {Object} node - DOM 节点对象
     * @returns {Array} 子节点 VNode 数组
     */
    getNodeChildren(node) {
      try {
        const children = htmlparser2.DomUtils.getChildren(node) || []
        return children
          .map(child => this.domToVNode(child))
          .filter(vnode => vnode !== null) // 过滤掉空节点
      } catch (error) {
        console.error('Failed to get node children:', error)
        return []
      }
    },

    /**
     * 转换属性对象，过滤和清理属性
     * @param {Object} attrs - 原始属性对象
     * @returns {Object} 清理后的属性对象
     */
    convertAttrs(attrs = {}) {
      const result = {}

      // 属性白名单，只保留安全的属性
      const allowedAttrs = [
        'id', 'class', 'style', 'title', 'alt', 'src', 'href', 'target',
        'width', 'height', 'data-*', 'aria-*', 'role', 'type', 'name',
        'placeholder', 'disabled', 'readonly', 'checked', 'selected'
      ]

      for (const key in attrs) {
        if (this.isAllowedAttribute(key, allowedAttrs)) {
          // 清理属性值
          const cleanValue = this.sanitizeAttributeValue(key, attrs[key])
          if (cleanValue !== null) {
            result[key] = cleanValue
          }
        }
      }

      return result
    },

    /**
     * 检查属性是否被允许
     * @param {string} attrName - 属性名
     * @param {Array} allowedAttrs - 允许的属性列表
     * @returns {boolean} 是否允许
     */
    isAllowedAttribute(attrName, allowedAttrs) {
      return true
      // return allowedAttrs.some(pattern => {
      //   if (pattern.endsWith('*')) {
      //     return attrName.startsWith(pattern.slice(0, -1))
      //   }
      //   return attrName === pattern
      // })
    },

    /**
     * 清理属性值
     * @param {string} attrName - 属性名
     * @param {string} attrValue - 属性值
     * @returns {string|null} 清理后的属性值
     */
    sanitizeAttributeValue(attrName, attrValue) {
      return attrValue
    }
  },
  render(h) {
    return h('div', { class: 'markdown-renderer markdown-body', on: { click: this.handleClick } }, this.vnodeChildren)
  }
}
</script>

<style lang="less" scoped>
@import "./github.markdown.css";

.markdown-body {
  --base-size-4: 4px;
  --base-size-8: 8px;
  --base-size-16: 16px;
  --base-size-24: 24px;
  --base-size-40: 40px;

  box-sizing: border-box;
  min-width: 200px;
  margin: 0 auto;
  background-color: transparent;
  padding: 8px 0;
  border-radius: 4px;

  ul {
    list-style-type: disc;
  }

  ol ul, ul ul {
    list-style-type: circle;
  }

  ol {
    list-style-type: decimal;
  }
}
</style>

<style scoped lang="less">
.markdown-renderer img {
  max-width: 100%;
}

/* 思维导图错误样式 */
.mindmap-error {
  padding: 15px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
  text-align: center;
  font-size: 14px;
}

/* ECharts 错误样式 - 保留用于兜底错误显示 */
.echarts-error {
  padding: 15px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
  text-align: center;
  font-size: 14px;
}

/* Mermaid 错误样式 */
.mermaid-error {
  padding: 15px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
  text-align: center;
  font-size: 14px;
}

/* CDATA 内容样式 */
.cdata-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
}

/* 调试信息样式 */
.debug-info {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 8px;
  font-size: 11px;
  color: #0066cc;
  margin: 5px 0;
}
</style>
