import OtherComp from './OtherComp'
import validateMerges from './validateMerges'
export default {
  data () {
    return {
      key: 0,
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          const { field } = column
          if (row[field] && row[field].type === 2) return 'table-body-cell-class'
          return ''
        },
      },
      cellSpanOption: {
        bodyCellSpan: this.bodyCellSpan
      },
      startRowIndex: 0,
      rowStyleOption: {
        clickHighlight: false,
        hoverHighlight: false
      },
      eventCustomOption: {
        //单元格点击
        bodyCellEvents: ({ row, column, rowIndex }) => {
          return {
            click: () => {
              this.$emit('handleEventColumn', { row, column, rowIndex })
            },
            dblclick: () => {
              this.$emit('dblclickEventColumn', { row, column, rowIndex })
            }
          };
        },
      },
      contextmenuBodyOption: {
        beforeShow: ({ isWholeColSelection, selectionRangeKeys, selectionRangeIndexes }) => {
          const { startColKey, startRowKey } = selectionRangeKeys
          const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
          this.contextmenuBodyOption.contextmenus.forEach(item => {
            const { type } = item
            if (type.indexOf('INSERT_ROW') !== -1) item.disabled = !(startRowIndex === endRowIndex)
            if (type.indexOf('INSERT_COL') !== -1) item.disabled = !(startColIndex === endColIndex)

          })
          const column = this.columns.find(item => item.key === startColKey)
          const row = this.tableData.find(item => item.rowKey === startRowKey)
          this.$emit('handleEventColumn', { row, column, rowIndex: startRowIndex })
        },
        // 菜单事件
        afterMenuClick: ({ type, selectionRangeKeys, selectionRangeIndexes }) => {
          switch (type) {
            case 'INSERT_ROW_UP':
              this.insertRow(selectionRangeIndexes, 'up')
              break
            case 'INSERT_ROW_DOWN':
              this.insertRow(selectionRangeIndexes)
              break
            case 'SET_ROW_HEIGHT':
              this.setRowHeight(selectionRangeIndexes)
              break
            case 'REMOVE_ROW_':
              this.removeRow(selectionRangeIndexes)
              break
            case 'INSERT_COL_LEFT':
              this.insertCol(selectionRangeIndexes, 'left')
              break
            case 'INSERT_COL_RIGHT':
              this.insertCol(selectionRangeIndexes)
              break
            case 'SET_COL_WIDTH':
              this.setColWidth(selectionRangeIndexes)
              break
            case 'REMOVE_COL':
              this.removeCol(selectionRangeIndexes)
              break
            case 'MERGE_CELL':
              this.mergeCells(selectionRangeIndexes, selectionRangeKeys)
              break
            case 'CANCEL_MERGE_CELL':
              this.cancelMergeCells(selectionRangeIndexes)
              break
            case 'SET_LOCK':
              this.setCells(selectionRangeIndexes, 1)
              break
            case 'SET_ANSWER':
              this.setCells(selectionRangeIndexes, 2)
              break
            case 'ALIGN_LEFT':
              this.setAlignCells(selectionRangeIndexes, 'left')
              break
            case 'ALIGN_CENTER':
              this.setAlignCells(selectionRangeIndexes, 'center')
              break
            case 'ALIGN_RIGHT':
              this.setAlignCells(selectionRangeIndexes, 'right')
              break
            default:
          }
          return false
        },
        // 菜单配置
        contextmenus: [
          {
            type: 'INSERT_ROW_UP',
            label: '上方插入行',
            disabled: false
          },
          {
            type: 'INSERT_ROW_DOWN',
            label: '下方插入行',
            disabled: false
          },
          {
            type: 'SET_ROW_HEIGHT',
            label: '行高设置',
            disabled: false
          },
          {
            type: 'REMOVE_ROW_',
            label: '删除行',
            disabled: false
          },
          {
            type: 'SEPARATOR'
          },
          {
            type: 'INSERT_COL_LEFT',
            label: '左侧插入列',
            disabled: false
          },
          {
            type: 'INSERT_COL_RIGHT',
            label: '右侧插入列',
            disabled: false
          },
          {
            type: 'SET_COL_WIDTH',
            label: '列宽设置',
            disabled: false
          },
          {
            type: 'REMOVE_COL',
            label: '删除列',
            disabled: false
          },
          {
            type: 'SEPARATOR'
          },
          {
            type: 'CELL_OPERATE',
            label: '单元格操作',
            disabled: false,
            children: [
              {
                type: 'MERGE_CELL',
                label: '合并单元格'
              },
              {
                type: 'CANCEL_MERGE_CELL',
                label: '取消合并'
              },
              // {
              //   type: 'ALIGN_CELL',
              //   label: '单元格对齐方式',
              //   children: [
              //     {
              //       type: 'ALIGN_LEFT',
              //       label: '左对齐'
              //     },
              //     {
              //       type: 'ALIGN_CENTER',
              //       label: '居中对齐'
              //     },
              //     {
              //       type: 'ALIGN_RIGHT',
              //       label: '右对齐'
              //     },
              //   ]
              // },
            ]
          },
          {
            type: 'CELL_SET',
            label: '单元格设置',
            disabled: false,
            children: [
              {
                type: 'SET_ANSWER',
                label: '设置为答案区'
              },
              {
                type: 'SET_LOCK',
                label: '设置为固定区'
              }
            ]
          }
        ]
      },
    }
  },
  computed: {
    spans () {
      const obj = {}
      this.merges.forEach(item => {
        const { startRowIndex, endRowIndex, startColIndex, endColIndex } = item
        for (let i = startRowIndex; i <= endRowIndex; i++) {
          for (let j = startColIndex; j <= endColIndex; j++) {
            const flag = i === startRowIndex && j === startColIndex
            obj[`${i}-${j}`] = {
              rowspan: flag ? endRowIndex - startRowIndex + 1 : 0,
              colspan: flag ? endColIndex - startColIndex + 1 : 0
            }
          }
        }
      })
      return obj
    }
  },
  methods: {
    bodyCellSpan ({ row, column, rowIndex }) {
      const colIndex = this.columns.findIndex(item => item.key === column.key)
      const data = this.spans[`${rowIndex}-${colIndex}`]
      if (data) return data
    },
    //删除行
    removeRow (selectionRangeIndexes) {
      let { startColIndex, startRowIndex, endRowIndex } = selectionRangeIndexes
      //判断当前鼠标右击的是否为合并单元格 并且找到合并对象
      const mergesCell = this.merges.find(item => item.startRowIndex === startRowIndex && item.startColIndex === startColIndex)
      if (mergesCell) endRowIndex = mergesCell.endRowIndex
      const len = endRowIndex - startRowIndex + 1
      if (this.tableData.length === len) {
        this.$message.warning('不可全部删除')
        return false
      }
      this.tableData.splice(startRowIndex, len)
      // 重置其中的合并单元格
      this.merges.forEach(item => {
        if (item.startRowIndex > startRowIndex) {
          item.startRowIndex = item.startRowIndex - len
        }
        if (item.startRowIndex <= startRowIndex && item.endRowIndex >= endRowIndex) {
          item.endRowIndex = item.endRowIndex - len
        }
      })
      this.merges = this.merges.filter(item => !(item.endColIndex === item.startColIndex && item.endRowIndex === item.startRowIndex) && item.endRowIndex >= item.startRowIndex)
      this.change()
      this.$nextTick(() => this.updateBorder())
    },
    // 删除列
    removeCol (selectionRangeIndexes) {
      let { startColIndex, endColIndex, startRowIndex } = selectionRangeIndexes
      //判断当前鼠标右击的是否为合并单元格 并且找到合并对象
      const mergesCell = this.merges.find(item => item.startRowIndex === startRowIndex && item.startColIndex === startColIndex)
      if (mergesCell) endColIndex = mergesCell.endColIndex
      const columns = this.columns.filter((item, index) => index >= startColIndex && index <= endColIndex)
      const len = endColIndex - startColIndex + 1
      if (this.columns.length === len) {
        this.$message.warning('不可全部删除')
        return false
      }
      this.columns.splice(startColIndex, len)
      // 数据删除
      for (const row of this.tableData) {
        for (const column of columns) {
          delete row[column.field]
        }
      }
      // 重置其中的合并单元格
      this.merges.forEach(item => {
        if (item.startColIndex > startColIndex) {
          item.startColIndex = item.startColIndex - len
        }
        if (item.startColIndex <= startColIndex && item.endColIndex >= endColIndex) {
          item.endColIndex = item.endColIndex - len
        }
      })
      this.merges = this.merges.filter(item => !(item.endColIndex === item.startColIndex && item.endRowIndex === item.startRowIndex) && item.endColIndex >= item.startColIndex)
      this.change()
      this.$nextTick(() => this.updateBorder())
    },
    // 插入行
    insertRow (selectionRangeIndexes, flag) {
      const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
      //判断当前鼠标右击的是否为合并单元格 并且找到合并对象
      const mergesCell = this.merges.find(item => item.startRowIndex === startRowIndex && item.startColIndex === startColIndex)
      let start = startRowIndex
      if (mergesCell) start = mergesCell.endRowIndex
      const data = {}
      this.columns.forEach(item => {
        data[item.field] = { value: '', type: 1 }
      })
      this.tableData.splice(flag === 'up' ? startRowIndex : start + 1, 0, {
        ...data,
        rowKey: Date.now()
      })
      // 重置其中的合并单元格
      this.merges.forEach(item => {
        if (flag === 'up') {
          if (startRowIndex <= item.startRowIndex) {
            item.startRowIndex += 1
            item.endRowIndex += 1
          } else if (startRowIndex > item.startRowIndex && startRowIndex <= item.endRowIndex) {
            item.endRowIndex += 1
          }
        } else if (!mergesCell) {
          //非合并单元格
          if (startRowIndex >= item.startRowIndex && startRowIndex < item.endRowIndex) {
            item.endRowIndex += 1
          } else {
            item.startRowIndex += 1
            item.endRowIndex += 1
          }
        }
      })
      this.change()
      this.$nextTick(() => this.updateBorder())
    },
    // 插入列
    insertCol (selectionRangeIndexes, flag) {
      const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
      //判断当前鼠标右击的是否为合并单元格 并且找到合并对象
      const mergesCell = this.merges.find(item => item.startRowIndex === startRowIndex && item.startColIndex === startColIndex)
      let start = startColIndex
      if (mergesCell) start = mergesCell.endColIndex
      this.columns.splice(flag === 'left' ? startColIndex : start + 1, 0, {
        key: Date.now(),
        field: `col${Date.now()}`,
        width: 100,
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          const { value, align } = row[column.field];
          return <OtherComp content={value} align={align || 'center'} />
        }
      })
      // 重置其中的合并单元格
      this.merges.forEach(item => {
        if (flag === 'left') {
          if (startColIndex <= item.startColIndex) {
            item.startColIndex += 1
            item.endColIndex += 1
          } else if (startColIndex > item.startColIndex && startColIndex <= item.endColIndex) {
            item.endColIndex += 1
          }
        } else if (!mergesCell) {
          //非合并单元格
          if (startColIndex >= item.startColIndex && startColIndex < item.endColIndex) {
            item.endColIndex += 1
          }
        }
      })
      // 补充表格内容数据
      for (const row of this.tableData) {
        for (const column of this.columns) {
          if (!row.hasOwnProperty(column.field)) {
            row[column.field] = { value: '', type: 1 }
          }
        }
      }
      this.change()
      this.$nextTick(() => this.updateBorder())
    },
    //合并单元格
    mergeCells (selectionRangeIndexes, selectionRangeKeys) {
      const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
      if (startRowIndex === endRowIndex && startColIndex === endColIndex) return
      if (validateMerges(selectionRangeIndexes, this.merges)) {
        this.$message.warning('所选区域包含合并单元格，请先取消合并')
        return
      }
      this.merges.push({ ...selectionRangeIndexes })
      this.change()
      this.$nextTick(() => this.updateBorder())
    },
    //取消合并
    cancelMergeCells (selectionRangeIndexes) {
      const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
      //判断当前鼠标右击的是否为合并单元格 并且找到合并对象的下标
      const mergesCellIndex = this.merges.findIndex(item => item.startRowIndex === startRowIndex && item.startColIndex === startColIndex)
      if (mergesCellIndex === -1) return false
      this.merges.splice(mergesCellIndex, 1)
      this.change()
      this.$nextTick(() => this.updateBorder())
    },
    //设置 固定|答案区
    setCells (selectionRangeIndexes, type) {
      const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
      const columns = this.columns.filter((item, index) => index >= startColIndex && index <= endColIndex)
      const rowspan = this.tableData.filter((item, index) => index >= startRowIndex && index <= endRowIndex)
      for (const row of rowspan) {
        for (const column of columns) {
          row[column.field].type = type
        }
      }
    },
    //设置单元格对齐方式
    setAlignCells (selectionRangeIndexes, align) {
      const { startRowIndex, endRowIndex, startColIndex, endColIndex } = selectionRangeIndexes
      const columns = this.columns.filter((item, index) => index >= startColIndex && index <= endColIndex)
      const rowspan = this.tableData.filter((item, index) => index >= startRowIndex && index <= endRowIndex)
      for (const row of rowspan) {
        for (const column of columns) {
          row[column.field].align = align
        }
      }
      this.key++
      this.$nextTick(() => {
        this.updateRowHeight();
        this.updateBorder()
      })
    },
    //列宽设置
    setColWidth (selectionRangeIndexes) {
      const { startColIndex, endColIndex } = selectionRangeIndexes
      const columns = this.columns.filter((item, index) => index >= startColIndex && index <= endColIndex)
      this.$prompt('', '列宽设置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\+?[1-9][0-9]*$/,
        inputErrorMessage: '请输入数字',
        inputValue: columns.length === 1 ? columns[0].width : 100
      }).then(({ value }) => {
        for (const column of columns) column.width = Number(value)
        this.key++
        this.$nextTick(() => {
          this.updateRowHeight()
          this.updateBorder()
        })
      }).catch(() => { })
    },
    //行高设置
    setRowHeight (selectionRangeIndexes) {
      const { startRowIndex, endRowIndex } = selectionRangeIndexes
      const rowspans = this.tableData.filter((item, index) => index >= startRowIndex && index <= endRowIndex)
      this.$prompt('', '行高设置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\+?[1-9][0-9]*$/,
        inputErrorMessage: '请输入数字',
        inputValue: rowspans.length === 1 ? (rowspans[0].height || 40) : 40
      }).then(({ value }) => {
        for (const row of rowspans) row.height = value
        this.updateRowHeight()
        this.updateBorder()
      }).catch(() => { })
    }
  }
}