const Analysis = {
  name: "Analysis",
  template: `<div  :style="{'text-align':align}" :class="classAnswer" style="word-break:break-word" v-html="value" v-katex></div>`,
  props: {
    content: {
      type: Object,
      default: function () {
        return {
          stuAnswer: '',
          answer: ''
        }
      }
    },
    align: String,
  },
  computed: {
    classAnswer () {
      if (this.content.type === 2) {
        const answerList = this.content.answer.split('#')
        if (answerList.includes(this.content.stuAnswer)) return 'success'
        else return 'error'
      }
      return ''
    },
    value () {
      if (this.content.type === 2) return this.content.stuAnswer
      return this.content.value
    }
  }
};
export default Analysis