<template>
  <div class="tableEdit">
    <div class="table-title"><b class="before-icon"></b><b>表格设置</b><span class="des">注：双击单元格编辑内容，鼠标右键可设置表格 </span>
    </div>
    <OperationTable v-model="content" ref="OperationTable" @dblclickEventColumn="dblclickEventColumn"></OperationTable>
  </div>
</template>
<script>
import OperationTable from './operationTable.vue'
import { transformatData, reverseFormatData } from './transformat'
export default {
  name: 'tableEdit',
  components: { OperationTable },
  data () {
    return {
      content: '',
      columnRow: {},
    }
  },
  methods: {
    init (ItemId, item) {
      const { ItemChange: { TableItemInfo } = {} } = item || {}
      const content = transformatData(TableItemInfo)
      this.$nextTick(() => {
        this.$refs.OperationTable.initTableData(content)
      })

    },
    //上交数据
    handInData () {
      const { tableData, columns, merges } = this.content
      return reverseFormatData(tableData, columns, merges)
    },
    //双击单元格
    dblclickEventColumn ({ row, column, rowIndex }) {
      this.columnRow.rowKey = row.rowKey
      this.columnRow.field = column.field
      const { rowKey, field } = this.columnRow
      const { tableData } = this.content
      const data = tableData.find(item => item.rowKey === rowKey)
      this.$refs.EditDialog.init('单元格', data[field].value)
    },
    //赋值表格数据
    updateEditItem (value) {
      const { rowKey, field } = this.columnRow
      const { tableData } = this.content
      const data = tableData.find(item => item.rowKey === rowKey)
      if (!data[field]) data[field] = { value, type: 1 }
      else data[field].value = value
    }
  }
}
</script>
<style scoped lang="less">
.tableEdit {
  margin-bottom: 25px;
  .table-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-bottom: 10px;
    .before-icon {
      width: 5px;
      height: 20px;
      background: #61bb96;
      border-radius: 2px;
      margin-right: 5px;
    }
    .des {
      font-size: 12px;
      margin-left: 10px;
    }
  }
}
</style>