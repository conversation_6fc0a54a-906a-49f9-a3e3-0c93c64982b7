<!-- 可操作的表格组件 -->
<template>
  <div class="table-box">
    <ve-table ref="easytable"  :style="{'width':`${tableWidth/192}rem`}" :key="key"
      row-key-field-name="rowKey" style="word-break:break-word" :columns="columns" :table-data="tableData"
      :cell-style-option="cellStyleOption" :row-style-option="rowStyleOption"
      :contextmenu-body-option="contextmenuBodyOption" :cell-span-option="cellSpanOption"
      :show-header="false" :event-custom-option="eventCustomOption" border-y>
    </ve-table>
  </div>
</template>

<script>
import OtherComp from './OtherComp'
import veTableMinix from './veTableMinix'
export default {
  name: 'operationTable',
  mixins: [veTableMinix],
  model: {
    prop: 'content',
    event: 'change'
  },
  props: {
    content: {
      type: Object | null,
      default: null
    }
  },
  data () {
    return {
      columns: [],
      tableData: [],
      merges: []
    }
  },
  computed: {
    tableWidth () {
      return this.columns.reduce((pre, cur) => {
        return pre + Number(cur.width)
      }, 0)
    }
  },
  created () {
    this.initTableData()
  },
  mounted () {
    this.updateRowHeight()
    this.updateBorder()
  },
  methods: {
    initTableData (content) {
      this.key++
      if (content) {
        const { columns, tableData, merges } = content
        this.columns = columns.map(item => {
          return {
            ...item,
            renderBodyCell: ({ row, column, rowIndex }, h) => {
              const { value, align } = row[column.field];
              return <OtherComp content={value} align={align || 'center'} />
            }
          }
        })
        this.tableData = tableData
        this.merges = merges
        this.change()
        this.$nextTick(() => { this.updateRowHeight(); this.updateBorder() })
        return
      }
      const columns = new Array(4).fill({})
      const tableData = new Array(3).fill({})
      this.columns = columns.map((item, i) => ({
        field: `col${i}`,
        key: i,
        width: 100,
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          const { value, align } = row[column.field];
          return <OtherComp content={value} align={align || 'center'} />
        }
      }))
      this.merges = []
      this.tableData = tableData.map((item, i) => ({ rowKey: i, col0: { value: '', type: 1 }, col1: { value: '', type: 1 }, col2: { value: '', type: 1 }, col3: { value: '', type: 1 } }))
      this.$emit('change', { columns: this.columns, tableData: this.tableData, merges: this.merges })
    },
    change () {
      this.$emit('change', { columns: this.columns, tableData: this.tableData, merges: this.merges })
    },
    updateRowHeight () {
      const NodeList = Array.from(this.$refs.easytable.$refs.tableRef.childNodes[1].childNodes)
      for (const item of this.tableData) {
        if (!item.height) continue
        const childrenNode = NodeList.find(val => val.getAttribute('row-key') === item.rowKey.toString())
        childrenNode.style.height = `${item.height / 192}rem`
      }
    },
    //更新单元格边框  解决最后一列合并多行边框消失的bug
    updateBorder () {
      const trNodeList = this.$refs.easytable.$refs.tableRef.childNodes[1].children
      for (let i = 0; i < trNodeList.length; i++) {
        const tdNodeList = trNodeList[i].children
        const lastNode = tdNodeList[tdNodeList.length - 1]
        const rowspan = lastNode?.getAttribute('rowspan') - 1
        while (rowspan > 0) {
          const tdNodeListNext = trNodeList[i + rowspan].children
          const lastNodeNext = tdNodeListNext[tdNodeListNext.length - 1]
          lastNodeNext?.setAttribute('style', 'border-right: 0.005208rem solid #eee')
          rowspan--
        }
      }
    }
  },
}
</script>
<style>
.table-box {
  overflow-x: auto;
  padding: 0 10px 10px 0;
}
.table-body-cell-class {
  background: #cae8ff !important;
  color: #fff !important;
}
.table-box::-webkit-scrollbar {
  /*滚动条整体样式*/
  /* width: 10px; */
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;
}

.table-box::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: #c1c1c1;
}

.table-box::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}
</style>
<style>
/* .ve-table.ve-table-border-around .ve-table-container table.ve-table-content.ve-table-border-y td:last-child {
  border-right: 1px solid #eee;
} */
</style>