import '@/components/Mathquill/js/mathquill'
const Mathquill = {
  name: "mathquill",
  template: `<div class="input"><div class="mathquill" ref="mathquill"></div></div>`,
  props: ['content'],
  data () {
    return {
      mathField: null
    }
  },
  watch: {
    content () {
      this.mathField.latex(this.content)
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      const _this = this
      this.mathField = MathQuill.MathField(this.$refs.mathquill, {
        restrictMismatchedBrackets: false,
        substituteTextarea: () => {
          const dom = document.createElement('textarea')
          dom.onfocus = () => _this.$emit('onfocus')
          dom.onblur = () => _this.$emit('onChangeValue', _this.mathField?.latex())
          return dom
        },
      })
      this.mathField.latex(this.content)
    }
  }
}
export default Mathquill