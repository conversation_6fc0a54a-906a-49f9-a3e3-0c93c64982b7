/**
 * 后台数据转换前端表格数据格式
 * @param TableItemInfo 
 * @returns 
 */
export function transformatData(TableItemInfo: TableItemInfo[]){
  if (!TableItemInfo) return ''
  const data: TableItemInfo[] = JSON.parse(JSON.stringify(TableItemInfo))
  const tableData: TableData[] = []
  const columns: Colum[] = []
  const merges: Merges[] = []
  data.forEach(item => {
    const { row, column, rowspan, colspan } = item
    if (rowspan > 1 || colspan > 1) merges.push({ startRowIndex: row, endRowIndex: row - 1 + rowspan, startColIndex: column, endColIndex: column - 1 + colspan })
  })
  //查找最大列序号
  let col = 0
  const MaxColum = data.sort((a, b) => b.column - a.column)[0]
  if (MaxColum.colspan > 1) col = MaxColum.column + MaxColum.colspan
  else col = MaxColum.column + 1
  for (let i = 0; i < col; i++) {
    const colum = data.find(item => item.column === i)
    columns.push({ field: `col${i}`, key: i, width: Number(colum?.columnHeight || 100) })
  }
  //查找最大行序号
  let rowIdx = 0
  const MaxRow = data.sort((a, b) => b.row - a.row)[0]
  if (MaxRow.rowspan > 1) rowIdx = MaxRow.row + MaxRow.rowspan
  else rowIdx = MaxRow.row + 1
  for (let i = 0; i < rowIdx; i++) {
    const rowData = data.find(item => item.row === i)
    const obj: otherData = {}
    columns.forEach((item, j) => {
      const cellData = data.find(val => val.row === i && val.column === j)
      const { text: value = '', type = 1, answer = '', stuAnswer = '' } = cellData || {}
      obj[item.field] = { value, type, answer, stuAnswer }
    });
    tableData.push({
      rowKey: i,
      height: rowData?.rowHeight || '',
      ...obj
    })
  }
  return { tableData, columns, merges }
}
/**
 * 转换成后台数据
 * @param tableData 
 * @param columns 
 * @param merges 
 * @returns 
 */
export function reverseFormatData(tableData: TableData[], columns: Colum[], merges: Merges[]) {
  const TableItemInfoTotal: TableItemInfo[] = []
  tableData.forEach((row, i) => {
    columns.forEach((colum, j) => {
      const tableItemValue = row[colum.field] as TableItemValue
      TableItemInfoTotal.push({
        row: i,
        column: j,
        rowspan: 1,
        colspan: 1,
        type: tableItemValue?.type || 1,
        rowHeight: row.height ?? '',
        columnHeight: String(colum.width),
        bgcolor: "",
        text: tableItemValue?.value || '',
        answer: tableItemValue?.type === 2 ? tableItemValue.value : '',
        stuAnswer: ""
      })
    })
  })
  let TableItemInfo = TableItemInfoTotal.map(item => item) //复制一份
  //移除已合并的单元格
  merges.forEach(item => {
    const { startRowIndex, endRowIndex, startColIndex, endColIndex } = item
    TableItemInfo = TableItemInfo.filter(val => !(val.row >= startRowIndex && val.row <= endRowIndex && val.column >= startColIndex && val.column <= endColIndex))
  })
  // 单独把合并单元格加入
  merges.forEach(item => {
    const { startRowIndex, endRowIndex, startColIndex, endColIndex } = item
    const mergeData = TableItemInfoTotal.find(val => startRowIndex === val.row && startColIndex === val.column) as TableItemInfo
    mergeData.rowspan = endRowIndex - startRowIndex + 1
    mergeData.colspan = endColIndex - startColIndex + 1
    TableItemInfo.push(mergeData)
  })
  return TableItemInfo
}

/**单元格数据 */
interface TableItemInfo {
  row: number
  column: number
  rowspan: number
  colspan: number
  type: number
  rowHeight: string
  columnHeight: string
  bgcolor: string
  text: string
  answer: string
  stuAnswer: string
}

/**表格数据 */
interface TableData {
  rowKey: number;
  height: string
  [key: string]: TableItemValue | number | string
}
interface otherData {
  [key: string]: TableItemValue
}
/**单元格的值 */
interface TableItemValue {
  value: string
  type: number
  answer: string
  stuAnswer: string
}

interface Merges {
  startRowIndex: number
  endRowIndex: number
  startColIndex: number
  endColIndex: number
}
interface Colum {
  field: string,
  key: number,
  width: number
}
