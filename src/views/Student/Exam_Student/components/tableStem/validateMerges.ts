/**
 * 校验选区内是否包含合并项
 * @param selectionRangeIndexes 
 * @param merges 
 */
export default function validateMerges(selectionRangeIndexes: RangeIndexes, merges: RangeIndexes[]): boolean {
  const { startRowIndex: Xa1, startColIndex: Ya1, endRowIndex: Xa2, endColIndex: Ya2 } = selectionRangeIndexes
  let flag = false
  for (let i = 0; i < merges.length; i++) {
    const { startRowIndex: Xb1, startColIndex: Yb1, endRowIndex: Xb2, endColIndex: Yb2 } = merges[i]
    flag = Math.abs(Xb2 + Xb1 - Xa2 - Xa1) <= Xa2 - Xa1 + Xb2 - Xb1 && Math.abs(Yb2 + Yb1 - Ya2 - Ya1) <= Ya2 - Ya1 + Yb2 - Yb1
    if (flag) break
  }
  return flag
}

interface RangeIndexes {
  startRowIndex: number
  endRowIndex: number
  startColIndex: number
  endColIndex: number
}