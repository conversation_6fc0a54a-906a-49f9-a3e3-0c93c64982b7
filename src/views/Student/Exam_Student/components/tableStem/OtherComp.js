const OtherComp = {
  name: "OtherComp",
  template: `<div ref="input" :style="{'text-align':align}" @focus="focus"  @input="onChangeValue" :class="{'input':isEdit}" style="word-break:break-word" v-html="content" v-katex :contenteditable="isEdit"></div>`,
  props: {
    content: String,
    align: String,
    isEdit: Boolean
  },
  data () {
    return {
      timer: null
    }
  },
  methods: {
    focus () {
      this.$emit('onfocus')
    },
    onChangeValue (event) {
      const value = event.currentTarget.innerHTML
      this.$nextTick(() => {
        this.$emit('onChangeValue', value)
        this.keepLastIndex()
      })
    },
    keepLastIndex () {
      const dom = this.$refs.input
      if (this.timer) clearTimeout(this.timer)
      if (window.getSelection) {
        this.timer = setTimeout(() => {
          dom.focus()
          const range = window.getSelection()
          range.selectAllChildren(dom)
          range.collapseToEnd()
        }, 0)

      } else if (document.selection) {
        this.timer = setTimeout(() => {
          const range = document.selection.createRange()
          range.moveToElementText(dom)
          range.collapse(false)
          range.select()
        }, 0)
      }
    }
  }
};
export default OtherComp