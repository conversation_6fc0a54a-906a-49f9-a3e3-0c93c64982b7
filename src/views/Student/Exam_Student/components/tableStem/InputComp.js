const InputComp = {
  name: "InputComp",
  template: `<input class="input" v-model="content.stuAnswer" :disabled="disabled" @change="onChangeValue" @focus="focus" autocomplete="off"
  type="text" style="width:100%;height:100%;border: 0px; border-radius:4px;
  color:#000; text-align:center; border-bottom:1px solid grey;background:rgba(137, 225, 186, 0.4)">`,
  props: {
    disabled: Boolean,
    content: {
      type: Object,
      default: function () {
        return {
          stuAnswer: ''
        }
      }
    },
  },
  data () {
    return {

    }
  },
  methods: {
    focus () {
      this.$emit('onfocus')
    },
    onChangeValue () {
      this.$nextTick(() => this.$emit('onChangeValue', this.content.stuAnswer))
    }
  }

};
export default InputComp