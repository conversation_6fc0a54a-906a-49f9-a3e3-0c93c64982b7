<!-- 纯渲染表格组件 -->
<template>
  <div class="table-box">
    <ve-table
      ref="renderTable"
      :style="{ width: `${tableWidth / 192}rem` }"
      :key="key"
      row-key-field-name="rowKey"
      style="word-break: break-word"
      :columns="columns"
      :table-data="tableData"
      :cellSelectionOption="cellSelectionOption"
      :cell-style-option="cellStyleOption"
      :cell-span-option="cellSpanOption"
      :row-style-option="rowStyleOption"
      :show-header="false"
      border-y
    >
    </ve-table>
  </div>
</template>
<script>
import { VeTable } from 'vue-easytable'
import OtherComp from './OtherComp'
import Mathquill from './Mathquill'
import '@/components/Mathquill/js/mathquill.css'
import InputComp from './InputComp'
import Analysis from './Analysis'
import { transformatData } from './transformat'
export default {
  name: 'RenderTable',
  components: { VeTable },
  props: ['TableItemInfo', 'showAnswer', 'analysis', 'showTitle','isUserAnswer'],
  data() {
    return {
      key: 0,
      cellStyleOption: {
        bodyCellClass: ({ row, column, rowIndex }) => {
          const { field } = column
          if (row[field]?.type === 2 && this.showAnswer) return 'table-body-cell-class'
          return ''
        },
      },
      cellSelectionOption: {
        enable: false,
      },
      rowStyleOption: {
        clickHighlight: false,
        hoverHighlight: false,
      },
      cellSpanOption: {
        bodyCellSpan: this.bodyCellSpan,
      },
      columns: [],
      tableData: [],
      merges: [],
    }
  },
  computed: {
    spans() {
      const obj = {}
      this.merges.forEach((item) => {
        const { startRowIndex, endRowIndex, startColIndex, endColIndex } = item
        for (let i = startRowIndex; i <= endRowIndex; i++) {
          for (let j = startColIndex; j <= endColIndex; j++) {
            const flag = i === startRowIndex && j === startColIndex
            obj[`${i}-${j}`] = {
              rowspan: flag ? endRowIndex - startRowIndex + 1 : 0,
              colspan: flag ? endColIndex - startColIndex + 1 : 0,
            }
          }
        }
      })
      return obj
    },
    tableWidth() {
      return this.columns.reduce((pre, cur) => {
        return pre + Number(cur.width)
      }, 0)
    },
  },
  mounted() {
    const { tableData = [], columns = [], merges = [] } = transformatData(this.TableItemInfo)
    this.tableData = tableData
    this.columns = columns.map((item) => {
      return {
        ...item,
        renderBodyCell: ({ row, column, rowIndex }, h) => {
          const { value, align, type, answer, stuAnswer } = row[column.field]
          if (this.analysis || type == 1) return <Analysis content={row[column.field]} />
          if (
            type === 2 &&
            !this.showAnswer &&
            !this.showTitle &&
            (stuAnswer.includes('\\') || stuAnswer.includes('^'))
          )
            return (
              <Mathquill
                content={row[column.field].stuAnswer}
                {...{
                  on: {
                    onChangeValue: (value) => this.onChangeValue(value, row, column),
                    onfocus: () => this.onfocus(row, column),
                  },
                }}
              />
            )
          if (type === 2 && !this.showAnswer)
            return (
              <InputComp
                content={this.showTitle ? {} : row[column.field]}
                disabled={this.showTitle || this.isUserAnswer}
                {...{
                  on: {
                    onChangeValue: (value) => this.onChangeValue(value, row, column),
                    onfocus: () => this.onfocus(row, column),
                  },
                }}
              />
            )
          return (
            <OtherComp
              content={type === 1 ? value : this.showAnswer ? answer : row[column.field].stuAnswer}
              align={align || 'center'}
              {...{
                on: {
                  onChangeValue: (value) => this.onChangeValue(value, row, column),
                  onfocus: () => this.onfocus(row, column),
                },
              }}
            />
          )
        },
      }
    })
    this.merges = merges
    this.$nextTick(() => {
      this.updateRowHeight()
      this.updateBorder()
      //第一个输入框聚焦
      const inputDomList = $('.table-box .input')
      let falg = true
      if (inputDomList.length) {
        for (let i = 0; i < inputDomList.length; i++) {
          if (inputDomList[i].value === '') {
            falg = false
            inputDomList[i].focus()
            break
          }
        }
        if (falg) {
          inputDomList[0].focus()
          inputDomList[0].children[0]?.children[0]?.children[0]?.focus()
        }
      }
    })
  },
  methods: {
    onfocus(row, column) {
      this.$emit('onfocus', { row, column })
    },
    onChangeValue(value, row, column) {
      const tableItem = this.TableItemInfo.find((item) => item.row === row.rowKey && item.column === column.key)
      const tableDataItem = this.tableData.find((item) => item.rowKey === row.rowKey)
      tableDataItem[column.field].stuAnswer = value
      tableItem.stuAnswer = value
      const answerList = this.TableItemInfo.filter((item) => item.type === 2)
      const updatefinishStatus = answerList.every((item) => item.stuAnswer)
      this.$emit('updatefinishStatus', updatefinishStatus)
    },
    bodyCellSpan({ row, column, rowIndex }) {
      const colIndex = this.columns.findIndex((item) => item.key === column.key)
      const data = this.spans[`${rowIndex}-${colIndex}`]
      if (data) return data
    },
    updateRowHeight() {
      const NodeList = Array.from(this.$refs.renderTable.$refs.tableRef.childNodes[1].childNodes)
      for (const item of this.tableData) {
        if (!item.height) continue
        const childrenNode = NodeList.find((val) => val.getAttribute('row-key') === item.rowKey.toString())
        childrenNode.style.height = `${item.height / 192}rem`
      }
    },
    //更新单元格边框  解决最后一列合并多行边框消失的bug
    updateBorder() {
      const trNodeList = this.$refs.renderTable.$refs.tableRef.childNodes[1].children
      for (let i = 0; i < trNodeList.length; i++) {
        const tdNodeList = trNodeList[i].children
        const lastNode = tdNodeList[tdNodeList.length - 1]
        let rowspan = lastNode?.getAttribute('rowspan') - 1
        while (rowspan > 0) {
          const tdNodeListNext = trNodeList[i + rowspan].children
          const lastNodeNext = tdNodeListNext[tdNodeListNext.length - 1]
          lastNodeNext?.setAttribute('style', 'border-right: 0.005208rem solid #eee')
          rowspan--
        }
      }
    },
  },
}
</script>
<style scoped>
.table-box {
  overflow-x: auto;
  padding: 0 10px 10px 0;
}

.table-box
  >>> .ve-table
  .ve-table-container
  .ve-table-content-wrapper
  table.ve-table-content
  tbody.ve-table-body
  tr.ve-table-body-tr
  td.ve-table-body-td {
  padding: 3px;
}
.table-box >>> .ve-table-body-td .input {
  background: rgba(137, 225, 186, 0.4);
  height: 100%;
  line-height: 35px;
  border-bottom: 1px solid;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-box >>> .ve-table-body-td .success {
  background: #61bb96;
}
.table-box >>> .ve-table-body-td .error {
  background: #da1b1b;
}
.table-box >>> .table-body-cell-class {
  background: #cae8ff !important;
  color: #fff !important;
}
.table-box::-webkit-scrollbar {
  /*滚动条整体样式*/
  /* width: 10px; */
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 10px;
}

.table-box::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  background-color: #c1c1c1;
}

.table-box::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 10px;
}
</style>
<style >
.ve-table-body-td > div {
  font-size: 18px;
}
.ve-table.ve-table-border-around {
  border-color: #999;
  border-right: none;
  border-bottom: none;
}
.ve-table
  .ve-table-container
  .ve-table-content-wrapper
  table.ve-table-content
  tbody.ve-table-body
  tr.ve-table-body-tr
  td.ve-table-body-td {
  border-bottom: 1px solid #999;
  border-right: 1px solid #999;
}
</style>