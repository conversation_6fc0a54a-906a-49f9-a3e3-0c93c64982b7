.subject-view {
  //position: absolute;
  //top: 0;
  //bottom: 0;
  //left: 0;
  //right: 0;
  //background: linear-gradient(180deg, #eff7fd 0%, #e5e9fd 100%);
  background: #FFFFFF;
  border-radius: 6px;
  overflow-x: hidden;
  .top-view {
    //position: absolute;
    //top: 0;
    //bottom: 0;
    //left: 0;
    //right: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .img1 {
      width: 180px;
      height: 180px;
    }
    .img2 {
      margin: 10px 0;
      width: 150px;
      height: 50px;
    }
  }
  .ai-content {
    padding-bottom: 20px;
    height: 100%;
    overflow-x: hidden;
    ::v-deep img{
      max-width: 100%;
    }
    .top-view1 {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #DCDCDCFF;
      padding: 10px 20px;
      img{
        width: 30px;
        margin-right: 6px;
      }
      span {
        font-size: 18px;
        background: linear-gradient(to right, #9BDDFE, #A1B7FE, #CB9CFE);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .el-button{
        font-size: 16px;
      }
      //padding: 20px;
      //display: flex;
      //justify-content: space-between;
      //.student-name {
      //  font-size: 25px;
      //  font-weight: bold;
      //  background-image: linear-gradient(0deg, #9BDDFE 0%, #A1B7FE 48%, #CB9CFE 100%);
      //  background-clip: text;
      //  -webkit-background-clip: text; /* 兼容 WebKit 内核浏览器 */
      //  -webkit-text-fill-color: transparent; /* 文字颜色透明 */
      //}
      //.pdf-btn{
      //  background: linear-gradient( 90deg, #9BDDFE 0%, #A1B7FE 48%, #CB9CFE 100%), #FFFFFF;
      //  color: white;
      //}
    }
  }
  .top-view2{
    display: flex;
    justify-content: center;
    padding-top: 10px;
    .radar-view{
      width: 320px;
      // height: 350px;
      margin-left: 10px;
    }
    .star-view{
      //flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .star-view1{
        // margin: 20px 0;
        color: #308DFF;
        font-size: 20px;
        height: 80px;
      }
      .score{
        font-family: Bahnschrift, Bahnschrift;
        font-weight: 400;
        font-size: 60px;
      }
      .score2{
        font-family: dengdi;
        font-weight: 400;
        font-size: 54px;
      }
    }
  }
  .circle-container {
    // flex: 1;
    position: relative;
    .knowledge-view{
      position: absolute;
      top: -5px;
      width: 100%;
      text-align: center;
      font-size: 20px;
      color: #666262;
    }
    .score-wraper {
      position: relative;
    }
    #score,
    #score1 {
      width: 240px;
      height: 240px;
    }
    .wyuan {
      width: 150px;
      height: 150px;
      border-radius: 50%;
      position: absolute;
      left: 50%;
      top: 50%;
      z-index: 90;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      // &::after{
      //   content: '';
      //   width: 175px;
      //   height: 175px;
      //   border-radius: 50%;
      //   border: 5px solid #F7F7F7;
      //   position: absolute;
      //   z-index: -1;
      // }
    }
    .yuan {
      width: 140px;
      height: 140px;
      border-radius: 50%;
      box-shadow: 0px 0px 10px 0px rgba(84, 112, 198, 0.8);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      p {
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        color: white;
      }
      p:first-child {
        font-size: 26px;
        margin-bottom: 6px;
      }
      .text-color {
        color: white;
      }
    }
  }
  .ai-back-view{
    margin: 20px;
    margin-top: 0;
    margin-bottom: 20px;
    h1{
      font-size: 24px;
    }
    .ai-text {
      margin-top: 5px;
      position: relative;
      background: rgba(86,116,246,0.07);
      padding: 15px 20px;
      font-size: 22px;
      img{
        width: 100%;
      }
      .modelssay-view{
        position: absolute;
        width: 120px;
        height: 40px;
        bottom: 30px;
        left: calc((100% - 120px)/2);
      }
    }
  }
  // .ai-back-view:first-child{
  //   margin-top: 0;
  // }
  .star-list-view1{
    width: 100%;
    display: flex;
    // flex-direction: column;
    // align-items: center;
    flex-wrap:wrap;
    align-items: center;
    justify-content: flex-start;
    .demonstration{
      margin-right: 5px;
      margin-bottom: 5px;
      width: 60px;
      text-align: right;
      color: #666262;
      font-size: 18px;
    }
    .star-view2{
      display: flex;
      width: 50%;
    }
  }
  //.star-list-view{
  //  width: 100%;
  //  display: flex;
  //  flex-wrap:wrap;
  //  align-items: center;
  //  justify-content: flex-start;
  //  .star-view2{
  //    display: flex;
  //    width: 50%;
  //  }
  //}
  .editor-container {
    height: 400px;
    border: 1px solid rgb(204, 204, 204);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    &.none {
      border: none;
      flex: auto;
      height: auto;
    }
    // ::v-deep .w-e-scroll {
    //   overflow-y: inherit !important;
    // }
  }
  ::v-deep .el-input-number {
    .el-input-number__decrease,
    .el-input-number__increase {
      display: none;
    }
    .el-input {
      width: 200px;
    }
  }
  //.back-stu{
  //  position: absolute;
  //  width: 100%;
  //  height: 36px;
  //  bottom: 0;
  //  display: flex;
  //  flex-direction: column;
  //  align-items: center;
  //  background: black;
  //  img{
  //    width: 172px;
  //    height: 36px;
  //  }
  //}
  .caret-view{
    margin-left: 5px;
    width: 20px;
    height: 20px;
    border-radius: 10px;
    color: #308DFF;
    border: 1px solid #308DFF;
    text-align: center;
    line-height: 20px;
  }
  ::v-deep code{
    white-space: pre-wrap; /* 保留换行和空格，并在必要时换行 */
    word-wrap: break-word;  /* 允许长单词或URL换行 */
  }
  ::v-deep .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: scroll;
  }
  //.full-btn{
  //  margin-left: 5px;
  //  font-size: 20px;
  //}
}
.big-detail{
  position: fixed;
  z-index: 99;
  right: 10%;
  bottom: 100px;
  width: 80%;
  height: 75%;
  border-radius: 10px;
  background: linear-gradient(179deg, #ebf9ff 0%, #ebf1ff 48%, #f5ebff 100%), #ffffff;
  box-shadow: 0px 4px 4px 4px rgba(0, 0, 0, 0.07);
  border: 2px solid #fff;
  padding: 20px;
  .close-view{
    padding: 5px 20px;
    display: flex;
    justify-content: flex-end;
    .close-btn{
      background-color: white;
      color: #666262;
      padding: 10px 25px;
      cursor: pointer;
    }
  }
}
.dialog-body2{
  .title-view{
    display: flex;
    align-items: center;
    font-size: 32px;
    img{
      width: 98px;
      height: 114px;
    }
  }
  .content-view{
    padding: 20px;
    font-size: 30px;
  }
}