<template>
  <div v-show="isShow" v-loading="Loading" element-loading-text="Ai识别中..." class="subject-view">
      <div v-show="showAi" class="top-view">
        <img class="img1" src="@/assets/aigif.gif" alt="" />
        <div>Ai正全力批改作业，部分内容仍在处理中。您可先查看已完成批改的学生作业！</div>
      </div>
      <div v-show="!showAi" class="ai-content">
        <el-scrollbar ref="scrollbarRef" style="height: 100%;">
          <div class="top-view1" style="justify-content: space-between;">
            <div>
            <img src="@/assets/AI.png" />
            <span>智能分析</span>
            </div>
            <el-button type="text" @click="exportClick">导出word</el-button>
          </div>
          <div ref="imageWrapper">
            <div class="top-view2">
              <!-- 雷达图 -->
              <div class="radar-view" id="radarChart"></div>
              <!-- 星星 -->
              <div class="star-view">
                <el-radio-group v-model="radio" size="small">
                  <el-radio-button label="等第" style="font-size: 18px;"></el-radio-button>
                  <el-radio-button label="得分" style="font-size: 18px;"></el-radio-button>
                </el-radio-group>
                <div v-show="radio == '等第'" class="star-view1">
                  <span class="score2">{{ RankInfo.Title }}</span>
<!--                  <i class="el-icon-edit" @click="showScore = true"></i>-->
                </div>
                <div v-show="radio == '得分'" class="star-view1">
                  <span class="score">{{ ScoreInfo.Title }}</span>
                  <span>分</span>
                </div>
                <div class="star-list-view1">
                  <div class="star-view2" v-for="item in ScoreInfo.Items">
                    <div class="demonstration" style="font-size: 18px;">{{ item.Name }}</div>
                    <el-rate v-model="item.star" disabled disabled-void-color="#B2B2B2"></el-rate>
                  </div>
                </div>
              </div>
              <!-- 圆 -->
              <div class="circle-container">
                <div class="knowledge-view" style="font-size: 18px;">{{ KnowledgePoints }}</div>
                <div class="score-wraper">
                  <div id="score"></div>
                  <div class="wyuan">
                    <div class="yuan">
                      <p>{{ Master }}%</p>
                      <p>掌握度</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="!item.export" class="ai-back-view" v-for="(item, index) in PromptAnalysisList">
              <div class="flex flex_space_between">
                <div class="flex flex_align_center">
                  <h1>{{ item.Title }}</h1>
                  <div class="caret-view el-icon-caret-bottom" @click="showItem(item)"></div>
                </div>
              </div>
              <div v-show="item.show">
                <!-- <div v-if="item.Value?.length" v-html="md.render(item.Value)" class="ai-text"></div> -->
                <MarkdownRenderer v-if="item.Value?.length" class="ai-text" :html="md.render(item.Value)" />
                <div v-else class="ai-text">暂无内容</div>
              </div>
            </div>
            <div v-if="EvaluationInfo.content.length">
              <div v-show="!EvaluationInfo.export" class="ai-back-view">
                <div class="flex flex_align_center">
                  <h1>短句精调</h1>
                  <div class="caret-view el-icon-caret-bottom" @click="duanjshow = !duanjshow"></div>
                </div>
                <div v-show="duanjshow">
                  <div v-for="item in EvaluationInfo.content" :key="item.id">
                    <!-- <div v-html="md.render(item.Ask)" class="ai-text"></div>
                    <div v-html="md.render(item.Answer)" class="ai-text"></div> -->
                    <MarkdownRenderer class="ai-text" :html="md.render(item.Ask)" />
                    <MarkdownRenderer class="ai-text" :html="md.render(item.Answer)" />
                  </div>
                </div>
              </div>
            </div>
            <div v-show="!modelssayInfo.export" class="ai-back-view">
              <div class="flex flex_align_center">
                <h1>范文推荐</h1>
                <div class="caret-view el-icon-caret-bottom" @click="fanwShow = !fanwShow"></div>
              </div>
              <div v-show="fanwShow">
                <div v-if="!modelssayInfo.content" v-loading="modelssayInfo.loading" class="ai-text">
                  <img src="@/assets/recommendBg.png" alt="" style="cursor: pointer;" @click="modelssayClick"/>
                </div>
                <!-- <div v-else v-html="md.render(this.obj.output)" class="ai-text"></div> -->
                <MarkdownRenderer v-else class="ai-text" :html="md.render(this.obj.output)" />
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import EasyTyper from 'easy-typer-js'
import documentExport from '@/utils/documentExport'
// import uuId from 'uuid'
import MarkdownIt from 'markdown-it'
import mindmapPlugin from '@/views/Student/Exam_Student/utils/markdown-it-plugins/markmap/mindmap'
import latexPlugin from '@/views/Student/Exam_Student/utils/markdown-it-plugins/latex'
import mermaidPlugin from '@/views/Student/Exam_Student/utils/markdown-it-plugins/mermaid'
import MarkdownRenderer from '@/views/Student/Exam_Student/components/markdown/MarkdownRenderer.vue'

import echartsPlugin from '@/views/Student/Exam_Student/utils/markdown-it-plugins/echarts'
// import hljs from 'highlight.js'
// import 'highlight.js/styles/atom-one-dark.css'
// import RunIframe from '@/views/Student/Exam_Student/components/RunIframe.vue'

const md = MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  // highlight: function(str, lang) {
  //   const isHTML = hljs.getLanguage(lang)?.name === 'HTML, XML'
  //   if (isHTML && lang && hljs.getLanguage(lang)) {
  //     try {
  //       const codeId = `code-${uuId.v4()}`
  //       const runBtnHTML = isHTML ? `<div class="run-btn" data-code-id="${codeId}">点击运行</div>` : ''
  //       return `<pre class="hljs">${runBtnHTML}<code id="${codeId}">${hljs.highlight(str, { language: lang }).value}</code></pre>`
  //     } catch (__) {
  //     }
  //   }
  //   // 使用默认的转义器
  //   return ''
  // }
})

// 思维导图
md.use(mindmapPlugin)

// 保护LaTeX公式不被Markdown处理
md.use(latexPlugin)

// mermaid图表展示
md.use(mermaidPlugin)

// echarts图表展示
md.use(echartsPlugin)

// 超链接渲染函数
md.renderer.rules.link_open = (tokens, idx, options, env, self) => {
  const token = tokens[idx]
  const href = token.attrGet('href')

  // 检查 href 是否是纯数字
  if (/^\d+$/.test(href)) {
    token.attrSet('data-uid', href)
    token.attrSet('href', '#')
  }

  return self.renderToken(tokens, idx, options)
}

export default {
  components: {
    MarkdownRenderer,
    // RunIframe
  },
  props: {},
  computed: {
    paperId() {
      return this.$store.state.answer.questionInfo.paperId
    },
    studentInfo() {
      return this.$store.state.answer.questionInfo.studentItem
    }
  },
  data() {
    return {
      time: null,
      md: Object.freeze(md),
      // emptyImg,
      isShow: false,
      Loading: false,
      showAi: false,
      easyTyper: null,
      eyeFlg: true,
      duanjshow: false,
      // fanwShow: false,
      fanwShow: true,
      bigItem: null,
      obj: {
        output: '',
        isEnd: false,
        speed: 20,
        singleBack: false,
        sleep: 0,
        type: 'normal',
        backSpeed: 40,
        sentencePause: false
      },
      radio: '等第',
      Master: 40, //环形图值
      KnowledgePoints: '', //重点知识点
      PromptAnalysisList: [],
      RankInfo: {},
      ScoreInfo: {},
      radarChart: null,
      EvaluationInfo: {
        Title: '短句精调',
        export: false,
        content: []
      },
      //范文推荐
      modelssayInfo: {
        Title: '范文推荐',
        loading: false,
        content: null,
        export: false,
        IsRecommend: false
      },
    }
  },
  created() {
    if (this.easyTyper) {
      this.easyTyper.close()
      this.easyTyper = null
    }
    this.modelssayInfo.loading = false
    // return false
    this.getIsRecommend()
    this.GetInstantStuLogOcr()
  },
  mounted() {},
  methods: {
    // 导出操作
    async exportClick () {
      const { data:res } = await documentExport.post('/MDPen/MDPen/GetStudentAnalysisReport', {
        paperId: this.$route.query.PaperId,
        classId: localStorage.getItem('StClassId'),
        studentId: localStorage.getItem('UserId')
      })
      this.fileExport(res)
    },
    // 文件导出方法
    fileExport(res){
      let url = window.URL.createObjectURL(res);
      let eleLink = document.createElement('a');
      eleLink.href = url
      eleLink.download = '分析报告.zip'
      document.body.appendChild(eleLink)
      eleLink.click()
      document.body.removeChild(eleLink)
      window.URL.revokeObjectURL(url)
    },
    showItem(item) {
      item.show = !item.show
      this.$forceUpdate()
    },
    /**
     * 个人报告
     * */
    async GetInstantStuLogOcr() {
      const params = {
        // PaperId: this.paperId,
        PaperId: this.$route.query.PaperId,
        // StudentId: this.studentInfo?.StudentId
        StudentId: localStorage.getItem('UserId')
      }
      const res = await this.$uwonhttp.post('/MDPen/MDPen/GetStudentAnalysisReportWrap', params)
      const { Data, Msg, Success } = res.data
      if (Success && Data) {
        // this.GetHaoweilaiX()
        const { Master, PromptAnalysisList, RankInfo, ScoreInfo, IsGenerateSuccess, KnowledgePoints } = Data
        if (IsGenerateSuccess) {
          //生成成功
          this.showAi = false
          this.PromptAnalysisList = PromptAnalysisList.map(item => {
            item.show = true
            item.Value = item.Value?.replace(/\n/g, '\n\n')
            return item
          })
          this.KnowledgePoints = KnowledgePoints
          this.Master = Master
          this.RankInfo = RankInfo
          let sum = 0
          ScoreInfo.Items.map(item => {
            item.star = ((item.Score * 100) / item.MaxScore / 100) * 5
            sum += item.Score
            return item
          })
          ScoreInfo.Title = sum
          this.ScoreInfo = ScoreInfo
          // 获取雷达图
          this.setRadar()
          // 水球分析图
          this.drawScore1('score')
          // this.textHtml = this.PromptAnalysisList[0]?.Value
          await this.GetEvaluationList()
          await this.GetRecommendSampleArticles()
          // this.makeVideoAndImage()
          setTimeout(() => {
            this.radarChart.resize()
          }, 500)
        } else {
          this.showAi = true
          clearInterval(this.time)
          this.time = setInterval(() => {
            this.GetInstantStuLogOcr()
          }, 5 * 1000)
        }
      } else {
        this.$message.error(Msg)
      }
      this.isShow = true
    },
    setRadar() {
      this.radarChart = echarts.init(document.getElementById('radarChart'))
      let indicator = []
      let seriesValue = []
      let tooltipStr = ''
      this.ScoreInfo.Items.forEach(item => {
        indicator.push({ name: item.Name, max: 100 })
        seriesValue.push((item.Score * 100) / item.MaxScore)
        tooltipStr += `<br>${item.Name}：${item.Score}分`
      })
      const option = {
        color: ['#308DFF'],
        textStyle: {
          color: ['#CCCCCC'],
          fontSize: 10
        },
        tooltip: {
          trigger: 'item',
          confine: true,
          textStyle: {
            color: '#fff',
            fontSize: 20
          },
          formatter: function(params) {
            // 自定义提示框内容
            return `${params.name}${tooltipStr}`
          }
        },
        radar: {
          shape: 'circle', // 设置为圆形
          name: {
            show: true,
            fontSize: 20,
            color: '#666262',
            fontWeight: 'bold',  // 设置字体加粗
            formatter: text => {
              return text.replace(/./g, (match, i) => {
                if (i ===4) {
                  return '\n' + match
                } else {
                  return match
                }
              })
            }
          },
          nameGap: 5, //Distance of process equidistant diagram in the diagram
          center: ['50%', '50%'],
          radius: '70%',
          splitNumber: 4,
          indicator,
          splitArea: {
            areaStyle: {
              color: ['#EFF3FF']
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.2)'
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.2)'
            }
          }
        },
        grid: {
          bottom: '0'
        },
        series: [
          {
            type: 'radar',
            symbolSize: 4,
            itemStyle: {
              normal: {
                // 普通状态时的样式
                lineStyle: {
                  width: 1
                },
                opacity: 0.2
              },
              emphasis: {
                //Style when highlighted
                lineStyle: {
                  width: 2
                },
                opacity: 1
              }
            },
            lineStyle: {
              color: '#308DFF'
            },
            areaStyle: {
              color: '#308DFF',
              opacity: 0.2
            },
            tooltip: {
              trigger: 'item'
            },
            data: [
              {
                value: seriesValue,
                name: '得分'
              }
            ]
          }
        ]
      }
      this.radarChart.setOption(option)
    },
    drawScore1(id) {
      // 数据
      const score = this.Master
      //const dataValArray = 0.72
      const r = echarts.init(document.getElementById(id))
      // console.log(r)
      r.setOption({
        angleAxis: {
          max: 100, //Full marks
          clockwise: false, //Counterclockwise
          //Hide Ttick marks
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        radiusAxis: {
          type: 'category',
          //Hide Ttick marks
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        polar: {
          center: ['50%', '50%'],
          radius: '140%' //Graphics Size
        },
        series: [
          {
            // 圆角环形图
            type: 'bar',
            data: [
              {
                value: score,
                itemStyle: {
                  normal: {
                    color: '#1890FF'
                  }
                }
              }
            ],
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: 5,
            barGap: '-100%', //Two rings overlap
            z: 2
          },
          {
            // 起始点的圆点
            name: 'Line 1',
            type: 'pie',
            startAngle: 270,
            clockWise: true,
            radius: ['67%', '67%'], //Control the size of the ring
            itemStyle: {
              normal: {
                label: {
                  show: true
                },
                labelLine: {
                  show: false
                }
              }
            },
            hoverAnimation: false,
            data: [
              {
                name: '',
                value: 0,
                //Size of starting point
                label: {
                  position: 'inside',
                  backgroundColor: '#1890FF',
                  width: 1,
                  height: 1,
                  borderRadius: 50,
                  padding: 4
                }
              }
            ]
          },
          {
            //Overlap Shadow
            type: 'bar',
            data: [
              {
                value: 100,
                itemStyle: {
                  normal: {
                    color: '#F7F7F7'
                  }
                }
              }
            ],
            coordinateSystem: 'polar',
            roundCap: true,
            barWidth: 3,
            barGap: '-100%', //Two rings overlap
            z: 1
          },
          // 水波求
          {
            type: 'liquidFill',
            data: [score / 100],
            radius: '58.7%',
            backgroundStyle: {
              //Background Style
              borderWidth: 5,
              color: '#91C2FE', // 水球未到的背景颜色
              opacity: 0.5 //水球未到的背景颜色的透明度
            },
            outline: {
              show: false //是否显示外边线
            },
            itemStyle: {
              color: '#1890FF',
              shadowBlur: 0 //波浪的阴影范围
            },
            label: {
              show: false
            }
          }
        ]
      })
    },
    /**
     * 获取评价
     */
    async GetEvaluationList() {
      const res = await this.$uwonhttp.post('/MDPen/MDPen/GetEvaluationList', {
        // PaperId: this.paperId,
        PaperId: this.$route.query.PaperId,
        // StudentId: this.studentInfo?.StudentId
        StudentId: localStorage.getItem('UserId')
      })
      const { Data, Msg, Success } = res.data
      this.EvaluationInfo.content = Data.map(item => {
        item.Ask = `【原文】${item.Ask?.replace(/\n/g, '\n\n')}`
        item.Answer = `【精调】${item.Answer?.replace(/\n/g, '\n\n')}`
        return item
      })
    },
    /**
     * 获取范文推荐
     */
    async GetRecommendSampleArticles(e) {
      // if (!this.studentInfo || this.studentInfo.MDType == 0) {
      //   return
      // }
      const res = await this.$uwonhttp.post('/MDPen/MDPen/GetRecommendSampleArticles', {
        // PaperId: this.paperId,
        PaperId: this.$route.query.PaperId,
        // StudentId: this.studentInfo?.StudentId
        StudentId: localStorage.getItem('UserId')
      })
      const { Data, Msg, Success } = res.data
      if (Success) {
        //有推荐范文
        this.modelssayInfo.content = Data?.Content?.replace(/\n/g, '\n\n')
        this.obj.output = this.modelssayInfo.content

        if (!this.obj.output && this.modelssayInfo.IsRecommend) {
          this.modelssayInfo.loading = true
          setTimeout(() => {
            this.GetRecommendSampleArticles(true)
          }, 5 * 1000)
        }

        if (e && this.obj.output) {
          this.easyTyper = new EasyTyper(this.obj, this.modelssayInfo.content, null, () => {
            this.$nextTick(() => {
              this.$refs.scrollbarRef.wrap.scrollTop = this.$refs.scrollbarRef.wrap.scrollHeight
            })
          })
        }
      } else {
        this.$message.error(Msg)
      }
    },
    /**
     * 识别范文推荐
     */
    async modelssayClick() {
      console.log('执行了')
      this.modelssayInfo.loading = true
      const res = await this.$uwonhttp.post('/MDPen/MDPen/RecommendSampleArticles', {
        // PaperId: this.paperId,
        PaperId: this.$route.query.PaperId,
        // StudentId: this.studentInfo?.StudentId
        StudentId: localStorage.getItem('UserId')
      })
      this.modelssayInfo.IsRecommend = true
      this.GetRecommendSampleArticles(true)
    },
    /**
     * 是否获取过范文
     */
    async getIsRecommend() {
      const res = await this.$uwonhttp.post('/MDPen/MDPen/GetIsRecommendSampleArticlesed', {
        // PaperId: this.paperId,
        PaperId: this.$route.query.PaperId,
        // StudentId: this.studentInfo?.StudentId
        StudentId: localStorage.getItem('UserId')
      })
      const { Data, Msg, Success } = res.data
      this.modelssayInfo.IsRecommend = Data.IsRecommend
    }
  }
}
</script>

<style lang="less" scoped>
@font-face {
  font-family: 'dengdi';
  src: url('./dengdi.ttf');
}
@import './index.less';
</style>
<!--<style src="@wangeditor/editor/dist/css/style.css" />-->
