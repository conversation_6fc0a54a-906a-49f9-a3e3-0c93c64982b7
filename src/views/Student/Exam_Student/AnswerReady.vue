<template>
  <div>
    <div class="AnswerReady_top">
      <span class="Goback pc_font_size18 font_size20 ipad_font_size24" @click="GoBack"><i class="el-icon-arrow-left"></i>返回</span>
      <p class="answer-ready pc_font_size20 font_size22 ipad_font_size26">答题准备</p>
      <div class="xuekeStyle pc_font_size16 font_size18 ipad_font_size20" :style="{'background-color':bgColog[Multidisciplinary.SubjectId+'bg'],'color':bgColog[Multidisciplinary.SubjectId]}">
        {{ Multidisciplinary.name }}
      </div>
    </div>
    <div class="answer-careful">
      <p><img class="ipad_img300 img180" src="@/assets/student/倒计时按钮.png" alt=""></p>
      <span v-if="RoruterShowType != '2' " class="PaperName pc_font_size22 font_size24 ipad_font_size26">{{ paperName }}</span>
      <span v-if="RoruterShowType == '2' " class="PaperName pc_font_size22 font_size24 ipad_font_size26">跟进练习——{{ paperName }}</span>
      <div style="line-height:30px;color:orange;" class="pc_font_size22 font_size24 ipad_font_size28" v-if="IsExistSubjectItem || ShowType=== 'true' || IsSolutions"><b>注意：内含主观题/核心素养题，请使用可拍照的设备答题。</b></div>
<!--      <div class="answer_content" v-show="this.ShowType==='false' && this.isShowType !='2' && this.isShowType!='1'">-->
      <div class="answer_content" v-show="this.isShowType !='2' && this.isShowType!='1'">
        <span class="pc_font_size18 font_size20 ipad_font_size22">1.点击开始答题按钮，开始计时</span>
        <br />
        <span class="pc_font_size18 font_size20 ipad_font_size22">2.做题过程中不可退出，退出后不保存已完成的答题记录</span>
      </div>

<!--      <div style="margin-top: 20px;" v-show="this.ShowType==='false'">-->
<!--      <div style="margin-top: 20px;" v-show="_isMobile()">-->
<!--      <div style="margin-top: 20px;" v-show="IsExistSubjectItem && _isMobile() || !IsExistSubjectItem && !_isMobile() || IsPhotograph == 1">-->
      <div style="margin-top: 20px;" v-show="IsExistSubjectItem && _isMobile() || !IsExistSubjectItem && !IsSolutions || IsSolutions && _isMobile() || IsPhotograph == 1">
        <p style="color:#68BB97;" class="pc_font_size20 font_size22 ipad_font_size24">准备好了吗？那就点击下面按钮，开始答题吧～</p>
        <p><span class="answer-btn cur pc_font_size18 font_size22 ipad_font_size24" @click="toAnswerPaper">{{ IsPhotograph == 1 ? '拍照上传' : '开始答题' }}</span></p>
      </div>
<!--      <div class="Teacher_box" v-show="IsExistSubjectItem && !_isMobile()">-->
<!--        <span class="notes">本套练习是拍照的题型，请在可拍照设备上答题哦～</span>-->
<!--      </div>-->
      <div style="margin-top: 20px;" v-show="IsExistSubjectItem && !_isMobile() || IsSolutions && !_isMobile()">
        <p><span class="answer-btn cur pc_font_size18 font_size22 ipad_font_size24" @click="GoBack">返回练习</span></p>
      </div>
<!--      <div class="Teacher_box" v-show="this.ShowType==='true'">-->
<!--        <img src="@/assets/teacher (2).png" alt="">-->
<!--        <span class="notes">本套练习中含有需要拍照的题型，请在可拍照设备上答题哦～</span>-->
<!--      </div>-->
<!--      <div style="margin-top: 20px;" v-show="this.ShowType==='true'">-->
<!--        <p><span class="answer-btn cur pc_font_size18 font_size22 ipad_font_size24" @click="GoBack">返回练习</span></p>-->
<!--      </div>-->
    </div>
  </div>
</template>

<script>
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'

export default {
  created () {
    this.paperId = this.$route.query.paperId
    this.HaveSpecial = this.$route.query.HaveSpecial
    this.isShowType = this.$route.query.type
    this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
    // this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)
    this.IsExistSubjectItem = this.$route.query.IsExistSubjectItem == 'false' ? false : true
    this.getPaperInfo()
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
  },
  data () {
    return {
      paperId: '',
      paperName: '',
      HaveSpecial: '',
      ShowType: '',
      isShowType: '',
      RoruterShowType: '',
      // 多学科部分
      Multidisciplinary: {}, // 学科ID
      bgColog: {},
      IsExistSubjectItem:false,
      // 区分试卷类型
      isDZB: null,
      IsPhotograph: null,
      IsSolutions: null, // 解析参数
      // 判定学生作答是否计时器需求
      PaperInfo: {}
    }
  },
  watch: {
    $route () {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      this.HaveSpecial = this.$route.query.HaveSpecial
      this.isShowType = this.$route.query.type
      this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
      this.IsExistSubjectItem = this.$route.query.IsExistSubjectItem == 'false'?false:true
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerReady?' + ChangId) {
        this.getPaperInfo()
      }
    },
    HaveSpecial (val) {
      this.ShowType = val
    },
    isShowType (val) {
      // console.log(val, 'val')
      this.RoruterShowType = val
    }
  },
  methods: {
    getPaperInfo () {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then(res => {
        this.PaperInfo = res.Data
        this.paperName = res.Data.Title
        this.isDZB = res.Data.IsDZB
        this.IsPhotograph = res.Data.IsPhotograph
        this.IsSolutions = res.Data.IsSolutions
      })
    },
    toAnswerPaper () {
      const Type_Paper = this.$route.query.type
      const { CanDo, NoticeTip } = this.PaperInfo
      if(CanDo){
      if (this.IsPhotograph == 1) {  // 高中数学拍照分割的跟进练习
        this.$router.push({
          path: '/Student/Exam_Student/highSchoolCapture/captureAnswer',
          query: {
            paperId: this.paperId,
            isDZB: 2
          }
        })
      } else {
        if (Type_Paper == 10) { // 未知页面
          this.$router.push({
            path: '/Student/Exam_Student/AnswerPaper',
            query: {
              paperId: this.paperId,
              // SubjectIdName: JSON.stringify(this.SubjectIdName) // 学科ID
            }
          })
        }
        if (Type_Paper != 10) { // 正常电子试卷作答的跟进练习
          this.$router.push({
            path: '/Student/Exam_Student/AdaptivePaper',
            query: {
              paperId: this.paperId,
              type: Type_Paper,
              from: this.$route.query.from || ''
            }
          })
        }
      }
      } else {
        this.$message.warning(NoticeTip)
      }
    },
    GoBack () {
      this.$router.go(-1)
    },
    _isMobile() {
      // const flag = navigator.userAgent.match(
      //   /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone|Macintosh)/i
      // )
      // return flag
      return true
    },
  }
}
</script>

<style lang="less" scoped>
.an-mb {
  margin-bottom: 50px;
}
.xuekeStyle{
  padding: 4px 18px;
  border-radius: 100px;
}
.AnswerReady_top{
  display: flex;
  justify-content: space-between;
  .answer-ready {
    //height: 38px;
    //line-height: 38px;
    //font-size: 22px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #4d5753;
    //padding-left: 15px;
    //background-color: #fff;
    //border-radius: 5px;
  }
  .Goback {
    //font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #858585;
    display: block;
    //margin-left: 9px;
    //margin-top: 10px;
    cursor: pointer;
  }
}

.answer-careful {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 60px;
}
.answer_content {
  margin-top: 30px;
  line-height: 50px;
  span {
    //font-size: 20px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #4d5753;
  }
}
.PaperName {
  //font-size: 26px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 500;
  color: #949494;
}
.Teacher_box {
  display: flex;
  margin-top: 20px;
  .notes {
    height: 80px;
    padding: 10px;
    background: #ffff;
    border-radius: 10px;
    display: block;
    font-size: 22px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 500;
    color: #949494;
  }
}
.answer-btn {
  //display: inline-block;
  width: 200px;
  height: 48px;
  padding: 12px 70px;
  border-radius: 100px;
  //line-height: 48px;
  color: #fff;
  //font-size: 21px;
  background-color: #68bb97;
  border-radius: 30px;
}
</style>
