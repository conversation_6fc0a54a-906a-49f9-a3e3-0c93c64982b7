
export default {
  navList: [
    {
      value: 'default',
      text: '默认键盘',
      active: true
    },
    {
      value: 'CAE',
      text: '中英文键盘',
      active: true
    },
    {
      value: 'unit',
      text: '单位键盘',
      active: true
    }
  ],
  middleNavList: [
    {
      value: 'default',
      text: '默认键盘',
      active: true
    },
    {
      value: 'letter',
      text: '字母键盘',
      active: true
    },
    {
      value: 'formula',
      text: '公式键盘',
      active: false
    },
    {
      value: 'CAE',
      text: '中英文键盘',
      active: true
    }
  ],
  defaultList: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
  smallSymbol: [
    {
      text: '+',
      value: '\\cdot'
    },
    {
      text: '-',
      value: '\\cdotp'
    },
    {
      text: '×',
      value: '\\circ'
    },
    {
      text: '÷',
      value: '\\centerdot'
    },
    {
      text: '=',
      value: '='
    },
    {
      text: '≠',
      value: '≠'
    },
    {
      text: '≈',
      value: '≈'
    },
    {
      text: '⊥',
      value: '\\perp'
    },
    {
      text: '∥',
      value: '\\shortparallel'
    },
    {
      text: '°',
      value: '°'
    },
    {
      text: '<',
      tv: '\\lt'
    },
    {
      text: '>',
      tv: '\\gt'
    },
    {
      text: '≤',
      tv: '\\le'
    },
    {
      text: '≥',
      value: '\\ngeq'
    },
    {
      text: '、',
      value: '\\'
    },
    {
      text: '……',
      value: '\\'
    },
    {
      text: ':',
      value: '\\'
    },
    {
      text: '.',
      value: '\\'
    },
    {
      text: '%',
      value: '\\%'
    },
    {
      text: '∠',
      value: '\\angle'
    },
    {
      text: '(',
      value: '\\lparen'
    },
    {
      text: ')',
      value: '\\rparen'
    },
    {
      text: '[',
      value: '\\lbrack'
    },
    {
      text: ']',
      value: '\\rbrack'
    },
    {
      text: '{',
      tv: '\\lbrace'
    },
    {
      text: '}',
      tv: '\\rbrace'
    },
    {
      text: '正确✓',
      value: '\\surd'
    },
    {
      text: '错误×',
      value: '\\times'
    }
  ],
  middleSymbol: [
    {
      text: '+',
      value: '\\cdot'
    },
    {
      text: '-',
      value: '\\cdotp'
    },
    {
      text: '×',
      value: '\\circ'
    },
    {
      text: '÷',
      value: '\\centerdot'
    },
    {
      text: '=',
      value: '='
    },
    {
      text: '≠',
      value: '≠'
    },
    {
      text: '≈',
      value: '≈'
    },
    {
      text: '·',
      value: '·'
    },
    {
      text: '±',
      value: '±'
    },
    {
      text: ':',
      value: ':'
    },
    {
      text: '< ',
      tv: '\\lt'
    },
    {
      text: ' > ',
      tv: '\\gt'
    },
    {
      text: '≤',
      tv: '\\le'
    },
    {
      text: '≥',
      tv: '\\ge'
    },
    {
      text: '、',
      value: '、'
    },
    {
      text: '……',
      value: '……'
    },
    {
      text: '%',
      value: '\\%'
    },
    {
      text: '⇐',
      tv: '\\Leftarrow'
    },
    {
      text: '⇒',
      tv: '\\Rightarrow'
    },
    {
      text: '⇔',
      value: '\\Leftrightarrow'
    },
    {
      text: '(',
      value: '\\lparen'
    },
    {
      text: ')',
      value: '\\rparen'
    },
    {
      text: '[',
      tv: '\\lbrack'
    },
    {
      text: ']',
      tv: '\\rbrack'
    },
    {
      text: '{',
      tv: '\\lbrace'
    },
    {
      text: '}',
      tv: '\\rbrace'
    },
    {
      text: '°',
      tv: '\\degree'
    },
    {
      text: '‘',
      value: '\\lq'
    },
    {
      text: '“',
      value: '“'
    },
    {
      text: '/',
      value: '\\notin'
    },
    {
      text: '∝',
      value: '\\propto'
    },
    {
      text: '∞',
      value: '\\infty'
    },
    {
      text: '~',
      value: '\\'
    },
    {
      text: '≌',
      value: '≌'
    },
    {
      text: '∈',
      value: '\\in'
    },
    {
      text: '∉',
      value: '\\notin',
      imgSrc: require('@/assets/keyboard/key4.png')
    },
    {
      text: '∩',
      value: '\\cap',
      imgSrc: require('@/assets/keyboard/key6.png')
    },
    {
      text: '∪',
      value: '\\cup',
      imgSrc: require('@/assets/keyboard/key5.png')
    },
    {
      text: '⊆',
      value: '⊆'
    },
    {
      text: '⊇',
      value: '⊇'
    },
    {
      text: 'Ø',
      value: 'Ø'
    },
    {
      text: '⊂',
      value: '⊂'
    },
    {
      text: '⊃',
      value: '⊃'
    },
    {
      text: '∧',
      value: '\\wedge'
    },
    {
      text: '∫',
      value: '\\int'
    },
    {
      text: '∵',
      value: '\\because'
    },
    {
      text: '∴',
      value: '\\therefore'
    },
    {
      text: '∥',
      value: '\\parallel'
    },
    {
      text: '⊥',
      value: '\\perp'
    },
    {
      text: '∠',
      value: '\\angle'
    },
    {
      text: '△',
      value: '\\vartriangle',
      tv: '\\Delta',
      imgSrc: require('@/assets/keyboard/key3.png')
    },
    {
      text: '▱',
      value: '\\square',
      imgSrc: require('@/assets/keyboard/key1.png')
    },
    {
      text: '⊙',
      value: '\\odot',
      imgSrc: require('@/assets/keyboard/key2.png')
    },
    {
      text: '〇',
      value: '\\bigcirc',
      imgSrc: require('@/assets/keyboard/yuan.png')
    },
    {
      text: '□',
      value: '\\Box',
      imgSrc: require('@/assets/keyboard/juxing.png')
    },
    {
      text: '☆',
      value: '\\star',
      imgSrc: require('@/assets/keyboard/xingxing.png')
    },
    {
      text: ',',
      value: ','
    },
    {
      text: '。',
      value: '。'
    },
    {
      text: '；',
      value: '；'
    },
    {
      text: '正确✓',
      value: '\\surd'
    },
    {
      text: '错误×',
      value: '\\times'
    }
  ],
  english: [
    'q',
    'w',
    'e',
    'r',
    't',
    'y',
    'u',
    'i',
    'o',
    'p',
    'a',
    's',
    'd',
    'f',
    'g',
    'h',
    'j',
    'k',
    'l',
    '拼音',
    'z',
    'x',
    'c',
    'v',
    'b',
    'n',
    'm'
  ],
  unit: [
    'mm',
    'cm',
    'dm',
    'm',
    'km',
    'mm²',
    'cm²',
    'dm²',
    '㎡',
    'km²',
    'mm³',
    'dm³',
    'cm³',
    'm³',
    'mg',
    'g',
    'kg',
    't',
    'ml',
    'l'
  ],
  letter: [
    {
      text: 'α',
      tv: '\\alpha'
    },
    {
      text: 'β',
      tv: '\\beta'
    },
    {
      text: 'γ',
      tv: '\\gamma'
    },
    {
      text: 'δ',
      tv: '\\delta'
    },
    {
      text: 'ε',
      tv: '\\epsilon'
    },
    {
      text: 'ζ',
      tv: '\\zeta'
    },
    {
      text: 'η',
      tv: '\\eta'
    },
    {
      text: 'θ',
      tv: '\\theta'
    },
    {
      text: 'λ',
      tv: '\\lambda'
    },
    {
      text: 'μ',
      tv: '\\mu'
    },
    {
      text: 'φ',
      tv: '\\phi'
    },
    {
      text: 'π',
      tv: '\\pi'
    },
    {
      text: 'Ω',
      tv: '\\Omega'
    }
  ],

  formula: [
    {
      v: '\\cmdsub',
      text: '',
      comp: 'Subscript',
      imgUrl: require('@/assets/keyboard/0.png')
    },
    {
      v: '\\cmdsup',
      text: '',
      comp: 'Superscript',
      imgUrl: require('@/assets/keyboard/1.png')
    },
    {
      v: '\\sqrt',
      text: '',
      comp: 'Sqrt',
      imgUrl: require('@/assets/keyboard/2.png')
    },
    {
      v: '\\nthroot',
      comp: 'Sqrts',
      text: '',
      imgUrl: require('@/assets/keyboard/3.png')
    },
    // {
    //   text: '',
    //   v: '\\bigg',
    //   comp: 'Div',
    //   imgUrl: require('@/assets/keyboard/4.png')
    // },
    {
      text: '',
      v: '/',
      comp: 'Frac',
      imgUrl: require('@/assets/keyboard/5.png')
    },
    {
      text: '',
      v: '\\overrightarrow',
      comp: 'Vector',
      imgUrl: require('@/assets/keyboard/6.png')
    },
    {
      text: '',
      v: '|',
      comp: 'Modulus',
      imgUrl: require('@/assets/keyboard/7.png')
    },
    {
      tv: `\\left\\{\\lines{}{}\\right.`,
      comp: 'Brace',
      text: '',
      index: 3,
      imgUrl: require('@/assets/keyboard/8.png')
    },
    {
      v: '\\sin',
      text: 'sin',
      comp: 'Sin'
    },
    {
      v: '\\cos',
      text: 'cos',
      comp: 'Cos'
    },
    {
      v: '\\tan',
      text: 'tan',
      comp: 'Tan'
    },
    {
      v: '\\cot',
      text: 'cot',
      comp: 'Cot'
    },
    {
      v: '\\clg',
      text: '',
      comp: 'Lg',
      imgUrl: require('@/assets/keyboard/13.png'),
      index: -1
    },
    {
      v: '\\cln',
      text: '',
      comp: 'Ln',
      imgUrl: require('@/assets/keyboard/14.png'),
      index: -1
    },
    {
      v: '\\clog',
      text: '',
      comp: 'Log',
      imgUrl: require('@/assets/keyboard/15.png'),
      index: -1
    },
    {
      tv: '\\dot{}',
      text: '',
      comp: '',
      imgUrl: require('@/assets/keyboard/xunhuan.png')
    },
    {
      tv: '^{0}',
      text: '',
      comp: '',
      imgUrl: require('@/assets/keyboard/0cimi.png')
    },
    {
      tv: '^{3}',
      text: '',
      comp: '',
      imgUrl: require('@/assets/keyboard/3cimi.png')
    }
  ]
}