<template>
  <a-card :bordered="false">
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="queryParam.condition">
                <a-select-option key="RealName">姓名</a-select-option>
                <a-select-option key="PhoneNum">手机号码</a-select-option>
                <a-select-option key="StudentNo">学号</a-select-option>
                <a-select-option key="ClassName">班级</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="4" :sm="24">
            <a-form-item>
              <a-input v-model="queryParam.schoolName" placeholder="学校名称" />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-button type="primary" @click="getDataList">查询</a-button>
            <a-button style="margin-left: 8px" @click="() => (queryParam = {})">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" :rowKey="row => row.Id" :dataSource="data" :pagination="pagination" :loading="loading" @change="handleTableChange"
      :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :bordered="true" size="small">
      <span slot="UserPinyin" slot-scope="text,record">
        <template>
          <editable-cell :text="text" @change="onCellChange(record.Id, 'UserPinyin', $event)" />
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
  </a-card>
</template>

<script>
import EditForm from './EditForm'

const EditableCell = {
  template: `
      <div class="editable-cell">
        <div v-if="editable" class="editable-cell-input-wrapper">
          <a-input :value="value" @change="handleChange" @pressEnter="check" /><a-icon
            type="check"
            class="editable-cell-icon-check"
            @click="check"
          />
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ value || ' ' }}
          <a-icon type="edit" class="editable-cell-icon" @click="edit" />
        </div>
      </div>
    `,
  props: {
    text: String
  },
  data() {
    return {
      value: this.text,
      editable: false
    }
  },
  methods: {
    handleChange(e) {
      const value = e.target.value
      this.value = value
    },
    check() {
      this.editable = false
      this.$emit('change', this.value)
    },
    edit() {
      this.editable = true
    }
  }
}

const columns = [
  { title: '用户名', dataIndex: 'UserName', width: '10%', align: 'center' },
  { title: '手机号码', dataIndex: 'PhoneNum', width: '10%', align: 'center' },
  { title: '姓名', dataIndex: 'RealName', width: '10%', align: 'center' },
  { title: '性别', dataIndex: 'SexText', width: '5%', align: 'center' },
  { title: '出生日期', dataIndex: 'BirthdayText', width: '10%', align: 'center' },
  {
    title: '学号',
    dataIndex: 'UserPinyin',
    width: '8%',
    align: 'center',
    scopedSlots: { customRender: 'UserPinyin' },
    sorter: true
  },
  { title: '班级', dataIndex: 'ClassName', width: '8%', align: 'center' },
  { title: '学校', dataIndex: 'SchoolName', width: '10%', align: 'center' }
]

export default {
  components: {
    EditForm,
    EditableCell
  },
  mounted() {
    this.getDataList()
  },
  data() {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 100,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'Id', order: 'asc' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: []
    }
  },
  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    getDataList() {
      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Student/Exam_Student/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field || 'Id',
          SortType: this.sorter.order,
          ...this.queryParam,
          ...this.filters
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected() {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd() {
      this.$refs.editForm.openForm()
    },
    handleEdit(id) {
      this.$refs.editForm.openForm(id)
    },
    onCellChange(key, dataIndex, value) {
      this.$http.post('/Student/Exam_Student/UpdateStudentNo', { studentId: key, studentNo: value }).then(resJson => {
        if (resJson.Success) {
          this.$message.success('操作成功!')
        } else {
          this.$message.error(resJson.Msg, 2)
        }
      })
    }
  }
}
</script>
