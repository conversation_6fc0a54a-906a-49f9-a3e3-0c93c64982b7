<template>
  <div class="examine whole-mr">
    <div class="example" v-if="loadPaper" style="text-align: center; width: 100%">
      <a-spin />
    </div>
    <div class="Answer_header">
      <span @click="GoBack" style="cursor: pointer;"><i class="el-icon-arrow-left"></i>返回</span>
      <span>{{paperName}}</span>
      <span></span>
    </div>
    <div class="Answer_content">
      <div class="div" style="width: 70%">
        <!-- class="alone-tit" -->
        <div v-for="(item, index) in paperList" :key="item.ItemId" :id="item.ItemId" v-katex>
          <div class="tit_header">
            <span>{{item.PaperItemType}}</span>
          </div>
          <p>
            <span>({{ index + 1 }}) </span><span v-html="item.ItemTitleWeb"></span>
          </p>
          <div>
            <!-- 选择题 -->
            <div v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
              <span v-for="(k, j) in item.Options" style="display: block;">
                <img v-if="item.IsKeyTrue === 1 && item.UserAnswer === k.Opt" style="width:18px;vertical-align: middle;" src="@/assets/student/对号.png" alt="" />
                <img v-else-if="item.IsKeyTrue === 2 && item.UserAnswer === k.Opt" style="width:18px;vertical-align: middle;" src="@/assets/student/叉号.png" alt="" />
                <span v-else class="Radio_raduis"></span>
                <span style="vertical-align: middle;"> {{ k.Opt }}.<span v-html="k.Content"></span></span>
              </span>
            </div>

            <!-- 多选题 -->
            <div v-if="item.ItemTypeId === 10">
              <div v-for="k in item.Options" :key="k.Opt">
                <img v-if="k.IsTrue === 1 && item.UserAnswer.indexOf(k.Opt)!=-1" style="width:18px;vertical-align: middle;" src="@/assets/student/对号.png" alt="" />
                <img v-else-if="k.IsTrue === 0 && item.UserAnswer.indexOf(k.Opt)!=-1" style="width:18px;vertical-align: middle;" src="@/assets/student/叉号.png" alt="" />
                <span v-else class="check_box"></span>
                <span style="vertical-align: middle;"> {{ k.Opt }}.<span v-html="k.Content"></span></span>
              </div>
            </div>

            <!-- 普通填空题 -->
            <div v-if="item.ItemTypeId === 5">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答：</span>
              <span style="margin-left: 10px;" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" v-for="(blank, ind) in item.UserAnswer">{{blank}}</span>
            </div>

            <!-- 可变行下拉填空题 -->
            <div class="select_46" v-if="item.ItemTypeId === 46">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答</span>
              <div v-for="(sel,ind) in item.ItemChange.Rows" :key="ind">
                <span v-for="(sel,indx) in sel" :key="indx" v-html="sel" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }"></span>
              </div>
            </div>

            <!-- 可变行表格 -->
            <div class="table_40" v-if="item.ItemTypeId === 40">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答</span>
              <table class="tableTitle">
                <tr v-for="(item, index) in item.ItemChange.Titles" :key="index">
                  <td>{{ item }}</td>
                </tr>
                <tbody>
                  <tr v-for="(td, ind) in item.ItemChange.Rows" :key="ind">
                    <td v-for="(td, indx) in td" :key="indx">
                      <span v-html="td"></span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 可变行填空 -->
            <div class="blank_41" v-if="item.ItemTypeId === 41">
              <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答</span>
              <div class="Blank_box" v-for="(blank, indx) in item.ItemChange.Rows" :key="indx">
                <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" class="blank_Answer">答案{{ indx + 1 }}</span>
                <span :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }" v-for="(i, ind) in blank" :key="ind">
                  <p v-html="i"></p>
                </span>
              </div>
            </div>

            <div v-if="item.ItemTypeId === 23">
              <img :src="item.PicUrl" alt="" style="max-width: 350px" />
            </div>
          </div>
          <p class="line"></p>
          <p class="clearfix" v-if="item.ItemTypeId !== 46  && item.ItemTypeId !== 40 && item.ItemTypeId !== 41 && item.ItemTypeId !== 5">
            <span class="fl" :class="{ correct: item.IsKeyTrue === 1, 'answer-wrong': item.IsKeyTrue === 2 }">我的作答：{{ item.UserAnswer }}</span>
          </p>
          <!-- @click="seeAnalysis(item.ItemId)" -->
          <span class="cur" style="color: #68bb97">解析:</span>
          <span v-show="item.Analysis!==''"><span v-html="item.Analysis"></span></span>
          <span v-show="item.Analysis===''"><span>暂无解析</span></span>
          <div v-if="item.VideoCoverUrl!=='' && item.VideoUrl!==''">
            <div class="paper_type">
              <div class="type_after"></div>
              <span>视频讲解</span>
            </div>
            <div>
              <video :preload="preload" :poster="item.VideoCoverUrl" :height="height" :width="width" align="center" :controls="controls" :autoplay="autoplay">
                <source :src="item.VideoUrl" type="video/mp4">
              </video>
            </div>
          </div>
          <br />
          <el-button v-if="item.IsKeyTrue === 2" type="primary" @click="ItemHelp(item)">求助老师辅导</el-button>
          <div v-for="(st,idx) in StsquareList" :key="idx">
            <div v-if="item.ItemId == st.ItemId">
              <div class="paper_type">
                <div class="type_after"></div>
                <span>互助学习广场:可以问问这些同学噢~~</span>
              </div>
              <div class="paper_photo" v-if="st.ItemQuestions.length!=0">
                <span v-for="st_i in st.ItemQuestions" class="Image_s">
                  <img style="width: 50px;height: 50px;border-radius: 100%;" :src="st_i.ImageUrl" alt="">
                  <br />
                  <span>{{st_i.RealName}}</span>
                </span>
              </div>
              <div class="paper_photo" v-if="st.ItemQuestions.length==0">
                <span>暂无</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="div2" style="width: 28%">
        <div class="right_header">
          <img src="@/assets/student/闹钟.png" alt="" />
          <br />
          <br />
          <span>答题用时:{{ doPaperTime }}</span>
          <br />
        </div>
        <div>
          <div class="Statistics">
            <p class="p-chart" style="margin-right: 32px">
              <a-progress type="circle" :percent="Accracy" height="100px" :width="65" :strokeColor="stuStatistics" :format="
                () => {
                  if (Accracy === 100) {
                    return '100%'
                  } else {
                    return Accracy + '%'
                  }
                }
              " /><br />
              <span>正确率</span>
            </p>
            <p class="p-chart">
              <a-progress type="circle" :percent="(correctNum / paperSubjectNum) * 100" height="100px" :width="65" :format="() => correctNum + '/' + paperSubjectNum" :strokeColor="stuStatistics">
              </a-progress><br />
              <span>正确题数</span>
            </p>
          </div>

          <!-- <div class="go_work">
            <el-button v-if="revision" @click="toBCRevision(paperName)">去订正</el-button>
          </div> -->

          <div class="List-topics">
            <p class="menu">答题卡</p>
            <!-- 'def-color': defalutCol === i.ItemId, -->
            <a v-for="(i, t) in paperList" :key="t" class="title-btn-whole" :class="{ 'def-color': i.IsKeyTrue === 1, 'wrong-bgc': i.IsKeyTrue === 2 }" @click="switchTitle(t, i.ItemId, i.TypeId)">
              {{ t + 1 }}
            </a>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  components: {},
  created() {
    
    this.userId = localStorage.getItem('UserId')
    this.StClassId = localStorage.getItem('StClassId')
    this.paperId = this.$route.query.paperId
    this.getPaperList()
    this.getPaperInfor()
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Student/FollowReview?' + ChangId) {
        this.loadPaper = true
        this.getPaperInfor()
        this.getPaperList()
      }
    }
  },
  data() {
    return {
      // 加载
      loadPaper: true,
      //
      doPaperTime: '',
      Accracy: 0,
      userId: '',
      paperId: '',
      // 试卷名称
      paperName: '',
      chapter: '',
      chapterId: '',
      // 默认第一题
      defalutCol: '',
      // 切换题目数
      newSubject: 1,
      // 题目数
      paperSubjectNum: '',
      // 错题数
      wrongNum: '',
      // 真确题数
      correctNum: '',
      // 是否要订正
      revision: false,
      // 试卷信息
      paperList: [],
      Arr_options: [],
      // 精度
      stuStatistics: '#3CB98F',
      analysis: false,
      StClassId: '',
      playStatus: '',
      muteStatus: '',
      isMute: true,
      isPlay: false,
      width: '820', // 设置视频播放器的显示宽度（以像素为单位）
      height: '500', // 设置视频播放器的显示高度（以像素为单位）
      autoplay: '',
      preload: 'auto', //  建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
      controls: true, // 确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
      StsquareList: [],
      radioStyle: {
        display: 'block'
      }
    }
  },
  methods: {
    GoBack() {
      this.$router.go(-1)
    },
    // seeAnalysis(id) {
    //   this.analysis = id
    // },
    // 获取试卷基本信息
    getPaperInfor(id) {
      // 1480449332074254337
      //  this.$route.query.paperId
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.$route.query.paperId }).then(res => {
        this.paperName = res.Data.Title
        this.chapterId = res.Data.ChapterId
      })
    },
    // 复习回顾题目详情
    getPaperList() {
      this.$uwonhttp
        .post('/Paper/Paper/GetSudentFinishPaperContent', { paperId: this.$route.query.paperId, userId: this.userId })
        .then(res => {
          this.paperList = res.data.Data.Items
          this.defalutCol = res.data.Data.Items[0].ItemId
          this.doPaperTime = res.data.Data.DoTime
          this.Accracy = res.data.Data.Accracy
          // 题目数
          this.paperSubjectNum = res.data.Data.Items.length
          this.paperList.forEach(item => {
            const regex = new RegExp('<img')
            const reg = /(#&\d+@)/g
            const ImgContent = `<img style="max-width: 80%" `
            const rep = ''
            const stem = item.ItemTitleWeb.replace(reg, rep, ImgContent)
            item.ItemTitleWeb = stem
            if (item.ItemTypeId == 5) {
              var Newtype_5 = item.UserAnswer.split('|')

              item.UserAnswer = Newtype_5
            }
          })
          const arr1 = this.paperList.filter(value => {
            return value.IsKeyTrue === 1
          })
          this.revision = this.paperList.some(value => {
            return value.IsKeyTrue === 2
          })

          this.correctNum = arr1.length
          this.loadPaper = false
          this.GetWebItemQuestionStudentList()
        })
    },
    // 切换题目
    switchTitle(index, ItemId, typeId) {
      document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      // this.defalutCol = ItemId
    },
    // 选项选择
    onChange(value) {},
    // bc订正
    toBCRevision(paperName) {
      // this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
      const paperId = this.$route.query.paperId
      this.$router.push({
        path: '/Student/Exam_Student/AnswerDetail',
        query: {
          paperId,
          paperName: this.paperName
        }
      })
    },

    //Web获取互助广场
    async GetWebItemQuestionStudentList() {
      let params = {
        classId: this.StClassId,
        paperId: this.$route.query.paperId,
        userId: this.userId
        // itemId:
      }
      const res = await this.$uwonhttp.post('/ExamItem/ExamItem/GetWebItemQuestionStudentList', params)
      if (res.data.Success) {
        this.StsquareList = res.data.Data
      }
    },

    async ItemHelp(item) {
      let params = {
        paperId: this.$route.query.paperId,
        itemId: item.ItemId,
        userId: this.userId
      }
      const res = await this.$uwonhttp.post('/ExamItem/ExamItem/StudentItemMutualHelp', params)
      if (res.data.Success) {
        this.$notify({
          title: '求助成功',
          message: '老师，我需要您辅导我这道题目！',
          type: 'success'
        })
      } else {
        this.$notify({
          title: '提示',
          message: res.data.Msg,
          type: 'warning'
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.f-t {
  font-size: 21px;
}
// 正确颜色
.correct {
  color: #68bb97;
  // font-size: 18px;
}
.answer-wrong {
  color: #ff682c;
  // font-size: 18px;
}
.wrong-bgc {
  background-color: #ff682c;
}
.def-color {
  background-color: #68bb97;
}

.select_46 {
  line-height: 30px;
}
.Blank_box {
  border: 1px solid #ccc;
  padding: 15px;
  margin-top: 10px;
  .blank_Answer {
    font-size: 18px;
  }
  span {
    p {
      font-size: 20px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #828282;
      white-space: pre-line;
      line-height: 40px;
    }
  }
}
.paper_type {
  display: flex;
  margin-top: 20px;
  padding: 6px;
  .type_after {
    width: 5px;
    height: 30px;
    background: #2751d8;
    border-radius: 5px;
  }
  span {
    // font-size: 18px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #3e3e3e;
    line-height: 33px;
    margin-left: 10px;
  }
}
.paper_photo {
  display: flex;
  .Image_s {
    text-align: center;
    margin-left: 16px;
  }
}
.Answer_header {
  display: flex;
  justify-content: space-between;
  font-size: 22px;
  padding: 15px;
  background: #ffffff;
  border-radius: 10px;
}
.Answer_content {
  display: flex;
  .div {
    height: 700px;
    height: 70vh;
    overflow: auto;
    margin-right: 23px;
  }
  // 右边
  .div2 {
    padding: 80px 16px 0 16px;
    background-color: #fff;
    .right_header {
      text-align: center;
      img {
        height: 50px;
      }
      span {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        display: contents;
      }
    }
    .p-chart {
      font-size: 17px;
      display: inline-block;
    }
    // 统计
    .Statistics {
      margin-bottom: 20px;
      margin-top: 20px;
      text-align: center;
    }
  }
}

.table_40 {
  line-height: 40px;
}
.tableTitle {
  border-collapse: collapse;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
}
.tableTitle tr {
  border: none;
}
.tableTitle td {
  width: 180px;
  height: 50px;
  font-size: 17px;
  cursor: pointer;
  border: 1px inset #cccc;
}

.Radio_raduis {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 1px solid #ccc;
  vertical-align: middle;
  display: inline-block;
}

.check_box {
  width: 15px;
  height: 15px;
  border: 1px solid #ccc;
  display: inline-block;
  vertical-align: middle;
}

.go_work {
  text-align: center;
  /deep/.el-button {
    width: 100px;
    color: #fff;
    background-color: #68bb97;
  }
}

.List-topics {
  margin-top: 20px;
  text-align: center;
  .menu {
    font-size: 18px;
  }
}
// 单题
.alone-tit {
  width: 100%;
  padding: 10px 30px;
  margin-bottom: 30px;
  font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
  line-height: 45px;
  .line {
    height: 1px;
    border-bottom: 1px solid #f6f6f6;
  }

  .tit_header {
    display: flex;
    /deep/.el-rate {
      line-height: 1;
      font-size: 35px;

      margin-left: 10px;
    }
    /deep/.el-rate__icon {
      font-size: 25px;
    }
    span {
      font-size: 21px;
    }
  }
}
// 题目目录
.title-btn-whole {
  display: inline-block;
  width: 41px;
  height: 40px;
  line-height: 40px;
  margin: 16px 16px 0 0;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 35px;
  cursor: pointer;
}
/deep/ .ant-radio-wrapper {
  margin-bottom: 10px;
  white-space: pre-wrap !important;
}
</style>
