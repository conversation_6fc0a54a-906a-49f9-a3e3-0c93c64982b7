/*
 * @Author: 周恩波 z<PERSON><PERSON><EMAIL>
 * @Date: 2024-09-13 13:26:27
 * @LastEditors: 周恩波
 * @LastEditTime: 2024-09-27 14:02:46
 * @Description:
 *
 */
import ExamFormatCommon from '@/common/ExamFormatCommon'

export default {
  data() {
    return {}
  },
  methods: {
    /**
     * @description: 单题暂时提交
     * @return {*}
     */
    submitItemHandel(item, index) {
      // console.log(item, index)
      // console.log(this.PaperList)

      const { UserAnswer, Answer, HasAnswer ,TypeId,url} = item
      if (!HasAnswer) return this.$message.error("请检查是否已经答完!")
      if(TypeId == 36 && !url?.length) return this.$message.error("请检查是否已经答完!")
      if (!UserAnswer) {
        this.$confirm("提交后，不可进行修改，请谨慎作答并提交答案哦！", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          console.log(this.PaperList[index])
          this.PaperList[index].UserAnswer = this.PaperList[index].Answer
          if(TypeId == 51){
            const answerList = item.ItemChange.TableItemInfo.filter((sub) => sub.type === 2).map((item) => {
              const { stuAnswer } = item
              return stuAnswer.includes('\\') || stuAnswer.includes('^')
                ? `<span class="math-tex">\\(${stuAnswer}\\)</span>`
                : stuAnswer
            }).join('|');
            console.log(answerList)
            this.PaperList[index].UserAnswer = answerList
          }
          if(TypeId == 36){
            this.PaperList[index].UserAnswer = this.PaperList[index].url
          }
          this.saveItemHandel(item,index)
        })
      }
    },
    /**
     * @description: 单题保存接口
     * @return {*}
     */
    async saveItemHandel(item, index) {
      console.log(this.PaperList)
      const UserAnswers = this.PaperList.filter(sub=>sub.UserAnswer || sub.url).map(k=>{
        return {
          ItemId: k.ItemId, //试题Id
          UserAnswer: k.UserAnswer,//用户答案 k.UserAnswer
          bgColorStatus:3
        }
      })
      console.log(UserAnswers)
      const res = await this.$uwonhttp.post("/Paper/Paper/StudentTemporaryDoItem", {
        DoPaperId: this.paperId, // 试卷ID
        UserId: localStorage.getItem('UserId'), // 用户ID
        UserAnswers
      })
      if(res.data.Success){
        this.PaperList = this.PaperList.map(sub=>{
          // 填空题
          if (sub.TypeId == 5 && sub.ItemId == item.ItemId) {
            sub.Title = ExamFormatCommon.SingleFormatInput(sub.Title, sub.TypeId, sub)
          }
          // 表格题
          if(sub.TypeId == 51 && sub.ItemId == item.ItemId){
            sub.isUserAnswer = true
          }
          if(sub.TypeId == 46 && sub.ItemId == item.ItemId){
            this.SplitSelectArr = this.forSelect(this.SelectList.Rows,false,true)
          }
          return sub
        })
        const nextIndex = index 
        console.log(nextIndex)
        if(index+1 != this.PaperList.length){
          this.next(nextIndex,this.PaperList[nextIndex])
        }
        // this.$forceUpdate()
      }
    },
  },
}
