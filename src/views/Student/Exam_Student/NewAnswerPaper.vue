<template>
  <div>
    <div class="example" v-if="loadingSubmit" style="text-align: center; width: 100%">
      <a-spin />
    </div>
    <div id="practice-preview" class="clearfix" style="display: flex;height:100%">
      <div class="subject">
        <div class="title_header">
          <span @click="GoBack"><i class="el-icon-arrow-left"></i>返回</span>
          <span>{{ paperName }}</span>
          <span></span>
        </div>
        <div class="sub-info" v-for="(item, index) in paperData" :key="item.ItemId" :id="item.ItemId">
          <div class="type_name" v-if="num===index">
            <div class="type_after"></div>
            <span v-if="item.TypeId==2">单项选择题</span>
            <span v-if="item.TypeId==5">填空题</span>
            <span v-if="item.TypeId==10">多项选择题</span>
            <span v-if="item.TypeId==11">判断题</span>
            <span v-if="item.TypeId==23">应用题</span>
            <span v-if="item.TypeId==36">主观题</span>
            <span v-if="item.TypeId==39">题型</span>
          </div>
          <div class="type_title" v-if="num===index">
            <div class="title">
              <span style="font-size: 18px">({{ index + 1 }})</span>
              <span v-html="item.Title" class="formatImg"></span>
            </div>
            <!-- 试题选项 -->
            <div style="padding-left: 35px" v-if="item.TypeId != 41 && num===index">
              <a-radio-group @change="SelectOptions" v-if="item.TypeId === '2' || item.TypeId === '11'">
                <a-radio :style="radioStyle" :value="item.ItemId + '|' + k.Option" v-for="(k, j) in item.AuditPaperItemAnswers" :key="j" v-show="k.Type === 1">
                  <span v-if="k.Type === 1" style="font-size: 20px">{{ k.Option }}:&nbsp;&nbsp;</span>
                  <span class="p-inline" v-if="k.Type === 1" ref="companyStyle" v-html="k.Content" style="font-size: 18px; display: inline-block"></span>
                </a-radio>
              </a-radio-group>
              <a-checkbox-group class="Multiple" @change="SelectOptions1" v-if="item.TypeId === '10'">
                <a-row>
                  <a-col :span="24">
                    <a-checkbox v-for="(i, ind) in item.AuditPaperItemAnswers" :key="ind" :value="item.ItemId + '|' + i.Option" v-show="i.Type === 1">
                      {{ i.Option }}:
                      <span v-html="i.Content" style="font-size: 20px"></span>
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
              <div v-if="item.TypeId === '23'" @click="subjectUpload(item.ItemId)">
                <a-upload :name="item.ItemId" list-type="picture-card" class="avatar-uploader" :show-upload-list="false" :action="defaultAddress" @change="handleChange">
                  <img :src="imageUrl" alt="avatar" style="max-width: 400px" :id="item.ItemId + '@'" />
                  <div>
                    <a-icon :type="loading ? 'loading' : 'plus'" />
                    <div class="ant-upload-text">注：*可用相机拍摄作答过程，形成图片，导入本地进行上传</div>
                  </div>
                </a-upload>
              </div>
            </div>
            <!-- 新题型添加删除 -->
            <div class="special_subject" v-if="item.TypeId == 41">
              <div v-for="(item, index) in ItemChange" :key="index">
                <span style="font-size: 18px">{{ index + 1 }}</span>
                <span class="formatImg" v-html="item"></span>
              </div>
              <div class="add_delete">
                <span v-if="ItemChange.length<8" style="font-size: 18px" @click="AddRows(item)"><i class="el-icon-circle-plus-outline"></i>添加一行</span>
                <span v-if="ItemChange.length>1" style="font-size: 18px" @click="deletRows"><i class="el-icon-remove-outline"></i>删除一行</span>
              </div>
            </div>
            <div class="btn_text">
              <el-button class="btn_prex" :disabled="preDisabled" @click="prex(index,item.ItemId)">上一题</el-button>
              <el-button class="btn_next" :disabled="nextDisabled" @click="next(index,item.ItemId)">下一题</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="sub-btn">
        <div class="btn_header">
          <img src="@/assets/student/闹钟.png" alt="">
          <br />
          <p>{{ callinTime }}</p>
          <br />
          <br />
          <span class="fl"> <span style="color:#61BB96">{{ newSubject }}</span>/ <span>{{ paperNum }}</span> 题</span>
        </div>
        <div class="title-btn" @scroll="onScroll">
          <span v-for="(i, t) in paperData" :key="t" class="title-btn-whole" :class="{ color: color === i.ItemId }" @click="switchTitle(t, i.ItemId, i.TypeId)">
            <p>{{ t + 1 }}</p>
          </span>
          <p class="submit" v-if="NullTyped">
            <el-button :class="{ 'submit-complete': submitSuccess === 1 }" @click="viewsSubject" :disabled="submitSuccess1">
              提交
            </el-button>
          </p>
          <p class="submit" v-if="DataTyped">
            <el-button :class="{ 'submit-complete': submitSuccess === 1 }" @click="specialSubmit" :disabled="submitSuccess1">
              提交
            </el-button>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import CUploadImg from '@/components/CUploadImg/CUploadImg'
// import CorrectLogsDetailsVue from '@/views/SchoolManager/LawReview/CorrectLogsDetails.vue'
function getBase64(img, callback) {
  const reader = new FileReader()
  reader.addEventListener('load', () => callback(reader.result))
  reader.readAsDataURL(img)
}
export default {
  components: {
    CUploadImg
  },
  created() {
    this.userId = localStorage.getItem('UserId')
    this.paperId = this.$route.query.paperId
    // 获取试卷基本信息
    this.getTitleList()
    // 获取试卷的名称
    this.getTestPaperName()
  },
  mounted() {
    this.timeRecord(true)
    window.addEventListener('scroll', this.getScrollPosition, false)
  },
  updated() {},
  destroyed() {
    window.removeEventListener('scroll', this.getScrollPosition, false)
  },
  computed: {},
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerPaper?' + ChangId) {
        this.$router.go(0)
        this.reset()
        this.getTestPaperName()
        this.getTitleList()
        this.timeRecord(false)
        setTimeout(() => {
          this.timeRecord(true)
        }, 300)
      }

    },
    paperData: {
      handler() {
        this.$nextTick(() => {
          this.$MathJaxToDom()
        })
      },
      deep: true //true 深度监听可监听到对象、数组的变化
    },
   num: {
    handler(now){
     if (now == this.paperData.length - 1) {
        this.nextDisabled = true;
      } else {
        this.nextDisabled = false;
      }
      if (now < 1) {
        this.preDisabled = true;
      }
     },
     deep: true //true 深度监听可监听到对象、数组的变化
    }
  },
  data() {
    return {
      submitSuccess1: false,
      submitSuccess: 0,
      loadingSubmit: false,
      imageUrl: require('@/assets/student/upload.png'),
      itemImgId: '',
      userId: '',
      loading: false,
      Img: '',
      heightPage: 0,
      // 试卷id
      paperId: '',
      // 计时器
      callinTime: '',
      // 试卷信息
      paperData: [],
      // 试卷名称
      paperName: '',
      // 试卷题数
      paperNum: '',
      // 当前题目标号
      newSubject: '1',
      // 切换题目颜色
      color: '',
      //  // 题目切换
      key: 1,
      // 填空题答案
      testAnswer: [],
      // 单选题答案
      testChoiceAnswer: [],
      // 多选题的答案
      testChoiceAnswer1: [],
      testChoiceAnswer2: [],
      radioStyle: {
        display: 'block',
        lineHeight: '30px'
      },
      // 图片地址
      defaultAddress: '',
      NullTypeId: true,
      HaveTypeId: false,
      ItemChange: [],
      newString: {},
      NullTyped:true,
      DataTyped:false,
      num:0,
      preDisabled: true, //上禁用按钮
      nextDisabled: false, //下禁用按钮
      active:'',
      answerList:[]
    }
  },
  methods: {
   prex(index,ItemId){
    const getindex = index - 1
    const itemID=this.paperData[getindex].ItemId
       if (this.num === 0) {
        this.num = 0;
      } else {
        this.newSubject=this.num-0
        this.num -=1;
        this.color=itemID
      }
  },
   next(index,ItemId){
    const getindex = index + 1
    const itemID=this.paperData[getindex].ItemId
    this.preDisabled = false;
    if (this.num < this.paperData.length - 1) {
        this.num += 1;
        this.newSubject=this.num+1
        this.color=itemID
      }
    },
    // 切换题目
    switchTitle(index, ItemId, typeId) {
      this.num=index
      this.newItemId = ItemId
      this.newSubject = index + 1
      // 切换题目(当前题目对应下标)
      this.key = index
      // 切换题目颜色 (当前题目的id)
      this.color = ItemId
      // 切换题目时的类型ID
      this.newTextType = typeId
      this.preDisabled = false 
      this.nextDisabled = true
      // 处理试卷切换题目选项
      this.switchTitleOption(ItemId)
    },
    // 处理切换题目时选项
    switchTitleOption(ItemId) {
      this.$http
        .post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', {
          itemId: ItemId
        })
        .then(res => {
          this.option = res.Data
        })
    },
   GoBack(){
     this.$router.go(-1)
   },
   reset() {
      this.paperData = []
      // 填空答案
      this.testAnswer = []
      // 上传总答案
      this.eachAnswer = []
      // 单选
      this.testChoiceAnswer = []
      this.testChoiceAnswer1 = []
      // 多选
      this.testChoiceAnswer2 = []
    },
    onScroll() {
      // const scrollItems = document.querySelectorAll('.sub-info')
    },
    // 计时器
    timeRecord(bolean) {
      const _this = this
      let hour, minute, second
      hour = minute = second = 0
      if (bolean === true) {
        _this.timer = setInterval(() => {
          if (second >= 0) {
            second = second + 1
          }
          if (second >= 60) {
            second = 0
            minute = minute + 1
          }
          if (minute >= 60) {
            minute = 0
            hour = hour + 1
          }
          _this.callinTime = hour + '时' + minute + '分' + second + '秒'
        }, 1000)
      } else {
        window.clearInterval(_this.timer)
      }
    },
    // 监听浏览器滚动事件
    getScrollPosition() {
    },
    // 获取试卷基本信息
    getTitleList() {
      this.$http
        .get('/Paper/Exam_Item/GetAuditPaperItemsList', {
          params: {
            paperId: this.paperId
          }
        })
        .then(res => {
          this.paperData = res.Data
          this.paperNum = res.Data.length
          this.color = res.Data[0].ItemId
          this.paperData.forEach(item => {
            const reg = /(#&\d+@)/g
            const inputele =
              '<input autocomplete="off"  type="text" itemId= "' +
              item.ItemId +
              '" style="width: 100px;border: 0px; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;" name="' +
              'input' +
              '"/>'
            const stem = item.Title.replace(reg, inputele)
            item.Title = stem
            if (item.TypeId != 41) {
              this.NullTyped=true
              this.DataTyped=false
              item.AuditPaperItemAnswers.forEach(el => {
                const regex = new RegExp('<img')
                const ImgContent = el.Content.replace(regex, `<img style="max-width: 80%"`)
                el.Content = ImgContent
              })
            }
            if (item.TypeId == 41) {
              this.NullTyped=false
              this.DataTyped=true
              item.ItemChange.Rows.forEach(i => {
                this.ItemChange = i
                const input =
                  '<input autocomplete="off"  type="text" itemId= "' +
                  item.ItemId +
                   '" style="width: 100px;border: 0px; BACKGROUND-COLOR: transparent; text-align:center; border-bottom:1px solid grey;" name="' +
                  'input' +
                  '"/>'
                const newText = i[0].replace(/\#\@/g, input)
                i[0] = newText
              })
            }
          })
        })
    },
    subjectUpload(id) {
      this.itemImgId = id
      this.defaultAddress = `http://uwoomobile.doggod.xyz/ExamItem/ExamItem/StuItemPic?userId=${this.userId}&paperId=${this.paperId}&itemId=${id}`
    },
    // 上传图片
    handleChange(info, label, extra) {
      if (info.file.status === 'uploading') {
        this.loading = true
        return
      }
      if (info.file.status === 'done') {
        getBase64(info.file.originFileObj, imageUrl => {
          document.getElementById(this.itemImgId + '@').src = imageUrl
          this.loading = false
        })
      }
    },
    // 获取试卷的名称
    getTestPaperName() {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id: this.paperId }).then(res => {
        this.paperName = res.Data.Title
      })
    },
   
    // 选择题的选项变化
    SelectOptions(e) {
      const choiceAnswer = e.target.value
      const choiceArray = choiceAnswer.split('|')
      const itemid = choiceArray[0]
      const selectOption = choiceArray[1]
      const exist = this.testChoiceAnswer.some(item => {
        if (item.ItemId === itemid) {
          return true
        }
      })
      if (!exist) {
        this.testChoiceAnswer.push({
          ItemId: itemid,
          Answer: selectOption
        })
        this.selectOption = selectOption
      } else {
        this.testChoiceAnswer.forEach(item => {
          if (item.ItemId === itemid) {
            item.Answer = selectOption
            this.selectOption = selectOption
          }
        })
      }
    },
    // 多选题的选项
    SelectOptions1(checkedValues) {
      checkedValues.forEach(item => {
        const items = item.split('|')
        const itemid = items[0]
        const selectOption = items[1]
        const exist = this.testChoiceAnswer1.some(item => {
          if (item.ItemId === itemid) {
            return true
          }
        })
        if (!exist) {
          this.testChoiceAnswer1.push({
            ItemId: itemid,
            AnswerArray: [selectOption],
            Answer: selectOption
          })
        } else {
          this.testChoiceAnswer1.forEach(item => {
            if (item.ItemId === itemid) {
              item.AnswerArray.push(selectOption)
            }
          })
        }
        this.testChoiceAnswer1.forEach(item => {
          const b = checkedValues.map(res => {
            return res.split('|')[1]
          })
          const TestAnswer = [...new Set(item.AnswerArray)].filter(i => {
            if (b.includes(i)) {
              return i
            }
          })
          TestAnswer.sort()
          item.Answer = TestAnswer.join('|')
        })
        const abc = []
        this.testChoiceAnswer1.forEach(value => {
          value.Answer.split(',')
            .sort()
            .join('|')
          abc.push({
            ItemId: value.ItemId,
            Answer: value.Answer
          })
          this.testChoiceAnswer2 = abc
        })
      })
    },
    // 提交试卷
    viewsSubject() {
      const that = this
      this.$confirm('是否确认提交练习?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(() => {
        const inputs = document.getElementsByTagName('input')
        for (var i = 0; i < inputs.length; i++) {
        const itemId = inputs[i].getAttribute('itemid')
        const each = inputs[i].value       //单个input值
        
        const exist = that.testAnswer.some(item => {
        if (item.ItemId === itemId) {
          return true
        }
        })
        if (!exist) {
        that.testAnswer.push({
          ItemId: itemId,
          AnswerArray: [each],
          Answer: each
        })
        } else {
        that.testAnswer.forEach(item => {
          if (item.ItemId === itemId) {
          item.AnswerArray.push(each)
          }
        })
      }
      }
      for (var x = 0; x < that.testAnswer.length; x++) {
        that.testAnswer[x].Answer = that.testAnswer[x].AnswerArray.join('|')
      }
      that.eachAnswer = [...that.testChoiceAnswer, ...that.testAnswer, ...that.testChoiceAnswer2]
      const EachAnswer = that.eachAnswer.filter(item => item.ItemId !== null)
      that.eachAnswer = EachAnswer
      let arr = 0
      that.eachAnswer.forEach(el => {
      if (el.Answer === '' || el.Answer.replace(/\|/g, '').replace(' ', '') === '') {
        arr = 1
      }
      })
      if (arr == 1) {
       return that.$message.error('请提交完整的答案')
      }
      if (that.eachAnswer.length !== that.paperData.length) {
          return that.$message.error('请提交完整的答案')
      } else {
        // 停止计时
        that.timeRecord(false)
        that.submitSuccess1 = true
        that.submitSuccess = 1
        that.loadingSubmit = true
        const hourNum = that.callinTime.indexOf('时')
        const minuteNum = that.callinTime.indexOf('分')
        const secondNum = that.callinTime.indexOf('秒')
        const hour = that.callinTime.slice(0, hourNum)
        const minute = that.callinTime.slice(hourNum + 1, minuteNum)
        const second = that.callinTime.slice(minuteNum + 1, secondNum)
        const time = hour * 360 + minute * 60 + second * 1
       that.$uwonhttp
        .post('/Paper/Paper/StudentDoPaper', {
          DoTime: time,
          UserId: that.userId,
          PaperId: that.paperId,
          answerJson: JSON.stringify(that.eachAnswer)
        })
        .then(res => {
          if (res.data.Success) {
            that.$message.success('提交成功')
            that.submitSuccess = 0
            that.submitSuccess1 = false
            that.loadingSubmit = false
            that.$router.push({
              path: '/Student/Exam_Student/jobReport',
              query: {
                paperId: that.paperId
              }
            })
          } else {
            that.$message.warning(res.data.Msg)
          }
        })
        }
      })

    },
    //添加一行
    AddRows(item) {
      //this.newString=item.ItemChange.Rows[0].[0]
      //this.ItemChange.push(this.newString)
    },
    //删除一行
    deletRows(item) {
      this.ItemChange.splice(-1)
    },
    //特殊题型提交
    specialSubmit(){
      const that = this
      this.$confirm('是否确认提交练习?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(() => {
        const inputs = document.getElementsByTagName('input')
        const Array=[]
        const Answer=[]
        const itemId = inputs[0].getAttribute('itemid')
        for (var i = 0; i < inputs.length; i++) {
        const itemVal = inputs[i].value
        Array.push(itemVal)
        }
        const newArray=Array.slice()
        newArray.pop()
        if(newArray.indexOf("")!=-1){
           return that.$message.error('请提交完整的答案')
        }else{
        const result = [];
        for(var i=0;i<newArray.length;i+=3){
            result.push(newArray.slice(i,i+3));
        }
        var strRes="";
        result.forEach(item=>{
          item.forEach(el => {
            strRes+=el+"|";
          });
          strRes=strRes.substring(0,strRes.length-1);
          strRes+=",";
        })
        strRes=strRes.substring(0,strRes.length-1);
        Answer.push({
          itemId:itemId,
          Answer:strRes
        })
        that.timeRecord(false)
        that.submitSuccess1 = true
        that.submitSuccess = 1
        that.loadingSubmit = true
        const hourNum = that.callinTime.indexOf('时')
        const minuteNum = that.callinTime.indexOf('分')
        const secondNum = that.callinTime.indexOf('秒')
        const hour = that.callinTime.slice(0, hourNum)
        const minute = that.callinTime.slice(hourNum + 1, minuteNum)
        const second = that.callinTime.slice(minuteNum + 1, secondNum)
        const time = hour * 360 + minute * 60 + second * 1
        that.$uwonhttp
        .post('/Paper/Paper/StudentDoPaper', {
          DoTime: time,
          UserId: that.userId,
          PaperId: that.paperId,
          answerJson: JSON.stringify(Answer)
        }).then(res => {
          if (res.data.Success) {
            that.$message.success('提交成功')
            that.submitSuccess = 0
            that.submitSuccess1 = false
            that.loadingSubmit = false
            that.$router.push({
              path: '/Student/Exam_Student/jobReport',
              query: {
                paperId: that.paperId
              }
            })
          } else {
            that.$message.warning(res.data.Msg)
          }
      })
    }
  })
 },
  getAnswer(id) {
      this.$http.post('/Paper/Exam_ItemOptiAnswer/GetAnswerDataByItemId', { itemId: '11177' }).then(res => {
        this.answer = res.Data.Content
        this.completion = this.answer.split('|')
        this.mulAnswer = res.Data.Content.split('')
      })
   },
  }
}
</script>

<style lang="less" scoped>
.color {
  background-color: #66bc9a;
  color: #ffffff;
}
// 题目
.subject {
  float: left;
  width: 70%;
  margin-right: 10px;
  padding: 20px;
  height: 640px;
  background-color: #fff;
  border-radius: 5px;
  position: relative;
  .title_header {
    display: flex;
    justify-content: space-between;
    span:nth-child(1) {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #858585;
      line-height: 30px;
      cursor: pointer;
    }
    span:nth-child(2) {
      font-size: 22px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #3e3e3e;
    }
  }
  // 试卷主体
  .sub-info {
    p {
      display: inline-block;
    }
    /deep/ .ant-radio-inner {
      border-color: #000;
    }
    /deep/.ant-radio-wrapper {
      height: 100%;
    }
    .type_name {
      display: flex;
      padding: 5px;
      margin-top: 10px;
      .type_after {
        width: 5px;
        height: 30px;
        background: #2751d8;
        border-radius: 5px;
      }
      span {
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #3e3e3e;
        margin-left: 10px;
      }
    }
    .type_title {
      margin-top: 10px;
    }
  }
  .btn_text {
    text-align: center;
    position: absolute;
    bottom: 5%;
    left: 40%;
    .btn_prex {
      width: 120px;
      border-radius: 20px;
      border: 1px solid #61bb96;
      color: #61bb96;
    }
    .btn_next {
      width: 120px;
      border-radius: 20px;
      margin-left: 10px;
      background: #61bb96;
      color: #fff;
    }
  }
}
// 题目对应按钮
.sub-btn {
  width: 25%;
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
  .title-info {
    padding-bottom: 32px;
    border-bottom: 1px dotted #ccc;
  }
  .title-btn {
    padding: 0 40px;
    text-align: center;
    margin-left: 10px;
    p {
      margin-bottom: 0;
    }
  }
  // 对应按钮
  .title-btn-whole {
    display: inline-block;
    width: 41px;
    height: 40px;
    line-height: 40px;
    margin: 16px 16px 0 0;
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 35px;
    cursor: pointer;
  }
  .btn_header {
    text-align: center;
    line-height: 30px;
    padding: 20px;
    img {
      height: 30px;
    }
    p {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #bcbcbc;
    }
    span {
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      // color: #bcbcbc;
      display: contents;
    }
  }
}
// 提交
.submit {
  margin-top: 75px;
  text-align: center;
  button {
    display: inline-block;
    width: 100px;
    height: 42px;
    color: #fff;
    background-color: #61bb96;
    border-radius: 0.130208rem;
  }
  .submit-complete {
    disabled: disabled;
    background-color: #ddd;
  }
}

.p-inline {
  p {
    display: inline-block;
  }
}
.p-inline img {
  width: 680px;
}
.formatImg {
  font-size: 20px;
  margin-left: 5px;
}
// 上传图的
/deep/.ant-upload.ant-upload-select-picture-card {
  width: 400px;
  height: 220px;
}
.special_subject {
  margin-top: 50px;
  line-height: 35px;
  .add_delete {
    margin-top: 20px;
    text-align: center;
    width: 70%;
    span {
      margin-right: 20px;
      cursor: pointer;
    }
  }
}
</style>
