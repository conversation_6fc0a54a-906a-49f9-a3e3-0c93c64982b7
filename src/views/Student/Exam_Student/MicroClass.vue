<template>
  <div class="whole-mr" style="margin: 0 auto">
    <div class="MicroTitle">
      <div @click="GoBack"><i class="el-icon-arrow-left"></i>返回</div>
      <div class="xuekeStyle" :style="{'background-color':bgColog[SubjectIdName.Id+'bg'],'color':bgColog[SubjectIdName.Id]}">
        {{ SubjectIdName.name }}
      </div>
    </div>
    <p class="navigation">
      <span class="cur" @click="reviewWrong">复习回顾</span>
      <span>微课堂</span>
    </p>
    <div v-if="loadDefault" style="text-align: center; color: #ccc">
      <img src="@/assets/lack/暂无搜索记录.png" alt="" />
      <p>暂无微课列表</p>
    </div>
    <!-- 新微课信息详情 -->
    <div class="clearfix">
      <div class="clearfix">
        <div class="micro-details fl" v-for="item in microList" :key="item.Id">
          <!-- 视频 -->
          <!-- :style="{ backgroundImage:'url(' + item.CoverUrl + ')',backgroundRepeat:'no-repeat',backgroundSize:'100% 100%' }" -->
          <div
            @click="toMicroDetail(item)"
            class="video"
            :style="{
              backgroundImage: 'url(' + item.CoverUrl + ')',
              backgroundRepeat: 'no-repeat',
              backgroundSize: '100% 100%',
            }"
          >
            <a-icon type="play-circle" style="fontsize: 42px; color: #fff" />
          </div>
          <!-- 微课详情 -->
          <div class="video-details">
            <div>
              <div style="display: flex;align-items: center;justify-content: space-between;">
                <p class="video-name textOverflow pc_font_size20 font_size22 ipad_font_size24">{{ item.Name }}</p>
                <div class="xuekeStyle pc_font_size16 font_size16 ipad_font_size18" :style="{'background-color':bgColog[item.SubjectId+'bg'],'color':bgColog[item.SubjectId]}">
                  {{ item.SubjectName }}
                </div>
              </div>
              <p class="video-name textOverflow font_size16 ipad_font_size18">{{ item.ChapterName }}</p>
              <p class="f-c pc_font_size14 font_size18 ipad_font_size20">
                <span
                  style="margin-right: 25px"
                ><img style="vertical-align: middle" src="@/assets/teacher/时长.png" alt="" />{{
                  item.Duration
                }}</span
                >
                <span
                ><img style="vertical-align: middle" src="@/assets/teacher/播放量.png" alt="" />{{
                  item.ClickNum
                }}次</span
                >
              </p>
              <p>
                <a-rate v-model="item.Score" disabled /><span class="fg time time-name pc_font_size14 font_size18 ipad_font_size20">{{ item.CreateTime }}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'

export default {
  created () {
    this.userId = localStorage.getItem('UserId')
    this.chapterId = this.$route.query.chapterId
    this.paperId = this.$route.query.paperId
    this.isBc = this.$route.query.isBc
    this.isOnly = this.$route.query.isOnly
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)

    this.getChapterMicro()
  },
  watch: {
    $route () {
      this.chapterId = this.$route.query.chapterId
      this.paperId = this.$route.query.paperId
      this.isBc = this.$route.query.isBc
      const ChangeId = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Student/Exam_Student/MicroClass?' + ChangeId) {
        this.getChapterMicro()
      }
    }
  },
  data () {
    return {
      // 缺省
      loadDefault: false,
      userId: '',
      paperId: '',
      PageIndex: 1,
      microList: [],
      chapterId: '',
      isBc: '',
      //  多学科部分
      bgColog: {},
      SubjectIdName: {}
    }
  },
  methods: {
    GoBack () {
      this.$router.go(-1)
    },
    // 获取章节对应的微课
    getChapterMicro () {
      this.$http
        .post('/Paper/Exam_MicroLesson/GetWeekVideoBy', {
          userId: this.userId,
          PageIndex: this.PageIndex,
          PageRows: 10,
          chapterId: this.chapterId
        })
        .then((res) => {
          this.microList = res.Data
          if (res.Data.length === 0) {
            this.loadDefault = true
          }
        })
    },
    reviewWrong () {
      if (this.isBc === '1') {
        this.$router.push({ path: '/Student/Exam_Paper/BCResult', query: { paperId: this.paperId } })
      } else if (this.isOnly === '1') {
        this.$router.push({ path: '/Student/Exam_Paper/onlyWrong', query: { paperId: this.paperId, dataId: 2 } })
      } else {
        this.$router.push({ path: '/Student/Exam_Student/ReviewWrong', query: { paperId: this.paperId } })
      }
    },
    // 微课详情
    toMicroDetail (item) {
      const id = item.Id
      const SubjectIdName = {
        Id: item.SubjectId,
        name: item.SubjectName
      }
      this.$router.push({
        path: '/Student/Fine_micro/MicroDetails',
        query: {
          videoId: id,
          SubjectIdName: JSON.stringify(SubjectIdName) // 学科ID
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.video-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 多学科部分
.xuekeStyle{
  padding: 4px 18px;
  border-radius: 100px;
}
.MicroTitle {
  margin: 0px 5px 20px;
  padding: 10px 14px;
  display: flex;
  font-size: 22px;
  justify-content: space-between;
  align-items: center;
  background: #FFFFFF;
}
.navigation {
  //line-height: 50px;
  margin-bottom: 25px;
  padding: 10px 14px;
  font-size: 22px;
  background-color: #fff;
  border-radius: 5px;
  span:nth-child(1) {
    margin-right: 25px;
  }
  span:nth-child(2) {
    display: inline-block;
    height: 100%;
    margin-right: 25px;
    border-bottom: 2px solid #68bb97;
  }
}
// 微课信息详情
.micro-details {
  position: relative;
  width: 300px;
  margin-right: 22px;
  margin-bottom: 75px;
  background-color: #fff;
  border-radius: 5px;
  .video {
    // width: 320px;
    height: 200px;
    line-height: 215px;
    text-align: center;
    border-radius: 5px;
    border: 1px solid #ccc;
    cursor: pointer;
  }
  .video-details {
    padding: 5px 10px;
    // 多学科部分
    .xuekeStyle{
      padding: 4px 18px;
      border-radius: 100px;
    }
    div {
      p:nth-child(2) {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 14px;
        padding-bottom: 11px;
        border-bottom: 1px solid #eeeeee;
      }
      p:nth-child(5) {
        margin-bottom: 0;
        font-size: 12px;
      }
    }
    .score {
      position: absolute;
      left: 158px;
      top: 85px;
      width: 71px;
    }
    .score-star {
      position: absolute;
      left: 144px;
      bottom: 22px;
      width: 140px;
    }
  }
}
</style>
