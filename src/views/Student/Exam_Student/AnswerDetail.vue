<template>
  <div class="whole-mr" style="margin: 0 auto">
    <div class="Answer_title pc_font_size20 font_size22 ipad_font_size24">
      <span @click="GoBack" style="cursor: pointer"><i class="el-icon-arrow-left"></i>返回</span>
      <span>答题情况</span>
      <div
        class="xuekeStyle"
        :style="{
          'background-color': bgColog[Multidisciplinary.SubjectId + 'bg'],
          color: bgColog[Multidisciplinary.SubjectId],
        }"
      >
        {{ Multidisciplinary.name }}
      </div>
    </div>
    <div class="Answer_content">
      <div class="Item_Answer">
        <div class="Card_header">
          <div></div>
          <span>答题情况</span>
          <span
            style="color: orange; font-size: 14px"
            v-if="finishData.IsExistSubjective && !finishData.Subjective.IsReview"
            >提示：总计正确率，将在教师批阅主观题后给出哦~</span
          >
        </div>
        <div class="Progress" style="display: flex; justify-content: center">
          <div v-for="(item, index) in ProgressList" :key="index">
            <p class="pc_font_size18 font_size22 ipad_font_size24">练习正确率</p>
            <p style="text-align: center; margin: 6px 0">
              <el-progress
                type="circle"
                :width="90"
                :stroke-width="8"
                :percentage="(item.planNum / item.completeNum) * 100"
                :format="
                  () =>
                    finishData.IsExistSubjective && !finishData.Subjective.IsReview
                      ? '--'
                      : item.planNum + '/' + item.completeNum
                "
              >
              </el-progress>
            </p>
            <p class="pc_font_size16 font_size18 ipad_font_size20">
              共{{ PaperList.length }}道
              <span v-if="finishData.IsExistSubjective">
                ，客观题{{ finishData.Objective.Count }}道，主观题{{ finishData.Subjective.Count }}道
              </span>
            </p>
          </div>
          <div v-if="finishData.IsExistSubjective">
            <p class="pc_font_size18 font_size22 ipad_font_size24">客观题</p>
            <p style="margin: 6px 0">
              <el-progress
                type="circle"
                :width="90"
                :stroke-width="8"
                :percentage="finishData.Objective.Accuracy"
                :format="
                  () =>
                    finishData.IsExistSubjective && !finishData.Subjective.IsReview
                      ? '--'
                      : finishData.Objective.Count - finishData.Objective.ErrorCount + '/' + finishData.Objective.Count
                "
              >
              </el-progress>
            </p>
            <p class="pc_font_size16 font_size18 ipad_font_size20">
              共{{ finishData.Objective.Count }}道，正确{{
                finishData.Objective.Count - finishData.Objective.ErrorCount
              }}道，错误{{ finishData.Objective.ErrorCount }}道
            </p>
          </div>
          <div v-if="finishData.IsExistSubjective">
            <p class="pc_font_size18 font_size22 ipad_font_size24">主观题</p>
            <p style="margin: 6px 0">
              <el-progress
                type="circle"
                :width="90"
                :stroke-width="8"
                :percentage="finishData.Subjective.Accuracy"
                :format="
                  () =>
                    finishData.IsExistSubjective && !finishData.Subjective.IsReview
                      ? '--'
                      : finishData.Subjective.Count -
                        finishData.Subjective.ErrorCount +
                        '/' +
                        finishData.Subjective.Count
                "
                :color="!finishData.Subjective.IsReview ? '#f7f7f7;' : ''"
              >
              </el-progress>
            </p>
            <p class="pc_font_size16 font_size18 ipad_font_size20">
              <span>共{{ finishData.Subjective.Count }}道，</span>
              <span v-if="finishData.Subjective.IsReview">
                正确{{ finishData.Subjective.Count - finishData.Subjective.ErrorCount }}道，错误{{
                  finishData.Subjective.ErrorCount
                }}道
              </span>
              <span v-else>待教师批阅。</span>
            </p>
          </div>
        </div>
        <div class="Progress_footer">
          <div>
            <span
              ><img src="@/assets/student/Title.png" alt="" /><span
                class="pc_font_size18 font_size20 ipad_font_size22"
                style="margin-left: 5px"
                >练习名称</span
              ></span
            >
            <br />
            <span class="Name_time pc_font_size20 font_size22 ipad_font_size24">{{ PaperTitle }}</span>
          </div>
          <div>
            <span
              ><img src="@/assets/student/Time1.png" alt="" /><span
                class="pc_font_size18 font_size20 ipad_font_size22"
                style="margin-left: 5px"
                >交卷时间</span
              >
            </span>
            <br />
            <span class="Name_time pc_font_size18 font_size22 ipad_font_size24">{{ SubmitTime }}</span>
          </div>
        </div>
      </div>
      <div class="Item_Answer">
        <div class="Card_header">
          <div></div>
          <span>练习情况概览</span>
          <ul class="Answer_ul">
            <li>
              <p class="Frist_p"></p>
              答对
            </li>
            <li>
              <p class="Second_p"></p>
              答错
            </li>
            <li>
              <p class="Third_p"></p>
              半对
            </li>
          </ul>
        </div>
        <div>
          <div class="List_btn">
            <div
              v-if="IsPhotograph !== 1"
              v-for="(item, index) in PaperList"
              :class="{
                'error-answer': item.IsKeyTrue == '2',
                'current-answer': item.IsKeyTrue == '1',
                'no-answer': item.IsKeyTrue == '3',
                'half-answer': item.IsKeyTrue == '4',
              }"
              :key="index"
              @click="toReview(index)"
            >
              <span>{{ index + 1 }}</span>
            </div>
            <div
              v-if="IsPhotograph === 1"
              v-for="(item, index) in PaperList"
              :class="{
                'error-answer': item.SubjectiveItemState == 4,
                'current-answer': item.SubjectiveItemState == 3,
                'no-answer': item.SubjectiveItemState == 1 || item.SubjectiveItemState == 2,
              }"
              :key="index"
              @click="toReview(index)"
            >
              <span>{{ index + 1 }}</span>
            </div>
          </div>
          <div class="Prce_btn" v-if="!finishData.IsExistSubjective && IsPhotograph !== 1">
            <el-button class="Number pc_font_size16 font_size18 ipad_font_size20" @click="toReview('0')"
              >题目解析</el-button
            >
            <el-button
              v-if="RevisedNum > 0"
              class="Resove pc_font_size16 font_size18 ipad_font_size20"
              @click="toCorrect"
            >
              去订正（{{ RevisedNum }}道）</el-button
            >
            <el-button
              v-if="RevisedNum <= 0"
              class="Resove pc_font_size16 font_size18 ipad_font_size20"
              @click="is_toCorrect"
              >暂无需要订正</el-button
            >
          </div>
          <div class="Prce_btn" v-if="finishData.IsExistSubjective && IsPhotograph !== 1">
            <el-button class="Number pc_font_size16 font_size18 ipad_font_size20" @click="toReview('0')"
              >题目解析</el-button
            >
            <el-button v-if="!finishData.Subjective.IsReview" class="Resove pc_font_size16 font_size18 ipad_font_size20"
              >暂无需要订正</el-button
            >
            <el-button
              v-if="
                (finishData.Subjective.IsReview && subjectiveCount.ErrorCount == 0) || subjectiveCount.WaitReviewSub > 0
              "
              class="Resove pc_font_size16 font_size18 ipad_font_size20"
            >
              暂无需要订正
            </el-button>
            <el-button
              v-if="
                finishData.Subjective.IsReview && subjectiveCount.ErrorCount > 0 && subjectiveCount.WaitReviewSub == 0
              "
              class="Resove pc_font_size16 font_size18 ipad_font_size20"
              @click="toCorrect"
            >
              去订正（{{ subjectiveCount.ErrorCount }}道）
            </el-button>
            <!--            <el-button-->
            <!--              type="primary"-->
            <!--              class="Practice_btn"-->
            <!--              style="background-color: #83caab;color:#FFFFFF;"-->
            <!--              v-if="item.Finish && item.IsExistSubjective && !item.IsReview || item.WaitReviewSub > 0">-->
            <!--              暂无需订正-->
            <!--            </el-button>-->
            <!--            <el-button-->
            <!--              v-if="item.Finish && item.IsExistSubjective && item.IsReview && item.WaitReviewSub == 0 && item.NoCorrectCount > 0"-->
            <!--              type="primary"-->
            <!--              class="Practice_btn Practice_btn_back"-->
            <!--              @click="toCorrectPersonalPractice(item)"-->
            <!--            >-->
            <!--              去订正（{{ item.NoCorrectCount }}道）-->
            <!--            </el-button>-->
            <!--            <el-button v-if="subjectiveCount.ErrorCount <= subjectiveCount.WaitReviewSub && subjectiveCount.ErrorCount != 0" class="Resove"-->
            <!--              >暂无需要订正（{{ subjectiveCount.WaitReviewSub }}道待批阅）</el-button-->
            <!--            >-->
            <!--            <el-button-->
            <!--              v-if="subjectiveCount.ErrorCount > subjectiveCount.WaitReviewSub && finishData.Subjective.IsReview"-->
            <!--              class="Resove"-->
            <!--              @click="toCorrect"-->
            <!--              >-->
            <!--              去订正（{{ subjectiveCount.ErrorCount }}道）-->
            <!--&lt;!&ndash;              ，包含{{subjectiveCount.WaitReviewSub}}道待批阅）&ndash;&gt;-->
            <!--            </el-button>-->
          </div>
          <div class="Prce_btn" v-if="IsPhotograph === 1">
            <el-button class="Number pc_font_size16 font_size18 ipad_font_size20" @click="toReview('0')"
              >题目解析</el-button
            >
            <el-button v-if="!PhotographIsCorrect" class="Resove pc_font_size16 font_size18 ipad_font_size20"
              >暂无需要订正</el-button
            >
            <el-button
              v-if="PhotographIsCorrect"
              class="Resove pc_font_size16 font_size18 ipad_font_size20"
              @click="toCorrect"
            >
              去订正（{{ subjectiveCount.ErrorCount }}道）
            </el-button>
          </div>
        </div>
      </div>
      <div class="Item_Answer">
        <div class="Card_header">
          <div></div>
          <span>个性化练习推荐</span>
        </div>
        <div class="pers">
          <div v-show="this.AdaptivePapers && this.AdaptivePapers.length != 0">
            <div v-for="(item, index) in AdaptivePapers" :key="index" class="pers_box">
              <div class="pers_left">
                <div
                  style="width: 60%"
                  :class="[
                    { Type1_papers: item.Type == '1', Type2_papers: item.Type == '2' },
                    'pc_font_size16',
                    'font_size18',
                    'ipad_font_size20',
                  ]"
                >
                  <span>{{ item.Type == '1' ? '自适应练习' : '跟进练习' }}</span>
                </div>
                <span class="pc_font_size18 font_size20 ipad_font_size22">{{ item.PaperTitle }}</span>
                <br />
                <span class="pc_font_size16 font_size18 ipad_font_size20">共{{ item.ItemCount }}题</span>
                <br />
                <span class="pc_font_size16 font_size18 ipad_font_size20">推送时间:{{ item.PublishTime }}</span>
              </div>
              <div class="pers_right">
                <el-button
                  v-if="item.Type == '1' && item.Finish != true"
                  class="Practice_btn"
                  type="primary pc_font_size16 font_size18 ipad_font_size20"
                  @click="PaperItem(item)"
                  >自适应练习</el-button
                >
                <el-button
                  v-if="item.Type == '1' && item.Finish == true"
                  class="Practice_btn pc_font_size16 font_size18 ipad_font_size20"
                  type="primary"
                  @click="PaperAnalysis(item)"
                  >题目解析</el-button
                >
                <el-button
                  v-if="item.Type == '2' && item.Finish != true"
                  class="Practice_btn pc_font_size16 font_size18 ipad_font_size20"
                  type="primary"
                  @click="PaperItem(item)"
                  >跟进练习</el-button
                >
                <el-button
                  v-if="item.Type == '2' && item.Finish == true"
                  class="Practice_btn pc_font_size16 font_size18 ipad_font_size20"
                  type="primary"
                  @click="PaperAnalysis(item)"
                  >题目解析</el-button
                >
                <el-button
                  v-if="item.Type == '2' && item.Finish == true && !item.IsExistSubjective && item.NoCorrectCount > 0"
                  type="primary"
                  class="Practice_btn Practice_btn_back pc_font_size16 font_size18 ipad_font_size20"
                  @click="toCorrectPersonalPractice(item)"
                >
                  去订正（{{ item.NoCorrectCount }}道）
                </el-button>
                <el-button
                  type="primary"
                  class="Practice_btn pc_font_size16 font_size18 ipad_font_size20"
                  style="background-color: #83caab; color: #ffffff"
                  v-if="(item.Finish && item.IsExistSubjective && !item.IsReview) || item.WaitReviewSub > 0"
                >
                  暂无需订正
                </el-button>
                <el-button
                  v-if="
                    item.Finish &&
                    item.IsExistSubjective &&
                    item.IsReview &&
                    item.WaitReviewSub == 0 &&
                    item.NoCorrectCount > 0
                  "
                  type="primary"
                  class="Practice_btn Practice_btn_back pc_font_size16 font_size18 ipad_font_size20"
                  @click="toCorrectPersonalPractice(item)"
                >
                  去订正（{{ item.NoCorrectCount }}道）
                </el-button>
              </div>
            </div>
          </div>
          <div class="NO_pers" v-show="this.AdaptivePapers == null || this.AdaptivePapers.length == 0">
            <div>
              <img src="@/assets/defaultImg.png" />
              <p>哎呀 ~ 暂无个性化练习</p>
            </div>
          </div>
        </div>
      </div>
      <div class="Item_Answer">
        <div class="Card_header">
          <div></div>
          <span>学习指导</span>
        </div>
        <div class="pers" v-if="this.AdaptiveVideos && this.AdaptiveVideos.length != 0">
          <el-card class="box-card">
            <el-carousel :autoplay="false" arrow="always" indicator-position="outside">
              <el-carousel-item v-for="(item, index) in AdaptiveVideos" :key="index">
                <img :src="item.CoverUrl" alt="" width="60%" height="100%" />
                <div class="Carousel_right">
                  <el-tooltip effect="dark" :content="item.Name" ref="tlp" placement="top-start">
                    <span class="Title_Name pc_font_size20 font_size22 ipad_font_size24">{{ item.Name }} </span>
                  </el-tooltip>
                  <span style="color: #a0a0a0" class="pc_font_size18 font_size20 ipad_font_size22"
                    >{{ item.RealName }}老师</span
                  >
                  <br />
                  <span>
                    <el-rate v-model="item.Score" disabled text-color="#ff9900"></el-rate>
                  </span>
                  <span>
                    <p
                      :class="[
                        { car_type1: item.Type == '2' },
                        { car_type2: item.Type == '1' },
                        'pc_font_size12',
                        'font_size14',
                        'ipad_font_size16',
                      ]"
                    >
                      {{ item.Type == '1' ? '微课' : '空中课堂' }}
                    </p>
                    <img style="margin-left: 8px; width: 20px" src="@/assets/student/icon-micro.png" alt="" />
                    <span
                      style="margin-left: 4px; vertical-align: middle; color: #bfbfbf"
                      class="pc_font_size12 font_size14 ipad_font_size16"
                      >{{ item.ClickNum }}</span
                    >
                  </span>
                  <br />
                  <el-button
                    style="margin-top: 10%"
                    type="primary pc_font_size14 font_size18 ipad_font_size20"
                    @click="DialogVideoUrl(item)"
                    >学习指导</el-button
                  >
                </div>
              </el-carousel-item>
            </el-carousel>
          </el-card>
        </div>
        <div class="NO_pers" v-if="this.AdaptiveVideos == null || this.AdaptiveVideos.length == 0">
          <div>
            <img src="@/assets/defaultImg.png" />
            <p>哎呀 ~ 暂无相关内容</p>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="视频"
      width="900"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <video
        :preload="preload"
        v-if="isShow"
        :poster="CoverUrl"
        :height="height"
        :width="width"
        :controls="controls"
        :autoplay="autoplay"
        ref="vueRef"
      >
        <source :src="videoSrc" type="video/mp4" />
      </video>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/utils.less'
import echarts from 'echarts'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import ExamFormatCommon from '@/common/ExamFormatCommon'
export default {
  name: 'CorrectPersonalPractice',
  created() {
    this.paperId = this.$route.query.paperId
    this.PaperTitle = this.$route.query.paperName
    this.userId = localStorage.getItem('UserId')
    this.Init()
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
    // this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)
  },
  mounted() {
    // this.videoElement = document.getElementsByTagName('video') // 获取页面上所有的video对象
  },
  data() {
    return {
      userId: '',
      paperId: '',
      percentage: 10,
      PaperTitle: '',
      SubmitTime: '',
      RevisedNum: 0,
      WaitReviewSub: 0, // 主观题待批阅数量
      accuracyColor: '#68BB97',
      ProgressList: [],
      AdaptivePapers: [],
      AdaptiveVideos: [],
      PaperList: [],
      dialogVisible: false,
      videoSrc: '',
      CoverUrl: '',
      isShow: false,
      width: '820', // 设置视频播放器的显示宽度（以像素为单位）
      height: '500', // 设置视频播放器的显示高度（以像素为单位）
      preload: 'auto', // 建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
      autoplay: '',
      controls: true, // 确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
      //  多学科部分
      bgColog: {},
      Multidisciplinary: {},
      finishData: {},
      subjectiveCount: {},
      IsPhotograph: 0, // 判断当前试题是否为高中拍照试题
      PhotographIsCorrect: false,
      LoginSource: localStorage.getItem('LoginSource') // 登录渠道 1：专科专练，2：三个助手
    }
  },
  watch: {
    $route() {
      this.paperId = this.$route.query.paperId
      this.PaperTitle = this.$route.query.paperName
      const ChangData = window.location.href.split('?')[1]
      this.Multidisciplinary = JSON.parse(localStorage.getItem('Multidisciplinary'))
      if (this.$route.fullPath === '/Student/Exam_Student/AnswerDetail?' + ChangData) {
        this.$router.go(0)
        this.Init()
      }
    },
  },
  methods: {
    GoBack() {
      // this.$router.go(-1)
      this.$router.push('/Student/TaskCenter')
    },
    async Init() {
      await this.GetSudentFinishPaperContent()
      await this.GetAdaptivePaper()
    },
    // 个性化练习试卷报告 '1480449332074254337'
    async GetAdaptivePaper() {
      const params = {
        userId: this.userId,
        paperId: this.paperId,
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetAdaptivePaper', params)
      if (res.data.Success) {
        this.SubmitTime = res.data.Data.SubmitTime
        //过滤掉自适应练习 2023/7/21 周京鹏提出
        this.AdaptivePapers = res.data.Data.AdaptivePapers.filter((item) => item.Type == 2)
        this.AdaptiveVideos = res.data.Data.AdaptiveVideos
      }
    },
    // 跟腱练习去订正
    toCorrectPersonalPractice(item) {
      if (item.NoCorrectCount == 0) {
        this.$message({
          message: '暂无题目需要更正！',
          type: 'warning',
        })
        return
      }
      const data = {
        Id: this.Multidisciplinary.SubjectId,
        name: this.Multidisciplinary.name,
      }
      this.$router.push({
        path: '/Student/Exam_Paper/correctPersonalPractice',
        query: {
          SubjectIdName: JSON.stringify(data),
          paperId: item.PaperId,
          type: item.Type,
        },
      })
    },
    // 主观题订正接口
    GetStudentPaperErrorCountSub() {
      const params = {
        userId: this.userId,
        paperId: this.paperId,
      }
      this.$uwonhttp.post('/Paper/Paper/GetStudentPaperErrorCountSub', params).then((res) => {
        this.subjectiveCount = res.data.Data
        this.RevisedNum = res.data.Data.ErrorCount
        this.WaitReviewSub = res.data.Data.WaitReviewSub
      })
    },
    // 获取学生订正错误
    async GetStudentPaperErrorCount() {
      const params = {
        userId: this.userId,
        paperId: this.paperId,
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetStudentPaperErrorCount', params)
      if (res.data.Success) {
        this.$nextTick(() => {
          this.RevisedNum = res.data.Data
        })
      }
    },

    // 获取学生完成试卷信息
    async GetSudentFinishPaperContent() {
      const params = {
        userId: this.userId,
        paperId: this.paperId,
      }
      const res = await this.$uwonhttp.post('/Paper/Paper/GetSudentFinishPaperContent', params)
      if (res.data.Success) {
        const Arr = res.data.Data.Items
        ExamFormatCommon.filterZuheti(Arr)
        const CorrectNum = []
        Arr.forEach((item) => {
          if (item.IsKeyTrue == '1') {
            CorrectNum.push(item)
          }
        })
        this.ProgressList.push({
          planNum: Number(CorrectNum.length),
          completeNum: Number(Arr.length),
        })
        this.PaperList = Arr
        this.finishData = res.data.Data
        this.IsPhotograph = res.data.Data.IsPhotograph
        this.PhotographIsCorrect = res.data.Data.PhotographIsCorrect
        if (res.data.Data.IsExistSubjective || res.data.Data.IsPhotograph === 1) {
          this.GetStudentPaperErrorCountSub()
        } else {
          await this.GetStudentPaperErrorCount()
        }
      } else {
        this.$message.error(res.data.Msg)
      }
    },

    async DialogVideoUrl(item) {
      let Video_Type = ''
      if (item.Type == '1') {
        Video_Type = 2
      }
      if (item.Type == '2') {
        Video_Type = 3
      }
      const params = {
        userId: localStorage.getItem('UserId'),
        paperId: this.paperId,
        itemId: '',
        type: Video_Type,
        videoId: item.Id,
      }
      this.isShow = false // 销毁组件
      this.$nextTick(() => {
        this.isShow = true // 重建组件
      })
      const res = await this.$uwonhttp.post('/Paper/Paper/PaperStudentEvent', params)
      this.dialogVisible = true
      this.CoverUrl = item.CoverUrl
      this.videoSrc = item.VideoUrl
    },

    closeDialog() {
      this.$refs.vueRef.pause()
    },

    // 跳转题目解析
    toReview(sIndex) {
      const paperId = this.$route.query.paperId
      const data = {
        Id: this.Multidisciplinary.SubjectId,
        name: this.Multidisciplinary.name,
      }
      if (sIndex == '0') {
        sIndex = 1
      } else {
        sIndex++
      }
      this.$router.push({
        path: '/Student/Exam_Student/FollowReview',
        query: {
          SubjectIdName: JSON.stringify(data),
          paperId: this.paperId,
          sIndex,
        },
      })
    },

    // 跳转订正错题
    toCorrect() {
      // const paperId = this.$route.query.paperId
      const data = {
        Id: this.Multidisciplinary.SubjectId,
        name: this.Multidisciplinary.name,
      }
      this.$router.push({
        path: '/Student/Exam_Paper/cleaningWrongQuestion',
        query: {
          SubjectIdName: JSON.stringify(data),
          paperId: this.paperId,
          dataId: 1,
          from: 'ad',
        },
      })
    },

    is_toCorrect() {
      this.$alert('暂无题目需订正哦~', '提示', {
        confirmButtonText: '确定',
        callback: (action) => {},
      })
    },

    //
    PaperItem(item) {
      const data = {
        Id: this.Multidisciplinary.SubjectId,
        name: this.Multidisciplinary.name,
      }
      // 专科专练需要中间页面
      if(this.LoginSource == 1){
        this.$router.push({
          path: '/Student/Exam_Student/AnswerReady',
          query: {
            SubjectIdName: JSON.stringify(data),
            paperId: item.PaperId,
            type: item.Type,
            HaveSpecial: 'false',
            IsExistSubjectItem: item.IsExistSubjective,
          },
        })
      } else {
        // 三个助手无需中间页面
        if(this.IsPhotograph == 1){
          this.$router.push({
            path: '/Student/Exam_Student/highSchoolCapture/captureAnswer',
            query: {
              paperId: item.PaperId,
              isDZB: 2
            }
          })
        } else {
          if (item.Type == 10) { // 未知页面
            this.$router.push({
              path: '/Student/Exam_Student/AnswerPaper',
              query: {
                paperId: item.PaperId,
                // SubjectIdName: JSON.stringify(this.SubjectIdName) // 学科ID
              }
            })
          }
          if (item.Type != 10) { // 正常电子试卷作答的跟进练习
            this.$router.push({
              path: '/Student/Exam_Student/AdaptivePaper',
              query: {
                paperId: item.PaperId,
                type: item.Type,
                from: ''
              }
            })
          }
        }
      }

    },

    PaperAnalysis(item) {
      const data = {
        Id: this.Multidisciplinary.SubjectId,
        name: this.Multidisciplinary.name,
      }
      this.$router.push({
        path: '/Student/Exam_Student/AnswerAnalysis',
        query: {
          SubjectIdName: JSON.stringify(data),
          paperId: item.PaperId,
          type: item.Type,
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
.Answer_title {
  display: flex;
  justify-content: space-between;
  //font-size: 22px;
  padding: 15px;
  background: #ffffff;
  border-radius: 10px;
}
// 多学科部分
.xuekeStyle {
  padding: 4px 18px;
  border-radius: 100px;
}
.Progress {
  padding: 10px;
  text-align: center;
  /deep/.ant-progress-circle .ant-progress-text {
    font-size: 20px;
  }
  div {
    margin: 0 5%;
  }
}
.el-carousel__item {
  width: 80%;
  height: 80%;
  padding: 20px;
  left: 5%;
  top: 10%;
  display: flex;
  justify-content: space-around;

  .Carousel_right {
    line-height: 30px;
    //span {
    //  font-size: 17px;
    //}
  }
}
/deep/.el-card__body {
  padding: 0;
}

.Title_Name {
  color: #6a6a6a;
  overflow: hidden;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  white-space: nowrap;
  width: 110px;
  display: block;
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}
/deep/.el-dialog__body {
  text-align: center;
}
/deep/.el-carousel__container {
  height: 280px;
}
.NO_pers {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  p {
    font-size: 18px;
    font-weight: 500;
    color: #b8b8b8;
    line-height: 28px;
    text-align: center;
  }
}
</style>
