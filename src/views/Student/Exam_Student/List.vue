﻿<template>
  <a-card :bordered="false">
    <div class="table-operator" v-if="!(roleId == '9999' || roleId == '9')">
      <a-button type="primary" icon="plus" @click="hanldleAdd()">新建</a-button>
      <a-button type="primary" icon="minus" @click="handleDelete(selectedRowKeys)" :disabled="!hasSelected()" :loading="loading">删除</a-button>
      <a-button type="primary" icon="redo" @click="getDataList()">刷新</a-button>
      <a-button type="primary" icon="el-icon-folder-opened" @click="Bulkload">批量导入</a-button>
    </div>

    <div class="table-page-search-wrapper">
      <a-form layout="inline" v-model="searchForm">
        <a-row :gutter="10">
          <a-col :md="4" :sm="24">
            <a-form-item label="查询类别">
              <a-select allowClear v-model="searchForm.condition">
                <a-select-option key="RealName">姓名</a-select-option>
                <a-select-option key="PhoneNum">手机号码</a-select-option>
                <a-select-option key="StudentNo">学号</a-select-option>
                <a-select-option key="ClassName">班级</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24">
            <a-form-item>
              <a-input v-model="searchForm.keyword" placeholder="关键字" />
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24">
            <a-form-item label="区">
              <a-select allowClear v-model="searchForm.areaId" @change="change(searchForm.areaId, 0)">
                <a-select-option a-select-option v-for="item in operationalArea" :key="item.Id">{{
                  item.Name
                }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="5" :sm="24" v-show="searchForm.areaId">
            <a-form-item label="校">
              <!--            
              <a-select allowClear v-model="schoolId" @change="change(schoolId,1)">
                <a-select-option a-select-option v-for="item in operationalSchool" :key="item.Id">{{ item.SchoolName }}</a-select-option>
              </a-select> -->
              <a-tree-select allowClear v-model="searchForm.schoolId" show-search :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择" allow-clear tree-default-expand-all @change="changeTree"
                treeNodeFilterProp="title">
                <!-- treeNodeFilterProp 改变搜索指向 -->
                <a-tree-select-node :value="item.Id" :title="item.SchoolName" v-for="item in operationalSchool" :key="item.Id">
                </a-tree-select-node>
              </a-tree-select>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24" v-show="searchForm.schoolId">
            <a-form-item label="年级">
              <a-select allowClear v-model="searchForm.gradeId" @change="change(searchForm.gradeId, 2)">
                <a-select-option a-select-option v-for="item in operationalGrade" :key="item.Id">{{
                  item.Gradename
                }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="3" :sm="24" v-show="searchForm.gradeId">
            <a-form-item label="班">
              <a-select allowClear v-model="classId" @change="change(classId, 3)">
                <a-select-option a-select-option v-for="item in operationalClass" :key="item.ClassId">{{
                  item.ClassName
                }}</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <!--           <a-col :md="3" :sm="24">
            <a-form-item>
              <a-input v-model="schoolName" placeholder="学校名称" />
            </a-form-item>
          </a-col> -->
          <a-col :md="3" :sm="24">
            <a-button type="primary" @click="getDataList">查询</a-button>
            <a-button style="margin-left: 8px" @click="searchForm = {}">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <a-table ref="table" :columns="columns" rowKey="Id" :dataSource="data" :pagination="pagination" :loading="loading" @change="handleTableChange" :rowSelection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
        columnWidth: '60px',
        type: 'checkbox',
      }" :bordered="true">
      <template slot="PhoneNum" slot-scope="text, record, index">
        <span>{{ text }}</span>

        <a-button @click="getPhone(record.Id, index)" value="small" type="primary" style="margin-left: 14px" shape="circle" icon="search">
        </a-button>
      </template>
      <span slot="UserPinyin" slot-scope="text, record">
        <template>
          <editable-cell :text="text" @change="onCellChange(record.Id, 'UserPinyin', $event)" />
        </template>
      </span>

      <span slot="CurrentScore" slot-scope="text, record">
        <template>
          <editable-cell-currentScore :text="text" @change="onCellCurrentScoreChange(record.Id, 'CurrentScore', $event)" />
        </template>
      </span>

      <span slot="action" slot-scope="text, record">
        <template>
          <a @click="handleEdit(record.Id)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete([record.Id])">删除</a>
        </template>
      </span>
    </a-table>

    <edit-form ref="editForm" :parentObj="this"></edit-form>
    <Editopen ref="Editopen"></Editopen>
  </a-card>
</template>

<script>
import EditForm from './EditForm'
import Editopen from './Editopen'
const EditableCell = {
  template: `
      <div class="editable-cell">
        <div v-if="editable" class="editable-cell-input-wrapper">
          <a-input :value="value" @change="handleChange" @pressEnter="check" /><a-icon
            type="check"
            class="editable-cell-icon-check"
            @click="check"
          />
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ value || ' ' }}
          <a-icon type="edit" class="editable-cell-icon" @click="edit" />
        </div>
      </div>
    `,
  props: {
    text: String
  },
  data() {
    return {
      value: this.text,
      editable: false
    }
  },
  methods: {
    handleChange(e) {
      const value = e.target.value
      this.value = value
    },
    check() {
      this.editable = false
      this.$emit('change', this.value)
    },
    edit() {
      this.editable = true
    }
  }
}

const EditableCellCurrentScore = {
  template: `
      <div class="editable-cell-currentScore">
        <div v-if="editable" class="editable-cell-input-wrapper">
          <a-input :value="value" @change="handleCurrentScoreChange" @pressEnter="checkCurrentScore" /><a-icon
            type="check"
            class="editable-cell-icon-check"
            @click="checkCurrentScore"
          />
        </div>
        <div v-else class="editable-cell-text-wrapper">
          {{ value || ' ' }}
          <a-icon type="edit" class="editable-cell-icon" @click="editCurrentScore" />
        </div>
      </div>
    `,
  props: {
    text: String
  },
  data() {
    return {
      value: this.text,
      editable: false
    }
  },
  methods: {
    handleCurrentScoreChange(e) {
      const value = e.target.value
      this.value = value
    },
    checkCurrentScore() {
      this.editable = false
      this.$emit('change', this.value)
    },
    editCurrentScore() {
      this.editable = true
    }
  }
}

const columns = [
  { title: 'Id', dataIndex: 'Id', width: 80, align: 'center' },
  { title: '用户名', dataIndex: 'UserName', align: 'center' },
  {
    title: '手机号码',
    dataIndex: 'PhoneNum',
    width: '16%',
    align: 'center',
    scopedSlots: { customRender: 'PhoneNum' }
  },
  { title: '姓名', dataIndex: 'RealName', align: 'center' },
  { title: '性别', dataIndex: 'SexText', align: 'center' },
  {
    title: '注册时间',
    dataIndex: 'CreateTime',
    align: 'center',
    scopedSlots: { customRender: 'CreateTime' },
    sorter: true
  },
  {
    title: '学号',
    dataIndex: 'UserPinyin',

    align: 'center',
    scopedSlots: { customRender: 'UserPinyin' },
    sorter: true
  },
  { title: '班级', dataIndex: 'ClassName', align: 'center' },
  { title: '学校', dataIndex: 'SchoolName', align: 'center' },
  {
    title: '跃力值',
    dataIndex: 'CurrentScore',

    align: 'center',
    scopedSlots: { customRender: 'CurrentScore' }
  },
  { title: '操作', dataIndex: 'action', scopedSlots: { customRender: 'action' }, align: 'center' }
]
let checkBoxObj
if (localStorage.Id == '9999' || localStorage.Id == '9') {
  columns.pop()
  // checkBoxObj=null
}
/* else{
  checkBoxObj={ selectedRowKeys: selectedRowKeys, onChange: onSelectChange,columnWidth:'60px',type:'checkbox',}
} */

export default {
  components: {
    EditForm,
    EditableCell,
    EditableCellCurrentScore,
    Editopen
  },
  mounted() {
    this.getDataList()
    this.getArea()
  },

  data() {
    return {
      data: [],
      pagination: {
        current: 1,
        pageSize: 100,
        showTotal: (total, range) => `总数:${total} 当前:${range[0]}-${range[1]}`
      },
      filters: {},
      sorter: { field: 'CreateTime', order: 'descend' },
      loading: false,
      columns,
      queryParam: {},
      selectedRowKeys: [],
      operationalArea: [],
      operationalSchool: [],
      operationalGrade: [],
      operationalClass: [],
      //区id
      areaId: '',
      //学校id
      schoolId: '',
      //年级
      gradeId: '',
      // 班级
      classId: '',
      //学校名称
      schoolName: '',
      //手机号
      phone: '',
      //表单
      searchForm: {},
      roleId: localStorage.Id
      // checkBoxObj,
    }
  },
  methods: {
    handleTableChange(pagination, filters, sorter) {
      this.pagination = { ...pagination }
      this.filters = { ...filters }
      this.sorter = { ...sorter }
      this.getDataList()
    },
    async getPhone(Id, index) {
      const req = {
        userId: localStorage.UserId,
        studentId: Id
      }
      const res = await this.$http.post('/Student/Exam_Student/GetStudentPhoneById', req)
      this.data[index].PhoneNum = res.Data
    },
    getDataList() {
      var that = this

      this.selectedRowKeys = []

      this.loading = true
      this.$http
        .post('/Student/Exam_Student/GetDataList', {
          PageIndex: this.pagination.current,
          PageRows: this.pagination.pageSize,
          SortField: this.sorter.field,
          SortType: this.sorter.order,
          ...this.searchForm,
          // ...this.queryParam,
          // ...this.filters,

          /*           areaId: this.areaId,
          schoolId: this.schoolId,
          gradeId: this.gradeId, */
          classId: this.classId,
          userId: localStorage.UserId
        })
        .then(resJson => {
          this.loading = false
          this.data = resJson.Data
          const pagination = { ...this.pagination }
          pagination.total = resJson.Total
          this.pagination = pagination
        })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
    },
    hasSelected() {
      return this.selectedRowKeys.length > 0
    },
    hanldleAdd() {
      this.$refs.editForm.openForm()
    },
    handleEdit(id) {
      this.$refs.editForm.openForm(id)
    },
    handleDelete(ids) {
      var thisObj = this
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return new Promise((resolve, reject) => {
          thisObj.$http.post('/Student/Exam_Student/DeleteData', { ids: ids }).then(resJson => {
            resolve()

            if (resJson.Success) {
              thisObj.$message.success('操作成功!')

              thisObj.getDataList()
            } else {
              thisObj.$message.error(resJson.Msg)
            }
          })
        })
      })
    },
    onCellChange(key, dataIndex, value) {
      this.$http.post('/Student/Exam_Student/UpdateStudentNo', { studentId: key, studentNo: value }).then(resJson => {
        if (resJson.Success) {
          this.$message.success('操作成功!')
        } else {
          this.$message.error(resJson.Msg, 2)
        }
      })
    },
    onCellCurrentScoreChange(key, dataIndex, value) {
      this.$http
        .post('/Student/Exam_Student/UpdateUserCurrentScore', { studentId: key, currentScore: value })
        .then(resJson => {
          if (resJson.Success) {
            this.$message.success('操作成功!')
          } else {
            this.$message.error(resJson.Msg, 2)
          }
        })
    },
    async getArea() {
      const resJson = await this.$http.post('/Area/Area/GetAreaListBy', { userId: localStorage.UserId })
      this.operationalArea = resJson.Data
    },
    change(id, status) {
      if (status == 0) {
        this.searchForm.areaId = id
        this.searchForm.schoolId = ''
        this.searchForm.gradeId = ''
        this.classId = ''
        this.getSchool()
      } else if (status == 2) {
        this.searchForm.gradeId = id
        this.classId = ''
        this.getClass()
      } else if (status == 3) {
        this.classId = id
      }
    },
    changeTree(id) {
      this.searchForm.schoolId = id

      this.searchForm.gradeId = ''
      this.classId = ''

      this.getGrade()
    },
    async getSchool() {
      const resJson = await this.$http.post('/School/Exam_School/GetSchoolByAreaIdUserId', {
        areaId: this.searchForm.areaId,
        userId: localStorage.UserId
      })
      this.operationalSchool = resJson.Data
    },
    async getGrade() {
      const resJson = await this.$uwonhttp.get('/Class/TeacherClassManager/GetGradeList')

      this.operationalGrade = resJson.data.Data
    },
    async getClass() {
      const resJson = await this.$uwonhttp.post('/Class/ClassManager/GetClassBySchoolGrade', {
        schoolId: this.searchForm.schoolId,
        grade: this.searchForm.gradeId
      })

      this.operationalClass = resJson.data.Data
    },
    Bulkload() {
      this.$refs.Editopen.showModal()
    }
  }
}
</script>
