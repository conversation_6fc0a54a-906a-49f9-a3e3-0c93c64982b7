<template>
  <div>
    <h2>答题开始</h2>
    <!-- 全屏 -->
    <FullScreen></FullScreen>
    <div class="">
      <span class="timer">{{ callinTime }}</span>
      <div class="title">
        <h2>{{ paperName }}</h2>
      </div>
      <!-- 步骤条 -->
      <div class="my-step">
        <a-steps :current="current[this.paperId]" @change="onChange" type="navigation">
          <a-step v-for="(item , index) in paperData" :key="index" icon="" status="wait">
          </a-step>
        </a-steps>
      </div>

      <!-- 步骤条对应内容 -->
      <div class="my-tabs" v-if="paperData.length > 0">
        <a-tabs @change="callback" v-model="num" tabPosition="left">
          <a-tab-pane class="my-tab-pane" v-for="item in paperData" :key="item.ItemId" tab="" disabled>
            <div v-html="item.Title" class="stem" v-katex></div>
            <div>
              <div v-if="newTestQuestionType === '2' || newTestQuestionType === '11'">
                <a-form-item :label="label">
                  <a-radio-group @change="SelectOptions">
                    <a-radio :style="radioStyle" :value="i.ItemId +'|'+ i.Option" v-for="i in options" :key="i.Option">
                      {{ i.Option }}:&nbsp;&nbsp;
                      <span v-html="i.Content" style="fontSize: 20px;"></span>
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
              </div>

              <div v-if="newTestQuestionType === '10'" class="Multiple">
                <a-checkbox-group @change="SelectOptions1" class="Multiple">
                  <div class="m-selection">{{ label }}&nbsp; :</div>
                  <a-row>
                    <a-col :span="24">
                      <a-checkbox v-for="(i) in options" :key="i.Option" :value="i.ItemId + '|' + i.Option">
                        {{ i.Option }}:
                        <span v-html="i.Content" style="fontSize: 20px;"></span>
                      </a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 切换题目 -->
      <div class="steps-action">
        <a-button v-if="current[paperId] > 0" style="margin-right: 8px" @click="prev">
          上一题
        </a-button>
        <a-button v-if="current[paperId] == paperData.length - 1" type="primary" @click="viewsSubject">
          提交
        </a-button>
        <a-button v-if="current[paperId] < paperData.length - 1" type="primary" style="margin-left: 8px" @click="next">
          下一题
        </a-button>
      </div>
    </div>
  </div>

</template>

<script>
// import moment from 'moment'
// 全屏显示
import FullScreen from '@/components/FullScreen/FullScreen'

export default {
  components: {
    // 全屏
    FullScreen
  },
  created() {
    this.paperId = this.$route.query.paperId
    // console.log('created--------试卷数据', this.paperId)
    this.$set(this.current, this.paperId, this.current[this.paperId] ? this.current[this.paperId] : 0)
    // 初始化数据
    this.getTitleList(this.paperId)
    // 获取试卷名称
    this.getTestPaperName(this.paperId)
  },
  mounted() {
    this.timeRecord(true)
  },
  Destroy() {},
  data() {
    return {
      // 做题计时
      callinTime: '',
      // 完成题目时间
      completeTime: '',
      num: '',
      // 模态框显示隐藏
      visible: false,
      // 自定义时间单选框
      value: '',
      // 自定义时间显示隐藏
      CustomTime: true,
      // 试卷id
      paperId: '',
      // 试卷名称
      paperName: '',
      // 试卷数据
      paperData: [],
      // 步骤条切换
      current: {},
      // 步骤条对应
      activeKey: {},
      // 上题的id
      itemId: '',
      // 目前的id
      id: '',
      newItemId: '',
      // 所有做的题的答案
      eachAnswer: [],
      // 每道选择题的答案
      choiceAnswer: '',
      // 所有填空题的答案
      testAnswer: [],
      // 所有选择题的答案
      testChoiceAnswer: [],
      // 多选题答案
      testChoiceAnswer1: [],
      // 当前试题类型
      newTestQuestionType: '',
      // 选项
      options: [],
      radioStyle: {
        display: 'block',
        height: '90px',
        lineHeight: '90px'
      },
      // 每个填空题
      each: [],
      // 题型显示
      label: ''
    }
  },
  watch: {
    $route() {
      this.paperId = this.$route.query.paperId
      if (this.$route.fullPath === '/Student/Exam_Student/ExamAnswer?paperId=' + this.paperId) {
        this.timeRecord(false)
        this.timeRecord(true)
        // 初始化数据
        this.getTitleList(this.paperId)
        // 获取试卷名称
        this.getTestPaperName(this.paperId)
        this.eachAnswer = []
        this.testChoiceAnswer = []
        this.testChoiceAnswer1 = []
        this.$set(this.current, this.paperId, this.current[this.paperId] ? this.current[this.paperId] : 0)
        this.$set(this.activeKey, this.paperId, this.activeKey[this.paperId] ? this.activeKey[this.paperId] : 0)
      }
    }
  },

  methods: {
    // 计时器
    timeRecord(bolean) {
      const _this = this
      let hour, minute, second
      hour = minute = second = 0
      if (bolean === true) {
        _this.timer = setInterval(() => {
          if (second >= 0) {
            second = second + 1
          }
          if (second >= 60) {
            second = 0
            minute = minute + 1
          }
          if (minute >= 60) {
            minute = 0
            hour = hour + 1
          }
          _this.callinTime = hour + '时' + minute + '分' + second + '秒'
        }, 1000)
      } else {
        window.clearInterval(_this.timer)
      }
    },
    next() {
      this.$nextTick(function() {
        this.current[this.paperId]++
        this.activeKey[this.paperId] = this.current[this.paperId]
        // 填空处理
        this.updateFormat()
        // 题目切换处理
        this.SwitchTopics()

        // 获取上题答案

        // this.getFillAnswer(this.itemId)
      })
    },
    prev() {
      this.$nextTick(function() {
        this.current[this.paperId]--
        this.activeKey[this.paperId] = this.current[this.paperId]

        // 填空处理
        this.updateFormat(this.paperData)
        // 题目切换处理
        this.SwitchTopics()
        // 处理默认选项
        // this.handleDefalutOption(this.newItemId,this.selectOption)
      })
    },
    // 点击步骤条
    onChange(current) {
      // this.current[this.paperId] = current;
      this.activeKey[this.paperId] = current
      this.current[this.paperId] = current
      // 题目切换处理
      this.SwitchTopics()
    },
    callback(key) {
      // key = this.current
    },
    // 提交试卷
    viewsSubject() {
      const that = this

      const modal = this.$confirm({
        title: '温馨提示',
        content: '是否确认提交试卷',
        okText: '确认提交',
        cancelText: '继续答题',
        // 确定按钮回调
        onOk(e) {
          // 停止计时
          that.timeRecord(false)
          that.testAnswer = []
          // 清除提示框
          modal.destroy()
          that.completeTime = document.getElementsByClassName('timer')[0].innerText
          const inputs = document.getElementsByTagName('input')

          for (var i = 0; i < inputs.length; i++) {
            const itemId = inputs[i].getAttribute('itemid')
            const each = inputs[i].value

            const exist = that.testAnswer.some(item => {
              if (item.ItemId === itemId) {
                return true
              }
            })

            if (!exist) {
              that.testAnswer.push({
                ItemId: itemId,
                AnswerArray: [each],
                Answer: each
              })
            } else {
              that.testAnswer.forEach(item => {
                if (item.ItemId === itemId) {
                  item.AnswerArray.push(each)
                }
              })
            }
          }

          for (var x = 0; x < that.testAnswer.length; x++) {
            that.testAnswer[x].Answer = that.testAnswer[x].AnswerArray.join('|')
          }

          that.eachAnswer = [...that.testChoiceAnswer, ...that.testAnswer, ...that.testChoiceAnswer1]
          const EachAnswer = that.eachAnswer.filter(item => item.ItemId !== null)
          that.eachAnswer = EachAnswer

          // var hash = {}
          // EachAnswer = EachAnswer.reduce(function (item, next) {
          //   hash[next.ItemId] ? '' : hash[next.ItemId] = true && item.push(next)
          //   return item
          // }, [])
          // that.eachAnswer.length !== that.paperData.length
          if (that.eachAnswer.length !== that.paperData.length) {
            return that.$message.error('请提交完整的答案')
          } else {
            that.$message.success('提交成功')
            that.$router.push({
              path: '/Student/Exam_Student/JobReport',
              query: {
                paperId: that.paperId,
                answerJson: JSON.stringify(that.eachAnswer),
                completeTime: that.completeTime
              }
            })
          }
        }
      })
    },
    // 获取试卷信息
    async getTitleList(id) {
      this.paperId = id
      await this.$http
        .get('/Paper/Exam_Item/GetAuditPaperItemsList', {
          params: {
            paperId: id
          }
        })
        .then(res => {
          this.paperData = res.Data
          // this.itemId = this.paperData[this.current[this.paperId]].ItemId
          this.id = this.paperData[this.current[this.paperId]].ItemId
          // this.testQuestionType = this.paperData[this.current[this.paperId]].TypeId
          this.num = this.id

          this.paperData.forEach((item, index) => {
            const reg = /(#&\d+@)/g
            // const id = item.ItemId
            const inputele =
              '<input  type="text" itemId= "' +
              item.ItemId +
              '" style="width: 100px;border: 0px; BACKGROUND-COLOR: transparent;text-align:center;border-bottom:1px solid grey;" name="' +
              'input' +
              '"/>'
            const stem = item.Title.replace(reg, inputele)
            item.Title = stem
          })
        })
      this.newTestQuestionType = this.paperData[this.current[this.paperId]].TypeId
      this.$http.post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', { itemId: this.id }).then(res => {
        this.options = res.Data
      })
    },
    // 获取试卷的名称
    getTestPaperName(id) {
      this.$http.post('/Paper/Exam_Paper/GetTheData', { id }).then(res => {
        this.paperName = res.Data.Title
      })
      this.$set(this.current, this.paperId, this.current[this.paperId] ? this.current[this.paperId] : 0)
      this.$set(this.activeKey, this.paperId, this.activeKey[this.paperId] ? this.activeKey[this.paperId] : 0)
    },
    // 选择题的选项变化
    SelectOptions(e) {
      const choiceAnswer = e.target.value
      const choiceArray = choiceAnswer.split('|')

      const itemid = choiceArray[0]
      const selectOption = choiceArray[1]

      const exist = this.testChoiceAnswer.some(item => {
        if (item.ItemId === itemid) {
          return true
        }
      })

      if (!exist) {
        this.testChoiceAnswer.push({
          ItemId: itemid,
          Answer: selectOption
        })
        this.selectOption = selectOption
      } else {
        this.testChoiceAnswer.forEach(item => {
          if (item.ItemId === itemid) {
            item.Answer = selectOption
            this.selectOption = selectOption
          }
        })
      }
    },
    // 修改解析格式
    updateFormat(paperData) {
      for (var i = 0; i < this.paperData.length; i++) {
        var item = this.paperData[i]
        const options = item.AuditPaperItemAnswers
        let answer = null
        let analysi = null
        options.forEach(option => {
          if (option.Type === 8) {
            answer = option
          }

          if (option.Type === 10) {
            analysi = option
          }
        })

        if (answer != null && analysi != null) {
          const answerArray = answer.Content.split('|')
          const matchRet = analysi.Content.match(/(#&\d+@)/g)
          if (matchRet != null) {
            matchRet.forEach(m => {
              const idx = Number.parseInt(/\d+/.exec(m))
              analysi.Content = analysi.Content.replace(m, answerArray[idx])
            })
          }
        }
      }
    },
    // 多选题的选项
    SelectOptions1(checkedValues) {
      checkedValues.forEach(item => {
        const items = item.split('|')
        const itemid = items[0]
        const selectOption = items[1]

        const exist = this.testChoiceAnswer1.some(item => {
          if (item.ItemId === itemid) {
            return true
          }
        })

        if (!exist) {
          this.testChoiceAnswer1.push({
            ItemId: itemid,
            AnswerArray: [selectOption],
            Answer: selectOption
          })
        } else {
          this.testChoiceAnswer1.forEach(item => {
            if (item.ItemId === itemid) {
              item.AnswerArray.push(selectOption)
            }
          })
        }
        this.testChoiceAnswer1.forEach(item => {
          const b = checkedValues.map(res => {
            return res.split('|')[1]
          })
          const TestAnswer = [...new Set(item.AnswerArray)].filter(i => {
            if (b.includes(i)) {
              return i
            }
          })
          item.Answer = TestAnswer.join('|')
        })
      })
    },
    // 处理切换题目的方法
    SwitchTopics() {
      // 每道提的id
      this.itemId = this.paperData[this.current[this.paperId]].ItemId
      this.num = this.itemId

      this.newTestQuestionType = this.paperData[this.current[this.paperId]].TypeId
      if (this.newTestQuestionType === '2') {
        this.label = '单选题'
        this.$http.post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', { itemId: this.itemId }).then(res => {
          this.options = res.Data
        })
      }
      if (this.newTestQuestionType === '10') {
        this.label = '多选题'
        this.$http.post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', { itemId: this.itemId }).then(res => {
          this.options = res.Data
        })
      }
      if (this.newTestQuestionType === '11') {
        this.label = '判断题'
        this.$http.post('/Paper/Exam_ItemOptiAnswer/GetOptionsByItemId', { itemId: this.itemId }).then(res => {
          this.options = res.Data
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
h2 {
  height: 45px;
  border-bottom: 2px solid #ccc;
}
.stem {
  font-size: 20px;
}
.timer {
  position: absolute;
  right: 90px;
  top: 141px;
}
.m-b {
  margin-bottom: 20px;
}
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 80px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}
.examine .ex-release {
  position: absolute;
  right: 127px;
  top: 132px;
  width: 102px;
  height: 37px;
  line-height: 37px;
  text-align: center;
  border-radius: 20px;
  cursor: pointer;
}
/* 标题 */
.title {
  text-align: center;
  margin-bottom: 25px;
  h2 {
    border: none;
    height: 32px;
  }
  .release-time {
    padding: 10px 0;
  }
}
.my-step /deep/ .ant-steps {
  width: 80%;
  margin: 0 auto;
  margin-bottom: 20px;
}
// tabs栏
.my-tabs {
  width: 1125px;
}
.my-tabs /deep/ .ant-tabs-nav .ant-tabs-tab {
  cursor: auto;
}
.test-modify {
  // position: absolute;
  // right: 100px;
  float: right;
  margin-right: 100px;
  width: 78px;
  height: 35px;
  line-height: 35px;
  margin: 25px;
  text-align: center;
  background-color: #00aaff;
  border-radius: 5px;
  cursor: pointer;
}
// 图片样式
.ant-radio-wrapper /deep/ img {
  width: 30px;
}
.ant-checkbox-wrapper /deep/ img {
  width: 30px;
}
// 多选框样式
.Multiple {
  margin-top: 12px;
  .m-selection {
    margin-bottom: 12px;
  }
  img {
    width: 25px;
    height: 40px;
  }
}
.Multiple /deep/ .ant-checkbox-wrapper {
  width: 100%;
  margin-bottom: 50px;
}
.ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}
</style>
