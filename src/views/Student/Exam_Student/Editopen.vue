<template>
  <div>
    <el-dialog title="批量导入" :visible.sync="dialogVisible" width="40%">
      <span>
        <el-form label-width="100px" :model="ruleForm" :rules="rules" ref="ruleForm">
          <el-form-item label="选择区域" prop="Area_val">
            <el-radio-group v-model="ruleForm.Area_val" @change="Change_Areaval">
              <el-radio v-for="item in authTypeList" :key="item.id" :label="item.id">
                {{ item.value }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择学校" prop="Scholl_val">
            <el-select v-model="ruleForm.Scholl_val" filterable placeholder="请选择活动区域" @change="SchoolChange">
              <el-option v-for="item in Scholl_list" :key="item.SchoolId" :label="item.title" :value="item.SchoolId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="ruleForm.Name_val" placeholder="请输入创建人"></el-input>
          </el-form-item>
          <el-form-item label="初始化密码" prop="pass">
            <el-input v-model="ruleForm.pass"></el-input>
          </el-form-item>
          <el-form-item label="导入名单">
            <el-upload class="upload-demo" :action="uploadWordUrl" :auto-upload='true' multiple :limit="3" :before-upload="beforeUploadFile" :file-list="fileList" @change="handleChangeUpload">
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传excel 文件</div>
            </el-upload>
          </el-form-item>
        </el-form>
      </span>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm(ruleForm)">确 定</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        Area_val: '',
        Scholl_val: '',
        Name_val: '',
        pass: '123456',
        region: '',
        resource: ''
      },
      userId: '',
      schoolId: '',
      Scholl_list: [],
      fileList: [],
      authTypeList: [
        { id: 1, value: '徐汇' },
        { id: 2, value: '黄浦' },
        { id: 3, value: '普陀' },
        { id: 6, value: '杨浦' },
        { id: 8, value: '松江' },
        { id: 10, value: '宝山' },
        { id: 13, value: '浦东' }
      ],
      rules: {
        Area_val: [{ required: true, message: '请选择区域', trigger: 'blur' }],
        Scholl_val: [{ required: true, message: '请选择学校', trigger: 'change' }],
        Name_val: [{ required: true, message: '请填写创建人', trigger: 'blur' }],
        pass: [{ required: true, message: '请填写密码', trigger: 'blur' }]
      },
      isDisabledUpload: false,
      uploadWordUrl_params: `${this.$rootUrl}/Student/Exam_Student/ImportStudent?`,
      uploadWordUrl: ''
    }
  },
  created() {
    this.schoolId = localStorage.getItem('SchoolId')
    this.userId = localStorage.getItem('UserId')
    this.GetSchoolData()
  },
  methods: {
    showModal() {
      this.dialogVisible = true
    },

    handleOk(e) {
      this.dialogVisible = false
    },

    Change_Areaval(val) {
      this.ruleForm.Area_val = val
      this.uploadWordUrl =
        this.uploadWordUrl_params +
        'AreaId=' +
        this.ruleForm.Area_val +
        '&SchoolId=' +
        this.ruleForm.Scholl_val +
        '&CreateName=' +
        this.ruleForm.Name_val +
        '&PassWord=' +
        this.ruleForm.pass +
        '&CreateUserId=' +
        this.userId
    },

    async GetSchoolData() {
      const res = await this.$http.post('/School/Exam_School/GetTreeDataList')
      if (res.Success) {
        this.Scholl_list = res.Data
      }
    },

    SchoolChange(val) {
      this.ruleForm.Scholl_val = val
      this.uploadWordUrl =
        this.uploadWordUrl_params +
        'AreaId=' +
        this.ruleForm.Area_val +
        '&SchoolId=' +
        this.ruleForm.Scholl_val +
        '&CreateName=' +
        this.ruleForm.Name_val +
        '&PassWord=' +
        this.ruleForm.pass +
        '&CreateUserId=' +
        this.userId
    },
    handleChangeUpload(arg1) {
      var thisObj = this
      if (arg1.file.status == 'done') {
        if (arg1.file.response.Success) {
          const paperid = arg1.file.response.Data
        }
      }
    },
    beforeUploadFile(file) {
      let extension = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (extension !== 'xlsx') {
        this.$message.warning('只能上传后缀是.xlsx的文件')
      }
    },
    beforeUpload(file) {},
    submitForm() {}
    // async submitForm(ruleForm) {
    //   const res = await this.$uwonhttp.post('/Student/Exam_Student/ImportStudent', {
    //     AreaId: this.ruleForm.Area_val,
    //     SchoolId: this.ruleForm.Scholl_val,
    //     CreateUserId: this.ruleForm.Name_val,
    //     PassWord: this.ruleForm.pass
    //   })
    //   console.log(res.Date)
    //   this.dialogVisible = false
    // }
  }
}
</script>
<style lang="less" scoped>
.el-input {
  width: 70% !important;
}
.el-select,
.el-upload {
  width: 70% !important;
}
// .el-form-item__text {
//   width: 70% !important;
// }
</style>
