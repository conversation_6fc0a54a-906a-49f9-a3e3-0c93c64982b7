<template>
  <div>
    <h1>父组件</h1>
    <textarea type="text" v-model="keyboards"></textarea>
    <!-- <input type="text" v-model="keyboards"> -->
    <keyboard @updatekey="GetKeyVal"></keyboard>
  </div>
</template>

<script>
// import Keyboard from './jianpandemo'
import Keyboard from './jianpandemo'

export default {
  data() {
    return {
      keyboards: ''
    }
  },
  components: {
    Keyboard
  },
  methods: {
    GetKeyVal(val) {
      console.log(val)
      this.keyboards = val
    }
  }
}
</script>

<style lang="less" scoped>
#app {
  width: 680px;
  margin: 20px auto;
  font-family: Verdana, Sans-Serif;

  h1 {
    color: #42b983;
    font-weight: bold;
  }

  textarea {
    display: block;
    width: 100%;
    min-height: 100px;
    padding: 0;
    margin: 20px 0;
    font-size: 16px;
  }
}
</style>
