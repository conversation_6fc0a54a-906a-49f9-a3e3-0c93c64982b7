<template>
  <div class="imgEvaluate">
    <div class="imgEvaluate_top">
      <div class="_top_dis">
        <h6>评价对象：</h6>
        <el-select v-model="value1" size="medium" placeholder="请选择" @change="optionBtn1">
          <el-option
            style="height: 40px;line-height: 36px;"
            v-for="(item,index) in options1"
            :key="index"
            :label="item.ByStudentName+'-第'+ (index+1) +'题'"
            :value="item.Oid">
          </el-option>
        </el-select>
      </div>
      <div class="_top_dis">
        <h6>评价试题：</h6>
        <el-select v-model="value2" size="medium" disabled placeholder="请选择" @change="optionBtn2">
          <el-option
            style="height: 40px;line-height: 36px;"
            v-for="(item,index) in answer"
            :key="index"
            :label="item.Title.substring(0,5)"
            :value="item.UserId">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="content" style="height: 700px;margin-top: 10px;">
      <el-carousel :autoplay="false" class="carousel" style="height:100%;">
        <el-carousel-item v-for="(item,index) in imgAnswer" :key="index" style="position: relative;">
          <span style="position: absolute;right: 0;top:0;background-color: rgba(0,0,0,0.6);padding: 2px 12px;color: #FFFFFF;">{{ Title.slice(0, 10)+'...' }}</span>
          <img :src="item" width="100%" height="100%" alt/>
        </el-carousel-item>
      </el-carousel>
      <!--  结果定位    -->
      <div class="tent" v-show="lidisList.length > 0">
        <ul>
          <li v-for="(item,index) in lidisList" :key="index">
            <div class="li_dis">{{ item.Title+(index+1) }}：<el-rate :max="item.DimStar" disabled v-model="item.Star" :colors="{10:'#F56C6C'}"></el-rate></div>
            <div>{{ item.TypeId == 1 ? item.Remark : item.EvaluationRemark }}</div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImgEvaluate',
  props: ['options1', 'options2', 'lidisList'],
  watch: {
    options1: {
      handler (newValue, oldValue) {
        this.search(newValue[0].Oid)
      },
      deep: true // 默认值是 false，代表是否深度监听
    },
    options2: {
      handler (newValue, oldValue) {
        this.options2 = newValue
      },
      deep: true // 默认值是 false，代表是否深度监听
    }
  },
  data () {
    return {
      value1: '',
      value2: '',
      answer: [],
      imgAnswer: [],
      // ExaId: null,
      value: null,
      Examvalue: null,
      Title: ''
    }
  },
  created () {
    console.log(this.options1)
    this.search(this.options1[0].Oid)
  },
  methods: {
    search (Oid) {
      this.remove()
      this.value1 = Oid
      const arr = this.options2
      arr.forEach((res) => {
        if (res.UserId === this.getUserXinXi(Oid,'ByStudentId') && res.ExamId === this.getUserXinXi(Oid,'ExamId')) {
          this.value2 = res.UserId
          this.answer.push(res)
          this.imgAnswer = this.answer[0].UserAnswer.split('|')
          this.Title = this.answer[0].Title
        }
      })
    },
    optionBtn1 (Oid) {
      const opt1 = JSON.parse(JSON.stringify(this.options1))
      this.search(Oid)
      opt1.forEach((res) => {
        if (res.Oid === Oid) {
          this.$emit('GetStudent', res.SettingId)
        }
      })
      return false
    },
    optionBtn2 (val) {

    },
    getUserXinXi (Oid, sta) {
      let id
      const ar = JSON.parse(JSON.stringify(this.options1))
      ar.forEach((res) => {
        if (res.Oid === Oid) {
          this.Examvalue = res.ExamId
          this.value = res.ByStudentId
          id = res[sta]
        }
      })
      return id
    },
    getValue2 () {
      // let Exate
      // const an = this.answer
      // an.forEach((res) => {
      //   if (res.UserId === this.value1) {
      //     Exate = res.ExamId
      //   }
      // })
      return this.Examvalue
    },
    getValue1 () {
      return this.value
    },
    // init (paperId, studentId){
    //   this.GetStudentEvaluationExamList(paperId, studentId)
    // },
    // 获取数据
    // 查询关联的试题
    //   optionTwo(){
    //
    //   }
    // 清空数据
    remove () {
      this.value1 = ''
      this.value2 = ''
      this.value = null
      this.Examvalue = null
      this.answer = []
      this.imgAnswer = []
    }
  }
}
</script>

<style lang="less" scoped>
.imgEvaluate{
  width: 100%;
  padding: 20px;
  .imgEvaluate_top{
    display: flex;
    justify-content: space-between;
    ._top_dis{
      display: flex;
      align-items: center;
      h6{
        font-size: 18px;
      }
      /deep/.el-select{
        width: 180px;
      }
    }
  }
  .content{
    position: relative;
    .carousel{
      width: 100%;
      height: 100%;
      /deep/.el-carousel__container{
        height: 100%;
      }
    }
    .tent{
      //width: 30%;
      height: 40%;
      padding: 20px;
      border-radius: 4px;
      position: absolute;
      bottom: 0px;
      left: 0px;
      background-color: rgba(0,0,0,0.6);
      z-index: 99;
      li{
        margin: 5px 0;
        font-size: 18px;
        color: #FFFFFF;
        .li_dis{
          display: flex;
          /deep/ .el-rate__icon{
            font-size: 22px;
          }
        }
      }
    }
  }
}
</style>
