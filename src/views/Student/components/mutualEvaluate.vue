<template>
  <el-dialog
    title="点评"
    top="4vh"
    :visible.sync="dialogVisible"
    width="80%"
    class="dialogClass"
    :before-close="handleClose">
    <div class="mutualEvaluate">
      <!--    <div class="_top">-->
      <!--      <div><i class="el-icon-arrow-left"></i>返回</div>-->
      <!--      <div>互评选择</div>-->
      <!--      <div>学科</div>-->
      <!--    </div>-->
      <div class="mutual">
<!--        <div class="mutual_left" :style="{'width': lidisList.length > 0 ? '100%' : '50%' }">-->
        <div class="mutual_left">
          <imgEvaluate ref="img_Evaluate" :options1="options1" :options2="options2" :lidisList="lidisList" @GetStudent="GetStudent"></imgEvaluate>
        </div>
        <div v-show="lidisList.length === 0" class="mutual_right">
          <div class="_right_click">
            <div class="_right_tit">我的点评</div>
            <div>
              <div class="_titBotm" v-for="(item,index) in dimensionList" :key="index">
                <div class="_titWeidu">
                  <span>{{ item.Title }}：</span>
                  <el-rate v-if="item.TypeId == 1" :colors="{10:'#F56C6C'}" :max="item.Star" v-model="item.star"></el-rate>
                </div>
                <el-input v-if="item.TypeId == 1" disabled :value="item.Remark" style="color:red;"></el-input>
                <el-input v-if="item.TypeId == 2" :value="item.Remark" v-model="item.Remark"></el-input>
              </div>
              <!--            <div class="_titInput">-->
              <!--              <h6>其他</h6>-->
              <!--              <el-input></el-input>-->
              <!--            </div>-->
            </div>
            <div class="_right_btn">
              <el-button size="medium" @click="handleClose">取 消</el-button>
              <el-button type="primary" size="medium" @click="submit">提 交</el-button>
            </div>
          </div>
        </div>
<!--   评价我的     -->
<!--        <div class="mutual_right" v-if="evaluateMyList.length > 0">-->
        <div class="mutual_right" v-if="sta == 2">
          <div class="imgEvaluate">
            <div class="imgEvaluate_top">
              <div class="_top_dis">
                <h6>评价我的：</h6>
                <el-select v-model="evaluateMyValue1" size="medium" placeholder="请选择" @change="evaluateMy1">
                  <el-option
                    style="height: 40px;line-height: 36px;"
                    v-for="(item,index) in evaluateMy"
                    :key="index"
                    :label="item.StudentName"
                    :value="item.Oid">
                  </el-option>
                </el-select>
              </div>
              <div class="_top_dis">
                <h6>评价试题：</h6>
                <el-select v-model="evaluateMyValue2" size="medium" disabled placeholder="请选择" @change="evaluateMy2">
                  <el-option
                    style="height: 40px;line-height: 36px;"
                    v-for="(item,index) in evaluateMytopic"
                    :key="index"
                    :label="item.Title.substring(0,5)"
                    :value="item.UserId">
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="content" style="height: 700px;margin-top: 10px;">
              <el-carousel :autoplay="false" class="carousel" style="height:100%;" v-show="evaluateMyList.length > 0">
                <el-carousel-item v-for="(item,index) in evaluateMyImgAnswer" :key="index">
                  <img :src="item" width="100%" height="100%" alt/>
                </el-carousel-item>
              </el-carousel>
              <div class="else_content" v-show="evaluateMyList.length === 0">
                暂无评价我的数据！
              </div>
              <!--  结果定位    -->
              <div class="tent" v-show="evaluateMyList.length > 0">
                <ul>
                  <li v-for="(item,index) in evaluateMyList" :key="index">
                    <div class="li_dis">{{ item.Title+(index+1) }}：<el-rate :max="item.DimStar" disabled v-model="item.Star" :colors="{10:'#F56C6C'}"></el-rate></div>
                    <div>{{ item.TypeId == 1 ? item.Remark : item.EvaluationRemark }}</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
<!--        <div class="mutual_right" v-if="evaluateMyList.length == 0 && sta == 2">-->
<!--          <div class="else_top">-->
<!--            <div>评价我的：<el-input disabled></el-input></div>-->
<!--            <div>评价试题：<el-input disabled></el-input></div>-->
<!--          </div>-->
<!--          -->
<!--        </div>-->
      </div>
    </div>
  </el-dialog>
</template>

<script>
import imgEvaluate from '@/views/Student/components/imgEvaluate'
export default {
  name: 'MutualEvaluate',
  components: {
    imgEvaluate
  },
  data () {
    return {
      // 我评价的
      dialogVisible: false,
      options1: [],
      options2: [],
      dimensionList: [],
      paperId: '',
      lidisList: [],
      sta: null,
      // 评价我的
      evaluateMy: [],
      evaluateMytopic: [],
      evaluateMyImgAnswer: [],
      evaluateMyList: [],
      evaluateMyValue1: '',
      evaluateMyValue2: '',
      evaluateMyExId: '',
      newEvaluateMyValue1: ''
    }
  },
  methods: {
    init (paperId, studentId, num) {
      // console.log(num)
      // console.log(typeof num)
      this.sta = num
      this.GetStudentEvaluationExamList(paperId, studentId, num)
      this.paperId = paperId
    },
    // newInit (paperId, studentId) {
    //   this.sta = 2
    //   this.GetStudentEvaluationExamList(paperId, studentId, 2)
    //   this.paperId = paperId
    // },
    submit () {
      // console.log(this.dimensionList)
      const EvaluaList = []
      const list = this.dimensionList
      const ExamId = this.$refs.img_Evaluate.getValue2()
      const ByStudentId = this.$refs.img_Evaluate.getValue1()
      list.forEach((res) => {
        if (res.star == 0) {
          this.$message.info('请完成星级评价！')
        } else {
          if (res.TypeId == 1) {
            EvaluaList.push({ DimId: res.DimId, Star: res.star, Content: '' })
          } else {
            EvaluaList.push({ DimId: res.DimId, Star: '', Content: res.Remark })
          }
        }
      })
      const data = {
        SettingId: this.dimensionList[0].SettingId,
        ExamId: ExamId,
        ByStudentId: ByStudentId,
        Evaluations: EvaluaList,
        PaperId: this.paperId,
        StudentId: localStorage.getItem('UserId')
      }
      this.StudentAddEvaluation(data)
    },
    GetStudentEvaluationExamList (paperId, studentId, sta) {
      console.log(sta)
      const that = this
      this.$uwonhttp.post('/Paper/TeacherPaper/GetStudentEvaluationExamList', { paperId: paperId, StudentId: studentId }).then((res) => {
        if (res.data.Success) {
          const data = res.data.Data
          this.options2 = data.Answers
          this.dialogVisible = true
          if (sta === 1) {
            // console.log('等于1')
            // return
            // 学生需要提交互评的状态
            this.options1 = this.optionSearch(data.ByStudents, 1)
            this.GetStudentDimensionList(this.options1[0].SettingId)
            return
          }
          if (sta === 2) {
            // console.log('等于2')
            // return
            // 学生查看详情的状态
            this.options1 = this.optionSearch(data.ByStudents, 2)
            const timer = setTimeout(function () {
              that.GetStudentEvaluationData()
              clearTimeout(timer)
            }, 100)
            if (data.Students.length > 0) {
              this.evaluateMy = this.optionSearch(data.Students, 2)
              this.search(this.evaluateMy[0].Oid)
              const newTimer = setTimeout(function () {
                that.GetByStudentEvaluationData()
                clearTimeout(newTimer)
              }, 100)
            }
          }
        }
      })
    },
    handleClose () {
      this.lidisList = []
      this.$parent.getPastPaper()
      this.dialogVisible = false
    },
    // 获取维度模板
    GetStudentDimensionList (settingId) {
      this.$uwonhttp.post('/Paper/TeacherPaper/GetStudentDimensionList', { SettingId: settingId }).then((res) => {
        if (res.data.Success) {
          const dataArr = []
          const arrlist = res.data.Data
          arrlist.forEach((res) => {
            if (res.TypeId == 1) {
              res.star = 0
            }
            dataArr.push(res)
          })
          this.dimensionList = dataArr
        }
      })
    },
    // 添加评价
    StudentAddEvaluation (data) {
      this.$uwonhttp.post('/Paper/TeacherPaper/StudentAddEvaluation', data).then((res) => {
        if (res.data.Success) {
          this.$message.success('提交成功')
          this.$parent.getPastPaper()
        } else {
          this.$message.error(res.data.Msg)
        }
        this.lidisList = []
        this.dialogVisible = false
      })
    },
    // 子组件传值
    GetStudent (val) {
      if (this.sta === 2) {
        this.GetStudentEvaluationData()
      } else {
        this.GetStudentDimensionList(val)
        console.log('等于1')
        return
      }
    },
    // 查看情况
    GetStudentEvaluationData () {
      const ExamId = this.$refs.img_Evaluate.getValue2()
      const ByStudentId = this.$refs.img_Evaluate.getValue1()
      const data = {
        ExamId: ExamId,
        ByStudentId: ByStudentId,
        PaperId: this.paperId,
        StudentId: localStorage.getItem('UserId')
      }
      this.$uwonhttp.post('/Paper/TeacherPaper/GetStudentEvaluationData', data).then((res) => {
        // console.log(res)
        if (res.data.Success) {
          this.lidisList = res.data.Data
        } else {
          this.$message.error(res.data.Msg)
        }
        // if (res.data.Success) {
        //   this.$message.success('提交成功')
        // } else {
        //   this.$message.error(res.data.Msg)
        // }
        // this.dialogVisible = false
      })
    },
    // 评价我的数据处理
    evaluateMy1 (Oid) {
      this.search(Oid)
      // this.GetByStudentEvaluationData()
    },
    evaluateMy2 (val) {

    },
    // 循环查看状态值
    optionSearch (list, nu) {
      const data = []
      list.forEach((res) => {
        if (nu === 1 && res.Result === 0) {
          data.push(res)
        }
        if (nu === 2 && res.Result === 1) {
          data.push(res)
        }
      })
      return data
    },
    search (Oid) {
      this.evaluateMyRemove()
      this.evaluateMyValue1 = Oid
      const arr = this.options2
      arr.forEach((res) => {
        if (res.UserId === this.getUserXinXi(Oid,'StudentId') && res.ExamId === this.getUserXinXi(Oid,'ExamId')) {
          this.evaluateMyValue2 = res.UserId
          this.evaluateMytopic.push(res)
          this.evaluateMyImgAnswer = this.evaluateMytopic[0].UserAnswer.split('|')
          this.GetByStudentEvaluationData()
        }
      })
    },
    getUserXinXi (Oid, sta) {
      let id
      const ar = JSON.parse(JSON.stringify(this.evaluateMy))
      ar.forEach((res) => {
        if (res.Oid === Oid) {
          this.evaluateMyExId = res.ExamId
          this.newEvaluateMyValue1 = res.StudentId
          id = res[sta]
        }
      })
      return id
    },
    // 清空数据
    evaluateMyRemove () {
      this.evaluateMytopic = []
      this.evaluateMyImgAnswer = []
      this.evaluateMyList = []
      this.evaluateMyValue1 = ''
      this.evaluateMyValue2 = ''
      this.evaluateMyExId = ''
    },
    // 评价我的数据
    GetByStudentEvaluationData () {
      const data = {
        ExamId: this.evaluateMyExId,
        ByStudentId: localStorage.getItem('UserId'),
        PaperId: this.paperId,
        StudentId: this.newEvaluateMyValue1
      }
      this.$uwonhttp.post('/Paper/TeacherPaper/GetByStudentEvaluationData', data).then((res) => {
        if (res.data.Success) {
          this.evaluateMyList = res.data.Data
        } else {
          this.$message.error(res.data.Msg)
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.dialogClass {
  /deep/.el-dialog__body {
    padding: 0px 0.104167rem;
    color: #606266;
    font-size: 0.072917rem;
    word-break: break-all;
  }
}
// 评价我的结束
.mutualEvaluate{
  //._top{
  //  display: flex;
  //  justify-content: space-between;
  //  padding: 10px 20px;
  //  font-size: 18px;
  //  background-color: #FFFFFF;
  //  border-radius: 4px;
  //  margin-bottom: 10px;
  //}
  .mutual{
    width: 100%;
    display: flex;
    justify-content: space-between;
    .mutual_left,.mutual_right{
      background-color: #FFFFFF;
    }
    .mutual_left{
      width: 50%;
      margin-right: 5px;
    }
    .mutual_right{
      width: 50%;
      margin-left: 5px;
      ._right_click{
        padding: 20px;
        ._right_tit{
          font-size: 18px;
          margin-bottom: 20px;
        }
        ._titBotm{
          margin-bottom: 6px;
          ._titWeidu{
            display: flex;
            font-size: 18px;
            /deep/ .el-rate__icon{
              font-size: 22px;
            }
          }
          ._tit_text{
            font-size: 18px;
          }
          /deep/.el-input__inner{
            color: #000000;
          }
        }
        ._titInput{
          h6{
            font-size: 18px;
            margin-bottom: 4px;
          }
        }
        ._right_btn{
          margin-top: 20px;
          display: flex;
          justify-content: flex-end;
          .el-button{
            margin: 0 10px;
          }
        }
      }
    }
    // 评价我的开始
    .imgEvaluate{
      width: 100%;
      padding: 20px;
      .imgEvaluate_top{
        display: flex;
        justify-content: space-between;
        ._top_dis{
          display: flex;
          align-items: center;
          h6{
            font-size: 18px;
          }
          /deep/.el-select{
            width: 180px;
          }
        }
      }
      .content{
        position: relative;
        .carousel{
          width: 100%;
          height: 100%;
          /deep/.el-carousel__container{
            height: 100%;
          }
        }
        .tent{
          //width: 30%;
          height: 40%;
          padding: 20px;
          border-radius: 4px;
          position: absolute;
          bottom: 0px;
          left: 0px;
          background-color: rgba(0,0,0,0.6);
          z-index: 99;
          li{
            margin: 5px 0;
            font-size: 18px;
            color: #FFFFFF;
            .li_dis{
              display: flex;
              /deep/ .el-rate__icon{
                font-size: 22px;
              }
            }
          }
        }
        .else_content{
          text-align: center;
          padding-top: 200px;
          font-size: 20px;
        }
      }
    }
  }
}
</style>
