<template>
  <div class="analysis-box clearfix" style="margin:0 auto;">
    <div class="example" v-if="loadWhole">
      <a-spin />
    </div>
    <div class="switch_top">
      <switch-Multidisciplinary :sortShow="sortShow" widthNum="100%" @handleClick="newHandleClick"></switch-Multidisciplinary>
    </div>
    <div>
      <div class="stu-analusis">
        <!-- 智能分析 -->
        <div class="int-analysis">
          <span class="fg"> </span>
          <div v-show="this.defalut === '1'">
            <ul class="analysis-center">
              <li>
                <img src="@/assets/finemicro/红点.png" alt="" />
                <span>我的平均正确率：</span>
                <span class="col">{{ classMakeSubject.MyAccuracy }}</span>
              </li>
              <li>
                <img src="@/assets/finemicro/红点.png" alt="" />
                <span>我的已订正错题数：</span>
                <span class="col">{{ classMakeSubject.BeenRevisedCount }}道</span>
              </li>
              <li>
                <img src="@/assets/finemicro/红点.png" alt="" />
                <span>我的已完成练习数：</span>
                <span class="col"> {{ classMakeSubject.MyPartake }}套</span>
              </li>
              <li>
                <img src="@/assets/finemicro/红点.png" alt="" />
                <span>我的未完成练习数：</span>
                <span class="col">{{ classMakeSubject.NoPartake }}套</span>
              </li>
            </ul>
          </div>
          <div class="badeg_img" v-show="SubjectId === '2' || SubjectId === '22' || SubjectId === '51'" >
            <div class="_img">
              <img v-for=" index of IsFinishPaperTypeCnt" :key="index" src="@/assets/student/badge.png" alt=""/>
            </div>
            <el-button type="primary pc_font_size14 font_size16 ipad_font_size18" v-show="LoginSource == 1" @click="centerDialogVisibleBtn">进度查看</el-button>
          </div>
        </div>
        <div class="honor" v-show="this.defalut === '1'">
          <ul class="honor-report">
            <li @click="toStudyWeekly">
              <img src="@/assets/student/学习周报.png" alt="" />
              <span>学习周报</span>
            </li>
            <li @click="termReport">
              <img src="@/assets/student/单元报告.png" alt="" />
              <span>单元报告</span>
            </li>
            <li @click="Perspractice">
              <img src="@/assets/student/个性练习.png" alt="" />
              <span>个性练习</span>
            </li>
            <!-- <li @click="toWrongStatistics">
              <img src="@/assets/student/wrongSub.png" alt="" />
              <span>错题统计</span>
            </li> -->
            <li @click="Peraccurate">
              <img src="@/assets/student/精准解析.png" alt="" />
              <span>精准解析</span>
            </li>
          </ul>
        </div>
      </div>
      <a-drawer
        :title="chartChapterName"
        placement="right"
        :closable="false"
        :visible="secondChapterReport"
        @close="onCloseSecondChapterReport"
        width="900"
        :headerStyle="{ textAlign: 'center' }"
        :drawerStyle="{ backgroundColor: '#F7F8F8' }"
        :bodyStyle="{ padding: '50px' }"
      >
        <span class="chapter-return cur font_size18 ipad_font_size20" @click="returnChapter">返回</span>
        <div class="term-paper">
          <span class="term-color m-r-info font_size20 ipad_font_size22">完成章节<span class="f-t">{{ chapterData.FinishChapter }}</span></span>
          <span class="font_size20 ipad_font_size22">已做题数</span>
          <span class="f-t m-r-info">{{ chapterData.DoItemCount }}</span>
          <span class="term-color font_size20 ipad_font_size22">错误题数</span>
          <span class="f-t m-r-info">{{ chapterData.ErrorItem }}</span>
          <span class="term-color font_size20 ipad_font_size22">未订正错题</span>
          <span class="f-t m-r-info">{{ chapterData.NotCorrectItem }}</span>
          <span class="cur go-exit font_size18 ipad_font_size20" @click="goWrongQuestionBank">去订正</span>
        </div>
        <p style="font-size: 16px; margin-top: 15px; color: #000">薄弱点分析</p>
        <div>
          <div ref="KnowledgePoints" class="knowledge-points"></div>
        </div>
        <div></div>
      </a-drawer>
      <!-- 学期报告 -->
      <a-drawer
        title="单元报告"
        placement="right"
        :closable="false"
        :visible="VisibleTask"
        @close="onClose"
        width="900"
        :headerStyle="{ textAlign: 'center' }"
        :drawerStyle="{ backgroundColor: '#FAF7F8' }"
        :bodyStyle="{ padding: '50px' }"
      >
        <div class="term-paper">
          <span class="term-color m-r-info font_size20 ipad_font_size22">完成章节<span class="f-t">{{ subjectData.FinishChapter }}</span></span>
          <span class="font_size20 ipad_font_size22">已做题数</span>
          <span class="f-t">{{ subjectData.DoItemCount }}</span>
          <span class="term-color m-r-info"></span>

          <span class="term-color font_size20 ipad_font_size22">错误题数</span>
          <span class="f-t m-r-info">{{ subjectData.ErrorItem }}</span>

          <span class="term-color font_size20 ipad_font_size22">未订正错题</span>
          <span class="f-t m-r-info">{{ subjectData.NotCorrectItem }}</span>
          <span class="cur go-exit font_size18 ipad_font_size20" @click="goWrongQuestionBank">去订正</span>
        </div>
        <div ref="termPaper" class="knowledge-points"></div>
        <div class="situation-ana">
          <p class="chapter-correct c-m-b font_size22 ipad_font_size24" v-show="masterSituation.length !== 0">掌握情况分析</p>
          <ul class="situation-info">
            <li v-for="(item, index) in masterSituation" :key="index">
              <p class="font_size20 ipad_font_size22">
                <span class="chap-col">{{ item.ChapterName }}</span>
                <span @click="toKnowledge(item.ChapterId, item.ChapterName)" class="fg chap-enl cur font_size20 ipad_font_size22">详情</span>
              </p>
              <p class="chap-dis font_size20 ipad_font_size22">
                <span class="chap-col">正确率对比</span>
                <span class="fg" v-show="item.IsWeak === 1" style="color: rgba(255, 0, 0)">薄弱</span>
                <span class="fg" v-show="item.IsWeak !== 1" style="color: rgba(102, 178, 145)">优势</span>
              </p>
              <p class="font_size20 ipad_font_size22">
                我的：
                <span style="color: rgb(217, 0, 27);">{{ item.Me }}%</span> &nbsp;&nbsp;&nbsp; 参考值：<span>{{ item.Class }}%</span>
              </p>
            </li>
          </ul>
        </div>
      </a-drawer>
      <!-- 正确率统计 -->
      <div class="Accuracy" v-show="this.defalut === '1'">
        <div class="accuracy_p">
          <p class="_p_width" ></p>
          <p class="p">本学期正确率趋势</p>
          <el-select class="_p_width" v-model="newValue" @change="selectBtn(newValue)">
            <el-option
              v-for="item in newOptions"
              :key="item.PaperType"
              :label="item.PaperTypeName"
              :value="item.PaperType">
            </el-option>
          </el-select>
        </div>
        <div class="situationAnalysis" ref="newAnalysis"></div>
      </div>
    </div>
    <el-dialog
      title="本学期习题已全部推送"
      :visible.sync="centerDialogVisible"
      width="40%">
      <div class="div_progress" v-for="(item,index) in progressList " :key="index">
        <div class="_progress_wieth">
          <div class="new_div_progress"><p>{{ item.PushPaperName }}</p><p><span>{{ item.DoPaperCnt }}</span>/{{ item.PushPaperCnt }}</p></div>
          <el-progress color="#997EDE" :stroke-width="20" :show-text="false" :percentage="parseInt((item.DoPaperCnt / item.PushPaperCnt)*100)> 100 ? 100 : parseInt((item.DoPaperCnt / item.PushPaperCnt)*100)"></el-progress>
        </div>
        <img v-show="item.DoPaperCnt === item.PushPaperCnt" src="@/assets/student/badge.png" alt=""/>
      </div>
      <div style="text-align: center">
        <el-button class="font_size18 ipad_font_size20" type="warning" round @click="goTaskCenter">前去练习</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import '@/utils/utils.less'
import echarts from 'echarts'
import switchMultidisciplinary from '@/components/switchMultidisciplinary/switchMultidisciplinary'
import { debounce } from 'lodash'
export default {
  components: {
    switchMultidisciplinary
  },
  created () {
    this.userId = localStorage.getItem('UserId')
    this.userPhone = localStorage.getItem('mobilePhone')
    this.SubjectId = localStorage.getItem('SubjectId')
    this.studentId = this.$route.query.id
    this.className = this.$route.query.className
    this.getClassData()
    this.getStudentPaperTypeList()
    this.$store.commit('setHeaderIndex', 4)
  },
  mounted () {
    this.init()
  },
  watch: {
    $route () {
      this.studentId = this.$route.query.id
      if (this.$route.fullPath === '/Student/studentPersonalAnalysis') {
        this.loadWhole = true
        this.SubjectId = localStorage.getItem('SubjectId')
        // 获取班级做卷信息
        this.getClassData()
        this.$store.commit('setHeaderIndex', 4)
      }
    }
  },
  data () {
    return {
      SuryFee: 2.33,
      // 加载
      loadWhole: true,
      color: '1',
      classId: '',
      userPhone: '',
      GradeId: '',
      // 学生id
      studentId: '',
      // 学生的姓名/头像
      studentName: {},
      // 知识点分析
      chapterReport: false,
      secondChapterReport: false,
      // 学期报告
      VisibleTask: false,
      // 知识点
      options: [],
      // 教师id
      userId: '',
      // 班级名称
      className: '',
      // 班级做卷信息
      classMakeSubject: {},
      chapterId: '',
      // 章节名称
      chapterName: [],
      chartChapterName: '',
      defalut: '1',
      SecondChapterId: '',
      chapterData: {},
      chapterFinishList: [],
      // 做卷信息
      subjectData: {},
      // 时间段统计
      termTimeList: [],
      // 掌握情况
      masterSituation: [],
      // 时间统计总数
      termNum: 0,
      // 试卷类型
      PaperType: 0, // 习题类型----0所有练习 1辅导助手 2专课专练(市试卷，区试卷，校试卷) 3校本练习 4高频错题 5纸笔练习
      // 试卷是否全部推送
      IsPushPaperFinish: 0,
      IsFinishPaperTypeCnt: 0,
      // 习题类型----0所有练习 1辅导助手 2专课专练(市试卷，区试卷，校试卷) 3校本练习 4高频错题 5纸笔练习
      newOptions: [],
      newValue: '0',
      // 弹窗
      centerDialogVisible: false,
      // 学生习题进度
      sortShow: false,
      progressList: [],
      SubjectId: '',
      LoginSource: localStorage.getItem('LoginSource')
    }
  },
  methods: {
    // 多学科选择学科点击事件
    newHandleClick: debounce(function (val) {
      this.SubjectId = val
      this.loadWhole = true
      this.getClassData()
    }, 800),
    // 选择习题类型
    selectBtn (val) {
      this.PaperType = val
      this.loadWhole = true
      this.getClassData()
    },
    // 打开弹窗
    centerDialogVisibleBtn () {
      this.getStudentPaperProgress()
    },
    // 全区练习
    wholeAreaPractice () {
      this.defalut = '1'
    },
    // 校本练习
    schoolPractice () {
      this.defalut = '2'
    },
    // 班级练习
    classPractice () {
      this.defalut = '3'
    },
    // 荣誉勋章
    handleHonorMedal () {
      this.$message.success('暂未开放')
    },
    init () {
      const newAnalysis = echarts.init(this.$refs.newAnalysis)
      newAnalysis.setOption({
        color: ['#5AD8A6', '#5B8FF9'],
        title: {
          subtext: '(百分比：%)',
          left: '2%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 8
          }
        ],
        tooltip: {
          formatter: function (params) {
            return params.name
          }
        },
        legend: {
          data: ['我的', '参考值']
        },
        xAxis: [
          {
            type: 'category',
            axisLabel: {}
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '我的',
            type: 'bar',
            data: [0]
          },
          {
            name: '参考值',
            type: 'bar',
            data: [0]
          }
        ]
      })
      // 设置图表随窗口大小变化
      window.addEventListener('resize', function () {
        newAnalysis.resize()
      })
    },
    get () {
      const knowledge = echarts.init(this.$refs.KnowledgePoints)
      knowledge.setOption({
        color: ['#ECBF8A', '#FF8B7F', '#71B6A1', '#DED9F6', '#DED9F6'],
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          x: 'center', // 可设定图例在左、右、居中
          y: 'bottom', // 可设定图例在上、下、居中
          data: ['我的', '参考值']
        },

        radar: [
          {
            indicator: [
              { text: '单元一' },
              { text: '单元二' },
              { text: '单元三' },
              { text: '单元四' },
              { text: '单元五' }
            ],
            center: ['50%', '50%'],
            radius: 150,
            startAngle: 90,
            splitNumber: 4,
            shape: 'circle',
            name: {
              formatter: '【{value}】',
              textStyle: {
                color: '#000'
              }
            },
            splitArea: {
              areaStyle: {
                color: ['#e8ebd6', '#f6f5f8', '#e8ebd6', '#f6f5f8', '#e8ebd6'],
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 10
              }
            },
            axisLine: {
              lineStyle: {}
            },
            splitLine: {
              lineStyle: {}
            }
          }
        ],
        series: [
          {
            name: '雷达图',
            type: 'radar',
            emphasis: {
              lineStyle: {
                width: 5
              }
            },
            data: [
              {
                value: [100, 8, 0.4, -80, 2000],
                name: '我的',
                type: 'bar',
                symbolSize: 5
              },
              {
                value: [60, 5, 0.3, -100, 1500],
                name: '参考值',
                type: 'bar',
                areaStyle: {}
              }
            ]
          }
        ]
      })
    },
    // 获取班级做卷信息
    getClassData () {
      const Platform = localStorage.getItem('PL_newP')
      this.$uwonhttp.post('/Report/StudentReport/GetStudentAnalytics', {
        Grade: this.GradeId,
        PaperType: this.PaperType,
        Platform: Platform,
        UserId: this.userId,
        SubjectId: this.SubjectId
      })
        .then((res) => {
          this.classMakeSubject = res.data.Data
          this.studentName = res.data.Data.User
          this.IsPushPaperFinish = res.data.Data.IsPushPaperFinish
          this.IsFinishPaperTypeCnt = res.data.Data.IsFinishPaperTypeCnt
          let classData = []
          if (this.SubjectId === '-1') {
            classData = res.data.Data.SubjectList
          } else {
            classData = res.data.Data.Line
          }
          const Me = classData.map((item) => {
            return item.Me
          })
          let chapter = []
          if (this.SubjectId === '-1') {
            chapter = classData.map((item) => {
              return item.SubjectName
            })
          } else {
            chapter = classData.map((item) => {
              return item.PaperTitle
            })
          }
          // const School = classData.map((item) => {
          //   return item.School
          // })
          const Class = classData.map((item) => {
            return item.Class
          })
          const newAnalysis = echarts.init(this.$refs.newAnalysis)
          var option = {
            xAxis: [
              {
                data: chapter
              }
            ],
            series: [
              {
                data: Me,
                type: 'bar'
              },
              {
                data: Class,
                type: 'bar'
              }
            ]
          }
          newAnalysis.setOption(option)
          this.loadWhole = false
        })
    },
    Perspractice () {
      if (this.SubjectId !== '2' && this.SubjectId !== '22' && this.SubjectId !== '51') {
        this.$message.warning('此功能暂未开放')
      } else {
        this.$router.push({ path: '/Student/PerPaper/index' })
      }
    },
    Peraccurate () {
      if (this.SubjectId !== '2' && this.SubjectId !== '22' && this.SubjectId !== '51') {
        this.$message.warning('此功能暂未开放')
      } else {
        this.$router.push({ path: '/Student/Practice/Accurate' })
      }
    },
    // 前去练习
    goTaskCenter () {
      this.centerDialogVisible = false
      this.$router.push({ path: '/Student/TaskCenter' })
    },
    getTermChart () {
      const termReport = echarts.init(this.$refs.termPaper)
      termReport.setOption({
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          x: 'center', // 可设定图例在左、右、居中
          y: 'bottom', // 可设定图例在上、下、居中
          data: ['我的', '参考值']
        },
        color: ['#ECBF8A', '#FF8B7F', '#71B6A1', '#DED9F6', '#DED9F6'],
        radar: [
          {
            indicator: [
              { text: '单元一' },
              { text: '单元二' },
              { text: '单元三' },
              { text: '单元四' },
              { text: '单元五' }
            ],
            center: ['50%', '50%'],
            // radius: 150,
            startAngle: 90,
            splitNumber: 4,
            shape: 'circle',
            name: {
              formatter: '【{value}】',
              textStyle: {
                color: '#000'
              }
            },
            splitArea: {
              areaStyle: {
                color: ['#e8ebd6', '#f6f5f8', '#e8ebd6', '#f6f5f8', '#e8ebd6'],
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 10
              }
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.5)'
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.5)'
              }
            }
          }
        ],
        series: [
          {
            name: '雷达图',
            type: 'radar',
            emphasis: {
              lineStyle: {
                width: 4
              }
            },
            data: [
              {
                value: [100, 8, 0.4, -80, 2000],
                name: '我的',
                symbol: 'rect',
                symbolSize: 5,
                lineStyle: {
                  type: 'dashed',
                  color: '#FF8B7F'
                }
              },
              {
                value: [60, 5, 0.4, -80, 2000],
                name: '参考值',
                areaStyle: {
                  color: '#71B6A1'
                }
              }
            ]
          }
        ]
      })
    },
    // 单元报告
    async termReport () {
      // 获取全部单元
      const { data: chapter } = await this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', { userid: localStorage.getItem('UserId'), IsWeek: 1 })
      const UnitIds = chapter.Data.map((item) => { return item.ChapterId })
      if (this.SubjectId !== '2' && this.SubjectId !== '22' && this.SubjectId !== '51') {
        this.$message.warning('此功能暂未开放')
      } else {
        this.VisibleTask = true
        // .post('/Report/StudentReport/GetStudentTerm', { Grade: this.GradeId, UserId: this.userId })
        this.$uwonhttp.post('/Report/StudentReport/GetStudentTerm', { Grade: this.GradeId, UserId: this.userId, UnitIds }).then((res) => {
            this.subjectData = res.data.Data.Statistics
            this.termTimeList = res.data.Data.Time
            this.masterSituation = res.data.Data.Weakness
            const name = res.data.Data.Radar.map((item) => {
              const sub = {}
              sub.text = item.ChapterName
              return sub
            })
            const Me = res.data.Data.Radar.map((value) => {
              return value.Me
            })
            const area = res.data.Data.Radar.map((value) => {
              return value.Area
            })
            const School = res.data.Data.Radar.map((value) => {
              return value.School
            })
            this.termTimeList.forEach((item, index) => {
              this.termNum = this.termNum + item.Count
            })
            const termReport = echarts.init(this.$refs.termPaper)
            termReport.setOption({
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  // type: 'cross',
                  crossStyle: {
                    color: '#999'
                  }
                }
              },
              radar: [
                {
                  indicator: name
                }
              ],
              series: [
                {
                  data: [
                    {
                      name: '我的',
                      value: Me
                    },
                    {
                      name: '参考值',
                      value: School
                    }
                  ]
                }
              ]
            })
          })
        this.$nextTick(() => {
          this.getTermChart()
        })
      }
    },
    // 学生各个小节分析
    getStudentList (chapterId) {},
    // 小节数据
    SectionList () {
      this.$uwonhttp.post('/Report/TeacherReport/GetAnalyticsStudentTermFromTeacherWeb', {
        UserId: this.userId,
        StudentId: this.studentId
      }).then((res) => {})
    },
    // 去订正
    goWrongQuestionBank () {
      this.VisibleTask = false
      this.secondChapterReport = false
      this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
    },
    // 关闭学期报告
    onClose () {
      this.VisibleTask = false
    },
    returnChapter () {
      this.secondChapterReport = false
      setTimeout(() => {
        this.VisibleTask = true
      }, 500)
    },
    // 打开知识点分析
    studyLevel () {
      this.chapterReport = true
      this.$uwonhttp
        .post('/Report/StudentReport/GetTwoLevalByUserId', { UserId: this.userId, Grade: this.GradeId })
        .then((res) => {
          this.chapterName = res.data.Data
        })
    },
    // 获取学生联系类别
    getStudentPaperTypeList () {
      this.$uwonhttp
        .post('/Report/StudentReport/GetStudentPaperTypeList', { UserId: this.userId })
        .then((res) => {
          const arrData = res.data.Data
          this.newOptions = arrData
        })
    },
    // 获取联系册进度
    getStudentPaperProgress () {
      this.$uwonhttp
        .post('/Paper/Paper/GetStudentPaperProgress', { UserId: this.userId })
        .then((res) => {
          const data = res.data.Data
          this.progressList = data.StudentProList
          this.centerDialogVisible = true
        })
    },
    // 跳转学习周报
    toStudyWeekly () {
      if (this.SubjectId !== '2' && this.SubjectId !== '22' && this.SubjectId !== '51') {
        this.$message.warning('此功能暂未开放')
      } else {
        this.$router.push({ path: '/Student/StudentWeekly/newStudyWeekly' })
      }
      // this.$router.push({ path: '/Student/StudentWeekly/StudyWeekly' })
      // this.$router.push({ path: '/Student/StudentWeekly/newStudyWeekly' })
    },
    // 错题统计
    toWrongStatistics () {
      this.$router.push({ path: '/Student/Fine_micro/WrongQuestionBank' })
    },
    getChapterData (secondId) {
      this.$uwonhttp
        .post('/Report/StudentReport/GetStudentChapter', {
          Grade: this.GradeId,
          UserId: this.userId,
          FirstChapter: secondId
        })
        .then((res) => {
          this.knowledgeData = res.data.Data.Weakness
          const name = this.knowledgeData.map((item) => {
            const sub = {}
            sub.text = item.ChapterName
            return sub
          })
          const Me = this.knowledgeData.map((item) => {
            return item.Me
          })
          const Class = this.knowledgeData.map((item) => {
            return item.Class
          })
          const knowledge = echarts.init(this.$refs.KnowledgePoints)
          knowledge.setOption({
            radar: [
              {
                indicator: name
              }
            ],
            series: [
              {
                type: 'radar',
                tooltip: {
                  trigger: 'item',
                  formatter: function (params) {
                    return ''
                  }
                },
                data: [
                  {
                    value: Me,
                    name: '我的'
                  },
                  {
                    value: Class,
                    name: '参考值'
                  }
                ]
              }
            ]
          })
        })
      this.$nextTick(() => {
        this.get()
      })
    },
    // 章节报告选择
    chapterSelect (value, name) {
      this.chapterReport = false
      this.chartChapterName = name
      this.SecondChapterId = value
      this.chapterList(value)
      const arr = this.chapterName.filter((item) => {
        return item.ChapterId === value
      })
      const secChapterList = arr[0].Second.map((item) => {
        return item.ChapterId
      })
      const secondId = secChapterList.join(',')
    },
    // 章节报告 - 错题 - 做题数 - 订正数 ==
    chapterList (value) {
      this.$uwonhttp
        .post('/Report/StudentReport/GetStudentChapter', {
          FirstChapter: value,
          Grade: this.GradeId,
          UserId: this.userId
        })
        .then((res) => {
          this.chapterData = res.data.Data.Statistics
          this.chapterFinishList = res.data.Data.NotFinish
        })
    },
    // 新的打开章节报告
    toKnowledge (chapterId) {
      this.secondChapterReport = true
      this.getChapterData(chapterId)
      this.chapterList(chapterId)
    },
    onCloseChapterReport () {
      this.chapterReport = false
    },
    onCloseSecondChapterReport () {
      this.secondChapterReport = false
    },
    change () {
      this.color = '1'
    },
    // 学期报告
    change2 () {
      this.color = '3'
    }
  }
}
</script>

<style lang="less" scoped>
.analysis-box {
  margin-right: 190px;
}
.switch_top{
  margin-bottom: 18px;
  padding: 18px 25px;
  background-color: #FFFFFF;
  border-radius: 10px;
}
.cur {
  cursor: pointer;
}
.color {
  color: #68bb97;
}
.col {
  font-size: 18px;
  color: #68bb97;
}
.example {
  text-align: center;
}
//奖章图
.badeg_img{
  display: flex;
  justify-content: space-between;
  align-items: center;
  ._img{
    display: flex;
    align-items: center;
    img{
      width: 32px;
      height: 49px;
      margin-right: 2px;
    }
  }
}
// 单元报告 / 掌握情况分析
.situation-ana {
  margin-top: 30px;
  .c-m-b {
    margin-bottom: 30px;
  }
  // 掌握内容
  .situation-info {
    display: flex;
    justify-content: start;
    flex-wrap: wrap;
    li {
      margin: 10px 5px;
      width: 32%;
      padding: 11px 15px;
      background-color: rgba(226, 248, 255);
      border-radius: 5px;
    }
    .chap-col {
      margin-right: 10px;
      color: rgba(85, 198, 210);
    }
    .chap-dis {
      margin: 15px 0;
    }
    .chap-enl {
      font-size: 15px;
    }
  }
}
// 章节报告
.knowledge-points {
  width: 100%;
  height: 528px;
}
// 学期报告
.term-paper {
  .m-r-info {
    margin-right: 35px;
  }
  span {
  }
  .f-t {
    font-size: 32px;
    color: #000;
  }
  .term-color {
    color: #737976;
  }
  .go-exit {
    display: inline-block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    background-color: #68bb97;
    border-radius: 30px;
  }
}
.chapter-correct {
  font-size: 16px;
  margin-top: 15px;
  color: rgb(0, 0, 0);
}
.chapter-report-btn {
  display: inline-block;
  width: 190px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background-color: #68bb97;
  border-radius: 30px;
}
// 章节返回
.chapter-return {
  position: absolute;
  top: 10px;
  display: inline-block;
  width: 75px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background-color: #65b391;
  border-radius: 5px;
}
.chapter-report {
  height: 53px;
  padding-left: 20px;
  padding-top: 15px;
  padding-bottom: 50px;
  font-size: 16px;
  border-bottom: 1px solid #ccc;
  border-radius: 5px;
}
.chapter-report:hover {
  background-color: #68bb97;
}
.btn-col {
  margin-right: 60px;
  color: #c3c3c3;
  background-color: #f7f8f8;
  border: 1px solid #c6c7c7;
}
.analysis-center {
  line-height: 40px;
  li {
    img {
      margin-bottom: 5px;
      margin-right: 6px;
    }
    span {
      background-color: #fff;
      border-radius: 5px;
      font-size: 19px;
    }
  }
}
// 选择框
/deep/.ant-select-selection--single {
  border: none;
}
.stu-analusis {
  display: flex;
}
.span {
  margin-right: 25px;
}
.center {
  margin-bottom: 30px;
  text-align: center;
  font-size: 16px;
  color: #000;
}
// 智能分析
.int-analysis {
  margin-right: 30px;
  padding: 30px 30px;
  border-radius: 5px;
  background-color: #fff;
  .practice-switch {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    border-radius: 20px;
    color: #b5b5b5;
    border: 1px solid #b5b5b5;
    cursor: pointer;
  }
  .def-practice {
    color: #fff;
    background-color: #68bb97;
    border: none;
  }
  span {
    display: inline-block;
  }
  span:nth-child(1) {
    margin-right: 20px;
  }
  span:nth-child(2) {
    margin-right: 30px;
  }
  .complete-dis {
    margin-left: 43px;
    margin-right: 100px;
  }
}
// 荣誉勋章
.honor {
  flex: 1;
  //   div:nth-child(1) {
  //   width: 100%;
  //   height: 167.5px;
  //   line-height: 167.5px;
  //   // padding: 56px 200px;
  //   font-size: 20px;
  //   text-align: center;
  //   background-color: #fff;
  //   border-radius: 5px;
  //   span {
  //     margin-left: 10px;
  //     }
  // }
  .honor-report {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    li {
      display: flex;
      width: 24%;
      height: 100%;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      border-radius: 5px;
      cursor: pointer;
    }
    span {
      margin: 13px 21px 0px 0px;
      background-color: #fff;
      border-radius: 5px;
      font-size: 20px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #474725;
    }
    span:nth-child(2) {
      margin-right: 0;
    }
  }

  img {
    width: 60px;
  }
  .search {
    .ant-select {
      width: 500px;
    }
  }
}
// 练习信息
.practice-infor {
  margin-top: 31px;
}
// 学情分析
.Accuracy {
  margin-top: 20px;
  padding-top: 20px;
  border-radius: 5px;
  background-color: #fff;
  .accuracy_p{
    display: flex;
    justify-content: space-between;
    align-items: center;
    ._p_width{
      width: 8%;
      margin-right: 20px;
      /deep/.el-input{
        font-size: 16px;
      }
    }
    .p {
      text-align: center;
      font-size: 22px;
      color: #4d5753;
    }
  }

}
// 推送是否完成弹窗进度条
.div_progress{
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  ._progress_wieth{
    width: 90%;
    .new_div_progress{
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      p:nth-child(1){
        font-size: 24px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #474747;
      }
      p:nth-child(2){
        font-size: 22px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #B2B2B2;
        line-height: 30px;
        span{
          color: #997EDE;
        }
      }
    }
  }
  img{
    width: 42px;
    height: 63px;
    margin-left: 10px;
  }
}
.situationAnalysis {
  width: 100%;
  height: 350px;
  margin-top: 20px;
}
// 学生学期分析图表
.semester-analysis {
  width: 100%;
  height: 500px;
}
// 学生章节分析图表
.chapter-analysis {
  width: 100%;
  height: 500px;
}
</style>
