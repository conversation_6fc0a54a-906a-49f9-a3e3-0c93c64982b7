<template>
  <div style="margin:0 auto;">
    <div class="task_header" >
      <span>作业报告</span>
    </div>
    <div class="task_detail">
      <span class="title_he">正确题数</span>
      <div class="detail_header">
        <a-progress :success-percent="(this.getList.RightCount / (this.getList.RightCount + this.getList.ErrorCount)) * 100" :showInfo="showInfo" :strokeWidth="strokeWidth" />
        <span>{{this.getList.RightCount}}/{{this.getList.AllCount}}</span>
      </div>
      <div class="line"></div>
      <h2>{{PaperTitle}}</h2>
      <div class="detail_footer">
        <div v-for="(item,index) in getList.Items" :key="index" class="footer_item">
          <img v-if="item.IsKeyTrue === 1" src="@/assets/student/对号.png" alt="">
          <img v-if="item.IsKeyTrue === 2" src="@/assets/student/叉号.png" alt="">
          <br />
          <span>{{ index + 1 }}</span>
        </div>
      </div>
    </div>
    <div class="btn">
      <span @click="toReviewWrong">查看解析</span>
      <span @click="KeepPracticing">继续练习</span>
    </div>
  </div>
</template>

<script>
import eventBus from '@/components/eventBus/eventBus'
export default {
  created() {
    this.paperId = this.$route.query.paperId
    this.PaperTitle = this.$route.query.PaperTitle
  },
  mounted() {
    this.$nextTick(() => {
      this.getList = JSON.parse(localStorage.getItem('studentList'))
    })
  },
  data() {
    return {
      getList: [],
      showInfo: false,
      // 进度条宽度
      strokeWidth: 22,
      paperId: '',
      PaperTitle: ''
    }
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.paperId = this.$route.query.paperId
      this.PaperTitle = this.$route.query.PaperTitle
    }
  },
  methods: {
    toReviewWrong() {
      this.$router.push({
        path: '/Student/Fine_micro/WrongDetails',
        query: {
          PaperTitle: this.PaperTitle
        }
      })
    },
    KeepPracticing() {
      this.$router.push({
        path: '/Student/Fine_micro/WrongQuestionBank'
      })
      eventBus.$emit('getWrongColor', this.getWrongColor)
    }
  }
}
</script>
<style lang="less" scoped>
.task_header {
  width: 100%;
  height: 50px;
  background: #ffff;
  padding: 10px;

  span {
    font-size: 18px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4d5753;
  }
}
.task_detail {
  width: 100%;
  height: 500px;
  background: #ffff;
  margin-top: 20px;
  .line {
    width: 98%;
    height: 0.005208rem;
    margin: 10px 10px;
    position: relative;
    background: #f1f1f1;
    top: 10%;
  }
  .title_he {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4d5753;
    margin: 20px 10px;
    display: block;
    position: relative;
    top: 2%;
  }
  .detail_header {
    margin-top: 20px;
    position: relative;
    margin: 10px;
    span {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4d5753;
      display: block;
      position: absolute;
      right: 40%;
      top: -1px;
    }
  }
  h2 {
    font-size: 17px;
    padding: 10px;
    position: relative;
    top: 13%;
  }
  .detail_footer {
    margin-top: 80px;
    display: flex;
    padding: 11px 12px;
    .footer_item {
      margin-right: 20px;
      text-align: center;
      span {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #4d5753;
      }
    }
  }
}
.btn {
  width: 95%;
  margin-top: 20px;
  display: flex;
  position: relative;
  span:nth-child(1) {
    width: 100px;
    background: #68bb97;
    margin-right: 14px;
    display: block;
    text-align: center;
    font-size: 15px;
    line-height: 29px;
    border-radius: 5px;
    color: #fff;
    cursor: pointer;
    position: absolute;
    right: 7%;
  }
  span:nth-child(2) {
    width: 100px;
    text-align: center;
    display: block;
    background: #537566;
    font-size: 15px;
    line-height: 29px;
    color: #fff;
    border-radius: 5px;
    cursor: pointer;
    position: absolute;
    right: 0;
  }
}
/deep/.ant-progress-inner {
  position: relative;
  display: inline-block;
  width: 55%;
  overflow: hidden;
  vertical-align: middle;
  background-color: #f5f5f5;
  border-radius: 0.520833rem;
}
/deep/.ant-progress-inner {
  background-color: #ec808d;
}
</style>
