<template>
  <div>
    <!-- 树菜单  -->
    <div class="fl task-catalog">
      <p>章节目录</p>
      <a-tree v-model="checkedKeys" checkable :expanded-keys="expandedKeys" :auto-expand-parent="autoExpandParent" :selected-keys="selectedKeys" :tree-data="treeData" @expand="onExpand" @select="onSelect" />
    </div>
    <!-- 错题详情 -->
    <div class="fl wrong-info">
      <span class="fl">待订正错题</span>
      <div class="wrong-line"></div>
      <ul class="clearfix">
        <li class="fl">
          <div>
            <p>二 . 乘与除</p>
            <p>两位数与两位数相乘</p>
            <p>共收纳<span>15</span>道错题<span>班</span></p>
            <p>2020-03-22 14:25</p>
          </div>
          <p class="review">开始订正</p>
        </li>
        <li class="fl">
          <div>
            <p>二 . 乘与除</p>
            <p>两位数与两位数相乘</p>
            <p>共收纳<span>15</span>道错题<span>班</span></p>
            <p>2020-03-22 14:25</p>
          </div>
          <p class="review">开始订正</p>
        </li>
        <li class="fl">
          <div>
            <p>二 . 乘与除</p>
            <p>两位数与两位数相乘</p>
            <p>共收纳<span>15</span>道错题<span>班</span></p>
            <p>2020-03-22 14:25</p>
          </div>
          <p class="review">开始订正</p>
        </li>
        <li class="fl">
          <div>
            <p>二 . 乘与除</p>
            <p>两位数与两位数相乘</p>
            <p>共收纳<span>15</span>道错题<span>班</span></p>
            <p>2020-03-22 14:25</p>
          </div>
          <p class="review">开始订正</p>
        </li>
        <li class="fl">
          <div>
            <p>二 . 乘与除</p>
            <p>两位数与两位数相乘</p>
            <p>共收纳<span>15</span>道错题<span>班</span></p>
            <p>2020-03-22 14:25</p>
          </div>
          <p class="review">开始订正</p>
        </li>
      </ul>
      <!-- 分页 -->
      <a-pagination show-quick-jumper :default-current="2" :total="500" @change="onChange" />
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
const treeData = [
  {
    title: '一 10以内的数',
    key: '一 10以内的数',
    children: [
      {
        title: '说一说',
        key: '说一说'
      },
      {
        title: '分一分',
        key: '分一分'
      },
      {
        title: '算一算',
        key: '算一算'
      }
    ]
  },

  {
    title: '二 10以内数的加减法',
    key: '二 10以内数的加减法'
  },
  {
    title: '三 20以内的加减法',
    key: '三 20以内的加减法',
    children: [{ title: '123', key: '123' }]
  }
]
export default {
  data() {
    return {
      // 默认展开指定的数菜单
      expandedKeys: ['一 10以内的数'],
      // 展开父节点
      autoExpandParent: true,
      // 默认选中的子节点
      checkedKeys: ['说一说'],
      // 设置选中的数节点
      selectedKeys: [],
      treeData
    }
  },
  watch: {
    checkedKeys(val) {}
  },
  methods: {
    // 展开 收起时 触发
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    // 点击复选框触发
    onCheck(checkedKeys) {
      this.checkedKeys = checkedKeys
    },
    // 点击树节点触发
    onSelect(selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    // 分页
    onChange(pageNumber) {
      console.log(pageNumber)
    }
  }
}
</script>

<style lang="less" scoped>
@color: #15335135;
// 树菜单
.task-catalog {
  width: 220px;
  margin-right: 25px;
  p {
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 5px;
    background-color: #ccc;
  }
}
// 错题描述详情
.wrong-info {
  width: 80%;
  padding: 10px;
  border: 2px solid #ccc;
  border-radius: 5px;
  span {
    font-size: 20px;
  }
  .wrong-line {
    height: 2px;
    margin-top: 38px;
    background-color: #ccc;
  }
  li {
    width: 235px;
    height: 186px;
    margin: 18px 0px 20px 80px;
    border: 1px solid #000;
    border-radius: 5px;
    background-color: @color;
    // 错题描述
    div {
      padding: 15px 0 0 38px;
      p:nth-child(1) {
        font-size: 13px;
        margin-bottom: 13px;
        color: grey;
      }
      p:nth-child(2) {
        margin-bottom: 5px;
        color: #000;
      }
      p:nth-child(3) {
        margin-bottom: 5px;
        color: #000;
        span:nth-child(1) {
          font-size: 19px;
          color: red;
        }
        span:nth-child(2) {
          margin-left: 20px;
          width: 15px;
          padding: 1px 2px;
          font-size: 13px;
          border: 1px solid #000;
          background-color: #def;
          border-radius: 5px;
        }
      }
    }
    // 复习按钮
    .review {
      width: 155px;
      height: 34px;
      line-height: 34px;
      margin: 0 auto;
      margin-top: 10px;
      text-align: center;
      color: #000;
      border-radius: 20px;
      background-color: #ddd;
      cursor: pointer;
    }
  }
  li:hover {
    box-shadow: 8px 8px 7px grey;
  }
}
// 分页
.ant-pagination {
  text-align: center;
}
</style>
