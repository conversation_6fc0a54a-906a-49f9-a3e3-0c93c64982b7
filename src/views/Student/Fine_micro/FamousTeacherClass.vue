<template>
  <div class="whole-mr clearfix" style="margin:0 auto;">
    <p class="teacher-menu">名师讲堂</p>
    <ul class="famous-class clearfix">

      <a-row style="font-size: 14px;" :gutter="{ xl: 16, xxl: 16 }" type="flex" justify="start" align="middle" class="ant-col-xl-24 ant-col-xxl-24">
        <a-col :xl="8" :xxl="6" v-for="item in teacherData" :key="item.Id">
          <div class="li" @click="toFamousTeacherPage(item.CreatorId)">
            <!-- 教师信息 -->
            <div class="clearfix">
              <img style="width:64px;height:64px;" class="fl" :src="item.Photo" alt="">
              <div class="fl info">
                <img v-if="item.collectionDeleted" class="fg" src="@/assets/student/收藏.png" alt="">
                <p>{{ item.UserName }}</p>
                <p><span>{{ item.SchoolName }}</span>&nbsp;&nbsp;{{ item.GradeName }}年级</p>
                <p class="micro-class">
                  <span style="color:#B5B5B5;">微课堂: <span style="color:#000;">{{ item.ProjectNumber }}</span></span> &nbsp;&nbsp;
                  <span style="color:#B5B5B5;" class="fg">粉丝: <span style="color:#000;">{{ item.FollowCount }}</span></span>
                </p>
                <p style="color:#B5B5B5;">课堂总播放次数: &nbsp;{{ item.ClickNum }}</p>
              </div>
            </div>
          </div>

        </a-col>
      </a-row>
    </ul>
    <ul class="famous-class clearfix">

      <!-- <li class="fl" @click="toFamousTeacherPage(item.CreatorId)" v-for="item in teacherData" :key="item.Id">
        <div class="clearfix">
          <img style="width:64px;height:64px;" class="fl" :src="item.Photo" alt="">
          <div class="fl">
            <img v-if="item.collectionDeleted" class="fg" src="@/assets/student/收藏.png" alt="">
            <p>{{ item.UserName }}</p>
            <p><span>{{ item.SchoolName }}</span>&nbsp;&nbsp;{{ item.GradeName }}年级</p>
            <p class="micro-class">
              <span style="color:#B5B5B5;">微课堂: <span style="color:#000;">{{ item.ProjectNumber }}</span></span> &nbsp;&nbsp;
              <span style="color:#B5B5B5;" class="fg">粉丝: <span style="color:#000;">{{ item.FollowCount }}</span></span>
            </p>
            <p style="color:#B5B5B5;">课堂总播放次数: &nbsp;{{ item.ClickNum }}</p>
          </div>
        </div>
      </li> -->
      <!-- <li class="fl">
        <div class="clearfix">
          <img class="fl" src="@/assets/finemicro/名师课堂.png" alt="">
          <div class="fl">
            <img class="fg" src="@/assets/student/收藏.png" alt="">
            <p>王丽娜</p>
            <p><span>康宁科技实现学校</span>&nbsp;&nbsp;一年级</p>
            <p>
              <span style="color:#B5B5B5;">微课堂: <span style="color:#000;">11</span></span> &nbsp;&nbsp;
              <span style="color:#B5B5B5;" class="fg">粉丝: <span style="color:#000;">365</span></span>
            </p>
            <p style="color:#B5B5B5;">课堂总播放次数: &nbsp;2985</p>
          </div>
        </div>
      </li> -->
    </ul>
    <!-- 分页 -->
    <!-- <a-pagination
      class="paging"
      show-quick-jumper
      :default-current="1"
      :total="25"
      @change="onChange"
      :defaultPageSize="PageRows"
    /> -->
  </div>
</template>

<script>
import '@/utils/utils.less'
export default {
  created() {
    // this.id = JSON.parse(localStorage.getItem('userId')).Id
    this.id = localStorage.getItem('UserId')
    // 获取教师数据
    this.getFamousTeacherList(this.id)
  },
  watch: {
    $route() {
      this.id = localStorage.getItem('UserId')
      if (this.$route.fullPath === '/Student/Fine_micro/FamousTeacherClass') {
        this.getFamousTeacherList(this.id)
      }
    }
  },
  data() {
    return {
      // 学生id
      id: '',
      // 教师数据
      teacherData: [],
      PageRows: 12
    }
  },
  methods: {
    // 分页
    onChange() {},
    // 跳转名师页面
    toFamousTeacherPage(teacherId) {
      this.$router.push({
        path: '/Student/Fine_micro/FamousTeacherPage',
        query: {
          teacherId: teacherId,
          userId: this.id
        }
      })
    },
    // 获取教师数据
    getFamousTeacherList(id) {
      this.$http.post('/Paper/Exam_MicroLesson/GetVideoTeacherList', { userId: id, pageNo: 1 }).then(res => {
        this.teacherData = res.Data.Items
      })
    }
  }
}
</script>

<style lang="less" scoped>
.teacher-menu {
  margin-bottom: 32px;
  height: 38px;
  line-height: 38px;
  border-radius: 5px;
  padding-left: 15px;
  font-size: 16px;
  background-color: #fff;
}
// 微课堂
.micro-class {
  margin-bottom: 9px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f6f6f6;
}
.info {
  width: 68%;
}
// 名师信息
.famous-class {
  .li {
    // width: 300px;
    // height: 153px;
    margin-top: 15px;
    // margin: 0 0px 30px 37px;
    border-radius: 5px;
    background-color: #fff;
    cursor: pointer;
    div:nth-child(1) {
      padding: 25px 20px 20px 20px;
      p:nth-child(1) {
        font-weight: 700;
      }
    }
    div:nth-child(3) {
      padding: 15px 20px 0 20px;
      p {
        margin-bottom: 5px;
      }
      p:nth-child(1) {
        font-weight: 700;
      }
      p:nth-child(2) {
        font-size: 12px;
        color: rgb(166, 153, 179);
      }
    }
    img {
      margin-right: 20px;
      border-radius: 35px;
    }
    .famous-line {
      width: 100%;
      height: 1px;
      margin-top: 12px;
      background-color: #000;
    }
  }
  li:nth-child(4n) {
    margin-right: 0;
  }
  li:hover {
    box-shadow: 5px 5px 7px grey;
  }
}
</style>
