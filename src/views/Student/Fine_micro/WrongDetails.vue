<template>
  <div >
    <div class="detail_box">
      <div class="detail_left">
        <div class="left_tabs">
          <span @click="GoBack" style="cursor: pointer; float: left"><i class="el-icon-arrow-left"></i>返回</span>
          <span>查看解析 - {{PaperTitle}}</span>
        </div>
        <br />
        <!-- <p class="title_name">{{PaperTitle}}</p> -->
        <div class="left_content">
          <div class="swiper_list">
            <div class="tea-analusis">
              <div class="line_border"></div>
              <swiper class="swiper" ref="mySwiper" :options="swiperOption">
                <!-- 'current-answer': i.CurrentAnswer, -->
                <swiper-slide style="text-align: center" v-for="(i, t) in getList.Items" :key="t">
                  <a
                    class="title-btn-whole"
                    :class="{
                      'def-color': defalutCol === i.ItemId,
                      'wrong-bgc': i.IsKeyTrue === 2,
                    }"
                    @click="switchTitle(t, i.ItemId, i.TypeId)"
                  >
                    {{ t + 1 }}
                  </a>
                </swiper-slide>
              </swiper>
              <div class="left-arrow">
                <div class="arrow-img" @click="prevPhoto">
                  <i></i>
                </div>
              </div>
              <div class="right-arrow">
                <div class="arrow-img" @click="nextPhoto">
                  <i></i>
                </div>
              </div>
            </div>
          </div>
          <div class="items" v-for="(item,index) in list_show" :key="index" :id="item.ItemId">
            <span>{{ newSubject }}.</span>
            <span v-if="item.ItemTypeId != 52 && item.ItemTypeId != 70" style="font-size: 18px" v-html="item.ItemTitle" v-katex></span>
            <span v-if="item.ItemTypeId == 52 || item.ItemTypeId == 70" style="font-size: 18px" v-html="bankedClozeTitle(item)" v-katex></span>
            <div>
              <a-radio-group v-model="value" v-if="item.ItemTypeId === 2 || item.ItemTypeId === 11">
                <a-radio :style="radioStyle" :value="item.ItemId + '|' + k.Opt" v-for="(k, j) in item.Options" :key="j">
                  <span style="font-size: 18pxmargin: 10px 0;">{{ k.Opt }}</span><span v-html="k.Content" style="font-size: 18px;" v-katex></span>
                </a-radio>
              </a-radio-group>
              <a-checkbox-group class="Multiple" v-if="item.ItemTypeId === '10'">
                <a-row>
                  <a-col :span="24">
                    <a-checkbox v-for="i in item.Options" :key="i.Opt" :value="item.ItemId + '|' + i.Opt" v-show="i.Type === 1">
                      {{ i.Option }}:
                      <span v-html="i.Content" style="fontsize: 20px" v-katex></span>
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
              <div v-if="item.ItemTypeId === 23">
                <img :src="item.PicUrl" alt="" style="max-width: 350px" />
              </div>
            </div>
            <div class="detail_answer">
              <p v-if="item.ItemTypeId!=41&&item.ItemTypeId!=51" >我的答案：<span v-html="item.ShowUserAnswer" v-katex ></span></p>
              <P v-if="item.ItemTypeId==41">我的答案:
                <div class="Special" v-if="item.ItemTypeId==41">
                  <div class="new_special" v-for="(item,index) in ItemChange" :key="index">
                    <span>{{item}}</span>
                  </div>
                </div>
              </P>
              <p v-if="item.ItemTypeId==51">我的答案：
                <RenderTable :TableItemInfo="item.ItemChange.TableItemInfo" :analysis="true" :key="key"></RenderTable>
              </p>
              <p v-if="item.ItemTypeId!=41&&item.ItemTypeId!=51">正确答案：<span v-html="item.ShowAnswer " v-katex ></span></p>
              <p v-if="item.ItemTypeId==41">正确答案：
                <div class="Special" v-if="item.ItemTypeId==41">
                  <div class="new_special" v-for="(item,index) in YesItemChange" :key="index">
                    <span>{{item}}</span>
                  </div>
                </div>
              </p>
               <p v-if="item.ItemTypeId==51">正确答案：
                 <RenderTable :TableItemInfo="item.ItemChange.TableItemInfo" :showAnswer="true" :key="key"></RenderTable>
               </p>
              <p class="cur" style="font-size: 14px; color: #68bb97">解析</p>
              <p v-show="item.ItemId">解析: <span v-html="item.Analysis" v-katex></span></p>

            </div>
          </div>
        </div>
      </div>
      <!-- <div class="detail_right">
        <span style="font-size:18px">题目目录</span>
        <br />
        <a v-for="(i, t) in this.getList.Items" :key="t" class="title-btn-whole" :class="{ 'def-color': defalutCol === i.ItemId, 'wrong-bgc': i.IsKeyTrue === 2 }" @click="switchTitle(t, i.ItemId, i.TypeId)">
          {{ t + 1 }}
        </a>
      </div> -->
    </div>
  </div>
</template>

<script>
import { HappyScroll } from 'vue-happy-scroll'
import { arrlistTitle } from '@/utils/bankedCloze/bankedCloze'
import eventBus from '@/components/eventBus/eventBus'
import RenderTable from '../Exam_Student/components/tableStem/renderTable.vue'
export default {
  created() {
    this.PaperTitle = this.$route.query.PaperTitle
    this.getList = JSON.parse(localStorage.getItem('studentList'))
    this.list_show.push(this.getList.Items[0])
  },
  components: {
    HappyScroll,
    RenderTable
  },
  mounted() {
    let timer = setTimeout(() => {
      this.init(this.getList)
      clearTimeout()
    }, 100)
    // this.$nextTick(() => {
    //   this.init(this.getList)
    // })
  },
  data() {
    return {
      value: 1,
      getList: [],
      list_show: [],
      key:0,
      radioStyle: {
        display: 'block',
      },
      PaperTitle: '',
      defalutCol: '',
      newSubject: 1,
      stuStatistics: '#3CB98F',
      paperSubjectNum: '',
      ItemChange: [],
      YesItemChange: [],
      swiperOption: {
        slidesPerView: 10, //一行显示4个
        spaceBetween: 10, //间隔30
        freeMode: true,
        speed: 1000, //滑动速度
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
          hideOnClick: true,
          disabledClass: 'my-button-disabled', //前进后退按钮不可用时的类名。
          hiddenClass: 'my-button-hidden', //按钮隐藏时的Class
        },
      },
    }
  },
  watch: {
    $route() {
      const ChangId = window.location.href.split('?')[1]
      this.PaperTitle = this.$route.query.PaperTitle
      window.location.reload()
    },
  },
  methods: {
    GoBack() {
      this.$router.go(-1)
    },
    prevPhoto() {
      this.$refs.mySwiper.$swiper.slidePrev()
    },
    nextPhoto() {
      this.$refs.mySwiper.$swiper.slideNext()
    },
    //获取信息
    init(data) {
      this.paperSubjectNum = data.Items.length
      this.defalutCol = data.Items[0].ItemId
      console.log(data,":data")
      data.Items.forEach((item) => {
        // item.ShowAnswer = item.ShowAnswer.replace(/\\\\/g,'\\');
        item.ItemTitle = item.ItemTitle.replace(/\#\#/g, ` `)
        const regx4 = /\$\$\/frac.*?\$\$/g
        const regx4List = item.ItemTitle.match(regx4)
        // console.log(regx4List)
        if (regx4List && regx4List.length > 0) {
          regx4List.forEach((item_regx4) => {
            let fenshu = item_regx4
            fenshu = fenshu.replace(
              /\$\$\/frac\{/g,
              `
          <div style="display:inline-block;vertical-align:middle;margin:0 8px;">
        `
            )
            fenshu = fenshu.replace(
              /\}\{/g,
              `
          <hr style="width:100%;height:3px;background:black;position:relative;top:2px"></hr>
        `
            )
            fenshu = fenshu.replace(/\}\$\$/g, '</div>')
            item.ItemTitle = item.ItemTitle.replace(item_regx4, fenshu)
          })
        }
        const regex = new RegExp('<img')
        const stem = item.ItemTitle.replace(/\#\@/g, '', `<img style="max-width: 80%; height: auto"`)
        item.ItemTitle = stem
        item.Options.forEach((el) => {
          const regex = new RegExp('<img')
          const stem = el.Content.replace(regex, `<img style="max-width: 80%; height: auto"`)
          el.Content = stem
        })
        var arr = []
        var YesArray = []
        if (item.ItemChange.Rows) {
          item.ItemChange.Rows.forEach((el) => {
            el.forEach((i) => {
              arr.push(i)
            })
          })
        }
        this.ItemChange = arr
        if (item.YesItemChange.Rows) {
          item.YesItemChange.Rows.forEach((el) => {
            el.forEach((i) => {
              YesArray.push(i)
            })
          })
        }
        this.YesItemChange = YesArray
      })
    },
    switchTitle(index, ItemId, typeId) {
      // document.getElementById(ItemId).scrollIntoView({ behavior: 'smooth' })
      this.list_show[0] = this.getList.Items[index]
      this.newSubject = index + 1
      this.key = index
      this.defalutCol = ItemId
      this.callswiper(index)
      this.key++
    },
    callswiper(index) {
      if (index >= 9) {
        this.nextPhoto()
      }
    },
    // 处理选词填空题展示样式
    bankedClozeTitle (item) {
      return arrlistTitle('展示', item, item.ItemTypeId)
    }
  }
}
</script>
<style lang="less" scoped>
.detail_box {
  width: 100%;
  display: flex;
  .title_name {
    font-size: 21px;
    margin-left: 10px;
    margin-bottom: 10px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(30, 30, 30, 0.85);
    text-align: center;
  }
  .detail_left {
    width: 100%;
    .left_tabs {
      width: 100%;
      background: #ffff;
      padding: 10px;
      border-radius: 5px;
      text-align: center;
      span {
        font-size: 22px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: rgba(30, 30, 30, 0.85);
      }
    }
    .left_content {
      width: 100%;
      font-size: 16px;
      border-radius: 5px;
      .items {
        margin-top: 15px;
        background: #fff;
        padding: 10px;
        line-height: 34px;
      }
      .swiper_list {
        width: 100%;
        display: flex;
        .tea-analusis {
          position: relative;
          width: 100%;
          height: auto;
          padding: 10px;
          background: #ffff;
          border-radius: 5px;
        }
        .line_border {
          width: 97%;
          height: 10px;
          position: absolute;
          border: 1px solid #e6e6e6;
          background: #e6e6e6;
          top: 55%;
        }
        .left-arrow,
        .right-arrow {
          position: absolute;
          display: flex;
          height: 100px;
          align-items: center;
          justify-content: center;
          bottom: -25%;
          z-index: 2;
          .arrow-img {
            &:hover {
              background: #68bb97;
            }
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #dadada;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            i {
              background: url('../../../assets/teacher/IntelligentCorrection/icon-arrow.png') no-repeat center center;
              width: 16px;
              height: 16px;
            }
          }
        }
        .left-arrow {
          left: 8px;
          .arrow-img {
            transform: rotate(180deg);
          }
        }
        .right-arrow {
          right: 6px;
        }
        .imgbox {
          width: 20%;
          font-size: 18px;
          padding: 10px;
          position: relative;
          background: #fff;
          img {
            width: 30px;
            right: 70%;
            top: 43%;
            position: absolute;
          }
          span {
            margin-left: 10px;
            right: 5%;
            top: 43%;
            position: absolute;
          }
        }
      }
    }
  }
  .detail_right {
    width: 28%;
    padding: 20px 18px;
    background-color: #fff;
    margin-left: 23px;
    text-align: center;
    .p-chart {
      display: inline-block;
    }
    // 统计
    .Statistics {
      margin-bottom: 70px;
      text-align: center;
    }
    // 错题扫除
    .wrong-topic {
      text-align: center;
      margin-bottom: 80px;
      span {
        display: inline-block;
        width: 190px;
        height: 32px;
        line-height: 32px;
        color: #fff;
        background-color: #68bb97;
        border-radius: 30px;
      }
    }
  }
}
/deep/.happy-scroll-content {
  width: 100%;
}
// 题目目录
.title-btn-whole {
  display: inline-block;
  width: 41px;
  height: 40px;
  line-height: 40px;
  margin: 16px 0px 0 8px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 35px;
  cursor: pointer;
  background-color: #ffff;
}
.correct {
  color: #68bb97;
}
.answer-wrong {
  color: #ff682c;
}
.wrong-bgc {
  border: 2px solid #da1b1b;
  color: #da1b1b;
}
.def-color {
  background-color: #68bb97;
  border-color: #68bb97;
  color: #fff;
}
</style>
