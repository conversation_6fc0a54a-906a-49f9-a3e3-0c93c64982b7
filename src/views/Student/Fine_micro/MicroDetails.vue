<template>
  <div>
    <!-- 头部视频 / 数据 -->
    <div class="video-infor">
      <!-- 播放器 -->
      <div class="video fl">
        <video-player class="video-player vjs-custom-skin" ref="videoPlayer" :playsinline="true" :options="playerOptions">
        </video-player>
      </div>
      <template>
        <div class="video-menu">
          <div class="chapter-name">
            <p class="pc_font_size20 font_size22 ipad_font_size24">{{ rigthdata.ChapterName }}</p>
            <div class="xuekeStyle pc_font_size16 font_size16 ipad_font_size18" :style="{'background-color':bgColog[SubjectIdName.Id+'bg'],'color':bgColog[SubjectIdName.Id]}">
              {{ SubjectIdName.name }}
            </div>
          </div>
<!--          <a-collapse :bordered="false" expand-icon-position="right" :style="{ fontSize: '14px', fontFamily: 'PingFangSC-Regular, PingFang SC', fontWeight: '400' }">-->
          <a-collapse :bordered="false" expand-icon-position="right" class="pc_font_size16 font_size20 ipad_font_size22">
            <a-collapse-panel :header="item.ChapterName + '      (' + item.ListLesson.length + '个视频)'" :style="{ backgroundColor: 'white' }" v-for="(item, index) in rightDataList" :key="index">
              <div class="chapterDetail pc_font_size14 font_size18 ipad_font_size20" v-for="(m, ind) in item.ListLesson" :key="ind" @click="handleClick(m.Id)">
                {{ ind + 1 }},{{ m.Name }}&nbsp;&nbsp;({{ m.Duration }})
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </template>
      <!-- <template>
        <div class="video-menu">
          <happy-scroll >
            <div class="chapter-name">{{ rigthdata.ChapterName }}</div>
            <div v-for="(item,index) in rightDataList" :key="item.Id" >
              <span style="font-size: 16px;color: #000;">{{ item.ChapterName }}</span>
              <br>
              <p class="chapter-little cur" :class="{'chap-col': chapCol === ite.Id }" v-for="(ite,ind) in item.ListLesson" :key="ind" @click="handleClick(ite.Id,ite.ChapterId)">
                <span>{{ ind + 1 }},</span>
                <span>{{ ite.Name }}</span>
              </p>
            </div>
          </happy-scroll>
        </div>
      </template> -->
    </div>
    <!-- 视频解析 -->
    <div class="video-info clearfix">
      <div class="video_div1">
        <div class="pc_font_size18 font_size20 ipad_font_size22">
          <div style="display: flex;align-items: center;padding: 0;margin: 0;">
            <p>{{ entity.ChapterName.substring(0,15)+'...' }}</p>
          </div>
          <h3> {{ entity.Name }}</h3>
          <p>发布时间：{{ entity.CreateTime }}</p>
        </div>
        <div class="pc_font_size18 font_size20 ipad_font_size22">
          <p>课程评价</p>
          <a-rate v-model="score" disabled />
        </div>
        <div class="pc_font_size18 font_size20 ipad_font_size22">
          <p>播放次数：</p>
          <p>{{ entity.ClickNum }}</p>
        </div>
        <div class="pc_font_size18 font_size20 ipad_font_size22">
          <p class="cur" @click="copyAppSecret()">
            复制链接：
            <a-icon type="copy" :style="{ fontSize: '20px', verticalAlign: 'middle' }" />
          </p>
          <p @click="collection(color)" style="cursor: pointer">
            收藏：
            <a-icon type="star" :class="{ color: color }" :style="{ fontSize: '20px', verticalAlign: 'middle' }" />
          </p>
        </div>
      </div>
      <!-- 教师信息 -->
      <div class="video_div2">
        <div class="pc_font_size18 font_size20 ipad_font_size22">
          <a-avatar :src="entity.Photo" class="fl" size="large" icon="user" />
          <div class="follow-id fl" @click="toTeacherMicro(entity.CreatorId)">
            <p>{{ entity.UserName }}</p>
            <p>{{ entity.SchoolName }}</p>
          </div>
        </div>
        <!-- 课程 -->
        <div class="pc_font_size18 font_size20 ipad_font_size22">
          <p>{{ entity.FollowCount }}粉丝</p>
          <p>共{{ entity.ProjectNumber }}个课程</p>
        </div>
        <!-- 关注 -->
        <div class="follow-concern pc_font_size18 font_size20 ipad_font_size22">
          <span class="follow" :class="{ follow: this.texts === '已关注', noFollow: this.texts === '关注Ta' }" @click="addMyCollection()">{{ this.texts }}</span>
          <a-icon type="right" />
        </div>
      </div>
    </div>
    <!-- <div class="discuss">
      <p class="practice">相关练习</p>
      <ul class="clearfix">

        <li class="fl" v-for="(item,index) in paperData" :key="index">
          <div class="clearfix">
            <div class="fl l-details">
              <p style="font-size: 10px;"><span class="chapter-width" style="display:inline-block;">{{ item.ChapterName }}</span><span class="class-area">{{ item.PaperTypeName }}</span></p>
              <p style="font-size: 10px;" class="chapter-width">{{ item.Title }}<a-icon v-if="item.IsVideo === 1" type="play-circle" /></p>
              <div class="correct-chart" v-if="item.DoPaperType === 1">
                <p class="p-chart">
                  <a-progress type="circle" :percent="item.Accuracy" height="100px" :width="50" :strokeColor="accuracyColor"/><br>
                  <span>正确率</span>
                </p>
                <p class="p-chart" v-if="item.RevisionCount !== 0">
                  <a-progress
                    type="circle"
                    :percent="50"
                    height="100px"
                    :width="50"
                    :format="() => item.RevisionCount+ '/' +item.ItemCount"
                    :strokeColor="stuStatistics"
                  >
                  </a-progress><br>
                  <span>待订正错题数</span>
                </p>
              </div>
              <div v-if="item.DoPaperType === 0 || item.DoPaperType === 2" style="margin: 21px 0 34px 0;">
                <p>共{{ item.ItemCount }}道题</p>
                <p>预计完成时长15分钟</p>
              </div>
            </div>
            <div class="fg">
              <p class="chapter-width" style="text-align: right;font-size: 10px;color:#ccc;margin-bottom: 50px">{{ item.CreateTime }}</p>
              <div class="r-info" v-if="item.DoPaperType === 1">

                <div v-if="item.Rank === 1">
                  <img src="@/assets/student/excellent100.png" alt="">
                  <p>优秀</p>
                </div>
                <div v-if="item.Rank === 2">
                  <img src="@/assets/student/excellent.png" alt="">
                  <p>优秀</p>
                </div>
                <div v-if="item.Rank === 3">
                  <img src="@/assets/student/good.png" alt="">
                  <p>良好</p>
                </div>
                <div v-if="item.Rank === 4">
                  <img src="@/assets/student/qualified.png" alt="">
                  <p>合格</p>
                </div>
                <div v-if="item.Rank === 5">
                  <img src="@/assets/student/strive.png" alt="">
                  <p>需努力</p>
                </div>
              </div>
            </div>
          </div>
          <div >
            <span v-if="item.DoPaperType === 1" class="operation"><span @click="toReviewWrong(item.PaperId, item.ChapteId)">复习回顾</span></span>
            <span v-if="item.DoPaperType === 0 || item.DoPaperType === 2" class="operation" ><span @click="toAnswerPaper(item.PaperId)" style="background-color:#68BB97">开始答题</span></span>
            <span v-show="item.EndTime !== '' && item.PaperTypeName !== '区'" style="text-align: right;font-size: 10px;color:#ccc;">{{ item.EndTime }} 截止</span>
          </div>
        </li>
      </ul>
    </div> -->
    <!-- <a-pagination
      class="paging"
      show-quick-jumper
      :default-current="1"
      :total="PageCounts"
      hideOnSinglePage
      @change="onChange"
      :defaultPageSize="6" /> -->
    <!-- <p class="footer">Copyright©上海有我科技有限公司，All Rights Reserved</p> -->
  </div>
</template>

<script>
import '@/utils/utils.less'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'

export default {
  created () {
    this.score = +this.$route.query.score
    //  获取学科对应颜色
    this.bgColog = CorrespondingColor()
    this.SubjectIdName = JSON.parse(this.$route.query.SubjectIdName)
  },
  mounted () {
    this.userId = localStorage.getItem('UserId')
    this.init(this.$route.query.videoId)

    // this.getEvaluate()
  },
  data () {
    return {
      score: 1, // 视频星数
      userId: '',
      playerOptions: {
        playbackRates: '', // 播放速度'[0.5, 1, 1.5, 2]'
        autoplay: false, // 如果true,浏览器准备好时开始回放。
        controls: true, // 控制条
        preload: 'auto', // 视频预加载
        muted: false, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        language: 'zh-CN',
        aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [
          {
            // type: 'video/mp4',
            // type: 'video/ogg',
            type: 'video/webm',
            src: '' // 你所放置的视频的地址，最好是放在服务器上
          }
        ],
        poster: '', // 你的封面地址（覆盖在视频上面的图片）
        width: document.documentElement.clientWidth,
        notSupportedMessage: '此视频暂无法播放，请稍后再试' // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
      },
      // 正确率
      accuracyColor: '#68BB97',
      // 待订正错题
      wrongColor: '#F87175',
      // 待订正
      stuStatistics: '#F87175',
      // 右侧tabbar样式切换
      // item: false,
      value: 2,
      color: false,
      entity: {},
      texts: '关注Ta',
      rigthdata: {},
      rightDataList: [],
      chapCol: '1',
      // 相关练习的章节id
      checkedKeys: '',
      PageCounts: 1,
      // 当前页码
      paperIndex: 1,
      paperData: [],
      //  多学科部分
      bgColog: {},
      SubjectIdName: {}
    }
  },
  watch: {
    $route () {
      const ChangeId = window.location.href.split('?')[1]
      if (this.$route.fullPath === '/Student/Fine_micro/MicroDetails?' + ChangeId) {
        this.init(this.$route.query.videoId)
        // this.getEvaluate()
        this.score = +this.$route.query.score
      }
    }
  },
  methods: {
    init (id) {
      this.loading = true
      // 获取参数
      if (id) {
        this.$nextTick(() => {
          this.$http.post('/Paper/Exam_MicroLesson/GetVedeoDataById', { id: id, userId: this.userId }).then(resJson => {
            this.entity = resJson.Data
            this.playerOptions.sources[0].src = resJson.Data.VideoUrl
            this.playerOptions.poster = resJson.Data.CoverUrl
            this.poster = resJson.Data.CoverUrl
            // 章节id
            this.checkedKeys = resJson.Data.ChapterId
            this.getMicroMenu()
            this.getPastPaper()
            if (this.entity.FocusDeleted === false) {
              this.color = true
            } else {
              this.color = false
            }
            if (this.entity.CollectionDeleted === false) {
              this.texts = '已关注'
            } else {
              this.texts = '关注Ta'
            }
          })
        })
      } else {
        this.loading = false
      }
    },
    getMicroMenu () {
      this.$http
        .post('/Paper/Exam_MicroLesson/GetSubject', {
          userId: this.entity.CreatorId,
          videoId: this.$route.query.videoId
        })
        .then(resJson => {
          this.loading = false
          this.rigthdata = resJson.Data.Master
          this.rightDataList = resJson.Data.ChildList
        })
    },
    getEvaluate () {
      this.$nextTick(() => {
        this.$http
          .post('/Evaluate/TeacherVideo_Evaluate/GetDataBy', {
            videoId: this.$route.query.videoId
          })
          .then(resJson => {
            this.value = resJson.Data.Score
          })
      })
    },
    // 获取往日练习的数据
    getPastPaper (id) {
      const Platform = parseInt(localStorage.getItem('PL_newP'))
      this.$http
        .post('/Student/Exam_Student/GetStudentPaper', {
          UserId: this.userId,
          PageNo: this.paperIndex,
          PageIndex: 6,
          ChapterId: this.checkedKeys,
          Platform
        })
        .then(res => {
          this.paperData = res.Data.Items
          this.PageCounts = res.Data.TotalItems
          if (res.Data.Items.length !== 0) {
            this.loadPaper = false
            this.loadDefault = false
          } else {
            this.loadPaper = false
            this.loadDefault = true
          }
        })
    },
    // 获取页码
    onChange (pageNumber) {
      this.paperIndex = pageNumber
      this.getPastPaper()
    },
    // 跳转名师页面
    toTeacherMicro (teacherId) {
      this.$router.push({
        path: '/Student/Fine_micro/FamousTeacherPage',
        query: {
          teacherId
        }
      })
    },
    // tabbar 样式切换
    handleClick (id, chapterId) {
      this.chapCol = id
      this.checkedKeys = chapterId
      // this.item = true
      this.init(id)
      this.getPastPaper()
    },
    // 选择星级后触发
    // handleChange () {
    //   this.$nextTick(() => {
    //     this.$http.post('/Evaluate/TeacherVideo_Evaluate/AddData', {
    //       Score: this.value,
    //       videoId: this.$route.query.videoId
    //     }).then(resJson => {
    //       this.entity = resJson.Data
    //     })
    //   })
    // },
    // 收藏功能
    collection (color) {
      // let qie = this.$message.success('已收藏')
      if (color) {
        this.$message.success('取消收藏')
        this.color = false
      } else {
        this.$message.success('已收藏')
        this.color = true
      }
      this.$http
        .post('/Focus/Focus_Collection/AddDataFocus', {
          videoId: this.$route.query.videoId
        })
        .then(resJson => {})
    },
    // 复制链接分享视频
    copyAppSecret () {
      const createInput = document.createElement('input')
      createInput.value = window.location.href
      document.body.appendChild(createInput)
      createInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      createInput.style.display = 'none'
      this.$message.success('复制成功')
    },
    // 关注
    addMyCollection () {
      this.$http
        .post('/Focus/Focus_Collection/AddDataColletion', {
          CreatorId: this.entity.CreatorId
        })
        .then(res => {
          this.init(this.$route.query.videoId)
          if (this.entity.collectionDeleted === false) {
            this.texts = '已关注'
          } else {
            this.texts = '关注Ta'
          }
        })
    },
    // 跳转到复习回顾
    toReviewWrong (id, chapterId) {
      this.$router.push({ path: '/Student/Exam_Student/ReviewWrong', query: { paperId: id, chapterId: chapterId } })
    },
    // 新跳转至答题页面
    toAnswerPaper (id) {
      this.$router.push({
        path: '/Student/Exam_Student/AnswerReady',
        query: {
          paperId: id
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.chap-col {
  color: #70bf9d;
}
// 多学科部分
.xuekeStyle{
  padding: 4px 18px !important;
  margin: 0 !important;
  border-radius: 100px;
}
.chapterDetail {
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #373737;
  cursor: pointer;
  width: 100%;
  height: 50px;
  line-height: 50px;
  padding-left: 20px;
}
.chapterDetail:hover {
  background-color: #68bb97;
}
.chapter-width {
  // display: inline-block;
  width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.chapter-width:hover {
  overflow: visible;
  z-index: 2;
}
// 校，班，区
.class-area {
  margin-left: 3px;
  padding: 2px 4px;
  text-align: center;
  color: #fff;
  background-color: RGBA(131, 147, 152, 0.4);
  border-radius: 10px;
}
.chapter-name {
  //font-size: 18px;
  //font-weight: 700;
  margin-bottom: 20px;
  color: #000;
  display: flex;
  justify-content: space-between;
}
.chapter-little {
  font-size: 16px;
  margin-left: 20px;
}
.chapter-little:hover {
  color: #70bf9d;
}
// 视频详情
.video-infor {
  display: flex;
  .video-menu {
    box-sizing: border-box;
    padding: 18px;
    height: 460px;
    overflow-y: scroll;
    width: 50%;
    position: relative;
    flex: 1;
    //padding-left: 25px;
    background-color: white;
  }
}
.video {
  display: inline-block;
  width: 830px;
  height: 465px;
  text-align: center;
  line-height: 100px;
  border: 1px solid transparent;
  border-radius: 12px;
  overflow: hidden;
  background: #fff;
  position: relative;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  margin-right: 25px;
}
// 微课名称
.video-name {
  width: 327px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
// 侧边栏
.sidebar {
  padding-left: 850px;
  .sidebar-content {
    margin-top: 10px;
    padding: 5px 17px;
    border-radius: 5px;
    background-color: #ccc;
    span:nth-child(4) {
      font-size: 10px;
    }
    span:nth-child(2) {
      line-height: 45px;
      float: right;
      font-weight: 600;
    }
  }
}
.video-info {
  height: 129px;
  padding: 0 16px;
  margin-top: 15px;
  margin-left: 4px;
  border-radius: 5px;
  background-color: #fff;
  display: flex;
  .video_div1{
    width: 60%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-right: 1px solid gainsboro;
  }
  .video_div2{
    width: 40%;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  //div {
  //  float: left;
  //  padding-left: 15px;
  //  margin-right: 25px;
  //  margin-top: 43px;
  //}
  //div:nth-child(1) {
  //  margin-top: 22px;
  //  h3 {
  //    margin: 10px 0;
  //  }
  //}
  //div:last-child {
  //  margin-right: 0;
  //}

  // 教师信息
  .follow-id {
    //margin-top: 0;
    margin-left: 6px;
    cursor: pointer;
  }
  .follow-id:hover {
    color: chocolate;
  }
  // 关注
  //.follow-concern {
  //  margin-top: 53px;
  //}
  .noFollow {
    color: #fff;
    background-color: #70bf9d;
  }
  // 底部竖线
  .vive-line {
    width: 1px;
    height: 100%;
    margin-top: 0;
    padding: 0;
    background-color: #000;
  }
}
// 正确图表
.correct-chart {
  margin-top: 30px;
  .p-chart {
    display: inline-block;
    font-size: 12px;
    text-align: center;
    color: #9ba6a9;
  }
}
// 关注按钮
.follow {
  width: 30px;
  height: 20px;
  margin-right: 10px;
  padding: 5px 10px;
  border-radius: 20px;
  background-color: #ddd;
  cursor: pointer;
}
.color {
  color: yellow;
}
// 相关练习
.discuss {
  margin-top: 56px;
  .practice {
    font-size: 16px;
    color: #4d5753;
  }
  li {
    position: relative;
    min-width: 400px;
    // width: 313px;
    // height: 235px;
    min-height: 235px;
    padding: 20px;
    margin-top: 18px;
    margin-bottom: 20px;
    margin-right: 20px;
    // margin-left: 16px;
    border-radius: 5px;
    background-color: #fff;
    p:nth-child(1) {
      margin-bottom: 8px;
      margin-right: 10px;
    }
    .operation {
      margin-bottom: 0;
      span:nth-child(1) {
        display: inline-block;
        width: 136px;
        height: 30px;
        margin-right: 28px;
        line-height: 30px;
        color: #fff;
        text-align: center;
        background-color: #bdd3ce;
        border-radius: 25px;
        cursor: pointer;
      }
    }
  }

  li:hover {
    box-shadow: 8px 8px 7px grey;
  }
  .l-details {
    margin-right: 50px;
    margin-bottom: 20px;
    p:nth-child(2) {
      //  font-size: 15px;
      margin-bottom: 5px;
    }
    p:nth-child(3) {
      margin-bottom: 52px;
      // span {
      //   margin-left: 20px;
      //   width: 15px;
      //   padding: 1px 2px;
      //   border: 1px solid #000;
      //   background-color: #DEF;
      //   border-radius: 5px;
      // }
    }
  }
  .r-info {
    position: relative;
    text-align: center;
    img {
      margin-bottom: 2px;
    }
    .span {
      position: absolute;
      top: 20px;
      right: 25px;
    }
    p {
      text-align: center;
    }
  }
}
</style>
