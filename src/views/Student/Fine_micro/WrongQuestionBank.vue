<template>
  <div class="whole-mr clearfix">
    <div class="switch_top">
      <switch-Multidisciplinary :sortShow="sortShow" widthNum="100%" @handleClick="newHandleClick"></switch-Multidisciplinary>
    </div>
    <div class="flex">
      <!-- 树菜单  -->
      <div
        class="flex_item_shrink "
        style="transition: all ease 0.3s"
        :class="{ openInDialogBorder: openInDialog }"
        :style="{ width: leftWidth + '%' }"
      >
        <left-screening ref="leftScreening" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 16 ? 2.3 : 16" @getAllList="getLeftScreening"></left-screening>
      </div>
      <div class="right">
        <!-- <div class="wrong-bank fl">
          <p class="clearfix screen-menu">
            <span class="fl pc_font_size18 font_size20 ipad_font_size24">
              <span class="cur f-s" :class="{ 'w-color': wrongColor === 1 }" @click="load ? retuBtn() : ToRevised()">错题扫除</span>
              <span>/</span>
              <span class="cur f-s" :class="{ 'w-color': wrongColor === 2 }" @click="load ? retuBtn() : wrongBank()">错题收纳</span>
            </span>
            <span class="fg m-r-s"> </span>
          </p>
        </div> -->
        <!-- 错题收纳本详情 -->
        <div class="fl wrong-info" v-show="wrongColor == 2">
          <div class="example" v-if="load" style="text-align: center">
            <a-spin />
          </div>
          <div
            v-else
            style="font-size: 14px;display: flex;align-items: center;flex-wrap: wrap;margin-bottom: 14px;"
            class="ant-col-xl-24 ant-col-xxl-24"
          >
            <!--          <a-col :xl="5" :xxl="5" v-for="item in wrongBankList" :key="item.PaperId">-->
            <div v-for="item in wrongBankList" :key="item.PaperId">
              <ul class="clearfix">
                <li class="fl clearfix_Li">
                  <div class="fl_padding">
                    <div style="display: flex;align-items: center;justify-content: space-between;">
                      <el-tooltip class="item pc_font_size16 font_size20 ipad_font_size22" effect="dark" placement="right">
                        <div slot="content">
                          <p class="title-width">{{ item.ChapterName }}</p>
                        </div>
                        <p class="title-width">{{ item.ChapterName | ellipsis }}</p>
                      </el-tooltip>
                      <div class="xuekeStyle pc_font_size14 font_size16 ipad_font_size18" :style="{'background-color':bgColog[item.SubjectId+'bg'],'color':bgColog[item.SubjectId]}">
                        {{ item.SubjectName }}
                      </div>
                    </div>
                    <el-tooltip class="item pc_font_size14 font_size18 ipad_font_size20" effect="dark" placement="top-start">
                      <div slot="content">
                        <p class="title-width paper-top">{{ item.PaperTitle }}</p>
                      </div>
                      <p class="title-width paper-top">{{ item.PaperTitle | ellipsis }}</p>
                    </el-tooltip>

                    <p class="pc_font_size12 font_size16 ipad_font_size18">
                      共收纳<span>{{ item.ErrorCount }}</span
                      >道错题
                    </p>
                    <p class="pc_font_size12 font_size16 ipad_font_size18">完成时间：{{ item.Intime }}</p>
                  </div>
                  <div class="error_btn pc_font_size14 font_size18 ipad_font_size20">
                    <span @click="toReviewWrong(item)">查看解析</span>
                    <span @click="Practice(item)">巩固练习</span>
                  </div>
                </li>
              </ul>
              <!--          </a-col>-->
            </div>
          </div>
          <!--        </a-row>-->
          <!-- 分页 -->
          <a-pagination
            class="paging"
            show-quick-jumper
            :default-current="1"
            :defaultPageSize="15"
            :total="total"
            @change="onChange"
            hideOnSinglePage
          />
        </div>
        <!-- 缺省 -->
        <div class="wrong-lack" v-if="showWrong">
          <img src="@/assets/lack/暂无搜索记录.png" alt="" />
          <p>暂无记录哦</p>
        </div>
        <div></div>
        <!-- 待订正错题详情 -->
        <div class="fl wrong-info" v-show="wrongColor == 1">
          <div class="example" v-if="load" style="text-align: center">
            <a-spin />
          </div>
          <ul class="clearfix" style="margin-bottom: 14px;" v-else>
            <li class="fl clearfix_Li" v-for="item in wrongBankList" :key="item.PaperId">
              <div class="fl_padding">
                <div style="display: flex;align-items: center;justify-content: space-between;">
                  <el-tooltip class="item pc_font_size18 font_size20 ipad_font_size22" effect="dark" placement="top-start" :content='item.ChapterName'>
                    <div slot="content">
                      <p class="title-width">{{ item.ChapterName }}</p>
                    </div>
                    <p class="title-width">{{ item.ChapterName | ellipsis }}</p>
                  </el-tooltip>
                  <div class="xuekeStyle pc_font_size16 font_size16 ipad_font_size18" :style="{'background-color':bgColog[item.SubjectId+'bg'],'color':bgColog[item.SubjectId]}">
                    {{ item.SubjectName }}
                  </div>
                </div>
                <el-tooltip class="item pc_font_size16 font_size18 ipad_font_size20" effect="dark" placement="top-start" :content='item.PaperTitle'>
                  <div slot="content">
                    <p class="title-width">{{ item.PaperTitle.substring(0,11)+'...' }}</p>
                  </div>
  <!--                <p class="title-width">{{ item.PaperTitle | ellipsis }}</p>-->
                  <p class="title-width">{{ item.PaperTitle.substring(0,11)+'...' }}</p>
                </el-tooltip>

                <p class="pc_font_size16 font_size16 ipad_font_size18" style="margin-top: 4px;">
                  共收纳<span>{{ item.ErrorCount }}</span
                  >道错题
                </p>
                <p class="pc_font_size16 font_size16 ipad_font_size16">完成时间：{{ item.Intime }}</p>
                <p class="review pc_font_size14 font_size18 ipad_font_size20" style="background-color: #68bb97;" @click="toCleaningWrong(item)">开始订正</p>
              </div>
            </li>
          </ul>
          <!-- 分页 -->
          <a-pagination
            class="paging"
            show-quick-jumper
            :default-current="1"
            :defaultPageSize="15"
            :total="total"
            @change="onChange"
            hideOnSinglePage
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import '@/utils/utils.less'
import eventBus from '@/components/eventBus/eventBus'
import switchMultidisciplinary from '@/components/switchMultidisciplinary/switchMultidisciplinary'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { debounce } from 'lodash'
import leftScreening from '@/components/leftScreening/index.vue'
export default {
  created () {
    // 多学科
    this.bgColog = CorrespondingColor()
    this.id = localStorage.getItem('UserId')
    // this.chapterTree()
    this.getWrongBank()
    this.$store.commit('setHeaderIndex', 2)
  },
  components: {
    leftScreening,
    switchMultidisciplinary
  },
  data () {
    return {
      // 缺省
      showWrong: false,
      // 加载
      load: true,
      // 默认展开指定的数菜单
      expandedKeys: ['一 10以内的数'],
      // 展开父节点
      autoExpandParent: true,
      // 默认选中的子节点
      checkedKeys: ['说一说'],
      // 设置选中的数节点
      selectedKeys: [],
      treeData: [],
      chapterId: '0',
      replaceFields: {
        children: 'Second',
        title: 'ChapterName',
        key: 'ChapterId'
      },
      wrongColor: 2,
      pageNo: 1,
      paperType: 0,
      regionColor: 1,
      // 错题收纳
      wrongBankList: [],
      total: 1,
      // 多学科部分
      sortShow: false,
      bgColog: {},
      // SubjectIdName: {}
      // 章节树使用
      openInDialog: false,
      leftWidth: 16,
    }
  },
  filters: {
    ellipsis (_val) {
      if (!_val) return ''
      if (_val.length > 7) {
        return _val.slice(0, 7) + '...'
      }
      return _val
    }
  },
  mounted () {
    eventBus.$on('getWrongColor', (getWrongColor) => {
      this.$nextTick(() => {
        this.wrongColor = 2
      })
    })
  },
  watch: {
    $route () {
      if (this.$route.fullPath === '/Student/Fine_micro/WrongQuestionBank') {
        this.load = true
        this.getWrongBank()
        // this.chapterTree()
        this.$store.commit('setHeaderIndex', 2)
        this.$refs.leftScreening.getChild()
      }
    }
  },
  methods: {
    // 章节树组件筛查
    getLeftScreening(opt){
      this.pageNo = 1
      this.getWrongBank()
    },
    retuBtn () {
      return false
    },
    // 多学科选择学科点击事件
    newHandleClick: debounce(function (val) {
      if (val === '-1') {
        // this.treeData = []
        this.$refs.leftScreening.empty()
      } else {
        // this.chapterTree()
        this.$refs.leftScreening.getChild()
      }
      this.pageNo = 1
      this.getWrongBank()
    }, 800),
    getWrongColor () {
      this.wrongColor = 2
    },
    // 章节数
    chapterTree () {
      this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', { userid: this.id, IsWeek: 1 }).then((res) => {
        this.treeData = res.data.Data
      })
    },
    // 点击复选框触发
    onCheck (checkedKeys) {
      this.chapterId = checkedKeys.join(',')
      this.pageNo = 1
      this.getWrongBank()
    },
    // 点击树节点触发
    onSelect (selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    // 分页
    onChange (pageNumber) {
      this.pageNo = pageNumber
      this.getWrongBank()
    },
    // 待订正错题
    ToRevised () {
      this.wrongColor = 1
      this.pageNo = 1
      this.total = 0
      this.load = true
      this.getWrongBank()
    },
    // 错题收纳本
    wrongBank () {
      this.wrongColor = 2
      this.pageNo = 1
      this.load = true
      this.getWrongBank()
    },
    getWrongBank (paperType) {
      this.wrongBankList = []
      const platform = localStorage.getItem('PL_newP')
      const { Ids } = this.$store.state.chapterStorage.ChapterObj
      this.$uwonhttp
        .post('/PersonalController/Personal/GetStudentWrongPaperList', {
          // firstChapter: this.chapterId,
          firstChapter: Ids.join(','),
          userID: this.id,
          pageNo: this.pageNo,
          pageSize: 15,
          paperType: this.paperType,
          datatype: this.wrongColor,
          platform
        })
        .then((res) => {
          if (res.data.Data.Items.length === 0) {
            this.showWrong = true
            // this.load = false
            // this.wrongBankList = res.data.Data.Items
          } else {
            this.showWrong = false
            // this.wrongBankList = res.data.Data.Items
            //
            // this.total = res.data.Data.TotalItems
            // this.load = false
          }
          this.wrongBankList = res.data.Data.Items
          this.total = res.data.Data.TotalItems
          this.load = false
        })
    },
    // 全部
    whole () {
      this.load = true
      this.regionColor = 1
      this.paperType = 0
      this.getWrongBank()
    },
    // 全区
    wholeArea () {
      this.load = true
      this.regionColor = 2
      this.paperType = 1
      this.getWrongBank()
    },
    // 全班
    wholeClass () {
      this.load = true
      this.regionColor = 3
      this.paperType = 2
      this.getWrongBank()
    },
    // 开始订正
    toCleaningWrong (item) {
      const id = item.PaperId
      // const SubjectIdName = {
      //   Id: item.SubjectId,
      //   name: item.SubjectName
      // }
      const Subject = {name:item.SubjectName,SubjectId: item.SubjectId}
      localStorage.setItem('Multidisciplinary',JSON.stringify(Subject))
      this.$router.push({
        path: '/Student/Exam_Paper/cleaningWrongQuestion',
        query: {
          paperId: id,
          dataId: this.wrongColor,
          // SubjectIdName: JSON.stringify(SubjectIdName), // 学科ID
          from: 'wqb'
        }
      })
    },
    // 查看解析
    toReviewWrong (item) {
      const id = item.PaperId
      const SubjectIdName = {
        Id: item.SubjectId,
        name: item.SubjectName
      }
      this.$router.push({
        path: '/Student/Exam_Paper/onlyWrong',
        query: {
          paperId: id,
          dataId: this.wrongColor,
          SubjectIdName: JSON.stringify(SubjectIdName) // 学科ID
        }
      })
    },
    Practice (item) {
      const id = item.PaperId
      const PaperTitle = item.PaperTitle
      const SubjectIdName = {
        Id: item.SubjectId,
        name: item.SubjectName
      }
      this.$router.push({
        path: '/Student/Fine_micro/PracticeError',
        query: {
          paperId: id,
          PaperTitle: PaperTitle,
          SubjectIdName: JSON.stringify(SubjectIdName) // 学科ID
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
@color: #15335135;
.flex_item_shrink{
  height: 70vh;
}
.whole-mr{
  // display: flex;
  width: 100%;
  .right{
    width: 82%;
    margin-left: 8px;
  }
}
.w-color {
  color: #68bb97;
}
// 多学科部分
.xuekeStyle{
  padding: 3px 14px;
  border-radius: 100px;
}
.switch_top{
  margin-bottom: 18px;
  padding: 18px 25px;
  background-color: #FFFFFF;
  border-radius: 10px;
}
.paper-top {
  font-size: 16px;
}
.regionColor {
  color: #68bb97;
}
//.f-s {
//  font-size: 14px;
//}
// 缺省
.wrong-lack {
  text-align: center;
  color: #ccc;
}
//.title-width {
//  width: 119px;
//  overflow: hidden;
//}
//.title-width:hover {
//  overflow: visible;
//}
//错题收纳 / 待订正错题
.wrong-bank {
  width: 100%;
  padding: 12px 10px;
  background-color: #fff;
  border-radius: 5px;
  p:nth-child(1) {
    //font-size: 24px;
    color: #000000a6;
    border-bottom: 1px solid rgb(246, 246, 246);
    padding-bottom: 8px;
  }
}
.m-r-s {
  margin-right: 24px;
}
// 树菜单
.task-catalog {
  width: 300px;
  height: 90vh;
  margin-right: 10px;
  font-size: 16px;
  padding: 0 10px;
  background-color: #FFFFFF;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space:nowrap;
  /deep/.ant-tree-title {
    font-size: 20px;
    font-weight: 700;
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li {
    margin: 0;
    padding: 10px 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }
  p {
    //height: 40px;
    //line-height: 40px;
    //font-size: 18px;
    padding: 6px;
    //font-weight: 700;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    background-color: #68bb97;
  }
}
// 错题描述详情
.wrong-info {
  width: 100%;
  border-radius: 5px;
  //span {
  //  font-size: 20px;
  //}
  .wrong-line {
    height: 2px;
    margin-top: 38px;
    background-color: #ccc;
  }
  .clearfix_Li {
    width: 280px;
    //min-height: 186px;
    //margin: 18px 30px 20px 0px;
    border-radius: 5px;
    background-color: #fff;
    padding: 12px;
    margin: 12px 12px 0 0;
    // 错题描述
    .fl_padding{
      //min-width: 260px;
      min-height: 160px;
      //padding: 10px 10px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    div {
      //padding: 10px 10px;
      p:nth-child(1) {
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
        //margin-bottom: 13px;
      }
      p:nth-child(2) {
        margin-top: 2px;
        color: #4d5753;
      }
      p:nth-child(3) {
        margin-bottom: 5px;
        span:nth-child(1) {
          color: red;
        }
        span:nth-child(2) {
          display: inline-block;
          width: 24px;
          height: 24px;
          line-height: 24px;
          margin-left: 20px;
          text-align: center;
          color: #fff;
          background-color: #839398;
          border-radius: 15px;
        }
      }
    }
    .error_btn {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
      span:nth-child(1) {
        width: 50%;
        background: #68bb97;
        padding: 6px 0;
        margin-right: 14px;
        text-align: center;
        //font-size: 15px;
        //line-height: 29px;
        border-radius: 5px;
        color: #fff;
        cursor: pointer;
      }
      span:nth-child(2) {
        width: 50%;
        text-align: center;
        background: #537566;
        padding: 6px 0;
        //font-size: 15px;
        //line-height: 29px;
        color: #fff;
        border-radius: 5px;
        cursor: pointer;
      }
    }
    // 复习按钮
    .review {
      width: 148px;
      padding: 6px 0;
      //height: 34px;
      //line-height: 34px;
      margin: 10px auto 0 auto;
      //margin-top: 10px;
      text-align: center;
      color: #fff;
      border-radius: 20px;
      background-color: #bdd3ce;
      cursor: pointer;
    }
  }
  //.clearfix_new_li{
  //  border-radius: 5px;
  //  background-color: #fff;
  //  padding: 12px;
  //  margin: 16px 16px 0 0;
  //  // 错题描述
  //  .fl_padding{
  //    padding: 10px 10px;
  //  }
  //  div {
  //    //padding: 10px 10px;
  //    p:nth-child(1) {
  //      font-size: 15px;
  //      font-family: PingFangSC-Medium, PingFang SC;
  //      font-weight: 500;
  //      color: #4d5753;
  //      margin-bottom: 13px;
  //    }
  //    p:nth-child(2) {
  //      margin-bottom: 5px;
  //      color: #000;
  //    }
  //    p:nth-child(3) {
  //      margin-bottom: 5px;
  //      color: #000;
  //      span:nth-child(1) {
  //        font-size: 19px;
  //        color: red;
  //      }
  //      span:nth-child(2) {
  //        display: inline-block;
  //        width: 24px;
  //        height: 24px;
  //        line-height: 24px;
  //        margin-left: 20px;
  //        text-align: center;
  //        font-size: 13px;
  //        color: #fff;
  //        background-color: #839398;
  //        border-radius: 15px;
  //      }
  //    }
  //  }
  //  .error_btn {
  //    display: flex;
  //    justify-content: space-between;
  //    span:nth-child(1) {
  //      width: 50%;
  //      background: #68bb97;
  //      margin-right: 14px;
  //      text-align: center;
  //      font-size: 15px;
  //      line-height: 29px;
  //      border-radius: 5px;
  //      color: #fff;
  //      cursor: pointer;
  //    }
  //    span:nth-child(2) {
  //      width: 50%;
  //      text-align: center;
  //      background: #537566;
  //      font-size: 15px;
  //      line-height: 29px;
  //      color: #fff;
  //      border-radius: 5px;
  //      cursor: pointer;
  //    }
  //  }
  //  // 复习按钮
  //  .review {
  //    width: 155px;
  //    height: 34px;
  //    line-height: 34px;
  //    margin: 0 auto;
  //    margin-top: 10px;
  //    text-align: center;
  //    color: #fff;
  //    border-radius: 20px;
  //    background-color: #bdd3ce;
  //    cursor: pointer;
  //  }
  //}
  li:hover {
    box-shadow: 8px 8px 7px grey;
  }
}
// 分页
.ant-pagination {
  text-align: center;
}
.paging {
  text-align: center;
  /deep/.ant-pagination-item:focus,
  .ant-pagination-item:hover {
    border-color: #68bb97;
    border-color: #68bb97;
  }
  /deep/.ant-pagination-item-active a {
    color: #000;
    background-color: #68bb97;
  }
}
</style>
