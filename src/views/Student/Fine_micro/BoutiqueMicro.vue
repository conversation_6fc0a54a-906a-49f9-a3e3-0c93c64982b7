<template>
  <div class="" >
    <div class="switch_top">
      <switch-Multidisciplinary :sortShow="sortShow" widthNum="100%" @handleClick="newHandleClick"></switch-Multidisciplinary>
    </div>
    <div class="flex">
      <!--  更换章节树  -->
      <div
        class="flex_item_shrink "
        style="transition: all ease 0.3s"
        :class="{ openInDialogBorder: openInDialog }"
        :style="{ width: leftWidth + '%' }"
      >
        <left-screening ref="leftScreening" :left-width="leftWidth" @changeWidth="leftWidth = leftWidth == 16 ? 2.3 : 16" @getAllList="getLeftScreening"></left-screening>
      </div>
      <!-- 微课详情 -->
      <div class="fl micro-info clearfix margin_l_15">
        <p class="clearfix micro-catalog">
          <span class="fl">名师微课</span>
          <span class="fg wrong-bank" @click="toFamousTeacher">名师讲堂</span>
        </p>
        <!-- 加载 -->
        <!-- v-if="showMicroLoad" -->
        <div class="example" v-if="showMicroLoad" style="text-align: center; width: 100%">
          <a-spin />
        </div>
        <div v-if="microData?.length > 0">
          <ul>
            <li class="fl" @click="toMicroDetails(item)" v-for="item in microData" :key="item.Id">
              <div
                class="micro-img"
                :style="{
                  backgroundImage: 'url(' + item.CoverUrl + ')',
                  backgroundRepeat: 'no-repeat',
                  backgroundSize: '100% 100%',
                  borderRadius: '5px 5px 0 0',
                }">
                <img class="img" src="@/assets/student/播放.png" alt="" />
              </div>

              <div class="clearfix micro-details">
                <div class="fl micro-describe">
                  <div class="micro-describe_div">
                    <p class="pc_font_size18 font_size20 ipad_font_size22">{{ item.Name.substring(0,6)+'...' }}</p>
                    <div class="xuekeStyle pc_font_size14 font_size16 ipad_font_size18" :style="{'background-color':bgColog[item.SubjectId+'bg'],'color':bgColog[item.SubjectId]}">
                      {{ item.SubjectName }}
                    </div>
                  </div>
                  <p class="pc_font_size14 font_size16 ipad_font_size18" style="color: #4d5753;">授课老师: {{ item.UserName }}</p>
                  <span class="t-p-col pc_font_size14 font_size16 ipad_font_size18" style="display: flex;align-items: center;"><img src="@/assets/student/时长.png" alt="" />{{ item.Duration }}</span>
                  <a-rate v-model="item.Score" disabled></a-rate>
                </div>
                <div class="fg micro-number">
                  <p><img v-if="item.collectionDeleted" src="@/assets/student/收藏.png" alt="" /></p>
                  <p class="t-p-col pc_font_size14 font_size16 ipad_font_size18">{{ item.ClickNum }}人已学</p>
                </div>
              </div>
            </li>
          </ul>
          <div class="paginstion">
            <!-- 分页 -->
            <a-pagination
              class="paging"
              show-quick-jumper
              :default-current="1"
              :total="microNum"
              hideOnSinglePage
              @change="onChange"
              :defaultPageSize="PageRows" />
          </div>
        </div>
        <div v-else class="defaultbg">
          <div>
            <img src="@/assets/defaultImg.png"/>
            <p>哎呀 ~ 暂无相关微课！</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import '@/utils/utils.less'
import switchMultidisciplinary from '@/components/switchMultidisciplinary/switchMultidisciplinary'
import { CorrespondingColor } from '@/utils/subjectCorrespondingColor'
import { debounce } from 'lodash'
import myMixIn from '@/mixIn'
import leftScreening from '@/components/leftScreening/index.vue'
export default {
  components: {
    switchMultidisciplinary,
    leftScreening
  },
  mixins: [myMixIn],
  created () {
    this.id = localStorage.getItem('UserId')
    this.chapterId = this.$route.query.chapterId
    // 获取精品微课信息
    this.initMicroClass(this.id)
    // this.chapterTree()
    this.$store.commit('setHeaderIndex', 3)
    // this.GetPeriodList(this.id)
    // 多学科
    this.bgColog = CorrespondingColor()
  },
  data () {
    return {
      // 加载
      showMicroLoad: true,
      // 用户ID
      id: '',
      // 默认展开指定的数菜单
      expandedKeys: [],
      // 展开父节点
      autoExpandParent: true,
      // 默认选中的子节点
      checkedKeys: [],
      // 设置选中的数节点
      selectedKeys: [],
      treeData: [],
      replaceFields: {
        children: 'Second',
        title: 'ChapterName',
        key: 'ChapterId'
      },
      // 当前页码
      PageIndex: 1,
      // 每页行数
      PageRows: 10,
      // 排序列
      SortField: 'Id',
      // 微课视频数据
      microData: [],
      // 微课视频总数
      microNum: 1,
      chapterId: '',
      // 多学科部分
      sortShow: false,
      bgColog: {},
      // 章节树使用
      openInDialog: false,
      leftWidth: 16,
    }
  },
  watch: {
    $route () {
      window.clearInterval(this.timer)
      this.chapterId = this.$route.query.chapterId
      if (this.$route.fullPath === '/Student/Fine_micro/BoutiqueMicro') {
        // 获取精品微课信息
        this.showMicroLoad = true
        this.initMicroClass(this.id)
        // this.chapterTree()
        this.$store.commit('setHeaderIndex', 3)
        this.$refs.leftScreening.getChild()
      }
    }
  },
  methods: {
    // 章节树组件筛查
    getLeftScreening(opt){
      this.PageIndex = 1
      this.initMicroClass(this.id)
    },
    // 多学科选择学科点击事件
    newHandleClick: debounce(function (val) {
      if (val === '-1') {
        // this.treeData = []
        this.$refs.leftScreening.empty()
      } else {
        // this.chapterTree()
        this.$refs.leftScreening.getChild()
      }
      this.PageIndex = 1
      this.initMicroClass(this.id)
    }, 800),
    // 根据学段id查学科列表
    // GetPeriodList (id) {
    //   this.$uwonhttp
    //     .post('/Period_Subject/Period_Subject/GetSubjectListByStudent', {
    //       userid: id
    //     }).then((res) => {
    //       const newRes = res.data.Data
    //       newRes.unshift({ Name: '全部', Id: '0' })
    //       this.multidisciplinaryList = newRes
    //     })
    // },
    // 章节数
    chapterTree () {
      this.$uwonhttp.post('/Chapter/Chapter/GetTwoLevelChapterList', { userid: this.id, IsWeek: 1 }).then(res => {
        this.treeData = res.data.Data
      })
    },
    // 点击复选框触发
    onCheck (checkedKeys) {
      this.chapterId = checkedKeys.join(',')
      this.PageIndex = 1
      this.initMicroClass(this.id)
    },
    // 点击树节点触发
    onSelect (selectedKeys, info) {
      this.selectedKeys = selectedKeys
    },
    // 分页
    onChange (pageNumber) {
      this.PageIndex = pageNumber
      this.initMicroClass(this.id)
    },
    // 跳转微课详情
    toMicroDetails (item) {
      const LoginSource = localStorage.getItem('LoginSource')
      if(this.$store.state.limitingJobPermissions == 0 && LoginSource == 1){
        this.popupPrompt()
        return false
      }
      const id = item.Id
      const score = item.Score
      const SubjectIdName = {
        Id: item.SubjectId,
        name: item.SubjectName
      }
      this.$router.push({
        path: '/Student/Fine_micro/MicroDetails',
        query: {
          videoId: id,
          score: score,
          SubjectIdName: JSON.stringify(SubjectIdName) // 学科ID
        }
      })
    },
    // 获取精品微课
    initMicroClass (id) {
      const { Ids } = this.$store.state.chapterStorage.ChapterObj
      this.$http
        .post('/Paper/Exam_MicroLesson/GetWeekVideoBy', {
          userId: id,
          PageIndex: this.PageIndex,
          PageRows: this.PageRows,
          // chapterId: this.chapterId
          chapterId: Ids.join(',')
        })
        .then(res => {
          this.showMicroLoad = false
          this.microData = res.Data
          this.microNum = res.Total
        })
    },
    // 跳转名师讲堂
    toFamousTeacher () {
      const LoginSource = localStorage.getItem('LoginSource')
      if(this.$store.state.limitingJobPermissions == 0 && LoginSource == 1){
        this.popupPrompt()
        return false
      }
      this.$router.push({ path: '/Student/Fine_micro/FamousTeacherClass' })
    }
  }
}
</script>

<style lang="less" scoped>
@color: #15335135;
.flex_item_shrink{
  height: 70vh;
}
.newClearfix{
  display: flex;
}
.micro-img {
  height: 170px;
}
.t-p-col {
  color: #b5b5b5;
}
.defaultbg{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  p{
    font-size: 20px;
    font-weight: 500;
    color: #B8B8B8;
    line-height: 28px;
    text-align: center;
  }
}
// 多学科部分
.xuekeStyle{
  padding: 4px 14px;
  border-radius: 100px;
}
.switch_top{
  margin-bottom: 18px;
  padding: 18px 25px;
  background-color: #FFFFFF;
  border-radius: 10px;
}
//.textOverflow {
//  overflow: hidden;
//  white-space: nowrap;
//  text-overflow: ellipsis;
//  word-break: break-all;
//}
//.limit-wid {
//  width: 200px;
//}
// 缺省
.week-lack {
  margin-top: 100px;
  text-align: center;
  color: #ccc;
}
.clearfix {
  //   margin-left: 35px;
  // padding: 0 6px;
}
// 目录
.micro-catalog {
  // margin-right: 0px;
  width:100%;
  padding: 12px;
  background-color: #fff;
  border-radius: 5px;
}
.task-catalog {
  //min-width: 280px;
  //margin-right: 28px;
  width: 290px;
  height: 90vh;
  padding: 0 10px;
  font-size: 16px;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space:nowrap;
  background-color: #FFFFFF;
  /deep/.ant-tree-title {
    font-size: 16px;
    font-weight: 700;
  }
  /deep/.ant-tree-title {
    font-size: 20px;
    font-weight: 700;
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_close .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-tree-switcher-icon, :root .ant-tree li span.ant-tree-switcher.ant-tree-switcher_open .ant-select-switcher-icon {
    font-size: 0.0625rem;
    transform: scale(1.5);
  }
  /deep/.ant-tree li {
    margin: 0;
    padding: 10px 0;
    white-space: nowrap;
    list-style: none;
    outline: 0;
  }
  p {
    //height: 40px;
    //line-height: 40px;
    //font-size: 18px;
    padding: 6px;
    //font-weight: 700;
    text-align: center;
    border-radius: 5px;
    color: #fff;
    background-color: #68bb97;
  }
}
// 微课内容
.micro-info {
  width: 82%;
  //float:right;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  margin-right: 8px;
  span:nth-child(1) {
    font-size: 20px;
    color: #4d5753;
  }
  .wrong-bank {
    font-size: 20px;
    color: #4d5753;
    cursor: pointer;
  }
  .wrong-bank:hover {
    color: #68bb97;
  }
  .header-line {
    margin-top: 38px;
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
  }
  li {
    position: relative;
    min-height: 269px;
    margin-top: 18px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 5px;
    background-color: #fff;
    cursor: pointer;
    .img {
      position: absolute;
      top: 29%;
      left: 50%;
      transform: translateX(-50%);
      font-size: 55px;
    }
    .line {
      height: 1px;
      margin-top: 126px;
      background-color: #000;
    }
    // 微课内容
    .micro-details {
      width:292px;
      height: 98px;
      padding: 0px 15px;
      background-color: #fff;
      border-radius: 0px 0px 5px 5px;
      // 微课描述
      .micro-describe {
        width: 100%;
        //width: 200px;
        .micro-describe_div{
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          p{
            margin: 9px 0;
            font-weight: 700;
          }
        }
        //p{
        //  //font-size: 12px;
        //  font-weight: 700;
        //}
      }
      // 微课信息
      .micro-number {
        p:nth-child(1) {
          text-align: right;
          margin: 9px 0;
          font-size: 12px;
        }
        p:nth-child(2) {
          margin-top: 40px;
          font-size: 10px;
        }
      }
      img{
        width: 18px;
        height: 18px;
      }
    }
  }
  //  li:hover {
  //   box-shadow: 8px 8px 7px grey;
  // }
  li:nth-child(3n) {
    // margin-right: 0;
  }
  // 播放时间
  .play-time {
    position: absolute;
    top: 98px;
    right: 24px;
  }
}
.paginstion {
  // width: 400px;
  margin: auto;
  // 分页
  .ant-pagination {
    text-align: center;
  }
  .paging {
    text-align: center;
    /deep/.ant-pagination-item:focus,
    .ant-pagination-item:hover {
      border-color: #68bb97;
    }
    /deep/.ant-pagination-item-active a {
      color: #000;
      background-color: #68bb97;
    }
  }
}
</style>
