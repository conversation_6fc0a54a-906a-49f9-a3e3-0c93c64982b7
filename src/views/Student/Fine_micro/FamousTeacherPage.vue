<template>
  <div class="teacher-micro whole-mr clearfix" style="margin:0 auto;">
    <p class="famous-teacher pc_font_size18 font_size20 ipad_font_size22">名师页面</p>
    <!-- 微课基本信息 -->
    <div class="micro">
      <!-- 个人信息 -->
      <div class="info">
        <a-avatar :src="teacherData.Photo" :size="64" icon="user" />
        <div class="info-content">
          <p class="pc_font_size20 font_size22 ipad_font_size24">{{ teacherData.RealName }}</p>
          <p class="pc_font_size16 font_size18 ipad_font_size20">{{ teacherData.SchoolName }}</p>
        </div>
      </div>
      <!-- 信息详情 -->
      <div class="info-details">
        <ul>
          <li>
            <p>{{ teacherData.FocusCount }}</p>
            <p>粉丝</p>
          </li>
          <li>
            <p>{{ teacherData.CurriculumCount }}</p>
            <p>课程</p>
          </li>
          <li>
            <p>{{ clickNum }}</p>
            <p>播放量</p>
          </li>
        </ul>
      </div>
      <!-- 关注 -->
      <div class="follow-tea" @click="follow()">
        <!-- <a-icon type="plus" /> -->
        {{ followhe }}
      </div>
    </div>
    <p class="he-micro-name">Ta的微课</p>
    <!-- 微课信息详情 -->
    <div class="clearfix all-mrico-info">
      <div class="micro-details fl" v-for="item in teacherMicro" :key="item.Id">
        <!-- 视频 -->
        <div class="video" @click="toVideoDetails(item.Id)" :style="{ backgroundImage:'url(' + item.CoverUrl + ')',backgroundRepeat:'no-repeat',backgroundSize:'100% 100%' }">
          <a-icon type="play-circle" style="fontSize: 42px;color:#fff;" />
        </div>
        <!-- 右侧详情 -->
        <div class="video-details">
          <div>
            <p class="written-words pc_font_size18 font_size20 ipad_font_size22">{{ item.ChapterName }}</p>
            <p class="written-words pc_font_size14 font_size16 ipad_font_size18">{{ item.Name }}</p>
            <a-rate v-model="value" disabled />
            <p style="border-bottom: 1px solid #ccc;padding-bottom: 5px" class="pc_font_size14 font_size16 ipad_font_size18">
              <span class="f-r"><img style="vertical-align: middle;" src="@/assets/teacher/时长.png" alt="">{{ item.Duration }}</span>
              <span><img style="vertical-align: middle;" src="@/assets/teacher/播放量.png" alt="">{{ item.ClickNum }}次</span>
            </p>
            <p>
              <span class="fg time-name pc_font_size14 font_size16 ipad_font_size18">{{ item.CreateTime }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="micro-details fl">
      视频
      <div class="video">
        <a-icon type="play-circle" style="fontSize: 42px" />
      </div>
      右侧详情
      <div class="video-details">
        <div>
          <p>一.10以内的数（比一比）</p>
          <p>分拆为加与减</p>
          <p>时长：05:36</p>
          <p> <a-icon type="play-circle" />3652次</p>
          <p>发布时间：2019-05-23</p>
        </div>
        <p class="score">课程评分：</p>
        <span class="score-star">
          <a-rate v-model="value" />
        </span>
      </div>
    </div> -->

    <!-- 分页 -->
    <!-- <div class="paging">
      <a-pagination :defaultCurrent="1" :total="total" @change="onChange" hideOnSinglePage />
    </div> -->
  </div>
</template>

<script>
import '@/utils/utils.less'
export default {
  created() {
    this.userId = localStorage.getItem('UserId')
    this.teacherId = this.$route.query.teacherId
    // 获取老师基本信息
    this.getTeacherData()
    // 获取老师微课信息
    this.getTeacherMicroData()
  },
  data() {
    return {
      value: 1,
      userId: '',
      teacherId: '',
      // 老师基本信息
      teacherData: {},
      // 老师的微课信息
      teacherMicro: [],
      // 播放量
      clickNum: '',
      // 关注
      followhe: '',
      // 微课总数
      total: 0
    }
  },
  watch: {
    $route() {
      const Change = window.location.href.split('?')[1]
      this.teacherId = this.$route.query.teacherId
      // console.log(Change)
      if (this.$route.fullPath === '/Student/Fine_micro/FamousTeacherPage?' + Change) {
        // 获取老师基本信息
        this.getTeacherData()
        // 获取老师微课信息
        this.getTeacherMicroData()
      }
    }
  },
  methods: {
    // 页码，每页条数
    onChange(page, pagesize) {},
    // 获取老师基本信息
    getTeacherData() {
      this.$http
        .post('/Focus/Focus_Collection/GetMyTeacherInfo', {
          userId: this.teacherId
        })
        .then(res => {
          this.teacherData = res.Data
        })
    },
    // 获取老师的微课信息
    getTeacherMicroData() {
      this.$http
        .post('/Paper/Exam_MicroLesson/GetWeekVideoBy', {
          userId: this.userId,
          teacherId: this.teacherId
        })
        .then(res => {
          this.teacherMicro = res.Data
          this.clickNum = res.Data[0].ClickNum
          this.total = res.Total
          if (this.teacherMicro[0].focusDeleted === true) {
            this.followhe = '取消关注'
          } else {
            this.followhe = '关注'
          }
        })
    },
    // 关注
    follow() {
      this.$http
        .post('/Focus/Focus_Collection/AddDataColletion', {
          CreatorId: this.teacherId
        })
        .then(res => {
          if (this.followhe === '关注') {
            this.followhe = '取消关注'
          } else {
            this.followhe = '关注'
          }
          this.getTeacherData()
        })
    },
    // 跳转视频详情
    toVideoDetails(videoId) {
      this.$router.push({
        path: '/Student/Fine_micro/MicroDetails',
        query: {
          videoId
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.teacher-micro {
  // div:nth-child(odd) {
  //   margin-right: 0;
  // }
}
.all-mrico-info {
  // display: flex;
  // justify-content: space-between;
  // flex-wrap: wrap;
}
.famous-teacher {
  //height: 38px;
  //line-height: 38px;
  //padding-left: 15px;
  padding: 10px 20px;
  font-weight: bold;
  //font-size: 16px;
  background-color: #fff;
  border-radius: 5px;
}
// 文字
.written-words {
  // width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.f-r {
  margin-right: 25px;
}
.time-name {
  font-size: 10px;
  color: #b5b5b5;
  margin-top: 9px;
}
.he-micro-name {
  font-size: 20px;
  margin-bottom: 10px;
  color: #4d5753;
}
// 微课基本信息
.micro {
  margin-top: 20px;
  padding-top: 40px;
  padding-left: 50px;
  position: relative;
  height: 170px;
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 5px;
}
// 个人信息
.info {
  float: left;
  position: relative;
  .info-content {
    float: right;
    margin-top: 8px;
    margin-left: 10px;
    // position: absolute;
    // left: 140px;
    // top: 50px;
    //p:nth-child(1) {
    //  font-weight: 700;
    //  font-size: 20px;
    //}
  }
}
// 信息详情
.info-details {
  position: relative;
  ul {
    position: absolute;
    right: 253px;
    // top: 46px;
    li {
      float: left;
      margin-right: 70px;
      text-align: center;
      p:nth-child(1) {
        font-size: 26px;
      }
      p:nth-child(2){
        font-size: 24px;
      }
    }
  }
}
// 关注
.follow-tea {
  position: absolute;
  right: 121px;
  // top: 66px;
  width: 112px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  // font-size: 20px;
  // font-weight: 700;
  border-radius: 15px;
  color: #fff;
  background-color: #68bb97;
  cursor: pointer;
}
// 微课信息详情
.micro-details {
  position: relative;
  width: 290px;
  margin-right: 12px;
  margin-bottom: 75px;
  background-color: #fff;
  border-radius: 5px;
  .video {
    // width: 320px;
    height: 200px;
    line-height: 215px;
    text-align: center;
    border-radius: 5px;
    border: 1px solid #ccc;
    cursor: pointer;
  }
  .video-details {
    // position: absolute;
    // left: 344px;
    // top: 8px;
    padding: 5px 10px;
    div {
      p:nth-child(2) {
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 8px;
      }
      // p:nth-child(4) {
      //   // margin-bottom: 29px;
      // }
      p:nth-child(5) {
        margin-bottom: 0;
        font-size: 12px;
      }
    }
    .score {
      position: absolute;
      left: 158px;
      top: 85px;
      width: 71px;
    }
    .score-star {
      position: absolute;
      left: 144px;
      bottom: 22px;
      width: 140px;
    }
  }
}
.paging {
  text-align: center;
  /deep/.ant-pagination-item:focus,
  .ant-pagination-item:hover {
    border-color: #68bb97;
    border-color: #68bb97;
  }
  /deep/.ant-pagination-item-active a {
    color: #000;
    background-color: #68bb97;
  }
}
</style>
