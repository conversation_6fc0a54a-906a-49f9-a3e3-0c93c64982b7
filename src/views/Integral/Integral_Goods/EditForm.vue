﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form">
        <a-form-item label="TypeId" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['TypeId', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Integral" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Integral', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Name" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Name', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Pic" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Pic', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Surplus" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Surplus', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Explain" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Explain', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Fee" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Fee', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Reference" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Reference', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="BuyPrice" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['BuyPrice', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Status" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Status', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="Recommend" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['Recommend', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
        <a-form-item label="OrderId" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-input v-decorator="['OrderId', { rules: [{ required: true, message: '必填' }] }]" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      entity: {},
      title: ''
    }
  },
  methods: {
    openForm(id, title) {
      //参数赋值
      this.title = title || '编辑表单'
      this.loading = true

      //组件初始化
      this.init()

      //编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()

          this.$http.post('/Integral/Integral_Goods/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.visible = true
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())

          this.loading = true
          this.$http.post('/Integral/Integral_Goods/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    }
  }
}
</script>
