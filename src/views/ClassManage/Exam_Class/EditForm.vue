﻿<template>
  <a-modal
    :title="title"
    width="40%"
    :maskClosable="false"
    :visible="visible"
    :confirmLoading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form">
        <a-form-item
          label="说明"
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          style="color:#ccc"
        >每个班级每年只能创建一次</a-form-item>
        <a-form-item label="学校" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            allowClear
            showSearch
            treeNodeFilterProp="title"
            style="width: 300px"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="SchoolTreeData"
            @change="SchoolChange"
            placeholder="选择学校"
            treeDefaultExpandAll
            v-decorator="['SchoolId', { rules: [{ required: true }] }]"
          ></a-tree-select>
        </a-form-item>

        <a-form-item label="班级名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            allowClear
            showSearch
            treeNodeFilterProp="title"
            style="width: 300px"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="Exam_ClassTreeData"
            placeholder="班级名称"
            treeDefaultExpandAll
            v-decorator="['SchoolClassSettingId', { rules: [{ required: true }] }]"
          ></a-tree-select>
        </a-form-item>

        <a-form-item label="班主任" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            allowClear
            showSearch
            treeNodeFilterProp="title"
            style="width: 300px"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="UserTreeData"
            placeholder="班主任"
            treeDefaultExpandAll
            v-decorator="['ClassAdviserId', { rules: [{ required: false }] }]"
          ></a-tree-select>
        </a-form-item>

        <a-form-item label="选择添加任课老师" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-tree-select
            allowClear
            showSearch
            treeNodeFilterProp="title"
            style="width: 300px"
            @change="TeacherChange"
            :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
            :treeData="TeachersTreeData"
            placeholder="任课老师"
            treeDefaultExpandAll
            v-decorator="['TeacherId', { rules: [{ required: false }] }]"
          ></a-tree-select>
        </a-form-item>

        <a-form-item label="任课老师" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-list bordered :dataSource="Teachers">
            <a-list-item slot="renderItem" slot-scope="item">
              {{ item.RealName }}
              <a slot="actions" v-on:click="DeleteTeacher(item.Id)">删除</a>
            </a-list-item>
          </a-list>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  props: {
    parentObj: Object
  },
  data() {
    return {
      form: this.$form.createForm(this),
      labelCol: { xs: { span: 24 }, sm: { span: 7 } },
      wrapperCol: { xs: { span: 24 }, sm: { span: 13 } },
      visible: false,
      loading: false,
      formFields: {},
      Teachers: [],
      TeachersTreeData: [],
      entity: {},
      SchoolTreeData: [],
      Exam_ClassTreeData: [],
      UserTreeData: [],
      ClassId :'',
      title: ''
    }
  },
  methods: {
    openForm(id, title) {
      //参数赋值
      this.title = title || '编辑表单'
      this.loading = true
      this.ClassId=''
      //组件初始化
      this.init()

      //编辑赋值
      if (id) {
        this.$nextTick(() => {
          this.formFields = this.form.getFieldsValue()

          this.$http.post('/ClassManage/Exam_Class/GetTheData', { id: id }).then(resJson => {
            this.entity = resJson.Data
            this.ClassId = resJson.Data.Id
            var setData = {}
            Object.keys(this.formFields).forEach(item => {
              setData[item] = this.entity[item]
            })
            this.form.setFieldsValue(setData)
            //编辑初始化
            this.BySchoolId(resJson.Data.SchoolId)
            this.GetTeachersByClassId(this.entity.Id)
            this.loading = false
          })
        })
      } else {
        this.loading = false
      }
    },
    init() {
      this.entity = {}
      this.form.resetFields()
      this.Teachers = []
      this.visible = true

      this.$http.post('/School/Exam_School/GetTreeDataList').then(resJson => {
        if (resJson.Success) {
          this.SchoolTreeData = resJson.Data
        }
      })
    },
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        //校验成功
        if (!errors) {
          this.entity = Object.assign(this.entity, this.form.getFieldsValue())

          this.loading = true
          this.$http.post('/ClassManage/Exam_Class/SaveData', this.entity).then(resJson => {
            this.loading = false

            if (resJson.Success) {
              this.$message.success('操作成功!')
              //this.visible = false

              this.parentObj.getDataList()
            } else {
              this.$message.error(resJson.Msg)
            }
          })
        }
      })
    },
    handleCancel() {
      this.visible = false
    },
    SchoolChange(value, label, extra) {
      this.Exam_ClassTreeData = []
      this.UserTreeData = []

      this.BySchoolId(value)
    },
    BySchoolId(schoolId) {
      this.$http
        .post('/School/Exam_SchoolClassSetting/GetTreeDataListBySchoolId', { schoolId: schoolId })
        .then(resJson => {
          if (resJson.Success) {
            this.Exam_ClassTreeData = resJson.Data
          }
        })

      this.$http.post('/Base_Manage/Base_User/GetTreeDataListBySchoolId', { schoolId: schoolId }).then(resJson => {
        if (resJson.Success) {
          this.UserTreeData = resJson.Data
          this.TeachersTreeData = resJson.Data
        }
      })
    },
    TeacherChange(value, label, extra) {
      this.$http
        .post('/Mappings/Exam_TeacherClassMapping/SaveData', { TeacherUserId: value, ClassId: this.ClassId })
        .then(resJson => {
          if (resJson.Success) {
            this.$message.success('操作成功!')
            this.GetTeachersByClassId(this.ClassId)
          } else {
            this.$message.error(resJson.Msg)
          }
        })
    },
    DeleteTeacher(userid) {
      this.$http
        .post('/Mappings/Exam_TeacherClassMapping/DeleteData', { userId: userid, classId: this.ClassId })
        .then(resJson => {
          if (resJson.Success) {
            this.GetTeachersByClassId(this.ClassId)
          }
        })
    },
    GetTeachersByClassId(classId) {
      this.$http.post('/Base_Manage/Base_User/GetTeachersByClassId', { classId: classId }).then(resJson => {
        if (resJson.Success) {
          this.Teachers = resJson.Data
        } else {
          this.$message.error(resJson.Msg)
        }
      })
    }
  }
}
</script>
