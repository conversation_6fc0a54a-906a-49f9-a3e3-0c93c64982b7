@font_38: 38px;
@font_35: 35px;
@font_34: 34px;
@font_30: 30px;
@font_28: 28px;
@font_26: 26px;
@font_24: 24px;
@font_22: 22px;
@font_20: 20px;
@font_18: 18px;
@font_16: 16px;
@font_14: 14px;
@font_12: 12px;

@pad_6: 6px;
@pad_8: 8px;
@pad_10: 10px;
@pad_12: 12px;
@pad_14: 14px;

.DIV_BOTTOM_DIV::after{
  content: " ";
  display: block;
  height: 7vh;
}

@media screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape){
  .el-message-box__title {
    font-size: @font_26 !important;
  }
  .el-message-box__message{
    font-size: @font_22 !important;
  }
  .el-message-box__btns{
    .el-button{
      font-size: @font_18 !important;
    }
  }
  .ipad_font_size38{
    font-size: @font_38 !important;
  }
  .ipad_font_size35{
    font-size: @font_35 !important;
  }
  .ipad_font_size30{
    font-size: @font_30 !important;
  }
  .ipad_font_size28{
    font-size: @font_28 !important;
  }
  .ipad_font_size26{
    font-size: @font_26 !important;
  }
  .ipad_font_size24{
    font-size: @font_24 !important;
  }
  .ipad_font_size22{
    font-size: @font_22 !important;
  }
  .ipad_font_size20{
    font-size: @font_20 !important;
  }
  .ipad_font_size18{
    font-size: @font_18 !important;
  }
  .ipad_font_size16{
    font-size: @font_16 !important;
  }
  .ipad_font_size14{
    font-size: @font_14 !important;
  }
  .ipad_img300{
    width: 220px;
    height: 220px;
  }
  // AI会话界面高度适配
  .ipad_img_cont_height{
    height: 62vh;
  }
  .ipad_cont_height{
    height: 68vh;
  }
  .ipad_all_height{
    height: 86vh;
  }

}

@media screen and (min-device-width: 1030px) and (max-device-width: 1360px) and (orientation: landscape) {
  .el-message-box__title {
    font-size: @font_26 !important;
  }
  .el-message-box__message{
    font-size: @font_22 !important;
  }
  .el-message-box__btns{
    .el-button{
      font-size: @font_18 !important;
    }
  }
  .font_size34{
    font-size: @font_34 !important;
  }
  .font_size30{
    font-size: @font_30 !important;
  }
  .font_size28{
    font-size: @font_28 !important;
  }
  .font_size26{
    font-size: @font_26 !important;
  }
  .font_size24{
    font-size: @font_24 !important;
  }
  .font_size22{
    font-size: @font_22 !important;
  }
  .font_size20{
    font-size: @font_20 !important;
  }
  .font_size18{
    font-size: @font_18 !important;
  }
  .font_size16{
    font-size: @font_16 !important;
  }
  .font_size14{
    font-size: @font_14 !important;
  }
  .font_size12{
    font-size: @font_12 !important;
  }
  .img180{
    width: 180px;
    height: 180px;
  }
}
@media screen and (min-device-width: 1440px) and (orientation: landscape){
  .pc_font_size26{
    font-size: @font_26 !important;
  }
  .pc_font_size24{
    font-size: @font_24 !important;
  }
  .pc_font_size22{
    font-size: @font_22 !important;
  }
  .pc_font_size20{
    font-size: @font_20 !important;
  }
  .pc_font_size18{
    font-size: @font_18 !important;
  }
  .pc_font_size16{
    font-size: @font_16 !important;
  }
  .pc_font_size14{
    font-size: @font_14 !important;
  }
  .pc_font_size12{
    font-size: @font_12 !important;
  }
  .pc_img_cont_height{
    height: 64vh;
  }
  .pc_cont_height{
    height: 70vh;
  }
  .pc_all_height{
    height: 90vh;
  }
}