<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
  <meta charset="utf-8">
  <!-- <meta HTTP-EQUIV="REFRESH" CONTENT="1"> -->
  <meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="<%= BASE_URL %>徐汇logo.png">
<!-- 暂时引入 -->
  <!-- 打字机样式 -->
  <link rel="stylesheet" href="vue-typed-js/dist/vue-typed-js.css"/>
<!-- markdown格式样式 -->
<!--  <link rel="stylesheet" href="https://cdn.jsdelivr.net/github-markdown-css/2.2.1/github-markdown.css"/>-->
  <!-- markdown公式解析器 -->
  <link rel="stylesheet" href="./katex/katex.min.css" crossorigin="anonymous">
  <script defer src="./katex/katex.min.js" crossorigin="anonymous"></script>
<!--  <script defer src="./katex/contrib/auto-render.min.js" crossorigin="anonymous">-->
<!--    window.onload= function () {-->
<!--      renderMathInElement(document.body, {-->
<!--        delimiters: [-->
<!--          { left: "$$", right: "$$", display: true },-->
<!--          { left: "$", right: "$", display: false },-->
<!--          { left: "\\(", right: "\\)", display: false },-->
<!--          { left: "\\[", right: "\\]", display: true }-->
<!--        ],-->
<!--        ignoredClasses: ["gist"]-->
<!--      })-->
<!--    }-->
<!--  </script>-->
  <!-- <script src="//cdn.bootcss.com/jquery/3.3.1/jquery.min.js"></script> -->
  <script src="https://uwoo.obs.cn-east-2.myhuaweicloud.com/jquery.min.js"></script>
  <!-- <link rel="dns-prefetch" href="//cdn.bootcss.com" />
  <script type="text/javascript" src="//cdn.bootcss.com/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script> -->
  <!-- <script src="/public/static/lib/katex.js"></script>
  <script src="/public/static/lib/katex.css"></script> -->
<!--  <script src="./katex.js"></script>-->
<!--  <script src="./katex.css"></script>-->

  <title>
  </title>
  <style>
    #loading-mask {
      position: fixed;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      background: #fff;
      user-select: none;
      z-index: 9999;
      overflow: hidden
    }

    .loading-wrapper {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -100%)
    }

    .loading-dot {
      animation: antRotate 1.2s infinite linear;
      transform: rotate(45deg);
      position: relative;
      display: inline-block;
      font-size: 64px;
      width: 64px;
      height: 64px;
      box-sizing: border-box
    }

    .loading-dot i {
      width: 22px;
      height: 22px;
      position: absolute;
      display: block;
      background-color: #1890ff;
      border-radius: 100%;
      transform: scale(.75);
      transform-origin: 50% 50%;
      opacity: .3;
      animation: antSpinMove 1s infinite linear alternate
    }

    .loading-dot i:nth-child(1) {
      top: 0;
      left: 0
    }

    .loading-dot i:nth-child(2) {
      top: 0;
      right: 0;
      -webkit-animation-delay: .4s;
      animation-delay: .4s
    }

    .loading-dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      -webkit-animation-delay: .8s;
      animation-delay: .8s
    }

    .loading-dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      -webkit-animation-delay: 1.2s;
      animation-delay: 1.2s
    }

    @keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
      }
    }

    @-webkit-keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
      }
    }

    @keyframes antSpinMove {
      to {
        opacity: 1
      }
    }

    @-webkit-keyframes antSpinMove {
      to {
        opacity: 1
      }
    }
  </style>

  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.10.1/dist/katex.min.css">
  <script defer src="https://cdn.jsdelivr.net/npm/katex@0.10.1/dist/katex.min.js"></script> -->

  <script>
    var katex_config = {
      delimiters: [{
          left: "$$",
          right: "$$",
          display: true
        },
        {
          left: '$',
          right: '$',
          display: false
        },
        {
          left: "\\(",
          right: "\\)",
          display: false
        },
        {
          left: "\\[",
          right: "\\]",
          display: true
        },
      ]

    };
  </script>
  <!-- <script defer src="https://cdn.bootcss.com/KaTeX/0.11.1/contrib/auto-render.min.js"
    onload="renderMathInElement(document.body,katex_config)"></script> -->

</head>

<body>
  <noscript>
    <strong>We're sorry but vue-antd-pro doesn't work properly without JavaScript enabled. Please enable it to
      continue.</strong>
  </noscript>
  <div id="app">
    <div id="loading-mask">
      <div class="loading-wrapper">
        <span class="loading-dot loading-dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
    </div>
  </div>


  <!-- built files will be auto injected -->
  <!-- <script src="https://gw.alipayobjects.com/os/antv/pkg/_antv.data-set-0.10.1/dist/data-set.min.js"></script> -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.2/MathJax.js?config=TeX-AMS_HTML"></script> -->
  <!-- <script type="text/javascript" src="//cdn.bootcss.com/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script> -->
  <!-- <script src="https://cdn.bootcss.com/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script> -->


  <!--
  <script type="text/javascript"
    src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-MML-AM_CHTML"></script> -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-MML-AM_CHTML" />
  </script> -->
  <!-- <script type="text/javascript" src="https://api.xkw.com/mathjax/MathJax.js?config=TeX-MML-AM_CHTML"></script> -->

  <script>
    window.onload = function () {
      var abc = 'hplogo'
      var address = window.location.href.split('/')[2]
      if (address === 'xh.eduwon.cn') {
        document.querySelector("link[rel*='icon']").href = 'http://xh.eduwon.cn/徐汇logo.png'
      } else if (address === 'hp.eduwon.cn') {
        document.querySelector("link[rel*='icon']").href = 'http://hp.eduwon.cn/hplogo.png'
      } else if (address === 'pt.eduwon.cn') {
        document.querySelector("link[rel*='icon']").href = 'http://pt.eduwon.cn/pt.png'
      } else if (address === 'sj.eduwon.cn') {
        document.querySelector("link[rel*='icon']").href = 'http://sj.eduwon.cn/sj-sx.png'
      } else if (address === 'yp.eduwon.cn') {
        document.querySelector("link[rel*='icon']").href = 'http://yp.eduwon.cn/yp-logo.png'
      } else {
        // document.querySelector("link[rel*='icon']").href = 'http://localhost:5001/logo.png'
      }
    }

  </script>
</body>

</html>