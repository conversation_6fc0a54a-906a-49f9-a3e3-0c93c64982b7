tinymce.PluginManager.add("mathjax",function(b,a){let mathjaxClassName=b.settings.mathjax.className||"math-tex";let mathjaxTempClassName=mathjaxClassName+"-original";let mathjaxSymbols=b.settings.mathjax.symbols||{start:"\\(",end:"\\)"};let mathjaxUrl=b.settings.mathjax.lib||null;let mathjaxConfigUrl=(b.settings.mathjax.configUrl||a+"/config.js")+"?class="+mathjaxTempClassName;let mathjaxScripts=[mathjaxConfigUrl];if(mathjaxUrl){mathjaxScripts.push(mathjaxUrl)}b.on("init",function(){let scripts=b.getDoc().getElementsByTagName("script");for(let i=0;i<mathjaxScripts.length;i++){let id=b.dom.uniqueId();let script=b.dom.create("script",{id:id,type:"text/javascript",src:mathjaxScripts[i]});let found=false;for(let j=0;j<scripts.length;j++){if(scripts[j].src==script.src){found=true;break}}if(!found){b.getDoc().getElementsByTagName("head")[0].appendChild(script)}}});b.on("GetContent",function(c){let div=b.dom.create("div");div.innerHTML=c.content;let elements=div.querySelectorAll("."+mathjaxClassName);for(let i=0;i<elements.length;i++){let children=elements[i].querySelectorAll("span");for(let j=0;j<children.length;j++){children[j].remove()}let latex=elements[i].getAttribute("data-latex");elements[i].removeAttribute("contenteditable");elements[i].removeAttribute("style");elements[i].removeAttribute("data-latex");elements[i].innerHTML=latex}c.content=div.innerHTML});let checkElement=function(c){if(c.childNodes.length!=2){c.setAttribute("contenteditable",false);c.style.cursor="pointer";let latex=c.getAttribute("data-latex")||c.innerHTML;c.setAttribute("data-latex",latex);c.innerHTML="";let math=b.dom.create("span");math.innerHTML=latex;math.classList.add(mathjaxTempClassName);c.appendChild(math);let dummy=b.dom.create("span");dummy.classList.add("dummy");dummy.innerHTML="dummy";dummy.setAttribute("hidden","hidden");c.appendChild(dummy)}};b.on("BeforeSetContent",function(c){let div=b.dom.create("div");div.innerHTML=c.content;let elements=div.querySelectorAll("."+mathjaxClassName);for(let i=0;i<elements.length;i++){checkElement(elements[i])}c.content=div.innerHTML});b.on("SetContent",function(c){if(b.getDoc().defaultView.MathJax){b.getDoc().defaultView.MathJax.startup.getComponents();b.getDoc().defaultView.MathJax.typeset()}});b.ui.registry.addToggleButton("mathjax",{text:"Σ",tooltip:"Mathjax",onAction:function(){let selected=b.selection.getNode();let target=undefined;if(selected.classList.contains(mathjaxClassName)){target=selected}openMathjaxEditor(target)},onSetup:function(c){return b.selection.selectorChangedWithUnbind("."+mathjaxClassName,c.setActive).unbind}});b.on("click",function(c){let closest=c.target.closest("."+mathjaxClassName);if(closest){openMathjaxEditor(closest)}});let openMathjaxEditor=function(d){let mathjaxId=b.dom.uniqueId();let latex="";if(d){latex_attribute=d.getAttribute("data-latex");if(latex_attribute.length>=(mathjaxSymbols.start+mathjaxSymbols.end).length){latex=latex_attribute.substr(mathjaxSymbols.start.length,latex_attribute.length-(mathjaxSymbols.start+mathjaxSymbols.end).length)}}b.windowManager.open({title:"Mathjax",width:600,height:300,body:{type:"panel",items:[{type:"textarea",name:"title",label:"LaTex"},{type:"htmlpanel",html:'<div style="text-align:right"><a href="https://wikibooks.org/wiki/LaTeX/Mathematics" target="_blank" style="font-size:small">LaTex</a></div>'},{type:"htmlpanel",html:'<iframe id="'+mathjaxId+'" style="width: 100%; min-height: 50px;"></iframe>'}]},buttons:[{type:"submit",text:"OK"}],onSubmit:function c(e){let value=e.getData().title.trim();if(d){d.innerHTML="";d.setAttribute("data-latex",getMathText(value));checkElement(d)}else{let newElement=b.getDoc().createElement("span");newElement.innerHTML=getMathText(value);newElement.classList.add(mathjaxClassName);checkElement(newElement);b.insertContent(newElement.outerHTML)}b.getDoc().defaultView.MathJax.startup.getComponents();b.getDoc().defaultView.MathJax.typeset();e.close()},onChange:function(e){var f=e.getData().title.trim();if(f!=latex){refreshDialogMathjax(f,document.getElementById(mathjaxId));latex=f}},initialData:{title:latex}});let iframe=document.getElementById(mathjaxId);let iframeWindow=iframe.contentWindow||iframe.contentDocument.document||iframe.contentDocument;let iframeDocument=iframeWindow.document;let iframeHead=iframeDocument.getElementsByTagName("head")[0];let iframeBody=iframeDocument.getElementsByTagName("body")[0];let getMathText=function(f,e){if(!e){e=mathjaxSymbols}return e.start+" "+f+" "+e.end};let refreshDialogMathjax=function(e){let MathJax=iframeWindow.MathJax;let div=iframeBody.querySelector("div");if(!div){div=iframeDocument.createElement("div");div.classList.add(mathjaxTempClassName);iframeBody.appendChild(div)}div.innerHTML=getMathText(e,{start:"$$",end:"$$"});if(MathJax&&MathJax.startup){MathJax.startup.getComponents();MathJax.typeset()}};refreshDialogMathjax(latex);for(let i=0;i<mathjaxScripts.length;i++){let node=iframeWindow.document.createElement("script");node.src=mathjaxScripts[i];node.type="text/javascript";node.async=false;node.charset="utf-8";iframeHead.appendChild(node)}}});