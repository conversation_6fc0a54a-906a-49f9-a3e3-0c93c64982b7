/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.5.1 (2020-10-01)
 */
!function(){"use strict";var e,t,n,r,m=function(e){var t=e;return{get:function(){return t},set:function(e){t=e}}},o=tinymce.util.Tools.resolve("tinymce.PluginManager"),a=function(e){return function(){return e}},i=a(!1),s=a(!0),u=function(){return l},l=(e=function(e){return e.isNone()},{fold:function(e,t){return e()},is:i,isSome:i,isNone:s,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:a(null),getOrUndefined:a(undefined),or:n,orThunk:t,map:u,each:function(){},bind:u,exists:i,forall:s,filter:u,equals:e,equals_:e,toArray:function(){return[]},toString:a("none()")}),c=function(n){var e=a(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:s,isNone:i,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return c(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(i,function(e){return t(n,e)})}};return o},p={some:c,none:u,from:function(e){return null===e||e===undefined?l:c(e)}},f=(r="function",function(e){return typeof e===r}),d=Array.prototype.slice,g=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var a=e[o];r[o]=t(a,o)}return r},v=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},h=f(Array.from)?Array.from:function(e){return d.call(e)},y=tinymce.util.Tools.resolve("tinymce.Env"),b=tinymce.util.Tools.resolve("tinymce.util.Delay"),x=tinymce.util.Tools.resolve("tinymce.util.Promise"),P=tinymce.util.Tools.resolve("tinymce.util.VK"),w=function(e,t){return e.fire("PastePlainTextToggle",{state:t})},_=function(e){return e.getParam("paste_data_images",!1)},T=function(e){return e.getParam("paste_retain_style_properties")},C=function(e){return e.getParam("validate")},D=function(e){return e.getParam("paste_data_images",!1,"boolean")},k="x-tinymce/html",S="\x3c!-- "+k+" --\x3e",O=function(e){return-1!==e.indexOf(S)},R=tinymce.util.Tools.resolve("tinymce.html.Entities"),A=tinymce.util.Tools.resolve("tinymce.util.Tools"),I=function(e,t,n){var r=e.split(/\n\n/),o=function(e,t){var n,r=[],o="<"+e;if("object"==typeof t){for(n in t)t.hasOwnProperty(n)&&r.push(n+'="'+R.encodeAllRaw(t[n])+'"');r.length&&(o+=" "+r.join(" "))}return o+">"}(t,n),a="</"+t+">",i=A.map(r,function(e){return e.split(/\n/).join("<br />")});return 1===i.length?i[0]:A.map(i,function(e){return o+e+a}).join("")},F=tinymce.util.Tools.resolve("tinymce.html.DomParser"),E=tinymce.util.Tools.resolve("tinymce.html.Serializer"),M="\xa0",N=tinymce.util.Tools.resolve("tinymce.html.Node"),B=tinymce.util.Tools.resolve("tinymce.html.Schema");function j(t,e){return A.each(e,function(e){t=e.constructor===RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function H(e){var t=B(),n=F({},t),r="",o=t.getShortEndedElements(),a=A.makeMap("script noscript style textarea video audio iframe object"," "),i=t.getBlockElements();return e=j(e,[/<!\[[^\]]+\]>/g]),function s(e){var t=e.name,n=e;if("br"!==t){if("wbr"!==t)if(o[t]&&(r+=" "),a[t])r+=" ";else{if(3===e.type&&(r+=e.value),!e.shortEnded&&(e=e.firstChild))for(;s(e),e=e.next;);i[t]&&n.next&&(r+="\n","p"===t&&(r+="\n"))}}else r+="\n"}(n.parse(e)),r}function $(e){return e=j(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,function(e,t,n){return t||n?M:" "}],/<br class="Apple-interchange-newline">/g,/<br>$/i])}function L(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function z(e){var a,i,s=1;function u(e,t){if(3!==e.type||!t.test(e.value)){if(e=e.firstChild)do{if(!u(e,t))return}while(e=e.next);return 1}e.value=e.value.replace(t,"")}function t(e,t,n){var r=e._listLevel||s;r!==s&&(a=r<s?a&&a.parent.parent:(i=a,null)),a&&a.name===t?a.append(e):(i=i||a,a=new N(t,1),1<n&&a.attr("start",""+n),e.wrap(a)),e.name="li",s<r&&i&&i.lastChild.append(a),s=r,function o(e){if(e._listIgnore)e.remove();else if(e=e.firstChild)for(;o(e),e=e.next;);}(e),u(e,/^\u00a0+/),u(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),u(e,/^\u00a0+/)}for(var n=[],r=e.firstChild;null!=r;)if(n.push(r),null!==(r=r.walk()))for(;void 0!==r&&r.parent!==e;)r=r.walk();for(var o=0;o<n.length;o++)if("p"===(e=n[o]).name&&e.firstChild){var l=function d(e){var t="";if(3===e.type)return e.value;if(e=e.firstChild)for(;t+=d(e),e=e.next;);return t}(e);if(/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(l)){t(e,"ul");continue}if(function(t){var n;return t=t.replace(/^[\u00a0 ]+/,""),A.each([/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],function(e){if(e.test(t))return!(n=!0)}),n}(l)){var c=/([0-9]+)\./.exec(l),f=1;c&&(f=parseInt(c[1],10)),t(e,"ol",f);continue}if(e._listLevel){t(e,"ul",1);continue}a=null}else i=a,a=null}var U,q,V=function(r,e){var o,t=T(r);t&&(o=A.makeMap(t.split(/[, ]/))),e=j(e,[/<br class="?Apple-interchange-newline"?>/gi,/<b[^>]+id="?docs-internal-[^>]*>/gi,/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,M],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return 0<t.length?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join(M):""}]]);var n=r.getParam("paste_word_valid_elements","-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody"),a=B({valid_elements:n,valid_children:"-li[p]"});A.each(a.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var i=F({},a);i.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)(t=e[n]).attr("style",function(n,r,o,a){var i,s={},e=n.dom.parseStyle(a);return A.each(e,function(e,t){switch(t){case"mso-list":(i=/\w+ \w+([0-9]+)/i.exec(a))&&(o._listLevel=parseInt(i[1],10)),/Ignore/i.test(e)&&o.firstChild&&(o._listIgnore=!0,o.firstChild._listIgnore=!0);break;case"horiz-align":t="text-align";break;case"vert-align":t="vertical-align";break;case"font-color":case"mso-foreground":t="color";break;case"mso-background":case"mso-highlight":t="background";break;case"font-weight":case"font-style":return void("normal"!==e&&(s[t]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void o.remove()}0!==t.indexOf("mso-comment")?0!==t.indexOf("mso-")&&("all"===T(n)||r&&r[t])&&(s[t]=e):o.remove()}),/(bold)/i.test(s["font-weight"])&&(delete s["font-weight"],o.wrap(new N("b",1))),/(italic)/i.test(s["font-style"])&&(delete s["font-style"],o.wrap(new N("i",1))),(s=n.dom.serializeStyle(s,o.name))||null}(r,o,t,t.attr("style"))),"span"===t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),i.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)n=(t=e[r]).attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),i.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),i.addNodeFilter("a",function(e){for(var t,n,r,o=e.length;o--;)if(n=(t=e[o]).attr("href"),r=t.attr("name"),n&&-1!==n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=(n=n.split("#")[1])&&"#"+n),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var s=i.parse(e);return r.getParam("paste_convert_word_fake_lists",!0)&&z(s),e=E({validate:C(r)},a).serialize(s)},K=function(e,t){return{content:e,cancelled:t}},X=function(e,t,n,r){var o,a,i,s,u,l,c,f,d,m,p,g,v=(o=t,a=n,i=r,e.fire("PastePreProcess",{content:o,internal:a,wordContent:i})),h=function(e,t){var n=F({},e.schema);n.addNodeFilter("meta",function(e){A.each(e,function(e){e.remove()})});var r=n.parse(t,{forced_root_block:!1,isRootContent:!0});return E({validate:C(e)},e.schema).serialize(r)}(e,v.content);return e.hasEventListeners("PastePostProcess")&&!v.isDefaultPrevented()?(u=h,l=n,c=r,p=(s=e).dom.create("div",{style:"display:none"},u),f=p,d=l,m=c,g=s.fire("PastePostProcess",{node:f,internal:d,wordContent:m}),K(g.node.innerHTML,g.isDefaultPrevented())):K(h,v.isDefaultPrevented())},W=function(e,t,n){var r,o,a=L(t),i=a?(o=t,(r=e).getParam("paste_enable_default_filters",!0)?V(r,o):o):t;return X(e,i,n,a)},Y=function(e,t){return e.insertContent(t,{merge:e.getParam("paste_merge_formats",!0),paste:!0}),!0},Z=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},G=function(e){return Z(e)&&/.(gif|jpe?g|png)$/.test(e)},J=function(e,t,n){return!(!1!==e.selection.isCollapsed()||!Z(t))&&(o=t,a=n,(r=e).undoManager.extra(function(){a(r,o)},function(){r.execCommand("mceInsertLink",!1,o)}),!0);var r,o,a},Q=function(e,t,n){return!!G(t)&&(o=t,a=n,(r=e).undoManager.extra(function(){a(r,o)},function(){r.insertContent('<img src="'+o+'">')}),!0);var r,o,a},ee=function(e,t,n){var r,o;n||!1===e.getParam("smart_paste",!0)?Y(e,t):(r=e,o=t,A.each([J,Q,Y],function(e){return!0!==e(r,o,Y)}))},te=function(e){return"\n"===e||"\r"===e},ne=function(e,t){var n,r,o,a,i=(n=" ",(r=e.getParam("paste_tab_spaces",4,"number"))<=0?"":new Array(r+1).join(n)),s=t.replace(/\t/g,i);return(a={pcIsSpace:!(o=function(e,t){return-1!==" \f\t\x0B".indexOf(t)||t===M?e.pcIsSpace||""===e.str||e.str.length===s.length-1||(n=s,(r=e.str.length+1)<n.length&&0<=r&&te(n[r]))?{pcIsSpace:!1,str:e.str+M}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:te(t),str:e.str+t};var n,r}),str:""},v(s,function(e){a=o(a,e)}),a).str},re=function(e,t,n,r){var o=W(e,t,n);!1===o.cancelled&&ee(e,o.content,r)},oe=function(e,t,n){var r=n||O(t);re(e,t.replace(S,""),r,!1)},ae=function(e,t){var n,r,o,a=e.dom.encode(t).replace(/\r\n/g,"\n"),i=ne(e,a),s=(n=i,r=e.getParam("forced_root_block"),o=e.getParam("forced_root_block_attrs"),r?I(n,!0===r?"p":r,o):n.replace(/\r?\n/g,"<br>"));re(e,s,!1,!0)},ie=function(e){var t,n={};if(e&&(!e.getData||(t=e.getData("Text"))&&0<t.length&&-1===t.indexOf("data:text/mce-internal,")&&(n["text/plain"]=t),e.types))for(var r=0;r<e.types.length;r++){var o=e.types[r];try{n[o]=e.getData(o)}catch(a){n[o]=""}}return n},se=function(e,t){return t in e&&0<e[t].length},ue=function(e){return se(e,"text/html")||se(e,"text/plain")},le=(U="mceclip",q=0,function(){return U+q++}),ce=function(e,t){var n,r,o,a,i,s,u,l,c,f,d=(n=t.uri,(r=/data:([^;]+);base64,([a-z0-9\+\/=]+)/i.exec(n))?{type:r[1],data:decodeURIComponent(r[2])}:{type:null,data:null}),m=d.data,p=d.type,g=le(),v=e.getParam("images_reuse_filename")&&t.blob.name?(o=e,a=t.blob.name,(i=a.match(/([\s\S]+?)\.(?:jpeg|jpg|png|gif)$/i))?o.dom.encode(i[1]):null):g,h=new Image;h.src=t.uri,c=h,!(f=e.getParam("images_dataimg_filter"))||f(c)?(u=void 0,(l=(s=e.editorUpload.blobCache).getByData(m,p))?u=l:(u=s.create(g,t.blob,m,v),s.add(u)),oe(e,'<img src="'+u.blobUri()+'">',!1)):oe(e,'<img src="'+t.uri+'">',!1)},fe=function(t,e,n){var r,o,a,i,s="paste"===e.type?e.clipboardData:e.dataTransfer;if(D(t)&&s){var u=(a=(o=s).items?g(h(o.items),function(e){return e.getAsFile()}):[],i=o.files?h(o.files):[],function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var a=e[r];t(a,r)&&n.push(a)}return n}(0<a.length?a:i,function(e){return/^image\/(jpeg|png|gif|bmp)$/.test(e.type)}));if(0<u.length)return e.preventDefault(),r=u,x.all(g(r,function(r){return new x(function(e){var t=r.getAsFile?r.getAsFile():r,n=new window.FileReader;n.onload=function(){e({blob:t,uri:n.result})},n.readAsDataURL(t)})})).then(function(e){n&&t.selection.setRng(n),v(e,function(e){ce(t,e)})}),!0}return!1},de=function(e){return P.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode},me=function(s,u,l){var t,c,f=(t=m(p.none()),{clear:function(){return t.set(p.none())},set:function(e){return t.set(p.some(e))},isSet:function(){return t.get().isSome()},on:function(e){return t.get().each(e)}});function d(e,t,n,r){var o;se(e,"text/html")?o=e["text/html"]:(o=u.getHtml(),r=r||O(o),u.isDefaultContent(o)&&(n=!0)),o=$(o),u.remove();var a=!1===r&&!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(o),i=G(o);o.length&&(!a||i)||(n=!0),(n||i)&&(o=se(e,"text/plain")&&a?e["text/plain"]:H(o)),u.isDefaultContent(o)?t||s.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents."):n?ae(s,o):oe(s,o,r)}s.on("keydown",function(e){function t(e){de(e)&&!e.isDefaultPrevented()&&u.remove()}if(de(e)&&!e.isDefaultPrevented()){if((c=e.shiftKey&&86===e.keyCode)&&y.webkit&&-1!==navigator.userAgent.indexOf("Version/"))return;if(e.stopImmediatePropagation(),f.set(e),window.setTimeout(function(){f.clear()},100),y.ie&&c)return e.preventDefault(),n=!0,void s.fire("paste",{ieFake:n});u.remove(),u.create(),s.once("keyup",t),s.once("paste",function(){s.off("keyup",t)})}var n});s.on("paste",function(e){var t,n,r=f.isSet(),o=(t=s,ie(e.clipboardData||t.getDoc().dataTransfer)),a="text"===l.get()||c,i=se(o,k);c=!1,!(e.isDefaultPrevented()||(n=e.clipboardData,-1!==navigator.userAgent.indexOf("Android")&&n&&n.items&&0===n.items.length))&&(ue(o)||!fe(s,e,u.getLastRng()||s.selection.getRng()))?(r||e.preventDefault(),!y.ie||r&&!e.ieFake||se(o,"text/html")||(u.create(),s.dom.bind(u.getEl(),"paste",function(e){e.stopPropagation()}),s.getDoc().execCommand("Paste",!1,null),o["text/html"]=u.getHtml()),se(o,"text/html")?(e.preventDefault(),i=i||O(o["text/html"]),d(o,r,a,i)):b.setEditorTimeout(s,function(){d(o,r,a,i)},0)):u.remove()})},pe=function(i,e,t){var s;me(i,e,t),i.parser.addNodeFilter("img",function(e,t,n){var r,o=function(e){e.attr("data-mce-object")||s===y.transparentSrc||e.remove()};if(!D(i)&&((r=n).data&&!0===r.data.paste))for(var a=e.length;a--;)(s=e[a].attr("src"))&&(0!==s.indexOf("webkit-fake-url")&&(i.getParam("allow_html_data_urls",!1,"boolean")||0!==s.indexOf("data:"))||o(e[a]))})},ge=function(e){return y.ie&&e.inline?document.body:e.getBody()},ve=function(t,e,n){var r;ge(r=t)!==r.getBody()&&t.dom.bind(e,"paste keyup",function(e){be(t,n)||t.fire("paste")})},he=function(e){return e.dom.get("mcepastebin")},ye=function(e,t){return t===e},be=function(e,t){var n,r=he(e);return(n=r)&&"mcepastebin"===n.id&&ye(t,r.innerHTML)},xe=function(e){var t=m(null),n="%MCEPASTEBIN%";return{create:function(){return function(e,t,n){var r=e.dom,o=e.getBody();t.set(e.selection.getRng());var a=e.dom.add(ge(e),"div",{id:"mcepastebin","class":"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},n);(y.ie||y.gecko)&&r.setStyle(a,"left","rtl"===r.getStyle(o,"direction",!0)?65535:-65535),r.bind(a,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),ve(e,a,n),a.focus(),e.selection.select(a,!0)}(e,t,n)},remove:function(){return function(e,t){if(he(e)){for(var n=void 0,r=t.get();n=e.dom.get("mcepastebin");)e.dom.remove(n),e.dom.unbind(n);r&&e.selection.setRng(r)}t.set(null)}(e,t)},getEl:function(){return he(e)},getHtml:function(){return function(n){var t=function(e,t){e.appendChild(t),n.dom.remove(t,!0)},e=A.grep(ge(n).childNodes,function(e){return"mcepastebin"===e.id}),r=e.shift();A.each(e,function(e){t(r,e)});for(var o=n.dom.select("div[id=mcepastebin]",r),a=o.length-1;0<=a;a--){var i=n.dom.create("div");r.insertBefore(i,o[a]),t(i,o[a])}return r?r.innerHTML:""}(e)},getLastRng:function(){return t.get()},isDefault:function(){return be(e,n)},isDefaultContent:function(e){return e===n}}},Pe=function(n,r){n.addCommand("mceTogglePlainTextPaste",function(){var e,t;e=n,"text"===(t=r).pasteFormat.get()?(t.pasteFormat.set("html"),w(e,!1)):(t.pasteFormat.set("text"),w(e,!0)),e.focus()}),n.addCommand("mceInsertClipboardContent",function(e,t){t.content&&r.pasteHtml(t.content,t.internal),t.text&&r.pasteText(t.text)})},we=function(e,t,n){if(r=e,!1!==y.iOS||"function"!=typeof(null==r?void 0:r.setData))return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(k,t),!0}catch(o){return!1}var r},_e=function(e,t,n,r){we(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},Te=function(s){return function(e,t){var n=S+e,r=s.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),o=s.dom.create("div",{contenteditable:"true"},n);s.dom.setStyles(r,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),r.appendChild(o),s.dom.add(s.getBody(),r);var a=s.selection.getRng();o.focus();var i=s.dom.createRng();i.selectNodeContents(o),s.selection.setRng(i),b.setTimeout(function(){s.selection.setRng(a),r.parentNode.removeChild(r),t()},0)}},Ce=function(e){return{html:e.selection.getContent({contextual:!0}),text:e.selection.getContent({format:"text"})}},De=function(e){return!e.selection.isCollapsed()||!!(t=e).dom.getParent(t.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",t.getBody());var t},ke=function(e){var t,n;e.on("cut",(t=e,function(e){De(t)&&_e(e,Ce(t),Te(t),function(){var e;y.browser.isChrome()?(e=t.selection.getRng(),b.setEditorTimeout(t,function(){t.selection.setRng(e),t.execCommand("Delete")},0)):t.execCommand("Delete")})})),e.on("copy",(n=e,function(e){De(n)&&_e(e,Ce(n),Te(n),function(){})}))},Se=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),Oe=function(e,t){return Se.getCaretRangeFromPoint(t.clientX,t.clientY,e.getDoc())},Re=function(e,t){e.focus(),e.selection.setRng(t)},Ae=function(i,s,u){i.getParam("paste_block_drop",!1)&&i.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),_(i)||i.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&0<t.files.length&&e.preventDefault()}),i.on("drop",function(e){var t,n,r,o,a=Oe(i,e);e.isDefaultPrevented()||u.get()||(t=s.getDataTransferItems(e.dataTransfer),n=s.hasContentType(t,k),(!s.hasHtmlOrText(t)||(r=t["text/plain"])&&0===r.indexOf("file://"))&&s.pasteImageData(e,a)||!a||!i.getParam("paste_filter_drop",!0)||(o=t["mce-internal"]||t["text/html"]||t["text/plain"])&&(e.preventDefault(),b.setEditorTimeout(i,function(){i.undoManager.transact(function(){t["mce-internal"]&&i.execCommand("Delete"),Re(i,a),o=$(o),t["text/html"]?s.pasteHtml(o,n):s.pasteText(o)})})))}),i.on("dragstart",function(e){u.set(!0)}),i.on("dragover dragend",function(e){_(i)&&!1===u.get()&&(e.preventDefault(),Re(i,Oe(i,e))),"dragend"===e.type&&u.set(!1)})};function Ie(t,n){t.on("PastePreProcess",function(e){e.content=n(t,e.content,e.internal,e.wordContent)})}function Fe(e,t){if(!L(t))return t;var n=[];return A.each(e.schema.getBlockElements(),function(e,t){n.push(t)}),t=j(t,[[new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+n.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g"),"$1"]]),t=j(t,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function Ee(e,t,n,r){if(r||n)return t;var l,c,f,o=e.getParam("paste_webkit_styles");return!1===e.getParam("paste_remove_styles_if_webkit",!0)||"all"===o?t:(o&&(l=o.split(/[, ]/)),t=(t=l?(c=e.dom,f=e.selection.getNode(),t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,t,n,r){var o=c.parseStyle(c.decode(n)),a={};if("none"===l)return t+r;for(var i=0;i<l.length;i++){var s=o[l[i]],u=c.getStyle(f,l[i],!0);/color/.test(l[i])&&(s=c.toHex(s),u=c.toHex(u)),u!==s&&(a[l[i]]=s)}return(a=c.serializeStyle(a,"span"))?t+' style="'+a+'"'+r:t+r})):t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3")).replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r}))}function Me(n,e){n.$("a",e).find("font,u").each(function(e,t){n.dom.remove(t,!0)})}var Ne=function(n,r){return function(t){t.setActive("text"===r.pasteFormat.get());var e=function(e){return t.setActive(e.state)};return n.on("PastePlainTextToggle",e),function(){return n.off("PastePlainTextToggle",e)}}};o.add("paste",function(e){if(!1==(!!e.hasPlugin("powerpaste",!0)&&("undefined"!=typeof window.console&&window.console.log&&window.console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option."),!0))){var t=m(!1),n=m(e.getParam("paste_as_text",!1)?"text":"html"),r=(c=n,f=xe(l=e),l.on("PreInit",function(){return pe(l,f,c)}),{pasteFormat:c,pasteHtml:function(e,t){return oe(l,e,t)},pasteText:function(e){return ae(l,e)},pasteImageData:function(e,t){return fe(l,e,t)},getDataTransferItems:ie,hasHtmlOrText:ue,hasContentType:se});i=e,y.webkit&&Ie(i,Ee),y.ie&&(Ie(i,Fe),u=Me,(s=i).on("PastePostProcess",function(e){u(s,e.node)}));return a=r,(o=e).ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:function(){return o.execCommand("mceTogglePlainTextPaste")},onSetup:Ne(o,a)}),o.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:function(){return o.execCommand("mceTogglePlainTextPaste")},onSetup:Ne(o,a)}),Pe(e,r),function(e){var t=e.plugins.paste,n=e.getParam("paste_preprocess");n&&e.on("PastePreProcess",function(e){n.call(t,t,e)});var r=e.getParam("paste_postprocess");r&&e.on("PastePostProcess",function(e){r.call(t,t,e)})}(e),ke(e),Ae(e,r,t),{clipboard:r,quirks:void 0}}var o,a,i,s,u,l,c,f})}();