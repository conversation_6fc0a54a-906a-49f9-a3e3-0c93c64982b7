/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.5.1 (2020-10-01)
 */
!function(){"use strict";var y=function(){},b=function(n){return function(){return n}},u=function(n){return n};function w(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var n,e,t,d=function(e){return function(n){return!e(n)}},f=b(!1),C=b(!0),r=function(){return i},i=(n=function(n){return n.isNone()},{fold:function(n,e){return n()},is:f,isSome:f,isNone:C,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:t,orThunk:e,map:r,each:y,bind:r,exists:f,forall:C,filter:r,equals:n,equals_:n,toArray:function(){return[]},toString:b("none()")}),c=function(t){var n=b(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:C,isNone:f,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return c(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:i},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(f,function(n){return e(t,n)})}};return o},S={some:c,none:r,from:function(n){return null===n||n===undefined?i:c(n)}},o=function(r){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===r;var e,t}},a=function(e){return function(n){return typeof n===e}},p=o("string"),m=o("object"),l=o("array"),s=a("boolean"),g=function(n){return!(null===(e=n)||e===undefined);var e},h=a("function"),v=a("number"),x=Array.prototype.slice,T=Array.prototype.indexOf,R=Array.prototype.push,D=function(n,e){return t=n,r=e,-1<T.call(t,r);var t,r},O=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1},A=function(n,e){for(var t=[],r=0;r<n;r++)t.push(e(r));return t},B=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},E=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},I=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var u=n[r];e(u,r)&&t.push(u)}return t},P=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t},k=function(n,e,t){return E(n,function(n){t=e(t,n)}),t},M=function(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var u=n[r];if(e(u,r))return S.some(u);if(t(u,r))break}return S.none()}(n,e,f)},N=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return S.some(t)}return S.none()},j=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!l(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);R.apply(e,n[t])}return e},_=function(n,e){return j(B(n,e))},W=function(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0},z=function(n){return[n]},F=function(n){return 0===n.length?S.none():S.some(n[n.length-1])},L=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return S.none()},H=function(){return(H=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function q(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var r=Array(n),o=0,e=0;e<t;e++)for(var u=arguments[e],i=0,c=u.length;i<c;i++,o++)r[o]=u[i];return r}var V,U=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},$=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return K(r(1),r(2))},G=function(){return K(0,0)},K=function(n,e){return{major:n,minor:e}},X={nu:K,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?G():$(n,t)},unknown:G},Y=function(n,e){var t=String(e).toLowerCase();return M(n,function(n){return n.search(t)})},J=function(n,t){return Y(n,t).map(function(n){var e=X.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Q=function(n,t){return Y(n,t).map(function(n){var e=X.detect(n.versionRegexes,t);return{current:n.name,version:e}})},Z=function(n,e,t){return""===e||n.length>=e.length&&n.substr(t,t+e.length)===e},nn=function(n,e){return-1!==n.indexOf(e)},en=function(n,e){return Z(n,e,0)},tn=function(n,e){return Z(n,e,n.length-e.length)},rn=(V=/^\s+|\s+$/g,function(n){return n.replace(V,"")}),on=function(n){return 0<n.length},un=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,cn=function(e){return function(n){return nn(n,e)}},an=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return nn(n,"edge/")&&nn(n,"chrome")&&nn(n,"safari")&&nn(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,un],search:function(n){return nn(n,"chrome")&&!nn(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return nn(n,"msie")||nn(n,"trident")}},{name:"Opera",versionRegexes:[un,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:cn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:cn("firefox")},{name:"Safari",versionRegexes:[un,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(nn(n,"safari")||nn(n,"mobile/"))&&nn(n,"applewebkit")}}],ln=[{name:"Windows",search:cn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return nn(n,"iphone")||nn(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:cn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:cn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:cn("linux"),versionRegexes:[]},{name:"Solaris",search:cn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:cn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:cn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],fn={browsers:b(an),oses:b(ln)},sn="Firefox",dn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r("Edge"),isChrome:r("Chrome"),isIE:r("IE"),isOpera:r("Opera"),isFirefox:r(sn),isSafari:r("Safari")}},mn={unknown:function(){return dn({current:undefined,version:X.unknown()})},nu:dn,edge:b("Edge"),chrome:b("Chrome"),ie:b("IE"),opera:b("Opera"),firefox:b(sn),safari:b("Safari")},gn="Windows",pn="Android",hn="Solaris",vn="FreeBSD",bn="ChromeOS",wn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r(gn),isiOS:r("iOS"),isAndroid:r(pn),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(hn),isFreeBSD:r(vn),isChromeOS:r(bn)}},yn={unknown:function(){return wn({current:undefined,version:X.unknown()})},nu:wn,windows:b(gn),ios:b("iOS"),android:b(pn),linux:b("Linux"),osx:b("OSX"),solaris:b(hn),freebsd:b(vn),chromeos:b(bn)},Cn=function(n,e){var t,r,o,u,i,c,a,l,f,s,d,m,g=fn.browsers(),p=fn.oses(),h=J(g,n).fold(mn.unknown,mn.nu),v=Q(p,n).fold(yn.unknown,yn.nu);return{browser:h,os:v,deviceType:(r=h,o=n,u=e,i=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!i,a=t.isiOS()||t.isAndroid(),l=a||u("(pointer:coarse)"),f=i||!c&&a&&u("(min-device-width:768px)"),s=c||a&&!f,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!s&&!f&&!d,{isiPad:b(i),isiPhone:b(c),isTablet:b(f),isPhone:b(s),isTouch:b(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:b(d),isDesktop:b(m)})}},Sn=function(n){return window.matchMedia(n).matches},xn=U(function(){return Cn(navigator.userAgent,Sn)}),Tn=function(){return xn()},Rn=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:n}},Dn={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Rn(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return Rn(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return Rn(t)},fromDom:Rn,fromPoint:function(n,e,t){return S.from(n.dom.elementFromPoint(e,t)).map(Rn)}},On=function(n,e){var t=n.dom;if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},An=function(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount},Bn=function(n,e){return n.dom===e.dom},En=function(n,e){return t=n.dom,r=e.dom,o=t,u=r,i=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(o.compareDocumentPosition(u)&i);var t,r,o,u,i},In=function(n,e){return Tn().browser.isIE()?En(n,e):(t=e,r=n.dom,o=t.dom,r!==o&&r.contains(o));var t,r,o},Pn=On,kn=Object.keys,Mn=Object.hasOwnProperty,Nn=function(n,e){for(var t=kn(n),r=0,o=t.length;r<o;r++){var u=t[r];e(n[u],u)}},jn=function(n,t){return _n(n,function(n,e){return{k:e,v:t(n,e)}})},_n=function(n,r){var o={};return Nn(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},Wn=function(n,e){var t,r,o,u,i={};return t=e,u=i,r=function(n,e){u[e]=n},o=y,Nn(n,function(n,e){(t(n,e)?r:o)(n,e)}),i},zn=function(n){return t=function(n){return n},r=[],Nn(n,function(n,e){r.push(t(n,e))}),r;var t,r},Fn=function(n,e){return Ln(n,e)?S.from(n[e]):S.none()},Ln=function(n,e){return Mn.call(n,e)},Hn=["tfoot","thead","tbody","colgroup"],qn=function(n,e,t){return{element:n,rowspan:e,colspan:t}},Vn=function(n,e,t){return{element:n,cells:e,section:t}},Un=function(n,e){return{element:n,isNew:e}},$n=function(n,e){return{cells:n,section:e}},Gn=("undefined"!=typeof window||Function("return this;")(),function(n){return n.dom.nodeName.toLowerCase()}),Kn=function(n){return n.dom.nodeType},Xn=function(e){return function(n){return Kn(n)===e}},Yn=function(n){return 8===Kn(n)||"#comment"===Gn(n)},Jn=Xn(1),Qn=Xn(3),Zn=Xn(9),ne=Xn(11),ee=function(n){return Dn.fromDom(n.dom.ownerDocument)},te=function(n){return Zn(n)?n:ee(n)},re=function(n){return S.from(n.dom.parentNode).map(Dn.fromDom)},oe=function(n,e){for(var t=h(e)?e:f,r=n.dom,o=[];null!==r.parentNode&&r.parentNode!==undefined;){var u=r.parentNode,i=Dn.fromDom(u);if(o.push(i),!0===t(i))break;r=u}return o},ue=function(n){return S.from(n.dom.previousSibling).map(Dn.fromDom)},ie=function(n){return S.from(n.dom.nextSibling).map(Dn.fromDom)},ce=function(n){return B(n.dom.childNodes,Dn.fromDom)},ae=function(n,e){var t=n.dom.childNodes;return S.from(t[e]).map(Dn.fromDom)},le=h(Element.prototype.attachShadow)&&h(Node.prototype.getRootNode),fe=b(le),se=le?function(n){return Dn.fromDom(n.dom.getRootNode())}:te,de=function(n){var e=se(n);return ne(e)?S.some(e):S.none()},me=function(n){return Dn.fromDom(n.dom.host)},ge=function(n){if(fe()&&g(n.target)){var e=Dn.fromDom(n.target);if(Jn(e)&&pe(e)&&n.composed&&n.composedPath){var t=n.composedPath();if(t)return 0===(r=t).length?S.none():S.some(r[0])}}var r;return S.from(n.target)},pe=function(n){return g(n.dom.shadowRoot)},he=function(n){var e=Qn(n)?n.dom.parentNode:n.dom;if(e===undefined||null===e||null===e.ownerDocument)return!1;var t,r,o=e.ownerDocument;return de(Dn.fromDom(e)).fold(function(){return o.body.contains(e)},(t=he,r=me,function(n){return t(r(n))}))},ve=function(n){var e=n.dom.body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Dn.fromDom(e)},be=function(n,e){var t=[];return E(ce(n),function(n){e(n)&&(t=t.concat([n])),t=t.concat(be(n,e))}),t},we=function(n,e,t){return r=function(n){return On(n,e)},I(oe(n,t),r);var r},ye=function(n,e){return t=function(n){return On(n,e)},I(ce(n),t);var t},Ce=function(n,e){return t=e,o=(r=n)===undefined?document:r.dom,An(o)?[]:B(o.querySelectorAll(t),Dn.fromDom);var t,r,o};function Se(n,e,t,r,o){return n(t,r)?S.some(t):h(o)&&o(t)?S.none():e(t,r,o)}var xe=function(n,e,t){for(var r=n.dom,o=h(t)?t:f;r.parentNode;){r=r.parentNode;var u=Dn.fromDom(r);if(e(u))return S.some(u);if(o(u))break}return S.none()},Te=function(n,e,t){return xe(n,function(n){return On(n,e)},t)},Re=function(n,e){return t=function(n){return On(n,e)},M(n.dom.childNodes,function(n){return t(Dn.fromDom(n))}).map(Dn.fromDom);var t},De=function(n,e){return t=e,o=(r=n)===undefined?document:r.dom,An(o)?S.none():S.from(o.querySelector(t)).map(Dn.fromDom);var t,r,o},Oe=function(n,e,t){return Se(On,Te,n,e,t)},Ae=function(n,e,t){if(!(p(t)||s(t)||v(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},Be=function(n,e,t){Ae(n.dom,e,t)},Ee=function(n,e){var t=n.dom;Nn(e,function(n,e){Ae(t,e,n)})},Ie=function(n,e){var t=n.dom.getAttribute(e);return null===t?undefined:t},Pe=function(n,e){return S.from(Ie(n,e))},ke=function(n,e){n.dom.removeAttribute(e)},Me=function(n){return k(n.dom.attributes,function(n,e){return n[e.name]=e.value,n},{})},Ne=function(n){return n.style!==undefined&&h(n.style.getPropertyValue)},je=function(n,e,t){if(!p(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Ne(n)&&n.style.setProperty(e,t)},_e=function(n,e,t){var r=n.dom;je(r,e,t)},We=function(n,e){var t=n.dom;Nn(e,function(n,e){je(t,e,n)})},ze=function(n,e){var t=n.dom,r=window.getComputedStyle(t).getPropertyValue(e);return""!==r||he(n)?r:Fe(t,e)},Fe=function(n,e){return Ne(n)?n.style.getPropertyValue(e):""},Le=function(n,e){var t=n.dom,r=Fe(t,e);return S.from(r).filter(function(n){return 0<n.length})},He=function(n,e){var t,r,o=n.dom;r=e,Ne(t=o)&&t.style.removeProperty(r),Pe(n,"style").map(rn).is("")&&ke(n,"style")},qe=function(n,e,t){return void 0===t&&(t=0),Pe(n,e).map(function(n){return parseInt(n,10)}).getOr(t)},Ve=function(n,e){return qe(n,e,1)},Ue=function(n){return 1<Ve(n,"colspan")},$e=function(n){return 1<Ve(n,"rowspan")},Ge=function(n,e){return parseInt(ze(n,e),10)},Ke=b(10),Xe=b(10),Ye=function(n,e){return Je(n,e,C)},Je=function(n,e,t){return _(ce(n),function(n){return On(n,e)?t(n)?[n]:[]:Je(n,e,t)})},Qe=function(n,e){return function(n,e,t){if(void 0===t&&(t=f),t(e))return S.none();if(D(n,Gn(e)))return S.some(e);return Te(e,n.join(","),function(n){return On(n,"table")||t(n)})}(["td","th"],n,e)},Ze=function(n){return Ye(n,"th,td")},nt=function(n){return Ye(n,"col")},et=function(n,e){return Oe(n,"table",e)},tt=function(n){return Ye(n,"tr")},rt=function(n,t){return B(n,function(n){if("colgroup"===Gn(n)){var e=B(nt(n),function(n){var e=qe(n,"span",1);return qn(n,1,e)});return Vn(n,e,"colgroup")}e=B(Ze(n),function(n){var e=qe(n,"rowspan",1),t=qe(n,"colspan",1);return qn(n,e,t)});return Vn(n,e,t(n))})},ot=function(n){return re(n).map(function(n){var e=Gn(n);return D(Hn,e)?e:"tbody"}).getOr("tbody")},ut=function(n){var e=tt(n),t=q(Ye(n,"colgroup"),e);return rt(t,ot)},it=function(n,e){return n+","+e},ct=function(n,e){var t=_(n.all,function(n){return n.cells});return I(t,e)},at=function(n){var a={},e=[],t={},r=0,l=0,f=0;return E(n,function(n){var c,o,u;"colgroup"===n.section?(o={},u=0,E(n.cells,function(t){var r=t.colspan;A(r,function(n){var e=u+n;o[e]={element:t.element,colspan:r,column:e}}),u+=r}),t=o):(c=[],E(n.cells,function(n){for(var e=0;a[it(f,e)]!==undefined;)e++;for(var t={element:n.element,rowspan:n.rowspan,colspan:n.colspan,row:f,column:e},r=0;r<n.colspan;r++)for(var o=0;o<n.rowspan;o++){var u=e+r,i=it(f+o,u);a[i]=t,l=Math.max(l,u+1)}c.push(t)}),r++,e.push(Vn(n.element,c,n.section)),f++)}),{grid:{rows:r,columns:l},access:a,all:e,columns:t}},lt={fromTable:function(n){var e=ut(n);return at(e)},generate:at,getAt:function(n,e,t){var r=n.access[it(e,t)];return r!==undefined?S.some(r):S.none()},findItem:function(n,e,t){var r=ct(n,function(n){return t(e,n.element)});return 0<r.length?S.some(r[0]):S.none()},filterItems:ct,justCells:function(n){return _(n.all,function(n){return n.cells})},justColumns:function(n){return zn(n.columns)},hasColumns:function(n){return 0<kn(n.columns).length},getColumnAt:function(n,e){return S.from(n.columns[e])}},ft=function(n,e){var t=e.column,r=e.column+e.colspan-1,o=e.row,u=e.row+e.rowspan-1;return t<=n.finishCol&&r>=n.startCol&&o<=n.finishRow&&u>=n.startRow},st=function(n,e){return e.column>=n.startCol&&e.column+e.colspan-1<=n.finishCol&&e.row>=n.startRow&&e.row+e.rowspan-1<=n.finishRow},dt=function(n,e,t){var r=lt.findItem(n,e,Bn),o=lt.findItem(n,t,Bn);return r.bind(function(r){return o.map(function(n){return e=r,t=n,{startRow:Math.min(e.row,t.row),startCol:Math.min(e.column,t.column),finishRow:Math.max(e.row+e.rowspan-1,t.row+t.rowspan-1),finishCol:Math.max(e.column+e.colspan-1,t.column+t.colspan-1)};var e,t})})},mt=function(e,n,t){return dt(e,n,t).bind(function(n){return function(n,e){for(var t=!0,r=w(st,e),o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++)t=t&&lt.getAt(n,o,u).exists(r);return t?S.some(e):S.none()}(e,n)})},gt=function(t,n,e){return dt(t,n,e).map(function(n){var e=lt.filterItems(t,w(ft,n));return B(e,function(n){return n.element})})},pt=function(n,e){return lt.findItem(n,e,function(n,e){return In(e,n)}).map(function(n){return n.element})},ht=function(i,c,a){return et(i).bind(function(n){var r,e,o,u,t=bt(n);return r=t,e=i,o=c,u=a,lt.findItem(r,e,Bn).bind(function(n){var e=0<o?n.row+n.rowspan-1:n.row,t=0<u?n.column+n.colspan-1:n.column;return lt.getAt(r,e+o,t+u).map(function(n){return n.element})})})},vt=function(n,e,t,r,o){var u=bt(n),i=Bn(n,t)?S.some(e):pt(u,e),c=Bn(n,o)?S.some(r):pt(u,r);return i.bind(function(e){return c.bind(function(n){return gt(u,e,n)})})},bt=lt.fromTable,wt=function(e,t){re(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})},yt=function(n,e){ie(n).fold(function(){re(n).each(function(n){St(n,e)})},function(n){wt(n,e)})},Ct=function(e,t){ae(e,0).fold(function(){St(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})},St=function(n,e){n.dom.appendChild(e.dom)},xt=function(n,e){wt(n,e),St(e,n)},Tt=function(r,o){E(o,function(n,e){var t=0===e?r:o[e-1];yt(t,n)})},Rt=function(e,n){E(n,function(n){St(e,n)})},Dt=function(n){n.dom.textContent="",E(ce(n),function(n){Ot(n)})},Ot=function(n){var e=n.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},At=function(n){var e,t=ce(n);0<t.length&&(e=n,E(t,function(n){wt(e,n)})),Ot(n)};var Bt,Et,It,Pt=(Bt=Qn,Et="text",{get:function(n){if(!Bt(n))throw new Error("Can only get "+Et+" value of a "+Et+" node");return It(n).getOr("")},getOption:It=function(n){return Bt(n)?S.from(n.dom.nodeValue):S.none()},set:function(n,e){if(!Bt(n))throw new Error("Can only set raw "+Et+" value of a "+Et+" node");n.dom.nodeValue=e}}),kt=function(n){return Pt.get(n)},Mt=function(n){return Pt.getOption(n)},Nt=function(n,e){return Pt.set(n,e)},jt=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function _t(){return{up:b({selector:Te,closest:Oe,predicate:xe,all:oe}),down:b({selector:Ce,predicate:be}),styles:b({get:ze,getRaw:Le,set:_e,remove:He}),attrs:b({get:Ie,set:Be,remove:ke,copyTo:function(n,e){var t=Me(n);Ee(e,t)}}),insert:b({before:wt,after:yt,afterAll:Tt,append:St,appendAll:Rt,prepend:Ct,wrap:xt}),remove:b({unwrap:At,remove:Ot}),create:b({nu:Dn.fromTag,clone:function(n){return Dn.fromDom(n.dom.cloneNode(!1))},text:Dn.fromText}),query:b({comparePosition:function(n,e){return n.dom.compareDocumentPosition(e.dom)},prevSibling:ue,nextSibling:ie}),property:b({children:ce,name:Gn,parent:re,document:function(n){return te(n).dom},isText:Qn,isComment:Yn,isElement:Jn,getText:kt,setText:Nt,isBoundary:function(n){return!!Jn(n)&&("body"===Gn(n)||D(jt,Gn(n)))},isEmptyTag:function(n){return!!Jn(n)&&D(["br","img","hr","input"],Gn(n))},isNonEditable:function(n){return Jn(n)&&"false"===Ie(n,"contenteditable")}}),eq:Bn,is:Pn}}var Wt=function(r,o,n,e){var t=o(r,n);return P(e,function(n,e){var t=o(r,e);return zt(r,n,t)},t)},zt=function(e,n,t){return n.bind(function(n){return t.filter(w(e.eq,n))})},Ft=function(n,e,t){return 0<t.length?Wt(n,e,(r=t)[0],r.slice(1)):S.none();var r},Lt=function(t,n,e,r){void 0===r&&(r=f);var o=[n].concat(t.up().all(n)),u=[e].concat(t.up().all(e)),i=function(e){return N(e,r).fold(function(){return e},function(n){return e.slice(0,n+1)})},c=i(o),a=i(u),l=M(c,function(n){return O(a,(e=n,w(t.eq,e)));var e});return{firstpath:c,secondpath:a,shared:l}},Ht=_t(),qt=function(t,n){return Ft(Ht,function(n,e){return t(e)},n)},Vt=function(n){return Te(n,"table")},Ut=function(l,f,s){var d=function(e){return function(n){return s!==undefined&&s(n)||Bn(n,e)}};return Bn(l,f)?S.some({boxes:S.some([l]),start:l,finish:f}):Vt(l).bind(function(a){return Vt(f).bind(function(u){if(Bn(a,u))return S.some({boxes:(o=l,i=f,c=bt(a),gt(c,o,i)),start:l,finish:f});if(In(a,u)){var n=0<(e=we(f,"td,th",d(a))).length?e[e.length-1]:f;return S.some({boxes:vt(a,l,a,f,u),start:l,finish:n})}if(In(u,a)){var e,t=0<(e=we(l,"td,th",d(u))).length?e[e.length-1]:l;return S.some({boxes:vt(u,l,a,f,u),start:l,finish:t})}return Lt(Ht,l,f,r).shared.bind(function(n){return Oe(n,"table",s).bind(function(n){var e=we(f,"td,th",d(n)),t=0<e.length?e[e.length-1]:f,r=we(l,"td,th",d(n)),o=0<r.length?r[r.length-1]:l;return S.some({boxes:vt(n,l,a,f,u),start:o,finish:t})})});var r,o,i,c})})},$t=function(n,e){var t=Ce(n,e);return 0<t.length?S.some(t):S.none()},Gt=function(n,e,r){return De(n,e).bind(function(t){return De(n,r).bind(function(e){return qt(Vt,[t,e]).map(function(n){return{first:t,last:e,table:n}})})})},Kt=function(n,e,t,r,o){return u=o,M(n,function(n){return On(n,u)}).bind(function(n){return ht(n,e,t).bind(function(n){return t=r,Te(e=n,"table").bind(function(n){return De(n,t).bind(function(n){return Ut(n,e).bind(function(e){return e.boxes.map(function(n){return{boxes:n,start:e.start,finish:e.finish}})})})});var e,t})});var u},Xt=$t,Yt=function(o,n,e){return Gt(o,n,e).bind(function(i){var n=function(n){return Bn(o,n)},e="thead,tfoot,tbody,table",t=Te(i.first,e,n),r=Te(i.last,e,n);return t.bind(function(u){return r.bind(function(n){return Bn(u,n)?(e=i.table,t=i.first,r=i.last,o=bt(e),mt(o,t,r)):S.none();var e,t,r,o})})})},Jt=function(i){if(!l(i))throw new Error("cases must be an array");if(0===i.length)throw new Error("there must be at least one case");var c=[],t={};return E(i,function(n,r){var e=kn(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],u=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!l(u))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==u.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+u.length+" ("+u+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==i.length)throw new Error("Wrong number of arguments to fold. Expected "+i.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=kn(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!W(c,function(n){return D(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},Qt=Jt([{none:[]},{multiple:["elements"]},{single:["element"]}]),Zt=function(n,e,t,r){return n.fold(e,t,r)},nr=Qt.none,er=Qt.multiple,tr=Qt.single,rr=function(n,e,t){return{get:function(){return Xt(n(),t).fold(function(){return e().map(tr).getOrThunk(nr)},function(n){return er(n)})}}},or=tinymce.util.Tools.resolve("tinymce.PluginManager"),ur=function(n,e,t,r){for(var o,u,i,c=e.grid.columns,a=e.grid.rows,l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){l<t.minRow||l>t.maxRow||s<t.minCol||s>t.maxCol||(lt.getAt(e,l,s).filter(r).isNone()?(o=f,0,u=n[l].element,i=Dn.fromTag("td"),St(i,Dn.fromTag("br")),(o?St:Ct)(u,i)):f=!0)}},ir=function(n,e){var t,u,r,i,c,a,l,o,f,s,d=function(n){return On(n.element,e)},m=ut(n),g=lt.generate(m),p=(u=d,r=(t=g).grid.columns,i=t.grid.rows,c=r,l=a=0,Nn(t.access,function(n){var e,t,r,o;u(n)&&(t=(e=n.row)+n.rowspan-1,o=(r=n.column)+n.colspan-1,e<i?i=e:a<t&&(a=t),r<c?c=r:l<o&&(l=o))}),{minRow:i,minCol:c,maxRow:a,maxCol:l}),h="th:not("+e+"),td:not("+e+")",v=Je(n,"th,td",function(n){return On(n,h)});return E(v,Ot),ur(m,g,p,d),f=p,s=I(Ye(o=n,"tr"),function(n){return 0===n.dom.childElementCount}),E(s,Ot),f.minCol!==f.maxCol&&f.minRow!==f.maxRow||E(Ye(o,"th,td"),function(n){ke(n,"rowspan"),ke(n,"colspan")}),ke(o,"width"),ke(o,"height"),He(o,"width"),He(o,"height"),n},cr=function(n){return"img"===Gn(n)?1:Mt(n).fold(function(){return ce(n).length},function(n){return n.length})},ar=["img","br"],lr=function(n){return Mt(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()||D(ar,Gn(n))},fr=function(n){return o=lr,(u=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Dn.fromDom(n.childNodes[e]);if(o(t))return S.some(t);var r=u(n.childNodes[e]);if(r.isSome())return r}return S.none()})(n.dom);var o,u},sr=function(n){return dr(n,lr)},dr=function(n,u){var i=function(n){for(var e=ce(n),t=e.length-1;0<=t;t--){var r=e[t];if(u(r))return S.some(r);var o=i(r);if(o.isSome())return o}return S.none()};return i(n)},mr=function(n,e){return Dn.fromDom(n.dom.cloneNode(e))},gr=function(n){return mr(n,!1)},pr=function(n){return mr(n,!0)},hr=function(n,e){var t,r,o,u,i=(t=n,r=e,o=Dn.fromTag(r),u=Me(t),Ee(o,u),o),c=ce(pr(n));return Rt(i,c),i},vr=function(){var n=Dn.fromTag("td");return St(n,Dn.fromTag("br")),n},br=function(){return Dn.fromTag("col")},wr=function(){return Dn.fromTag("colgroup")},yr=function(n,e,t){var r=hr(n,e);return Nn(t,function(n,e){null===n?ke(r,e):Be(r,e,n)}),r},Cr=function(n){return n},Sr=function(n){return function(){return Dn.fromTag("tr",n.dom)}},xr=function(a,n,l){var f=function(n,e){var t,r,o,u;t=n.element,r=e,o=t.dom,u=r.dom,Ne(o)&&Ne(u)&&(u.style.cssText=o.style.cssText),He(e,"height"),1!==n.colspan&&He(n.element,"width")};return{col:function(n){var e=ee(n.element),t=Dn.fromTag(Gn(n.element),e.dom);return f(n,t),a(n.element,t),t},colgroup:wr,row:Sr(n),cell:function(n){var r,o,u,e=ee(n.element),t=Dn.fromTag(Gn(n.element),e.dom),i=l.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),c=0<i.length?(r=n.element,o=t,u=i,fr(r).map(function(n){var e=u.join(","),t=we(n,e,function(n){return Bn(n,r)});return P(t,function(n,e){var t=gr(e);return ke(t,"contenteditable"),St(n,t),t},o)}).getOr(o)):t;return St(c,Dn.fromTag("br")),f(n,t),a(n.element,t),t},replace:yr,gap:vr}},Tr=function(n){return{col:br,colgroup:wr,row:Sr(n),cell:vr,replace:Cr,gap:vr}},Rr=function(n){return B(n,Dn.fromDom)},Dr=function(n){return Zt(n.get(),b([]),u,z)},Or="data-mce-selected",Ar="data-mce-first-selected",Br="data-mce-last-selected",Er={selected:Or,selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:Ar,firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:Br,lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},Ir=function(n){return{element:n,mergable:S.none(),unmergable:S.none(),selection:[n]}},Pr=function(n,e,t){return{element:t,mergable:(u=e,i=Er,Zt(n.get(),S.none,function(e){return e.length<=1?S.none():Yt(u,i.firstSelectedSelector,i.lastSelectedSelector).map(function(n){return{bounds:n,cells:e}})},S.none)),unmergable:(r=function(n,e){return Pe(n,e).exists(function(n){return 1<parseInt(n,10)})},0<(o=Dr(n)).length&&W(o,function(n){return r(n,"rowspan")||r(n,"colspan")})?S.some(o):S.none()),selection:Dr(n)};var r,o,u,i},kr=function(s,n,d,m){s.on("BeforeGetContent",function(t){!0===t.selection&&Zt(n.get(),y,function(n){t.preventDefault(),et(n[0]).map(pr).map(function(n){return[ir(n,"[data-mce-selected]")]}).each(function(n){var e;t.content="text"===t.format?B(n,function(n){return n.dom.innerText}).join(""):(e=s,B(n,function(n){return e.selection.serializer.serialize(n.dom,{})}).join(""))})},y)}),s.on("BeforeSetContent",function(f){!0===f.selection&&!0===f.paste&&S.from(s.dom.getParent(s.selection.getStart(),"th,td")).each(function(n){var l=Dn.fromDom(n);et(l).each(function(e){var n,t,r,o,u,i,c,a=I((n=f.content,(r=(t||document).createElement("div")).innerHTML=n,ce(Dn.fromDom(r))),function(n){return"meta"!==Gn(n)});1===a.length&&(c=a[0],"table"===Gn(c))&&(f.preventDefault(),o=Dn.fromDom(s.getDoc()),u=Tr(o),i={element:l,clipboard:a[0],generators:u},d.pasteCells(e,i).each(function(n){s.selection.setRng(n),s.focus(),m.clear(e)}))})})})},Mr=Jt([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),Nr=H({},Mr),jr=function(n,e,r,o,u){var t,i,c=n.slice(0),a=(i=e,0===(t=n).length?Nr.none():1===t.length?Nr.only(0):0===i?Nr.left(0,1):i===t.length-1?Nr.right(i-1,i):0<i&&i<t.length-1?Nr.middle(i-1,i,i+1):Nr.none()),l=b(B(c,b(0)));return a.fold(l,function(n){return o.singleColumnWidth(c[n],r)},function(n,e){return u.calcLeftEdgeDeltas(c,n,e,r,o.minCellWidth(),o.isRelative)},function(n,e,t){return u.calcMiddleDeltas(c,n,e,t,r,o.minCellWidth(),o.isRelative)},function(n,e){return u.calcRightEdgeDeltas(c,n,e,r,o.minCellWidth(),o.isRelative)})},_r=function(t){var n=t.grid,e=A(n.columns,u),r=A(n.rows,u);return B(e,function(e){return Wr(function(){return _(r,function(n){return lt.getAt(t,n,e).filter(function(n){return n.column===e}).toArray()})},function(n){return 1===n.colspan},function(){return lt.getAt(t,0,e)})})},Wr=function(n,e,t){var r=n();return M(r,e).orThunk(function(){return S.from(r[0]).orThunk(t)}).map(function(n){return n.element})},zr=function(t){var n=t.grid,e=A(n.rows,u),r=A(n.columns,u);return B(e,function(e){return Wr(function(){return _(r,function(n){return lt.getAt(t,e,n).filter(function(n){return n.row===e}).fold(b([]),function(n){return[n]})})},function(n){return 1===n.rowspan},function(){return lt.getAt(t,e,0)})})},Fr=function(r,o){if(o<0||o>=r.length-1)return S.none();var n=r[o].fold(function(){var n,e,t=(n=r.slice(0,o),(e=x.call(n,0)).reverse(),e);return L(t,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return S.some({value:n,delta:0})}),e=r[o+1].fold(function(){var n=r.slice(o+1);return L(n,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return S.some({value:n,delta:1})});return n.bind(function(t){return e.map(function(n){var e=n.delta+t.delta;return Math.abs(n.value-t.value)/e})})},Lr=function(e,t){return function(n){return"rtl"===Hr(n)?t:e}},Hr=function(n){return"rtl"===ze(n,"direction")?"rtl":"ltr"};function qr(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=ze(n,r);return parseFloat(t)||0}return e},u=function(o,n){return k(n,function(n,e){var t=ze(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!v(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom;Ne(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:u,max:function(n,e,t){var r=u(n,t);return r<e?e-r:0}}}var Vr,Ur,$r,Gr,Kr=qr("height",function(n){var e=n.dom;return he(n)?e.getBoundingClientRect().height:e.offsetHeight}),Xr=function(n){return Kr.get(n)},Yr=function(n){return Kr.getOuter(n)},Jr=function(t,r){return{left:t,top:r,translate:function(n,e){return Jr(t+n,r+e)}}},Qr=Jr,Zr=function(n,e){return n!==undefined?n:e!==undefined?e:0},no=function(n){var e=n.dom.ownerDocument,t=e.body,r=e.defaultView,o=e.documentElement;if(t===n.dom)return Qr(t.offsetLeft,t.offsetTop);var u=Zr(null==r?void 0:r.pageYOffset,o.scrollTop),i=Zr(null==r?void 0:r.pageXOffset,o.scrollLeft),c=Zr(o.clientTop,t.clientTop),a=Zr(o.clientLeft,t.clientLeft);return eo(n).translate(i-a,u-c)},eo=function(n){var e,t=n.dom,r=t.ownerDocument.body;return r===t?Qr(r.offsetLeft,r.offsetTop):he(n)?(e=t.getBoundingClientRect(),Qr(e.left,e.top)):Qr(0,0)},to=qr("width",function(n){return n.dom.offsetWidth}),ro=function(n){return to.get(n)},oo=function(n){return to.getOuter(n)},uo=function(n,e){return{row:n,y:e}},io=function(n,e){return{col:n,x:e}},co=function(n){return no(n).left+oo(n)},ao=function(n){return no(n).left},lo=function(n,e){return io(n,ao(e))},fo=function(n,e){return io(n,co(e))},so=function(n){return no(n).top},mo=function(n,e){return uo(n,so(e))},go=function(n,e){return uo(n,so(e)+Yr(e))},po=function(t,e,r){if(0===r.length)return[];var n=B(r.slice(1),function(n,e){return n.map(function(n){return t(e,n)})}),o=r[r.length-1].map(function(n){return e(r.length-1,n)});return n.concat([o])},ho={delta:u,positions:function(n){return po(mo,go,n)},edge:so},vo=Lr({delta:u,edge:ao,positions:function(n){return po(lo,fo,n)}},{delta:function(n){return-n},edge:co,positions:function(n){return po(fo,lo,n)}}),bo={delta:function(n,e){return vo(e).delta(n,e)},positions:function(n,e){return vo(e).positions(n,e)},edge:function(n){return vo(n).edge(n)}},wo={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},yo=(Ur="[eE][+-]?[0-9]+",Gr=["Infinity",(Vr="[0-9]+")+"\\."+($r=function(n){return"(?:"+n+")?"})(Vr)+$r(Ur),"\\."+Vr+$r(Ur),Vr+$r(Ur)].join("|"),new RegExp("^([+-]?(?:"+Gr+"))(.*)$")),Co=function(n,o){return S.from(yo.exec(n)).bind(function(n){var e,t=Number(n[1]),r=n[2];return e=r,O(o,function(n){return O(wo[n],function(n){return e===n})})?S.some({value:t,unit:r}):S.none()})},So=function(){var n=Tn().browser;return n.isIE()||n.isEdge()},xo=function(n,e,t){return r=ze(n,e),o=t,u=parseFloat(r),isNaN(u)?o:u;var r,o,u},To=function(n){return So()?(t=(e=n).dom.getBoundingClientRect().height,"border-box"===ze(e,"box-sizing")?t:t-xo(e,"padding-top",0)-xo(e,"padding-bottom",0)-(xo(e,"border-top-width",0)+xo(e,"border-bottom-width",0))):xo(n,"height",Xr(n));var e,t},Ro=function(n){return So()?(t=(e=n).dom.getBoundingClientRect().width,"border-box"===ze(e,"box-sizing")?t:t-xo(e,"padding-left",0)-xo(e,"padding-right",0)-(xo(e,"border-left-width",0)+xo(e,"border-right-width",0))):xo(n,"width",ro(n));var e,t},Do=/(\d+(\.\d+)?)%/,Oo=/(\d+(\.\d+)?)px|em/,Ao=function(n,e){var t,r=(t=n,S.from(t.dom.offsetParent).map(Dn.fromDom).getOr(ve(ee(n))));return e(n)/e(r)*100},Bo=function(n,e){_e(n,"width",e+"px")},Eo=function(n,e){_e(n,"width",e+"%")},Io=function(n,e){_e(n,"height",e+"px")},Po=function(n,e,t,r){var o,u,i,c,a,l=parseInt(n,10);return tn(n,"%")&&"table"!==Gn(e)?(u=l,i=t,c=r,a=et(o=e).map(function(n){var e=i(n);return Math.floor(u/100*e)}).getOr(u),c(o,a),a):l},ko=function(n){var e,t=Le(e=n,"height").getOrThunk(function(){return To(e)+"px"});return t?Po(t,n,Xr,Io):Xr(n)},Mo=function(n){return Le(n,"width").fold(function(){return S.from(Ie(n,"width"))},function(n){return S.some(n)})},No=function(n,e){return n/e.pixelWidth()*100},jo=function(e,t){return Mo(e).fold(function(){var n=ro(e);return No(n,t)},function(n){return function(n,e,t){var r=Do.exec(e);if(null!==r)return parseFloat(r[1]);var o=Ro(n);return No(o,t)}(e,n,t)})},_o=function(e,t){return Mo(e).fold(function(){return Ro(e)},function(n){return function(n,e,t){var r=Oo.exec(e);if(null!==r)return parseInt(r[1],10);var o=Do.exec(e);if(null===o)return Ro(n);var u=parseFloat(o[1]);return u/100*t.pixelWidth()}(e,n,t)})},Wo=function(n){return t="rowspan",ko(e=n)/Ve(e,t);var e,t},zo=function(n,e,t){_e(n,"width",e+t)},Fo=function(n){return Ao(n,ro)+"%"},Lo=b(Do),Ho=b(Oo),qo=function(n,e,t){return Le(n,e).fold(function(){return t(n)+"px"},function(n){return n})},Vo=function(n,e){return qo(n,"width",function(n){return _o(n,e)})},Uo=function(n){return qo(n,"height",Wo)},$o=function(n,t,r,o){var e,u=lt.hasColumns(n)?(e=n,B(lt.justColumns(e),function(n){return S.from(n.element)})):_r(n),i=B(u,function(n){return n.map(bo.edge)});return B(u,function(n,e){return n.filter(d(Ue)).fold(function(){var n=Fr(i,e);return r(n)},function(n){return t(n,o)})})},Go=function(n){return n.map(function(n){return n+"px"}).getOr("")},Ko=function(n,e){return $o(n,jo,function(n){return n.fold(function(){return e.minCellWidth()},function(n){return n/e.pixelWidth()*100})},e)},Xo=function(n,e){return $o(n,_o,function(n){return n.getOrThunk(e.minCellWidth)},e)},Yo=function(n,e,t,r){var o=zr(n),u=B(o,function(n){return n.map(e.edge)});return B(o,function(n,e){return n.filter(d($e)).fold(function(){var n=Fr(u,e);return r(n)},function(n){return t(n)})})},Jo=function(n,e,t){for(var r=0,o=n;o<e;o++)r+=t[o]!==undefined?t[o]:0;return r},Qo=function(n,e){return lt.hasColumns(n)?(u=n,i=e,c=lt.justColumns(u),B(c,function(n,e){return{element:n.element,width:i[e],colspan:n.colspan}})):(t=n,r=e,o=lt.justCells(t),B(o,function(n){var e=Jo(n.column,n.column+n.colspan,r);return{element:n.element,width:e,colspan:n.colspan}}));var t,r,o,u,i,c},Zo=function(n,e,t){var r=Qo(n,e);E(r,function(n){t.setElementWidth(n.element,n.width)})},nu=function(n,e,t,r,o){var u=lt.fromTable(n),i=o.getCellDelta(e),c=o.getWidths(u,o),a=t===u.grid.columns-1,l=r.clampTableDelta(c,t,i,o.minCellWidth(),a),f=jr(c,t,l,o,r),s=B(f,function(n,e){return n+c[e]});Zo(u,s,o),r.resizeTable(o.adjustTableWidth,l,a)},eu=function(n,t,r,e){var o,u,i,c,a=lt.fromTable(n),l=Yo(a,e,Wo,function(n){return n.getOrThunk(Xe)}),f=B(l,function(n,e){return r===e?Math.max(t+n,Xe()):n}),s=(o=a,u=f,i=lt.justCells(o),B(i,function(n){var e=Jo(n.row,n.row+n.rowspan,u);return{element:n.element,height:e,rowspan:n.rowspan}})),d=(c=f,B(a.all,function(n,e){return{element:n.element,height:c[e]}}));E(d,function(n){Io(n.element,n.height)}),E(s,function(n){Io(n.element,n.height)});var m=P(f,function(n,e){return n+e},0);Io(n,m)},tu=function(n){return B(n,b(0))},ru=function(n,e,t,r,o){return o(n.slice(0,e)).concat(r).concat(o(n.slice(t)))},ou=function(i){return function(n,e,t,r){if(i(t)){var o=Math.max(r,n[e]-Math.abs(t)),u=Math.abs(o-n[e]);return 0<=t?u:-u}return t}},uu=ou(function(n){return n<0}),iu=ou(C),cu=function(){var f=function(n,t,e,r){var o=(100+e)/100,u=Math.max(r,(n[t]+e)/o);return B(n,function(n,e){return(e===t?u:n/o)-n})},c=function(n,e,t,r,o,u){return u?f(n,e,r,o):(a=t,l=uu(i=n,c=e,r,o),ru(i,c,a+1,[l,0],tu));var i,c,a,l};return{resizeTable:function(n,e){return n(e)},clampTableDelta:uu,calcLeftEdgeDeltas:c,calcMiddleDeltas:function(n,e,t,r,o,u,i){return c(n,t,r,o,u,i)},calcRightEdgeDeltas:function(n,e,t,r,o,u){if(u)return f(n,t,r,o);var i=uu(n,t,r,o);return tu(n.slice(0,t)).concat([i])}}},au=function(n){var e=S.from(n.dom.documentElement).map(Dn.fromDom).getOr(n);return{parent:b(e),view:b(n),origin:b(Qr(0,0))}},lu=function(n,e){return{parent:b(e),view:b(n),origin:b(Qr(0,0))}},fu=Jt([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),su=function(n,e,t){var r=t.substring(0,t.length-n.length),o=parseFloat(r);return r===o.toString()?e(o):fu.invalid(t)},du=H(H({},fu),{from:function(n){return tn(n,"%")?su("%",fu.percent,n):tn(n,"px")?su("px",fu.pixels,n):fu.invalid(n)}}),mu=function(n,r,o){return n.fold(function(){return r},function(n){return t=(e=n)/o,B(r,function(n){return du.from(n).fold(function(){return n},function(n){return n*t+"px"},function(n){return n/100*e+"px"})});var e,t},function(n){return e=o,B(r,function(n){return du.from(n).fold(function(){return n},function(n){return n/e*100+"%"},function(n){return n+"%"})});var e})},gu=function(n,e,t){var r,o,u,i=du.from(t),c=W(n,function(n){return"0px"===n})?(r=i,o=n.length,u=r.fold(function(){return b("")},function(n){return b(n/o+"px")},function(){return b(100/o+"%")}),A(o,u)):mu(i,n,e);return hu(c)},pu=function(n,e){return 0===n.length?e:P(n,function(n,e){return du.from(e).fold(b(0),u,u)+n},0)},hu=function(n){if(0===n.length)return n;var e,t,r=P(n,function(n,e){var t=du.from(e).fold(function(){return{value:e,remainder:0}},function(n){return e=n,t="px",{value:(r=Math.floor(e))+t,remainder:e-r};var e,t,r},function(n){return{value:n+"%",remainder:0}});return{output:[t.value].concat(n.output),remainder:n.remainder+t.remainder}},{output:[],remainder:0}),o=r.output;return o.slice(0,o.length-1).concat([(e=o[o.length-1],t=Math.round(r.remainder),du.from(e).fold(b(e),function(n){return n+t+"px"},function(n){return n+t+"%"}))])},vu=du.from,bu=function(n){return vu(n).fold(b("px"),b("px"),b("%"))},wu=function(l,n,e,f){var s=lt.fromTable(l),a=s.all,d=lt.justCells(s),m=lt.justColumns(s);n.each(function(n){var r,o,u,i,e=bu(n),t=ro(l),c=$o(s,Vo,Go,f),a=gu(c,t,n);lt.hasColumns(s)?(u=a,i=e,E(m,function(n,e){var t=pu([u[e]],Ke());_e(n.element,"width",t+i)})):(r=a,o=e,E(d,function(n){var e=r.slice(n.column,n.colspan+n.column),t=pu(e,Ke());_e(n.element,"width",t+o)})),_e(l,"width",n)}),e.each(function(n){var r,e,o,t=bu(n),u=Xr(l),i=Yo(s,ho,Uo,Go),c=gu(i,u,n);r=c,e=a,o=t,E(d,function(n){var e=r.slice(n.row,n.rowspan+n.row),t=pu(e,Xe());_e(n.element,"height",t+o)}),E(e,function(n,e){_e(n.element,"height",r[e])}),_e(l,"height",n)})},yu=function(n){return Mo(n).exists(function(n){return Do.test(n)})},Cu=function(n){return Mo(n).exists(function(n){return Oo.test(n)})},Su=function(n){return Mo(n).isNone()},xu=Fo,Tu=function(n){return lt.fromTable(n).grid},Ru=function(e){var o=[];return{bind:function(n){if(n===undefined)throw new Error("Event bind error: undefined handler");o.push(n)},unbind:function(e){o=I(o,function(n){return n!==e})},trigger:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r={};E(e,function(n,e){r[n]=t[e]}),E(o,function(n){n(r)})}}},Du=function(n){return{registry:jn(n,function(n){return{bind:n.bind,unbind:n.unbind}}),trigger:jn(n,function(n){return n.trigger})}},Ou=function(n){return n.slice(0).sort()},Au=function(r,o,u){if(0===o.length)throw new Error("You must specify at least one required field.");var t;return function(e,n){if(!l(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");E(n,function(n){if(!p(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}("required",o),t=Ou(o),M(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(e){var t=kn(e);W(o,function(n){return D(t,n)})||function(n,e){throw new Error("All required keys ("+Ou(n).join(", ")+") were not specified. Specified keys were: "+Ou(e).join(", ")+".")}(o,t),r(o,t);var n=I(o,function(n){return!u.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+Ou(n).join(", ")+") were not.")}(n,u.label),e}},Bu=function(e,n){var t=I(n,function(n){return!D(e,n)});0<t.length&&function(n){throw new Error("Unsupported keys for object: "+Ou(n).join(", "))}(t)},Eu=function(n){return Au(Bu,n,{validate:h,label:"function"})},Iu=Eu(["compare","extract","mutate","sink"]),Pu=Eu(["element","start","stop","destroy"]),ku=Eu(["forceDrop","drop","move","delayDrop"]);function Mu(){var u=S.none(),i=Du({move:Ru(["info"])});return{onEvent:function(n,o){o.extract(n).each(function(n){var e,t,r;(e=o,t=n,r=u.map(function(n){return e.compare(n,t)}),u=S.some(t),r).each(function(n){i.trigger.move(n)})})},reset:function(){u=S.none()},events:i.registry}}function Nu(){var n={onEvent:y,reset:y},e=Mu(),t=n;return{on:function(){t.reset(),t=e},off:function(){t.reset(),t=n},isOn:function(){return t===e},onEvent:function(n,e){t.onEvent(n,e)},events:e.events}}var ju=function(e,t,n){var r,o,u,i=!1,c=Du({start:Ru([]),stop:Ru([])}),a=Nu(),l=function(){d.stop(),a.isOn()&&(a.off(),c.trigger.stop())},f=(r=l,o=200,u=null,{cancel:function(){null!==u&&(clearTimeout(u),u=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==u&&clearTimeout(u),u=setTimeout(function(){r.apply(null,n),u=null},o)}});a.events.move.bind(function(n){t.mutate(e,n.info)});var s=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i&&t.apply(null,n)}},d=t.sink(ku({forceDrop:l,drop:s(l),move:s(function(n){f.cancel(),a.onEvent(n,t)}),delayDrop:s(f.throttle)}),n);return{element:d.element,go:function(n){d.start(n),a.on(),c.trigger.start()},on:function(){i=!0},off:function(){i=!1},destroy:function(){d.destroy()},events:c.registry}},_u=function(n){var t,r,e=Dn.fromDom(ge(n).getOr(n.target)),o=function(){return n.stopPropagation()},u=function(){return n.preventDefault()},i=(t=u,r=o,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))});return{target:e,x:n.clientX,y:n.clientY,stop:o,prevent:u,kill:i,raw:n}},Wu=function(n,e,t,r,o){var u,i,c=(u=t,i=r,function(n){u(n)&&i(_u(n))});return n.dom.addEventListener(e,c,o),{unbind:w(zu,n,e,c,o)}},zu=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},Fu=C,Lu=function(n,e,t){return Wu(n,e,Fu,t,!1)},Hu=_u,qu=function(n,e){var t=Ie(n,e);return t===undefined||""===t?[]:t.split(" ")},Vu=function(n){return n.dom.classList!==undefined},Uu=function(n,e){return o=e,u=qu(t=n,r="class").concat([o]),Be(t,r,u.join(" ")),!0;var t,r,o,u},$u=function(n,e){return o=e,0<(u=I(qu(t=n,r="class"),function(n){return n!==o})).length?Be(t,r,u.join(" ")):ke(t,r),!1;var t,r,o,u},Gu=function(n,e){Vu(n)?n.dom.classList.add(e):Uu(n,e)},Ku=function(n){0===(Vu(n)?n.dom.classList:qu(n,"class")).length&&ke(n,"class")},Xu=function(n,e){return Vu(n)&&n.dom.classList.contains(e)},Yu=function(n){var e=n.replace(/\./g,"-");return{resolve:function(n){return e+"-"+n}}},Ju=Yu("ephox-dragster").resolve,Qu=Iu({compare:function(n,e){return Qr(e.left-n.left,e.top-n.top)},extract:function(n){return S.some(Qr(n.x,n.y))},sink:function(n,e){var t=function(n){var e=H({layerClass:Ju("blocker")},n),t=Dn.fromTag("div");Be(t,"role","presentation"),We(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Gu(t,Ju("blocker")),Gu(t,e.layerClass);return{element:function(){return t},destroy:function(){Ot(t)}}}(e),r=Lu(t.element(),"mousedown",n.forceDrop),o=Lu(t.element(),"mouseup",n.drop),u=Lu(t.element(),"mousemove",n.move),i=Lu(t.element(),"mouseout",n.delayDrop);return Pu({element:t.element,start:function(n){St(n,t.element())},stop:function(){Ot(t.element())},destroy:function(){t.destroy(),o.unbind(),u.unbind(),i.unbind(),r.unbind()}})},mutate:function(n,e){n.mutate(e.left,e.top)}}),Zu=function(n){return"true"===Ie(n,"contenteditable")},ni=Yu("ephox-snooker").resolve,ei=function(){var t,r=Du({drag:Ru(["xDelta","yDelta","target"])}),o=S.none(),n={mutate:function(n,e){t.trigger.drag(n,e)},events:(t=Du({drag:Ru(["xDelta","yDelta"])})).registry};n.events.drag.bind(function(e){o.each(function(n){r.trigger.drag(e.xDelta,e.yDelta,n)})});return{assign:function(n){o=S.some(n)},get:function(){return o},mutate:n.mutate,events:r.registry}},ti=ni("resizer-bar"),ri=ni("resizer-rows"),oi=ni("resizer-cols"),ui=function(n){var e=Ce(n.parent(),"."+ti);E(e,Ot)},ii=function(t,n,r){var o=t.origin();E(n,function(n){n.each(function(n){var e=r(o,n);Gu(e,ti),St(t.parent(),e)})})},ci=function(n,e,l,f){ii(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.col,r=e.x-n.left,o=l.top-n.top,u=7,i=f,c=Dn.fromTag("div"),We(c,{position:"absolute",left:r-u/2+"px",top:o+"px",height:i+"px",width:u+"px"}),Ee(c,{"data-column":t,role:"presentation"}),c);return Gu(a,oi),a})},ai=function(n,e,l,f){ii(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.row,r=l.left-n.left,o=e.y-n.top,u=f,i=7,c=Dn.fromTag("div"),We(c,{position:"absolute",left:r+"px",top:o-i/2+"px",height:i+"px",width:u+"px"}),Ee(c,{"data-row":t,role:"presentation"}),c);return Gu(a,ri),a})},li=function(n,e){ui(n);var t=lt.fromTable(e);!function(n,e,t,r){var o=no(e),u=0<t.length?ho.positions(t,e):[];ai(n,u,o,oo(e));var i=0<r.length?bo.positions(r,e):[];ci(n,i,o,Yr(e))}(n,e,zr(t),_r(t))},fi=function(n,e){var t=Ce(n.parent(),"."+ti);E(t,e)},si=function(n){fi(n,function(n){_e(n,"display","none")})},di=function(n){fi(n,function(n){_e(n,"display","block")})},mi=ni("resizer-bar-dragging"),gi=function(o){var t=ei(),r=function(n,e){void 0===e&&(e={});var t=e.mode!==undefined?e.mode:Qu;return ju(n,t,e)}(t,{}),e=S.none(),n=function(n,e){return S.from(Ie(n,e))};t.events.drag.bind(function(t){n(t.target,"data-row").each(function(n){var e=Ge(t.target,"top");_e(t.target,"top",e+t.yDelta+"px")}),n(t.target,"data-column").each(function(n){var e=Ge(t.target,"left");_e(t.target,"left",e+t.xDelta+"px")})});var u=function(n,e){return Ge(n,e)-qe(n,"data-initial-"+e,0)};r.events.stop.bind(function(){t.get().each(function(r){e.each(function(t){n(r,"data-row").each(function(n){var e=u(r,"top");ke(r,"data-initial-top"),s.trigger.adjustHeight(t,e,parseInt(n,10))}),n(r,"data-column").each(function(n){var e=u(r,"left");ke(r,"data-initial-left"),s.trigger.adjustWidth(t,e,parseInt(n,10))}),li(o,t)})})});var i=function(n,e){s.trigger.startAdjust(),t.assign(n),Be(n,"data-initial-"+e,Ge(n,e)),Gu(n,mi),_e(n,"opacity","0.2"),r.go(o.parent())},c=Lu(o.parent(),"mousedown",function(n){var e,t;e=n.target,Xu(e,ri)&&i(n.target,"top"),t=n.target,Xu(t,oi)&&i(n.target,"left")}),a=function(n){return Bn(n,o.view())},l=function(n){return Oe(n,"table",a).filter(function(n){return Oe(n,"[contenteditable]",a).exists(Zu)})},f=Lu(o.view(),"mouseover",function(n){l(n.target).fold(function(){he(n.target)&&ui(o)},function(n){e=S.some(n),li(o,n)})}),s=Du({adjustHeight:Ru(["table","delta","row"]),adjustWidth:Ru(["table","delta","column"]),startAdjust:Ru([])});return{destroy:function(){c.unbind(),f.unbind(),r.destroy(),ui(o)},refresh:function(n){li(o,n)},on:r.on,off:r.off,hideBars:w(si,o),showBars:w(di,o),events:s.registry}},pi=function(n,o,u){var r=ho,i=bo,e=gi(n),c=Du({beforeResize:Ru(["table","type"]),afterResize:Ru(["table","type"]),startDrag:Ru([])});return e.events.adjustHeight.bind(function(n){var e=n.table;c.trigger.beforeResize(e,"row");var t=r.delta(n.delta,e);eu(e,t,n.row,r),c.trigger.afterResize(e,"row")}),e.events.startAdjust.bind(function(n){c.trigger.startDrag()}),e.events.adjustWidth.bind(function(n){var e=n.table;c.trigger.beforeResize(e,"col");var t=i.delta(n.delta,e),r=u(e);nu(e,t,n.column,o,r),c.trigger.afterResize(e,"col")}),{on:e.on,off:e.off,hideBars:e.hideBars,showBars:e.showBars,destroy:e.destroy,events:c.registry}},hi=function(n,e){return n.fire("newrow",{node:e})},vi=function(n,e){return n.fire("newcell",{node:e})},bi=function(n,e,t,r,o){n.fire("TableSelectionChange",{cells:e,start:t,finish:r,otherCells:o})},wi=function(n){n.fire("TableSelectionClear")},yi={"border-collapse":"collapse",width:"100%"},Ci={border:"1"},Si="preservetable",xi=function(n){return n.getParam("table_sizing_mode","auto")},Ti=function(n){return n.getParam("table_responsive_width")},Ri=function(n){return n.getParam("table_default_attributes",Ci,"object")},Di=function(n){return n.getParam("table_default_styles",function(n){if(ki(n)){var e=n.getBody().offsetWidth;return H(H({},yi),{width:e+"px"})}return Mi(n)?Wn(yi,function(n,e){return"width"!==e}):yi}(n),"object")},Oi=function(n){return n.getParam("table_tab_navigation",!0,"boolean")},Ai=function(n){return n.getParam("table_cell_advtab",!0,"boolean")},Bi=function(n){return n.getParam("table_row_advtab",!0,"boolean")},Ei=function(n){return n.getParam("table_advtab",!0,"boolean")},Ii=function(n){return n.getParam("table_style_by_css",!1,"boolean")},Pi=function(n){return"relative"===xi(n)||!0===Ti(n)},ki=function(n){return"fixed"===xi(n)||!1===Ti(n)},Mi=function(n){return"responsive"===xi(n)},Ni=function(n){var e="section",t=n.getParam("table_header_type",e,"string");return D(["section","cells","sectionCells","auto"],t)?t:e},ji=function(n){var e=n.getParam("table_column_resizing",Si,"string");return M(["preservetable","resizetable"],function(n){return n===e}).getOr(Si)},_i=function(n){return"preservetable"===ji(n)},Wi=function(n){var e=n.getParam("table_clone_elements");return p(e)?S.some(e.split(/[ ,]/)):Array.isArray(e)?S.some(e):S.none()},zi=function(n){return n.nodeName.toLowerCase()},Fi=function(n){return Dn.fromDom(n.getBody())},Li=function(n){return n.getBoundingClientRect().width},Hi=function(n){return n.getBoundingClientRect().height},qi=function(e){return function(n){return Bn(n,Fi(e))}},Vi=function(n){return/^\d+(\.\d+)?$/.test(n)?n+"px":n},Ui=function(n){ke(n,"data-mce-style");var e=function(n){return ke(n,"data-mce-style")};E(Ze(n),e),E(nt(n),e)},$i=function(n,e){var t=n.dom.getStyle(e,"width")||n.dom.getAttrib(e,"width");return S.from(t).filter(on)},Gi=function(n){return/^(\d+(\.\d+)?)%$/.test(n)},Ki=function(n){return Dn.fromDom(n.selection.getStart())},Xi=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}},Yi=function(n){var e=function(){return ro(n)},t=b(0);return{width:e,pixelWidth:e,getWidths:Xo,getCellDelta:t,singleColumnWidth:b([0]),minCellWidth:t,setElementWidth:y,adjustTableWidth:y,isRelative:!0,label:"none"}},Ji=function(n,r){var o=Xi(parseFloat(n)),u=Xi(ro(r));return{width:o.get,pixelWidth:u.get,getWidths:Ko,getCellDelta:function(n){return n/u.get()*100},singleColumnWidth:function(n,e){return[100-n]},minCellWidth:function(){return Ke()/u.get()*100},setElementWidth:Eo,adjustTableWidth:function(n){var e=o.get(),t=e+n/100*e;Eo(r,t),o.set(t),u.set(ro(r))},isRelative:!0,label:"percent"}},Qi=function(n,t){var r=Xi(n),o=r.get;return{width:o,pixelWidth:o,getWidths:Xo,getCellDelta:u,singleColumnWidth:function(n,e){return[Math.max(Ke(),n+e)-n]},minCellWidth:Ke,setElementWidth:Bo,adjustTableWidth:function(n){var e=o()+n;Bo(t,e),r.set(e)},isRelative:!1,label:"pixel"}},Zi=function(e){return Mo(e).fold(function(){return Yi(e)},function(n){return function(n,e){var t=Lo().exec(e);if(null!==t)return Ji(t[1],n);var r=Ho().exec(e);if(null!==r){var o=parseInt(r[1],10);return Qi(o,n)}var u=ro(n);return Qi(u,n)}(e,n)})},nc=Qi,ec=Ji,tc=function(n,e){if(Pi(n)){var t=$i(n,e.dom).filter(Gi).getOrThunk(function(){return xu(e)});return ec(t,e)}return ki(n)?nc(ro(e),e):Zi(e)},rc=function(n){ke(n,"width")},oc=function(n,e){var t=ro(n)+"px";wu(n,S.some(t),S.none(),e),rc(n)},uc=function(n,e){var t,r,o,u=tc(n,e);r=u,o=Fo(t=e),wu(t,S.some(o),S.none(),r),rc(t)},ic=function(n,e){var t=tc(n,e);oc(e,t)},cc=function(n){He(n,"width");var e=nt(n),t=0<e.length?e:Ze(n);E(t,function(n){He(n,"width"),rc(n)}),rc(n)},ac=function(){var n=Dn.fromTag("div");return We(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),St(ve(Dn.fromDom(document)),n),n},lc=function(f){var s,d,a=S.none(),u=S.none(),i=S.none(),o=function(n){return"TABLE"===n.nodeName},n=function(){return u},m=function(n){return tc(f,n)},g=function(){return _i(f)?{resizeTable:function(n,e,t){t&&n(e)},clampTableDelta:function(n,e,t,r,o){if(o){if(0<=t)return t;var u=k(n,function(n,e){return n+e-r},0);return Math.max(-u,t)}return uu(n,e,t,r)},calcLeftEdgeDeltas:i=function(n,e,t,r,o){var u=iu(n,0<=r?t:e,r,o);return ru(n,e,t+1,[u,-u],tu)},calcMiddleDeltas:function(n,e,t,r,o,u){return i(n,t,r,o,u)},calcRightEdgeDeltas:function(n,e,t,r,o,u){if(u)return tu(n);var i=r/n.length;return B(n,b(i))}}:cu();var i},c=function(n,e,t){var r,o,u,i,c,a,l=tn(e,"e");t!==s&&""!==d?(_e(n,"width",d),r=g(),o=m(n),u=_i(f)||l?Tu(n).columns-1:0,nu(n,t-s,u,r,o)):Gi(d)&&(i=parseFloat(d.replace("%","")),_e(n,"width",t*i/s+"%")),/^(\d+(\.\d+)?)px$/.test(d)&&(c=n,a=lt.fromTable(c),lt.hasColumns(a)||E(Ze(c),function(n){var e=ze(n,"width");_e(n,"width",e),ke(n,"width")}))};return f.on("init",function(){var n,e,t,r,o=(n=f).inline?lu(Fi(n),ac()):au(Dn.fromDom(n.getDoc()));i=S.some(o),r=f.getParam("object_resizing",!0),(p(r)?"table"===r:r)&&f.getParam("table_resize_bars",!0,"boolean")&&(e=g(),(t=pi(o,e,m)).on(),t.events.startDrag.bind(function(n){a=S.some(f.selection.getRng())}),t.events.beforeResize.bind(function(n){var e,t,r,o,u,i=n.table.dom;e=f,r=Li(t=i),o=Hi(i),u="bar-"+n.type,e.fire("ObjectResizeStart",{target:t,width:r,height:o,origin:u})}),t.events.afterResize.bind(function(n){var e,t,r,o,u,i=n.table,c=i.dom;Ui(i),a.each(function(n){f.selection.setRng(n),f.focus()}),e=f,r=Li(t=c),o=Hi(c),u="bar-"+n.type,e.fire("ObjectResized",{target:t,width:r,height:o,origin:u}),f.undoManager.add()}),u=S.some(t))}),f.on("ObjectResizeStart",function(n){var e,t=n.target;o(t)&&(e=Dn.fromDom(t),E(f.dom.select(".mce-clonedresizable"),function(n){f.dom.addClass(n,"mce-"+ji(f)+"-columns")}),!Cu(e)&&ki(f)?ic(f,e):!yu(e)&&Pi(f)&&uc(f,e),s=n.width,d=Mi(f)?"":$i(f,t).getOr(""))}),f.on("ObjectResized",function(n){var e,t,r=n.target;o(r)&&(e=Dn.fromDom(r),""===d&&uc(f,e),t=n.origin,en(t,"corner-")&&c(e,t,n.width),Ui(e))}),f.on("SwitchMode",function(){u.each(function(n){f.mode.isReadOnly()?n.hideBars():n.showBars()})}),{lazyResize:n,lazyWire:function(){return i.getOr(au(Dn.fromDom(f.getBody())))},destroy:function(){u.each(function(n){n.destroy()}),i.each(function(n){var e;e=n,f.inline&&Ot(e.parent())})}}},fc=function(n,e){return{element:n,offset:e}},sc=function(e,n,t){return e.property().isText(n)&&0===e.property().getText(n).trim().length||e.property().isComment(n)?t(n).bind(function(n){return sc(e,n,t).orThunk(function(){return S.some(n)})}):S.none()},dc=function(n,e){return n.property().isText(e)?n.property().getText(e).length:n.property().children(e).length},mc=function(n,e){var t=sc(n,e,n.query().prevSibling).getOr(e);if(n.property().isText(t))return fc(t,dc(n,t));var r=n.property().children(t);return 0<r.length?mc(n,r[r.length-1]):fc(t,dc(n,t))},gc=mc,pc=_t(),hc=function(t,r){Mo(t).bind(function(n){return Co(n,["fixed","relative","empty"])}).each(function(n){var e=n.value/2;zo(t,e,n.unit),zo(r,e,n.unit)})},vc=function(n,e,t){n.cells[e]=t},bc=function(n,e){return $n(e,n.section)},wc=function(n,e){var t=n.cells,r=B(t,e);return $n(r,n.section)},yc=function(n,e){return n.cells[e]},Cc=function(n,e){return yc(n,e).element},Sc=function(n){return n.cells.length},xc=function(n){var e=function(n,e){for(var t=[],r=[],o=0,u=n.length;o<u;o++){var i=n[o];(e(i,o)?t:r).push(i)}return{pass:t,fail:r}}(n,function(n){return"colgroup"===n.section});return{rows:e.fail,cols:e.pass}},Tc=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Rc=function(n,e,t,r){t===r?ke(n,e):Be(n,e,t)},Dc=function(n,e,t){F(ye(n,e)).fold(function(){return Ct(n,t)},function(n){return yt(n,t)})},Oc=function(c,n){var t=[],r=[],a=function(n){return B(n,function(n){n.isNew&&t.push(n.element);var e=n.element;return Dt(e),E(n.cells,function(n){n.isNew&&r.push(n.element),Rc(n.element,"colspan",n.colspan,1),Rc(n.element,"rowspan",n.rowspan,1),St(e,n.element)}),e})},l=function(n){return _(n,function(n){return B(n.cells,function(n){return Rc(n.element,"span",n.colspan,1),n.element})})},o=function(n,e){var t,r,o,u=(o=Re(t=c,r=e).getOrThunk(function(){var n=Dn.fromTag(r,ee(t).dom);return"thead"===r?Dc(t,"caption,colgroup",n):"colgroup"===r?Dc(t,"caption",n):St(t,n),n}),Dt(o),o),i=("colgroup"===e?l:a)(n);Rt(u,i)},e=function(n,e){0<n.length?o(n,e):Re(c,e).each(Ot)},u=[],i=[],f=[],s=[];return E(n,function(n){switch(n.section){case"thead":u.push(n);break;case"tbody":i.push(n);break;case"tfoot":f.push(n);break;case"colgroup":s.push(n)}}),e(s,"colgroup"),e(u,"thead"),e(i,"tbody"),e(f,"tfoot"),{newRows:t,newCells:r}},Ac=function(n,e){if(0===n.length)return 0;var t=n[0];return N(n,function(n){return!e(t.element,n.element)}).fold(function(){return n.length},function(n){return n})},Bc=function(n,e,t,r){var o,u=n[e].cells.slice(t),i=Ac(u,r),c=(o=t,B(n,function(n){return yc(n,o)}).slice(e));return{colspan:i,rowspan:Ac(c,r)}},Ec=function(o,u){var i=B(o,function(n){return B(n.cells,f)});return B(o,function(n,r){return{details:_(n.cells,function(n,e){if(!1!==i[r][e])return[];var t=Bc(o,r,e,u);return function(n,e,t,r){for(var o=n;o<n+t;o++)for(var u=e;u<e+r;u++)i[o][u]=!0}(r,e,t.rowspan,t.colspan),[{element:n.element,rowspan:t.rowspan,colspan:t.colspan,isNew:n.isNew}]}),section:n.section}})},Ic=function(n,e,t){var r,o=[];lt.hasColumns(n)&&(r=B(lt.justColumns(n),function(n){return Un(n.element,t)}),o.push($n(r,"colgroup")));for(var u=0;u<n.grid.rows;u++){for(var i=[],c=0;c<n.grid.columns;c++){var a=lt.getAt(n,u,c).map(function(n){return Un(n.element,t)}).getOrThunk(function(){return Un(e.gap(),!0)});i.push(a)}var l=$n(i,n.all[u].section);o.push(l)}return o},Pc=function(n,r){return B(n,function(n){var e,t=(e=n.details,L(e,function(n){return re(n.element).map(function(n){var e=re(n).isNone();return Un(n,e)})}).getOrThunk(function(){return Un(r.row(),!0)}));return{element:t.element,cells:n.details,section:n.section,isNew:t.isNew}})},kc=function(n,e){var t=Ec(n,Bn);return Pc(t,e)},Mc=function(n,e){return L(n.all,function(n){return M(n.cells,function(n){return Bn(e,n.element)})})},Nc=function(a,e,l,f,s){return function(r,o,n,u,i){var c=lt.fromTable(o);return e(c,n).map(function(n){var e=Ic(c,u,!1),t=a(e,n,Bn,s(u));return{grid:kc(t.grid,u),cursor:t.cursor}}).fold(function(){return S.none()},function(n){var e=Oc(o,n.grid),t=S.from(i).getOrThunk(function(){return Zi(o)});return l(o,n.grid,t),f(o),li(r,o),S.some({cursor:n.cursor,newRows:e.newRows,newCells:e.newCells})})}},jc=function(e,n){return Qe(n.element).bind(function(n){return Mc(e,n)})},_c=function(e,n){var t=B(n.selection,function(n){return Qe(n).bind(function(n){return Mc(e,n)})}),r=Tc(t);return 0<r.length?S.some({cells:r,generators:n.generators,clipboard:n.clipboard}):S.none()},Wc=function(e,n){var t=B(n.selection,function(n){return Qe(n).bind(function(n){return Mc(e,n)})}),r=Tc(t);return 0<r.length?S.some(r):S.none()},zc=function(n,e,t,r){for(var o=xc(n).rows,u=!0,i=0;i<o.length;i++)for(var c=0;c<Sc(o[0]);c++){var a=t(Cc(o[i],c),e);!0===a&&!1===u?vc(o[i],c,Un(r(),!0)):!0===a&&(u=!1)}return n},Fc=function(n,e,u,i){var t,r,o,c=xc(n).rows;return 0<e&&e<c.length&&(t=c[e-1].cells,o=u,r=k(t,function(n,e){return O(n,function(n){return o(n.element,e.element)})?n:n.concat([e])},[]),E(r,function(r){for(var o=S.none(),n=e;n<c.length;n++)!function(t){for(var n=0;n<Sc(c[0]);n++)!function(e){var n=c[t].cells[e];u(n.element,r.element)&&(o.isNone()&&(o=S.some(i())),o.each(function(n){vc(c[t],e,Un(n,!0))}))}(n)}(n)})),n},Lc=function(t){return{is:function(n){return t===n},isValue:C,isError:f,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(n){return Lc(t)},orThunk:function(n){return Lc(t)},fold:function(n,e){return e(t)},map:function(n){return Lc(n(t))},mapError:function(n){return Lc(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return S.some(t)}}},Hc=function(t){return{is:f,isValue:f,isError:C,getOr:u,getOrThunk:function(n){return n()},getOrDie:function(){return n=String(t),function(){throw new Error(n)}();var n},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Hc(t)},mapError:function(n){return Hc(n(t))},each:y,bind:function(n){return Hc(t)},exists:f,forall:C,toOptional:S.none}},qc={value:Lc,error:Hc,fromOption:function(n,e){return n.fold(function(){return Hc(e)},Lc)}},Vc=function(n,e){return{rowDelta:0,colDelta:Sc(n[0])-Sc(e[0])}},Uc=function(n,e){return{rowDelta:n.length-e.length,colDelta:0}},$c=function(n,e,t){var r="colgroup"===e.section?t.col:t.cell;return B(n,function(){return Un(r(),!0)})},Gc=function(e,n,t){return e.concat(A(n,function(){var n=e[e.length-1];return bc(n,$c(n.cells,n,t))}))},Kc=function(n,t,r){return B(n,function(n){var e=$c(A(t,u),n,r);return bc(n,n.cells.concat(e))})},Xc=function(n,e,t){var r=e.colDelta<0?Kc:u;return(e.rowDelta<0?Gc:u)(r(n,Math.abs(e.colDelta),t),Math.abs(e.rowDelta),t)},Yc=function(n,e,t,r,o){for(var u,i,c,a,l,f=n.row,s=n.column,d=f+t.length,m=s+Sc(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){c=p,a=void 0,a=w(o,yc((u=e)[i=g],c).element),l=u[i],1<u.length&&1<Sc(l)&&(0<c&&a(Cc(l,c-1))||c<l.cells.length-1&&a(Cc(l,c+1))||0<i&&a(Cc(u[i-1],c))||i<u.length-1&&a(Cc(u[i+1],c)))&&zc(e,Cc(e[g],p),o,r.cell);var h=Cc(t[g-f],p-s),v=r.replace(h);vc(e[g],p,Un(v,!0))}return e},Jc=function(t,r,o,u,i){return function(n,e,t){if(n.row>=e.length||n.column>Sc(e[0]))return qc.error("invalid start address out of table bounds, row: "+n.row+", column: "+n.column);var r=e.slice(n.row),o=r[0].cells.slice(n.column),u=Sc(t[0]),i=t.length;return qc.value({rowDelta:r.length-i,colDelta:o.length-u})}(t,r,o).map(function(n){var e=Xc(r,n,u);return Yc(t,e,o,u,i)})},Qc=function(r,n,e,t,o){var u,i,c,a;u=n,i=r,c=o,a=t.cell,0<i&&i<u[0].cells.length&&E(u,function(n){var e=n.cells[i-1],t=n.cells[i];c(t.element,e.element)&&vc(n,i,Un(a(),!0))});var l=Uc(e,n),f=Xc(e,l,t),s=Uc(n,f),d=Xc(n,s,t);return B(d,function(n,e){var t=n.cells.slice(0,r).concat(f[e].cells).concat(n.cells.slice(r,n.cells.length));return bc(n,t)})},Zc=function(n,e,t,r,o){Fc(e,n,o,r.cell);var u=Vc(t,e),i=Xc(t,u,r),c=Vc(e,i),a=Xc(e,c,r),l=xc(a),f=l.cols,s=l.rows;return f.concat(s.slice(0,n)).concat(i).concat(s.slice(n,s.length))},na=function(n,t,e,r,o){var u=xc(n),i=u.rows,c=u.cols,a=i.slice(0,t),l=i.slice(t),f=wc(i[e],function(n,e){return 0<t&&t<i.length&&r(Cc(i[t-1],e),Cc(i[t],e))?yc(i[t],e):Un(o(n.element,r),!0)});return c.concat(a).concat([f]).concat(l)},ea=function(n,v,b,w,y){return B(n,function(n){var e,t,r,o,u,i,c,a,l,f,s,d,m,g,p=0<v&&v<Sc(n)&&w(Cc(n,v-1),Cc(n,v)),h=(t=v,r=(e=n).section,o=p,u=b,i=w,c=y,"colgroup"!==r&&o?yc(e,t):Un(c(Cc(e,u),i),!0));return l=v,f=h,s=(a=n).cells,d=s.slice(0,l),m=s.slice(l),g=d.concat([f]).concat(m),bc(a,g)})},ta=function(n,t,r,o){return B(n,function(n){return wc(n,function(n){return e=n,O(t,function(n){return r(e.element,n.element)})?Un(o(n.element,r),!0):n;var e})})},ra=function(n,e,t,r){return Cc(n[e],t)!==undefined&&0<e&&r(Cc(n[e-1],t),Cc(n[e],t))},oa=function(n,e,t){return 0<e&&t(Cc(n,e-1),Cc(n,e))},ua=function(t,r,o,n){var e=_(t,function(n,e){return ra(t,e,r,o)||oa(n,r,o)?[]:[yc(n,r)]});return ta(t,e,o,n)},ia=function(n,t,r,e){var o=xc(n).rows,u=o[t],i=_(u.cells,function(n,e){return ra(o,t,e,r)||oa(u,e,r)?[]:[n]});return ta(n,i,r,e)},ca=Eu(["cell","row","replace","gap","col","colgroup"]),aa=function(n){return{element:n,colspan:qe(n,"colspan",1),rowspan:qe(n,"rowspan",1)}},la=function(e,t){void 0===t&&(t=aa),ca(e);var r=Xi(S.none()),o=function(n){return function(n){switch(Gn(n.element)){case"col":return e.col(n);default:return e.cell(n)}}(t(n))},u=function(n){var e=o(n);return r.get().isNone()&&r.set(S.some(e)),i=S.some({item:n,replacement:e}),e},i=S.none();return{getOrInit:function(e,t){return i.fold(function(){return u(e)},function(n){return t(e,n.item)?n.replacement:u(e)})},cursor:r.get}},fa=function(c,a){return function(r){var o=Xi(S.none());ca(r);var u=[],i=function(n){var e={scope:c},t=r.replace(n,a,e);return u.push({item:n,sub:t}),o.get().isNone()&&o.set(S.some(t)),t};return{replaceOrInit:function(e,t){return r=e,o=t,M(u,function(n){return o(n.item,r)}).fold(function(){return i(e)},function(n){return t(e,n.item)?n.sub:i(e)});var r,o},cursor:o.get}}},sa=function(t){ca(t);var n=Xi(S.none());return{combine:function(e){return n.get().isNone()&&n.set(S.some(e)),function(){var n=t.cell({element:e,colspan:1,rowspan:1});return He(n,"width"),He(e,"width"),n}},cursor:n.get}},da=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],ma=_t(),ga=function(n){return e=n,t=ma.property().name(e),D(da,t);var e,t},pa=function(n){return e=n,t=ma.property().name(e),D(["ol","ul"],t);var e,t},ha=function(n){return e=n,D(["br","img","hr","input"],ma.property().name(e));var e},va=function(n){var e,u=function(n){return"br"===Gn(n)},t=function(o){return sr(o).bind(function(t){var r=ie(t).map(function(n){return!!ga(n)||!!ha(n)&&"img"!==Gn(n)}).getOr(!1);return re(t).map(function(n){return!0===r||("li"===Gn(e=n)||xe(e,pa).isSome())||u(t)||ga(n)&&!Bn(o,n)?[]:[Dn.fromTag("br")];var e})}).getOr([])},r=0===(e=_(n,function(n){var e=ce(n);return W(e,function(n){return u(n)||Qn(n)&&0===kt(n).trim().length})?[]:e.concat(t(n))})).length?[Dn.fromTag("br")]:e;Dt(n[0]),Rt(n[0],r)},ba=function(n){0===Ze(n).length&&Ot(n)},wa=function(n,e){return{grid:n,cursor:e}},ya=function(n,e,t){var r=xc(n).rows;return Ca(r,e,t).orThunk(function(){return Ca(r,0,0)})},Ca=function(n,e,t){return S.from(n[e]).bind(function(n){return S.from(n.cells[t]).bind(function(n){return S.from(n.element)})})},Sa=function(n,e,t){var r=xc(n).rows;return wa(n,Ca(r,e,t))},xa=function(n){return k(n,function(n,e){return O(n,function(n){return n.row===e.row})?n:n.concat([e])},[]).sort(function(n,e){return n.row-e.row})},Ta=function(n){return k(n,function(n,e){return O(n,function(n){return n.column===e.column})?n:n.concat([e])},[]).sort(function(n,e){return n.column-e.column})},Ra=function(n,e,t){var r,o,u=(r=n,o=t.section,rt(r,function(){return o})),i=lt.generate(u);return Ic(i,e,!0)},Da=function(n,e){var t=I(n,e);return 0===t.length?S.some("td"):t.length===n.length?S.some("th"):S.none()},Oa=function(n,e,t){var r=lt.generate(e),o=t.getWidths(r,t);Zo(r,o,t)},Aa=Nc(function(n,e,t,r){var o=e[0].row,u=e[0].row,i=xa(e),c=k(i,function(n,e){return na(n,u,o,t,r.getOrInit)},n);return Sa(c,u,e[0].column)},Wc,y,y,la),Ba=Nc(function(n,e,t,r){var o=xa(e),u=o[o.length-1].row,i=o[o.length-1].row+o[o.length-1].rowspan,c=k(o,function(n,e){return na(n,i,u,t,r.getOrInit)},n);return Sa(c,i,e[0].column)},Wc,y,y,la),Ea=Nc(function(n,e,t,r){var o=Ta(e),u=o[0].column,i=o[0].column,c=k(o,function(n,e){return ea(n,i,u,t,r.getOrInit)},n);return Sa(c,e[0].row,i)},Wc,Oa,y,la),Ia=Nc(function(n,e,t,r){var o=e[e.length-1].column,u=e[e.length-1].column+e[e.length-1].colspan,i=Ta(e),c=k(i,function(n,e){return ea(n,u,o,t,r.getOrInit)},n);return Sa(c,e[0].row,u)},Wc,Oa,y,la),Pa=Nc(function(n,e,t,r){var o,u,i,c,a=Ta(e),l=(o=n,u=a[0].column,i=a[a.length-1].column,c=B(o,function(n){var e=n.cells.slice(0,u).concat(n.cells.slice(i+1));return $n(e,n.section)}),I(c,function(n){return 0<n.cells.length})),f=ya(l,e[0].row,e[0].column);return wa(l,f)},Wc,Oa,ba,la),ka=Nc(function(n,e,t,r){var o,u,i,c,a,l=xa(e),f=(o=n,u=l[0].row,i=l[l.length-1].row,c=xc(o),a=c.rows,c.cols.concat(a.slice(0,u)).concat(a.slice(i+1))),s=ya(f,e[0].row,e[0].column);return wa(f,s)},Wc,y,ba,la),Ma=Nc(function(n,e,t,r){var o=ua(n,e.column,t,r.replaceOrInit);return Sa(o,e.row,e.column)},jc,y,y,fa("row","th")),Na=Nc(function(n,e,t,r){var o=ua(n,e.column,t,r.replaceOrInit);return Sa(o,e.row,e.column)},jc,y,y,fa(null,"td")),ja=(Nc(function(n,e,t,r){var o=ia(n,e.row,t,r.replaceOrInit);return Sa(o,e.row,e.column)},jc,y,y,fa("col","th")),Nc(function(n,e,t,r){var o=ia(n,e.row,t,r.replaceOrInit);return Sa(o,e.row,e.column)},jc,y,y,fa(null,"td")),Nc(function(n,e,t,r){var o=e.cells;va(o);var u=function(n,e,t){var r=xc(n).rows;if(0===r.length)return n;for(var o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++)vc(r[o],u,Un(t(),!1));return n}(n,e.bounds,b(o[0]));return wa(u,S.from(o[0]))},function(n,e){return e.mergable},y,y,sa)),_a=Nc(function(n,e,t,r){var o=P(e,function(n,e){return zc(n,e,t,r.combine(e))},n);return wa(o,S.from(e[0]))},function(n,e){return e.unmergable},Oa,y,sa),Wa=Nc(function(n,t,e,r){var o,u,i,c=(o=t.clipboard,u=t.generators,i=lt.fromTable(o),Ic(i,u,!0)),a={row:t.row,column:t.column};return Jc(a,n,c,t.generators,e).fold(function(){return wa(n,S.some(t.element))},function(n){var e=ya(n,t.row,t.column);return wa(n,e)})},function(e,t){return Qe(t.element).bind(function(n){return Mc(e,n).map(function(n){return H(H({},n),{generators:t.generators,clipboard:t.clipboard})})})},Oa,y,la),za=Nc(function(n,e,t,r){var o=xc(n).rows,u=e.cells[0].column,i=o[e.cells[0].row],c=Ra(e.clipboard,e.generators,i),a=Qc(u,n,c,e.generators,t),l=ya(a,e.cells[0].row,e.cells[0].column);return wa(a,l)},_c,y,y,la),Fa=Nc(function(n,e,t,r){var o=xc(n).rows,u=e.cells[e.cells.length-1].column+e.cells[e.cells.length-1].colspan,i=o[e.cells[0].row],c=Ra(e.clipboard,e.generators,i),a=Qc(u,n,c,e.generators,t),l=ya(a,e.cells[0].row,e.cells[0].column);return wa(a,l)},_c,y,y,la),La=Nc(function(n,e,t,r){var o=xc(n).rows,u=e.cells[0].row,i=o[u],c=Ra(e.clipboard,e.generators,i),a=Zc(u,n,c,e.generators,t),l=ya(a,e.cells[0].row,e.cells[0].column);return wa(a,l)},_c,y,y,la),Ha=Nc(function(n,e,t,r){var o=xc(n).rows,u=e.cells[e.cells.length-1].row+e.cells[e.cells.length-1].rowspan,i=o[e.cells[0].row],c=Ra(e.clipboard,e.generators,i),a=Zc(u,n,c,e.generators,t),l=ya(a,e.cells[0].row,e.cells[0].column);return wa(a,l)},_c,y,y,la),qa=function(n,e){var u=lt.fromTable(n);return Wc(u,e).bind(function(n){var e=n[n.length-1],t=n[0].column,r=e.column+e.colspan,o=j(B(u.all,function(n){return I(n.cells,function(n){return n.column>=t&&n.column<r})}));return Da(o,function(n){return"th"===Gn(n.element)})}).getOr("")},Va=function(n){return zi(n.parentNode)},Ua=function(n,e){var t="thead"===Va(e),r=!O(e.cells,function(n){return"th"!==zi(n)});return t||r?S.some({thead:t,ths:r}):S.none()},$a=function(n,e){return"thead"===(t=Ua(0,e).fold(function(){return Va(e)},function(n){return"thead"}))?"header":"tfoot"===t?"footer":"body";var t},Ga=function(e,n,t){var r,o,u=e.getParent(n,"table"),i=n.parentNode,c=zi(i);t!==c&&((r=e.select(t,u)[0])||(r=e.create(t),o=u.firstChild,"thead"===t?F(ye(Dn.fromDom(u),"caption,colgroup")).fold(function(){return u.insertBefore(r,o)},function(n){return e.insertAfter(r,n.dom)}):u.appendChild(r)),"tbody"===t&&"thead"===c&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),i.hasChildNodes()||e.remove(i))},Ka=function(t,n,r,o){return E(n,function(n){var e=zi(n)!==r?t.rename(n,r):n;t.setAttrib(e,"scope",o)})},Xa=function(n,e,t){var r,o,u,i=n.dom;"header"===t?(o="auto"===(r=Ni(n))?(u=et(Dn.fromDom(e.cells[0])).map(tt).getOr([]),L(u,function(n){return Ua(0,n.dom)}).map(function(n){return n.thead&&n.ths?"sectionCells":n.thead?"section":"cells"}).getOr("section")):r,Ka(i,e.cells,"section"===o?"td":"th","col"),Ga(i,e,"cells"===o?"tbody":"thead")):(Ka(i,e.cells,"td",null),Ga(i,e,"footer"===t?"tfoot":"tbody"))},Ya=function(o){return function(n){var e,t=Gn(n),r="col"===t||"colgroup"===t?et(e=n).bind(function(n){return Xt(n,Er.firstSelectedSelector)}).fold(function(){return e},function(n){return n[0]}):n;return Oe(r,o)}},Ja=Ya("th,td"),Qa=Ya("th,td,caption"),Za=function(n,e){return Ja(n).map(function(n){return Dr(e)}).getOr([])},nl=function(n,t){var e,r,o,u=Ja(n),i=u.bind(function(n){return et(n)}).map(tt);return r=i,o=function(e,n){return I(n,function(n){return O(Rr(n.dom.cells),function(n){return"1"===Ie(n,t)||Bn(n,e)})})},((e=u).isSome()&&r.isSome()?S.some(o(e.getOrDie(),r.getOrDie())):S.none()).getOr([])},el=function(f,n,r){var e=function(n){return"table"===Gn(Fi(n))},s=Wi(f),t=function(i,c,a,l){return function(n,e){Ui(n);var t=l(),r=Dn.fromDom(f.getDoc()),o=xr(a,r,s),u=tc(f,n);return c(n)?i(t,n,e,o,u).bind(function(n){return E(n.newRows,function(n){hi(f,n.dom)}),E(n.newCells,function(n){vi(f,n.dom)}),n.cursor.map(function(n){var e=gc(pc,n),t=f.dom.createRng();return t.setStart(e.element.dom,e.offset),t.setEnd(e.element.dom,e.offset),t})}):S.none()}},o=t(ka,function(n){return!1===e(f)||1<Tu(n).rows},y,n),u=t(Pa,function(n){return!1===e(f)||1<Tu(n).columns},y,n),i=t(Aa,C,y,n),c=t(Ba,C,y,n),a=t(Ea,C,hc,n),l=t(Ia,C,hc,n),d=t(ja,C,y,n),m=t(_a,C,y,n),g=t(za,C,y,n),p=t(Fa,C,y,n),h=t(La,C,y,n),v=t(Ha,C,y,n),b=t(Wa,C,y,n),w=function(n,e){return Fn(n,"type").filter(function(n){return D(e,n)})};return{deleteRow:o,deleteColumn:u,insertRowsBefore:i,insertRowsAfter:c,insertColumnsBefore:a,insertColumnsAfter:l,mergeCells:d,unmergeCells:m,pasteColsBefore:g,pasteColsAfter:p,pasteRowsBefore:h,pasteRowsAfter:v,pasteCells:b,setTableCellType:function(t,n){return w(n,["td","th"]).each(function(n){var e=B(Za(Ki(t),r),function(n){return n.dom});Ka(t.dom,e,n,null)})},setTableRowType:function(t,n){return w(n,["header","body","footer"]).each(function(e){B(nl(Ki(t),Er.selected),function(n){return Xa(t,n.dom,e)})})},makeColumnHeader:t(Ma,C,y,n),unmakeColumnHeader:t(Na,C,y,n),getTableRowType:function(n){var e=nl(Ki(n),Er.selected);if(0<e.length){var t=B(e,function(n){return $a(0,n.dom)}),r=D(t,"header"),o=D(t,"footer");if(r||o){var u=D(t,"body");return!r||u||o?r||u||!o?"":"footer":"header"}return"body"}},getTableCellType:function(n){return Da(Za(Ki(n),r),function(n){return"th"===Gn(n)}).getOr("")},getTableColType:qa}},tl={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},rl=function(n,e,t,r){for(var o=Dn.fromTag("tr"),u=0;u<n;u++){var i=r<e||u<t?Dn.fromTag("th"):Dn.fromTag("td");u<t&&Be(i,"scope","row"),r<e&&Be(i,"scope","col"),St(i,Dn.fromTag("br")),St(o,i)}return o},ol=function(n){var e=Dn.fromTag("colgroup");return A(n,function(){return St(e,Dn.fromTag("col"))}),e},ul=function(n,e,t,r){return A(n,function(n){return rl(e,t,r,n)})},il=function(n,e){n.selection.select(e.dom,!0),n.selection.collapse(!0)},cl=function(o,n,e,t,r){var u=Di(o),i={styles:u,attributes:Ri(o),colGroups:o.getParam("table_use_colgroups",!1,"boolean")},c=function(n,e,t,r,o,u){void 0===u&&(u=tl);var i=Dn.fromTag("table"),c="cells"!==o;We(i,u.styles),Ee(i,u.attributes),u.colGroups&&St(i,ol(e));var a,l,f=Math.min(n,t);c&&0<t&&(a=Dn.fromTag("thead"),St(i,a),l=ul(t,e,"sectionCells"===o?f:0,r),Rt(a,l));var s=Dn.fromTag("tbody");St(i,s);var d=ul(c?n-f:n,e,c?0:t,r);return Rt(s,d),i}(e,n,r,t,Ni(o),i);Be(c,"data-mce-id","__mce");var a,l,f,s=(a=c,l=Dn.fromTag("div"),f=Dn.fromDom(a.dom.cloneNode(!0)),St(l,f),l.dom.innerHTML);return o.insertContent(s),De(Fi(o),'table[data-mce-id="__mce"]').map(function(n){var e,t,r;return ki(o)?ic(o,n):Mi(o)?cc(n):(Pi(o)||(e=u.width,p(e)&&-1!==e.indexOf("%")))&&uc(o,n),Ui(n),ke(n,"data-mce-id"),t=o,E(Ce(n,"tr"),function(n){hi(t,n.dom),E(Ce(n,"th,td"),function(n){vi(t,n.dom)})}),r=o,De(n,"td,th").each(w(il,r)),n.dom}).getOr(null)},al=function(n,e,t,r,o){void 0===r&&(r={});var u=function(n){return v(n)&&0<n};if(u(e)&&u(t)){var i=r.headerRows||0,c=r.headerColumns||0;return cl(n,t,e,c,i)}return console.error(o),null},ll=function(n){return function(){return n().fold(function(){return[]},function(n){return B(n,function(n){return n.dom})})}},fl=function(t){return function(n){var e=0<n.length?S.some(Rr(n)):S.none();t(e)}},sl=function(r,n,e,t){return{insertTable:function(n,e,t){return void 0===t&&(t={}),al(r,e,n,t,"Invalid values for insertTable - rows and columns values are required to insert a table.")},setClipboardRows:fl(n.setRows),getClipboardRows:ll(n.getRows),setClipboardCols:fl(n.setColumns),getClipboardCols:ll(n.getColumns),resizeHandler:e,selectionTargets:t}},dl=function(n,e,t){var r=qe(n,e,1);1===t||r<=1?ke(n,e):Be(n,e,Math.min(t,r))},ml=function(n,e){var i=lt.fromTable(n);return Wc(i,e).map(function(n){var o,u,e=n[n.length-1],t=n[0].column,r=e.column+e.colspan;return q(function(n,t,r){if(lt.hasColumns(n)){var e=I(lt.justColumns(n),function(n){return n.column>=t&&n.column<r}),o=B(e,function(n){var e=pr(n.element);return dl(e,"span",r-t),e}),u=Dn.fromTag("colgroup");return Rt(u,o),[u]}return[]}(i,t,r),(o=t,u=r,B(i.all,function(n){var e=I(n.cells,function(n){return n.column>=o&&n.column<u}),t=B(e,function(n){var e=pr(n.element);return dl(e,"colspan",u-o),e}),r=Dn.fromTag("tr");return Rt(r,t),r})))})},gl=function(n,e,o){var u=lt.fromTable(n);return Wc(u,e).map(function(n){var e=Ic(u,o,!1),t=xc(e).rows.slice(n[0].row,n[n.length-1].row+n[n.length-1].rowspan),r=kc(t,o);return B(r,function(n){var t=gr(n.element);return E(n.cells,function(n){var e=pr(n.element);Rc(e,"colspan",n.colspan,1),Rc(e,"rowspan",n.rowspan,1),St(t,e)}),t})})},pl=tinymce.util.Tools.resolve("tinymce.util.Tools"),hl=function(o,n,u){return function(n,e){for(var t=0;t<e.length;t++){var r=o.getStyle(e[t],u);if(void 0===n&&(n=r),n!==r)return""}return n}(void 0,o.select("td,th",n))},vl=function(n,e,t){t&&n.formatter.apply("align"+t,{},e)},bl=function(e,t){pl.each("left center right".split(" "),function(n){e.formatter.remove("align"+n,{},t)})},wl=function(n){return Ln(e=n,t="menu")&&e[t]!==undefined&&null!==e[t];var e,t},yl=function(n,e){return(e||[]).concat(B(n,function(n){var e=n.text||n.title;return wl(n)?{text:e,items:yl(n.menu)}:{text:e,value:n.value}}))},Cl=function(e){return function(n){return en(n,"rgb")?e.toHex(n):n}},Sl=function(n,e){var t=Dn.fromDom(e);return{borderwidth:Le(t,"border-width").getOr(""),borderstyle:Le(t,"border-style").getOr(""),bordercolor:Le(t,"border-color").map(Cl(n)).getOr(""),backgroundcolor:Le(t,"background-color").map(Cl(n)).getOr("")}},xl=function(n){var o=n[0],e=n.slice(1);return E(e,function(n){E(kn(o),function(r){Nn(n,function(n,e){var t=o[r];""!==t&&r===e&&t!==n&&(o[r]="")})})}),o},Tl=function(n){var e=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===n?[{name:"borderwidth",type:"input",label:"Border width"}].concat(e):e}},Rl=function(n,e,t,r){return M(n,function(n){return t.formatter.matchNode(r,e+n)}).getOr("")},Dl=w(Rl,["left","center","right"],"align"),Ol=w(Rl,["top","middle","bottom"],"valign"),Al=function(n,e){var t,r,o,u,i=Di(n),c=Ri(n),a=e?(t=n.dom,{borderstyle:Fn(i,"border-style").getOr(""),bordercolor:Cl(t)(Fn(i,"border-color").getOr("")),backgroundcolor:Cl(t)(Fn(i,"background-color").getOr(""))}):{};return H(H(H(H(H(H({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),i),c),a),(u=i["border-width"],Ii(n)&&u?{border:u}:Fn(c,"border").fold(function(){return{}},function(n){return{border:n}}))),(r=Fn(i,"border-spacing").or(Fn(c,"cellspacing")).fold(function(){return{}},function(n){return{cellspacing:n}}),o=Fn(i,"border-padding").or(Fn(c,"cellpadding")).fold(function(){return{}},function(n){return{cellpadding:n}}),H(H({},r),o)))},Bl=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],El=function(n){return Bl.concat((0<(e=yl(n.getParam("table_cell_class_list",[],"array"))).length?S.some({name:"class",type:"listbox",label:"Class",items:e}):S.none()).toArray());var e},Il=function(u){return function(t,r){var o=t.dom;return{setAttrib:function(n,e){u&&!e||o.setAttrib(r,n,e)},setStyle:function(n,e){u&&!e||o.setStyle(r,n,e)},setFormat:function(n,e){u&&!e||(""===e?t.formatter.remove(n,{value:null},r,!0):t.formatter.apply(n,{value:e},r))}}}},Pl={normal:Il(!1),ifTruthy:Il(!0)},kl=function(o){return et(o[0]).map(function(n){var e=lt.fromTable(n),t=lt.justCells(e),r=I(t,function(e){return O(o,function(n){return Bn(e.element,n)})});return B(r,function(n){return{element:n.element.dom,column:lt.getColumnAt(e,n.column).map(function(n){return n.element.dom})}})})},Ml=function(p,n,h){var v=p.dom,b=1===n.length;1<=n.length&&kl(n).each(function(n){return E(n,function(n){var e,t,r,o,u,i,c,a,l,f,s=n.element,d=h.celltype&&zi(s)!==h.celltype?v.rename(s,h.celltype):s,m=b?Pl.normal(p,d):Pl.ifTruthy(p,d),g=n.column.map(function(n){return b?Pl.normal(p,n):Pl.ifTruthy(p,n)}).getOr(m);t=g,r=h,(e=m).setAttrib("scope",r.scope),e.setAttrib("class",r["class"]),e.setStyle("height",Vi(r.height)),t.setStyle("width",Vi(r.width)),Ai(p)&&(u=h,(o=m).setFormat("tablecellbackgroundcolor",u.backgroundcolor),o.setFormat("tablecellbordercolor",u.bordercolor),o.setFormat("tablecellborderstyle",u.borderstyle),o.setFormat("tablecellborderwidth",Vi(u.borderwidth))),b&&(bl(p,d),i=p,c=d,pl.each("top middle bottom".split(" "),function(n){i.formatter.remove("valign"+n,{},c)})),h.halign&&vl(p,d,h.halign),h.valign&&(a=p,l=d,(f=h.valign)&&a.formatter.apply("valign"+f,{},l))})})},Nl=function(n,e,t){var r=t.getData();t.close(),n.undoManager.transact(function(){Ml(n,e,r),n.focus()})},jl=function(a,n){var e=kl(n).map(function(n){return B(n,function(n){return e=a,t=n.element,r=Ai(a),o=n.column,u=e.dom,i=o.getOr(t),H({width:(c=function(n,e){return u.getStyle(n,e)||u.getAttrib(n,e)})(i,"width"),height:c(t,"height"),scope:u.getAttrib(t,"scope"),celltype:zi(t),"class":u.getAttrib(t,"class",""),halign:Dl(e,t),valign:Ol(e,t)},r?Sl(u,t):{});var e,t,r,o,u,i,c})});return xl(e.getOrDie())},_l=function(n,e){var t,r,o,u=Za(Ki(n),e);0!==u.length&&(t=jl(n,u),r={type:"tabpanel",tabs:[{title:"General",name:"general",items:El(n)},Tl("cell")]},o={type:"panel",items:[{type:"grid",columns:2,items:El(n)}]},n.windowManager.open({title:"Cell Properties",size:"normal",body:Ai(n)?r:o,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:t,onSubmit:w(Nl,n,u)}))},Wl=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],zl=function(n){return Wl.concat((0<(e=yl(n.getParam("table_row_class_list",[],"array"))).length?S.some({name:"class",type:"listbox",label:"Class",items:e}):S.none()).toArray());var e},Fl=function(i,n,c,a){var l=1===n.length;E(n,function(n){a.type!==zi(n.parentNode)&&Xa(i,n,a.type);var e,t,r,o,u=l?Pl.normal(i,n):Pl.ifTruthy(i,n);t=a,(e=u).setAttrib("scope",t.scope),e.setAttrib("class",t["class"]),e.setStyle("height",Vi(t.height)),Bi(i)&&(o=a,(r=u).setStyle("background-color",o.backgroundcolor),r.setStyle("border-color",o.bordercolor),r.setStyle("border-style",o.borderstyle)),a.align!==c.align&&(bl(i,n),vl(i,n,a.align))})},Ll=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact(function(){Fl(n,e,t,o),n.focus()})},Hl=function(u){var n,e,t,r,o=nl(Ki(u),Er.selected);0!==o.length&&(n=B(o,function(n){return e=u,t=n.dom,r=Bi(u),o=e.dom,H({height:o.getStyle(t,"height")||o.getAttrib(t,"height"),scope:o.getAttrib(t,"scope"),"class":o.getAttrib(t,"class",""),type:$a(0,t),align:Dl(e,t)},r?Sl(o,t):{});var e,t,r,o}),e=xl(n),t={type:"tabpanel",tabs:[{title:"General",name:"general",items:zl(u)},Tl("row")]},r={type:"panel",items:[{type:"grid",columns:2,items:zl(u)}]},u.windowManager.open({title:"Row Properties",size:"normal",body:Bi(u)?t:r,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:e,onSubmit:w(Ll,u,B(o,function(n){return n.dom}),e)}))},ql=tinymce.util.Tools.resolve("tinymce.Env"),Vl=function(n,e,t,r){if("TD"===e.tagName||"TH"===e.tagName)p(t)?n.setStyle(e,t,r):n.setStyle(e,t);else if(e.children)for(var o=0;o<e.children.length;o++)Vl(n,e.children[o],t,r)},Ul=function(t,r,n){var o,u=t.dom,i=n.getData();n.close(),""===i["class"]&&delete i["class"],t.undoManager.transact(function(){var n,e;r||(n=parseInt(i.cols,10)||1,e=parseInt(i.rows,10)||1,r=cl(t,n,e,0,0)),function(n,e,t){var r,o=n.dom,u={},i={};if(u["class"]=t["class"],i.height=Vi(t.height),o.getAttrib(e,"width")&&!Ii(n)?u.width=(r=t.width)?r.replace(/px$/,""):"":i.width=Vi(t.width),Ii(n)?(i["border-width"]=Vi(t.border),i["border-spacing"]=Vi(t.cellspacing)):(u.border=t.border,u.cellpadding=t.cellpadding,u.cellspacing=t.cellspacing),Ii(n)&&e.children)for(var c=0;c<e.children.length;c++)Vl(o,e.children[c],{"border-width":Vi(t.border),padding:Vi(t.cellpadding)}),Ei(n)&&Vl(o,e.children[c],{"border-color":t.bordercolor});Ei(n)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),u.style=o.serializeStyle(H(H({},Di(n)),i)),o.setAttribs(e,H(H({},Ri(n)),u))}(t,r,i),(o=u.select("caption",r)[0])&&!i.caption&&u.remove(o),!o&&i.caption&&((o=u.create("caption")).innerHTML=ql.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===i.align?bl(t,r):vl(t,r,i.align),t.focus(),t.addVisual()})},$l=function(n,e){var t,r,o,u,i,c,a,l,f=n.dom,s=Al(n,Ei(n));!1===e?(t=f.getParent(n.selection.getStart(),"table"))?(o=t,u=Ei(r=n),l=r.dom,s=H({width:l.getStyle(o,"width")||l.getAttrib(o,"width"),height:l.getStyle(o,"height")||l.getAttrib(o,"height"),cellspacing:l.getStyle(o,"border-spacing")||l.getAttrib(o,"cellspacing"),cellpadding:l.getAttrib(o,"cellpadding")||hl(r.dom,o,"padding"),border:(i=l,c=o,a=Le(Dn.fromDom(c),"border-width"),Ii(r)&&a.isSome()?a.getOr(""):i.getAttrib(c,"border")||hl(r.dom,c,"border-width")||hl(r.dom,c,"border")),caption:!!l.select("caption",o)[0],"class":l.getAttrib(o,"class",""),align:Dl(r,o)},u?Sl(l,o):{})):Ei(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""):(s.cols="1",s.rows="1",Ei(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""));var d=yl(n.getParam("table_class_list",[],"array"));0<d.length&&s["class"]&&(s["class"]=s["class"].replace(/\s*mce\-item\-table\s*/g,""));var m,g,p,h,v={type:"grid",columns:2,items:(m=d,g=e?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],p=n.getParam("table_appearance_options",!0,"boolean")?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],h=0<m.length?[{type:"listbox",name:"class",label:"Class",items:m}]:[],g.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(p).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(h))},b=Ei(n)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[v]},Tl("table")]}:{type:"panel",items:[v]};n.windowManager.open({title:"Table Properties",size:"normal",body:b,onSubmit:w(Ul,n,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s})},Gl=function(n){return Qa(Ki(n))},Kl=function(n){return Ja(Ki(n))},Xl=function(c,t,a,l,e){var r=qi(c),o=function(n){return et(n,r)},u=function(r){return Kl(c).each(function(t){o(t).each(function(e){var n=Pr(l,e,t);r(e,n).each(function(n){c.selection.setRng(n),c.focus(),a.clear(e),Ui(e)})})})},i=function(){return Kl(c).map(function(r){return o(r).bind(function(n){var e=Pr(l,n,r),t=xr(y,Dn.fromDom(c.getDoc()),S.none());return gl(n,e,t)})})},f=function(){return Kl(c).map(function(t){return o(t).bind(function(n){var e=Pr(l,n,t);return ml(n,e)})})},s=function(i,n){return n().each(function(n){var u=B(n,pr);Kl(c).each(function(n){return o(n).each(function(e){var n,t,r=Tr(Dn.fromDom(c.getDoc())),o=(n=u,t=r,{selection:Dr(l),clipboard:n,generators:t});i(e,o).each(function(n){c.selection.setRng(n),c.focus(),a.clear(e)})})})})};Nn({mceTableSplitCells:function(){return u(t.unmergeCells)},mceTableMergeCells:function(){return u(t.mergeCells)},mceTableInsertRowBefore:function(){return u(t.insertRowsBefore)},mceTableInsertRowAfter:function(){return u(t.insertRowsAfter)},mceTableInsertColBefore:function(){return u(t.insertColumnsBefore)},mceTableInsertColAfter:function(){return u(t.insertColumnsAfter)},mceTableDeleteCol:function(){return u(t.deleteColumn)},mceTableDeleteRow:function(){return u(t.deleteRow)},mceTableCutCol:function(n){return f().each(function(n){e.setColumns(n),u(t.deleteColumn)})},mceTableCutRow:function(n){return i().each(function(n){e.setRows(n),u(t.deleteRow)})},mceTableCopyCol:function(n){return f().each(function(n){return e.setColumns(n)})},mceTableCopyRow:function(n){return i().each(function(n){return e.setRows(n)})},mceTablePasteColBefore:function(n){return s(t.pasteColsBefore,e.getColumns)},mceTablePasteColAfter:function(n){return s(t.pasteColsAfter,e.getColumns)},mceTablePasteRowBefore:function(n){return s(t.pasteRowsBefore,e.getRows)},mceTablePasteRowAfter:function(n){return s(t.pasteRowsAfter,e.getRows)},mceTableDelete:function(){return Gl(c).each(function(n){et(n,r).filter(d(r)).each(function(n){var e,t=Dn.fromText("");yt(n,t),Ot(n),c.dom.isEmpty(c.getBody())?(c.setContent(""),c.selection.setCursorLocation()):((e=c.dom.createRng()).setStart(t.dom,0),e.setEnd(t.dom,0),c.selection.setRng(e),c.nodeChanged())})})},mceTableSizingMode:function(n,e){return t=e,Gl(c).each(function(n){Mi(c)||ki(c)||Pi(c)||et(n,r).each(function(n){"relative"!==t||yu(n)?"fixed"!==t||Cu(n)?"responsive"!==t||Su(n)||cc(n):ic(c,n):uc(c,n),Ui(n)})});var t}},function(n,e){return c.addCommand(e,n)}),Nn({mceTableCellType:function(n,e){return t.setTableCellType(c,e)},mceTableRowType:function(n,e){return t.setTableRowType(c,e)}},function(n,e){return c.addCommand(e,n)}),c.addCommand("mceTableColType",function(n,e){return Fn(e,"type").each(function(n){return u("th"===n?t.makeColumnHeader:t.unmakeColumnHeader)})}),Nn({mceTableProps:w($l,c,!1),mceTableRowProps:w(Hl,c),mceTableCellProps:w(_l,c,l)},function(n,e){return c.addCommand(e,function(){return n()})}),c.addCommand("mceInsertTable",function(n,e){m(e)&&0<kn(e).length?al(c,e.rows,e.columns,e.options,"Invalid values for mceInsertTable - rows and columns values are required to insert a table."):$l(c,!0)}),c.addCommand("mceTableApplyCellStyle",function(n,e){var r;!m(e)||0!==(r=Za(Ki(c),l)).length&&Nn(e,function(e,n){var t="tablecell"+n.toLowerCase().replace("-","");c.formatter.has(t)&&p(e)&&E(r,function(n){Pl.normal(c,n.dom).setFormat(t,e)})})})},Yl=function(t,r,o){var n=qi(t);Nn({mceTableRowType:function(){return r.getTableRowType(t)},mceTableCellType:function(){return r.getTableCellType(t)},mceTableColType:function(){return Ja(Ki(t)).bind(function(t){return et(t,n).map(function(n){var e=Pr(o,n,t);return r.getTableColType(n,e)})}).getOr("")}},function(n,e){return t.addQueryValueHandler(e,n)})},Jl=function(){var e=Xi(S.none()),t=Xi(S.none()),r=function(n){n.set(S.none())};return{getRows:e.get,setRows:function(n){e.set(n),r(t)},clearRows:function(){return r(e)},getColumns:t.get,setColumns:function(n){t.set(n),r(e)},clearColumns:function(){return r(t)}}},Ql={tablecellbackgroundcolor:{selector:"td,th",styles:{backgroundColor:"%value"},remove_similar:!0},tablecellbordercolor:{selector:"td,th",styles:{borderColor:"%value"},remove_similar:!0},tablecellborderstyle:{selector:"td,th",styles:{borderStyle:"%value"},remove_similar:!0},tablecellborderwidth:{selector:"td,th",styles:{borderWidth:"%value"},remove_similar:!0}},Zl=function(n){n.formatter.register(Ql)},nf=Jt([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),ef=H(H({},nf),{none:function(n){return void 0===n&&(n=undefined),nf.none(n)}}),tf=function(t,n){return et(t,n).bind(function(n){var e=Ze(n);return N(e,function(n){return Bn(t,n)}).map(function(n){return{index:n,all:e}})})},rf=function(n,e,t,r){return{start:n,soffset:e,finish:t,foffset:r}},of=Jt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),uf={before:of.before,on:of.on,after:of.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(u,u,u)}},cf=Jt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),af={domRange:cf.domRange,relative:cf.relative,exact:cf.exact,exactFromRange:function(n){return cf.exact(n.start,n.soffset,n.finish,n.foffset)},getWin:function(n){var e,t=n.match({domRange:function(n){return Dn.fromDom(n.startContainer)},relative:function(n,e){return uf.getStart(n)},exact:function(n,e,t,r){return n}});return e=t,Dn.fromDom(te(e).dom.defaultView)},range:rf},lf=function(n,e){return n.selectNodeContents(e.dom)},ff=function(n,e,t){var r,o,u=n.document.createRange();return r=u,e.fold(function(n){r.setStartBefore(n.dom)},function(n,e){r.setStart(n.dom,e)},function(n){r.setStartAfter(n.dom)}),o=u,t.fold(function(n){o.setEndBefore(n.dom)},function(n,e){o.setEnd(n.dom,e)},function(n){o.setEndAfter(n.dom)}),u},sf=function(n,e,t,r,o){var u=n.document.createRange();return u.setStart(e.dom,t),u.setEnd(r.dom,o),u},df=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}},mf=Jt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),gf=function(n,e,t){return e(Dn.fromDom(t.startContainer),t.startOffset,Dn.fromDom(t.endContainer),t.endOffset)},pf=function(n,e){var o,t,r,u=(o=n,e.match({domRange:function(n){return{ltr:b(n),rtl:S.none}},relative:function(n,e){return{ltr:U(function(){return ff(o,n,e)}),rtl:U(function(){return S.some(ff(o,e,n))})}},exact:function(n,e,t,r){return{ltr:U(function(){return sf(o,n,e,t,r)}),rtl:U(function(){return S.some(sf(o,t,r,n,e))})}}}));return(r=(t=u).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return mf.rtl(Dn.fromDom(n.endContainer),n.endOffset,Dn.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return gf(0,mf.ltr,r)}):gf(0,mf.ltr,r)},hf=function(u,n){return pf(u,n).match({ltr:function(n,e,t,r){var o=u.document.createRange();return o.setStart(n.dom,e),o.setEnd(t.dom,r),o},rtl:function(n,e,t,r){var o=u.document.createRange();return o.setStart(t.dom,r),o.setEnd(n.dom,e),o}})},vf=(mf.ltr,mf.rtl,function(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}),bf=function(t,r,n,e,o){var u=function(n){var e=t.dom.createRange();return e.setStart(r.dom,n),e.collapse(!0),e},i=kt(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var u=r,i=1;i<o;i++){var c=n(i),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||u<a)return i-1;u=a}}return 0}(function(n){return u(n).getBoundingClientRect()},n,e,o.right,i);return u(c)},wf=function(n,e,t,r){return Qn(e)?function(e,t,r,o){var n=e.dom.createRange();n.selectNode(t.dom);var u=n.getClientRects();return L(u,function(n){return vf(n,r,o)?S.some(n):S.none()}).map(function(n){return bf(e,t,r,o,n)})}(n,e,t,r):(u=e,i=t,c=r,a=(o=n).dom.createRange(),l=ce(u),L(l,function(n){return a.selectNode(n.dom),vf(a.getBoundingClientRect(),i,c)?wf(o,n,i,c):S.none()}));var o,u,i,c,a,l},yf=function(n,e){return e-n.left<n.right-e},Cf=function(n,e,t){var r=n.dom.createRange();return r.selectNode(e.dom),r.collapse(t),r},Sf=function(e,n,t){var r=e.dom.createRange();r.selectNode(n.dom);var o=r.getBoundingClientRect(),u=yf(o,t);return(!0===u?fr:sr)(n).map(function(n){return Cf(e,n,u)})},xf=function(n,e,t){var r=e.dom.getBoundingClientRect(),o=yf(r,t);return S.some(Cf(n,e,o))},Tf=function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect(),i=Math.max(u.left,Math.min(u.right,t)),c=Math.max(u.top,Math.min(u.bottom,r));return wf(n,e,i,c)}(n,e,Math.max(u.left,Math.min(u.right,t)),Math.max(u.top,Math.min(u.bottom,r)))},Rf=document.caretPositionFromPoint?function(t,n,e){return S.from(t.dom.caretPositionFromPoint(n,e)).bind(function(n){if(null===n.offsetNode)return S.none();var e=t.dom.createRange();return e.setStart(n.offsetNode,n.offset),e.collapse(),S.some(e)})}:document.caretRangeFromPoint?function(n,e,t){return S.from(n.dom.caretRangeFromPoint(e,t))}:function(o,u,e){return Dn.fromPoint(o,u,e).bind(function(r){var n=function(){return n=o,t=u,(0===ce(e=r).length?xf:Sf)(n,e,t);var n,e,t};return 0===ce(r).length?n():Tf(o,r,u,e).orThunk(n)})},Df=function(n,e){var t=Gn(n);return"input"===t?uf.after(n):D(["br","img"],t)?0===e?uf.before(n):uf.after(n):uf.on(n,e)},Of=function(n,e){var t=n.fold(uf.before,Df,uf.after),r=e.fold(uf.before,Df,uf.after);return af.relative(t,r)},Af=function(n,e,t,r){var o=Df(n,e),u=Df(t,r);return af.relative(o,u)},Bf=function(n,e,t,r){var o,u,i,c,a,l=(u=e,i=t,c=r,(a=ee(o=n).dom.createRange()).setStart(o.dom,u),a.setEnd(i.dom,c),a),f=Bn(n,t)&&e===r;return l.collapsed&&!f},Ef=function(n){return S.from(n.getSelection())},If=function(n,e){Ef(n).each(function(n){n.removeAllRanges(),n.addRange(e)})},Pf=function(n,e,t,r,o){var u=sf(n,e,t,r,o);If(n,u)},kf=function(s,n){return pf(s,n).match({ltr:function(n,e,t,r){Pf(s,n,e,t,r)},rtl:function(c,a,l,f){Ef(s).each(function(n){if(n.setBaseAndExtent)n.setBaseAndExtent(c.dom,a,l.dom,f);else if(n.extend)try{t=c,r=a,o=l,u=f,(e=n).collapse(t.dom,r),e.extend(o.dom,u)}catch(i){Pf(s,l,f,c,a)}else Pf(s,l,f,c,a);var e,t,r,o,u})}})},Mf=function(n,e,t,r,o){var u=Af(e,t,r,o);kf(n,u)},Nf=function(n,e,t){var r=Of(e,t);kf(n,r)},jf=function(n){var o=af.getWin(n).dom,e=function(n,e,t,r){return sf(o,n,e,t,r)},t=n.match({domRange:function(n){var e=Dn.fromDom(n.startContainer),t=Dn.fromDom(n.endContainer);return Af(e,n.startOffset,t,n.endOffset)},relative:Of,exact:Af});return pf(o,t).match({ltr:e,rtl:e})},_f=function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return S.some(rf(Dn.fromDom(e.startContainer),e.startOffset,Dn.fromDom(t.endContainer),t.endOffset))}return S.none()},Wf=function(n){if(null===n.anchorNode||null===n.focusNode)return _f(n);var e=Dn.fromDom(n.anchorNode),t=Dn.fromDom(n.focusNode);return Bf(e,n.anchorOffset,t,n.focusOffset)?S.some(rf(e,n.anchorOffset,t,n.focusOffset)):_f(n)},zf=function(n,e){var t,r,o=(t=e,r=n.document.createRange(),lf(r,t),r);If(n,o)},Ff=function(n){return Ef(n).filter(function(n){return 0<n.rangeCount}).bind(Wf).map(function(n){return af.exact(n.start,n.soffset,n.finish,n.foffset)})},Lf=function(n,e){var t,r,o,u=hf(n,e);return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?S.some(o).map(df):S.none()},Hf=function(n,e,t){return r=n,o=e,u=t,i=Dn.fromDom(r.document),Rf(i,o,u).map(function(n){return rf(Dn.fromDom(n.startContainer),n.startOffset,Dn.fromDom(n.endContainer),n.endOffset)});var r,o,u,i},qf=tinymce.util.Tools.resolve("tinymce.util.VK"),Vf=function(n,e,t,r){return Gf(n,e,tf(o=t,u).fold(function(){return ef.none(o)},function(n){return n.index+1<n.all.length?ef.middle(o,n.all[n.index+1]):ef.last(o)}),r);var o,u},Uf=function(n,e,t,r){return Gf(n,e,tf(o=t,u).fold(function(){return ef.none()},function(n){return 0<=n.index-1?ef.middle(o,n.all[n.index-1]):ef.first(o)}),r);var o,u},$f=function(n,e){var t=af.exact(e,0,e,0);return jf(t)},Gf=function(o,n,e,u){return e.fold(S.none,S.none,function(n,e){return fr(e).map(function(n){return $f(0,n)})},function(r){return et(r,n).bind(function(n){var e,t=Ir(r);return o.undoManager.transact(function(){u.insertRowsAfter(n,t)}),e=Ce(n,"tr"),F(e).bind(function(n){return De(n,"td,th").map(function(n){return $f(0,n)})})})})},Kf=["table","li","dl"],Xf=function(e,t,r){var o,u,n,i;e.keyCode===qf.TAB&&(o=Fi(t),u=function(n){var e=Gn(n);return Bn(n,o)||D(Kf,e)},(n=t.selection.getRng()).collapsed&&(i=Dn.fromDom(n.startContainer),Qe(i,u).each(function(n){e.preventDefault(),(e.shiftKey?Uf:Vf)(t,u,n,r).each(function(n){t.selection.setRng(n)})})))},Yf=function(n,e){return{selection:n,kill:e}},Jf=function(n,e,t,r){return{start:uf.on(n,e),finish:uf.on(t,r)}},Qf=function(n,e){var t=hf(n,e);return rf(Dn.fromDom(t.startContainer),t.startOffset,Dn.fromDom(t.endContainer),t.endOffset)},Zf=Jf,ns=function(t,n,r,e,o){return Bn(r,e)?S.none():Ut(r,e,n).bind(function(n){var e=n.boxes.getOr([]);return 0<e.length?(o(t,e,n.start,n.finish),S.some(Yf(S.some(Zf(r,0,r,cr(r))),!0))):S.none()})},es=function(n,e){return{item:n,mode:e}},ts=function(n,e,t,r){return void 0===r&&(r=rs),n.property().parent(e).map(function(n){return es(n,r)})},rs=function(n,e,t,r){return void 0===r&&(r=os),t.sibling(n,e).map(function(n){return es(n,r)})},os=function(n,e,t,r){void 0===r&&(r=os);var o=n.property().children(e);return t.first(o).map(function(n){return es(n,r)})},us=[{current:ts,next:rs,fallback:S.none()},{current:rs,next:os,fallback:S.some(ts)},{current:os,next:os,fallback:S.some(rs)}],is=function(e,t,r,o,n){return void 0===n&&(n=us),M(n,function(n){return n.current===r}).bind(function(n){return n.current(e,t,o,n.next).orThunk(function(){return n.fallback.bind(function(n){return is(e,t,n,o)})})})},cs=function(){return{sibling:function(n,e){return n.query().prevSibling(e)},first:function(n){return 0<n.length?S.some(n[n.length-1]):S.none()}}},as=function(){return{sibling:function(n,e){return n.query().nextSibling(e)},first:function(n){return 0<n.length?S.some(n[0]):S.none()}}},ls=function(e,n,t,r,o,u){return is(e,n,r,o).bind(function(n){return u(n.item)?S.none():t(n.item)?S.some(n.item):ls(e,n.item,t,n.mode,o,u)})},fs=function(e){return function(n){return 0===e.property().children(n).length}},ss=function(n,e,t,r){return ls(n,e,t,rs,cs(),r)},ds=function(n,e,t,r){return ls(n,e,t,rs,as(),r)},ms=_t(),gs=function(n,e){return ss(t=ms,n,fs(t),e);var t},ps=function(n,e){return ds(t=ms,n,fs(t),e);var t},hs=Jt([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),vs=function(n){return Oe(n,"tr")},bs=H(H({},hs),{verify:function(c,n,e,t,r,a,o){return Oe(t,"td,th",o).bind(function(i){return Oe(n,"td,th",o).map(function(u){return Bn(i,u)?Bn(t,i)&&cr(i)===r?a(u):hs.none("in same cell"):qt(vs,[i,u]).fold(function(){return e=u,t=i,r=(n=c).getRect(e),(o=n.getRect(t)).right>r.left&&o.left<r.right?hs.success():a(u);var n,e,t,r,o},function(n){return a(u)})})}).getOr(hs.none("default"))},cata:function(n,e,t,r,o){return n.fold(e,t,r,o)}}),ws=function(n,e){return N(n,w(Bn,e))},ys=function(n){return"br"===Gn(n)},Cs=function(n,e,t){return e(n,t).bind(function(n){return Qn(n)&&0===kt(n).trim().length?Cs(n,e,t):S.some(n)})},Ss=function(e,n,t,r){return ae(o=n,u=t).filter(ys).orThunk(function(){return ae(o,u-1).filter(ys)}).bind(function(n){return r.traverse(n).fold(function(){return Cs(n,r.gather,e).map(r.relative)},function(n){return re(r=n).bind(function(e){var t=ce(e);return ws(t,r).map(function(n){return{parent:e,children:t,element:r,index:n}})}).map(function(n){return uf.on(n.parent,n.index)});var r})});var o,u},xs=function(n,e,t,r){var o,u,i;return(ys(e)?(o=n,u=e,(i=r).traverse(u).orThunk(function(){return Cs(u,i.gather,o)}).map(i.relative)):Ss(n,e,t,r)).map(function(n){return{start:n,finish:n}})},Ts=function(n,e){return{left:n.left,top:n.top+e,right:n.right,bottom:n.bottom+e}},Rs=function(n,e){return{left:n.left,top:n.top-e,right:n.right,bottom:n.bottom-e}},Ds=function(n,e,t){return{left:n.left+e,top:n.top+t,right:n.right+e,bottom:n.bottom+t}},Os=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom}},As=function(n,e){return S.some(n.getRect(e))},Bs=function(n,e,t){return Jn(e)?As(n,e).map(Os):Qn(e)?(r=n,o=e,(0<=(u=t)&&u<cr(o)?r.getRangedRect(o,u,o,u+1):0<u?r.getRangedRect(o,u-1,o,u):S.none()).map(Os)):S.none();var r,o,u},Es=function(n,e){return Jn(e)?As(n,e).map(Os):Qn(e)?n.getRangedRect(e,0,e,cr(e)).map(Os):S.none()},Is=Jt([{none:[]},{retry:["caret"]}]),Ps=function(e,n,r){return Se(function(n,e){return e(n)},xe,n,ga,t).fold(f,function(n){return Es(e,n).exists(function(n){return t=n,(e=r).left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right;var e,t})});var t},ks={point:function(n){return n.bottom},adjuster:function(n,e,t,r,o){var u=Ts(o,5);return Math.abs(t.bottom-r.bottom)<1||t.top>o.bottom?Is.retry(u):t.top===o.bottom?Is.retry(Ts(o,1)):Ps(n,e,o)?Is.retry(Ds(u,5,0)):Is.none()},move:Ts,gather:ps},Ms=function(t,r,o,u,i){return 0===i?S.some(u):(a=t,l=u.left,f=r.point(u),a.elementFromPoint(l,f).filter(function(n){return"table"===Gn(n)}).isSome()?(e=u,c=i-1,Ms(t,n=r,o,n.move(e,5),c)):t.situsFromPoint(u.left,r.point(u)).bind(function(n){return n.start.fold(S.none,function(e){return Es(t,e).bind(function(n){return r.adjuster(t,e,n,o,u).fold(S.none,function(n){return Ms(t,r,o,n,i-1)})}).orThunk(function(){return S.some(u)})},S.none)}));var n,e,c,a,l,f},Ns=function(e,t,n){var r,o,u,i=e.move(n,5),c=Ms(t,e,n,i,100).getOr(i);return o=c,u=t,((r=e).point(o)>u.getInnerHeight()?S.some(r.point(o)-u.getInnerHeight()):r.point(o)<0?S.some(-r.point(o)):S.none()).fold(function(){return t.situsFromPoint(c.left,e.point(c))},function(n){return t.scrollBy(0,n),t.situsFromPoint(c.left,e.point(c)-n)})},js={tryUp:w(Ns,{point:function(n){return n.top},adjuster:function(n,e,t,r,o){var u=Rs(o,5);return Math.abs(t.top-r.top)<1||t.bottom<o.top?Is.retry(u):t.bottom===o.top?Is.retry(Rs(o,1)):Ps(n,e,o)?Is.retry(Ds(u,5,0)):Is.none()},move:Rs,gather:gs}),tryDown:w(Ns,ks),ieTryUp:function(n,e){return n.situsFromPoint(e.left,e.top-5)},ieTryDown:function(n,e){return n.situsFromPoint(e.left,e.bottom+5)},getJumpSize:b(5)},_s=function(u,i,c){return u.getSelection().bind(function(o){return xs(i,o.finish,o.foffset,c).fold(function(){return S.some(fc(o.finish,o.foffset))},function(n){var e,t=u.fromSitus(n),r=bs.verify(u,o.finish,o.foffset,t.finish,t.foffset,c.failure,i);return e=r,bs.cata(e,function(n){return S.none()},function(){return S.none()},function(n){return S.some(fc(n,0))},function(n){return S.some(fc(n,cr(n)))})})})},Ws=function(r,o,u,i,c,a){return 0===a?S.none():Ls(r,o,u,i,c).bind(function(n){var e=r.fromSitus(n),t=bs.verify(r,u,i,e.finish,e.foffset,c.failure,o);return bs.cata(t,function(){return S.none()},function(){return S.some(n)},function(n){return Bn(u,n)&&0===i?zs(r,u,i,Rs,c):Ws(r,o,n,0,c,a-1)},function(n){return Bn(u,n)&&i===cr(n)?zs(r,u,i,Ts,c):Ws(r,o,n,cr(n),c,a-1)})})},zs=function(e,n,t,r,o){return Bs(e,n,t).bind(function(n){return Fs(e,o,r(n,js.getJumpSize()))})},Fs=function(n,e,t){var r=Tn().browser;return r.isChrome()||r.isSafari()||r.isFirefox()||r.isEdge()?e.otherRetry(n,t):r.isIE()?e.ieRetry(n,t):S.none()},Ls=function(e,n,t,r,o){return Bs(e,t,r).bind(function(n){return Fs(e,o,n)})},Hs=function(n,e){return xe(n,function(n){return re(n).exists(function(n){return Bn(n,e)})},t).isSome();var t},qs=function(u,i,c,n,a){return Oe(n,"td,th",i).bind(function(o){return Oe(o,"table",i).bind(function(n){return Hs(a,n)?_s(e=u,t=i,r=c).bind(function(n){return Ws(e,t,n.element,n.offset,r,20).map(e.fromSitus)}).bind(function(e){return Oe(e.finish,"td,th",i).map(function(n){return{start:o,finish:n,range:e}})}):S.none();var e,t,r})})},Vs=function(n,e,t,r,o,u){return Tn().browser.isIE()?S.none():u(r,e).orThunk(function(){return qs(n,e,t,r,o).map(function(n){var e=n.range;return Yf(S.some(Zf(e.start,e.soffset,e.finish,e.foffset)),!0)})})},Us=function(n,r){return Oe(n,"tr",r).bind(function(t){return Oe(t,"table",r).bind(function(n){var e=Ce(n,"tr");return Bn(t,e[0])?ss(ms,n,function(n){return sr(n).isSome()},r).map(function(n){var e=cr(n);return Yf(S.some(Zf(n,e,n,e)),!0)}):S.none()})})},$s=function(n,r){return Oe(n,"tr",r).bind(function(t){return Oe(t,"table",r).bind(function(n){var e=Ce(n,"tr");return Bn(t,e[e.length-1])?ds(ms,n,function(n){return fr(n).isSome()},r).map(function(n){return Yf(S.some(Zf(n,0,n,0)),!0)}):S.none()})})},Gs=function(n,e,t,r,o,u,i){return qs(n,t,r,o,u).bind(function(n){return ns(e,t,n.start,n.finish,i)})},Ks=function(n,e){return Oe(n,"td,th",e)};function Xs(o,u,e,i){var t,r=(t=Xi(S.none()),{clear:function(){return t.set(S.none())},set:function(n){return t.set(S.some(n))},isSet:function(){return t.get().isSome()},on:function(n){return t.get().each(n)}}),c=r.clear;return{clearstate:c,mousedown:function(n){i.clear(u),Ks(n.target,e).each(r.set)},mouseover:function(n){r.on(function(r){i.clearBeforeUpdate(u),Ks(n.target,e).each(function(t){Ut(r,t,e).each(function(n){var e=n.boxes.getOr([]);(1<e.length||1===e.length&&!Bn(r,t))&&(i.selectRange(u,e,n.start,n.finish),o.selectContents(t))})})})},mouseup:function(n){c()}}}var Ys={traverse:ie,gather:ps,relative:uf.before,otherRetry:js.tryDown,ieRetry:js.ieTryDown,failure:bs.failedDown},Js={traverse:ue,gather:gs,relative:uf.before,otherRetry:js.tryUp,ieRetry:js.ieTryUp,failure:bs.failedUp},Qs=function(e){return function(n){return n===e}},Zs=Qs(38),nd=Qs(40),ed=function(n){return 37<=n&&n<=40},td={isBackward:Qs(37),isForward:Qs(39)},rd={isBackward:Qs(39),isForward:Qs(37)},od=function(c){return{elementFromPoint:function(n,e){return Dn.fromPoint(Dn.fromDom(c.document),n,e)},getRect:function(n){return n.dom.getBoundingClientRect()},getRangedRect:function(n,e,t,r){var o=af.exact(n,e,t,r);return Lf(c,o)},getSelection:function(){return Ff(c).map(function(n){return Qf(c,n)})},fromSitus:function(n){var e=af.relative(n.start,n.finish);return Qf(c,e)},situsFromPoint:function(n,e){return Hf(c,n,e).map(function(n){return Jf(n.start,n.soffset,n.finish,n.foffset)})},clearSelection:function(){Ef(c).each(function(n){return n.removeAllRanges()})},collapseSelection:function(i){void 0===i&&(i=!1),Ff(c).each(function(n){return n.fold(function(n){return n.collapse(i)},function(n,e){var t=i?n:e;Nf(c,t,t)},function(n,e,t,r){var o=i?n:t,u=i?e:r;Mf(c,o,u,o,u)})})},setSelection:function(n){Mf(c,n.start,n.soffset,n.finish,n.foffset)},setRelativeSelection:function(n,e){Nf(c,n,e)},selectContents:function(n){zf(c,n)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){var n,e,t,r;return(n=Dn.fromDom(c.document),e=n!==undefined?n.dom:document,t=e.body.scrollLeft||e.documentElement.scrollLeft,r=e.body.scrollTop||e.documentElement.scrollTop,Qr(t,r)).top},scrollBy:function(n,e){var t,r,o,u;t=n,r=e,o=Dn.fromDom(c.document),(u=(o!==undefined?o.dom:document).defaultView)&&u.scrollBy(t,r)}}},ud=function(n,e){return{rows:n,cols:e}},id=function(n,e,t,r){var o=Xs(od(n),e,t,r);return{clearstate:o.clearstate,mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},cd=function(n,g,p,h){var l=od(n),f=function(){return h.clear(g),S.none()};return{keydown:function(n,e,t,r,o,i){var u=n.raw,c=u.which,a=!0===u.shiftKey;return $t(g,h.selectedSelector).fold(function(){return nd(c)&&a?w(Gs,l,g,p,Ys,r,e,h.selectRange):Zs(c)&&a?w(Gs,l,g,p,Js,r,e,h.selectRange):nd(c)?w(Vs,l,p,Ys,r,e,$s):Zs(c)?w(Vs,l,p,Js,r,e,Us):S.none},function(u){var n=function(n){return function(){return L(n,function(n){return e=n.rows,t=n.cols,r=g,Kt(u,e,t,(o=h).firstSelectedSelector,o.lastSelectedSelector).map(function(n){return o.clearBeforeUpdate(r),o.selectRange(r,n.boxes,n.start,n.finish),n.boxes});var e,t,r,o}).fold(function(){return Gt(g,h.firstSelectedSelector,h.lastSelectedSelector).map(function(n){var e=nd(c)||i.isForward(c)?uf.after:uf.before;return l.setRelativeSelection(uf.on(n.first,0),e(n.table)),h.clear(g),Yf(S.none(),!0)})},function(n){return S.some(Yf(S.none(),!0))})}};return nd(c)&&a?n([ud(1,0)]):Zs(c)&&a?n([ud(-1,0)]):i.isBackward(c)&&a?n([ud(0,-1),ud(-1,0)]):i.isForward(c)&&a?n([ud(0,1),ud(1,0)]):ed(c)&&!1==a?f:S.none})()},keyup:function(l,f,s,d,m){return $t(g,h.selectedSelector).fold(function(){var t,r,n,e,o,u,i,c=l.raw,a=c.which;return!1!=(!0===c.shiftKey)&&ed(a)?(t=g,r=p,n=f,e=s,o=d,u=m,i=h.selectRange,Bn(n,o)&&e===u?S.none():Oe(n,"td,th",r).bind(function(e){return Oe(o,"td,th",r).bind(function(n){return ns(t,r,e,n,i)})})):S.none()},S.none)}}},ad=function(n,r,e,o){var u=od(n);return function(n,t){o.clearBeforeUpdate(r),Ut(n,t,e).each(function(n){var e=n.boxes.getOr([]);o.selectRange(r,e,n.start,n.finish),u.selectContents(t),u.collapseSelection()})}},ld=function(r,n){E(n,function(n){var e,t;t=n,Vu(e=r)?e.dom.classList.remove(t):$u(e,t),Ku(e)})},fd={byClass:function(o){var e,t,u=(e=o.selected,function(n){Gu(n,e)}),r=(t=[o.selected,o.lastSelected,o.firstSelected],function(n){ld(n,t)}),i=function(n){var e=Ce(n,o.selectedSelector);E(e,r)};return{clearBeforeUpdate:i,clear:i,selectRange:function(n,e,t,r){i(n),E(e,u),Gu(t,o.firstSelected),Gu(r,o.lastSelected)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,u,e){var t=function(n){ke(n,o.selected),ke(n,o.firstSelected),ke(n,o.lastSelected)},i=function(n){Be(n,o.selected,"1")},c=function(n){r(n),e()},r=function(n){var e=Ce(n,o.selectedSelector);E(e,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(n,e,t,r){c(n),E(e,i),Be(t,o.firstSelected,"1"),Be(r,o.lastSelected,"1"),u(e,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},sd=function(n,e,s){var d=lt.fromTable(n);return Wc(d,e).map(function(n){var t,e,r,o,u,i,c,a,l,f=Ic(d,s,!1);return{upOrLeftCells:(t=n,e=s,r=f.slice(0,t[t.length-1].row+1),o=kc(r,e),_(o,function(n){var e=n.cells.slice(0,t[t.length-1].column+1);return B(e,function(n){return n.element})})),downOrRightCells:(i=n,c=s,a=(u=f).slice(i[0].row+i[0].rowspan-1,u.length),l=kc(a,c),_(l,function(n){var e=n.cells.slice(i[0].column+i[0].colspan-1,n.cells.length);return B(e,function(n){return n.element})}))}})},dd=function(n){return!1===Xu(Dn.fromDom(n.target),"ephox-snooker-resizer-bar")};function md(d,m,n){var g=fd.byAttr(Er,function(u,i,c){n.targets().each(function(o){et(i).each(function(n){var e=Wi(d),t=xr(y,Dn.fromDom(d.getDoc()),e),r=sd(n,o,t);bi(d,u,i,c,r)})})},function(){return wi(d)});return d.on("init",function(n){var r=d.getWin(),o=Fi(d),e=qi(d),t=id(r,o,e,g),i=cd(r,o,e,g),u=ad(r,o,e,g);d.on("TableSelectorChange",function(n){return u(n.start,n.finish)});var c,a,l=function(n,e){!0===n.raw.shiftKey&&(e.kill&&n.kill(),e.selection.each(function(n){var e=af.relative(n.start,n.finish),t=hf(r,e);d.selection.setRng(t)}))},f=function(n){return 0===n.button},s=(c=Xi(Dn.fromDom(o)),a=Xi(0),{touchEnd:function(n){var e,t,r=Dn.fromDom(n.target);"td"!==Gn(r)&&"th"!==Gn(r)||(e=c.get(),t=a.get(),Bn(e,r)&&n.timeStamp-t<300&&(n.preventDefault(),u(r,r))),c.set(r),a.set(n.timeStamp)}});d.on("dragstart",function(n){t.clearstate()}),d.on("mousedown",function(n){f(n)&&dd(n)&&t.mousedown(Hu(n))}),d.on("mouseover",function(n){var e;((e=n).buttons===undefined||ql.browser.isEdge()&&0===e.buttons||0!=(1&e.buttons))&&dd(n)&&t.mouseover(Hu(n))}),d.on("mouseup",function(n){f(n)&&dd(n)&&t.mouseup(Hu(n))}),d.on("touchend",s.touchEnd),d.on("keyup",function(n){var e,t,r,o=Hu(n);o.raw.shiftKey&&ed(o.raw.which)&&(e=d.selection.getRng(),t=Dn.fromDom(e.startContainer),r=Dn.fromDom(e.endContainer),i.keyup(o,t,e.startOffset,r,e.endOffset).each(function(n){l(o,n)}))}),d.on("keydown",function(n){var e=Hu(n);m().each(function(n){return n.hideBars()});var t=d.selection.getRng(),r=Dn.fromDom(t.startContainer),o=Dn.fromDom(t.endContainer),u=Lr(td,rd)(Dn.fromDom(d.selection.getStart()));i.keydown(e,r,t.startOffset,o,t.endOffset,u).each(function(n){l(e,n)}),m().each(function(n){return n.showBars()})}),d.on("NodeChange",function(){var n=d.selection,e=Dn.fromDom(n.getStart()),t=Dn.fromDom(n.getEnd());qt(et,[e,t]).fold(function(){return g.clear(o)},y)})}),{clear:g.clear}}var gd=function(n,t){var o=Xi(S.none()),u=Xi([]),e=function(){return Qa(Ki(n)).bind(function(e){var n=et(e);return n.map(function(n){return"caption"===Gn(e)?Ir(e):Pr(t,n,e)})})},r=function(){o.set(U(e)()),E(u.get(),function(n){return n()})},i=function(e,t){var r=function(){return o.get().fold(function(){e.setDisabled(!0)},function(n){e.setDisabled(t(n))})};return r(),u.set(u.get().concat([r])),function(){u.set(I(u.get(),function(n){return n!==r}))}};return n.on("NodeChange ExecCommand TableSelectorChange",r),{onSetupTable:function(n){return i(n,function(n){return!1})},onSetupCellOrRow:function(n){return i(n,function(n){return"caption"===Gn(n.element)})},onSetupPasteable:function(e){return function(n){return i(n,function(n){return"caption"===Gn(n.element)||e().isNone()})}},onSetupMergeable:function(n){return i(n,function(n){return n.mergable.isNone()})},onSetupUnmergeable:function(n){return i(n,function(n){return n.unmergable.isNone()})},resetTargets:r,targets:function(){return o.get()}}},pd=function(e,n,t){e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(n){return n("inserttable | cell row column | advtablesort | tableprops deletetable")}});var r=function(n){return function(){return e.execCommand(n)}};e.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:r("mceTableProps"),icon:"table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:r("mceTableDelete"),icon:"table-delete-table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:r("mceTableCellProps"),icon:"table-cell-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:r("mceTableMergeCells"),icon:"table-merge-cells",onSetup:n.onSetupMergeable}),e.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:r("mceTableSplitCells"),icon:"table-split-cells",onSetup:n.onSetupUnmergeable}),e.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:r("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:r("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:r("mceTableDeleteRow"),icon:"table-delete-row",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:r("mceTableRowProps"),icon:"table-row-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:r("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:r("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:r("mceTableDeleteCol"),icon:"table-delete-column",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",icon:"cut-row",onAction:r("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",icon:"duplicate-row",onAction:r("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",icon:"paste-row-before",onAction:r("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",icon:"paste-row-after",onAction:r("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablecutcol",{tooltip:"Cut column",icon:"cut-column",onAction:r("mceTableCutCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopycol",{tooltip:"Copy column",icon:"duplicate-column",onAction:r("mceTableCopyCol"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepastecolbefore",{tooltip:"Paste column before",icon:"paste-column-before",onAction:r("mceTablePasteColBefore"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addButton("tablepastecolafter",{tooltip:"Paste column after",icon:"paste-column-after",onAction:r("mceTablePasteColAfter"),onSetup:n.onSetupPasteable(t.getColumns)}),e.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:r("mceInsertTable"),icon:"table"})},hd=function(e){var n=e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol");0<n.length&&e.ui.registry.addContextToolbar("table",{predicate:function(n){return e.dom.is(n,"table")&&e.getBody().contains(n)},items:n,scope:"node",position:"node"})},vd=function(r,n,e){var t=function(n){return function(){return r.execCommand(n)}},o=function(n){var e=n.numRows,t=n.numColumns;r.undoManager.transact(function(){cl(r,t,e,0,0)}),r.addVisual()},u={text:"Table properties",onSetup:n.onSetupTable,onAction:t("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:n.onSetupTable,onAction:t("mceTableDelete")};r.ui.registry.addMenuItem("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",onAction:t("mceTableInsertRowBefore"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",onAction:t("mceTableInsertRowAfter"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tabledeleterow",{text:"Delete row",icon:"table-delete-row",onAction:t("mceTableDeleteRow"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablerowprops",{text:"Row properties",icon:"table-row-properties",onAction:t("mceTableRowProps"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecutrow",{text:"Cut row",icon:"cut-row",onAction:t("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecopyrow",{text:"Copy row",icon:"duplicate-row",onAction:t("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",onAction:t("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(e.getRows)}),r.ui.registry.addMenuItem("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",onAction:t("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(e.getRows)});r.ui.registry.addMenuItem("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",onAction:t("mceTableInsertColBefore"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",onAction:t("mceTableInsertColAfter"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",onAction:t("mceTableDeleteCol"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecutcolumn",{text:"Cut column",icon:"cut-column",onAction:t("mceTableCutCol"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",onAction:t("mceTableCopyCol"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",onAction:t("mceTablePasteColBefore"),onSetup:n.onSetupPasteable(e.getColumns)}),r.ui.registry.addMenuItem("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",onAction:t("mceTablePasteColAfter"),onSetup:n.onSetupPasteable(e.getColumns)});r.ui.registry.addMenuItem("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",onAction:t("mceTableCellProps"),onSetup:n.onSetupCellOrRow}),r.ui.registry.addMenuItem("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",onAction:t("mceTableMergeCells"),onSetup:n.onSetupMergeable}),r.ui.registry.addMenuItem("tablesplitcells",{text:"Split cell",icon:"table-split-cells",onAction:t("mceTableSplitCells"),onSetup:n.onSetupUnmergeable});!1===r.getParam("table_grid",!0,"boolean")?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:t("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:o}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:t("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",u),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return"tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter"}}),r.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return"tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter"}}),r.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return"tablecellprops tablemergecells tablesplitcells"}}),r.ui.registry.addContextMenu("table",{update:function(){return n.resetTargets(),n.targets().fold(function(){return""},function(n){return"caption"===Gn(n.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})};function bd(e){var n=rr(function(){return Fi(e)},function(){return Qa(Ki(e))},Er.selectedSelector),t=gd(e,n),r=lc(e),o=md(e,r.lazyResize,t),u=el(e,r.lazyWire,n),i=Jl();return Xl(e,u,o,n,i),Yl(e,u,n),kr(e,n,u,o),vd(e,t,i),pd(e,t,i),hd(e),e.on("PreInit",function(){e.serializer.addTempAttr(Er.firstSelected),e.serializer.addTempAttr(Er.lastSelected),Zl(e)}),Oi(e)&&e.on("keydown",function(n){Xf(n,e,u)}),e.on("remove",function(){r.destroy()}),sl(e,i,r,t)}or.add("table",bd)}();