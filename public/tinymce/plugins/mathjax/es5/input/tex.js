!function(t){var e={};function r(n){if(e[n])return e[n].exports;var a=e[n]={i:n,l:!1,exports:{}};return t[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)r.d(n,a,function(e){return t[e]}.bind(null,a));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=54)}([function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(e,r){for(var n=[],a=2;a<arguments.length;a++)n[a-2]=arguments[a];this.id=e,this.message=t.processString(r,n)}return t.processString=function(e,r){for(var n=e.split(t.pattern),a=1,i=n.length;a<i;a+=2){var o=n[a].charAt(0);if(o>="0"&&o<="9")n[a]=r[parseInt(n[a],10)-1],"number"==typeof n[a]&&(n[a]=n[a].toString());else if("{"===o){if((o=n[a].substr(1))>="0"&&o<="9")n[a]=r[parseInt(n[a].substr(1,n[a].length-2),10)-1],"number"==typeof n[a]&&(n[a]=n[a].toString());else n[a].match(/^\{([a-z]+):%(\d+)\|(.*)\}$/)&&(n[a]="%"+n[a])}null==n[a]&&(n[a]="???")}return n.join("")},t.pattern=/%(\d+|\{\d+\}|\{[a-z]+:\%\d+(?:\|(?:%\{\d+\}|%.|[^\}])*)+\}|.)/g,t}();e.default=n},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var o,s=r(5),l=r(46);!function(t){var e=new Map([["autoOP",!0],["fnOP",!0],["movesupsub",!0],["subsupOK",!0],["texprimestyle",!0],["useHeight",!0],["variantForm",!0],["withDelims",!0],["open",!0],["close",!0]]);function r(t,r){var a,i;try{for(var o=n(Object.keys(r)),s=o.next();!s.done;s=o.next()){var l=s.value,u=r[l];"texClass"===l?(t.texClass=u,t.setProperty(l,u)):"movablelimits"===l?(t.setProperty("movablelimits",u),(t.isKind("mo")||t.isKind("mstyle"))&&t.attributes.set("movablelimits",u)):"inferred"===l||(e.has(l)?t.setProperty(l,u):t.attributes.set(l,u))}}catch(t){a={error:t}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(a)throw a.error}}}function a(t,e,r){t.childNodes[e]=r,r&&(r.parent=t)}function o(t,e){return t.isKind(e)}t.createEntity=function(t){return String.fromCodePoint(parseInt(t,16))},t.getChildren=function(t){return t.childNodes},t.getText=function(t){return t.getText()},t.appendChildren=function(t,e){var r,a;try{for(var i=n(e),o=i.next();!o.done;o=i.next()){var s=o.value;t.appendChild(s)}}catch(t){r={error:t}}finally{try{o&&!o.done&&(a=i.return)&&a.call(i)}finally{if(r)throw r.error}}},t.setAttribute=function(t,e,r){t.attributes.set(e,r)},t.setProperty=function(t,e,r){t.setProperty(e,r)},t.setProperties=r,t.getProperty=function(t,e){return t.getProperty(e)},t.getAttribute=function(t,e){return t.attributes.get(e)},t.removeProperties=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.removeProperty.apply(t,i(e))},t.getChildAt=function(t,e){return t.childNodes[e]},t.setChild=a,t.copyChildren=function(t,e){for(var r=t.childNodes,n=0;n<r.length;n++)a(e,n,r[n])},t.copyAttributes=function(t,e){e.attributes=t.attributes,r(e,t.getAllProperties())},t.isType=o,t.isEmbellished=function(t){return t.isEmbellished},t.getTexClass=function(t){return t.texClass},t.getCoreMO=function(t){return t.coreMO()},t.isNode=function(t){return t instanceof s.AbstractMmlNode||t instanceof s.AbstractMmlEmptyNode},t.isInferred=function(t){return t.isInferred},t.getForm=function(t){var e,r;if(!o(t,"mo"))return null;var a=t,i=a.getForms();try{for(var s=n(i),u=s.next();!u.done;u=s.next()){var c=u.value,f=l.MmlMo.OPTABLE[c][a.getText()];if(f)return f}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=s.return)&&r.call(s)}finally{if(e)throw e.error}}return null}}(o||(o={})),e.default=o},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.EnvironmentMap=e.CommandMap=e.MacroMap=e.DelimiterMap=e.CharacterMap=e.AbstractParseMap=e.RegExpMap=e.AbstractSymbolMap=void 0;var l=r(10),u=r(12),c=function(){function t(t,e){this._name=t,this._parser=e,u.MapHandler.register(this)}return Object.defineProperty(t.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),t.prototype.parserFor=function(t){return this.contains(t)?this.parser:null},t.prototype.parse=function(t){var e=i(t,2),r=e[0],n=e[1],a=this.parserFor(n),o=this.lookup(n);return a&&o?a(r,o)||!0:null},Object.defineProperty(t.prototype,"parser",{get:function(){return this._parser},set:function(t){this._parser=t},enumerable:!1,configurable:!0}),t}();e.AbstractSymbolMap=c;var f=function(t){function e(e,r,n){var a=t.call(this,e,r)||this;return a._regExp=n,a}return a(e,t),e.prototype.contains=function(t){return this._regExp.test(t)},e.prototype.lookup=function(t){return this.contains(t)?t:null},e}(c);e.RegExpMap=f;var p=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.map=new Map,e}return a(e,t),e.prototype.lookup=function(t){return this.map.get(t)},e.prototype.contains=function(t){return this.map.has(t)},e.prototype.add=function(t,e){this.map.set(t,e)},e.prototype.remove=function(t){this.map.delete(t)},e}(c);e.AbstractParseMap=p;var d=function(t){function e(e,r,n){var a,s,u=t.call(this,e,r)||this;try{for(var c=o(Object.keys(n)),f=c.next();!f.done;f=c.next()){var p=f.value,d=n[p],h=i("string"==typeof d?[d,null]:d,2),m=h[0],g=h[1],y=new l.Symbol(p,m,g);u.add(p,y)}}catch(t){a={error:t}}finally{try{f&&!f.done&&(s=c.return)&&s.call(c)}finally{if(a)throw a.error}}return u}return a(e,t),e}(p);e.CharacterMap=d;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.parse=function(e){var r=i(e,2),n=r[0],a=r[1];return t.prototype.parse.call(this,[n,"\\"+a])},e}(d);e.DelimiterMap=h;var m=function(t){function e(e,r,n){var a,s,u=t.call(this,e,null)||this;try{for(var c=o(Object.keys(r)),f=c.next();!f.done;f=c.next()){var p=f.value,d=r[p],h=i("string"==typeof d?[d]:d),m=h[0],g=h.slice(1),y=new l.Macro(p,n[m],g);u.add(p,y)}}catch(t){a={error:t}}finally{try{f&&!f.done&&(s=c.return)&&s.call(c)}finally{if(a)throw a.error}}return u}return a(e,t),e.prototype.parserFor=function(t){var e=this.lookup(t);return e?e.func:null},e.prototype.parse=function(t){var e=i(t,2),r=e[0],n=e[1],a=this.lookup(n),o=this.parserFor(n);return a&&o?o.apply(void 0,s([r,a.symbol],a.args))||!0:null},e}(p);e.MacroMap=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.parse=function(t){var e=i(t,2),r=e[0],n=e[1],a=this.lookup(n),o=this.parserFor(n);if(!a||!o)return null;if(!o)return null;var l=r.currentCS;r.currentCS="\\"+n;var u=o.apply(void 0,s([r,"\\"+a.symbol],a.args));return r.currentCS=l,u||!0},e}(m);e.CommandMap=g;var y=function(t){function e(e,r,n,a){var i=t.call(this,e,n,a)||this;return i.parser=r,i}return a(e,t),e.prototype.parse=function(t){var e=i(t,2),r=e[0],n=e[1],a=this.lookup(n),o=this.parserFor(n);return a&&o?(this.parser(r,a.symbol,o,a.args),!0):null},e}(m);e.EnvironmentMap=y},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var i,o=r(5),s=r(1),l=r(8),u=r(0),c=r(11);r(33),function(t){var e={em:function(t){return t},ex:function(t){return.43*t},pt:function(t){return t/10},pc:function(t){return 1.2*t},px:function(t){return 7.2*t/72},in:function(t){return 7.2*t},cm:function(t){return 7.2*t/2.54},mm:function(t){return 7.2*t/25.4},mu:function(t){return t/18}},r="([-+]?([.,]\\d+|\\d+([.,]\\d*)?))",i="(pt|em|ex|mu|px|mm|cm|in|pc)",f=RegExp("^\\s*"+r+"\\s*"+i+"\\s*$"),p=RegExp("^\\s*"+r+"\\s*"+i+" ?");function d(t,r){void 0===r&&(r=!1);var a=t.match(r?p:f);return a?function(t){var r=n(t,3),a=r[0],i=r[1],o=r[2];if("mu"!==i)return[a,i,o];return[h(e[i](parseFloat(a||"1"))).slice(0,-2),"em",o]}([a[1].replace(/,/,"."),a[4],a[0].length]):[null,null,0]}function h(t){return Math.abs(t)<6e-4?"0em":t.toFixed(3).replace(/\.?0+$/,"")+"em"}function m(t,e,r){"{"!==e&&"}"!==e||(e="\\"+e);var n="{\\bigg"+r+" "+e+"}",a="{\\big"+r+" "+e+"}";return new l.default("\\mathchoice"+n+a+a+a,{},t).mml()}function g(t,e,r){e=e.replace(/^\s+/,c.entities.nbsp).replace(/\s+$/,c.entities.nbsp);var n=t.create("text",e);return t.create("node","mtext",[],r,n)}function y(t,e,r){if(r.match(/^[a-z]/i)&&e.match(/(^|[^\\])(\\\\)*\\[a-z]+$/i)&&(e+=" "),e.length+r.length>t.configuration.options.maxBuffer)throw new u.default("MaxBufferSize","MathJax internal buffer size exceeded; is there a recursive macro call?");return e+r}function v(t,e){for(;e>0;)t=t.trim().slice(1,-1),e--;return t.trim()}function b(t,e){for(var r=t.length,n=0,a="",i=0,o=0,s=!0,l=!1;i<r;){var c=t[i++];switch(c){case" ":break;case"{":s?o++:(l=!1,o>n&&(o=n)),n++;break;case"}":n&&n--,(s||l)&&(o--,l=!0),s=!1;break;default:if(!n&&-1!==e.indexOf(c))return[l?"true":v(a,o),c,t.slice(i)];s=!1,l=!1}a+=c}if(n)throw new u.default("ExtraOpenMissingClose","Extra open brace or missing close brace");return[l?"true":v(a,o),"",t.slice(i)]}t.matchDimen=d,t.dimen2em=function(t){var r=n(d(t),2),a=r[0],i=r[1],o=parseFloat(a||"1"),s=e[i];return s?s(o):0},t.Em=h,t.fenced=function(t,e,r,n,a){void 0===a&&(a="");var i,u=t.nodeFactory,c=u.create("node","mrow",[],{open:e,close:n,texClass:o.TEXCLASS.INNER});if(a)i=new l.default("\\"+a+"l"+e,t.parser.stack.env,t).mml();else{var f=u.create("text",e);i=u.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:o.TEXCLASS.OPEN},f)}if(s.default.appendChildren(c,[i]),s.default.isType(r,"mrow")&&s.default.isInferred(r)?s.default.appendChildren(c,s.default.getChildren(r)):s.default.appendChildren(c,[r]),a)i=new l.default("\\"+a+"r"+n,t.parser.stack.env,t).mml();else{var p=u.create("text",n);i=u.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:o.TEXCLASS.CLOSE},p)}return s.default.appendChildren(c,[i]),c},t.fixedFence=function(t,e,r,n){var a=t.nodeFactory.create("node","mrow",[],{open:e,close:n,texClass:o.TEXCLASS.ORD});return e&&s.default.appendChildren(a,[m(t,e,"l")]),s.default.isType(r,"mrow")?s.default.appendChildren(a,s.default.getChildren(r)):s.default.appendChildren(a,[r]),n&&s.default.appendChildren(a,[m(t,n,"r")]),a},t.mathPalette=m,t.fixInitialMO=function(t,e){for(var r=0,n=e.length;r<n;r++){var a=e[r];if(a&&!s.default.isType(a,"mspace")&&(!s.default.isType(a,"TeXAtom")||s.default.getChildren(a)[0]&&s.default.getChildren(s.default.getChildren(a)[0]).length)){if(s.default.isEmbellished(a)||s.default.isType(a,"TeXAtom")&&s.default.getTexClass(a)===o.TEXCLASS.REL){var i=t.nodeFactory.create("node","mi");e.unshift(i)}break}}},t.internalMath=function(t,e,r,n){if(t.configuration.options.internalMath)return t.configuration.options.internalMath(t,e,r,n);var a,i,o=n||t.stack.env.font,s=o?{mathvariant:o}:{},c=[],f=0,p=0,d="",h=0;if(e.match(/\\?[${}\\]|\\\(|\\(eq)?ref\s*\{/)){for(;f<e.length;)if("$"===(a=e.charAt(f++)))"$"===d&&0===h?(i=t.create("node","TeXAtom",[new l.default(e.slice(p,f-1),{},t.configuration).mml()]),c.push(i),d="",p=f):""===d&&(p<f-1&&c.push(g(t,e.slice(p,f-1),s)),d="$",p=f);else if("{"===a&&""!==d)h++;else if("}"===a)if("}"===d&&0===h){var m=new l.default(e.slice(p,f),{},t.configuration).mml();i=t.create("node","TeXAtom",[m],s),c.push(i),d="",p=f}else""!==d&&h&&h--;else if("\\"===a)if(""===d&&e.substr(f).match(/^(eq)?ref\s*\{/)){var y=RegExp["$&"].length;p<f-1&&c.push(g(t,e.slice(p,f-1),s)),d="}",p=f-1,f+=y}else"("===(a=e.charAt(f++))&&""===d?(p<f-2&&c.push(g(t,e.slice(p,f-2),s)),d=")",p=f):")"===a&&")"===d&&0===h?(i=t.create("node","TeXAtom",[new l.default(e.slice(p,f-2),{},t.configuration).mml()]),c.push(i),d="",p=f):a.match(/[${}\\]/)&&""===d&&(f--,e=e.substr(0,f-1)+e.substr(f));if(""!==d)throw new u.default("MathNotTerminated","Math not terminated in text box")}return p<e.length&&c.push(g(t,e.slice(p),s)),null!=r?c=[t.create("node","mstyle",c,{displaystyle:!1,scriptlevel:r})]:c.length>1&&(c=[t.create("node","mrow",c)]),c},t.internalText=g,t.trimSpaces=function(t){if("string"!=typeof t)return t;var e=t.trim();return e.match(/\\$/)&&t.match(/ $/)&&(e+=" "),e},t.setArrayAlign=function(e,r){return"t"===(r=t.trimSpaces(r||""))?e.arraydef.align="baseline 1":"b"===r?e.arraydef.align="baseline -1":"c"===r?e.arraydef.align="center":r&&(e.arraydef.align=r),e},t.substituteArgs=function(t,e,r){for(var n="",a="",i=0;i<r.length;){var o=r.charAt(i++);if("\\"===o)n+=o+r.charAt(i++);else if("#"===o)if("#"===(o=r.charAt(i++)))n+=o;else{if(!o.match(/[1-9]/)||parseInt(o,10)>e.length)throw new u.default("IllegalMacroParam","Illegal macro parameter reference");a=y(t,y(t,a,n),e[parseInt(o,10)-1]),n=""}else n+=o}return y(t,a,n)},t.addArgs=y,t.checkEqnEnv=function(t){if(t.stack.global.eqnenv)throw new u.default("ErroneousNestingEq","Erroneous nesting of equation structures");t.stack.global.eqnenv=!0},t.MmlFilterAttribute=function(t,e,r){return r},t.getFontDef=function(t){var e=t.stack.env.font;return e?{mathvariant:e}:{}},t.keyvalOptions=function(t,e,r){var i,o;void 0===e&&(e=null),void 0===r&&(r=!1);var s=function(t){var e,r,a,i,o,s={},l=t;for(;l;)i=(e=n(b(l,["=",","]),3))[0],a=e[1],l=e[2],"="===a?(o=(r=n(b(l,[","]),3))[0],a=r[1],l=r[2],o="false"===o||"true"===o?JSON.parse(o):o,s[i]=o):i&&(s[i]=!0);return s}(t);if(e)try{for(var l=a(Object.keys(s)),c=l.next();!c.done;c=l.next()){var f=c.value;if(!e.hasOwnProperty(f)){if(r)throw new u.default("InvalidOption","Invalid optional argument: %1",f);delete s[f]}}}catch(t){i={error:t}}finally{try{c&&!c.done&&(o=l.return)&&o.call(l)}finally{if(i)throw i.error}}return s}}(i||(i={})),e.default=i},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.ParserConfiguration=e.ConfigurationHandler=e.Configuration=void 0;var i,o=r(6),s=r(12),l=r(35),u=r(34),c=r(9),f=function(){function t(t,e,r,n,a,i,o,s,l,u,c,f){void 0===e&&(e={}),void 0===r&&(r={}),void 0===n&&(n={}),void 0===a&&(a={}),void 0===i&&(i={}),void 0===o&&(o={}),void 0===s&&(s=[]),void 0===l&&(l=[]),void 0===u&&(u=null),void 0===c&&(c=null),this.name=t,this.handler=e,this.fallback=r,this.items=n,this.tags=a,this.options=i,this.nodes=o,this.preprocessors=s,this.postprocessors=l,this.initMethod=u,this.configMethod=c,this.priority=f,this.handler=Object.assign({character:[],delimiter:[],macro:[],environment:[]},e)}return t.makeProcessor=function(t,e){return Array.isArray(t)?t:[t,e]},t._create=function(e,r){var n=this;void 0===r&&(r={});var a=r.priority||u.PrioritizedList.DEFAULTPRIORITY,i=r.init?this.makeProcessor(r.init,a):null,o=r.config?this.makeProcessor(r.config,a):null,s=(r.preprocessors||[]).map((function(t){return n.makeProcessor(t,a)})),l=(r.postprocessors||[]).map((function(t){return n.makeProcessor(t,a)}));return new t(e,r.handler||{},r.fallback||{},r.items||{},r.tags||{},r.options||{},r.nodes||{},s,l,i,o,a)},t.create=function(e,r){void 0===r&&(r={});var n=t._create(e,r);return i.set(e,n),n},t.local=function(e){return void 0===e&&(e={}),t._create("",e)},Object.defineProperty(t.prototype,"init",{get:function(){return this.initMethod?this.initMethod[0]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"config",{get:function(){return this.configMethod?this.configMethod[0]:null},enumerable:!1,configurable:!0}),t}();e.Configuration=f,function(t){var e=new Map;t.set=function(t,r){e.set(t,r)},t.get=function(t){return e.get(t)},t.keys=function(){return e.keys()}}(i=e.ConfigurationHandler||(e.ConfigurationHandler={}));var p=function(){function t(t){var e,r,a,i;this.initMethod=new l.FunctionList,this.configMethod=new l.FunctionList,this.configurations=new u.PrioritizedList,this.handlers=new s.SubHandlers,this.items={},this.tags={},this.options={},this.nodes={};try{for(var o=n(t.slice().reverse()),c=o.next();!c.done;c=o.next()){var f=c.value;this.addPackage(f)}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}try{for(var p=n(this.configurations),d=p.next();!d.done;d=p.next()){var h=d.value,m=h.item,g=h.priority;this.append(m,g)}}catch(t){a={error:t}}finally{try{d&&!d.done&&(i=p.return)&&i.call(p)}finally{if(a)throw a.error}}}return t.prototype.init=function(){this.initMethod.execute(this)},t.prototype.config=function(t){var e,r;this.configMethod.execute(this,t);try{for(var a=n(this.configurations),i=a.next();!i.done;i=a.next()){var o=i.value;this.addFilters(t,o.item)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},t.prototype.addPackage=function(t){var e="string"==typeof t?t:t[0],r=i.get(e);r&&this.configurations.add(r,"string"==typeof t?r.priority:t[1])},t.prototype.add=function(t,e,r){var a,i;void 0===r&&(r={}),this.append(t),this.configurations.add(t,t.priority),this.init();var s=e.parseOptions;s.nodeFactory.setCreators(t.nodes);try{for(var l=n(Object.keys(t.items)),u=l.next();!u.done;u=l.next()){var f=u.value;s.itemFactory.setNodeClass(f,t.items[f])}}catch(t){a={error:t}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(a)throw a.error}}c.TagsFactory.addTags(t.tags),o.defaultOptions(s.options,t.options),o.userOptions(s.options,r),this.addFilters(e,t),t.config&&t.config(this,e)},t.prototype.append=function(t,e){e=e||t.priority,t.initMethod&&this.initMethod.add(t.initMethod[0],t.initMethod[1]),t.configMethod&&this.configMethod.add(t.configMethod[0],t.configMethod[1]),this.handlers.add(t.handler,t.fallback,e),Object.assign(this.items,t.items),Object.assign(this.tags,t.tags),o.defaultOptions(this.options,t.options),Object.assign(this.nodes,t.nodes)},t.prototype.addFilters=function(t,e){var r,i,o,s;try{for(var l=n(e.preprocessors),u=l.next();!u.done;u=l.next()){var c=a(u.value,2),f=c[0],p=c[1];t.preFilters.add(f,p)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(r)throw r.error}}try{for(var d=n(e.postprocessors),h=d.next();!h.done;h=d.next()){var m=a(h.value,2),g=m[0];p=m[1];t.postFilters.add(g,p)}}catch(t){o={error:t}}finally{try{h&&!h.done&&(s=d.return)&&s.call(d)}finally{if(o)throw o.error}}},t}();e.ParserConfiguration=p},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TexConstant=void 0,function(t){t.Variant={NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"inital",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALLIGRAPHIC:"-tex-calligraphic",OLDSTYLE:"-tex-oldstyle"},t.Form={PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},t.LineBreak={AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},t.LineBreakStyle={BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},t.IndentAlign={LEFT:"left",CENTER:"center",RIGHT:"right",AUTO:"auto",ID:"id",INDENTALIGN:"indentalign"},t.IndentShift={INDENTSHIFT:"indentshift"},t.LineThickness={THIN:"thin",MEDIUM:"medium",THICK:"thick"},t.Notation={LONGDIV:"longdiv",ACTUARIAL:"actuarial",PHASORANGLE:"phasorangle",RADICAL:"radical",BOX:"box",ROUNDEDBOX:"roundedbox",CIRCLE:"circle",LEFT:"left",RIGHT:"right",TOP:"top",BOTTOM:"bottom",UPDIAGONALSTRIKE:"updiagonalstrike",DOWNDIAGONALSTRIKE:"downdiagonalstrike",VERTICALSTRIKE:"verticalstrike",HORIZONTALSTRIKE:"horizontalstrike",NORTHEASTARROW:"northeastarrow",MADRUWB:"madruwb",UPDIAGONALARROW:"updiagonalarrow"},t.Align={TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},t.Lines={NONE:"none",SOLID:"solid",DASHED:"dashed"},t.Side={LEFT:"left",RIGHT:"right",LEFTOVERLAP:"leftoverlap",RIGHTOVERLAP:"rightoverlap"},t.Width={AUTO:"auto",FIT:"fit"},t.Actiontype={TOGGLE:"toggle",STATUSLINE:"statusline",TOOLTIP:"tooltip",INPUT:"input"},t.Length={VERYVERYTHINMATHSPACE:"veryverythinmathspace",VERYTHINMATHSPACE:"verythinmathspace",THINMATHSPACE:"thinmathspace",MEDIUMMATHSPACE:"mediummathspace",THICKMATHSPACE:"thickmathspace",VERYTHICKMATHSPACE:"verythickmathspace",VERYVERYTHICKMATHSPACE:"veryverythickmathspace",NEGATIVEVERYVERYTHINMATHSPACE:"negativeveryverythinmathspace",NEGATIVEVERYTHINMATHSPACE:"negativeverythinmathspace",NEGATIVETHINMATHSPACE:"negativethinmathspace",NEGATIVEMEDIUMMATHSPACE:"negativemediummathspace",NEGATIVETHICKMATHSPACE:"negativethickmathspace",NEGATIVEVERYTHICKMATHSPACE:"negativeverythickmathspace",NEGATIVEVERYVERYTHICKMATHSPACE:"negativeveryverythickmathspace"},t.Overflow={LINBREAK:"linebreak",SCROLL:"scroll",ELIDE:"elide",TRUNCATE:"truncate",SCALE:"scale"},t.Unit={EM:"em",EX:"ex",PX:"px",IN:"in",CM:"cm",MM:"mm",PT:"pt",PC:"pc"}}(e.TexConstant||(e.TexConstant={}))},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var o=r(3),s=r(24),l=r(0),u=r(5),c=function(){function t(t,e,r){var a,i;this._string=t,this.configuration=r,this.macroCount=0,this.i=0,this.currentCS="";var o,l=e.hasOwnProperty("isInner"),u=e.isInner;if(delete e.isInner,e){o={};try{for(var c=n(Object.keys(e)),f=c.next();!f.done;f=c.next()){var p=f.value;o[p]=e[p]}}catch(t){a={error:t}}finally{try{f&&!f.done&&(i=c.return)&&i.call(c)}finally{if(a)throw a.error}}}this.configuration.pushParser(this),this.stack=new s.default(this.itemFactory,o,!l||u),this.Parse(),this.Push(this.itemFactory.create("stop"))}return Object.defineProperty(t.prototype,"options",{get:function(){return this.configuration.options},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"itemFactory",{get:function(){return this.configuration.itemFactory},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tags",{get:function(){return this.configuration.tags},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"string",{get:function(){return this._string},set:function(t){this._string=t},enumerable:!1,configurable:!0}),t.prototype.parse=function(t,e){return this.configuration.handlers.get(t).parse(e)},t.prototype.lookup=function(t,e){return this.configuration.handlers.get(t).lookup(e)},t.prototype.contains=function(t,e){return this.configuration.handlers.get(t).contains(e)},t.prototype.toString=function(){var t,e,r="";try{for(var a=n(Array.from(this.configuration.handlers.keys())),i=a.next();!i.done;i=a.next()){var o=i.value;r+=o+": "+this.configuration.handlers.get(o)+"\n"}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}return r},t.prototype.Parse=function(){for(var t;this.i<this.string.length;)t=this.getCodePoint(),this.i+=t.length,this.parse("character",[this,t])},t.prototype.Push=function(t){t instanceof u.AbstractMmlNode&&t.isInferred?this.PushAll(t.childNodes):this.stack.Push(t)},t.prototype.PushAll=function(t){var e,r;try{for(var a=n(t),i=a.next();!i.done;i=a.next()){var o=i.value;this.stack.Push(o)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},t.prototype.mml=function(){if(!this.stack.Top().isKind("mml"))return null;var t=this.stack.Top().First;return this.configuration.popParser(),t},t.prototype.convertDelimiter=function(t){var e=this.lookup("delimiter",t);return e?e.char:null},t.prototype.getCodePoint=function(){var t=this.string.codePointAt(this.i);return void 0===t?"":String.fromCodePoint(t)},t.prototype.nextIsSpace=function(){return!!this.string.charAt(this.i).match(/\s/)},t.prototype.GetNext=function(){for(;this.nextIsSpace();)this.i++;return this.getCodePoint()},t.prototype.GetCS=function(){var t=this.string.slice(this.i).match(/^(([a-z]+) ?|[\uD800-\uDBFF].|.)/i);return t?(this.i+=t[0].length,t[2]||t[1]):(this.i++," ")},t.prototype.GetArgument=function(t,e){switch(this.GetNext()){case"":if(!e)throw new l.default("MissingArgFor","Missing argument for %1",this.currentCS);return null;case"}":if(!e)throw new l.default("ExtraCloseMissingOpen","Extra close brace or missing open brace");return null;case"\\":return this.i++,"\\"+this.GetCS();case"{":for(var r=++this.i,n=1;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"\\":this.i++;break;case"{":n++;break;case"}":if(0==--n)return this.string.slice(r,this.i-1)}throw new l.default("MissingCloseBrace","Missing close brace")}var a=this.getCodePoint();return this.i+=a.length,a},t.prototype.GetBrackets=function(t,e){if("["!==this.GetNext())return e;for(var r=++this.i,n=0;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"{":n++;break;case"\\":this.i++;break;case"}":if(n--<=0)throw new l.default("ExtraCloseLooking","Extra close brace while looking for %1","']'");break;case"]":if(0===n)return this.string.slice(r,this.i-1)}throw new l.default("MissingCloseBracket","Could not find closing ']' for argument to %1",this.currentCS)},t.prototype.GetDelimiter=function(t,e){var r=this.GetNext();if(this.i+=r.length,this.i<=this.string.length&&("\\"===r?r+=this.GetCS():"{"===r&&e&&(this.i--,r=this.GetArgument(t)),this.contains("delimiter",r)))return this.convertDelimiter(r);throw new l.default("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)},t.prototype.GetDimen=function(t){if("{"===this.GetNext()){var e=this.GetArgument(t),r=a(o.default.matchDimen(e),2),n=r[0],i=r[1];if(n)return n+i}else{e=this.string.slice(this.i);var s=a(o.default.matchDimen(e,!0),3),u=(n=s[0],i=s[1],s[2]);if(n)return this.i+=u,n+i}throw new l.default("MissingDimOrUnits","Missing dimension or its units for %1",this.currentCS)},t.prototype.GetUpTo=function(t,e){for(;this.nextIsSpace();)this.i++;for(var r=this.i,n=0;this.i<this.string.length;){var a=this.i,i=this.GetNext();switch(this.i+=i.length,i){case"\\":i+=this.GetCS();break;case"{":n++;break;case"}":if(0===n)throw new l.default("ExtraCloseLooking","Extra close brace while looking for %1",e);n--}if(0===n&&i===e)return this.string.slice(r,a)}throw new l.default("TokenNotFoundForCommand","Could not find %1 for %2",e,this.currentCS)},t.prototype.ParseArg=function(e){return new t(this.GetArgument(e),this.stack.env,this.configuration).mml()},t.prototype.ParseUpTo=function(e,r){return new t(this.GetUpTo(e,r),this.stack.env,this.configuration).mml()},t.prototype.GetDelimiterArg=function(t){var e=o.default.trimSpaces(this.GetArgument(t));if(""===e)return null;if(this.contains("delimiter",e))return e;throw new l.default("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)},t.prototype.GetStar=function(){var t="*"===this.GetNext();return t&&this.i++,t},t.prototype.create=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.configuration.nodeFactory).create.apply(e,i([t],r))},t}();e.default=c},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.TagsFactory=e.AllTags=e.NoTags=e.AbstractTags=e.TagInfo=e.Label=void 0;var o=r(8),s=function(t,e){void 0===t&&(t="???"),void 0===e&&(e=""),this.tag=t,this.id=e};e.Label=s;var l=function(t,e,r,n,a,i,o,s){void 0===t&&(t=""),void 0===e&&(e=!1),void 0===r&&(r=!1),void 0===n&&(n=null),void 0===a&&(a=""),void 0===i&&(i=""),void 0===o&&(o=!1),void 0===s&&(s=""),this.env=t,this.taggable=e,this.defaultTags=r,this.tag=n,this.tagId=a,this.tagFormat=i,this.noTag=o,this.labelId=s};e.TagInfo=l;var u=function(){function t(){this.counter=0,this.allCounter=0,this.configuration=null,this.ids={},this.allIds={},this.labels={},this.allLabels={},this.redo=!1,this.refUpdate=!1,this.currentTag=new l,this.history=[],this.stack=[],this.enTag=function(t,e){var r=this.configuration.nodeFactory,n=r.create("node","mtd",[t]),a=r.create("node","mlabeledtr",[e,n]);return r.create("node","mtable",[a],{side:this.configuration.options.tagSide,minlabelspacing:this.configuration.options.tagIndent,displaystyle:!0})}}return t.prototype.start=function(t,e,r){this.currentTag&&this.stack.push(this.currentTag),this.currentTag=new l(t,e,r)},Object.defineProperty(t.prototype,"env",{get:function(){return this.currentTag.env},enumerable:!1,configurable:!0}),t.prototype.end=function(){this.history.push(this.currentTag),this.currentTag=this.stack.pop()},t.prototype.tag=function(t,e){this.currentTag.tag=t,this.currentTag.tagFormat=e?t:this.formatTag(t),this.currentTag.noTag=!1},t.prototype.notag=function(){this.tag("",!0),this.currentTag.noTag=!0},Object.defineProperty(t.prototype,"noTag",{get:function(){return this.currentTag.noTag},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return this.currentTag.labelId},set:function(t){this.currentTag.labelId=t},enumerable:!1,configurable:!0}),t.prototype.formatUrl=function(t,e){return e+"#"+encodeURIComponent(t)},t.prototype.formatTag=function(t){return"("+t+")"},t.prototype.formatId=function(t){return"mjx-eqn-"+t.replace(/\s/g,"_")},t.prototype.formatNumber=function(t){return t.toString()},t.prototype.autoTag=function(){null==this.currentTag.tag&&(this.counter++,this.tag(this.formatNumber(this.counter),!1))},t.prototype.clearTag=function(){this.label="",this.tag(null,!0),this.currentTag.tagId=""},t.prototype.getTag=function(t){if(void 0===t&&(t=!1),t)return this.autoTag(),this.makeTag();var e=this.currentTag;return e.taggable&&!e.noTag&&(e.defaultTags&&this.autoTag(),e.tag)?this.makeTag():null},t.prototype.resetTag=function(){this.history=[],this.redo=!1,this.refUpdate=!1,this.clearTag()},t.prototype.reset=function(t){void 0===t&&(t=0),this.resetTag(),this.counter=this.allCounter=t,this.allLabels={},this.allIds={}},t.prototype.startEquation=function(t){this.labels={},this.ids={},this.counter=this.allCounter,this.redo=!1;var e=t.inputData.recompile;e&&(this.refUpdate=!0,this.counter=e.counter)},t.prototype.finishEquation=function(t){this.redo&&(t.inputData.recompile={state:t.state(),counter:this.allCounter}),this.refUpdate||(this.allCounter=this.counter),Object.assign(this.allIds,this.ids),Object.assign(this.allLabels,this.labels)},t.prototype.finalize=function(t,e){if(!e.display||this.currentTag.env||null==this.currentTag.tag)return t;var r=this.makeTag();return this.enTag(t,r)},t.prototype.makeId=function(){this.currentTag.tagId=this.formatId(this.configuration.options.useLabelIds&&this.label||this.currentTag.tag)},t.prototype.makeTag=function(){this.makeId(),this.label&&(this.labels[this.label]=new s(this.currentTag.tag,this.currentTag.tagId));var t=new o.default("\\text{"+this.currentTag.tagFormat+"}",{},this.configuration).mml();return this.configuration.nodeFactory.create("node","mtd",[t],{id:this.currentTag.tagId})},t}();e.AbstractTags=u;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.autoTag=function(){},e.prototype.getTag=function(){return this.currentTag.tag?t.prototype.getTag.call(this):null},e}(u);e.NoTags=c;var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.finalize=function(t,e){if(!e.display||this.history.find((function(t){return t.taggable})))return t;var r=this.getTag(!0);return this.enTag(t,r)},e}(u);e.AllTags=f,function(t){var e=new Map([["none",c],["all",f]]),r="none";t.OPTIONS={tags:r,tagSide:"right",tagIndent:"0.8em",multlineWidth:"85%",useLabelIds:!0,ignoreDuplicateLabels:!1},t.add=function(t,r){e.set(t,r)},t.addTags=function(e){var r,n;try{for(var a=i(Object.keys(e)),o=a.next();!o.done;o=a.next()){var s=o.value;t.add(s,e[s])}}catch(t){r={error:t}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}},t.create=function(t){var n=e.get(t)||e.get(r);if(!n)throw Error("Unknown tags class");return new n},t.setDefault=function(t){r=t},t.getDefault=function(){return t.create(r)}}(e.TagsFactory||(e.TagsFactory={}))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Macro=e.Symbol=void 0;var n=function(){function t(t,e,r){this._symbol=t,this._char=e,this._attributes=r}return Object.defineProperty(t.prototype,"symbol",{get:function(){return this._symbol},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"char",{get:function(){return this._char},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){return this._attributes},enumerable:!1,configurable:!0}),t}();e.Symbol=n;var a=function(){function t(t,e,r){void 0===r&&(r=[]),this._symbol=t,this._func=e,this._args=r}return Object.defineProperty(t.prototype,"symbol",{get:function(){return this._symbol},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"func",{get:function(){return this._func},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"args",{get:function(){return this._args},enumerable:!1,configurable:!0}),t}();e.Macro=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.options=MathJax._.util.Entities.options,e.entities=MathJax._.util.Entities.entities,e.add=MathJax._.util.Entities.add,e.remove=MathJax._.util.Entities.remove,e.translate=MathJax._.util.Entities.translate,e.numeric=MathJax._.util.Entities.numeric},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.SubHandlers=e.SubHandler=e.MapHandler=void 0;var i,o=r(34),s=r(35);!function(t){var e=new Map;t.register=function(t){e.set(t.name,t)},t.getMap=function(t){return e.get(t)}}(i=e.MapHandler||(e.MapHandler={}));var l=function(){function t(){this._configuration=new o.PrioritizedList,this._fallback=new s.FunctionList}return t.prototype.add=function(t,e,r){var a,s;void 0===r&&(r=o.PrioritizedList.DEFAULTPRIORITY);try{for(var l=n(t.slice().reverse()),u=l.next();!u.done;u=l.next()){var c=u.value,f=i.getMap(c);if(!f)return void this.warn("Configuration "+c+" not found! Omitted.");this._configuration.add(f,r)}}catch(t){a={error:t}}finally{try{u&&!u.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}e&&this._fallback.add(e,r)},t.prototype.parse=function(t){var e,r;try{for(var i=n(this._configuration),o=i.next();!o.done;o=i.next()){var s=o.value.item.parse(t);if(s)return s}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}var l=a(t,2),u=l[0],c=l[1];this._fallback.toArray()[0].item(u,c)},t.prototype.lookup=function(t){var e=this.applicable(t);return e?e.lookup(t):null},t.prototype.contains=function(t){return!!this.applicable(t)},t.prototype.toString=function(){var t,e,r=[];try{for(var a=n(this._configuration),i=a.next();!i.done;i=a.next()){var o=i.value.item;r.push(o.name)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}return r.join(", ")},t.prototype.applicable=function(t){var e,r;try{for(var a=n(this._configuration),i=a.next();!i.done;i=a.next()){var o=i.value.item;if(o.contains(t))return o}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return null},t.prototype.retrieve=function(t){var e,r;try{for(var a=n(this._configuration),i=a.next();!i.done;i=a.next()){var o=i.value.item;if(o.name===t)return o}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return null},t.prototype.warn=function(t){console.log("TexParser Warning: "+t)},t}();e.SubHandler=l;var u=function(){function t(){this.map=new Map}return t.prototype.add=function(t,e,r){var a,i;void 0===r&&(r=o.PrioritizedList.DEFAULTPRIORITY);try{for(var s=n(Object.keys(t)),u=s.next();!u.done;u=s.next()){var c=u.value,f=this.get(c);f||(f=new l,this.set(c,f)),f.add(t[c],e[c],r)}}catch(t){a={error:t}}finally{try{u&&!u.done&&(i=s.return)&&i.call(s)}finally{if(a)throw a.error}}},t.prototype.set=function(t,e){this.map.set(t,e)},t.prototype.get=function(t){return this.map.get(t)},t.prototype.retrieve=function(t){var e,r;try{for(var a=n(this.map.values()),i=a.next();!i.done;i=a.next()){var o=i.value.retrieve(t);if(o)return o}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return null},t.prototype.keys=function(){return this.map.keys()},t}();e.SubHandlers=u},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.BaseItem=e.MmlStack=void 0;var l=r(0),u=function(){function t(t){this._nodes=t}return Object.defineProperty(t.prototype,"nodes",{get:function(){return this._nodes},enumerable:!1,configurable:!0}),t.prototype.Push=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._nodes).push.apply(t,o(e))},t.prototype.Pop=function(){return this._nodes.pop()},Object.defineProperty(t.prototype,"First",{get:function(){return this._nodes[this.Size()-1]},set:function(t){this._nodes[this.Size()-1]=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Last",{get:function(){return this._nodes[0]},set:function(t){this._nodes[0]=t},enumerable:!1,configurable:!0}),t.prototype.Peek=function(t){return null==t&&(t=1),this._nodes.slice(this.Size()-t)},t.prototype.Size=function(){return this._nodes.length},t.prototype.Clear=function(){this._nodes=[]},t.prototype.toMml=function(t,e){return void 0===t&&(t=!0),1!==this._nodes.length||e?this.create("node",t?"inferredMrow":"mrow",this._nodes,{}):this.First},t.prototype.create=function(t){for(var e,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return(e=this.factory.configuration.nodeFactory).create.apply(e,o([t],r))},t}();e.MmlStack=u;var c=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,r)||this;return a.factory=e,a.global={},a._properties={},a.isOpen&&(a._env={}),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"base"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"env",{get:function(){return this._env},set:function(t){this._env=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.getProperty=function(t){return this._properties[t]},e.prototype.setProperty=function(t,e){return this._properties[t]=e,this},Object.defineProperty(e.prototype,"isOpen",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.isKind=function(t){return t===this.kind},e.prototype.checkItem=function(t){if(t.isKind("over")&&this.isOpen&&(t.setProperty("num",this.toMml(!1)),this.Clear()),t.isKind("cell")&&this.isOpen){if(t.getProperty("linebreak"))return e.fail;throw new l.default("Misplaced","Misplaced %1",t.getName())}if(t.isClose&&this.getErrors(t.kind)){var r=i(this.getErrors(t.kind),2),n=r[0],a=r[1];throw new l.default(n,a,t.getName())}return t.isFinal?(this.Push(t.First),e.fail):e.success},e.prototype.clearEnv=function(){var t,e;try{for(var r=s(Object.keys(this.env)),n=r.next();!n.done;n=r.next()){var a=n.value;delete this.env[a]}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.setProperties=function(t){return Object.assign(this._properties,t),this},e.prototype.getName=function(){return this.getProperty("name")},e.prototype.toString=function(){return this.kind+"["+this.nodes.join("; ")+"]"},e.prototype.getErrors=function(t){return(this.constructor.errors||{})[t]||e.errors[t]},e.fail=[null,!1],e.success=[null,!0],e.errors={end:["MissingBeginExtraEnd","Missing \\begin{%1} or extra \\end{%1}"],close:["ExtraCloseMissingOpen","Extra close brace or missing open brace"],right:["MissingLeftExtraRight","Missing \\left or extra \\right"]},e}(u);e.BaseItem=c},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},o=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.EquationItem=e.EqnArrayItem=e.ArrayItem=e.DotsItem=e.NotItem=e.FnItem=e.MmlItem=e.CellItem=e.PositionItem=e.StyleItem=e.EndItem=e.BeginItem=e.RightItem=e.LeftItem=e.OverItem=e.SubsupItem=e.PrimeItem=e.CloseItem=e.OpenItem=e.StopItem=e.StartItem=void 0;var s=r(12),l=r(11),u=r(5),c=r(0),f=r(3),p=r(1),d=r(13),h=function(t){function e(e,r){var n=t.call(this,e)||this;return n.global=r,n}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"start"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("stop")){var r=this.toMml();return this.global.isInner||(r=this.factory.configuration.tags.finalize(r,this.env)),[[this.factory.create("mml",r)],!0]}return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.StartItem=h;var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"stop"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.StopItem=m;var g=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"open"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("close")){var r=this.toMml(),n=this.create("node","TeXAtom",[r]);return[[this.factory.create("mml",n)],!0]}return t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(d.BaseItem.errors),{stop:["ExtraOpenMissingClose","Extra open brace or missing close brace"]}),e}(d.BaseItem);e.OpenItem=g;var y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"close"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.CloseItem=y;var v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"prime"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){var e=i(this.Peek(2),2),r=e[0],n=e[1];return!p.default.isType(r,"msubsup")||p.default.isType(r,"msup")?[[this.create("node","msup",[r,n]),t],!0]:(p.default.setChild(r,r.sup,n),[[r,t],!0])},e}(d.BaseItem);e.PrimeItem=v;var b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"subsup"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("open")||e.isKind("left"))return d.BaseItem.success;var r=this.First,n=this.getProperty("position");if(e.isKind("mml")){if(this.getProperty("primes"))if(2!==n)p.default.setChild(r,2,this.getProperty("primes"));else{p.default.setProperty(this.getProperty("primes"),"variantForm",!0);var a=this.create("node","mrow",[this.getProperty("primes"),e.First]);e.First=a}return p.default.setChild(r,n,e.First),null!=this.getProperty("movesupsub")&&p.default.setProperty(r,"movesupsub",this.getProperty("movesupsub")),[[this.factory.create("mml",r)],!0]}if(t.prototype.checkItem.call(this,e)[1]){var i=this.getErrors(["","sub","sup"][n]);throw new(c.default.bind.apply(c.default,o([void 0,i[0],i[1]],i.splice(2))))}return null},e.errors=Object.assign(Object.create(d.BaseItem.errors),{stop:["MissingScript","Missing superscript or subscript argument"],sup:["MissingOpenForSup","Missing open brace for superscript"],sub:["MissingOpenForSub","Missing open brace for subscript"]}),e}(d.BaseItem);e.SubsupItem=b;var A=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("name","\\over"),r}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"over"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("over"))throw new c.default("AmbiguousUseOf","Ambiguous use of %1",e.getName());if(e.isClose){var r=this.create("node","mfrac",[this.getProperty("num"),this.toMml(!1)]);return null!=this.getProperty("thickness")&&p.default.setAttribute(r,"linethickness",this.getProperty("thickness")),(this.getProperty("open")||this.getProperty("close"))&&(p.default.setProperty(r,"withDelims",!0),r=f.default.fixedFence(this.factory.configuration,this.getProperty("open"),r,this.getProperty("close"))),[[this.factory.create("mml",r),e],!0]}return t.prototype.checkItem.call(this,e)},e.prototype.toString=function(){return"over["+this.getProperty("num")+" / "+this.nodes.join("; ")+"]"},e}(d.BaseItem);e.OverItem=A;var M=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("delim","("),r}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"left"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){return e.isKind("right")?[[this.factory.create("mml",f.default.fenced(this.factory.configuration,this.getProperty("delim"),this.toMml(),e.getProperty("delim")))],!0]:t.prototype.checkItem.call(this,e)},e.errors=Object.assign(Object.create(d.BaseItem.errors),{stop:["ExtraLeftMissingRight","Extra \\left or missing \\right"]}),e}(d.BaseItem);e.LeftItem=M;var x=function(t){function e(e){var r=t.call(this,e)||this;return r.setProperty("delim",")"),r}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"right"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.RightItem=x;var T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"begin"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){if(e.getName()!==this.getName())throw new c.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),e.getName());return this.getProperty("end")?d.BaseItem.fail:[[this.factory.create("mml",this.toMml())],!0]}if(e.isKind("stop"))throw new c.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.BeginItem=T;var w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"end"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.EndItem=w;var S=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"style"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(!e.isClose)return t.prototype.checkItem.call(this,e);var r=this.create("node","mstyle",this.nodes,this.getProperty("styles"));return[[this.factory.create("mml",r),e],!0]},e}(d.BaseItem);e.StyleItem=S;var P=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"position"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isClose)throw new c.default("MissingBoxFor","Missing box for %1",this.getName());if(e.isFinal){var r=e.toMml();switch(this.getProperty("move")){case"vertical":return r=this.create("node","mpadded",[r],{height:this.getProperty("dh"),depth:this.getProperty("dd"),voffset:this.getProperty("dh")}),[[this.factory.create("mml",r)],!0];case"horizontal":return[[this.factory.create("mml",this.getProperty("left")),e,this.factory.create("mml",this.getProperty("right"))],!0]}}return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.PositionItem=P;var C=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"cell"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.CellItem=C;var O=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"kind",{get:function(){return"mml"},enumerable:!1,configurable:!0}),e}(d.BaseItem);e.MmlItem=O;var E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"fn"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){var r=this.First;if(r){if(e.isOpen)return d.BaseItem.success;if(!e.isKind("fn")){var n=e.First;if(!e.isKind("mml")||!n)return[[r,e],!0];if(p.default.isType(n,"mstyle")&&n.childNodes.length&&p.default.isType(n.childNodes[0].childNodes[0],"mspace")||p.default.isType(n,"mspace"))return[[r,e],!0];p.default.isEmbellished(n)&&(n=p.default.getCoreMO(n));var a=p.default.getForm(n);if(null!=a&&[0,0,1,1,0,1,1,0,0,0][a[2]])return[[r,e],!0]}var i=this.create("token","mo",{texClass:u.TEXCLASS.NONE},l.entities.ApplyFunction);return[[r,i,e],!0]}return t.prototype.checkItem.apply(this,arguments)},e}(d.BaseItem);e.FnItem=E;var k=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.remap=s.MapHandler.getMap("not_remap"),e}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"not"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){var e,r,n;if(t.isKind("open")||t.isKind("left"))return d.BaseItem.success;if(t.isKind("mml")&&(p.default.isType(t.First,"mo")||p.default.isType(t.First,"mi")||p.default.isType(t.First,"mtext"))&&(e=t.First,1===(r=p.default.getText(e)).length&&!p.default.getProperty(e,"movesupsub")&&1===p.default.getChildren(e).length))return this.remap.contains(r)?(n=this.create("text",this.remap.lookup(r).char),p.default.setChild(e,0,n)):(n=this.create("text","\u0338"),p.default.appendChildren(e,[n])),[[t],!0];n=this.create("text","\u29f8");var a=this.create("node","mtext",[],{},n),i=this.create("node","mpadded",[a],{width:0});return[[e=this.create("node","TeXAtom",[i],{texClass:u.TEXCLASS.REL}),t],!0]},e}(d.BaseItem);e.NotItem=k;var _=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"dots"},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(t){if(t.isKind("open")||t.isKind("left"))return d.BaseItem.success;var e=this.getProperty("ldots"),r=t.First;if(t.isKind("mml")&&p.default.isEmbellished(r)){var n=p.default.getTexClass(p.default.getCoreMO(r));n!==u.TEXCLASS.BIN&&n!==u.TEXCLASS.REL||(e=this.getProperty("cdots"))}return[[e,t],!0]},e}(d.BaseItem);e.DotsItem=_;var I=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.table=[],e.row=[],e.frame=[],e.hfill=[],e.arraydef={},e.dashed=!1,e}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"array"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isClose&&!e.isKind("over")){if(e.getProperty("isEntry"))return this.EndEntry(),this.clearEnv(),d.BaseItem.fail;if(e.getProperty("isCR"))return this.EndEntry(),this.EndRow(),this.clearEnv(),d.BaseItem.fail;this.EndTable(),this.clearEnv();var r=this.arraydef.scriptlevel;delete this.arraydef.scriptlevel;var n=this.create("node","mtable",this.table,this.arraydef);4===this.frame.length?p.default.setAttribute(n,"frame",this.dashed?"dashed":"solid"):this.frame.length&&(this.arraydef.rowlines&&(this.arraydef.rowlines=this.arraydef.rowlines.replace(/none( none)+$/,"none")),n=this.create("node","menclose",[n],{notation:this.frame.join(" "),isFrame:!0}),"none"===(this.arraydef.columnlines||"none")&&"none"===(this.arraydef.rowlines||"none")||p.default.setAttribute(n,"padding",0)),r&&(n=this.create("node","mstyle",[n],{scriptlevel:r})),(this.getProperty("open")||this.getProperty("close"))&&(n=f.default.fenced(this.factory.configuration,this.getProperty("open"),n,this.getProperty("close")));var a=this.factory.create("mml",n);if(this.getProperty("requireClose")){if(e.isKind("close"))return[[a],!0];throw new c.default("MissingCloseBrace","Missing close brace")}return[[a,e],!0]}return t.prototype.checkItem.call(this,e)},e.prototype.EndEntry=function(){var t=this.create("node","mtd",this.nodes);this.hfill.length&&(0===this.hfill[0]&&p.default.setAttribute(t,"columnalign","right"),this.hfill[this.hfill.length-1]===this.Size()&&p.default.setAttribute(t,"columnalign",p.default.getAttribute(t,"columnalign")?"center":"left")),this.row.push(t),this.Clear(),this.hfill=[]},e.prototype.EndRow=function(){var t;this.getProperty("isNumbered")&&3===this.row.length?(this.row.unshift(this.row.pop()),t=this.create("node","mlabeledtr",this.row)):t=this.create("node","mtr",this.row),this.table.push(t),this.row=[]},e.prototype.EndTable=function(){(this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.checkLines()},e.prototype.checkLines=function(){if(this.arraydef.rowlines){var t=this.arraydef.rowlines.split(/ /);t.length===this.table.length?(this.frame.push("bottom"),t.pop(),this.arraydef.rowlines=t.join(" ")):t.length<this.table.length-1&&(this.arraydef.rowlines+=" none")}if(this.getProperty("rowspacing")){for(var e=this.arraydef.rowspacing.split(/ /);e.length<this.table.length;)e.push(this.getProperty("rowspacing")+"em");this.arraydef.rowspacing=e.join(" ")}},e}(d.BaseItem);e.ArrayItem=I;var N=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,e)||this;return a.factory.configuration.tags.start(r[0],r[2],r[1]),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"eqnarray"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){this.row.length&&f.default.fixInitialMO(this.factory.configuration,this.nodes);var t=this.create("node","mtd",this.nodes);this.row.push(t),this.Clear()},e.prototype.EndRow=function(){var t="mtr",e=this.factory.configuration.tags.getTag();e&&(this.row=[e].concat(this.row),t="mlabeledtr"),this.factory.configuration.tags.clearTag();var r=this.create("node",t,this.row);this.table.push(r),this.row=[]},e.prototype.EndTable=function(){t.prototype.EndTable.call(this),this.factory.configuration.tags.end()},e}(I);e.EqnArrayItem=N;var L=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,e)||this;return a.factory.configuration.tags.start("equation",!0,r[0]),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"equation"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){var r=this.toMml(),n=this.factory.configuration.tags.getTag();return this.factory.configuration.tags.end(),[[n?this.factory.configuration.tags.enTag(r,n):r,e],!0]}if(e.isKind("stop"))throw new c.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(d.BaseItem);e.EquationItem=L},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o};Object.defineProperty(e,"__esModule",{value:!0});var a=r(14),i=r(1),o=r(0),s=r(8),l=r(7),u=r(3),c=r(5),f=r(9),p=r(11);r(33),r(49),r(50);var d={},h={fontfamily:1,fontsize:1,fontweight:1,fontstyle:1,color:1,background:1,id:1,class:1,href:1,style:1};function m(t,e){var r=t.stack.env,n=r.inRoot;r.inRoot=!0;var a=new s.default(e,r,t.configuration),i=a.mml(),o=a.stack.global;if(o.leftRoot||o.upRoot){var l={};o.leftRoot&&(l.width=o.leftRoot),o.upRoot&&(l.voffset=o.upRoot,l.height=o.upRoot),i=t.create("node","mpadded",[i],l)}return r.inRoot=n,i}d.Open=function(t,e){t.Push(t.itemFactory.create("open"))},d.Close=function(t,e){t.Push(t.itemFactory.create("close"))},d.Tilde=function(t,e){t.Push(t.create("token","mtext",{},p.entities.nbsp))},d.Space=function(t,e){},d.Superscript=function(t,e){var r,a,s;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var l=t.stack.Top();l.isKind("prime")?(s=(r=n(l.Peek(2),2))[0],a=r[1],t.stack.Pop()):(s=t.stack.Prev())||(s=t.create("token","mi",{},""));var u=i.default.getProperty(s,"movesupsub"),c=i.default.isType(s,"msubsup")?s.sup:s.over;if(i.default.isType(s,"msubsup")&&!i.default.isType(s,"msup")&&i.default.getChildAt(s,s.sup)||i.default.isType(s,"munderover")&&!i.default.isType(s,"mover")&&i.default.getChildAt(s,s.over)&&!i.default.getProperty(s,"subsupOK"))throw new o.default("DoubleExponent","Double exponent: use braces to clarify");i.default.isType(s,"msubsup")&&!i.default.isType(s,"msup")||(u?((!i.default.isType(s,"munderover")||i.default.isType(s,"mover")||i.default.getChildAt(s,s.over))&&(s=t.create("node","munderover",[s],{movesupsub:!0})),c=s.over):c=(s=t.create("node","msubsup",[s])).sup),t.Push(t.itemFactory.create("subsup",s).setProperties({position:c,primes:a,movesupsub:u}))},d.Subscript=function(t,e){var r,a,s;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var l=t.stack.Top();l.isKind("prime")?(s=(r=n(l.Peek(2),2))[0],a=r[1],t.stack.Pop()):(s=t.stack.Prev())||(s=t.create("token","mi",{},""));var u=i.default.getProperty(s,"movesupsub"),c=i.default.isType(s,"msubsup")?s.sub:s.under;if(i.default.isType(s,"msubsup")&&!i.default.isType(s,"msup")&&i.default.getChildAt(s,s.sub)||i.default.isType(s,"munderover")&&!i.default.isType(s,"mover")&&i.default.getChildAt(s,s.under)&&!i.default.getProperty(s,"subsupOK"))throw new o.default("DoubleSubscripts","Double subscripts: use braces to clarify");i.default.isType(s,"msubsup")&&!i.default.isType(s,"msup")||(u?((!i.default.isType(s,"munderover")||i.default.isType(s,"mover")||i.default.getChildAt(s,s.under))&&(s=t.create("node","munderover",[s],{movesupsub:!0})),c=s.under):c=(s=t.create("node","msubsup",[s])).sub),t.Push(t.itemFactory.create("subsup",s).setProperties({position:c,primes:a,movesupsub:u}))},d.Prime=function(t,e){var r=t.stack.Prev();if(r||(r=t.create("node","mi")),i.default.isType(r,"msubsup")&&!i.default.isType(r,"msup")&&i.default.getChildAt(r,r.sup))throw new o.default("DoubleExponentPrime","Prime causes double exponent: use braces to clarify");var n="";t.i--;do{n+=p.entities.prime,t.i++,e=t.GetNext()}while("'"===e||e===p.entities.rquote);n=["","\u2032","\u2033","\u2034","\u2057"][n.length]||n;var a=t.create("token","mo",{},n);t.Push(t.itemFactory.create("prime",r,a))},d.Comment=function(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++},d.Hash=function(t,e){throw new o.default("CantUseHash1","You can't use 'macro parameter character #' in math mode")},d.SetFont=function(t,e,r){t.stack.env.font=r},d.SetStyle=function(t,e,r,n,a){t.stack.env.style=r,t.stack.env.level=a,t.Push(t.itemFactory.create("style").setProperty("styles",{displaystyle:n,scriptlevel:a}))},d.SetSize=function(t,e,r){t.stack.env.size=r,t.Push(t.itemFactory.create("style").setProperty("styles",{mathsize:r+"em"}))},d.Spacer=function(t,e,r){var n=t.create("node","mspace",[],{width:r}),a=t.create("node","mstyle",[n],{scriptlevel:0});t.Push(a)},d.LeftRight=function(t,e){var r=e.substr(1);t.Push(t.itemFactory.create(r).setProperty("delim",t.GetDelimiter(e)))},d.Middle=function(t,e){var r=t.GetDelimiter(e),n=t.create("node","TeXAtom",[],{texClass:c.TEXCLASS.CLOSE});if(t.Push(n),!t.stack.Top().isKind("left"))throw new o.default("MisplacedMiddle","%1 must be within \\left and \\right",t.currentCS);n=t.create("token","mo",{stretchy:!0},r),t.Push(n),n=t.create("node","TeXAtom",[],{texClass:c.TEXCLASS.OPEN}),t.Push(n)},d.NamedFn=function(t,e,r){r||(r=e.substr(1));var n=t.create("token","mi",{texClass:c.TEXCLASS.OP},r);t.Push(t.itemFactory.create("fn",n))},d.NamedOp=function(t,e,r){r||(r=e.substr(1)),r=r.replace(/&thinsp;/,"\u2006");var n=t.create("token","mo",{movablelimits:!0,movesupsub:!0,form:l.TexConstant.Form.PREFIX,texClass:c.TEXCLASS.OP},r);t.Push(n)},d.Limits=function(t,e,r){var n=t.stack.Prev(!0);if(!n||i.default.getTexClass(i.default.getCoreMO(n))!==c.TEXCLASS.OP&&null==i.default.getProperty(n,"movesupsub"))throw new o.default("MisplacedLimits","%1 is allowed only on operators",t.currentCS);var a,s=t.stack.Top();i.default.isType(n,"munderover")&&!r?(a=t.create("node","msubsup"),i.default.copyChildren(n,a),n=s.Last=a):i.default.isType(n,"msubsup")&&r&&(a=t.create("node","munderover"),i.default.copyChildren(n,a),n=s.Last=a),i.default.setProperty(n,"movesupsub",!!r),i.default.setProperties(i.default.getCoreMO(n),{movablelimits:!1}),(i.default.getAttribute(n,"movablelimits")||i.default.getProperty(n,"movablelimits"))&&i.default.setProperties(n,{movablelimits:!1})},d.Over=function(t,e,r,n){var a=t.itemFactory.create("over").setProperty("name",t.currentCS);r||n?(a.setProperty("open",r),a.setProperty("close",n)):e.match(/withdelims$/)&&(a.setProperty("open",t.GetDelimiter(e)),a.setProperty("close",t.GetDelimiter(e))),e.match(/^\\above/)?a.setProperty("thickness",t.GetDimen(e)):(e.match(/^\\atop/)||r||n)&&a.setProperty("thickness",0),t.Push(a)},d.Frac=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e),a=t.create("node","mfrac",[r,n]);t.Push(a)},d.Sqrt=function(t,e){var r=t.GetBrackets(e),n=t.GetArgument(e);"\\frac"===n&&(n+="{"+t.GetArgument(n)+"}{"+t.GetArgument(n)+"}");var a=new s.default(n,t.stack.env,t.configuration).mml();a=r?t.create("node","mroot",[a,m(t,r)]):t.create("node","msqrt",[a]),t.Push(a)},d.Root=function(t,e){var r=t.GetUpTo(e,"\\of"),n=t.ParseArg(e),a=t.create("node","mroot",[n,m(t,r)]);t.Push(a)},d.MoveRoot=function(t,e,r){if(!t.stack.env.inRoot)throw new o.default("MisplacedMoveRoot","%1 can appear only within a root",t.currentCS);if(t.stack.global[r])throw new o.default("MultipleMoveRoot","Multiple use of %1",t.currentCS);var n=t.GetArgument(e);if(!n.match(/-?[0-9]+/))throw new o.default("IntegerArg","The argument to %1 must be an integer",t.currentCS);"-"!==(n=parseInt(n,10)/15+"em").substr(0,1)&&(n="+"+n),t.stack.global[r]=n},d.Accent=function(t,e,r,n){var a=t.ParseArg(e),o=u.default.getFontDef(t);o.accent=!0;var s=i.default.createEntity(r),l=t.create("token","mo",o,s);i.default.setAttribute(l,"stretchy",!!n);var c=i.default.isEmbellished(a)?i.default.getCoreMO(a):a;i.default.isType(c,"mo")&&i.default.setProperties(c,{movablelimits:!1});var f=t.create("node","munderover");i.default.setChild(f,0,a),i.default.setChild(f,1,null),i.default.setChild(f,2,l);var p=t.create("node","TeXAtom",[f]);t.Push(p)},d.UnderOver=function(t,e,r,n,a){var o,s=t.ParseArg(e),l=i.default.getForm(s);(l&&l[3]&&l[3].movablelimits||i.default.getProperty(s,"movablelimits"))&&i.default.setProperties(s,{movablelimits:!1}),i.default.isType(s,"munderover")&&i.default.isEmbellished(s)&&(i.default.setProperties(i.default.getCoreMO(s),{lspace:0,rspace:0}),o=t.create("node","mo",[],{rspace:0}),s=t.create("node","mrow",[o,s]));var u=t.create("node","munderover",[s]),f=i.default.createEntity(r);o=t.create("token","mo",{stretchy:!0,accent:!a},f),i.default.setChild(u,"o"===e.charAt(1)?u.over:u.under,o);var p=u;n&&(p=t.create("node","TeXAtom",[u],{texClass:c.TEXCLASS.OP,movesupsub:!0})),i.default.setProperty(p,"subsupOK",!0),t.Push(p)},d.Overset=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);(i.default.getAttribute(n,"movablelimits")||i.default.getProperty(n,"movablelimits"))&&i.default.setProperties(n,{movablelimits:!1});var a=t.create("node","mover",[n,r]);t.Push(a)},d.Underset=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e);(i.default.isType(n,"mo")||i.default.getProperty(n,"movablelimits"))&&i.default.setProperties(n,{movablelimits:!1});var a=t.create("node","munder",[n,r]);t.Push(a)},d.TeXAtom=function(t,e,r){var n,a,i,o={texClass:r};if(r===c.TEXCLASS.OP){o.movesupsub=o.movablelimits=!0;var u=t.GetArgument(e),f=u.match(/^\s*\\rm\s+([a-zA-Z0-9 ]+)$/);f?(o.mathvariant=l.TexConstant.Variant.NORMAL,a=t.create("token","mi",o,f[1])):(i=new s.default(u,t.stack.env,t.configuration).mml(),a=t.create("node","TeXAtom",[i],o)),n=t.itemFactory.create("fn",a)}else i=t.ParseArg(e),n=t.create("node","TeXAtom",[i],o);t.Push(n)},d.MmlToken=function(t,e){var r,n=t.GetArgument(e),a=t.GetBrackets(e,"").replace(/^\s+/,""),s=t.GetArgument(e),l={};try{r=t.create("node",n)}catch(t){r=null}if(!r||!r.isToken)throw new o.default("NotMathMLToken","%1 is not a token element",n);for(;""!==a;){var c=a.match(/^([a-z]+)\s*=\s*('[^']*'|"[^"]*"|[^ ,]*)\s*,?\s*/i);if(!c)throw new o.default("InvalidMathMLAttr","Invalid MathML attribute: %1",a);if(!r.attributes.hasDefault(c[1])&&!h[c[1]])throw new o.default("UnknownAttrForElement","%1 is not a recognized attribute for %2",c[1],n);var f=u.default.MmlFilterAttribute(t,c[1],c[2].replace(/^(['"])(.*)\1$/,"$2"));f&&("true"===f.toLowerCase()?f=!0:"false"===f.toLowerCase()&&(f=!1),l[c[1]]=f),a=a.substr(c[0].length)}var p=t.create("text",s);r.appendChild(p),i.default.setProperties(r,l),t.Push(r)},d.Strut=function(t,e){var r=t.create("node","mrow"),n=t.create("node","mpadded",[r],{height:"8.6pt",depth:"3pt",width:0});t.Push(n)},d.Phantom=function(t,e,r,n){var a=t.create("node","mphantom",[t.ParseArg(e)]);(r||n)&&(a=t.create("node","mpadded",[a]),n&&(i.default.setAttribute(a,"height",0),i.default.setAttribute(a,"depth",0)),r&&i.default.setAttribute(a,"width",0));var o=t.create("node","TeXAtom",[a]);t.Push(o)},d.Smash=function(t,e){var r=u.default.trimSpaces(t.GetBrackets(e,"")),n=t.create("node","mpadded",[t.ParseArg(e)]);switch(r){case"b":i.default.setAttribute(n,"depth",0);break;case"t":i.default.setAttribute(n,"height",0);break;default:i.default.setAttribute(n,"height",0),i.default.setAttribute(n,"depth",0)}var a=t.create("node","TeXAtom",[n]);t.Push(a)},d.Lap=function(t,e){var r=t.create("node","mpadded",[t.ParseArg(e)],{width:0});"\\llap"===e&&i.default.setAttribute(r,"lspace","-1width");var n=t.create("node","TeXAtom",[r]);t.Push(n)},d.RaiseLower=function(t,e){var r=t.GetDimen(e),n=t.itemFactory.create("position").setProperties({name:t.currentCS,move:"vertical"});"-"===r.charAt(0)&&(r=r.slice(1),e="raise"===e.substr(1)?"\\lower":"\\raise"),"\\lower"===e?(n.setProperty("dh","-"+r),n.setProperty("dd","+"+r)):(n.setProperty("dh","+"+r),n.setProperty("dd","-"+r)),t.Push(n)},d.MoveLeftRight=function(t,e){var r=t.GetDimen(e),n="-"===r.charAt(0)?r.slice(1):"-"+r;if("\\moveleft"===e){var a=r;r=n,n=a}t.Push(t.itemFactory.create("position").setProperties({name:t.currentCS,move:"horizontal",left:t.create("node","mspace",[],{width:r}),right:t.create("node","mspace",[],{width:n})}))},d.Hskip=function(t,e){var r=t.create("node","mspace",[],{width:t.GetDimen(e)});t.Push(r)},d.Rule=function(t,e,r){var n={width:t.GetDimen(e),height:t.GetDimen(e),depth:t.GetDimen(e)};"blank"!==r&&(n.mathbackground=t.stack.env.color||"black");var a=t.create("node","mspace",[],n);t.Push(a)},d.rule=function(t,e){var r=t.GetBrackets(e),n=t.GetDimen(e),a=t.GetDimen(e),o=t.create("node","mspace",[],{width:n,height:a,mathbackground:t.stack.env.color||"black"});r&&(o=t.create("node","mpadded",[o],{voffset:r}),r.match(/^\-/)?(i.default.setAttribute(o,"height",r),i.default.setAttribute(o,"depth","+"+r.substr(1))):i.default.setAttribute(o,"height","+"+r)),t.Push(o)},d.MakeBig=function(t,e,r,n){var a=String(n*=1.411764705882353).replace(/(\.\d\d\d).+/,"$1")+"em",i=t.GetDelimiter(e,!0),o=t.create("token","mo",{minsize:a,maxsize:a,fence:!0,stretchy:!0,symmetric:!0},i),s=t.create("node","TeXAtom",[o],{texClass:r});t.Push(s)},d.BuildRel=function(t,e){var r=t.ParseUpTo(e,"\\over"),n=t.ParseArg(e),a=t.create("node","munderover");i.default.setChild(a,0,n),i.default.setChild(a,1,null),i.default.setChild(a,2,r);var o=t.create("node","TeXAtom",[a],{texClass:c.TEXCLASS.REL});t.Push(o)},d.HBox=function(t,e,r,n){t.PushAll(u.default.internalMath(t,t.GetArgument(e),r,n))},d.FBox=function(t,e){var r=u.default.internalMath(t,t.GetArgument(e)),n=t.create("node","menclose",r,{notation:"box"});t.Push(n)},d.Not=function(t,e){t.Push(t.itemFactory.create("not"))},d.Dots=function(t,e){var r=i.default.createEntity("2026"),n=i.default.createEntity("22EF"),a=t.create("token","mo",{stretchy:!1},r),o=t.create("token","mo",{stretchy:!1},n);t.Push(t.itemFactory.create("dots").setProperties({ldots:a,cdots:o}))},d.Matrix=function(t,e,r,n,a,i,s,l,u,c){var f=t.GetNext();if(""===f)throw new o.default("MissingArgFor","Missing argument for %1",t.currentCS);"{"===f?t.i++:(t.string=f+"}"+t.string.slice(t.i+1),t.i=0);var p=t.itemFactory.create("array").setProperty("requireClose",!0);p.arraydef={rowspacing:s||"4pt",columnspacing:i||"1em"},u&&p.setProperty("isCases",!0),c&&(p.setProperty("isNumbered",!0),p.arraydef.side=c),(r||n)&&(p.setProperty("open",r),p.setProperty("close",n)),"D"===l&&(p.arraydef.displaystyle=!0),null!=a&&(p.arraydef.columnalign=a),t.Push(p)},d.Entry=function(t,e){if(t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e})),t.stack.Top().getProperty("isCases")){for(var r=t.string,n=0,a=-1,i=t.i,s=r.length;i<s;){var l=r.charAt(i);if("{"===l)n++,i++;else if("}"===l)0===n?s=0:(0===--n&&a<0&&(a=i-t.i),i++);else{if("&"===l&&0===n)throw new o.default("ExtraAlignTab","Extra alignment tab in \\cases text");"\\"===l?r.substr(i).match(/^((\\cr)[^a-zA-Z]|\\\\)/)?s=0:i+=2:i++}}var c=r.substr(t.i,i-t.i);if(!c.match(/^\s*\\text[^a-zA-Z]/)||a!==c.replace(/\s+$/,"").length-1){var f=u.default.internalMath(t,c,0);t.PushAll(f),t.i=i}}},d.Cr=function(t,e){t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e}))},d.CrLaTeX=function(t,e,r){var i;if(void 0===r&&(r=!1),!r&&"["===t.string.charAt(t.i)){var s=t.GetBrackets(e,""),c=n(u.default.matchDimen(s),2),f=c[0],p=c[1];if(s&&!f)throw new o.default("BracketMustBeDimension","Bracket argument to %1 must be a dimension",t.currentCS);i=f+p}t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e,linebreak:!0}));var d,h=t.stack.Top();if(h instanceof a.ArrayItem){if(i&&h.arraydef.rowspacing){var m=h.arraydef.rowspacing.split(/ /);if(!h.getProperty("rowspacing")){var g=u.default.dimen2em(m[0]);h.setProperty("rowspacing",g)}for(var y=h.getProperty("rowspacing");m.length<h.table.length;)m.push(u.default.Em(y));m[h.table.length-1]=u.default.Em(Math.max(0,y+u.default.dimen2em(i))),h.arraydef.rowspacing=m.join(" ")}}else i&&(d=t.create("node","mspace",[],{depth:i}),t.Push(d)),d=t.create("node","mspace",[],{linebreak:l.TexConstant.LineBreak.NEWLINE}),t.Push(d)},d.HLine=function(t,e,r){null==r&&(r="solid");var n=t.stack.Top();if(!(n instanceof a.ArrayItem)||n.Size())throw new o.default("Misplaced","Misplaced %1",t.currentCS);if(n.table.length){for(var i=n.arraydef.rowlines?n.arraydef.rowlines.split(/ /):[];i.length<n.table.length;)i.push("none");i[n.table.length-1]=r,n.arraydef.rowlines=i.join(" ")}else n.frame.push("top")},d.HFill=function(t,e){var r=t.stack.Top();if(!(r instanceof a.ArrayItem))throw new o.default("UnsupportedHFill","Unsupported use of %1",t.currentCS);r.hfill.push(r.Size())},d.BeginEnd=function(t,e){var r=t.GetArgument(e);if(r.match(/\\/i))throw new o.default("InvalidEnv","Invalid environment name '%1'",r);var n=t.configuration.handlers.get("environment").lookup(r);if(n&&"\\end"===e){if(!n.args[0]){var a=t.itemFactory.create("end").setProperty("name",r);return void t.Push(a)}t.stack.env.closing=r}if(++t.macroCount>t.configuration.options.maxMacros)throw new o.default("MaxMacroSub2","MathJax maximum substitution count exceeded; is there a recursive latex environment?");t.parse("environment",[t,r])},d.Array=function(t,e,r,n,a,i,o,s,l){a||(a=t.GetArgument("\\begin{"+e.getName()+"}"));var u=("c"+a).replace(/[^clr|:]/g,"").replace(/[^|:]([|:])+/g,"$1");a=(a=a.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var c=t.itemFactory.create("array");return c.arraydef={columnalign:a,columnspacing:i||"1em",rowspacing:o||"4pt"},u.match(/[|:]/)&&(u.charAt(0).match(/[|:]/)&&(c.frame.push("left"),c.dashed=":"===u.charAt(0)),u.charAt(u.length-1).match(/[|:]/)&&c.frame.push("right"),u=u.substr(1,u.length-2),c.arraydef.columnlines=u.split("").join(" ").replace(/[^|: ]/g,"none").replace(/\|/g,"solid").replace(/:/g,"dashed")),r&&c.setProperty("open",t.convertDelimiter(r)),n&&c.setProperty("close",t.convertDelimiter(n)),"D"===s?c.arraydef.displaystyle=!0:s&&(c.arraydef.displaystyle=!1),"S"===s&&(c.arraydef.scriptlevel=1),l&&(c.arraydef.useHeight=!1),t.Push(e),c},d.AlignedArray=function(t,e){var r=t.GetBrackets("\\begin{"+e.getName()+"}"),n=d.Array(t,e);return u.default.setArrayAlign(n,r)},d.Equation=function(t,e,r){return t.Push(e),u.default.checkEqnEnv(t),t.itemFactory.create("equation",r).setProperty("name",e.getName())},d.EqnArray=function(t,e,r,n,a,i){t.Push(e),n&&u.default.checkEqnEnv(t),a=(a=a.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var o=t.itemFactory.create("eqnarray",e.getName(),r,n,t.stack.global);return o.arraydef={displaystyle:!0,columnalign:a,columnspacing:i||"1em",rowspacing:"3pt",side:t.options.tagSide,minlabelspacing:t.options.tagIndent},o},d.HandleNoTag=function(t,e){t.tags.notag()},d.HandleLabel=function(t,e){var r=t.GetArgument(e);if(""!==r&&!t.tags.refUpdate){if(t.tags.label)throw new o.default("MultipleCommand","Multiple %1",t.currentCS);if(t.tags.label=r,(t.tags.allLabels[r]||t.tags.labels[r])&&!t.options.ignoreDuplicateLabels)throw new o.default("MultipleLabel","Label '%1' multiply defined",r);t.tags.labels[r]=new f.Label}},d.HandleRef=function(t,e,r){var n=t.GetArgument(e),a=t.tags.allLabels[n]||t.tags.labels[n];a||(t.tags.refUpdate||(t.tags.redo=!0),a=new f.Label);var i=a.tag;r&&(i=t.tags.formatTag(i));var o=t.create("node","mrow",u.default.internalMath(t,i),{href:t.tags.formatUrl(a.id,t.options.baseURL),class:"MathJax_ref"});t.Push(o)},d.Macro=function(t,e,r,n,a){if(n){var i=[];if(null!=a){var s=t.GetBrackets(e);i.push(null==s?a:s)}for(var l=i.length;l<n;l++)i.push(t.GetArgument(e));r=u.default.substituteArgs(t,i,r)}if(t.string=u.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,++t.macroCount>t.configuration.options.maxMacros)throw new o.default("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is there a recursive macro call?")},d.MathChoice=function(t,e){var r=t.ParseArg(e),n=t.ParseArg(e),a=t.ParseArg(e),i=t.ParseArg(e);t.Push(t.create("node","MathChoice",[r,n,a,i]))},e.default=d},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var i,o=r(1),s=r(7),l=r(3);!function(t){t.variable=function(t,e){var r=l.default.getFontDef(t),n=t.create("token","mi",r,e);t.Push(n)},t.digit=function(t,e){var r,n=t.configuration.options.digits,a=t.string.slice(t.i-1).match(n),i=l.default.getFontDef(t);a?(r=t.create("token","mn",i,a[0].replace(/[{}]/g,"")),t.i+=a[0].length-1):r=t.create("token","mo",i,e),t.Push(r)},t.controlSequence=function(t,e){var r=t.GetCS();t.parse("macro",[t,r])},t.mathchar0mi=function(t,e){var r=e.attributes||{mathvariant:s.TexConstant.Variant.ITALIC},n=t.create("token","mi",r,e.char);t.Push(n)},t.mathchar0mo=function(t,e){var r=e.attributes||{};r.stretchy=!1;var n=t.create("token","mo",r,e.char);o.default.setProperty(n,"fixStretchy",!0),t.configuration.addNode("fixStretchy",n),t.Push(n)},t.mathchar7=function(t,e){var r=e.attributes||{mathvariant:s.TexConstant.Variant.NORMAL};t.stack.env.font&&(r.mathvariant=t.stack.env.font);var n=t.create("token","mi",r,e.char);t.Push(n)},t.delimiter=function(t,e){var r=e.attributes||{};r=Object.assign({fence:!1,stretchy:!1},r);var n=t.create("token","mo",r,e.char);t.Push(n)},t.environment=function(t,e,r,n){var i=n[0],o=t.itemFactory.create("begin").setProperties({name:e,end:i});o=r.apply(void 0,a([t,o],n.slice(1))),t.Push(o)}}(i||(i={})),e.default=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.NEW_OPS=e.AmsMethods=void 0;var n=r(3),a=r(1),i=r(7),o=r(8),s=r(0),l=r(10),u=r(15),c=r(5);e.AmsMethods={},e.AmsMethods.AmsEqnArray=function(t,e,r,a,i,o,s){var l=t.GetBrackets("\\begin{"+e.getName()+"}"),c=u.default.EqnArray(t,e,r,a,i,o,s);return n.default.setArrayAlign(c,l)},e.AmsMethods.AlignAt=function(t,r,a,i){var o,l,u=r.getName(),c="",f=[];if(i||(l=t.GetBrackets("\\begin{"+u+"}")),(o=t.GetArgument("\\begin{"+u+"}")).match(/[^0-9]/))throw new s.default("PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+u+"}");for(var p=parseInt(o,10);p>0;)c+="rl",f.push("0em 0em"),p--;var d=f.join(" ");if(i)return e.AmsMethods.EqnArray(t,r,a,i,c,d);var h=e.AmsMethods.EqnArray(t,r,a,i,c,d);return n.default.setArrayAlign(h,l)},e.AmsMethods.Multline=function(t,e,r){t.Push(e),n.default.checkEqnEnv(t);var a=t.itemFactory.create("multline",r,t.stack);return a.arraydef={displaystyle:!0,rowspacing:".5em",columnwidth:"100%",width:t.options.multlineWidth,side:t.options.tagSide,minlabelspacing:t.options.tagIndent},a},e.NEW_OPS="ams-declare-ops",e.AmsMethods.HandleDeclareOp=function(t,r){var a=t.GetStar()?"":"\\nolimits\\SkipLimits",i=n.default.trimSpaces(t.GetArgument(r));"\\"===i.charAt(0)&&(i=i.substr(1));var o=t.GetArgument(r);o.match(/\\text/)||(o=o.replace(/\*/g,"\\text{*}").replace(/-/g,"\\text{-}")),t.configuration.handlers.retrieve(e.NEW_OPS).add(i,new l.Macro(i,e.AmsMethods.Macro,["\\mathop{\\rm "+o+"}"+a]))},e.AmsMethods.HandleOperatorName=function(t,e){var r=t.GetStar()?"":"\\nolimits\\SkipLimits",a=n.default.trimSpaces(t.GetArgument(e));a.match(/\\text/)||(a=a.replace(/\*/g,"\\text{*}").replace(/-/g,"\\text{-}")),t.string="\\mathop{\\rm "+a+"}"+r+" "+t.string.slice(t.i),t.i=0},e.AmsMethods.SkipLimits=function(t,e){var r=t.GetNext(),n=t.i;"\\"===r&&++t.i&&"limits"!==t.GetCS()&&(t.i=n)},e.AmsMethods.MultiIntegral=function(t,e,r){var n=t.GetNext();if("\\"===n){var a=t.i;n=t.GetArgument(e),t.i=a,"\\limits"===n&&(r="\\idotsint"===e?"\\!\\!\\mathop{\\,\\,"+r+"}":"\\!\\!\\!\\mathop{\\,\\,\\,"+r+"}")}t.string=r+" "+t.string.slice(t.i),t.i=0},e.AmsMethods.xArrow=function(t,e,r,i,s){var l={width:"+"+n.default.Em((i+s)/18),lspace:n.default.Em(i/18)},u=t.GetBrackets(e),f=t.ParseArg(e),p=t.create("token","mo",{stretchy:!0,texClass:c.TEXCLASS.REL},String.fromCodePoint(r)),d=t.create("node","munderover",[p]),h=t.create("node","mpadded",[f],l);if(a.default.setAttribute(h,"voffset",".15em"),a.default.setChild(d,d.over,h),u){var m=new o.default(u,t.stack.env,t.configuration).mml();h=t.create("node","mpadded",[m],l),a.default.setAttribute(h,"voffset","-.24em"),a.default.setChild(d,d.under,h)}a.default.setProperty(d,"subsupOK",!0),t.Push(d)},e.AmsMethods.HandleShove=function(t,e,r){var n=t.stack.Top();if("multline"!==n.kind)throw new s.default("CommandOnlyAllowedInEnv","%1 only allowed in %2 environment",t.currentCS,"multline");if(n.Size())throw new s.default("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",t.currentCS);n.setProperty("shove",r)},e.AmsMethods.CFrac=function(t,e){var r=n.default.trimSpaces(t.GetBrackets(e,"")),l=t.GetArgument(e),u=t.GetArgument(e),c={l:i.TexConstant.Align.LEFT,r:i.TexConstant.Align.RIGHT,"":""},f=new o.default("\\strut\\textstyle{"+l+"}",t.stack.env,t.configuration).mml(),p=new o.default("\\strut\\textstyle{"+u+"}",t.stack.env,t.configuration).mml(),d=t.create("node","mfrac",[f,p]);if(null==(r=c[r]))throw new s.default("IllegalAlign","Illegal alignment specified in %1",t.currentCS);r&&a.default.setProperties(d,{numalign:r,denomalign:r}),t.Push(d)},e.AmsMethods.Genfrac=function(t,e,r,i,o,l){null==r&&(r=t.GetDelimiterArg(e)),null==i&&(i=t.GetDelimiterArg(e)),null==o&&(o=t.GetArgument(e)),null==l&&(l=n.default.trimSpaces(t.GetArgument(e)));var u=t.ParseArg(e),c=t.ParseArg(e),f=t.create("node","mfrac",[u,c]);if(""!==o&&a.default.setAttribute(f,"linethickness",o),(r||i)&&(a.default.setProperty(f,"withDelims",!0),f=n.default.fixedFence(t.configuration,r,f,i)),""!==l){var p=parseInt(l,10),d=["D","T","S","SS"][p];if(null==d)throw new s.default("BadMathStyleFor","Bad math style for %1",t.currentCS);f=t.create("node","mstyle",[f]),"D"===d?a.default.setProperties(f,{displaystyle:!0,scriptlevel:0}):a.default.setProperties(f,{displaystyle:!1,scriptlevel:p-1})}t.Push(f)},e.AmsMethods.HandleTag=function(t,e){if(!t.tags.currentTag.taggable&&t.tags.env)throw new s.default("CommandNotAllowedInEnv","%1 not allowed in %2 environment",t.currentCS,t.tags.env);if(t.tags.currentTag.tag)throw new s.default("MultipleCommand","Multiple %1",t.currentCS);var r=t.GetStar(),a=n.default.trimSpaces(t.GetArgument(e));t.tags.tag(a,r)},e.AmsMethods.HandleNoTag=u.default.HandleNoTag,e.AmsMethods.HandleRef=u.default.HandleRef,e.AmsMethods.Macro=u.default.Macro,e.AmsMethods.Accent=u.default.Accent,e.AmsMethods.Tilde=u.default.Tilde,e.AmsMethods.Array=u.default.Array,e.AmsMethods.Spacer=u.default.Spacer,e.AmsMethods.NamedOp=u.default.NamedOp,e.AmsMethods.EqnArray=u.default.EqnArray},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(0),a=r(2),i=r(15),o=r(3),s=r(19),l={NewCommand:function(t,e){var r=o.default.trimSpaces(t.GetArgument(e)),a=t.GetBrackets(e),i=t.GetBrackets(e),u=t.GetArgument(e);if("\\"===r.charAt(0)&&(r=r.substr(1)),!r.match(/^(.|[a-z]+)$/i))throw new n.default("IllegalControlSequenceName","Illegal control sequence name for %1",e);if(a&&!(a=o.default.trimSpaces(a)).match(/^[0-9]+$/))throw new n.default("IllegalParamNumber","Illegal number of parameters specified in %1",e);s.default.addMacro(t,r,l.Macro,[u,a,i])},NewEnvironment:function(t,e){var r=o.default.trimSpaces(t.GetArgument(e)),a=t.GetBrackets(e),i=t.GetBrackets(e),u=t.GetArgument(e),c=t.GetArgument(e);if(a&&!(a=o.default.trimSpaces(a)).match(/^[0-9]+$/))throw new n.default("IllegalParamNumber","Illegal number of parameters specified in %1",e);s.default.addEnvironment(t,r,l.BeginEnv,[!0,u,c,a,i])},MacroDef:function(t,e){var r=s.default.GetCSname(t,e),n=s.default.GetTemplate(t,e,"\\"+r),a=t.GetArgument(e);n instanceof Array?s.default.addMacro(t,r,l.MacroWithTemplate,[a].concat(n)):s.default.addMacro(t,r,l.Macro,[a,n])},Let:function(t,e){var r=s.default.GetCSname(t,e),n=t.GetNext();"="===n&&(t.i++,n=t.GetNext());var i=t.configuration.handlers;if("\\"!==n){t.i++;var o=i.get("delimiter").lookup(n);o?s.default.addDelimiter(t,"\\"+r,o.char,o.attributes):s.default.addMacro(t,r,l.Macro,[n])}else{e=s.default.GetCSname(t,e);var u=i.get("delimiter").lookup("\\"+e);if(u)return void s.default.addDelimiter(t,"\\"+r,u.char,u.attributes);var c=i.get("macro").applicable(e);if(!c)return;if(c instanceof a.MacroMap){var f=c.lookup(e);return void s.default.addMacro(t,r,f.func,f.args,f.symbol)}u=c.lookup(e);var p=s.default.disassembleSymbol(r,u);s.default.addMacro(t,r,(function(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var a=s.default.assembleSymbol(r);return c.parser(t,a)}),p)}},MacroWithTemplate:function(t,e,r,a){for(var i=[],l=4;l<arguments.length;l++)i[l-4]=arguments[l];var u=parseInt(a,10);if(u){var c=[];if(t.GetNext(),i[0]&&!s.default.MatchParam(t,i[0]))throw new n.default("MismatchUseDef","Use of %1 doesn't match its definition",e);for(var f=0;f<u;f++)c.push(s.default.GetParameter(t,e,i[f+1]));r=o.default.substituteArgs(t,c,r)}if(t.string=o.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,++t.macroCount>t.configuration.options.maxMacros)throw new n.default("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is here a recursive macro call?")},BeginEnv:function(t,e,r,n,a,i){if(e.getProperty("end")&&t.stack.env.closing===e.getName()){delete t.stack.env.closing;var s=t.string.slice(t.i);return t.string=n,t.i=0,t.Parse(),t.string=s,t.i=0,t.itemFactory.create("end").setProperty("name",e.getName())}if(a){var l=[];if(null!=i){var u=t.GetBrackets("\\begin{"+e.getName()+"}");l.push(null==u?i:u)}for(var c=l.length;c<a;c++)l.push(t.GetArgument("\\begin{"+e.getName()+"}"));r=o.default.substituteArgs(t,l,r),n=o.default.substituteArgs(t,[],n)}return t.string=o.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,t.itemFactory.create("beginEnv").setProperty("name",e.getName())}};l.Macro=i.default.Macro,e.default=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n,a=r(3),i=r(0),o=r(10);!function(t){function e(t,e){return t.string.substr(t.i,e.length)!==e||e.match(/\\[a-z]+$/i)&&t.string.charAt(t.i+e.length).match(/[a-z]/i)?0:(t.i+=e.length,1)}t.disassembleSymbol=function(t,e){var r=[t,e.char];if(e.attributes)for(var n in e.attributes)r.push(n),r.push(e.attributes[n]);return r},t.assembleSymbol=function(t){for(var e=t[0],r=t[1],n={},a=2;a<t.length;a+=2)n[t[a]]=t[a+1];return new o.Symbol(e,r,n)},t.GetCSname=function(t,e){if("\\"!==t.GetNext())throw new i.default("MissingCS","%1 must be followed by a control sequence",e);return a.default.trimSpaces(t.GetArgument(e)).substr(1)},t.GetTemplate=function(t,e,r){for(var n=t.GetNext(),a=[],o=0,s=t.i;t.i<t.string.length;){if("#"===(n=t.GetNext())){if(s!==t.i&&(a[o]=t.string.substr(s,t.i-s)),!(n=t.string.charAt(++t.i)).match(/^[1-9]$/))throw new i.default("CantUseHash2","Illegal use of # in template for %1",r);if(parseInt(n)!==++o)throw new i.default("SequentialParam","Parameters for %1 must be numbered sequentially",r);s=t.i+1}else if("{"===n)return s!==t.i&&(a[o]=t.string.substr(s,t.i-s)),a.length>0?[o.toString()].concat(a):o;t.i++}throw new i.default("MissingReplacementString","Missing replacement string for definition of %1",e)},t.GetParameter=function(t,r,n){if(null==n)return t.GetArgument(r);for(var a=t.i,o=0,s=0;t.i<t.string.length;){var l=t.string.charAt(t.i);if("{"===l)t.i===a&&(s=1),t.GetArgument(r),o=t.i-a;else{if(e(t,n))return s&&(a++,o-=2),t.string.substr(a,o);if("\\"===l){t.i++,o++,s=0;var u=t.string.substr(t.i).match(/[a-z]+|./i);u&&(t.i+=u[0].length,o=t.i-a)}else t.i++,o++,s=0}}throw new i.default("RunawayArgument","Runaway argument for %1?",r)},t.MatchParam=e,t.addDelimiter=function(e,r,n,a){e.configuration.handlers.retrieve(t.NEW_DELIMITER).add(r,new o.Symbol(r,n,a))},t.addMacro=function(e,r,n,a,i){void 0===i&&(i=""),e.configuration.handlers.retrieve(t.NEW_COMMAND).add(r,new o.Macro(i||r,n,a))},t.addEnvironment=function(e,r,n,a){e.configuration.handlers.retrieve(t.NEW_ENVIRONMENT).add(r,new o.Macro(r,n,a))},t.NEW_DELIMITER="new-Delimiter",t.NEW_COMMAND="new-Command",t.NEW_ENVIRONMENT="new-Environment"}(n||(n={})),e.default=n},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var a in e=arguments[r])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t}).apply(this,arguments)},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.TeX=void 0;var s=r(42),l=r(6),u=r(22),c=r(23),f=r(1),p=r(8),d=r(0),h=r(25),m=r(9),g=r(4);r(28);var y=function(t){function e(r){void 0===r&&(r={});var n=this,a=o(l.separateOptions(r,e.OPTIONS,u.FindTeX.OPTIONS),3),i=a[0],s=a[1],f=a[2];(n=t.call(this,s)||this).findTeX=n.options.FindTeX||new u.FindTeX(f);var p=n.options.packages,d=n.configuration=e.configure(p),g=n._parseOptions=new h.default(d,[n.options,m.TagsFactory.OPTIONS]);return l.userOptions(g.options,i),d.config(n),e.tags(g,d),n.postFilters.add(c.default.cleanSubSup,-6),n.postFilters.add(c.default.setInherited,-5),n.postFilters.add(c.default.moveLimits,-4),n.postFilters.add(c.default.cleanStretchy,-3),n.postFilters.add(c.default.cleanAttributes,-2),n.postFilters.add(c.default.combineRelations,-1),n}return a(e,t),e.configure=function(t){var e=new g.ParserConfiguration(t);return e.init(),e},e.tags=function(t,e){m.TagsFactory.addTags(e.tags),m.TagsFactory.setDefault(t.options.tags),t.tags=m.TagsFactory.getDefault(),t.tags.configuration=t},e.prototype.setMmlFactory=function(e){t.prototype.setMmlFactory.call(this,e),this._parseOptions.nodeFactory.setMmlFactory(e)},Object.defineProperty(e.prototype,"parseOptions",{get:function(){return this._parseOptions},enumerable:!1,configurable:!0}),e.prototype.compile=function(t,e){this.parseOptions.clear(),this.executeFilters(this.preFilters,t,e,this.parseOptions);var r,n=t.display;this.latex=t.math,this.parseOptions.tags.startEquation(t);try{r=new p.default(this.latex,{display:n,isInner:!1},this.parseOptions).mml()}catch(t){if(!(t instanceof d.default))throw t;this.parseOptions.error=!0,r=this.options.formatError(this,t)}return r=this.parseOptions.nodeFactory.create("node","math",[r]),n&&f.default.setAttribute(r,"display","block"),this.parseOptions.tags.finishEquation(t),this.parseOptions.root=r,this.executeFilters(this.postFilters,t,e,this.parseOptions),this.mathNode=this.parseOptions.root,this.mathNode},e.prototype.findMath=function(t){return this.findTeX.findMath(t)},e.prototype.formatError=function(t){var e=t.message.replace(/\n.*/,"");return this.parseOptions.nodeFactory.create("error",e,t.id,this.latex)},e.NAME="TeX",e.OPTIONS=i(i({},s.AbstractInputJax.OPTIONS),{FindTeX:null,packages:["base"],digits:/^(?:[0-9]+(?:\{,\}[0-9]{3})*(?:\.[0-9]*)?|\.[0-9]+)/,maxBuffer:5120,formatError:function(t,e){return t.formatError(e)}}),e}(s.AbstractInputJax);e.TeX=y},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.FindTeX=void 0;var o=r(43),s=r(44),l=r(45),u=function(t){function e(e){var r=t.call(this,e)||this;return r.getPatterns(),r}return a(e,t),e.prototype.getPatterns=function(){var t=this,e=this.options,r=[],n=[],a=[];this.end={},this.env=this.sub=0;var i=1;e.inlineMath.forEach((function(e){return t.addPattern(r,e,!1)})),e.displayMath.forEach((function(e){return t.addPattern(r,e,!0)})),r.length&&n.push(r.sort(s.sortLength).join("|")),e.processEnvironments&&(n.push("\\\\begin\\s*\\{([^}]*)\\}"),this.env=i,i++),e.processEscapes&&a.push("\\\\([\\\\$])"),e.processRefs&&a.push("(\\\\(?:eq)?ref\\s*\\{[^}]*\\})"),a.length&&(n.push("("+a.join("|")+")"),this.sub=i),this.start=new RegExp(n.join("|"),"g"),this.hasPatterns=n.length>0},e.prototype.addPattern=function(t,e,r){var n=i(e,2),a=n[0],o=n[1];t.push(s.quotePattern(a)),this.end[a]=[o,r,this.endPattern(o)]},e.prototype.endPattern=function(t,e){return new RegExp((e||s.quotePattern(t))+"|\\\\(?:[a-zA-Z]|.)|[{}]","g")},e.prototype.findEnd=function(t,e,r,n){for(var a,o=i(n,3),s=o[0],u=o[1],c=o[2],f=c.lastIndex=r.index+r[0].length,p=0;a=c.exec(t);){if((a[1]||a[0])===s&&0===p)return l.protoItem(r[0],t.substr(f,a.index-f),a[0],e,r.index,a.index+a[0].length,u);"{"===a[0]?p++:"}"===a[0]&&p&&p--}return null},e.prototype.findMathInString=function(t,e,r){var n,a;for(this.start.lastIndex=0;n=this.start.exec(r);){if(void 0!==n[this.env]&&this.env){var i="\\\\end\\s*(\\{"+s.quotePattern(n[this.env])+"\\})";(a=this.findEnd(r,e,n,["{"+n[this.env]+"}",!0,this.endPattern(null,i)]))&&(a.math=a.open+a.math+a.close,a.open=a.close="")}else if(void 0!==n[this.sub]&&this.sub){var o=n[this.sub];i=n.index+n[this.sub].length;a=2===o.length?l.protoItem("",o.substr(1),"",e,n.index,i):l.protoItem("",o,"",e,n.index,i,!1)}else a=this.findEnd(r,e,n,this.end[n[0]]);a&&(t.push(a),this.start.lastIndex=a.end.n)}},e.prototype.findMath=function(t){var e=[];if(this.hasPatterns)for(var r=0,n=t.length;r<n;r++)this.findMathInString(e,r,t[r]);return e},e.OPTIONS={inlineMath:[["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],processEscapes:!0,processEnvironments:!0,processRefs:!0},e}(o.AbstractFindMath);e.FindTeX=u},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var a,i=r(5),o=r(1);!function(t){t.cleanStretchy=function(t){var e,r,a=t.data;try{for(var i=n(a.getList("fixStretchy")),s=i.next();!s.done;s=i.next()){var l=s.value;if(o.default.getProperty(l,"fixStretchy")){var u=o.default.getForm(l);u&&u[3]&&u[3].stretchy&&o.default.setAttribute(l,"stretchy",!1);var c=l.parent;if(!(o.default.getTexClass(l)||u&&u[2])){var f=a.nodeFactory.create("node","TeXAtom",[l]);c.replaceChild(f,l),f.inheritAttributesFrom(l)}o.default.removeProperties(l,"fixStretchy")}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},t.cleanAttributes=function(t){t.data.root.walkTree((function(t,e){var r,a,i=t.attributes;if(i)try{for(var o=n(i.getExplicitNames()),s=o.next();!s.done;s=o.next()){var l=s.value;i.attributes[l]===t.attributes.getInherited(l)&&delete i.attributes[l]}}catch(t){r={error:t}}finally{try{s&&!s.done&&(a=o.return)&&a.call(o)}finally{if(r)throw r.error}}}),{})},t.combineRelations=function(t){var a,s;try{for(var l=n(t.data.getList("mo")),u=l.next();!u.done;u=l.next()){var c=u.value;if(!c.getProperty("relationsCombined")&&c.parent&&(!c.parent||o.default.isType(c.parent,"mrow"))&&o.default.getTexClass(c)===i.TEXCLASS.REL){for(var f=c.parent,p=void 0,d=f.childNodes,h=d.indexOf(c)+1,m=o.default.getProperty(c,"variantForm");h<d.length&&(p=d[h])&&o.default.isType(p,"mo")&&o.default.getTexClass(p)===i.TEXCLASS.REL;){if(m!==o.default.getProperty(p,"variantForm")||!r(c,p)){null==c.attributes.getExplicit("rspace")&&o.default.setAttribute(c,"rspace","0pt"),null==p.attributes.getExplicit("lspace")&&o.default.setAttribute(p,"lspace","0pt");break}o.default.appendChildren(c,o.default.getChildren(p)),e(["stretchy","rspace"],c,p),o.default.setProperties(c,p.getAllProperties()),d.splice(h,1),p.parent=null,p.setProperty("relationsCombined",!0)}c.attributes.setInherited("form",c.getForms()[0])}}}catch(t){a={error:t}}finally{try{u&&!u.done&&(s=l.return)&&s.call(l)}finally{if(a)throw a.error}}};var e=function(t,e,r){var n=e.attributes,a=r.attributes;t.forEach((function(t){var e=a.getExplicit(t);null!=e&&n.set(t,e)}))},r=function(t,e){var r,a,i=function(t,e){return t.getExplicitNames().filter((function(r){return r!==e&&("stretchy"!==r||t.getExplicit("stretchy"))}))},o=t.attributes,s=e.attributes,l=i(o,"lspace"),u=i(s,"rspace");if(l.length!==u.length)return!1;try{for(var c=n(l),f=c.next();!f.done;f=c.next()){var p=f.value;if(o.getExplicit(p)!==s.getExplicit(p))return!1}}catch(t){r={error:t}}finally{try{f&&!f.done&&(a=c.return)&&a.call(c)}finally{if(r)throw r.error}}return!0},a=function(t,e,r){var a,i;try{for(var s=n(t.getList("m"+e+r)),l=s.next();!l.done;l=s.next()){var u=l.value,c=u.childNodes;if(!c[u[e]]||!c[u[r]]){var f=u.parent,p=c[u[e]]?t.nodeFactory.create("node","m"+e,[c[u.base],c[u[e]]]):t.nodeFactory.create("node","m"+r,[c[u.base],c[u[r]]]);o.default.copyAttributes(u,p),f?f.replaceChild(p,u):t.root=p}}}catch(t){a={error:t}}finally{try{l&&!l.done&&(i=s.return)&&i.call(s)}finally{if(a)throw a.error}}};t.cleanSubSup=function(t){var e=t.data;e.error||(a(e,"sub","sup"),a(e,"under","over"))};var s=function(t,e,r){var a,i;try{for(var s=n(t.getList(e)),l=s.next();!l.done;l=s.next()){var u=l.value;if(!u.attributes.get("displaystyle")){var c=u.childNodes[u.base],f=c.coreMO();if(c.getProperty("movablelimits")&&!f.attributes.getExplicit("movablelimits")){var p=t.nodeFactory.create("node",r,u.childNodes);o.default.copyAttributes(u,p),u.parent?u.parent.replaceChild(p,u):t.root=p}}}}catch(t){a={error:t}}finally{try{l&&!l.done&&(i=s.return)&&i.call(s)}finally{if(a)throw a.error}}};t.moveLimits=function(t){var e=t.data;s(e,"munderover","msubsup"),s(e,"munder","msub"),s(e,"mover","msup")},t.setInherited=function(t){t.data.root.setInheritedAttributes({},t.math.display,0,!1)}}(a||(a={})),e.default=a},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0});var o=r(1),s=function(){function t(t,e,r){this._factory=t,this._env=e,this.global={},this.stack=[],this.global={isInner:r},this.stack=[this._factory.create("start",this.global)],e&&(this.stack[0].env=e),this.env=this.stack[0].env}return Object.defineProperty(t.prototype,"env",{get:function(){return this._env},set:function(t){this._env=t},enumerable:!1,configurable:!0}),t.prototype.Push=function(){for(var t,e,r=[],s=0;s<arguments.length;s++)r[s]=arguments[s];try{for(var l=n(r),u=l.next();!u.done;u=l.next()){var c=u.value;if(c){var f=o.default.isNode(c)?this._factory.create("mml",c):c;f.global=this.global;var p=a(this.stack.length?this.Top().checkItem(f):[null,!0],2),d=p[0],h=p[1];h&&(d?(this.Pop(),this.Push.apply(this,i(d))):(this.stack.push(f),f.env?(f.copyEnv&&Object.assign(f.env,this.env),this.env=f.env):f.env=this.env))}}}catch(e){t={error:e}}finally{try{u&&!u.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}},t.prototype.Pop=function(){var t=this.stack.pop();return t.isOpen||delete t.env,this.env=this.stack.length?this.Top().env:{},t},t.prototype.Top=function(t){return void 0===t&&(t=1),this.stack.length<t?null:this.stack[this.stack.length-t]},t.prototype.Prev=function(t){var e=this.Top();return t?e.First:e.Pop()},t.prototype.toString=function(){return"stack[\n  "+this.stack.join("\n  ")+"\n]"},t}();e.default=s},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0});var o=r(26),s=r(27),l=r(6),u=function(){function t(t,e){void 0===e&&(e=[]),this.options={},this.packageData=new Map,this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.handlers=t.handlers,this.nodeFactory=new s.NodeFactory,this.nodeFactory.configuration=this,this.nodeFactory.setCreators(t.nodes),this.itemFactory=new o.default(t.items),this.itemFactory.configuration=this,l.defaultOptions.apply(void 0,a([this.options],e)),l.defaultOptions(this.options,t.options)}return t.prototype.pushParser=function(t){this.parsers.unshift(t)},t.prototype.popParser=function(){this.parsers.shift()},Object.defineProperty(t.prototype,"parser",{get:function(){return this.parsers[0]},enumerable:!1,configurable:!0}),t.prototype.clear=function(){this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.tags.resetTag()},t.prototype.addNode=function(t,e){var r=this.nodeLists[t];r||(r=this.nodeLists[t]=[]),r.push(e)},t.prototype.getList=function(t){var e,r,n=this.nodeLists[t]||[],a=[];try{for(var o=i(n),s=o.next();!s.done;s=o.next()){var l=s.value;this.inTree(l)&&a.push(l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return this.nodeLists[t]=a,a},t.prototype.inTree=function(t){for(;t&&t!==this.root;)t=t.parent;return!!t},t}();e.default=u},function(t,e,r){"use strict";var n,a,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0});var o=r(13),s=r(47),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(o.BaseItem),u=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.defaultKind="dummy",e.configuration=null,e}return i(e,t),e.DefaultStackItems=((a={})[l.prototype.kind]=l,a),e}(s.AbstractFactory);e.default=u},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.NodeFactory=void 0;var i=r(1),o=function(){function t(){this.mmlFactory=null,this.factory={node:t.createNode,token:t.createToken,text:t.createText,error:t.createError}}return t.createNode=function(t,e,r,n,a){void 0===r&&(r=[]),void 0===n&&(n={});var o=t.mmlFactory.create(e),s=o.arity;if(s===1/0||-1===s)1===r.length&&r[0].isInferred?o.setChildren(i.default.getChildren(r[0])):o.setChildren(r);else{for(var l=[],u=0,c=void 0;c=r[u];u++)if(c.isInferred){var f=t.mmlFactory.create("mrow",{},i.default.getChildren(c));i.default.copyAttributes(c,f),l.push(f)}else l.push(c);o.setChildren(l)}return a&&o.appendChild(a),i.default.setProperties(o,n),o},t.createToken=function(t,e,r,n){void 0===r&&(r={}),void 0===n&&(n="");var a=t.create("text",n);return t.create("node",e,[],r,a)},t.createText=function(t,e){return null==e?null:t.mmlFactory.create("text").setText(e)},t.createError=function(t,e){var r=t.create("text",e),n=t.create("node","mtext",[],{},r);return t.create("node","merror",[n],{"data-mjx-error":e})},t.prototype.setMmlFactory=function(t){this.mmlFactory=t},t.prototype.set=function(t,e){this.factory[t]=e},t.prototype.setCreators=function(t){for(var e in t)this.set(e,t[e])},t.prototype.create=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=this.factory[t]||this.factory.node,i=n.apply(void 0,a([this,e[0]],e.slice(1)));return"node"===t&&this.configuration.addNode(e[0],i),i},t.prototype.get=function(t){return this.factory[t]},t}();e.NodeFactory=o},function(t,e,r){"use strict";var n,a,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BaseConfiguration=e.BaseTags=e.Other=void 0;var o=r(4),s=r(12),l=r(0),u=r(1),c=r(2),f=r(14),p=r(9);function d(t,e){var r=t.stack.env.font?{mathvariant:t.stack.env.font}:{},n=s.MapHandler.getMap("remap").lookup(e),a=t.create("token","mo",r,n?n.char:e);u.default.setProperty(a,"fixStretchy",!0),t.configuration.addNode("fixStretchy",a),t.Push(a)}r(48),new c.CharacterMap("remap",null,{"-":"\u2212","*":"\u2217","`":"\u2018"}),e.Other=d;var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(p.AbstractTags);e.BaseTags=h,e.BaseConfiguration=o.Configuration.create("base",{handler:{character:["command","special","letter","digit"],delimiter:["delimiter"],macro:["delimiter","macros","mathchar0mi","mathchar0mo","mathchar7"],environment:["environment"]},fallback:{character:d,macro:function(t,e){throw new l.default("UndefinedControlSequence","Undefined control sequence %1","\\"+e)},environment:function(t,e){throw new l.default("UnknownEnv","Unknown environment '%1'",e)}},items:(a={},a[f.StartItem.prototype.kind]=f.StartItem,a[f.StopItem.prototype.kind]=f.StopItem,a[f.OpenItem.prototype.kind]=f.OpenItem,a[f.CloseItem.prototype.kind]=f.CloseItem,a[f.PrimeItem.prototype.kind]=f.PrimeItem,a[f.SubsupItem.prototype.kind]=f.SubsupItem,a[f.OverItem.prototype.kind]=f.OverItem,a[f.LeftItem.prototype.kind]=f.LeftItem,a[f.RightItem.prototype.kind]=f.RightItem,a[f.BeginItem.prototype.kind]=f.BeginItem,a[f.EndItem.prototype.kind]=f.EndItem,a[f.StyleItem.prototype.kind]=f.StyleItem,a[f.PositionItem.prototype.kind]=f.PositionItem,a[f.CellItem.prototype.kind]=f.CellItem,a[f.MmlItem.prototype.kind]=f.MmlItem,a[f.FnItem.prototype.kind]=f.FnItem,a[f.NotItem.prototype.kind]=f.NotItem,a[f.DotsItem.prototype.kind]=f.DotsItem,a[f.ArrayItem.prototype.kind]=f.ArrayItem,a[f.EqnArrayItem.prototype.kind]=f.EqnArrayItem,a[f.EquationItem.prototype.kind]=f.EquationItem,a),options:{maxMacros:1e3,baseURL:"undefined"==typeof document||0===document.getElementsByTagName("base").length?"":String(document.location).replace(/#.*$/,"")},tags:{base:h}})},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MultlineItem=void 0;var i=r(14),o=r(3),s=r(1),l=r(0),u=r(7),c=function(t){function e(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var a=t.call(this,e)||this;return a.factory.configuration.tags.start("multline",!0,r[0]),a}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"multline"},enumerable:!1,configurable:!0}),e.prototype.EndEntry=function(){this.table.length&&o.default.fixInitialMO(this.factory.configuration,this.nodes);var t=this.getProperty("shove"),e=this.create("node","mtd",this.nodes,t?{columnalign:t}:{});this.setProperty("shove",null),this.row.push(e),this.Clear()},e.prototype.EndRow=function(){if(1!==this.row.length)throw new l.default("MultlineRowsOneCol","The rows within the %1 environment must have exactly one column","multline");var t=this.create("node","mtr",this.row);this.table.push(t),this.row=[]},e.prototype.EndTable=function(){if(t.prototype.EndTable.call(this),this.table.length){var e=this.table.length-1,r=-1;s.default.getAttribute(s.default.getChildren(this.table[0])[0],"columnalign")||s.default.setAttribute(s.default.getChildren(this.table[0])[0],"columnalign",u.TexConstant.Align.LEFT),s.default.getAttribute(s.default.getChildren(this.table[e])[0],"columnalign")||s.default.setAttribute(s.default.getChildren(this.table[e])[0],"columnalign",u.TexConstant.Align.RIGHT);var n=this.factory.configuration.tags.getTag();if(n){r=this.arraydef.side===u.TexConstant.Align.LEFT?0:this.table.length-1;var a=this.table[r],i=this.create("node","mlabeledtr",[n].concat(s.default.getChildren(a)));s.default.copyAttributes(a,i),this.table[r]=i}}this.factory.configuration.tags.end()},e}(i.ArrayItem);e.MultlineItem=c},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.RequireConfiguration=e.options=e.RequireMethods=e.RequireLoad=void 0;var o=r(4),s=r(2),l=r(0),u=r(21),c=r(36),f=r(31),p=r(52),d=r(6),h=u.MathJax.config;function m(t,e){var r,a=t.parseOptions.options.require,i=t.parseOptions.packageData.get("require").required,s=e.substr(a.prefix.length);if(i.indexOf(s)<0){i.push(s),function(t,e){var r,a;void 0===e&&(e=[]);var i=t.parseOptions.options.require.prefix;try{for(var o=n(e),s=o.next();!s.done;s=o.next()){var l=s.value;l.substr(0,i.length)===i&&m(t,l)}}catch(t){r={error:t}}finally{try{s&&!s.done&&(a=o.return)&&a.call(o)}finally{if(r)throw r.error}}}(t,f.CONFIG.dependencies[e]);var l=o.ConfigurationHandler.get(s);if(l){var u=h[e]||{};l.options&&1===Object.keys(l.options).length&&l.options[s]&&((r={})[s]=u,u=r),t.configuration.add(l,t,u);var c=t.parseOptions.packageData.get("require").configured;l.preprocessors.length&&!c.has(s)&&(c.set(s,!0),p.mathjax.retryAfter(Promise.resolve()))}}}function g(t,e){var r=t.options.require,n=r.allow,a=("["===e.substr(0,1)?"":r.prefix)+e;if(!(n.hasOwnProperty(a)?n[a]:n.hasOwnProperty(e)?n[e]:r.defaultAllow))throw new l.default("BadRequire",'Extension "%1" is now allowed to be loaded',a);c.Package.packages.has(a)?m(t.configuration.packageData.get("require").jax,a):p.mathjax.retryAfter(f.Loader.load(a))}e.RequireLoad=g,e.RequireMethods={Require:function(t,e){var r=t.GetArgument(e);if(r.match(/[^_a-zA-Z0-9]/)||""===r)throw new l.default("BadPackageName","Argument for %1 is not a valid package name",e);g(t,r)}},e.options={require:{allow:d.expandable({base:!1,"all-packages":!1}),defaultAllow:!0,prefix:"tex"}},new s.CommandMap("require",{require:"Require"},e.RequireMethods),e.RequireConfiguration=o.Configuration.create("require",{handler:{macro:["require"]},config:function(t,e){e.parseOptions.packageData.set("require",{jax:e,required:i(e.options.packages),configured:new Map});var r=e.parseOptions.options.require,n=r.prefix;if(n.match(/[^_a-zA-Z0-9]/))throw Error("Illegal characters used in \\require prefix");f.CONFIG.paths[n]||(f.CONFIG.paths[n]="[mathjax]/input/tex/extensions"),r.prefix="["+n+"]/"},options:e.options})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Loader=MathJax._.components.loader.Loader,e.MathJax=MathJax._.components.loader.MathJax,e.CONFIG=MathJax._.components.loader.CONFIG},function(t,e,r){"use strict";var n,a=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.BeginEnvItem=void 0;var i=r(0),o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),Object.defineProperty(e.prototype,"kind",{get:function(){return"beginEnv"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.checkItem=function(e){if(e.isKind("end")){if(e.getName()!==this.getName())throw new i.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),e.getName());return[[this.factory.create("mml",this.toMml())],!0]}if(e.isKind("stop"))throw new i.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return t.prototype.checkItem.call(this,e)},e}(r(13).BaseItem);e.BeginEnvItem=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(11).add({NJcy:"\u040a",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041d",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",NewLine:"\n",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Not:"\u2aec",NotCongruent:"\u2262",NotCupCap:"\u226d",NotEqualTilde:"\u2242\u0338",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",NotLeftTriangleBar:"\u29cf\u0338",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",NotPrecedesEqual:"\u2aaf\u0338",NotReverseElement:"\u220c",NotRightTriangleBar:"\u29d0\u0338",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",Ntilde:"\xd1",Nu:"\u039d",nGg:"\u22d9\u0338",nGt:"\u226b\u20d2",nGtv:"\u226b\u0338",nLl:"\u22d8\u0338",nLt:"\u226a\u20d2",nLtv:"\u226a\u0338",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",neArr:"\u21d7",nearhk:"\u2924",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",ngsim:"\u2275",ngt:"\u226f",ngtr:"\u226f",nhArr:"\u21ce",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",njcy:"\u045a",nlArr:"\u21cd",nlE:"\u2266\u0338",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219a",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nlsim:"\u2274",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nmid:"\u2224",notin:"\u2209",notinE:"\u22f9\u0338",notindot:"\u22f5\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",num:"#",numero:"\u2116",numsp:"\u2007",nvHarr:"\u2904",nvap:"\u224d\u20d2",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwArr:"\u21d6",nwarhk:"\u2923",nwarrow:"\u2196",nwnear:"\u2927"},"n")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PrioritizedList=MathJax._.util.PrioritizedList.PrioritizedList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FunctionList=MathJax._.util.FunctionList.FunctionList},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.PackageError=MathJax._.components.package.PackageError,e.Package=MathJax._.components.package.Package},function(t,e,r){"use strict";var n,a,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.AmsConfiguration=e.AmsTags=void 0;var o=r(4),s=r(29),l=r(9),u=r(17);r(51);var c=r(2),f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e}(l.AbstractTags);e.AmsTags=f;e.AmsConfiguration=o.Configuration.create("ams",{handler:{delimiter:["AMSsymbols-delimiter","AMSmath-delimiter"],macro:["AMSsymbols-mathchar0mi","AMSsymbols-mathchar0m0","AMSsymbols-delimiter","AMSsymbols-macros","AMSmath-mathchar0mo","AMSmath-macros","AMSmath-delimiter"],environment:["AMSmath-environment"]},items:(a={},a[s.MultlineItem.prototype.kind]=s.MultlineItem,a),tags:{ams:f},init:function(t){new c.CommandMap(u.NEW_OPS,{},{}),t.append(o.Configuration.local({handler:{macro:[u.NEW_OPS]},priority:-1}))}})},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,a,i=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)o.push(n.value)}catch(t){a={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(a)throw a.error}}return o},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.AutoloadConfiguration=void 0;var i=r(4),o=r(2),s=r(10),l=r(30),u=r(36),c=r(6);function f(t,e,r,i){var o,s,c,f;if(u.Package.packages.has(t.options.require.prefix+r)){var h=t.options.autoload[r],m=n(2===h.length&&Array.isArray(h[0])?h:[h,[]],2),g=m[0],y=m[1];try{for(var v=a(g),b=v.next();!b.done;b=v.next()){var A=b.value;p.remove(A)}}catch(t){o={error:t}}finally{try{b&&!b.done&&(s=v.return)&&s.call(v)}finally{if(o)throw o.error}}try{for(var M=a(y),x=M.next();!x.done;x=M.next()){var T=x.value;d.remove(T)}}catch(t){c={error:t}}finally{try{x&&!x.done&&(f=M.return)&&f.call(M)}finally{if(c)throw c.error}}t.string=(i?e:"\\begin{"+e.slice(1)+"}")+t.string.slice(t.i),t.i=0}l.RequireLoad(t,r)}var p=new o.CommandMap("autoload-macros",{},{}),d=new o.CommandMap("autoload-environments",{},{});e.AutoloadConfiguration=i.Configuration.create("autoload",{handler:{macro:["autoload-macros"],environment:["autoload-environments"]},options:{autoload:c.expandable({action:["toggle","mathtip","texttip"],amscd:[[],["CD"]],bbox:["bbox"],boldsymbol:["boldsymbol"],braket:["bra","ket","braket","set","Bra","Ket","Braket","Set","ketbra","Ketbra"],bussproofs:[[],["prooftree"]],cancel:["cancel","bcancel","xcancel","cancelto"],color:["color","definecolor","textcolor","colorbox","fcolorbox"],enclose:["enclose"],extpfeil:["xtwoheadrightarrow","xtwoheadleftarrow","xmapsto","xlongequal","xtofrom","Newextarrow"],html:["href","class","style","cssId"],mhchem:["ce","pu"],newcommand:["newcommand","renewcommand","newenvironment","renewenvironment","def","let"],unicode:["unicode"],verb:["verb"]})},config:function(t,e){var r,i,o,u,c,h,m=e.parseOptions,g=m.handlers.get("macro"),y=m.handlers.get("environment"),v=m.options.autoload;m.packageData.set("autoload",{Autoload:f});try{for(var b=a(Object.keys(v)),A=b.next();!A.done;A=b.next()){var M=A.value,x=v[M],T=n(2===x.length&&Array.isArray(x[0])?x:[x,[]],2),w=T[0],S=T[1];try{for(var P=(o=void 0,a(w)),C=P.next();!C.done;C=P.next()){var O=C.value;g.lookup(O)&&"color"!==O||p.add(O,new s.Macro(O,f,[M,!0]))}}catch(t){o={error:t}}finally{try{C&&!C.done&&(u=P.return)&&u.call(P)}finally{if(o)throw o.error}}try{for(var E=(c=void 0,a(S)),k=E.next();!k.done;k=E.next()){var _=k.value;y.lookup(_)||d.add(_,new s.Macro(_,f,[M,!1]))}}catch(t){c={error:t}}finally{try{k&&!k.done&&(h=E.return)&&h.call(E)}finally{if(c)throw c.error}}}}catch(t){r={error:t}}finally{try{A&&!A.done&&(i=b.return)&&i.call(b)}finally{if(r)throw r.error}}m.packageData.get("require")||l.RequireConfiguration.config(t,e)},init:function(t){t.options.require||c.defaultOptions(t.options,l.RequireConfiguration.options)},priority:10})},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.ConfigMacrosConfiguration=void 0;var a=r(4),i=r(6),o=r(2),s=r(10),l=r(18);e.ConfigMacrosConfiguration=a.Configuration.create("configmacros",{init:function(t){new o.CommandMap("configmacros-map",{},{}),t.append(a.Configuration.local({handler:{macro:["configmacros-map"]},priority:3}))},config:function(t,e){var r,a,i=e.parseOptions.handlers.retrieve("configmacros-map"),o=e.parseOptions.options.macros;try{for(var u=n(Object.keys(o)),c=u.next();!c.done;c=u.next()){var f=c.value,p="string"==typeof o[f]?[o[f]]:o[f],d=Array.isArray(p[2])?new s.Macro(f,l.default.MacroWithTemplate,p.slice(0,2).concat(p[2])):new s.Macro(f,l.default.Macro,p);i.add(f,d)}}catch(t){r={error:t}}finally{try{c&&!c.done&&(a=u.return)&&a.call(u)}finally{if(r)throw r.error}}},options:{macros:i.expandable({})}})},function(t,e,r){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),e.NewcommandConfiguration=void 0;var a=r(4),i=r(32),o=r(19);r(53);var s=r(16),l=r(2);e.NewcommandConfiguration=a.Configuration.create("newcommand",{handler:{macro:["Newcommand-macros"]},items:(n={},n[i.BeginEnvItem.prototype.kind]=i.BeginEnvItem,n),options:{maxMacros:1e3},init:function(t){new l.DelimiterMap(o.default.NEW_DELIMITER,s.default.delimiter,{}),new l.CommandMap(o.default.NEW_COMMAND,{},{}),new l.EnvironmentMap(o.default.NEW_ENVIRONMENT,s.default.environment,{},{}),t.append(a.Configuration.local({handler:{character:[],delimiter:[o.default.NEW_DELIMITER],macro:[o.default.NEW_DELIMITER,o.default.NEW_COMMAND],environment:[o.default.NEW_ENVIRONMENT]},priority:-1}))}})},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.NoUndefinedConfiguration=void 0;var a=r(4);e.NoUndefinedConfiguration=a.Configuration.create("noundefined",{fallback:{macro:function(t,e){var r,a,i=t.create("text","\\"+e),o=t.options.noundefined||{},s={};try{for(var l=n(["color","background","size"]),u=l.next();!u.done;u=l.next()){var c=u.value;o[c]&&(s["math"+c]=o[c])}}catch(t){r={error:t}}finally{try{u&&!u.done&&(a=l.return)&&a.call(l)}finally{if(r)throw r.error}}t.Push(t.create("node","mtext",[],s,i))}},options:{noundefined:{color:"red",background:"",size:""}},priority:3})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractInputJax=MathJax._.core.InputJax.AbstractInputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFindMath=MathJax._.core.FindMath.AbstractFindMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sortLength=MathJax._.util.string.sortLength,e.quotePattern=MathJax._.util.string.quotePattern,e.unicodeChars=MathJax._.util.string.unicodeChars,e.isPercent=MathJax._.util.string.isPercent,e.split=MathJax._.util.string.split},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.protoItem=MathJax._.core.MathItem.protoItem,e.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,e.STATE=MathJax._.core.MathItem.STATE,e.newState=MathJax._.core.MathItem.newState},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMo=MathJax._.core.MmlTree.MmlNodes.mo.MmlMo},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractFactory=MathJax._.core.Tree.Factory.AbstractFactory},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(2),a=r(7),i=r(15),o=r(16),s=r(5);new n.RegExpMap("letter",o.default.variable,/[a-z]/i),new n.RegExpMap("digit",o.default.digit,/[0-9.,]/),new n.RegExpMap("command",o.default.controlSequence,/^\\/),new n.MacroMap("special",{"{":"Open","}":"Close","~":"Tilde","^":"Superscript",_:"Subscript"," ":"Space","\t":"Space","\r":"Space","\n":"Space","'":"Prime","%":"Comment","&":"Entry","#":"Hash","\xa0":"Space","\u2019":"Prime"},i.default),new n.CharacterMap("mathchar0mi",o.default.mathchar0mi,{alpha:"\u03b1",beta:"\u03b2",gamma:"\u03b3",delta:"\u03b4",epsilon:"\u03f5",zeta:"\u03b6",eta:"\u03b7",theta:"\u03b8",iota:"\u03b9",kappa:"\u03ba",lambda:"\u03bb",mu:"\u03bc",nu:"\u03bd",xi:"\u03be",omicron:"\u03bf",pi:"\u03c0",rho:"\u03c1",sigma:"\u03c3",tau:"\u03c4",upsilon:"\u03c5",phi:"\u03d5",chi:"\u03c7",psi:"\u03c8",omega:"\u03c9",varepsilon:"\u03b5",vartheta:"\u03d1",varpi:"\u03d6",varrho:"\u03f1",varsigma:"\u03c2",varphi:"\u03c6",S:["\xa7",{mathvariant:a.TexConstant.Variant.NORMAL}],aleph:["\u2135",{mathvariant:a.TexConstant.Variant.NORMAL}],hbar:["\u210f",{variantForm:!0}],imath:"\u0131",jmath:"\u0237",ell:"\u2113",wp:["\u2118",{mathvariant:a.TexConstant.Variant.NORMAL}],Re:["\u211c",{mathvariant:a.TexConstant.Variant.NORMAL}],Im:["\u2111",{mathvariant:a.TexConstant.Variant.NORMAL}],partial:["\u2202",{mathvariant:a.TexConstant.Variant.ITALIC}],infty:["\u221e",{mathvariant:a.TexConstant.Variant.NORMAL}],prime:["\u2032",{mathvariant:a.TexConstant.Variant.NORMAL,variantForm:!0}],emptyset:["\u2205",{mathvariant:a.TexConstant.Variant.NORMAL}],nabla:["\u2207",{mathvariant:a.TexConstant.Variant.NORMAL}],top:["\u22a4",{mathvariant:a.TexConstant.Variant.NORMAL}],bot:["\u22a5",{mathvariant:a.TexConstant.Variant.NORMAL}],angle:["\u2220",{mathvariant:a.TexConstant.Variant.NORMAL}],triangle:["\u25b3",{mathvariant:a.TexConstant.Variant.NORMAL}],backslash:["\u2216",{mathvariant:a.TexConstant.Variant.NORMAL,variantForm:!0}],forall:["\u2200",{mathvariant:a.TexConstant.Variant.NORMAL}],exists:["\u2203",{mathvariant:a.TexConstant.Variant.NORMAL}],neg:["\xac",{mathvariant:a.TexConstant.Variant.NORMAL}],lnot:["\xac",{mathvariant:a.TexConstant.Variant.NORMAL}],flat:["\u266d",{mathvariant:a.TexConstant.Variant.NORMAL}],natural:["\u266e",{mathvariant:a.TexConstant.Variant.NORMAL}],sharp:["\u266f",{mathvariant:a.TexConstant.Variant.NORMAL}],clubsuit:["\u2663",{mathvariant:a.TexConstant.Variant.NORMAL}],diamondsuit:["\u2662",{mathvariant:a.TexConstant.Variant.NORMAL}],heartsuit:["\u2661",{mathvariant:a.TexConstant.Variant.NORMAL}],spadesuit:["\u2660",{mathvariant:a.TexConstant.Variant.NORMAL}]}),new n.CharacterMap("mathchar0mo",o.default.mathchar0mo,{surd:"\u221a",coprod:["\u2210",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigvee:["\u22c1",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigwedge:["\u22c0",{texClass:s.TEXCLASS.OP,movesupsub:!0}],biguplus:["\u2a04",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigcap:["\u22c2",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigcup:["\u22c3",{texClass:s.TEXCLASS.OP,movesupsub:!0}],int:["\u222b",{texClass:s.TEXCLASS.OP}],intop:["\u222b",{texClass:s.TEXCLASS.OP,movesupsub:!0,movablelimits:!0}],iint:["\u222c",{texClass:s.TEXCLASS.OP}],iiint:["\u222d",{texClass:s.TEXCLASS.OP}],prod:["\u220f",{texClass:s.TEXCLASS.OP,movesupsub:!0}],sum:["\u2211",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigotimes:["\u2a02",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigoplus:["\u2a01",{texClass:s.TEXCLASS.OP,movesupsub:!0}],bigodot:["\u2a00",{texClass:s.TEXCLASS.OP,movesupsub:!0}],oint:["\u222e",{texClass:s.TEXCLASS.OP}],bigsqcup:["\u2a06",{texClass:s.TEXCLASS.OP,movesupsub:!0}],smallint:["\u222b",{largeop:!1}],triangleleft:"\u25c3",triangleright:"\u25b9",bigtriangleup:"\u25b3",bigtriangledown:"\u25bd",wedge:"\u2227",land:"\u2227",vee:"\u2228",lor:"\u2228",cap:"\u2229",cup:"\u222a",ddagger:"\u2021",dagger:"\u2020",sqcap:"\u2293",sqcup:"\u2294",uplus:"\u228e",amalg:"\u2a3f",diamond:"\u22c4",bullet:"\u2219",wr:"\u2240",div:"\xf7",odot:["\u2299",{largeop:!1}],oslash:["\u2298",{largeop:!1}],otimes:["\u2297",{largeop:!1}],ominus:["\u2296",{largeop:!1}],oplus:["\u2295",{largeop:!1}],mp:"\u2213",pm:"\xb1",circ:"\u2218",bigcirc:"\u25ef",setminus:"\u2216",cdot:"\u22c5",ast:"\u2217",times:"\xd7",star:"\u22c6",propto:"\u221d",sqsubseteq:"\u2291",sqsupseteq:"\u2292",parallel:"\u2225",mid:"\u2223",dashv:"\u22a3",vdash:"\u22a2",leq:"\u2264",le:"\u2264",geq:"\u2265",ge:"\u2265",lt:"<",gt:">",succ:"\u227b",prec:"\u227a",approx:"\u2248",succeq:"\u2ab0",preceq:"\u2aaf",supset:"\u2283",subset:"\u2282",supseteq:"\u2287",subseteq:"\u2286",in:"\u2208",ni:"\u220b",notin:"\u2209",owns:"\u220b",gg:"\u226b",ll:"\u226a",sim:"\u223c",simeq:"\u2243",perp:"\u22a5",equiv:"\u2261",asymp:"\u224d",smile:"\u2323",frown:"\u2322",ne:"\u2260",neq:"\u2260",cong:"\u2245",doteq:"\u2250",bowtie:"\u22c8",models:"\u22a8",notChar:"\u29f8",Leftrightarrow:"\u21d4",Leftarrow:"\u21d0",Rightarrow:"\u21d2",leftrightarrow:"\u2194",leftarrow:"\u2190",gets:"\u2190",rightarrow:"\u2192",to:["\u2192",{accent:!1}],mapsto:"\u21a6",leftharpoonup:"\u21bc",leftharpoondown:"\u21bd",rightharpoonup:"\u21c0",rightharpoondown:"\u21c1",nearrow:"\u2197",searrow:"\u2198",nwarrow:"\u2196",swarrow:"\u2199",rightleftharpoons:"\u21cc",hookrightarrow:"\u21aa",hookleftarrow:"\u21a9",longleftarrow:"\u27f5",Longleftarrow:"\u27f8",longrightarrow:"\u27f6",Longrightarrow:"\u27f9",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",ldots:"\u2026",cdots:"\u22ef",vdots:"\u22ee",ddots:"\u22f1",dotsc:"\u2026",dotsb:"\u22ef",dotsm:"\u22ef",dotsi:"\u22ef",dotso:"\u2026",ldotp:[".",{texClass:s.TEXCLASS.PUNCT}],cdotp:["\u22c5",{texClass:s.TEXCLASS.PUNCT}],colon:[":",{texClass:s.TEXCLASS.PUNCT}]}),new n.CharacterMap("mathchar7",o.default.mathchar7,{Gamma:"\u0393",Delta:"\u0394",Theta:"\u0398",Lambda:"\u039b",Xi:"\u039e",Pi:"\u03a0",Sigma:"\u03a3",Upsilon:"\u03a5",Phi:"\u03a6",Psi:"\u03a8",Omega:"\u03a9",_:"_","#":"#",$:"$","%":"%","&":"&",And:"&"}),new n.DelimiterMap("delimiter",o.default.delimiter,{"(":"(",")":")","[":"[","]":"]","<":"\u27e8",">":"\u27e9","\\lt":"\u27e8","\\gt":"\u27e9","/":"/","|":["|",{texClass:s.TEXCLASS.ORD}],".":"","\\\\":"\\","\\lmoustache":"\u23b0","\\rmoustache":"\u23b1","\\lgroup":"\u27ee","\\rgroup":"\u27ef","\\arrowvert":"\u23d0","\\Arrowvert":"\u2016","\\bracevert":"\u23aa","\\Vert":["\u2225",{texClass:s.TEXCLASS.ORD}],"\\|":["\u2225",{texClass:s.TEXCLASS.ORD}],"\\vert":["|",{texClass:s.TEXCLASS.ORD}],"\\uparrow":"\u2191","\\downarrow":"\u2193","\\updownarrow":"\u2195","\\Uparrow":"\u21d1","\\Downarrow":"\u21d3","\\Updownarrow":"\u21d5","\\backslash":"\\","\\rangle":"\u27e9","\\langle":"\u27e8","\\rbrace":"}","\\lbrace":"{","\\}":"}","\\{":"{","\\rceil":"\u2309","\\lceil":"\u2308","\\rfloor":"\u230b","\\lfloor":"\u230a","\\lbrack":"[","\\rbrack":"]"}),new n.CommandMap("macros",{displaystyle:["SetStyle","D",!0,0],textstyle:["SetStyle","T",!1,0],scriptstyle:["SetStyle","S",!1,1],scriptscriptstyle:["SetStyle","SS",!1,2],rm:["SetFont",a.TexConstant.Variant.NORMAL],mit:["SetFont",a.TexConstant.Variant.ITALIC],oldstyle:["SetFont",a.TexConstant.Variant.OLDSTYLE],cal:["SetFont",a.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",a.TexConstant.Variant.BOLD],bbFont:["SetFont",a.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",a.TexConstant.Variant.SCRIPT],frak:["SetFont",a.TexConstant.Variant.FRAKTUR],sf:["SetFont",a.TexConstant.Variant.SANSSERIF],tt:["SetFont",a.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],arcsin:["NamedFn"],arccos:["NamedFn"],arctan:["NamedFn"],arg:["NamedFn"],cos:["NamedFn"],cosh:["NamedFn"],cot:["NamedFn"],coth:["NamedFn"],csc:["NamedFn"],deg:["NamedFn"],det:"NamedOp",dim:["NamedFn"],exp:["NamedFn"],gcd:"NamedOp",hom:["NamedFn"],inf:"NamedOp",ker:["NamedFn"],lg:["NamedFn"],lim:"NamedOp",liminf:["NamedOp","lim&thinsp;inf"],limsup:["NamedOp","lim&thinsp;sup"],ln:["NamedFn"],log:["NamedFn"],max:"NamedOp",min:"NamedOp",Pr:"NamedOp",sec:["NamedFn"],sin:["NamedFn"],sinh:["NamedFn"],sup:"NamedOp",tan:["NamedFn"],tanh:["NamedFn"],limits:["Limits",1],nolimits:["Limits",0],overline:["UnderOver","00AF",null,1],underline:["UnderOver","005F"],overbrace:["UnderOver","23DE",1],underbrace:["UnderOver","23DF",1],overparen:["UnderOver","23DC"],underparen:["UnderOver","23DD"],overrightarrow:["UnderOver","2192"],underrightarrow:["UnderOver","2192"],overleftarrow:["UnderOver","2190"],underleftarrow:["UnderOver","2190"],overleftrightarrow:["UnderOver","2194"],underleftrightarrow:["UnderOver","2194"],overset:"Overset",underset:"Underset",stackrel:["Macro","\\mathrel{\\mathop{#2}\\limits^{#1}}",2],over:"Over",overwithdelims:"Over",atop:"Over",atopwithdelims:"Over",above:"Over",abovewithdelims:"Over",brace:["Over","{","}"],brack:["Over","[","]"],choose:["Over","(",")"],frac:"Frac",sqrt:"Sqrt",root:"Root",uproot:["MoveRoot","upRoot"],leftroot:["MoveRoot","leftRoot"],left:"LeftRight",right:"LeftRight",middle:"Middle",llap:"Lap",rlap:"Lap",raise:"RaiseLower",lower:"RaiseLower",moveleft:"MoveLeftRight",moveright:"MoveLeftRight",",":["Spacer",a.TexConstant.Length.THINMATHSPACE],":":["Spacer",a.TexConstant.Length.MEDIUMMATHSPACE],">":["Spacer",a.TexConstant.Length.MEDIUMMATHSPACE],";":["Spacer",a.TexConstant.Length.THICKMATHSPACE],"!":["Spacer",a.TexConstant.Length.NEGATIVETHINMATHSPACE],enspace:["Spacer",".5em"],quad:["Spacer","1em"],qquad:["Spacer","2em"],thinspace:["Spacer",a.TexConstant.Length.THINMATHSPACE],negthinspace:["Spacer",a.TexConstant.Length.NEGATIVETHINMATHSPACE],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],big:["MakeBig",s.TEXCLASS.ORD,.85],Big:["MakeBig",s.TEXCLASS.ORD,1.15],bigg:["MakeBig",s.TEXCLASS.ORD,1.45],Bigg:["MakeBig",s.TEXCLASS.ORD,1.75],bigl:["MakeBig",s.TEXCLASS.OPEN,.85],Bigl:["MakeBig",s.TEXCLASS.OPEN,1.15],biggl:["MakeBig",s.TEXCLASS.OPEN,1.45],Biggl:["MakeBig",s.TEXCLASS.OPEN,1.75],bigr:["MakeBig",s.TEXCLASS.CLOSE,.85],Bigr:["MakeBig",s.TEXCLASS.CLOSE,1.15],biggr:["MakeBig",s.TEXCLASS.CLOSE,1.45],Biggr:["MakeBig",s.TEXCLASS.CLOSE,1.75],bigm:["MakeBig",s.TEXCLASS.REL,.85],Bigm:["MakeBig",s.TEXCLASS.REL,1.15],biggm:["MakeBig",s.TEXCLASS.REL,1.45],Biggm:["MakeBig",s.TEXCLASS.REL,1.75],mathord:["TeXAtom",s.TEXCLASS.ORD],mathop:["TeXAtom",s.TEXCLASS.OP],mathopen:["TeXAtom",s.TEXCLASS.OPEN],mathclose:["TeXAtom",s.TEXCLASS.CLOSE],mathbin:["TeXAtom",s.TEXCLASS.BIN],mathrel:["TeXAtom",s.TEXCLASS.REL],mathpunct:["TeXAtom",s.TEXCLASS.PUNCT],mathinner:["TeXAtom",s.TEXCLASS.INNER],vcenter:["TeXAtom",s.TEXCLASS.VCENTER],buildrel:"BuildRel",hbox:["HBox",0],text:"HBox",mbox:["HBox",0],fbox:"FBox",strut:"Strut",mathstrut:["Macro","\\vphantom{(}"],phantom:"Phantom",vphantom:["Phantom",1,0],hphantom:["Phantom",0,1],smash:"Smash",acute:["Accent","00B4"],grave:["Accent","0060"],ddot:["Accent","00A8"],tilde:["Accent","007E"],bar:["Accent","00AF"],breve:["Accent","02D8"],check:["Accent","02C7"],hat:["Accent","005E"],vec:["Accent","2192"],dot:["Accent","02D9"],widetilde:["Accent","007E",1],widehat:["Accent","005E",1],matrix:"Matrix",array:"Matrix",pmatrix:["Matrix","(",")"],cases:["Matrix","{","","left left",null,".1em",null,!0],eqalign:["Matrix",null,null,"right left",a.TexConstant.Length.THICKMATHSPACE,".5em","D"],displaylines:["Matrix",null,null,"center",null,".5em","D"],cr:"Cr","\\":"CrLaTeX",newline:["CrLaTeX",!0],hline:["HLine","solid"],hdashline:["HLine","dashed"],eqalignno:["Matrix",null,null,"right left",a.TexConstant.Length.THICKMATHSPACE,".5em","D",null,"right"],leqalignno:["Matrix",null,null,"right left",a.TexConstant.Length.THICKMATHSPACE,".5em","D",null,"left"],hfill:"HFill",hfil:"HFill",hfilll:"HFill",bmod:["Macro",'\\mmlToken{mo}[lspace="thickmathspace" rspace="thickmathspace"]{mod}'],pmod:["Macro","\\pod{\\mmlToken{mi}{mod}\\kern 6mu #1}",1],mod:["Macro","\\mathchoice{\\kern18mu}{\\kern12mu}{\\kern12mu}{\\kern12mu}\\mmlToken{mi}{mod}\\,\\,#1",1],pod:["Macro","\\mathchoice{\\kern18mu}{\\kern8mu}{\\kern8mu}{\\kern8mu}(#1)",1],iff:["Macro","\\;\\Longleftrightarrow\\;"],skew:["Macro","{{#2{#3\\mkern#1mu}\\mkern-#1mu}{}}",3],mathcal:["Macro","{\\cal #1}",1],mathscr:["Macro","{\\scr #1}",1],mathrm:["Macro","{\\rm #1}",1],mathbf:["Macro","{\\bf #1}",1],mathbb:["Macro","{\\bbFont #1}",1],Bbb:["Macro","{\\bbFont #1}",1],mathit:["Macro","{\\it #1}",1],mathfrak:["Macro","{\\frak #1}",1],mathsf:["Macro","{\\sf #1}",1],mathtt:["Macro","{\\tt #1}",1],textrm:["HBox",null,a.TexConstant.Variant.NORMAL],textit:["HBox",null,a.TexConstant.Variant.ITALIC],textbf:["HBox",null,a.TexConstant.Variant.BOLD],textsf:["HBox",null,a.TexConstant.Variant.SANSSERIF],texttt:["HBox",null,a.TexConstant.Variant.MONOSPACE],pmb:["Macro","\\rlap{#1}\\kern1px{#1}",1],TeX:["Macro","T\\kern-.14em\\lower.5ex{E}\\kern-.115em X"],LaTeX:["Macro","L\\kern-.325em\\raise.21em{\\scriptstyle{A}}\\kern-.17em\\TeX"]," ":["Macro","\\text{ }"],not:"Not",dots:"Dots",space:"Tilde","\xa0":"Tilde",begin:"BeginEnd",end:"BeginEnd",label:"HandleLabel",ref:"HandleRef",nonumber:"HandleNoTag",mathchoice:"MathChoice",mmlToken:"MmlToken"},i.default),new n.EnvironmentMap("environment",o.default.environment,{array:["AlignedArray"],equation:["Equation",null,!0],"equation*":["Equation",null,!1],eqnarray:["EqnArray",null,!0,!0,"rcl","0 "+a.TexConstant.Length.THICKMATHSPACE,".5em"]},i.default),new n.CharacterMap("not_remap",null,{"\u2190":"\u219a","\u2192":"\u219b","\u2194":"\u21ae","\u21d0":"\u21cd","\u21d2":"\u21cf","\u21d4":"\u21ce","\u2208":"\u2209","\u220b":"\u220c","\u2223":"\u2224","\u2225":"\u2226","\u223c":"\u2241","~":"\u2241","\u2243":"\u2244","\u2245":"\u2247","\u2248":"\u2249","\u224d":"\u226d","=":"\u2260","\u2261":"\u2262","<":"\u226e",">":"\u226f","\u2264":"\u2270","\u2265":"\u2271","\u2272":"\u2274","\u2273":"\u2275","\u2276":"\u2278","\u2277":"\u2279","\u227a":"\u2280","\u227b":"\u2281","\u2282":"\u2284","\u2283":"\u2285","\u2286":"\u2288","\u2287":"\u2289","\u22a2":"\u22ac","\u22a8":"\u22ad","\u22a9":"\u22ae","\u22ab":"\u22af","\u227c":"\u22e0","\u227d":"\u22e1","\u2291":"\u22e2","\u2292":"\u22e3","\u22b2":"\u22ea","\u22b3":"\u22eb","\u22b4":"\u22ec","\u22b5":"\u22ed","\u2203":"\u2204"})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(11).add({Pcy:"\u041f",Poincareplane:"\u210c",Pr:"\u2abb",Prime:"\u2033",Proportion:"\u2237",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",pcy:"\u043f",percnt:"%",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",phmmat:"\u2133",phone:"\u260e",pitchfork:"\u22d4",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",pointint:"\u2a15",pound:"\xa3",pr:"\u227a",prE:"\u2ab3",prcue:"\u227c",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",preceq:"\u2aaf",precsim:"\u227e",primes:"\u2119",prnE:"\u2ab5",prnap:"\u2ab9",prnsim:"\u22e8",prod:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",puncsp:"\u2008"},"p")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),r(11).add({RBarr:"\u2910",REG:"\xae",Racute:"\u0154",Rang:"\u27eb",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",ReverseElement:"\u220b",ReverseUpEquilibrium:"\u296f",Rho:"\u03a1",RightArrowBar:"\u21e5",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVectorBar:"\u2955",RightTeeVector:"\u295b",RightTriangleBar:"\u29d0",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVectorBar:"\u2954",RightVectorBar:"\u2953",RoundImplies:"\u2970",RuleDelayed:"\u29f4",rAarr:"\u21db",rArr:"\u21d2",rAtail:"\u291c",rBarr:"\u290f",rHar:"\u2964",race:"\u223d\u0331",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",rarrw:"\u219d",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",rbarr:"\u290d",rbbrk:"\u2773",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",reg:"\xae",rfisht:"\u297d",rfloor:"\u230b",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",rightarrow:"\u2192",rightarrowtail:"\u21a3",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightsquigarrow:"\u219d",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",roplus:"\u2a2e",rotimes:"\u2a35",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",rsaquo:"\u203a",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",ruluhar:"\u2968",rx:"\u211e"},"r")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(17),a=r(2),i=r(7),o=r(16),s=r(3),l=r(5),u=function(t){for(var e=[],r=0,n=t.length;r<n;r++)e[r]=s.default.Em(t[r]);return e.join(" ")};new a.CharacterMap("AMSmath-mathchar0mo",o.default.mathchar0mo,{iiiint:["\u2a0c",{texClass:l.TEXCLASS.OP}]}),new a.CommandMap("AMSmath-macros",{mathring:["Accent","02DA"],nobreakspace:"Tilde",negmedspace:["Spacer",i.TexConstant.Length.NEGATIVEMEDIUMMATHSPACE],negthickspace:["Spacer",i.TexConstant.Length.NEGATIVETHICKMATHSPACE],idotsint:["MultiIntegral","\\int\\cdots\\int"],dddot:["Accent","20DB"],ddddot:["Accent","20DC"],sideset:["Macro","\\mathop{\\mathop{\\rlap{\\phantom{#3}}}\\nolimits#1\\!\\mathop{#3}\\nolimits#2}",3],boxed:["Macro","\\fbox{$\\displaystyle{#1}$}",1],tag:"HandleTag",notag:"HandleNoTag",eqref:["HandleRef",!0],substack:["Macro","\\begin{subarray}{c}#1\\end{subarray}",1],injlim:["NamedOp","inj&thinsp;lim"],projlim:["NamedOp","proj&thinsp;lim"],varliminf:["Macro","\\mathop{\\underline{\\mmlToken{mi}{lim}}}"],varlimsup:["Macro","\\mathop{\\overline{\\mmlToken{mi}{lim}}}"],varinjlim:["Macro","\\mathop{\\underrightarrow{\\mmlToken{mi}{lim}}}"],varprojlim:["Macro","\\mathop{\\underleftarrow{\\mmlToken{mi}{lim}}}"],DeclareMathOperator:"HandleDeclareOp",operatorname:"HandleOperatorName",SkipLimits:"SkipLimits",genfrac:"Genfrac",frac:["Genfrac","","","",""],tfrac:["Genfrac","","","","1"],dfrac:["Genfrac","","","","0"],binom:["Genfrac","(",")","0",""],tbinom:["Genfrac","(",")","0","1"],dbinom:["Genfrac","(",")","0","0"],cfrac:"CFrac",shoveleft:["HandleShove",i.TexConstant.Align.LEFT],shoveright:["HandleShove",i.TexConstant.Align.RIGHT],xrightarrow:["xArrow",8594,5,6],xleftarrow:["xArrow",8592,7,3]},n.AmsMethods),new a.EnvironmentMap("AMSmath-environment",o.default.environment,{"eqnarray*":["EqnArray",null,!1,!0,"rcl","0 "+i.TexConstant.Length.THICKMATHSPACE,".5em"],align:["EqnArray",null,!0,!0,"rlrlrlrlrlrl",u([0,2,0,2,0,2,0,2,0,2,0])],"align*":["EqnArray",null,!1,!0,"rlrlrlrlrlrl",u([0,2,0,2,0,2,0,2,0,2,0])],multline:["Multline",null,!0],"multline*":["Multline",null,!1],split:["EqnArray",null,!1,!1,"rl",u([0])],gather:["EqnArray",null,!0,!0,"c"],"gather*":["EqnArray",null,!1,!0,"c"],alignat:["AlignAt",null,!0,!0],"alignat*":["AlignAt",null,!1,!0],alignedat:["AlignAt",null,!1,!1],aligned:["AmsEqnArray",null,null,null,"rlrlrlrlrlrl",u([0,2,0,2,0,2,0,2,0,2,0]),".5em","D"],gathered:["AmsEqnArray",null,null,null,"c",null,".5em","D"],subarray:["Array",null,null,null,null,u([0]),"0.1em","S",1],smallmatrix:["Array",null,null,null,"c",u([1/3]),".2em","S",1],matrix:["Array",null,null,null,"c"],pmatrix:["Array",null,"(",")","c"],bmatrix:["Array",null,"[","]","c"],Bmatrix:["Array",null,"\\{","\\}","c"],vmatrix:["Array",null,"\\vert","\\vert","c"],Vmatrix:["Array",null,"\\Vert","\\Vert","c"],cases:["Array",null,"\\{",".","ll",null,".2em","T"]},n.AmsMethods),new a.DelimiterMap("AMSmath-delimiter",o.default.delimiter,{"\\lvert":["|",{texClass:l.TEXCLASS.OPEN}],"\\rvert":["|",{texClass:l.TEXCLASS.CLOSE}],"\\lVert":["\u2016",{texClass:l.TEXCLASS.OPEN}],"\\rVert":["\u2016",{texClass:l.TEXCLASS.CLOSE}]}),new a.CharacterMap("AMSsymbols-mathchar0mi",o.default.mathchar0mi,{digamma:"\u03dd",varkappa:"\u03f0",varGamma:["\u0393",{mathvariant:i.TexConstant.Variant.ITALIC}],varDelta:["\u0394",{mathvariant:i.TexConstant.Variant.ITALIC}],varTheta:["\u0398",{mathvariant:i.TexConstant.Variant.ITALIC}],varLambda:["\u039b",{mathvariant:i.TexConstant.Variant.ITALIC}],varXi:["\u039e",{mathvariant:i.TexConstant.Variant.ITALIC}],varPi:["\u03a0",{mathvariant:i.TexConstant.Variant.ITALIC}],varSigma:["\u03a3",{mathvariant:i.TexConstant.Variant.ITALIC}],varUpsilon:["\u03a5",{mathvariant:i.TexConstant.Variant.ITALIC}],varPhi:["\u03a6",{mathvariant:i.TexConstant.Variant.ITALIC}],varPsi:["\u03a8",{mathvariant:i.TexConstant.Variant.ITALIC}],varOmega:["\u03a9",{mathvariant:i.TexConstant.Variant.ITALIC}],beth:"\u2136",gimel:"\u2137",daleth:"\u2138",backprime:["\u2035",{variantForm:!0}],hslash:"\u210f",varnothing:["\u2205",{variantForm:!0}],blacktriangle:"\u25b4",triangledown:["\u25bd",{variantForm:!0}],blacktriangledown:"\u25be",square:"\u25fb",Box:"\u25fb",blacksquare:"\u25fc",lozenge:"\u25ca",Diamond:"\u25ca",blacklozenge:"\u29eb",circledS:["\u24c8",{mathvariant:i.TexConstant.Variant.NORMAL}],bigstar:"\u2605",sphericalangle:"\u2222",measuredangle:"\u2221",nexists:"\u2204",complement:"\u2201",mho:"\u2127",eth:["\xf0",{mathvariant:i.TexConstant.Variant.NORMAL}],Finv:"\u2132",diagup:"\u2571",Game:"\u2141",diagdown:"\u2572",Bbbk:["k",{mathvariant:i.TexConstant.Variant.DOUBLESTRUCK}],yen:"\xa5",circledR:"\xae",checkmark:"\u2713",maltese:"\u2720"}),new a.CharacterMap("AMSsymbols-mathchar0m0",o.default.mathchar0mo,{dotplus:"\u2214",ltimes:"\u22c9",smallsetminus:["\u2216",{variantForm:!0}],rtimes:"\u22ca",Cap:"\u22d2",doublecap:"\u22d2",leftthreetimes:"\u22cb",Cup:"\u22d3",doublecup:"\u22d3",rightthreetimes:"\u22cc",barwedge:"\u22bc",curlywedge:"\u22cf",veebar:"\u22bb",curlyvee:"\u22ce",doublebarwedge:"\u2a5e",boxminus:"\u229f",circleddash:"\u229d",boxtimes:"\u22a0",circledast:"\u229b",boxdot:"\u22a1",circledcirc:"\u229a",boxplus:"\u229e",centerdot:["\u22c5",{variantForm:!0}],divideontimes:"\u22c7",intercal:"\u22ba",leqq:"\u2266",geqq:"\u2267",leqslant:"\u2a7d",geqslant:"\u2a7e",eqslantless:"\u2a95",eqslantgtr:"\u2a96",lesssim:"\u2272",gtrsim:"\u2273",lessapprox:"\u2a85",gtrapprox:"\u2a86",approxeq:"\u224a",lessdot:"\u22d6",gtrdot:"\u22d7",lll:"\u22d8",llless:"\u22d8",ggg:"\u22d9",gggtr:"\u22d9",lessgtr:"\u2276",gtrless:"\u2277",lesseqgtr:"\u22da",gtreqless:"\u22db",lesseqqgtr:"\u2a8b",gtreqqless:"\u2a8c",doteqdot:"\u2251",Doteq:"\u2251",eqcirc:"\u2256",risingdotseq:"\u2253",circeq:"\u2257",fallingdotseq:"\u2252",triangleq:"\u225c",backsim:"\u223d",thicksim:["\u223c",{variantForm:!0}],backsimeq:"\u22cd",thickapprox:["\u2248",{variantForm:!0}],subseteqq:"\u2ac5",supseteqq:"\u2ac6",Subset:"\u22d0",Supset:"\u22d1",sqsubset:"\u228f",sqsupset:"\u2290",preccurlyeq:"\u227c",succcurlyeq:"\u227d",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",precsim:"\u227e",succsim:"\u227f",precapprox:"\u2ab7",succapprox:"\u2ab8",vartriangleleft:"\u22b2",lhd:"\u22b2",vartriangleright:"\u22b3",rhd:"\u22b3",trianglelefteq:"\u22b4",unlhd:"\u22b4",trianglerighteq:"\u22b5",unrhd:"\u22b5",vDash:["\u22a8",{variantForm:!0}],Vdash:"\u22a9",Vvdash:"\u22aa",smallsmile:["\u2323",{variantForm:!0}],shortmid:["\u2223",{variantForm:!0}],smallfrown:["\u2322",{variantForm:!0}],shortparallel:["\u2225",{variantForm:!0}],bumpeq:"\u224f",between:"\u226c",Bumpeq:"\u224e",pitchfork:"\u22d4",varpropto:["\u221d",{variantForm:!0}],backepsilon:"\u220d",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",therefore:"\u2234",because:"\u2235",eqsim:"\u2242",vartriangle:["\u25b3",{variantForm:!0}],Join:"\u22c8",nless:"\u226e",ngtr:"\u226f",nleq:"\u2270",ngeq:"\u2271",nleqslant:["\u2a87",{variantForm:!0}],ngeqslant:["\u2a88",{variantForm:!0}],nleqq:["\u2270",{variantForm:!0}],ngeqq:["\u2271",{variantForm:!0}],lneq:"\u2a87",gneq:"\u2a88",lneqq:"\u2268",gneqq:"\u2269",lvertneqq:["\u2268",{variantForm:!0}],gvertneqq:["\u2269",{variantForm:!0}],lnsim:"\u22e6",gnsim:"\u22e7",lnapprox:"\u2a89",gnapprox:"\u2a8a",nprec:"\u2280",nsucc:"\u2281",npreceq:["\u22e0",{variantForm:!0}],nsucceq:["\u22e1",{variantForm:!0}],precneqq:"\u2ab5",succneqq:"\u2ab6",precnsim:"\u22e8",succnsim:"\u22e9",precnapprox:"\u2ab9",succnapprox:"\u2aba",nsim:"\u2241",ncong:"\u2247",nshortmid:["\u2224",{variantForm:!0}],nshortparallel:["\u2226",{variantForm:!0}],nmid:"\u2224",nparallel:"\u2226",nvdash:"\u22ac",nvDash:"\u22ad",nVdash:"\u22ae",nVDash:"\u22af",ntriangleleft:"\u22ea",ntriangleright:"\u22eb",ntrianglelefteq:"\u22ec",ntrianglerighteq:"\u22ed",nsubseteq:"\u2288",nsupseteq:"\u2289",nsubseteqq:["\u2288",{variantForm:!0}],nsupseteqq:["\u2289",{variantForm:!0}],subsetneq:"\u228a",supsetneq:"\u228b",varsubsetneq:["\u228a",{variantForm:!0}],varsupsetneq:["\u228b",{variantForm:!0}],subsetneqq:"\u2acb",supsetneqq:"\u2acc",varsubsetneqq:["\u2acb",{variantForm:!0}],varsupsetneqq:["\u2acc",{variantForm:!0}],leftleftarrows:"\u21c7",rightrightarrows:"\u21c9",leftrightarrows:"\u21c6",rightleftarrows:"\u21c4",Lleftarrow:"\u21da",Rrightarrow:"\u21db",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",leftarrowtail:"\u21a2",rightarrowtail:"\u21a3",looparrowleft:"\u21ab",looparrowright:"\u21ac",leftrightharpoons:"\u21cb",rightleftharpoons:["\u21cc",{variantForm:!0}],curvearrowleft:"\u21b6",curvearrowright:"\u21b7",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",Lsh:"\u21b0",Rsh:"\u21b1",upuparrows:"\u21c8",downdownarrows:"\u21ca",upharpoonleft:"\u21bf",upharpoonright:"\u21be",downharpoonleft:"\u21c3",restriction:"\u21be",multimap:"\u22b8",downharpoonright:"\u21c2",leftrightsquigarrow:"\u21ad",rightsquigarrow:"\u21dd",leadsto:"\u21dd",dashrightarrow:"\u21e2",dashleftarrow:"\u21e0",nleftarrow:"\u219a",nrightarrow:"\u219b",nLeftarrow:"\u21cd",nRightarrow:"\u21cf",nleftrightarrow:"\u21ae",nLeftrightarrow:"\u21ce"}),new a.DelimiterMap("AMSsymbols-delimiter",o.default.delimiter,{"\\ulcorner":"\u231c","\\urcorner":"\u231d","\\llcorner":"\u231e","\\lrcorner":"\u231f"}),new a.CommandMap("AMSsymbols-macros",{implies:["Macro","\\;\\Longrightarrow\\;"],impliedby:["Macro","\\;\\Longleftarrow\\;"]},n.AmsMethods)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.mathjax=MathJax._.mathjax.mathjax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(18);new(r(2).CommandMap)("Newcommand-macros",{newcommand:"NewCommand",renewcommand:"NewCommand",newenvironment:"NewEnvironment",renewenvironment:"NewEnvironment",def:"MacroDef",let:"Let"},n.default)},function(t,e,r){"use strict";r.r(e);var n=r(21),a=r(20),i=r(4),o=r(23),s=r(22),l=r(12),u=r(27),c=r(1),f=r(16),p=r(25),d=r(3),h=r(24),m=r(13),g=r(26),y=r(10),v=r(2),b=r(9),A=r(7),M=r(0),x=r(8),T=r(37),w=r(29),S=r(17),P=r(38),C=r(28),O=r(14),E=r(15),k=r(39),_=r(40),I=r(32),N=r(18),L=r(19),F=r(41),q=r(30);Object(n.combineWithMathJax)({_:{input:{tex_ts:a,tex:{Configuration:i,FilterUtil:o,FindTeX:s,MapHandler:l,NodeFactory:u,NodeUtil:c,ParseMethods:f,ParseOptions:p,ParseUtil:d,Stack:h,StackItem:m,StackItemFactory:g,Symbol:y,SymbolMap:v,Tags:b,TexConstants:A,TexError:M,TexParser:x,ams:{AmsConfiguration:T,AmsItems:w,AmsMethods:S},autoload:{AutoloadConfiguration:P},base:{BaseConfiguration:C,BaseItems:O,BaseMethods:E},configmacros:{ConfigMacrosConfiguration:k},newcommand:{NewcommandConfiguration:_,NewcommandItems:I,NewcommandMethods:N,NewcommandUtil:L},noundefined:{NoUndefinedConfiguration:F},require:{RequireConfiguration:q}}}}});var R=r(6);r(31).Loader.preLoad("input/tex-base","[tex]/ams","[tex]/newcommand","[tex]/noundefined","[tex]/require","[tex]/autoload","[tex]/configmacros"),function(t){if(MathJax.startup){MathJax.startup.registerConstructor("tex",a.TeX),MathJax.startup.useInput("tex"),MathJax.config.tex||(MathJax.config.tex={});var e=MathJax.config.tex.packages;MathJax.config.tex.packages=t,e&&Object(R.insert)(MathJax.config.tex,{packages:e})}}(["base","ams","newcommand","noundefined","require","autoload","configmacros"])}]);