!function(t){var e={};function r(o){if(e[o])return e[o].exports;var n=e[o]={i:o,l:!1,exports:{}};return t[o].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=t,r.c=e,r.d=function(t,e,o){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(o,n,function(e){return t[e]}.bind(null,n));return o},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=106)}([function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGWrapper=void 0;var a=r(18),l=r(9),h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.element=null,e}return n(e,t),e.prototype.toSVG=function(t){this.addChildren(this.standardSVGnode(t))},e.prototype.addChildren=function(t){var e,r,o=0;try{for(var n=i(this.childNodes),s=n.next();!s.done;s=n.next()){var a=s.value;a.toSVG(t),a.element&&a.place(o+a.bbox.L*a.bbox.rscale,0),o+=(a.bbox.L+a.bbox.w+a.bbox.R)*a.bbox.rscale}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},e.prototype.standardSVGnode=function(t){var e=this.createSVGnode(t);return this.handleStyles(),this.handleScale(),this.handleColor(),this.handleAttributes(),e},e.prototype.createSVGnode=function(t){var e=this.node.attributes.get("href");if(e){t=this.adaptor.append(t,this.svg("a",{href:e}));var r=this.getBBox(),o=r.h,n=r.d,i=r.w;this.adaptor.append(t,this.svg("rect",{"data-hitbox":!0,fill:"none",stroke:"none","pointer-events":"all",width:this.fixed(i),height:this.fixed(o+n),y:this.fixed(-n)}))}return this.element=this.adaptor.append(t,this.svg("g",{"data-mml-node":this.node.kind})),this.element},e.prototype.handleStyles=function(){if(this.styles){var t=this.styles.cssText;t&&this.adaptor.setAttribute(this.element,"style",t)}},e.prototype.handleScale=function(){if(1!==this.bbox.rscale){var t="scale("+this.fixed(this.bbox.rscale/1e3,3)+")";this.adaptor.setAttribute(this.element,"transform",t)}},e.prototype.handleColor=function(){var t=this.adaptor,e=this.node.attributes,r=e.getExplicit("mathcolor"),o=e.getExplicit("color"),n=e.getExplicit("mathbackground"),i=e.getExplicit("background");if((r||o)&&(t.setAttribute(this.element,"fill",r||o),t.setAttribute(this.element,"stroke",r||o)),n||i){var s=this.getBBox(),a=s.h,l=s.d,h=s.w,c=this.svg("rect",{fill:n||i,x:0,y:this.fixed(-l),width:this.fixed(h),height:this.fixed(a+l),"data-bgcolor":!0}),u=t.firstChild(this.element);u?t.insert(c,u):t.append(this.element,c)}},e.prototype.handleAttributes=function(){var t,r,o,n,s=this.node.attributes,a=s.getAllDefaults(),l=e.skipAttributes;try{for(var h=i(s.getExplicitNames()),c=h.next();!c.done;c=h.next()){var u=c.value;!1!==l[u]&&(u in a||l[u]||this.adaptor.hasAttribute(this.element,u))||this.adaptor.setAttribute(this.element,u,s.getExplicit(u))}}catch(e){t={error:e}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(t)throw t.error}}if(s.get("class")){var p=s.get("class").trim().split(/ +/);try{for(var d=i(p),f=d.next();!f.done;f=d.next()){var y=f.value;this.adaptor.addClass(this.element,y)}}catch(t){o={error:t}}finally{try{f&&!f.done&&(n=d.return)&&n.call(d)}finally{if(o)throw o.error}}}},e.prototype.place=function(t,e,r){if(void 0===r&&(r=null),t||e){r||(r=this.element);var o=this.adaptor.getAttribute(r,"transform")||"";o="translate("+this.fixed(t)+", "+this.fixed(e)+")"+(o?" "+o:""),this.adaptor.setAttribute(r,"transform",o)}},e.prototype.placeChar=function(t,e,r,o,n){var a,l;void 0===n&&(n=null),null===n&&(n=this.variant);var h=t.toString(16).toUpperCase(),c=s(this.getVariantChar(n,t),4),u=c[2],p=c[3];if("p"in p){var d=p.p?"M"+p.p+"Z":"";this.place(e,r,this.adaptor.append(o,this.charNode(n,h,d)))}else if("c"in p){var f=this.adaptor.append(o,this.svg("g",{"data-c":h}));this.place(e,r,f),e=0;try{for(var y=i(this.unicodeChars(p.c,n)),m=y.next();!m.done;m=y.next()){var v=m.value;e+=this.placeChar(v,e,r,f,n)}}catch(t){a={error:t}}finally{try{m&&!m.done&&(l=y.return)&&l.call(y)}finally{if(a)throw a.error}}}else if(p.unknown){var b=String.fromCodePoint(t),x=this.adaptor.append(o,this.jax.unknownText(b,n));return this.place(e,r,x),this.jax.measureTextNodeWithCache(x,b,n).w}return u},e.prototype.charNode=function(t,e,r){return"none"!==this.jax.options.fontCache?this.useNode(t,e,r):this.pathNode(e,r)},e.prototype.pathNode=function(t,e){return this.svg("path",{"data-c":t,d:e})},e.prototype.useNode=function(t,e,r){var o=this.svg("use"),n="#"+this.jax.fontCache.cachePath(t,e,r);return this.adaptor.setAttribute(o,"href",n,l.XLINKNS),o},e.prototype.drawBBox=function(){var t=this.getBBox(),e=t.w,r=t.h,o=t.d,n=this.svg("g",{style:{opacity:.25}},[this.svg("rect",{fill:"red",height:this.fixed(r),width:this.fixed(e)}),this.svg("rect",{fill:"green",height:this.fixed(o),width:this.fixed(e),y:this.fixed(-o)})]),i=this.element||this.parent.element;this.adaptor.append(i,n)},e.prototype.html=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.jax.html(t,e,r)},e.prototype.svg=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.jax.svg(t,e,r)},e.prototype.text=function(t){return this.jax.text(t)},e.prototype.fixed=function(t,e){return void 0===e&&(e=1),this.jax.fixed(1e3*t,e)},e.kind="unknown",e}(a.CommonWrapper);e.SVGWrapper=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BBoxStyleAdjust=MathJax._.util.BBox.BBoxStyleAdjust,e.BBox=MathJax._.util.BBox.BBox},function(t,e,r){"use strict";var o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},i=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.FontData=e.NOSTRETCH=e.H=e.V=void 0,e.V=1,e.H=2,e.NOSTRETCH={dir:0};var a=function(){function t(){var t,e;this.variant={},this.delimiters={},this.cssFontMap={},this.remapChars={};var r=this.constructor;this.params=o({},r.defaultParams),this.sizeVariants=i(r.defaultSizeVariants),this.cssFontMap=o({},r.defaultCssFonts),this.cssFamilyPrefix=r.defaultCssFamilyPrefix,this.createVariants(r.defaultVariants),this.defineDelimiters(r.defaultDelimiters);try{for(var n=s(Object.keys(r.defaultChars)),a=n.next();!a.done;a=n.next()){var l=a.value;this.defineChars(l,r.defaultChars[l])}}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}this.defineRemap("accent",r.defaultAccentMap),this.defineRemap("mo",r.defaultMoMap),this.defineRemap("mn",r.defaultMnMap)}return t.charOptions=function(t,e){var r=t[e];return 3===r.length&&(r[3]={}),r[3]},t.prototype.createVariant=function(t,e,r){void 0===e&&(e=null),void 0===r&&(r=null);var o={linked:[],chars:e?Object.create(this.variant[e].chars):{}};r&&this.variant[r]&&(Object.assign(o.chars,this.variant[r].chars),this.variant[r].linked.push(o.chars),o.chars=Object.create(o.chars)),this.remapSmpChars(o.chars,t),this.variant[t]=o},t.prototype.remapSmpChars=function(t,e){var r,o,i,a,l=this.constructor;if(l.VariantSmp[e]){var h=l.SmpRemap,c=[null,null,l.SmpRemapGreekU,l.SmpRemapGreekL];try{for(var u=s(l.SmpRanges),p=u.next();!p.done;p=u.next()){var d=n(p.value,3),f=d[0],y=d[1],m=d[2],v=l.VariantSmp[e][f];if(v){for(var b=y;b<=m;b++)if(930!==b){var x=v+b-y;t[b]=this.smpChar(h[x]||x)}if(c[f])try{for(var g=(i=void 0,s(Object.keys(c[f]).map((function(t){return parseInt(t)})))),_=g.next();!_.done;_=g.next()){t[b=_.value]=this.smpChar(v+c[f][b])}}catch(t){i={error:t}}finally{try{_&&!_.done&&(a=g.return)&&a.call(g)}finally{if(i)throw i.error}}}}}catch(t){r={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}}"bold"===e&&(t[988]=this.smpChar(120778),t[989]=this.smpChar(120779))},t.prototype.smpChar=function(t){return[,,,{smp:t}]},t.prototype.createVariants=function(t){var e,r;try{for(var o=s(t),n=o.next();!n.done;n=o.next()){var i=n.value;this.createVariant(i[0],i[1],i[2])}}catch(t){e={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.prototype.defineChars=function(t,e){var r,o,n=this.variant[t];Object.assign(n.chars,e);try{for(var i=s(n.linked),a=i.next();!a.done;a=i.next()){var l=a.value;Object.assign(l,e)}}catch(t){r={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}},t.prototype.defineDelimiters=function(t){Object.assign(this.delimiters,t)},t.prototype.defineRemap=function(t,e){this.remapChars.hasOwnProperty(t)||(this.remapChars[t]={}),Object.assign(this.remapChars[t],e)},t.prototype.getDelimiter=function(t){return this.delimiters[t]},t.prototype.getSizeVariant=function(t,e){return this.delimiters[t].variants&&(e=this.delimiters[t].variants[e]),this.sizeVariants[e]},t.prototype.getChar=function(t,e){return this.variant[t].chars[e]},t.prototype.getVariant=function(t){return this.variant[t]},t.prototype.getCssFont=function(t){return this.cssFontMap[t]||["serif",!1,!1]},t.prototype.getFamily=function(t){return this.cssFamilyPrefix?this.cssFamilyPrefix+", "+t:t},t.prototype.getRemappedChar=function(t,e){return(this.remapChars[t]||{})[e]},t.OPTIONS={},t.defaultVariants=[["normal"],["bold","normal"],["italic","normal"],["bold-italic","italic","bold"],["double-struck","bold"],["fraktur","normal"],["bold-fraktur","bold","fraktur"],["script","italic"],["bold-script","bold-italic","script"],["sans-serif","normal"],["bold-sans-serif","bold","sans-serif"],["sans-serif-italic","italic","sans-serif"],["sans-serif-bold-italic","bold-italic","bold-sans-serif"],["monospace","normal"]],t.defaultCssFonts={normal:["serif",!1,!1],bold:["serif",!1,!0],italic:["serif",!0,!1],"bold-italic":["serif",!0,!0],"double-struck":["serif",!1,!0],fraktur:["serif",!1,!1],"bold-fraktur":["serif",!1,!0],script:["cursive",!1,!1],"bold-script":["cursive",!1,!0],"sans-serif":["sans-serif",!1,!1],"bold-sans-serif":["sans-serif",!1,!0],"sans-serif-italic":["sans-serif",!0,!1],"sans-serif-bold-italic":["sans-serif",!0,!0],monospace:["monospace",!1,!1]},t.defaultCssFamilyPrefix="",t.VariantSmp={bold:[119808,119834,120488,120514,120782],italic:[119860,119886,120546,120572],"bold-italic":[119912,119938,120604,120630],script:[119964,119990],"bold-script":[120016,120042],fraktur:[120068,120094],"double-struck":[120120,120146,,,120792],"bold-fraktur":[120172,120198],"sans-serif":[120224,120250,,,120802],"bold-sans-serif":[120276,120302,120662,120688,120812],"sans-serif-italic":[120328,120354],"sans-serif-bold-italic":[120380,120406,120720,120746],monospace:[120432,120458,,,120822]},t.SmpRanges=[[0,65,90],[1,97,122],[2,913,937],[3,945,969],[4,48,57]],t.SmpRemap={119893:8462,119965:8492,119968:8496,119969:8497,119971:8459,119972:8464,119975:8466,119976:8499,119981:8475,119994:8495,119996:8458,120004:8500,120070:8493,120075:8460,120076:8465,120085:8476,120093:8488,120122:8450,120127:8461,120133:8469,120135:8473,120136:8474,120137:8477,120145:8484},t.SmpRemapGreekU={8711:25,1012:17},t.SmpRemapGreekL={977:27,981:29,982:31,1008:28,1009:30,1013:26,8706:25},t.defaultAccentMap={768:"\u02cb",769:"\u02ca",770:"\u02c6",771:"\u02dc",772:"\u02c9",774:"\u02d8",775:"\u02d9",776:"\xa8",778:"\u02da",780:"\u02c7",8594:"\u20d7",8242:"'",8243:"''",8244:"'''",8245:"`",8246:"``",8247:"```",8279:"''''",8400:"\u21bc",8401:"\u21c0",8406:"\u2190",8417:"\u2194",8432:"*",8411:"...",8412:"....",8428:"\u21c1",8429:"\u21bd",8430:"\u2190",8431:"\u2192"},t.defaultMoMap={45:"\u2212"},t.defaultMnMap={45:"\u2212"},t.defaultParams={x_height:.442,quad:1,num1:.676,num2:.394,num3:.444,denom1:.686,denom2:.345,sup1:.413,sup2:.363,sup3:.289,sub1:.15,sub2:.247,sup_drop:.386,sub_drop:.05,delim1:2.39,delim2:1,axis_height:.25,rule_thickness:.06,big_op_spacing1:.111,big_op_spacing2:.167,big_op_spacing3:.2,big_op_spacing4:.6,big_op_spacing5:.1,surd_height:.075,scriptspace:.05,nulldelimiterspace:.12,delimiterfactor:901,delimitershortfall:.3,min_rule_thickness:1.25},t.defaultDelimiters={},t.defaultChars={},t.defaultSizeVariants=[],t}();e.FontData=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sortLength=MathJax._.util.string.sortLength,e.quotePattern=MathJax._.util.string.quotePattern,e.unicodeChars=MathJax._.util.string.unicodeChars,e.isPercent=MathJax._.util.string.isPercent,e.split=MathJax._.util.string.split},function(t,e,r){"use strict";var o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonArrow=e.CommonDiagonalArrow=e.CommonDiagonalStrike=e.CommonBorder2=e.CommonBorder=e.arrowBBox=e.diagonalArrowDef=e.arrowDef=e.arrowBBoxW=e.arrowBBoxHD=e.arrowHead=e.fullBorder=e.fullPadding=e.fullBBox=e.sideNames=e.sideIndex=e.SOLID=e.PADDING=e.THICKNESS=e.ARROWY=e.ARROWDX=e.ARROWX=void 0,e.ARROWX=4,e.ARROWDX=1,e.ARROWY=2,e.THICKNESS=.067,e.PADDING=.2,e.SOLID=e.THICKNESS+"em solid",e.sideIndex={top:0,right:1,bottom:2,left:3},e.sideNames=Object.keys(e.sideIndex),e.fullBBox=function(t){return new Array(4).fill(t.thickness+t.padding)},e.fullPadding=function(t){return new Array(4).fill(t.padding)},e.fullBorder=function(t){return new Array(4).fill(t.thickness)},e.arrowHead=function(t){return Math.max(t.padding,t.thickness*(t.arrowhead.x+t.arrowhead.dx+1))},e.arrowBBoxHD=function(t,e){if(t.childNodes[0]){var r=t.childNodes[0].getBBox(),o=r.h,n=r.d;e[0]=e[2]=Math.max(0,t.thickness*t.arrowhead.y-(o+n)/2)}return e},e.arrowBBoxW=function(t,e){if(t.childNodes[0]){var r=t.childNodes[0].getBBox().w;e[1]=e[3]=Math.max(0,t.thickness*t.arrowhead.y-r/2)}return e},e.arrowDef={up:[-Math.PI/2,!1,!0,"verticalstrike"],down:[Math.PI/2,!1,!0,"verticakstrike"],right:[0,!1,!1,"horizontalstrike"],left:[Math.PI,!1,!1,"horizontalstrike"],updown:[Math.PI/2,!0,!0,"verticalstrike uparrow downarrow"],leftright:[0,!0,!1,"horizontalstrike leftarrow rightarrow"]},e.diagonalArrowDef={updiagonal:[-1,0,!1,"updiagonalstrike northeastarrow"],northeast:[-1,0,!1,"updiagonalstrike updiagonalarrow"],southeast:[1,0,!1,"downdiagonalstrike"],northwest:[1,Math.PI,!1,"downdiagonalstrike"],southwest:[-1,Math.PI,!1,"updiagonalstrike"],northeastsouthwest:[-1,0,!0,"updiagonalstrike northeastarrow updiagonalarrow southwestarrow"],northwestsoutheast:[1,0,!0,"downdiagonalstrike northwestarrow southeastarrow"]},e.arrowBBox={up:function(t){return e.arrowBBoxW(t,[e.arrowHead(t),0,t.padding,0])},down:function(t){return e.arrowBBoxW(t,[t.padding,0,e.arrowHead(t),0])},right:function(t){return e.arrowBBoxHD(t,[0,e.arrowHead(t),0,t.padding])},left:function(t){return e.arrowBBoxHD(t,[0,t.padding,0,e.arrowHead(t)])},updown:function(t){return e.arrowBBoxW(t,[e.arrowHead(t),0,e.arrowHead(t),0])},leftright:function(t){return e.arrowBBoxHD(t,[0,e.arrowHead(t),0,e.arrowHead(t)])}},e.CommonBorder=function(t){return function(r){var o=e.sideIndex[r];return[r,{renderer:t,bbox:function(t){var e=[0,0,0,0];return e[o]=t.thickness+t.padding,e},border:function(t){var e=[0,0,0,0];return e[o]=t.thickness,e}}]}},e.CommonBorder2=function(t){return function(r,o,n){var i=e.sideIndex[o],s=e.sideIndex[n];return[r,{renderer:t,bbox:function(t){var e=t.thickness+t.padding,r=[0,0,0,0];return r[i]=r[s]=e,r},border:function(t){var e=[0,0,0,0];return e[i]=e[s]=t.thickness,e},remove:o+" "+n}]}},e.CommonDiagonalStrike=function(t){return function(r){var o="mjx-"+r.charAt(0)+"strike";return[r+"diagonalstrike",{renderer:t(o),bbox:e.fullBBox}]}},e.CommonDiagonalArrow=function(t){return function(r){var n=o(e.diagonalArrowDef[r],4),i=n[0],s=n[1],a=n[2];return[r+"arrow",{renderer:function(e,r){var o=e.arrowData(),n=o.a,l=o.W,h=e.arrow(l,i*(n-s),a);t(e,h)},bbox:function(t){var e=t.arrowData(),r=e.a,n=e.x,i=e.y,s=o([t.arrowhead.x,t.arrowhead.y,t.arrowhead.dx],3),a=s[0],l=s[1],h=s[2],c=o(t.getArgMod(a+h,l),2),u=c[0],p=c[1],d=i+(u>r?t.thickness*p*Math.sin(u-r):0),f=n+(u>Math.PI/2-r?t.thickness*p*Math.sin(u+r-Math.PI/2):0);return[d,f,d,f]},remove:n[3]}]}},e.CommonArrow=function(t){return function(r){var n=o(e.arrowDef[r],4),i=n[0],s=n[1],a=n[2],l=n[3];return[r+"arrow",{renderer:function(e,r){var o=e.getBBox(),n=o.w,l=o.h,h=o.d,c=a?l+h:n,u=e.arrow(c,i,s);t(e,u)},bbox:e.arrowBBox[r],remove:l}]}}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsubsupMixin=e.CommonMsupMixin=e.CommonMsubMixin=void 0,e.CommonMsubMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.sub]},enumerable:!1,configurable:!0}),e.prototype.getOffset=function(t,e){return[0,-this.getV(t,e)]},e}(t)},e.CommonMsupMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.sup]},enumerable:!1,configurable:!0}),e.prototype.getOffset=function(t,e){return[(this.baseCore.bbox.ic?.05*this.baseCore.bbox.ic+.05:0)*this.coreScale(),this.getU(t,e)]},e}(t)},e.CommonMsubsupMixin=function(t){return function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.UVQ=null,e}return n(e,t),Object.defineProperty(e.prototype,"subChild",{get:function(){return this.childNodes[this.node.sub]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"supChild",{get:function(){return this.childNodes[this.node.sup]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.baseChild.getBBox(),o=this.subChild.getBBox(),n=this.supChild.getBBox();t.empty(),t.append(r);var s=t.w,a=i(this.getUVQ(r,o,n),2),l=a[0],h=a[1],c=this.baseCore.bbox.ic?this.coreIC()*this.coreScale():0;t.combine(o,s,h),t.combine(n,s+c,l),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(e)},e.prototype.getUVQ=function(t,e,r){if(this.UVQ)return this.UVQ;var o=this.font.params,n=3*o.rule_thickness,s=this.length2em(this.node.attributes.get("subscriptshift"),o.sub2),a=this.isCharBase()?0:t.d*t.rscale+o.sub_drop*e.rscale,l=i([this.getU(t,r),Math.max(a,s)],2),h=l[0],c=l[1],u=h-r.d*r.rscale-(e.h*e.rscale-c);if(u<n){c+=n-u;var p=.8*o.x_height-(h-r.d*r.rscale);p>0&&(h+=p,c-=p)}return h=Math.max(this.length2em(this.node.attributes.get("superscriptshift"),h),h),c=Math.max(this.length2em(this.node.attributes.get("subscriptshift"),c),c),u=h-r.d*r.rscale-(e.h*e.rscale-c),this.UVQ=[h,-c,u],this.UVQ},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMunderoverMixin=e.CommonMoverMixin=e.CommonMunderMixin=void 0,e.CommonMunderMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.stretchChildren(),o}return n(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.under]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(e,r){if(void 0===r&&(r=!1),this.hasMovableLimits())t.prototype.computeBBox.call(this,e,r);else{e.empty();var o=this.baseChild.getBBox(),n=this.script.getBBox(),s=this.getUnderKV(o,n)[1],a=this.getDelta(!0),l=i(this.getDeltaW([o,n],[0,-a]),2),h=l[0],c=l[1];e.combine(o,h,0),e.combine(n,c,s),e.d+=this.font.params.big_op_spacing5,e.ic=-this.baseCore.bbox.ic,e.clean(),this.setChildPWidths(r)}},e}(t)},e.CommonMoverMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.stretchChildren(),o}return n(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.over]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(e){if(this.hasMovableLimits())t.prototype.computeBBox.call(this,e);else{e.empty();var r=this.baseChild.getBBox(),o=this.script.getBBox(),n=this.getOverKU(r,o)[1],s=this.getDelta(),a=i(this.getDeltaW([r,o],[0,s]),2),l=a[0],h=a[1];e.combine(r,l,0),e.combine(o,h,n),e.h+=this.font.params.big_op_spacing5,e.ic=-this.baseCore.bbox.ic,e.clean()}},e}(t)},e.CommonMunderoverMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.stretchChildren(),o}return n(e,t),Object.defineProperty(e.prototype,"underChild",{get:function(){return this.childNodes[this.node.under]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overChild",{get:function(){return this.childNodes[this.node.over]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subChild",{get:function(){return this.underChild},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"supChild",{get:function(){return this.overChild},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(e){if(this.hasMovableLimits())t.prototype.computeBBox.call(this,e);else{e.empty();var r=this.overChild.getBBox(),o=this.baseChild.getBBox(),n=this.underChild.getBBox(),s=this.getOverKU(o,r)[1],a=this.getUnderKV(o,n)[1],l=this.getDelta(),h=i(this.getDeltaW([o,n,r],[0,-l,l]),3),c=h[0],u=h[1],p=h[2];e.combine(o,c,0),e.combine(r,p,s),e.combine(n,u,a);var d=this.font.params.big_op_spacing5;e.h+=d,e.d+=d,e.ic=-this.baseCore.bbox.ic,e.clean()}},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmsubsup=e.SVGmsup=e.SVGmsub=void 0;var s=r(57),a=r(6),l=r(6),h=r(6),c=r(94),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=c.MmlMsub.prototype.kind,e.useIC=!1,e}(a.CommonMsubMixin(s.SVGscriptbase));e.SVGmsub=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=c.MmlMsup.prototype.kind,e.useIC=!0,e}(l.CommonMsupMixin(s.SVGscriptbase));e.SVGmsup=p;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=i([this.baseChild,this.supChild,this.subChild],3),o=r[0],n=r[1],s=r[2],a=o.getBBox(),l=i(this.getUVQ(a,s.getBBox(),n.getBBox()),2),h=l[0],c=l[1],u=this.baseCore.bbox.ic?this.coreIC()*this.coreScale():0;o.toSVG(e),n.toSVG(e),s.toSVG(e),s.place(a.w*a.rscale,c),n.place(a.w*a.rscale+u,h)},e.kind=c.MmlMsubsup.prototype.kind,e.useIC=!1,e}(h.CommonMsubsupMixin(s.SVGscriptbase));e.SVGmsubsup=d},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVG=e.XLINKNS=e.SVGNS=void 0;var a=r(17),l=r(41),h=r(105),c=r(70),u=r(4),p=r(16);e.SVGNS="http://www.w3.org/2000/svg",e.XLINKNS="http://www.w3.org/1999/xlink";var d=function(t){function r(e){void 0===e&&(e=null);var r=t.call(this,e,l.SVGWrapperFactory,h.TeXFont)||this;return r.minwidth=0,r.shift=0,r.container=null,r.svgStyles=null,r.fontCache=new c.FontCache(r),r}return n(r,t),r.prototype.initialize=function(){"global"===this.options.fontCache&&this.fontCache.clearCache()},r.prototype.clearFontCache=function(){this.fontCache.clearCache()},r.prototype.setScale=function(t){1!==this.options.scale&&this.adaptor.setStyle(t,"fontSize",p.percent(this.options.scale))},r.prototype.escaped=function(t,e){return this.setDocument(e),this.html("span",{},[this.text(t.math)])},r.prototype.styleSheet=function(e){if(this.svgStyles)return null;var o=this.svgStyles=t.prototype.styleSheet.call(this,e);return this.adaptor.setAttribute(o,"id",r.STYLESHEETID),o},r.prototype.pageElements=function(t){return"global"!==this.options.fontCache||this.findCache(t)?null:this.svg("svg",{id:r.FONTCACHEID,style:{display:"none"}},[this.fontCache.getCache()])},r.prototype.findCache=function(t){for(var e=this.adaptor,o=e.tags(e.body(t.document),"svg"),n=o.length-1;n>=0;n--)if(this.adaptor.getAttribute(o[n],"id")===r.FONTCACHEID)return!0;return!1},r.prototype.processMath=function(t,e){var r=this.container;this.container=e;var o=this.factory.wrap(t),n=s(this.createRoot(o),2),i=n[0],a=n[1];this.typesetSVG(o,i,a),this.container=r},r.prototype.createRoot=function(t){var r=t.getBBox(),o=r.w,n=r.h,i=r.d,s=r.pwidth,a=Math.max(o,.001),l=this.svg("g",{stroke:"currentColor",fill:"currentColor","stroke-width":0,transform:"matrix(1 0 0 -1 0 0)"}),h=this.adaptor,c=h.append(this.container,this.svg("svg",{xmlns:e.SVGNS,width:this.ex(a),height:this.ex(n+i),role:"img",focusable:!1,style:{"vertical-align":this.ex(-i)},viewBox:[0,this.fixed(1e3*-n,1),this.fixed(1e3*a,1),this.fixed(1e3*(n+i),1)].join(" ")},[l]));if(.001===a&&(h.setAttribute(c,"preserveAspectRatio","xMidYMid slice"),o<0&&h.setStyle(this.container,"margin-right",this.ex(o))),s){h.setStyle(c,"min-width",this.ex(a)),h.setAttribute(c,"width",s),h.removeAttribute(c,"viewBox");var u=t.metrics.ex/(1e3*this.font.params.x_height);h.setAttribute(l,"transform","matrix(1 0 0 -1 0 0) scale("+this.fixed(u,6)+") translate(0, "+this.fixed(1e3*-n,1)+")")}return"none"!==this.options.fontCache&&h.setAttribute(c,"xmlns:xlink",e.XLINKNS),[c,l]},r.prototype.typesetSVG=function(t,e,r){var o=this.adaptor;if(this.minwidth=this.shift=0,"local"===this.options.fontCache&&(this.fontCache.clearCache(),this.fontCache.useLocalID(this.options.localID),o.insert(this.fontCache.getCache(),r)),t.toSVG(r),this.fontCache.clearLocalID(),this.minwidth)o.setStyle(e,"minWidth",this.ex(this.minwidth)),o.setStyle(this.container,"minWidth",this.ex(this.minwidth));else if(this.shift){var n=o.getAttribute(this.container,"justify")||"center";this.setIndent(e,n,this.shift)}},r.prototype.setIndent=function(t,e,r){"center"!==e&&"left"!==e||this.adaptor.setStyle(t,"margin-left",this.ex(r)),"center"!==e&&"right"!==e||this.adaptor.setStyle(t,"margin-right",this.ex(-r))},r.prototype.ex=function(t){return t/=this.font.params.x_height,Math.abs(t)<.001?"0":t.toFixed(3).replace(/\.?0+$/,"")+"ex"},r.prototype.svg=function(t,r,o){return void 0===r&&(r={}),void 0===o&&(o=[]),this.html(t,r,o,e.SVGNS)},r.prototype.unknownText=function(t,e){var r=this.math.metrics,o=this.font.params.x_height/r.ex*r.em*1e3,n=this.svg("text",{"data-variant":e,transform:"matrix(1 0 0 -1 0 0)","font-size":this.fixed(o,1)+"px"},[this.text(t)]),i=this.adaptor;if("-explicitFont"!==e){var a=u.unicodeChars(t);if(1!==a.length||a[0]<119808||a[0]>120831){var l=s(this.font.getCssFont(e),3),h=l[0],c=l[1],p=l[2];i.setAttribute(n,"font-family",h),c&&i.setAttribute(n,"font-style","italic"),p&&i.setAttribute(n,"font-weight","bold")}}return n},r.prototype.measureTextNode=function(t){var e=this.adaptor;t=e.clone(t),e.removeAttribute(t,"transform");var r=this.fixed(1e3*this.font.params.x_height,1),o=this.svg("svg",{position:"absolute",visibility:"hidden",width:"1ex",height:"1ex",viewBox:[0,0,r,r].join(" ")},[t]);e.append(e.body(e.document),o);var n=e.nodeSize(t,1e3,!0)[0];return e.remove(o),{w:n,h:.75,d:.2}},r.NAME="SVG",r.OPTIONS=i(i({},a.CommonOutputJax.OPTIONS),{internalSpeechTitles:!0,titleID:0,fontCache:"local",localID:null}),r.commonStyles={'mjx-container[jax="SVG"]':{direction:"ltr"},'mjx-container[jax="SVG"] > svg':{overflow:"visible"},'mjx-container[jax="SVG"] > svg a':{fill:"blue",stroke:"blue"}},r.FONTCACHEID="MJX-SVG-global-cache",r.STYLESHEETID="MJX-SVG-styles",r}(a.CommonOutputJax);e.SVG=d},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMactionMixin=e.TooltipData=void 0;var a=r(4);e.TooltipData={dx:".2em",dy:".1em",postDelay:600,clearDelay:100,hoverTimer:new Map,clearTimer:new Map,stopTimers:function(t,e){e.clearTimer.has(t)&&(clearTimeout(e.clearTimer.get(t)),e.clearTimer.delete(t)),e.hoverTimer.has(t)&&(clearTimeout(e.hoverTimer.get(t)),e.hoverTimer.delete(t))}},e.CommonMactionMixin=function(t){return function(t){function r(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this,n=o.constructor.actions,a=o.node.attributes.get("actiontype"),l=i(n.get(a)||[function(t,e){},{}],2),h=l[0],c=l[1];return o.action=h,o.data=c,o.getParameters(),o}return n(r,t),Object.defineProperty(r.prototype,"selected",{get:function(){var t=this.node.attributes.get("selection"),e=Math.max(1,Math.min(this.childNodes.length,t))-1;return this.childNodes[e]||this.wrap(this.node.selected)},enumerable:!1,configurable:!0}),r.prototype.getParameters=function(){var t=this.node.attributes.get("data-offsets"),r=i(a.split(t||""),2),o=r[0],n=r[1];this.dx=this.length2em(o||e.TooltipData.dx),this.dy=this.length2em(n||e.TooltipData.dy)},r.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.updateFrom(this.selected.getBBox()),this.selected.setChildPWidths(e)},r}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonInferredMrowMixin=e.CommonMrowMixin=void 0;var l=r(1);e.CommonMrowMixin=function(t){return function(t){function e(){for(var e,r,o=[],n=0;n<arguments.length;n++)o[n]=arguments[n];var i=t.apply(this,s(o))||this;i.stretchChildren();try{for(var h=a(i.childNodes),c=h.next();!c.done;c=h.next()){var u=c.value;if(u.bbox.pwidth){i.bbox.pwidth=l.BBox.fullWidth;break}}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}return i}return n(e,t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.stretchChildren=function(){var t,e,r,o,n,i,s=[];try{for(var l=a(this.childNodes),h=l.next();!h.done;h=l.next()){(S=h.value).canStretch(1)&&s.push(S)}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}var c=s.length,u=this.childNodes.length;if(c&&u>1){var p=0,d=0,f=c>1&&c===u;try{for(var y=a(this.childNodes),m=y.next();!m.done;m=y.next()){var v=0===(S=m.value).stretch.dir;if(f||v){var b=S.getBBox(v),x=b.h,g=b.d;x>p&&(p=x),g>d&&(d=g)}}}catch(t){r={error:t}}finally{try{m&&!m.done&&(o=y.return)&&o.call(y)}finally{if(r)throw r.error}}try{for(var _=a(s),M=_.next();!M.done;M=_.next()){var S;(S=M.value).coreMO().getStretchedVariant([p,d])}}catch(t){n={error:t}}finally{try{M&&!M.done&&(i=_.return)&&i.call(_)}finally{if(n)throw n.error}}}},e}(t)},e.CommonInferredMrowMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getScale=function(){this.bbox.scale=this.parent.bbox.scale,this.bbox.rscale=1},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMlabeledtrMixin=e.CommonMtrMixin=void 0,e.CommonMtrMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"numCells",{get:function(){return this.childNodes.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"labeled",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableCells",{get:function(){return this.childNodes},enumerable:!1,configurable:!0}),e.prototype.getChild=function(t){return this.childNodes[t]},e.prototype.getChildBBoxes=function(){return this.childNodes.map((function(t){return t.getBBox()}))},e.prototype.stretchChildren=function(t){var e,r,o,n,s,a;void 0===t&&(t=null);var l=[],h=this.labeled?this.childNodes.slice(1):this.childNodes;try{for(var c=i(h),u=c.next();!u.done;u=c.next()){(O=u.value.childNodes[0]).canStretch(1)&&l.push(O)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}var p=l.length,d=this.childNodes.length;if(p&&d>1){if(null===t){var f=0,y=0,m=p>1&&p===d;try{for(var v=i(h),b=v.next();!b.done;b=v.next()){var x=0===(O=b.value.childNodes[0]).stretch.dir;if(m||x){var g=O.getBBox(x),_=g.h,M=g.d;_>f&&(f=_),M>y&&(y=M)}}}catch(t){o={error:t}}finally{try{b&&!b.done&&(n=v.return)&&n.call(v)}finally{if(o)throw o.error}}t=[f,y]}try{for(var S=i(l),w=S.next();!w.done;w=S.next()){var O;(O=w.value).coreMO().getStretchedVariant(t)}}catch(t){s={error:t}}finally{try{w&&!w.done&&(a=S.return)&&a.call(S)}finally{if(s)throw s.error}}}},e}(t)},e.CommonMlabeledtrMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),Object.defineProperty(e.prototype,"numCells",{get:function(){return Math.max(0,this.childNodes.length-1)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"labeled",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableCells",{get:function(){return this.childNodes.slice(1)},enumerable:!1,configurable:!0}),e.prototype.getChild=function(t){return this.childNodes[t+1]},e.prototype.getChildBBoxes=function(){return this.childNodes.slice(1).map((function(t){return t.getBBox()}))},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmsqrt=void 0;var i=r(0),s=r(35),a=r(91),l=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.dx=0,e}return n(e,t),e.prototype.toSVG=function(t){var e=this.childNodes[this.surd],r=this.childNodes[this.base],o=this.root?this.childNodes[this.root]:null,n=this.getBBox(),i=e.getBBox(),s=r.getBBox(),a=this.getPQ(i)[1],l=this.font.params.rule_thickness*this.bbox.scale,h=s.h+a+l,c=this.standardSVGnode(t),u=this.adaptor.append(c,this.svg("g"));this.addRoot(c,o,i,h),e.toSVG(c),e.place(this.dx,n.h-i.h-l),r.toSVG(u),r.place(this.dx+i.w,0),this.adaptor.append(c,this.svg("rect",{width:this.fixed(s.w),height:this.fixed(l),x:this.fixed(this.dx+i.w),y:this.fixed(n.h-2*l)}))},e.prototype.addRoot=function(t,e,r,o){},e.kind=a.MmlMsqrt.prototype.kind,e}(s.CommonMsqrtMixin(i.SVGWrapper));e.SVGmsqrt=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__createBinding||(Object.create?function(t,e,r,o){void 0===o&&(o=r),Object.defineProperty(t,o,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,o){void 0===o&&(o=r),t[o]=e[r]}),s=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||i(e,t,r)},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.AddPaths=e.SVGFontData=void 0;var l=r(2);s(r(2),e);var h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.charOptions=function(e,r){return t.charOptions.call(this,e,r)},e}(l.FontData);e.SVGFontData=h,e.AddPaths=function(t,e,r){var o,n,i,s;try{for(var l=a(Object.keys(e)),c=l.next();!c.done;c=l.next()){var u=c.value,p=parseInt(u);h.charOptions(t,p).p=e[p]}}catch(t){o={error:t}}finally{try{c&&!c.done&&(n=l.return)&&n.call(l)}finally{if(o)throw o.error}}try{for(var d=a(Object.keys(r)),f=d.next();!f.done;f=d.next()){u=f.value,p=parseInt(u);h.charOptions(t,p).c=r[p]}}catch(t){i={error:t}}finally{try{f&&!f.done&&(s=d.return)&&s.call(d)}finally{if(i)throw i.error}}return t}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BIGDIMEN=MathJax._.util.lengths.BIGDIMEN,e.UNITS=MathJax._.util.lengths.UNITS,e.RELUNITS=MathJax._.util.lengths.RELUNITS,e.MATHSPACE=MathJax._.util.lengths.MATHSPACE,e.length2em=MathJax._.util.lengths.length2em,e.percent=MathJax._.util.lengths.percent,e.em=MathJax._.util.lengths.em,e.emRounded=MathJax._.util.lengths.emRounded,e.px=MathJax._.util.lengths.px},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonOutputJax=void 0;var l=r(72),h=r(73),c=r(74),u=r(16),p=r(71),d=r(75),f=function(t){function e(e,r,o){void 0===e&&(e=null),void 0===r&&(r=null),void 0===o&&(o=null);var n=this,i=s(c.separateOptions(e,o.OPTIONS),2),a=i[0],l=i[1];return(n=t.call(this,a)||this).factory=n.options.wrapperFactory||new r,n.factory.jax=n,n.cssStyles=n.options.cssStyles||new d.CssStyles,n.font=n.options.font||new o(l),n.unknownCache=new Map,n}return n(e,t),e.prototype.typeset=function(t,e){this.setDocument(e);var r=this.createNode();return this.toDOM(t,r,e),r},e.prototype.createNode=function(){var t=this.constructor.NAME;return this.html("mjx-container",{class:"MathJax",jax:t})},e.prototype.setScale=function(t){var e=this.math.metrics.scale*this.options.scale;1!==e&&this.adaptor.setStyle(t,"fontSize",u.percent(e))},e.prototype.toDOM=function(t,e,r){void 0===r&&(r=null),this.setDocument(r),this.math=t,this.pxPerEm=t.metrics.ex/this.font.params.x_height,t.root.setTeXclass(null),this.setScale(e),this.nodeMap=new Map,this.container=e,this.processMath(t.root,e),this.nodeMap=null,this.executeFilters(this.postFilters,t,r,e)},e.prototype.getBBox=function(t,e){this.setDocument(e),this.math=t,t.root.setTeXclass(null),this.nodeMap=new Map;var r=this.factory.wrap(t.root).getBBox();return this.nodeMap=null,r},e.prototype.getMetrics=function(t){var e,r;this.setDocument(t);var o=this.adaptor,n=this.getMetricMaps(t);try{for(var i=a(t.math),s=i.next();!s.done;s=i.next()){var l=s.value,c=o.parent(l.start.node);if(l.state()<h.STATE.METRICS&&c){var u=n[l.display?1:0].get(c),p=u.em,d=u.ex,f=u.containerWidth,y=u.lineWidth,m=u.scale,v=u.family;l.setMetrics(p,d,f,y,m),this.options.mtextInheritFont&&(l.outputData.mtextFamily=v),this.options.merrorInheritFont&&(l.outputData.merrorFamily=v),l.state(h.STATE.METRICS)}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},e.prototype.getMetricsFor=function(t,e){var r=this.options.mtextInheritFont||this.options.merrorInheritFont,o=this.getTestElement(t,e),n=this.measureMetrics(o,r);return this.adaptor.remove(o),n},e.prototype.getMetricMaps=function(t){var e,r,o,n,i,s,l,c,u,p,d=this.adaptor,f=[new Map,new Map];try{for(var y=a(t.math),m=y.next();!m.done;m=y.next()){var v=m.value;if((C=d.parent(v.start.node))&&v.state()<h.STATE.METRICS){var b=f[v.display?1:0];b.has(C)||b.set(C,this.getTestElement(C,v.display))}}}catch(t){e={error:t}}finally{try{m&&!m.done&&(r=y.return)&&r.call(y)}finally{if(e)throw e.error}}var x=this.options.mtextInheritFont||this.options.merrorInheritFont,g=[new Map,new Map];try{for(var _=a(g.keys()),M=_.next();!M.done;M=_.next()){var S=M.value;try{for(var w=(i=void 0,a(f[S].keys())),O=w.next();!O.done;O=w.next()){var C=O.value;g[S].set(C,this.measureMetrics(f[S].get(C),x))}}catch(t){i={error:t}}finally{try{O&&!O.done&&(s=w.return)&&s.call(w)}finally{if(i)throw i.error}}}}catch(t){o={error:t}}finally{try{M&&!M.done&&(n=_.return)&&n.call(_)}finally{if(o)throw o.error}}try{for(var B=a(g.keys()),j=B.next();!j.done;j=B.next()){S=j.value;try{for(var P=(u=void 0,a(f[S].values())),V=P.next();!V.done;V=P.next()){C=V.value;d.remove(C)}}catch(t){u={error:t}}finally{try{V&&!V.done&&(p=P.return)&&p.call(P)}finally{if(u)throw u.error}}}}catch(t){l={error:t}}finally{try{j&&!j.done&&(c=B.return)&&c.call(B)}finally{if(l)throw l.error}}return g},e.prototype.getTestElement=function(t,e){var r=this.adaptor;if(!this.testInline){this.testInline=this.html("mjx-test",{style:{display:"inline-block",width:"100%","font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","text-indent":0,"text-transform":"none","letter-spacing":"normal","word-spacing":"normal",overflow:"hidden",height:"1px","margin-right":"-1px"}},[this.html("mjx-left-box",{style:{display:"inline-block",width:0,float:"left"}}),this.html("mjx-ex-box",{style:{position:"absolute",overflow:"hidden",width:"1px",height:"60ex"}}),this.html("mjx-right-box",{style:{display:"inline-block",width:0,float:"right"}})]),this.testDisplay=r.clone(this.testInline),r.setStyle(this.testDisplay,"display","table"),r.setStyle(this.testDisplay,"margin-right",""),r.setStyle(r.firstChild(this.testDisplay),"display","none");var o=r.lastChild(this.testDisplay);r.setStyle(o,"display","table-cell"),r.setStyle(o,"width","10000em"),r.setStyle(o,"float","")}return r.append(t,r.clone(e?this.testDisplay:this.testInline))},e.prototype.measureMetrics=function(t,e){var r=this.adaptor,o=e?r.fontFamily(t):"",n=r.fontSize(t),i=r.nodeSize(r.childNode(t,1))[1]/60||n*this.options.exFactor;return{em:n,ex:i,containerWidth:"table"===r.getStyle(t,"display")?r.nodeSize(r.lastChild(t))[0]-1:r.nodeBBox(r.lastChild(t)).left-r.nodeBBox(r.firstChild(t)).left-2,lineWidth:1e6,scale:Math.max(this.options.minScale,this.options.matchFontHeight?i/this.font.params.x_height/n:1),family:o}},e.prototype.styleSheet=function(t){var e,r,o,n;if(this.setDocument(t),this.cssStyles.clear(),this.cssStyles.addStyles(this.constructor.commonStyles),"getStyles"in t)try{for(var i=a(t.getStyles()),s=i.next();!s.done;s=i.next()){var l=s.value;this.cssStyles.addStyles(l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}try{for(var h=a(this.factory.getKinds()),c=h.next();!c.done;c=h.next()){var u=c.value;this.addClassStyles(this.factory.getNodeClass(u))}}catch(t){o={error:t}}finally{try{c&&!c.done&&(n=h.return)&&n.call(h)}finally{if(o)throw o.error}}return this.cssStyles.addStyles(this.font.styles),this.html("style",{id:"MJX-styles"},[this.text("\n"+this.cssStyles.cssText+"\n")])},e.prototype.addClassStyles=function(t){this.cssStyles.addStyles(t.styles)},e.prototype.setDocument=function(t){t&&(this.document=t,this.adaptor.document=t.document)},e.prototype.html=function(t,e,r,o){return void 0===e&&(e={}),void 0===r&&(r=[]),this.adaptor.node(t,e,r,o)},e.prototype.text=function(t){return this.adaptor.text(t)},e.prototype.fixed=function(t,e){return void 0===e&&(e=3),Math.abs(t)<6e-4?"0":t.toFixed(e).replace(/\.?0+$/,"")},e.prototype.measureText=function(t,e,r){void 0===r&&(r=["",!1,!1]);var o=this.unknownText(t,e);if("-explicitFont"===e){var n=this.cssFontStyles(r);this.adaptor.setAttributes(o,{style:n})}return this.measureTextNodeWithCache(o,t,e,r)},e.prototype.measureTextNodeWithCache=function(t,e,r,o){void 0===o&&(o=["",!1,!1]),"-explicitFont"===r&&(r=[o[0],o[1]?"T":"F",o[2]?"T":"F",""].join("-")),this.unknownCache.has(r)||this.unknownCache.set(r,new Map);var n=this.unknownCache.get(r),i=n.get(e);if(i)return i;var s=this.measureTextNode(t);return n.set(e,s),s},e.prototype.measureXMLnode=function(t){var e=this.adaptor,r=this.html("mjx-xml-block",{style:{display:"inline-block"}},[e.clone(t)]),o=this.html("mjx-baseline",{style:{display:"inline-block",width:0,height:0}}),n=this.html("mjx-measure-xml",{style:{position:"absolute",display:"inline-block","font-family":"initial","line-height":"normal"}},[o,r]);e.append(e.parent(this.math.start.node),this.container),e.append(this.container,n);var i=this.math.metrics.em*this.math.metrics.scale,s=e.nodeBBox(r),a=s.left,l=s.right,h=s.bottom,c=s.top,u=(l-a)/i,p=(e.nodeBBox(o).top-c)/i,d=(h-c)/i-p;return e.remove(this.container),e.remove(n),{w:u,h:p,d:d}},e.prototype.cssFontStyles=function(t,e){void 0===e&&(e={});var r=s(t,3),o=r[0],n=r[1],i=r[2];return e["font-family"]=this.font.getFamily(o),n&&(e["font-style"]="italic"),i&&(e["font-weight"]="bold"),e},e.prototype.getFontData=function(t){return t||(t=new p.Styles),[this.font.getFamily(t.get("font-family")),"italic"===t.get("font-style"),"bold"===t.get("font-weight")]},e.NAME="Common",e.OPTIONS=i(i({},l.AbstractOutputJax.OPTIONS),{scale:1,minScale:.5,matchFontHeight:!0,mtextInheritFont:!1,merrorInheritFont:!1,mtextFont:"",merrorFont:"serif",mathmlSpacing:!1,skipAttributes:{},exFactor:.5,displayAlign:"center",displayIndent:"0",wrapperFactory:null,font:null,cssStyles:null}),e.commonStyles={},e}(l.AbstractOutputJax);e.CommonOutputJax=f},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(s(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonWrapper=void 0;var l=r(76),h=r(3),c=r(4),u=r(16),p=r(71),d=r(1),f=r(2);function y(t,e){return t?e<.1111111111111111?0:.1111111111111111:e}var m=function(t){function e(e,r,o){void 0===o&&(o=null);var n=t.call(this,e,r)||this;return n.parent=null,n.removedStyles=null,n.styles=null,n.variant="",n.bboxComputed=!1,n.stretch=f.NOSTRETCH,n.font=null,n.parent=o,n.font=e.jax.font,n.bbox=d.BBox.zero(),n.getStyles(),n.getVariant(),n.getScale(),n.getSpace(),n.childNodes=r.childNodes.map((function(t){var e=n.wrap(t);return e.bbox.pwidth&&(r.notParent||r.isKind("math"))&&(n.bbox.pwidth=d.BBox.fullWidth),e})),n}return n(e,t),Object.defineProperty(e.prototype,"jax",{get:function(){return this.factory.jax},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"adaptor",{get:function(){return this.factory.jax.adaptor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metrics",{get:function(){return this.factory.jax.math.metrics},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!this.node.notParent&&!this.node.isToken},enumerable:!1,configurable:!0}),e.prototype.wrap=function(t,e){void 0===e&&(e=null);var r=this.factory.wrap(t,e||this);return e&&e.childNodes.push(r),this.jax.nodeMap.set(t,r),r},e.prototype.getBBox=function(t){if(void 0===t&&(t=!0),this.bboxComputed)return this.bbox;var e=t?this.bbox:d.BBox.zero();return this.computeBBox(e),this.bboxComputed=t,e},e.prototype.computeBBox=function(t,e){var r,o;void 0===e&&(e=!1),t.empty();try{for(var n=i(this.childNodes),s=n.next();!s.done;s=n.next()){var a=s.value;t.append(a.getBBox())}}catch(t){r={error:t}}finally{try{s&&!s.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}t.clean(),this.fixesPWidth&&this.setChildPWidths(e)&&this.computeBBox(t,!0)},e.prototype.setChildPWidths=function(t,e,r){var o,n;if(void 0===e&&(e=null),void 0===r&&(r=!0),t)return!1;r&&(this.bbox.pwidth="");var s=!1;try{for(var a=i(this.childNodes),l=a.next();!l.done;l=a.next()){var h=l.value,c=h.getBBox();c.pwidth&&h.setChildPWidths(t,null===e?c.w:e,r)&&(s=!0)}}catch(t){o={error:t}}finally{try{l&&!l.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return s},e.prototype.invalidateBBox=function(){this.bboxComputed&&(this.bboxComputed=!1,this.parent&&this.parent.invalidateBBox())},e.prototype.copySkewIC=function(t){var e=this.childNodes[0];e&&e.bbox.sk&&(t.sk=e.bbox.sk);var r=this.childNodes[this.childNodes.length-1];r&&r.bbox.ic&&(t.ic=r.bbox.ic,t.w+=t.ic)},e.prototype.getStyles=function(){var t=this.node.attributes.getExplicit("style");if(t)for(var r=this.styles=new p.Styles(t),o=0,n=e.removeStyles.length;o<n;o++){var i=e.removeStyles[o];r.get(i)&&(this.removedStyles||(this.removedStyles={}),this.removedStyles[i]=r.get(i),r.set(i,""))}},e.prototype.getVariant=function(){if(this.node.isToken){var t=this.node.attributes,r=t.get("mathvariant");if(!t.getExplicit("mathvariant")){var o=t.getList("fontfamily","fontweight","fontstyle");if(this.removedStyles){var n=this.removedStyles;n.fontFamily&&(o.family=n.fontFamily),n.fontWeight&&(o.weight=n.fontWeight),n.fontStyle&&(o.style=n.fontStyle)}o.fontfamily&&(o.family=o.fontfamily),o.fontweight&&(o.weight=o.fontweight),o.fontstyle&&(o.style=o.fontstyle),o.weight&&o.weight.match(/^\d+$/)&&(o.weight=parseInt(o.weight)>600?"bold":"normal"),o.family?r=this.explicitVariant(o.family,o.weight,o.style):(this.node.getProperty("variantForm")&&(r="-tex-variant"),r=(e.BOLDVARIANTS[o.weight]||{})[r]||r,r=(e.ITALICVARIANTS[o.style]||{})[r]||r)}this.variant=r}},e.prototype.explicitVariant=function(t,e,r){var o=this.styles;return o||(o=this.styles=new p.Styles),o.set("fontFamily",t),e&&o.set("fontWeight",e),r&&o.set("fontStyle",r),"-explicitFont"},e.prototype.getScale=function(){var t=1,e=this.parent,r=e?e.bbox.scale:1,o=this.node.attributes,n=Math.min(o.get("scriptlevel"),2),i=o.get("fontsize"),s=this.node.isToken||this.node.isKind("mstyle")?o.get("mathsize"):o.getInherited("mathsize");if(0!==n){t=Math.pow(o.get("scriptsizemultiplier"),n);var a=this.length2em(o.get("scriptminsize"),.8,1);t<a&&(t=a)}this.removedStyles&&this.removedStyles.fontSize&&!i&&(i=this.removedStyles.fontSize),i&&!o.getExplicit("mathsize")&&(s=i),"1"!==s&&(t*=this.length2em(s,1,1)),this.bbox.scale=t,this.bbox.rscale=t/r},e.prototype.getSpace=function(){var t=this.isTopEmbellished(),e=this.node.hasSpacingAttributes();this.jax.options.mathmlSpacing||e?t&&this.getMathMLSpacing():this.getTeXSpacing(t,e)},e.prototype.getMathMLSpacing=function(){var t=this.node.coreMO(),e=t.attributes,r=e.get("scriptlevel")>0;this.bbox.L=e.isSet("lspace")?Math.max(0,this.length2em(e.get("lspace"))):y(r,t.lspace),this.bbox.R=e.isSet("rspace")?Math.max(0,this.length2em(e.get("rspace"))):y(r,t.rspace)},e.prototype.getTeXSpacing=function(t,e){if(!e){var r=this.node.texSpacing();r&&(this.bbox.L=this.length2em(r))}if(t||e){var o=this.node.coreMO().attributes;o.isSet("lspace")&&(this.bbox.L=Math.max(0,this.length2em(o.get("lspace")))),o.isSet("rspace")&&(this.bbox.R=Math.max(0,this.length2em(o.get("rspace"))))}},e.prototype.isTopEmbellished=function(){return this.node.isEmbellished&&!(this.node.Parent&&this.node.Parent.isEmbellished)},e.prototype.core=function(){return this.jax.nodeMap.get(this.node.core())},e.prototype.coreMO=function(){return this.jax.nodeMap.get(this.node.coreMO())},e.prototype.getText=function(){var t,e,r="";if(this.node.isToken)try{for(var o=i(this.node.childNodes),n=o.next();!n.done;n=o.next()){var s=n.value;s instanceof h.TextNode&&(r+=s.getText())}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},e.prototype.canStretch=function(t){if(this.stretch=f.NOSTRETCH,this.node.isEmbellished){var e=this.core();e&&e.node!==this.node&&e.canStretch(t)&&(this.stretch=e.stretch)}return 0!==this.stretch.dir},e.prototype.getAlignShift=function(){var t,e=(t=this.node.attributes).getList.apply(t,a(h.indentAttributes)),r=e.indentalign,o=e.indentshift,n=e.indentalignfirst,i=e.indentshiftfirst;return"indentalign"!==n&&(r=n),"auto"===r&&(r=this.jax.options.displayAlign),"indentshift"!==i&&(o=i),"auto"===o&&(o=this.jax.options.displayIndent,"right"!==r||o.match(/^\s*0[a-z]*\s*$/)||(o=("-"+o.trim()).replace(/^--/,""))),[r,this.length2em(o,this.metrics.containerWidth)]},e.prototype.getAlignX=function(t,e,r){return"right"===r?t-(e.w+e.R)*e.rscale:"left"===r?e.L*e.rscale:(t-e.w*e.rscale)/2},e.prototype.getAlignY=function(t,e,r,o,n){return"top"===n?t-r:"bottom"===n?o-e:"middle"===n?(t-r-(e-o))/2:0},e.prototype.getWrapWidth=function(t){return this.childNodes[t].getBBox().w},e.prototype.getChildAlign=function(t){return"left"},e.prototype.percent=function(t){return u.percent(t)},e.prototype.em=function(t){return u.em(t)},e.prototype.px=function(t,e){return void 0===e&&(e=-u.BIGDIMEN),u.px(t,e,this.metrics.em)},e.prototype.length2em=function(t,e,r){return void 0===e&&(e=1),void 0===r&&(r=null),null===r&&(r=this.bbox.scale),u.length2em(t,e,r,this.jax.pxPerEm)},e.prototype.unicodeChars=function(t,e){void 0===e&&(e=this.variant);var r=c.unicodeChars(t),o=this.font.getVariant(e);if(o&&o.chars){var n=o.chars;r=r.map((function(t){return((n[t]||[])[3]||{}).smp||t}))}return r},e.prototype.remapChars=function(t){return t},e.prototype.mmlText=function(t){return this.node.factory.create("text").setText(t)},e.prototype.mmlNode=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.node.factory.create(t,e,r)},e.prototype.createMo=function(t){var e=this.node.factory,r=e.create("text").setText(t),o=e.create("mo",{stretchy:!0},[r]);o.inheritAttributesFrom(this.node);var n=this.wrap(o);return n.parent=this,n},e.prototype.getVariantChar=function(t,e){var r=this.font.getChar(t,e)||[0,0,0,{unknown:!0}];return 3===r.length&&(r[3]={}),r},e.kind="unknown",e.styles={},e.removeStyles=["fontSize","fontFamily","fontWeight","fontStyle","fontVariant","font"],e.skipAttributes={fontfamily:!0,fontsize:!0,fontweight:!0,fontstyle:!0,color:!0,background:!0,class:!0,href:!0,style:!0,xmlns:!0},e.BOLDVARIANTS={bold:{normal:"bold",italic:"bold-italic",fraktur:"bold-fraktur",script:"bold-script","sans-serif":"bold-sans-serif","sans-serif-italic":"sans-serif-bold-italic"},normal:{bold:"normal","bold-italic":"italic","bold-fraktur":"fraktur","bold-script":"script","bold-sans-serif":"sans-serif","sans-serif-bold-italic":"sans-serif-italic"}},e.ITALICVARIANTS={italic:{normal:"italic",bold:"bold-italic","sans-serif":"sans-serif-italic","bold-sans-serif":"sans-serif-bold-italic"},normal:{italic:"normal","bold-italic":"bold","sans-serif-italic":"sans-serif","sans-serif-bold-italic":"bold-sans-serif"}},e}(l.AbstractWrapper);e.CommonWrapper=m},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonWrapperFactory=void 0;var i=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.jax=null,e}return n(e,t),Object.defineProperty(e.prototype,"Wrappers",{get:function(){return this.node},enumerable:!1,configurable:!0}),e.defaultNodes={},e}(r(77).AbstractWrapperFactory);e.CommonWrapperFactory=i},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTeXAtomMixin=void 0;var i=r(3);e.CommonTeXAtomMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.computeBBox=function(e,r){if(void 0===r&&(r=!1),t.prototype.computeBBox.call(this,e,r),this.childNodes[0]&&this.childNodes[0].bbox.ic&&(e.ic=this.childNodes[0].bbox.ic),this.node.texClass===i.TEXCLASS.VCENTER){var o=e.h,n=(o+e.d)/2+this.font.params.axis_height-o;e.h+=n,e.d-=n}},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTextNodeMixin=void 0,e.CommonTextNodeMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.computeBBox=function(t,e){var r,o;void 0===e&&(e=!1);var n=this.parent.variant,a=this.node.getText();if("-explicitFont"===n){var l=this.jax.getFontData(this.parent.styles),h=this.jax.measureText(a,n,l),c=h.w,u=h.h,p=h.d;t.h=u,t.d=p,t.w=c}else{var d=this.remappedText(a,n);t.empty();try{for(var f=i(d),y=f.next();!y.done;y=f.next()){var m=y.value,v=s(this.getVariantChar(n,m),4),b=(u=v[0],p=v[1],c=v[2],v[3]);if(b.unknown){var x=this.jax.measureText(String.fromCodePoint(m),n);c=x.w,u=x.h,p=x.d}t.w+=c,u>t.h&&(t.h=u),p>t.d&&(t.d=p),t.ic=b.ic||0,t.sk=b.sk||0}}catch(t){r={error:t}}finally{try{y&&!y.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}d.length>1&&(t.sk=0),t.clean()}},e.prototype.remappedText=function(t,e){var r=this.parent.stretch.c;return r?[r]:this.parent.remapChars(this.unicodeChars(t,e))},e.prototype.getStyles=function(){},e.prototype.getVariant=function(){},e.prototype.getScale=function(){},e.prototype.getSpace=function(){},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMathMixin=void 0,e.CommonMathMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getWrapWidth=function(t){return this.parent?this.getBBox().w:this.metrics.containerWidth/this.jax.pxPerEm},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMencloseMixin=void 0;var l=r(5),h=r(4);e.CommonMencloseMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.notations={},o.renderChild=null,o.msqrt=null,o.padding=l.PADDING,o.thickness=l.THICKNESS,o.arrowhead={x:l.ARROWX,y:l.ARROWY,dx:l.ARROWDX},o.getParameters(),o.getNotations(),o.removeRedundantNotations(),o.initializeNotations(),o}return n(e,t),e.prototype.getParameters=function(){var t=this.node.attributes,e=t.get("data-padding");void 0!==e&&(this.padding=this.length2em(e,l.PADDING));var r=t.get("data-thickness");void 0!==r&&(this.thickness=this.length2em(r,l.THICKNESS));var o=t.get("data-arrowhead");if(void 0!==o){var n=i(h.split(o),3),s=n[0],a=n[1],c=n[2];this.arrowhead={x:s?parseFloat(s):l.ARROWX,y:a?parseFloat(a):l.ARROWY,dx:c?parseFloat(c):l.ARROWDX}}},e.prototype.getNotations=function(){var t,e,r=this.constructor.notations;try{for(var o=a(h.split(this.node.attributes.get("notation"))),n=o.next();!n.done;n=o.next()){var i=n.value,s=r.get(i);s&&(this.notations[i]=s,s.renderChild&&(this.renderChild=s.renderer))}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}},e.prototype.removeRedundantNotations=function(){var t,e,r,o;try{for(var n=a(Object.keys(this.notations)),i=n.next();!i.done;i=n.next()){var s=i.value;if(this.notations[s]){var l=this.notations[s].remove||"";try{for(var h=(r=void 0,a(l.split(/ /))),c=h.next();!c.done;c=h.next()){var u=c.value;delete this.notations[u]}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=h.return)&&o.call(h)}finally{if(r)throw r.error}}}}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}},e.prototype.initializeNotations=function(){var t,e;try{for(var r=a(Object.keys(this.notations)),o=r.next();!o.done;o=r.next()){var n=o.value,i=this.notations[n].init;i&&i(this)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=i(this.getBBoxExtenders(),4),o=r[0],n=r[1],s=r[2],a=r[3],l=this.childNodes[0].getBBox();t.combine(l,a,0),t.h+=o,t.d+=s,t.w+=n,this.setChildPWidths(e)},e.prototype.getBBoxExtenders=function(){var t,e,r=[0,0,0,0];try{for(var o=a(Object.keys(this.notations)),n=o.next();!n.done;n=o.next()){var i=n.value;this.maximizeEntries(r,this.notations[i].bbox(this))}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},e.prototype.getPadding=function(){var t,e,r=[0,0,0,0],o=[0,0,0,0];try{for(var n=a(Object.keys(this.notations)),i=n.next();!i.done;i=n.next()){var s=i.value;this.maximizeEntries(r,this.notations[s].bbox(this));var l=this.notations[s].border;l&&this.maximizeEntries(o,l(this))}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}return[0,1,2,3].map((function(t){return r[t]-o[t]}))},e.prototype.maximizeEntries=function(t,e){for(var r=0;r<t.length;r++)t[r]<e[r]&&(t[r]=e[r])},e.prototype.getArgMod=function(t,e){return[Math.atan2(e,t),Math.sqrt(t*t+e*e)]},e.prototype.arrow=function(t,e,r){return void 0===r&&(r=!1),null},e.prototype.arrowData=function(){var t=i([this.padding,this.thickness],2),e=t[0],r=t[1]*(this.arrowhead.x+Math.max(1,this.arrowhead.dx)),o=this.childNodes[0].getBBox(),n=o.h,s=o.d,a=o.w,l=n+s,h=Math.sqrt(l*l+a*a),c=Math.max(e,r*a/h),u=Math.max(e,r*l/h),p=i(this.getArgMod(a+2*c,l+2*u),2);return{a:p[0],W:p[1],x:c,y:u}},e.prototype.createMsqrt=function(t){var e=this.node.factory.create("msqrt");e.inheritAttributesFrom(this.node),e.childNodes[0]=t.node;var r=this.wrap(e);return r.parent=this,r},e.prototype.sqrtTRBL=function(){var t=this.msqrt.getBBox(),e=this.msqrt.childNodes[0].getBBox();return[t.h-e.h,0,t.d-e.d,t.w-e.w]},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMfencedMixin=void 0,e.CommonMfencedMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.mrow=null,o.createMrow(),o.addMrowChildren(),o}return n(e,t),e.prototype.createMrow=function(){var t=this.node.factory.create("inferredMrow");t.inheritAttributesFrom(this.node),this.mrow=this.wrap(t),this.mrow.parent=this},e.prototype.addMrowChildren=function(){var t,e,r=this.node,o=this.mrow;this.addMo(r.open),this.childNodes.length&&o.childNodes.push(this.childNodes[0]);var n=0;try{for(var i=a(this.childNodes.slice(1)),s=i.next();!s.done;s=i.next()){var l=s.value;this.addMo(r.separators[n++]),o.childNodes.push(l)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}this.addMo(r.close),o.stretchChildren()},e.prototype.addMo=function(t){if(t){var e=this.wrap(t);this.mrow.childNodes.push(e),e.parent=this.mrow}},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.updateFrom(this.mrow.getBBox()),this.setChildPWidths(e)},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMfracMixin=void 0,e.CommonMfracMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;if(o.bevel=null,o.pad=o.node.getProperty("withDelims")?0:o.font.params.nulldelimiterspace,o.node.attributes.get("bevelled")){var n=o.getBevelData(o.isDisplay()).H,i=o.bevel=o.createMo("/");i.canStretch(1),i.getStretchedVariant([n],!0)}return o}return n(e,t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.empty();var r=this.node.attributes.getList("linethickness","bevelled"),o=r.linethickness,n=r.bevelled,i=this.isDisplay(),s=null;if(n)this.getBevelledBBox(t,i);else{var a=this.length2em(String(o),.06);s=-2*this.pad,0===a?this.getAtopBBox(t,i):(this.getFractionBBox(t,i,a),s-=.2),s+=t.w}t.clean(),this.setChildPWidths(e,s)},e.prototype.getFractionBBox=function(t,e,r){var o=this.childNodes[0].getBBox(),n=this.childNodes[1].getBBox(),i=this.font.params.axis_height,s=this.getTUV(e,r),a=s.T,l=s.u,h=s.v;t.combine(o,0,i+a+Math.max(o.d*o.rscale,l)),t.combine(n,0,i-a-Math.max(n.h*n.rscale,h)),t.w+=2*this.pad+.2},e.prototype.getTUV=function(t,e){var r=this.font.params,o=r.axis_height,n=(t?3.5:1.5)*e;return{T:(t?3.5:1.5)*e,u:(t?r.num1:r.num2)-o-n,v:(t?r.denom1:r.denom2)+o-n}},e.prototype.getAtopBBox=function(t,e){var r=this.getUVQ(e),o=r.u,n=r.v,i=r.nbox,s=r.dbox;t.combine(i,0,o),t.combine(s,0,-n),t.w+=2*this.pad},e.prototype.getUVQ=function(t){var e=this.childNodes[0].getBBox(),r=this.childNodes[1].getBBox(),o=this.font.params,n=i(t?[o.num1,o.denom1]:[o.num3,o.denom2],2),s=n[0],a=n[1],l=(t?7:3)*o.rule_thickness,h=s-e.d*e.scale-(r.h*r.scale-a);return h<l&&(s+=(l-h)/2,a+=(l-h)/2,h=l),{u:s,v:a,q:h,nbox:e,dbox:r}},e.prototype.getBevelledBBox=function(t,e){var r=this.getBevelData(e),o=r.u,n=r.v,i=r.delta,s=r.nbox,a=r.dbox,l=this.bevel.getBBox();t.combine(s,0,o),t.combine(l,t.w-i/2,0),t.combine(a,t.w-i/2,n)},e.prototype.getBevelData=function(t){var e=this.childNodes[0].getBBox(),r=this.childNodes[1].getBBox(),o=t?.4:.15,n=Math.max(e.scale*(e.h+e.d),r.scale*(r.h+r.d))+2*o,i=this.font.params.axis_height;return{H:n,delta:o,u:e.scale*(e.d-e.h)/2+i+o,v:r.scale*(r.d-r.h)/2+i-o,nbox:e,dbox:r}},e.prototype.canStretch=function(t){return!1},e.prototype.isDisplay=function(){var t=this.node.attributes.getList("displaystyle","scriptlevel"),e=t.displaystyle,r=t.scriptlevel;return e&&0===r},e.prototype.getWrapWidth=function(t){var e=this.node.attributes;return e.get("bevelled")?this.childNodes[t].getBBox().w:this.getBBox().w-(this.length2em(e.get("linethickness"))?.2:0)-2*this.pad},e.prototype.getChildAlign=function(t){var e=this.node.attributes;return e.get("bevelled")?"left":e.get(["numalign","denomalign"][t])},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMglyphMixin=void 0,e.CommonMglyphMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.getParameters(),o}return n(e,t),e.prototype.getParameters=function(){var t=this.node.attributes.getList("width","height","valign"),e=t.width,r=t.height,o=t.valign;this.width="auto"===e?1:this.length2em(e),this.height="auto"===r?1:this.length2em(r),this.valign=this.length2em(o||"0")},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.w=this.width,t.h=this.height+this.valign,t.d=-this.valign},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMiMixin=void 0,e.CommonMiMixin=function(t){return function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.noIC=!1,e}return n(e,t),e.prototype.computeBBox=function(e,r){void 0===r&&(r=!1),t.prototype.computeBBox.call(this,e),this.copySkewIC(e),this.noIC&&(e.w-=e.ic)},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMmultiscriptsMixin=e.ScriptNames=e.NextScript=void 0;var a=r(1);e.NextScript={base:"subList",subList:"supList",supList:"subList",psubList:"psupList",psupList:"psubList"},e.ScriptNames=["sup","sup","psup","psub"],e.CommonMmultiscriptsMixin=function(t){return function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return e.scriptData=null,e.firstPrescript=0,e}return n(r,t),r.prototype.combinePrePost=function(t,e){var r=new a.BBox(t);return r.combine(e,0,0),r},r.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.font.params.scriptspace,o=this.getScriptData(),n=this.combinePrePost(o.sub,o.psub),s=this.combinePrePost(o.sup,o.psup),a=i(this.getUVQ(o.base,n,s),2),l=a[0],h=a[1];if(t.empty(),o.numPrescripts&&(t.combine(o.psup,r,l),t.combine(o.psub,r,h)),t.append(o.base),o.numScripts){var c=t.w;t.combine(o.sup,c,l),t.combine(o.sub,c,h),t.w+=r}t.clean(),this.setChildPWidths(e)},r.prototype.getScriptData=function(){if(this.scriptData)return this.scriptData;var t=this.scriptData={base:null,sub:a.BBox.empty(),sup:a.BBox.empty(),psub:a.BBox.empty(),psup:a.BBox.empty(),numPrescripts:0,numScripts:0},e=this.getScriptBBoxLists();return this.combineBBoxLists(t.sub,t.sup,e.subList,e.supList),this.combineBBoxLists(t.psub,t.psup,e.psubList,e.psupList),this.scriptData.base=e.base[0],this.scriptData.numPrescripts=e.psubList.length,this.scriptData.numScripts=e.subList.length,this.scriptData},r.prototype.getScriptBBoxLists=function(){var t,r,o={base:[],subList:[],supList:[],psubList:[],psupList:[]},n="base";try{for(var i=s(this.childNodes),a=i.next();!a.done;a=i.next()){var l=a.value;l.node.isKind("mprescripts")?n="psubList":(o[n].push(l.getBBox()),n=e.NextScript[n])}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return this.firstPrescript=o.subList.length+o.supList.length+2,this.padLists(o.subList,o.supList),this.padLists(o.psubList,o.psupList),o},r.prototype.padLists=function(t,e){t.length>e.length&&e.push(a.BBox.empty())},r.prototype.combineBBoxLists=function(t,e,r,o){for(var n=0;n<r.length;n++){var s=i(this.getScaledWHD(r[n]),3),a=s[0],l=s[1],h=s[2],c=i(this.getScaledWHD(o[n]),3),u=c[0],p=c[1],d=c[2],f=Math.max(a,u);t.w+=f,e.w+=f,l>t.h&&(t.h=l),h>t.d&&(t.d=h),p>e.h&&(e.h=p),d>e.d&&(e.d=d)}},r.prototype.getScaledWHD=function(t){var e=t.w,r=t.h,o=t.d,n=t.rscale;return[e*n,r*n,o*n]},r.prototype.getUVQ=function(e,r,o){var n;if(!this.UVQ){var s=i([0,0,0],3),a=s[0],l=s[1],h=s[2];0===r.h&&0===r.d?a=this.getU(e,o):0===o.h&&0===o.d?a=-this.getV(e,r):(a=(n=i(t.prototype.getUVQ.call(this,e,r,o),3))[0],l=n[1],h=n[2]),this.UVQ=[a,l,h]}return this.UVQ},r}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMnMixin=void 0,e.CommonMnMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.remapChars=function(t){if(t.length){var e=this.font.getRemappedChar("mn",t[0]);if(e){var r=this.unicodeChars(e,this.variant);1===r.length?t[0]=r[0]:t=r.concat(t.slice(1))}}return t},e}(t)}},function(t,e,r){"use strict";var o,n,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(s(arguments[e]));return t},l=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMoMixin=e.DirectionVH=void 0;var h=r(2);e.DirectionVH=((n={})[1]="v",n[2]="h",n),e.CommonMoMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.noIC=!1,o.size=null,o.isAccent=o.node.isAccent,o}return i(e,t),e.prototype.computeBBox=function(e,r){void 0===r&&(r=!1);var o=0!==this.stretch.dir;if(o&&null===this.size&&this.getStretchedVariant([0]),!(o&&this.size<0)&&(t.prototype.computeBBox.call(this,e),this.copySkewIC(e),this.noIC&&(e.w-=e.ic),this.node.attributes.get("symmetric")&&2!==this.stretch.dir)){var n=(e.h+e.d)/2+this.font.params.axis_height-e.h;e.h+=n,e.d-=n}},e.prototype.getVariant=function(){this.node.attributes.get("largeop")?this.variant=this.node.attributes.get("displaystyle")?"-largeop":"-smallop":t.prototype.getVariant.call(this)},e.prototype.canStretch=function(t){if(0!==this.stretch.dir)return this.stretch.dir===t;if(!this.node.attributes.get("stretchy"))return!1;var e=this.getText();if(1!==Array.from(e).length)return!1;var r=this.font.getDelimiter(e.codePointAt(0));return this.stretch=r&&r.dir===t?r:h.NOSTRETCH,0!==this.stretch.dir},e.prototype.getStretchedVariant=function(t,e){var r,o;if(void 0===e&&(e=!1),0!==this.stretch.dir){var n=this.getWH(t),i=this.getSize("minsize",0),s=this.getSize("maxsize",1/0);n=Math.max(i,Math.min(s,n));var a=i||e?n:Math.max(n*this.font.params.delimiterfactor/1e3,n-this.font.params.delimitershortfall),h=this.stretch,c=h.c||this.getText().codePointAt(0),u=0;if(h.sizes)try{for(var p=l(h.sizes),d=p.next();!d.done;d=p.next()){if(d.value>=a)return this.variant=this.font.getSizeVariant(c,u),void(this.size=u);u++}}catch(t){r={error:t}}finally{try{d&&!d.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}h.stretch?(this.size=-1,this.invalidateBBox(),this.getStretchBBox(t,n,h)):(this.variant=this.font.getSizeVariant(c,u-1),this.size=u-1)}},e.prototype.getSize=function(t,e){var r=this.node.attributes;return r.isSet(t)&&(e=this.length2em(r.get(t),1,1)),e},e.prototype.getWH=function(t){if(0===t.length)return 0;if(1===t.length)return t[0];var e=s(t,2),r=e[0],o=e[1],n=this.font.params.axis_height;return this.node.attributes.get("symmetric")?2*Math.max(r-n,o+n):r+o},e.prototype.getStretchBBox=function(t,e,r){var o;r.hasOwnProperty("min")&&r.min>e&&(e=r.min);var n=s(r.HDW,3),i=n[0],a=n[1],l=n[2];1===this.stretch.dir?(i=(o=s(this.getBaseline(t,e,r),2))[0],a=o[1]):l=e,this.bbox.h=i,this.bbox.d=a,this.bbox.w=l},e.prototype.getBaseline=function(t,e,r){var o=2===t.length&&t[0]+t[1]===e,n=this.node.attributes.get("symmetric"),i=s(o?t:[e,0],2),a=i[0],l=i[1],h=s([a+l,0],2),c=h[0],u=h[1];if(n){var p=this.font.params.axis_height;o&&(c=2*Math.max(a-p,l+p)),u=c/2-p}else if(o)u=l;else{var d=s(r.HDW||[.75,.25],2),f=d[0],y=d[1];u=y*(c/(f+y))}return[c-u,u]},e.prototype.remapChars=function(t){if(1===t.length){var e=this.node.coreParent().parent,r=this.isAccent&&!e.isKind("mrow")?"accent":"mo",o=this.font.getRemappedChar(r,t[0]);o&&(t=this.unicodeChars(o,this.variant))}return t},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMpaddedMixin=void 0,e.CommonMpaddedMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getDimens=function(){var t=this.node.attributes.getList("width","height","depth","lspace","voffset"),e=this.childNodes[0].getBBox(),r=e.w,o=e.h,n=e.d,i=r,s=o,a=n,l=0,h=0,c=0;""!==t.width&&(r=this.dimen(t.width,e,"w",0)),""!==t.height&&(o=this.dimen(t.height,e,"h",0)),""!==t.depth&&(n=this.dimen(t.depth,e,"d",0)),""!==t.voffset&&(h=this.dimen(t.voffset,e)),""!==t.lspace&&(l=this.dimen(t.lspace,e));var u=this.node.attributes.get("data-align");return u&&(c=this.getAlignX(r,e,u)),[s,a,i,o-s,n-a,r-i,l,h,c]},e.prototype.dimen=function(t,e,r,o){void 0===r&&(r=""),void 0===o&&(o=null);var n=(t=String(t)).match(/width|height|depth/),i=n?e[n[0].charAt(0)]:r?e[r]:0,s=this.length2em(t,i)||0;return t.match(/^[-+]/)&&r&&(s+=i),null!=o&&(s=Math.max(o,s)),s},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=i(this.getDimens(),6),o=r[0],n=r[1],s=r[2],a=r[3],l=r[4],h=r[5];t.w=s+h,t.h=o+a,t.d=n+l,this.setChildPWidths(e,t.w)},e.prototype.getWrapWidth=function(t){return this.getBBox().w},e.prototype.getChildAlign=function(t){return this.node.attributes.get("data-align")||"left"},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMrootMixin=void 0,e.CommonMrootMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),Object.defineProperty(e.prototype,"surd",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"root",{get:function(){return 1},enumerable:!1,configurable:!0}),e.prototype.combineRootBBox=function(t,e,r){var o=this.childNodes[this.root].getBBox(),n=this.getRootDimens(e,r)[1];t.combine(o,0,n)},e.prototype.getRootDimens=function(t,e){var r=this.childNodes[this.surd],o=this.childNodes[this.root].getBBox(),n=(r.size<0?.5:.6)*t.w,i=o.w,s=o.rscale,a=Math.max(i,n/s),l=Math.max(0,a-i);return[a*s-n,this.rootHeight(o,t,r.size,e),l]},e.prototype.rootHeight=function(t,e,r,o){var n=e.h+e.d;return(r<0?1.9:.55*n)-(n-o)+Math.max(0,t.d*t.rscale)},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsMixin=void 0,e.CommonMsMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this,n=o.node.attributes,i=n.getList("lquote","rquote");return"monospace"!==o.variant&&(n.isSet("lquote")||'"'!==i.lquote||(i.lquote="\u201c"),n.isSet("rquote")||'"'!==i.rquote||(i.rquote="\u201d")),o.childNodes.unshift(o.createText(i.lquote)),o.childNodes.push(o.createText(i.rquote)),o}return n(e,t),e.prototype.createText=function(t){var e=this.wrap(this.mmlText(t));return e.parent=this,e},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMspaceMixin=void 0,e.CommonMspaceMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.node.attributes;t.w=this.length2em(r.get("width"),0),t.h=this.length2em(r.get("height"),0),t.d=this.length2em(r.get("depth"),0)},e.prototype.handleVariant=function(){},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsqrtMixin=void 0;var a=r(1);e.CommonMsqrtMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this,n=o.createMo("\u221a");n.canStretch(1);var i=o.childNodes[o.base].getBBox(),a=i.h,l=i.d,h=o.font.params.rule_thickness,c=o.node.attributes.get("displaystyle")?o.font.params.x_height:h;return o.surdH=a+l+2*h+c/4,n.getStretchedVariant([o.surdH-l,l],!0),o}return n(e,t),Object.defineProperty(e.prototype,"base",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"surd",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"root",{get:function(){return null},enumerable:!1,configurable:!0}),e.prototype.createMo=function(e){var r=t.prototype.createMo.call(this,e);return this.childNodes.push(r),r},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.childNodes[this.surd].getBBox(),o=new a.BBox(this.childNodes[this.base].getBBox()),n=this.getPQ(r)[1],s=this.font.params.rule_thickness,l=o.h+n+s,h=i(this.getRootDimens(r,l),1)[0];t.h=l+s,this.combineRootBBox(t,r,l),t.combine(r,h,l-r.h),t.combine(o,h+r.w,0),t.clean(),this.setChildPWidths(e)},e.prototype.combineRootBBox=function(t,e,r){},e.prototype.getPQ=function(t){var e=this.font.params.rule_thickness,r=this.node.attributes.get("displaystyle")?this.font.params.x_height:e;return[r,t.h+t.d>this.surdH?(t.h+t.d-(this.surdH-2*e-r/2))/2:e+r/4]},e.prototype.getRootDimens=function(t,e){return[0,0,0,0]},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtableMixin=void 0;var l=r(1),h=r(4),c=r(78);e.CommonMtableMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;o.numCols=0,o.numRows=0,o.data=null,o.pwidthCells=[],o.pWidth=0,o.numCols=c.max(o.tableRows.map((function(t){return t.numCells}))),o.numRows=o.childNodes.length,o.hasLabels=o.childNodes.reduce((function(t,e){return t||e.node.isKind("mlabeledtr")}),!1),o.findContainer(),o.isTop=!o.container||o.container.node.isKind("math")&&!o.container.parent,o.isTop&&(o.jax.table=o),o.getPercentageWidth();var n=o.node.attributes;return o.frame="none"!==n.get("frame"),o.fLine=o.frame?.07:0,o.fSpace=o.frame?o.convertLengths(o.getAttributeArray("framespacing")):[0,0],o.cSpace=o.convertLengths(o.getColumnAttributes("columnspacing")),o.rSpace=o.convertLengths(o.getRowAttributes("rowspacing")),o.cLines=o.getColumnAttributes("columnlines").map((function(t){return"none"===t?0:.07})),o.rLines=o.getRowAttributes("rowlines").map((function(t){return"none"===t?0:.07})),o.cWidths=o.getColumnWidths(),o.stretchRows(),o.stretchColumns(),o}return n(e,t),Object.defineProperty(e.prototype,"tableRows",{get:function(){return this.childNodes},enumerable:!1,configurable:!0}),e.prototype.findContainer=function(){for(var t=this,e=t.parent;e&&(e.node.notParent||e.node.isKind("mrow"));)t=e,e=e.parent;this.container=e,this.containerI=t.node.childPosition()},e.prototype.getPercentageWidth=function(){if(this.hasLabels)this.bbox.pwidth=l.BBox.fullWidth;else{var t=this.node.attributes.get("width");h.isPercent(t)&&(this.bbox.pwidth=t)}},e.prototype.stretchRows=function(){for(var t=this.node.attributes.get("equalrows"),e=t?this.getEqualRowHeight():0,r=t?this.getTableData():{H:[0],D:[0]},o=r.H,n=r.D,i=this.tableRows,s=0;s<this.numRows;s++){var a=t?[(e+o[s]-n[s])/2,(e-o[s]+n[s])/2]:null;i[s].stretchChildren(a)}},e.prototype.stretchColumns=function(){for(var t=0;t<this.numCols;t++){var e="number"==typeof this.cWidths[t]?this.cWidths[t]:null;this.stretchColumn(t,e)}},e.prototype.stretchColumn=function(t,e){var r,o,n,i,s,l,h=[];try{for(var c=a(this.tableRows),u=c.next();!u.done;u=c.next()){if(v=u.value.getChild(t))0===(M=v.childNodes[0]).stretch.dir&&M.canStretch(2)&&h.push(M)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}var p=h.length,d=this.childNodes.length;if(p&&d>1){if(null===e){e=0;var f=p>1&&p===d;try{for(var y=a(this.tableRows),m=y.next();!m.done;m=y.next()){var v;if(v=m.value.getChild(t)){var b=0===(M=v.childNodes[0]).stretch.dir;if(f||b){var x=M.getBBox(b).w;x>e&&(e=x)}}}}catch(t){n={error:t}}finally{try{m&&!m.done&&(i=y.return)&&i.call(y)}finally{if(n)throw n.error}}}try{for(var g=a(h),_=g.next();!_.done;_=g.next()){var M;(M=_.value).coreMO().getStretchedVariant([e])}}catch(t){s={error:t}}finally{try{_&&!_.done&&(l=g.return)&&l.call(g)}finally{if(s)throw s.error}}}},e.prototype.getTableData=function(){if(this.data)return this.data;for(var t=new Array(this.numRows).fill(0),e=new Array(this.numRows).fill(0),r=new Array(this.numCols).fill(0),o=new Array(this.numRows),n=new Array(this.numRows),i=[0],s=this.tableRows,a=0;a<s.length;a++){for(var l=s[a],h=0;h<l.numCells;h++){var c=l.getChild(h);this.updateHDW(c,h,a,t,e,r),this.recordPWidthCell(c,h)}o[a]=t[a],n[a]=e[a],l.labeled&&this.updateHDW(l.childNodes[0],0,a,t,e,i)}var u=i[0];return this.data={H:t,D:e,W:r,NH:o,ND:n,L:u},this.data},e.prototype.updateHDW=function(t,e,r,o,n,i){void 0===i&&(i=null);var s=t.getBBox(),a=s.h,l=s.d,h=s.w;a<.75&&(a=.75),l<.25&&(l=.25),a>o[r]&&(o[r]=a),l>n[r]&&(n[r]=l),i&&h>i[e]&&(i[e]=h)},e.prototype.recordPWidthCell=function(t,e){t.childNodes[0]&&t.childNodes[0].getBBox().pwidth&&this.pwidthCells.push([t,e])},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r,o,n=this.getTableData(),s=n.H,a=n.D;if(this.node.attributes.get("equalrows")){var l=this.getEqualRowHeight();r=c.sum([].concat(this.rLines,this.rSpace))+l*this.numRows}else r=c.sum(s.concat(a,this.rLines,this.rSpace));r+=2*(this.fLine+this.fSpace[1]);var u=this.getComputedWidths();o=c.sum(u.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]);var p=this.node.attributes.get("width");"auto"!==p&&(o=Math.max(this.length2em(p,0)+2*this.fLine,o));var d=i(this.getBBoxHD(r),2),f=d[0],y=d[1];t.h=f,t.d=y,t.w=o;var m=i(this.getBBoxLR(),2),v=m[0],b=m[1];t.L=v,t.R=b,h.isPercent(p)||this.setColumnPWidths()},e.prototype.setChildPWidths=function(t,e,r){var o=this.node.attributes.get("width");if(!h.isPercent(o))return!1;this.hasLabels||(this.bbox.pwidth="",this.container.bbox.pwidth="");var n=this.bbox,i=n.w,s=n.L,a=n.R,l=Math.max(i,this.length2em(o,Math.max(e,s+i+a))),u=this.node.attributes.get("equalcolumns")?Array(this.numCols).fill(this.percent(1/Math.max(1,this.numCols))):this.getColumnAttributes("columnwidth",0);this.cWidths=this.getColumnWidthsFixed(u,l);var p=this.getComputedWidths();return this.pWidth=c.sum(p.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]),this.isTop&&(this.bbox.w=this.pWidth),this.setColumnPWidths(),this.pWidth!==i&&this.parent.invalidateBBox(),this.pWidth!==i},e.prototype.setColumnPWidths=function(){var t,e,r=this.cWidths;try{for(var o=a(this.pwidthCells),n=o.next();!n.done;n=o.next()){var s=i(n.value,2),l=s[0],h=s[1];l.setChildPWidths(!1,r[h])&&(l.invalidateBBox(),l.getBBox())}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}},e.prototype.getBBoxHD=function(t){var e=i(this.getAlignmentRow(),2),r=e[0],o=e[1];if(null===o){var n=this.font.params.axis_height,s=t/2;return{top:[0,t],center:[s,s],bottom:[t,0],baseline:[s,s],axis:[s+n,s-n]}[r]||[s,s]}var a=this.getVerticalPosition(o,r);return[a,t-a]},e.prototype.getBBoxLR=function(){if(this.hasLabels){var t=this.node.attributes.get("side"),e=i(this.getPadAlignShift(t),2),r=e[0];return"center"===e[1]?[r,r]:"left"===t?[r,0]:[0,r]}return[0,0]},e.prototype.getPadAlignShift=function(t){var e=this.getTableData().L+this.length2em(this.node.attributes.get("minlabelspacing")),r=i(null==this.styles?["",""]:[this.styles.get("padding-left"),this.styles.get("padding-right")],2),o=r[0],n=r[1];(o||n)&&(e=Math.max(e,this.length2em(o||"0"),this.length2em(n||"0")));var s=i(this.getAlignShift(),2),a=s[0],l=s[1];return a===t&&(l="left"===t?Math.max(e,l)-e:Math.min(-e,l)+e),[e,a,l]},e.prototype.getAlignShift=function(){return this.isTop?t.prototype.getAlignShift.call(this):[this.container.getChildAlign(this.containerI),0]},e.prototype.getWidth=function(){return this.pWidth||this.getBBox().w},e.prototype.getEqualRowHeight=function(){var t=this.getTableData(),e=t.H,r=t.D,o=Array.from(e.keys()).map((function(t){return e[t]+r[t]}));return Math.max.apply(Math,o)},e.prototype.getComputedWidths=function(){var t=this,e=this.getTableData().W,r=Array.from(e.keys()).map((function(r){return"number"==typeof t.cWidths[r]?t.cWidths[r]:e[r]}));return this.node.attributes.get("equalcolumns")&&(r=Array(r.length).fill(c.max(r))),r},e.prototype.getColumnWidths=function(){var t=this.node.attributes.get("width");if(this.node.attributes.get("equalcolumns"))return this.getEqualColumns(t);var e=this.getColumnAttributes("columnwidth",0);return"auto"===t?this.getColumnWidthsAuto(e):h.isPercent(t)?this.getColumnWidthsPercent(e):this.getColumnWidthsFixed(e,this.length2em(t))},e.prototype.getEqualColumns=function(t){var e,r=Math.max(1,this.numCols);if("auto"===t){var o=this.getTableData().W;e=c.max(o)}else if(h.isPercent(t))e=this.percent(1/r);else{var n=c.sum([].concat(this.cLines,this.cSpace))+2*this.fSpace[0];e=Math.max(0,this.length2em(t)-n)/r}return Array(this.numCols).fill(e)},e.prototype.getColumnWidthsAuto=function(t){var e=this;return t.map((function(t){return"auto"===t||"fit"===t?null:h.isPercent(t)?t:e.length2em(t)}))},e.prototype.getColumnWidthsPercent=function(t){var e=this,r=t.indexOf("fit")>=0,o=(r?this.getTableData():{W:null}).W;return Array.from(t.keys()).map((function(n){var i=t[n];return"fit"===i?null:"auto"===i?r?o[n]:null:h.isPercent(i)?i:e.length2em(i)}))},e.prototype.getColumnWidthsFixed=function(t,e){var r=this,o=Array.from(t.keys()),n=o.filter((function(e){return"fit"===t[e]})),i=o.filter((function(e){return"auto"===t[e]})),s=n.length||i.length,a=(s?this.getTableData():{W:null}).W,l=e-c.sum([].concat(this.cLines,this.cSpace))-2*this.fSpace[0],h=l;o.forEach((function(o){var n=t[o];h-="fit"===n||"auto"===n?a[o]:r.length2em(n,e)}));var u=s&&h>0?h/s:0;return o.map((function(e){var o=t[e];return"fit"===o?a[e]+u:"auto"===o?a[e]+(0===n.length?u:0):r.length2em(o,l)}))},e.prototype.getVerticalPosition=function(t,e){for(var r=this.node.attributes.get("equalrows"),o=this.getTableData(),n=o.H,s=o.D,a=r?this.getEqualRowHeight():0,l=this.getRowHalfSpacing(),h=this.fLine,c=0;c<t;c++)h+=l[c]+(r?a:n[c]+s[c])+l[c+1]+this.rLines[c];var u=i(r?[(a+n[t]-s[t])/2,(a-n[t]+s[t])/2]:[n[t],s[t]],2),p=u[0],d=u[1];return h+={top:0,center:l[t]+(p+d)/2,bottom:l[t]+p+d+l[t+1],baseline:l[t]+p,axis:l[t]+p-.25}[e]||0},e.prototype.getEmHalfSpacing=function(t,e){var r=this.em(t),o=this.addEm(e,2);return o.unshift(r),o.push(r),o},e.prototype.getRowHalfSpacing=function(){var t=this.rSpace.map((function(t){return t/2}));return t.unshift(this.fSpace[1]),t.push(this.fSpace[1]),t},e.prototype.getColumnHalfSpacing=function(){var t=this.cSpace.map((function(t){return t/2}));return t.unshift(this.fSpace[0]),t.push(this.fSpace[0]),t},e.prototype.getAlignmentRow=function(){var t=i(h.split(this.node.attributes.get("align")),2),e=t[0],r=t[1];if(null==r)return[e,null];var o=parseInt(r);return o<0&&(o+=this.numRows+1),[e,o<1||o>this.numRows?null:o-1]},e.prototype.getColumnAttributes=function(t,e){void 0===e&&(e=1);var r=this.numCols-e,o=this.getAttributeArray(t);if(0===o.length)return null;for(;o.length<r;)o.push(o[o.length-1]);return o.length>r&&o.splice(r),o},e.prototype.getRowAttributes=function(t,e){void 0===e&&(e=1);var r=this.numRows-e,o=this.getAttributeArray(t);if(0===o.length)return null;for(;o.length<r;)o.push(o[o.length-1]);return o.length>r&&o.splice(r),o},e.prototype.getAttributeArray=function(t){var e=this.node.attributes.get(t);return e?h.split(e):[this.node.attributes.getDefault(t)]},e.prototype.addEm=function(t,e){var r=this;return void 0===e&&(e=1),t?t.map((function(t){return r.em(t/e)})):null},e.prototype.convertLengths=function(t){var e=this;return t?t.map((function(t){return e.length2em(t)})):null},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtdMixin=void 0,e.CommonMtdMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.invalidateBBox=function(){this.bboxComputed=!1},e.prototype.getWrapWidth=function(t){var e=this.parent.parent,r=this.parent,o=this.node.childPosition()-(r.labeled?1:0);return"number"==typeof e.cWidths[o]?e.cWidths[o]:e.getTableData().W[o]},e.prototype.getChildAlign=function(t){return this.node.attributes.get("columnalign")},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtextMixin=void 0,e.CommonMtextMixin=function(t){var e;return(e=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getVariant=function(){var e=this.jax.options,r=this.jax.math.outputData,o=(!!r.merrorFamily||!!e.merrorFont)&&this.node.Parent.isKind("merror");if(r.mtextFamily||e.mtextFont||o){var n=this.node.attributes.get("mathvariant"),i=this.constructor.INHERITFONTS[n]||this.jax.font.getCssFont(n),s=i[0]||(o?r.merrorFamily||e.merrorFont:r.mtextFamily||e.mtextFont);this.variant=this.explicitVariant(s,i[2]?"bold":"",i[1]?"italic":"")}else t.prototype.getVariant.call(this)},e}(t)).INHERITFONTS={normal:["",!1,!1],bold:["",!1,!0],italic:["",!0,!1],"bold-italic":["",!0,!0]},e}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonScriptbaseMixin=void 0;e.CommonScriptbaseMixin=function(t){var e;return(e=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this,n=o.baseCore=o.childNodes[0];if(!n)return o;for(;1===n.childNodes.length&&(n.node.isKind("mrow")||n.node.isKind("TeXAtom")||n.node.isKind("mstyle")||n.node.isKind("mpadded")||n.node.isKind("mphantom")||n.node.isKind("semantics"));)if(!(n=n.childNodes[0]))return o;return"noIC"in n?(o.baseCore=n,o.constructor.useIC||(n.noIC=!0),o):o}return n(e,t),Object.defineProperty(e.prototype,"baseChild",{get:function(){return this.childNodes[this.node.base]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[1]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.baseChild.getBBox(),o=this.script.getBBox(),n=i(this.getOffset(r,o),2),s=n[0],a=n[1];t.append(r),t.combine(o,t.w+s,a),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(e)},e.prototype.coreIC=function(){var t=this.baseCore.getBBox();return t.ic?1.05*t.ic+.05:0},e.prototype.coreScale=function(){for(var t=this.baseChild.getBBox().rscale,e=this.baseChild;(e.node.isKind("mstyle")||e.node.isKind("mrow")||e.node.isKind("TeXAtom"))&&1===e.childNodes.length;)t*=(e=e.childNodes[0]).getBBox().rscale;return t},e.prototype.isCharBase=function(){for(var t=this.baseChild;(t.node.isKind("mstyle")||t.node.isKind("mrow"))&&1===t.childNodes.length;)t=t.childNodes[0];return(t.node.isKind("mo")||t.node.isKind("mi")||t.node.isKind("mn"))&&1===t.bbox.rscale&&1===Array.from(t.getText()).length&&!t.node.attributes.get("largeop")},e.prototype.getOffset=function(t,e){return[0,0]},e.prototype.getV=function(t,e){var r=this.font.params,o=this.length2em(this.node.attributes.get("subscriptshift"),r.sub1);return Math.max(this.isCharBase()?0:t.d*t.rscale+r.sub_drop*e.rscale,o,e.h*e.rscale-.8*r.x_height)},e.prototype.getU=function(t,e){var r=this.font.params,o=this.node.attributes.getList("displaystyle","superscriptshift"),n=this.node.getProperty("texprimestyle")?r.sup3:o.displaystyle?r.sup1:r.sup2,i=this.length2em(o.superscriptshift,n);return Math.max(this.isCharBase()?0:t.h*t.rscale-r.sup_drop*e.rscale,i,e.d*e.rscale+1/4*r.x_height)},e.prototype.hasMovableLimits=function(){var t=this.node.attributes.get("displaystyle"),e=this.baseChild.coreMO().node;return!t&&!!e.attributes.get("movablelimits")},e.prototype.getOverKU=function(t,e){var r=this.node.attributes.get("accent"),o=this.font.params,n=e.d*e.rscale,i=(r?o.rule_thickness:Math.max(o.big_op_spacing1,o.big_op_spacing3-Math.max(0,n)))-(this.baseChild.node.isKind("munderover")?.1:0);return[i,t.h*t.rscale+i+n]},e.prototype.getUnderKV=function(t,e){var r=this.node.attributes.get("accentunder"),o=this.font.params,n=e.h*e.rscale,i=(r?o.rule_thickness:Math.max(o.big_op_spacing2,o.big_op_spacing4-n))-(this.baseChild.node.isKind("munderover")?.1:0);return[i,-(t.d*t.rscale+i+n)]},e.prototype.getDeltaW=function(t,e){var r,o,n,i;void 0===e&&(e=[0,0,0]);var l=this.node.attributes.get("align"),h=t.map((function(t){return t.w*t.rscale})),c=Math.max.apply(Math,s(h)),u=[],p=0;try{for(var d=a(h.keys()),f=d.next();!f.done;f=d.next()){u[v=f.value]=("center"===l?(c-h[v])/2:"right"===l?c-h[v]:0)+e[v],u[v]<p&&(p=-u[v])}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}if(p)try{for(var y=a(u.keys()),m=y.next();!m.done;m=y.next()){var v;u[v=m.value]+=p}}catch(t){n={error:t}}finally{try{m&&!m.done&&(i=y.return)&&i.call(y)}finally{if(n)throw n.error}}return u},e.prototype.getDelta=function(t){void 0===t&&(t=!1);var e=this.node.attributes.get("accent")&&!t?this.baseChild.coreMO().bbox.sk:0;return(1.5*this.baseCore.bbox.ic/2+e)*this.coreScale()},e.prototype.stretchChildren=function(){var t,e,r,o,n,i,s=[];try{for(var l=a(this.childNodes),h=l.next();!h.done;h=l.next()){(M=h.value).canStretch(2)&&s.push(M)}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}var c=s.length,u=this.childNodes.length;if(c&&u>1){var p=0,d=c>1&&c===u;try{for(var f=a(this.childNodes),y=f.next();!y.done;y=f.next()){var m=0===(M=y.value).stretch.dir;if(d||m){var v=M.getBBox(m),b=v.w,x=v.rscale;b*x>p&&(p=b*x)}}}catch(t){r={error:t}}finally{try{y&&!y.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}try{for(var g=a(s),_=g.next();!_.done;_=g.next()){var M;(M=_.value).coreMO().getStretchedVariant([p/M.bbox.rscale])}}catch(t){n={error:t}}finally{try{_&&!_.done&&(i=g.return)&&i.call(g)}finally{if(n)throw n.error}}}},e}(t)).useIC=!1,e}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonSemanticsMixin=void 0,e.CommonSemanticsMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.computeBBox=function(t,e){if(void 0===e&&(e=!1),this.childNodes.length){var r=this.childNodes[0].getBBox(),o=r.w,n=r.h,i=r.d;t.w=o,t.h=n,t.d=i}},e}(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGWrapperFactory=void 0;var i=r(19),s=r(42),a=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.jax=null,e}return n(e,t),e.defaultNodes=s.SVGWrappers,e}(i.CommonWrapperFactory);e.SVGWrapperFactory=a},function(t,e,r){"use strict";var o;Object.defineProperty(e,"__esModule",{value:!0}),e.SVGWrappers=void 0;var n=r(0),i=r(43),s=r(44),a=r(45),l=r(46),h=r(47),c=r(48),u=r(49),p=r(50),d=r(51),f=r(52),y=r(53),m=r(54),v=r(13),b=r(55),x=r(56),g=r(8),_=r(58),M=r(59),S=r(60),w=r(61),O=r(62),C=r(63),B=r(64),j=r(66),P=r(67),V=r(68),A=r(69);e.SVGWrappers=((o={})[i.SVGmath.kind]=i.SVGmath,o[s.SVGmrow.kind]=s.SVGmrow,o[s.SVGinferredMrow.kind]=s.SVGinferredMrow,o[a.SVGmi.kind]=a.SVGmi,o[l.SVGmo.kind]=l.SVGmo,o[h.SVGmn.kind]=h.SVGmn,o[c.SVGms.kind]=c.SVGms,o[u.SVGmtext.kind]=u.SVGmtext,o[p.SVGmerror.kind]=p.SVGmerror,o[d.SVGmspace.kind]=d.SVGmspace,o[f.SVGmpadded.kind]=f.SVGmpadded,o[y.SVGmphantom.kind]=y.SVGmphantom,o[m.SVGmfrac.kind]=m.SVGmfrac,o[v.SVGmsqrt.kind]=v.SVGmsqrt,o[b.SVGmroot.kind]=b.SVGmroot,o[x.SVGmfenced.kind]=x.SVGmfenced,o[g.SVGmsub.kind]=g.SVGmsub,o[g.SVGmsup.kind]=g.SVGmsup,o[g.SVGmsubsup.kind]=g.SVGmsubsup,o[_.SVGmunder.kind]=_.SVGmunder,o[_.SVGmover.kind]=_.SVGmover,o[_.SVGmunderover.kind]=_.SVGmunderover,o[M.SVGmmultiscripts.kind]=M.SVGmmultiscripts,o[S.SVGmtable.kind]=S.SVGmtable,o[w.SVGmtr.kind]=w.SVGmtr,o[w.SVGmlabeledtr.kind]=w.SVGmlabeledtr,o[O.SVGmtd.kind]=O.SVGmtd,o[C.SVGmaction.kind]=C.SVGmaction,o[B.SVGmenclose.kind]=B.SVGmenclose,o[j.SVGsemantics.kind]=j.SVGsemantics,o[j.SVGannotation.kind]=j.SVGannotation,o[j.SVGannotationXML.kind]=j.SVGannotationXML,o[j.SVGxml.kind]=j.SVGxml,o[P.SVGmglyph.kind]=P.SVGmglyph,o[V.SVGTeXAtom.kind]=V.SVGTeXAtom,o[A.SVGTextNode.kind]=A.SVGTextNode,o[n.SVGWrapper.kind]=n.SVGWrapper,o)},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmath=void 0;var a=r(0),l=r(22),h=r(79),c=r(1),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){t.prototype.toSVG.call(this,e);var r=this.adaptor;"block"===this.node.attributes.get("display")&&(r.setAttribute(this.jax.container,"display","true"),this.handleDisplay()),this.jax.document.options.internalSpeechTitles&&this.handleSpeech()},e.prototype.handleDisplay=function(){var t=i(this.getAlignShift(),2),e=t[0],r=t[1];if("center"!==e&&this.adaptor.setAttribute(this.jax.container,"justify",e),this.bbox.pwidth===c.BBox.fullWidth){if(this.adaptor.setAttribute(this.jax.container,"width","full"),this.jax.table){var o=this.jax.table.getBBox(),n=o.L,s=o.w,a=o.R;"right"===e?a=Math.max(a||-r,-r):"left"===e?n=Math.max(n||r,r):"center"===e&&(s+=2*Math.abs(r)),this.jax.minwidth=Math.max(0,n+s+a)}}else this.jax.shift=r},e.prototype.handleSpeech=function(){var t,e,r=this.adaptor,o=this.node.attributes,n=o.get("aria-label")||o.get("data-semantic-speech");if(n){var i=this.getTitleID(),a=this.svg("title",{id:i},[this.text(n)]);r.insert(a,r.firstChild(this.element)),r.setAttribute(this.element,"aria-labeledby",i),r.removeAttribute(this.element,"aria-label");try{for(var l=s(this.childNodes[0].childNodes),h=l.next();!h.done;h=l.next()){var c=h.value;r.setAttribute(c.element,"aria-hidden","true")}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}}},e.prototype.getTitleID=function(){return"mjx-svg-title-"+String(this.jax.options.titleID++)},e.prototype.setChildPWidths=function(e,r,o){return void 0===r&&(r=null),void 0===o&&(o=!0),t.prototype.setChildPWidths.call(this,e,this.parent?r:this.metrics.containerWidth/this.jax.pxPerEm,!1)},e.kind=h.MmlMath.prototype.kind,e.styles={'mjx-container[jax="SVG"][display="true"]':{display:"block","text-align":"center",margin:"1em 0"},'mjx-container[jax="SVG"][display="true"][width="full"]':{display:"flex"},'mjx-container[jax="SVG"][justify="left"]':{"text-align":"left"},'mjx-container[jax="SVG"][justify="right"]':{"text-align":"right"}},e}(l.CommonMathMixin(a.SVGWrapper));e.SVGmath=u},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGinferredMrow=e.SVGmrow=void 0;var i=r(0),s=r(11),a=r(11),l=r(80),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.node.isInferred?this.element=t:this.standardSVGnode(t);this.addChildren(e)},e.kind=l.MmlMrow.prototype.kind,e}(s.CommonMrowMixin(i.SVGWrapper));e.SVGmrow=h;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=l.MmlInferredMrow.prototype.kind,e}(a.CommonInferredMrowMixin(h));e.SVGinferredMrow=c},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmi=void 0;var i=r(0),s=r(27),a=r(81),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=a.MmlMi.prototype.kind,e}(s.CommonMiMixin(i.SVGWrapper));e.SVGmi=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmo=void 0;var s=r(0),a=r(30),l=r(82),h=r(1),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){var r=this.node.attributes,o=r.get("symmetric")&&2!==this.stretch.dir,n=0!==this.stretch.dir;n&&null===this.size&&this.getStretchedVariant([]);var i=this.standardSVGnode(e);if(n&&this.size<0)this.stretchSVG();else{if(o||r.get("largeop")){var s=h.BBox.empty();t.prototype.computeBBox.call(this,s);var a=this.fixed((s.d-s.h)/2+this.font.params.axis_height);"0"!==a&&this.adaptor.setAttribute(i,"transform","translate(0 "+a+")")}this.addChildren(i)}},e.prototype.stretchSVG=function(){var t=this.stretch.stretch,e=this.getBBox();1===this.stretch.dir?this.stretchVertical(t,e):this.stretchHorizontal(t,e)},e.prototype.stretchVertical=function(t,e){var r=e.h,o=e.d,n=e.w,s=this.addTop(t[0],r,n),a=this.addBot(t[2],o,n);if(4===t.length){var l=i(this.addMidV(t[3],n),2),h=l[0],c=l[1];this.addExtV(t[1],r,0,s,h,n),this.addExtV(t[1],0,o,c,a,n)}else this.addExtV(t[1],r,o,s,a,n)},e.prototype.stretchHorizontal=function(t,e){var r=e.w,o=this.addLeft(t[0]),n=this.addRight(t[2],r);if(4===t.length){var s=i(this.addMidH(t[3],r),2),a=s[0],l=s[1],h=r/2;this.addExtH(t[1],h,o,h-a),this.addExtH(t[1],h,l-h,n,h)}else this.addExtH(t[1],r,o,n)},e.prototype.getChar=function(t){var e=this.font.getChar("-size4",t)||[0,0,0,null];return[e[0],e[1],e[2],e[3]||{}]},e.prototype.addGlyph=function(t,e,r,o){return void 0===o&&(o=null),this.placeChar(t,e,r,o||this.element,"-size4")},e.prototype.addTop=function(t,e,r){if(!t)return 0;var o=i(this.getChar(t),3),n=o[0],s=o[1],a=o[2];return this.addGlyph(t,(r-a)/2,e-n),n+s},e.prototype.addExtV=function(t,e,r,o,n,s){var a=this;if(t){o=Math.max(0,o-.1),n=Math.max(0,n-.1);var l=this.adaptor,h=i(this.getChar(t),3),c=h[0],u=h[1],p=h[2],d=e+r-o-n,f=1.5*d/(c+u),y=(f*(c-u)-d)/2;if(!(d<=0)){var m=this.svg("svg",{width:this.fixed(p),height:this.fixed(d),y:this.fixed(n-r),x:this.fixed((s-p)/2),viewBox:[0,y,p,d].map((function(t){return a.fixed(t)})).join(" ")});this.addGlyph(t,0,0,m);var v=l.lastChild(m);l.setAttribute(v,"transform","scale(1, "+this.jax.fixed(f)+")"),l.append(this.element,m)}}},e.prototype.addBot=function(t,e,r){if(!t)return 0;var o=i(this.getChar(t),3),n=o[0],s=o[1],a=o[2];return this.addGlyph(t,(r-a)/2,s-e),n+s},e.prototype.addMidV=function(t,e){if(!t)return[0,0];var r=i(this.getChar(t),3),o=r[0],n=r[1],s=r[2],a=(n-o)/2+this.font.params.axis_height;return this.addGlyph(t,(e-s)/2,a),[o+a,n-a]},e.prototype.addLeft=function(t){return t?this.addGlyph(t,0,0):0},e.prototype.addExtH=function(t,e,r,o,n){var s=this;if(void 0===n&&(n=0),t){o=Math.max(0,o-.1),r=Math.max(0,r-.1);var a=i(this.getChar(t),3),l=a[0],h=a[1],c=a[2],u=e-r-o,p=l+h+.2,d=u/c*1.5,f=-(h+.1);if(!(u<=0)){var y=this.svg("svg",{width:this.fixed(u),height:this.fixed(p),x:this.fixed(n+r),y:this.fixed(f),viewBox:[(d*c-u)/2,f,u,p].map((function(t){return s.fixed(t)})).join(" ")});this.addGlyph(t,0,0,y);var m=this.adaptor.lastChild(y);this.adaptor.setAttribute(m,"transform","scale("+this.jax.fixed(d)+", 1)"),this.adaptor.append(this.element,y)}}},e.prototype.addRight=function(t,e){if(!t)return 0;var r=this.getChar(t)[2];return this.addGlyph(t,e-r,0)},e.prototype.addMidH=function(t,e){if(!t)return[0,0];var r=this.getChar(t)[2];return this.addGlyph(t,(e-r)/2,0),[(e-r)/2,(e+r)/2]},e.kind=l.MmlMo.prototype.kind,e}(a.CommonMoMixin(s.SVGWrapper));e.SVGmo=c},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmn=void 0;var i=r(0),s=r(29),a=r(83),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=a.MmlMn.prototype.kind,e}(s.CommonMnMixin(i.SVGWrapper));e.SVGmn=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGms=void 0;var i=r(0),s=r(33),a=r(84),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=a.MmlMs.prototype.kind,e}(s.CommonMsMixin(i.SVGWrapper));e.SVGms=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmtext=void 0;var i=r(0),s=r(38),a=r(85),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=a.MmlMtext.prototype.kind,e}(s.CommonMtextMixin(i.SVGWrapper));e.SVGmtext=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmerror=void 0;var i=r(0),s=r(86),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=this.getBBox(),o=r.h,n=r.d,i=r.w;this.adaptor.append(this.element,this.svg("rect",{"data-background":!0,width:this.fixed(i),height:this.fixed(o+n),y:this.fixed(-n)}));var s=this.node.attributes.get("title");s&&this.adaptor.append(this.element,this.svg("title",{},[this.adaptor.text(s)])),this.addChildren(e)},e.kind=s.MmlMerror.prototype.kind,e.styles={'g[data-mml-node="merror"] > g':{fill:"red",stroke:"red"},'g[data-mml-node="merror"] > rect[data-background]':{fill:"yellow",stroke:"none"}},e}(i.SVGWrapper);e.SVGmerror=a},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmspace=void 0;var i=r(0),s=r(34),a=r(87),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=a.MmlMspace.prototype.kind,e}(s.CommonMspaceMixin(i.SVGWrapper));e.SVGmspace=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmpadded=void 0;var s=r(0),a=r(31),l=r(88),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=i(this.getDimens(),9),o=r[5],n=r[6],s=r[7],a=r[8],l=this.node.attributes.get("data-align")||"left",h=n+a-(o<0&&"left"!==l?"center"===l?o/2:o:0);(h||s)&&(e=this.adaptor.append(e,this.svg("g")),this.place(h,s,e)),this.addChildren(e)},e.kind=l.MmlMpadded.prototype.kind,e}(a.CommonMpaddedMixin(s.SVGWrapper));e.SVGmpadded=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmphantom=void 0;var i=r(0),s=r(89),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){this.standardSVGnode(t)},e.kind=s.MmlMphantom.prototype.kind,e}(i.SVGWrapper);e.SVGmphantom=a},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmfrac=void 0;var s=r(0),a=r(25),l=r(90),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){this.standardSVGnode(t);var e=this.node.attributes.getList("linethickness","bevelled"),r=e.linethickness,o=e.bevelled,n=this.isDisplay();if(o)this.makeBevelled(n);else{var i=this.length2em(String(r),.06);0===i?this.makeAtop(n):this.makeFraction(n,i)}},e.prototype.makeFraction=function(t,e){var r=this.element,o=this.node.attributes.getList("numalign","denomalign"),n=o.numalign,s=o.denomalign,a=i(this.childNodes,2),l=a[0],h=a[1],c=l.getBBox(),u=h.getBBox(),p=this.font.params,d=p.axis_height,f=this.node.getProperty("withDelims")?0:p.nulldelimiterspace,y=Math.max((c.L+c.w+c.R)*c.rscale,(u.L+u.w+u.R)*u.rscale),m=this.getAlignX(y,c,n)+.1+f,v=this.getAlignX(y,u,s)+.1+f,b=this.getTUV(t,e),x=b.T,g=b.u,_=b.v;l.toSVG(r),l.place(m,d+x+Math.max(c.d*c.rscale,g)),h.toSVG(r),h.place(v,d-x-Math.max(u.h*u.rscale,_)),this.adaptor.append(r,this.svg("rect",{width:this.fixed(y+.2),height:this.fixed(e),x:this.fixed(f),y:this.fixed(d-e/2)}))},e.prototype.makeAtop=function(t){var e=this.element,r=this.node.attributes.getList("numalign","denomalign"),o=r.numalign,n=r.denomalign,s=i(this.childNodes,2),a=s[0],l=s[1],h=a.getBBox(),c=l.getBBox(),u=this.font.params,p=this.node.getProperty("withDelims")?0:u.nulldelimiterspace,d=Math.max((h.L+h.w+h.R)*h.rscale,(c.L+c.w+c.R)*c.rscale),f=this.getAlignX(d,h,o)+p,y=this.getAlignX(d,c,n)+p,m=this.getUVQ(t),v=m.u,b=m.v;a.toSVG(e),a.place(f,v),l.toSVG(e),l.place(y,-b)},e.prototype.makeBevelled=function(t){var e=this.element,r=i(this.childNodes,2),o=r[0],n=r[1],s=this.getBevelData(t),a=s.u,l=s.v,h=s.delta,c=s.nbox,u=s.dbox,p=(c.L+c.w+c.R)*c.rscale;o.toSVG(e),this.bevel.toSVG(e),n.toSVG(e),o.place(c.L*c.rscale,a),this.bevel.place(p-h/2,0),n.place(p+this.bevel.getBBox().w+u.L*u.rscale-h,l)},e.kind=l.MmlMfrac.prototype.kind,e}(a.CommonMfracMixin(s.SVGWrapper));e.SVGmfrac=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmroot=void 0;var s=r(13),a=r(32),l=r(92),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.addRoot=function(t,e,r,o){e.toSVG(t);var n=i(this.getRootDimens(r,o),3),s=n[0],a=n[1],l=n[2],h=e.getBBox();e.place(l*h.rscale,a),this.dx=s},e.kind=l.MmlMroot.prototype.kind,e}(a.CommonMrootMixin(s.SVGmsqrt));e.SVGmroot=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmfenced=void 0;var s=r(0),a=r(24),l=r(93),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t);this.setChildrenParent(this.mrow),this.mrow.toSVG(e),this.setChildrenParent(this)},e.prototype.setChildrenParent=function(t){var e,r;try{for(var o=i(this.childNodes),n=o.next();!n.done;n=o.next()){n.value.parent=t}}catch(t){e={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},e.kind=l.MmlMfenced.prototype.kind,e}(a.CommonMfencedMixin(s.SVGWrapper));e.SVGmfenced=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGscriptbase=void 0;var s=r(0),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=this.baseChild.getBBox(),o=this.script.getBBox(),n=i(this.getOffset(r,o),2),s=n[0],a=n[1];this.baseChild.toSVG(e),this.script.toSVG(e),this.script.place(r.w*r.rscale+s,a)},e.kind="scriptbase",e.useIC=!1,e}(r(39).CommonScriptbaseMixin(s.SVGWrapper));e.SVGscriptbase=a},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmunderover=e.SVGmover=e.SVGmunder=void 0;var s=r(8),a=r(7),l=r(7),h=r(7),c=r(95),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){if(this.hasMovableLimits())t.prototype.toSVG.call(this,e);else{var r=this.standardSVGnode(e),o=i([this.baseChild,this.script],2),n=o[0],s=o[1],a=i([n.getBBox(),s.getBBox()],2),l=a[0],h=a[1];n.toSVG(r),s.toSVG(r);var c=this.getDelta(!0),u=this.getUnderKV(l,h)[1],p=i(this.getDeltaW([l,h],[0,-c]),2),d=p[0],f=p[1];n.place(d,0),s.place(f,u)}},e.kind=c.MmlMunder.prototype.kind,e.useIC=!0,e}(a.CommonMunderMixin(s.SVGmsub));e.SVGmunder=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){if(this.hasMovableLimits())t.prototype.toSVG.call(this,e);else{var r=this.standardSVGnode(e),o=i([this.baseChild,this.script],2),n=o[0],s=o[1],a=i([n.getBBox(),s.getBBox()],2),l=a[0],h=a[1];n.toSVG(r),s.toSVG(r);var c=this.getDelta(),u=this.getOverKU(l,h)[1],p=i(this.getDeltaW([l,h],[0,c]),2),d=p[0],f=p[1];n.place(d,0),s.place(f,u)}},e.kind=c.MmlMover.prototype.kind,e.useIC=!0,e}(l.CommonMoverMixin(s.SVGmsup));e.SVGmover=p;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){if(this.hasMovableLimits())t.prototype.toSVG.call(this,e);else{var r=this.standardSVGnode(e),o=i([this.baseChild,this.overChild,this.underChild],3),n=o[0],s=o[1],a=o[2],l=i([n.getBBox(),s.getBBox(),a.getBBox()],3),h=l[0],c=l[1],u=l[2];n.toSVG(r),a.toSVG(r),s.toSVG(r);var p=this.getDelta(),d=this.getOverKU(h,c)[1],f=this.getUnderKV(h,u)[1],y=i(this.getDeltaW([h,u,c],[0,-p,p]),3),m=y[0],v=y[1],b=y[2];n.place(m,0),a.place(v,f),s.place(b,d)}},e.kind=c.MmlMunderover.prototype.kind,e.useIC=!0,e}(h.CommonMunderoverMixin(s.SVGmsubsup));e.SVGmunderover=d},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmmultiscripts=void 0;var s=r(8),a=r(28),l=r(96),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=this.getScriptData(),o=this.combinePrePost(r.sub,r.psub),n=this.combinePrePost(r.sup,r.psup),s=i(this.getUVQ(r.base,o,n),2),a=s[0],l=s[1],h=0;r.numPrescripts&&(h=this.addScripts(.05,a,l,!0,this.firstPrescript,r.numPrescripts));var c=this.baseChild;c.toSVG(e),c.place(h,0),h+=c.getBBox().w,r.numScripts&&this.addScripts(h,a,l,!1,1,r.numScripts)},e.prototype.addScripts=function(t,e,r,o,n,s){var a=this.adaptor,l=a.append(this.element,this.svg("g")),h=a.append(this.element,this.svg("g"));this.place(t,e,l),this.place(t,r,h);for(var c=n+2*s,u=0;n<c;){var p=i([this.childNodes[n++],this.childNodes[n++]],2),d=p[0],f=p[1],y=i([d.getBBox(),f.getBBox()],2),m=y[0],v=y[1],b=i([m.rscale,v.rscale],2),x=b[0],g=b[1],_=Math.max(m.w*x,v.w*g);d.toSVG(h),f.toSVG(l),d.place(u+(o?_-m.w*x:0),0),f.place(u+(o?_-v.w*g:0),0),u+=_}return t+u},e.kind=l.MmlMmultiscripts.prototype.kind,e}(a.CommonMmultiscriptsMixin(s.SVGmsubsup));e.SVGmmultiscripts=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmtable=void 0;var a=r(0),l=r(36),h=r(97),c=function(t){function e(e,r,o){void 0===o&&(o=null);var n=t.call(this,e,r,o)||this,i={"data-labels":!0};return n.isTop&&(i.transform="matrix(1 0 0 -1 0 0)"),n.labels=n.svg("g",i),n}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t);this.placeRows(e),this.handleColumnLines(e),this.handleRowLines(e),this.handleFrame(e);var r=this.handlePWidth(e);this.handleLabels(e,t,r)},e.prototype.placeRows=function(t){for(var e,r,o,n=this.node.attributes.get("equalrows"),a=this.getTableData(),l=a.H,h=a.D,c=this.getEqualRowHeight(),u=this.getRowHalfSpacing(),p=s([this.fLine],this.rLines,[this.fLine]),d=this.getBBox().h-p[0],f=0;f<this.numRows;f++){var y=this.childNodes[f];e=i(this.getRowHD(n,c,l[f],h[f]),2),y.H=e[0],y.D=e[1],r=i([u[f],u[f+1]],2),y.tSpace=r[0],y.bSpace=r[1],o=i([p[f],p[f+1]],2),y.tLine=o[0],y.bLine=o[1],y.toSVG(t),y.place(0,d-u[f]-y.H),d-=u[f]+y.H+y.D+u[f+1]+p[f+1]}},e.prototype.getRowHD=function(t,e,r,o){return t?[(e+r-o)/2,(e-r+o)/2]:[r,o]},e.prototype.handleColor=function(){t.prototype.handleColor.call(this);var e=this.adaptor.firstChild(this.element);e&&this.adaptor.setAttribute(e,"width",this.fixed(this.getWidth()))},e.prototype.handleColumnLines=function(t){if("none"!==this.node.attributes.get("columnlines")){var e=this.getColumnAttributes("columnlines");if(e)for(var r=this.getColumnHalfSpacing(),o=this.cLines,n=this.getComputedWidths(),i=this.fLine,s=0;s<e.length;s++)i+=r[s]+n[s]+r[s+1],"none"!==e[s]&&this.adaptor.append(t,this.makeVLine(i,e[s],o[s])),i+=o[s]}},e.prototype.handleRowLines=function(t){if("none"!==this.node.attributes.get("rowlines")){var e=this.getRowAttributes("rowlines");if(e)for(var r=this.node.attributes.get("equalrows"),o=this.getTableData(),n=o.H,s=o.D,a=this.getEqualRowHeight(),l=this.getRowHalfSpacing(),h=this.rLines,c=this.getBBox().h-this.fLine,u=0;u<e.length;u++){var p=i(this.getRowHD(r,a,n[u],s[u]),2),d=p[0],f=p[1];c-=l[u]+d+f+l[u+1],"none"!==e[u]&&this.adaptor.append(t,this.makeHLine(c,e[u],h[u])),c-=h[u]}}},e.prototype.handleFrame=function(t){if(this.frame){var e=this.getBBox(),r=e.h,o=e.d,n=e.w,i=this.node.attributes.get("frame");this.adaptor.append(t,this.makeFrame(n,r,o,i))}},e.prototype.handlePWidth=function(t){if(!this.pWidth)return 0;var e=this.getBBox(),r=e.w,o=e.L,n=e.R,i=o+this.pWidth+n,s=this.getAlignShift()[0],a=Math.max(this.isTop?i:0,this.container.getWrapWidth(this.containerI))-o-n,l=r-(this.pWidth>a?a:this.pWidth),h="left"===s?0:"right"===s?l:l/2;if(h){var c=this.svg("g",{},this.adaptor.childNodes(t));this.place(h,0,c),this.adaptor.append(t,c)}return h},e.prototype.lineClass=function(t){return"mjx-"+t},e.prototype.makeFrame=function(t,e,r,o){var n=this.fLine;return this.svg("rect",this.setLineThickness(n,o,{"data-frame":!0,class:this.lineClass(o),width:this.fixed(t-n),height:this.fixed(e+r-n),x:this.fixed(n/2),y:this.fixed(n/2-r)}))},e.prototype.makeVLine=function(t,e,r){var o=this.getBBox(),n=o.h,i=o.d,s="dotted"===e?r/2:0,a=this.fixed(t+r/2);return this.svg("line",this.setLineThickness(r,e,{"data-line":"v",class:this.lineClass(e),x1:a,y1:this.fixed(s-i),x2:a,y2:this.fixed(n-s)}))},e.prototype.makeHLine=function(t,e,r){var o=this.getBBox().w,n="dotted"===e?r/2:0,i=this.fixed(t-r/2);return this.svg("line",this.setLineThickness(r,e,{"data-line":"h",class:this.lineClass(e),x1:this.fixed(n),y1:i,x2:this.fixed(o-n),y2:i}))},e.prototype.setLineThickness=function(t,e,r){return.07!==t&&(r["stroke-thickness"]=this.fixed(t),"solid"!==e&&(r["stroke-dasharray"]=("dotted"===e?"0,":"")+this.fixed(2*t))),r},e.prototype.handleLabels=function(t,e,r){if(this.hasLabels){var o=this.labels,n=this.node.attributes.get("side");this.spaceLabels(),this.isTop?this.topTable(t,o,n):this.subTable(t,o,n,r)}},e.prototype.spaceLabels=function(){for(var t=this.adaptor,e=this.getBBox().h,r=this.getTableData().L,o=this.getRowHalfSpacing(),n=e-this.fLine,i=t.firstChild(this.labels),s=0;s<this.numRows;s++){var a=this.childNodes[s];if(a.node.isKind("mlabeledtr")){var l=a.childNodes[0];n-=o[s]+a.H,a.placeCell(l,{x:0,y:n,w:r,lSpace:0,rSpace:0,lLine:0,rLine:0}),n-=a.D+o[s+1]+this.rLines[s],i=t.next(i)}else n-=o[s]+a.H+a.D+o[s+1]+this.rLines[s]}},e.prototype.topTable=function(t,e,r){var o=this.adaptor,n=this.getBBox(),s=n.h,a=n.d,l=n.w,h=n.L,c=n.R,u=h+(this.pWidth||l)+c,p=this.getTableData().L,d=i(this.getPadAlignShift(r),3),f=d[1],y=d[2]+("right"===f?-u:"center"===f?-u/2:0)+h,m="matrix(1 0 0 -1 0 0)",v="scale("+this.jax.fixed(1e3*this.font.params.x_height/this.metrics.ex,2)+")",b="translate(0 "+this.fixed(s)+") "+m+" "+v,x=this.svg("svg",{"data-table":!0,preserveAspectRatio:"left"===f?"xMinYMid":"right"===f?"xMaxYMid":"xMidYMid",viewBox:[this.fixed(-y),this.fixed(-s),1,this.fixed(s+a)].join(" ")},[this.svg("g",{transform:m},o.childNodes(t))]);e=this.svg("svg",{"data-labels":!0,preserveAspectRatio:"left"===r?"xMinYMid":"xMaxYMid",viewBox:["left"===r?0:this.fixed(p),this.fixed(-s),1,this.fixed(s+a)].join(" ")},[e]),o.append(t,this.svg("g",{transform:b},[x,e])),this.place(-h,0,t)},e.prototype.subTable=function(t,e,r,o){var n=this.adaptor,i=this.getBBox(),s=i.w,a=i.L,l=i.R,h=a+(this.pWidth||s)+l,c=this.getTableData().L,u=this.getAlignShift()[0],p=Math.max(h,this.container.getWrapWidth(this.containerI));this.place("left"===r?("left"===u?0:"right"===u?h-p+o:(h-p)/2+o)-a:("left"===u?p:"right"===u?h+o:(p+h)/2+o)-a-c,0,e),n.append(t,e)},e.kind=h.MmlMtable.prototype.kind,e.styles={'g[data-mml-node="mtable"] > line[data-line]':{"stroke-width":"70px",fill:"none"},'g[data-mml-node="mtable"] > rect[data-frame]':{"stroke-width":"70px",fill:"none"},'g[data-mml-node="mtable"] > .mjx-dashed':{"stroke-dasharray":"140"},'g[data-mml-node="mtable"] > .mjx-dotted':{"stroke-linecap":"round","stroke-dasharray":"0,140"},'g[data-mml-node="mtable"] > g > svg':{overflow:"visible"}},e}(l.CommonMtableMixin(a.SVGWrapper));e.SVGmtable=c},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmlabeledtr=e.SVGmtr=void 0;var a=r(0),l=r(12),h=r(12),c=r(98),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t);this.placeCells(e),this.placeColor()},e.prototype.placeCells=function(t){for(var e=this.parent.getColumnHalfSpacing(),r=s([this.parent.fLine],this.parent.cLines,[this.parent.fLine]),o=this.parent.getComputedWidths(),n=r[0],i=0;i<this.numCells;i++){var a=this.getChild(i);a.toSVG(t),n+=this.placeCell(a,{x:n,y:0,lSpace:e[i],rSpace:e[i+1],w:o[i],lLine:r[i],rLine:r[i+1]})}},e.prototype.placeCell=function(t,e){var r=e.x,o=e.y,n=e.lSpace,s=e.w,a=e.rSpace,l=e.lLine,h=e.rLine,c=i(t.placeCell(r+n,o,s,this.H,this.D),2),u=c[0],p=c[1],d=n+s+a,f=i([this.H+this.tSpace,this.D+this.bSpace],2),y=f[0],m=f[1];return t.placeColor(-(u+n+l/2),-(m+this.bLine/2+p),d+(l+h)/2,y+m+(this.tLine+this.bLine)/2),d+h},e.prototype.placeColor=function(){var t=this.adaptor,e=t.firstChild(this.element);if(e&&"rect"===t.kind(e)&&t.getAttribute(e,"data-bgcolor")){var r=i([this.tLine/2,this.bLine/2],2),o=r[0],n=r[1],s=i([this.tSpace,this.bSpace],2),a=s[0],l=s[1],h=i([this.H,this.D],2),c=h[0],u=h[1];t.setAttribute(e,"y",this.fixed(-(u+l+n))),t.setAttribute(e,"width",this.fixed(this.parent.getWidth())),t.setAttribute(e,"height",this.fixed(o+a+c+u+l+n))}},e.kind=c.MmlMtr.prototype.kind,e}(l.CommonMtrMixin(a.SVGWrapper));e.SVGmtr=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){t.prototype.toSVG.call(this,e);var r=this.childNodes[0];r&&r.toSVG(this.parent.labels)},e.kind=c.MmlMlabeledtr.prototype.kind,e}(h.CommonMlabeledtrMixin(u));e.SVGmlabeledtr=p},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmtd=void 0;var i=r(0),s=r(37),a=r(99),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.placeCell=function(t,e,r,o,n){var i=this.getBBox(),s=Math.max(i.h,.75),a=Math.max(i.d,.25),l=this.node.attributes.get("columnalign"),h=this.node.attributes.get("rowalign"),c=this.getAlignX(r,i,l),u=this.getAlignY(o,n,s,a,h);return this.place(t+c,e+u),[c,u]},e.prototype.placeColor=function(t,e,r,o){var n=this.adaptor,i=n.firstChild(this.element);i&&"rect"===n.kind(i)&&n.getAttribute(i,"data-bgcolor")&&(n.setAttribute(i,"x",this.fixed(t)),n.setAttribute(i,"y",this.fixed(e)),n.setAttribute(i,"width",this.fixed(r)),n.setAttribute(i,"height",this.fixed(o)))},e.kind=a.MmlMtd.prototype.kind,e}(s.CommonMtdMixin(i.SVGWrapper));e.SVGmtd=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmaction=void 0;var i=r(0),s=r(10),a=r(10),l=r(100),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=this.selected,o=r.getBBox(),n=o.h,i=o.d,s=o.w;this.adaptor.append(this.element,this.svg("rect",{width:this.fixed(s),height:this.fixed(n+i),y:this.fixed(-i),fill:"none","pointer-events":"all"})),r.toSVG(e),this.action(this,this.data)},e.prototype.setEventHandler=function(t,e){this.element.addEventListener(t,e)},e.kind=l.MmlMaction.prototype.kind,e.styles={'[jax="SVG"] mjx-tool':{display:"inline-block",position:"relative",width:0,height:0},'[jax="SVG"] mjx-tool > mjx-tip':{position:"absolute",top:0,left:0},"mjx-tool > mjx-tip":{display:"inline-block",padding:".2em",border:"1px solid #888","font-size":"70%","background-color":"#F8F8F8",color:"black","box-shadow":"2px 2px 5px #AAAAAA"},'g[data-mml-node="maction"][data-toggle]':{cursor:"pointer"},"mjx-status":{display:"block",position:"fixed",left:"1em",bottom:"1em","min-width":"25%",padding:".2em .4em",border:"1px solid #888","font-size":"90%","background-color":"#F8F8F8",color:"black"}},e.actions=new Map([["toggle",[function(t,e){t.adaptor.setAttribute(t.element,"data-toggle",t.node.attributes.get("selection"));var r=t.factory.jax.math,o=t.factory.jax.document,n=t.node;t.setEventHandler("click",(function(t){r.end.node||(r.start.node=r.end.node=r.typesetRoot,r.start.n=r.end.n=0),n.nextToggleSelection(),r.rerender(o),t.stopPropagation()}))},{}]],["tooltip",[function(t,e){var r=t.childNodes[1];if(r){var o=t.adaptor.firstChild(t.element);if(r.node.isKind("mtext")){var n=r.node.getText();t.adaptor.insert(t.svg("title",{},[t.text(n)]),o)}else{var i=t.adaptor,s=t.jax.container,a=t.node.factory.create("math",{},[t.childNodes[1].node]),l=t.html("mjx-tool",{},[t.html("mjx-tip")]),h=i.append(o,t.svg("foreignObject",{style:{display:"none"}},[l]));t.jax.processMath(a,i.firstChild(l)),t.childNodes[1].node.parent=t.node,t.setEventHandler("mouseover",(function(r){e.stopTimers(t,e),e.hoverTimer.set(t,setTimeout((function(){i.setStyle(l,"left","0"),i.setStyle(l,"top","0"),i.append(s,l);var e=i.nodeBBox(l),r=i.nodeBBox(t.element),o=(r.right-e.left)/t.metrics.em+t.dx,n=(r.bottom-e.bottom)/t.metrics.em+t.dy;i.setStyle(l,"left",t.px(o)),i.setStyle(l,"top",t.px(n))}),e.postDelay)),r.stopPropagation()})),t.setEventHandler("mouseout",(function(r){e.stopTimers(t,e);var o=setTimeout((function(){return i.append(h,l)}),e.clearDelay);e.clearTimer.set(t,o),r.stopPropagation()}))}}},a.TooltipData]],["statusline",[function(t,e){var r=t.childNodes[1];if(r&&r.node.isKind("mtext")){var o=t.adaptor,n=r.node.getText();o.setAttribute(t.element,"data-statusline",n),t.setEventHandler("mouseover",(function(r){if(null===e.status){var i=o.body(o.document);e.status=o.append(i,t.html("mjx-status",{},[t.text(n)]))}r.stopPropagation()})),t.setEventHandler("mouseout",(function(t){e.status&&(o.remove(e.status),e.status=null),t.stopPropagation()}))}},{status:null}]]]),e}(s.CommonMactionMixin(i.SVGWrapper));e.SVGmaction=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,n,i=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(o=i.next()).done;)s.push(o.value)}catch(t){n={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}return s};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmenclose=void 0;var a=r(0),l=r(23),h=r(65),c=r(101),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e,r,o=this.standardSVGnode(t),n=this.getBBoxExtenders()[3],s={};n>0&&(s.transform="translate("+this.fixed(n)+", 0)");var a=this.adaptor.append(o,this.svg("g",s));this.renderChild?this.renderChild(this,a):this.childNodes[0].toSVG(a);try{for(var l=i(Object.keys(this.notations)),h=l.next();!h.done;h=l.next()){var c=h.value,u=this.notations[c];!u.renderChild&&u.renderer(this,o)}}catch(t){e={error:t}}finally{try{h&&!h.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}},e.prototype.arrow=function(t,e,r){void 0===r&&(r=!1);var o=this.getBBox(),n=o.w,i=(t-n)/2,a=(o.h-o.d)/2,l=this.thickness,h=l/2,c=s([l*this.arrowhead.x,l*this.arrowhead.y,l*this.arrowhead.dx],3),u=c[0],p=c[1],d=c[2],f=r?this.fill("M",n+i,a,"l",-(u+d),p,"l",d,h-p,"L",u-i,a+h,"l",d,p-h,"l",-(u+d),-p,"l",u+d,-p,"l",-d,p-h,"L",n+i-u,a-h,"l",-d,h-p,"Z"):this.fill("M",n+i,a,"l",-(u+d),p,"l",d,h-p,"L",-i,a+h,"l",0,-l,"L",n+i-u,a-h,"l",-d,h-p,"Z");if(e){var y=this.jax.fixed(180*-e/Math.PI);this.adaptor.setAttribute(f,"transform","rotate("+[y,this.fixed(n/2),this.fixed(a)].join(" ")+")")}return f},e.prototype.line=function(t){var e=s(t,4),r=e[0],o=e[1],n=e[2],i=e[3];return this.svg("line",{x1:this.fixed(r),y1:this.fixed(o),x2:this.fixed(n),y2:this.fixed(i),"stroke-width":this.fixed(this.thickness)})},e.prototype.box=function(t,e,r,o){void 0===o&&(o=0);var n=this.thickness,i={x:this.fixed(n/2),y:this.fixed(n/2-r),width:this.fixed(t-n),height:this.fixed(e+r-n),fill:"none","stroke-width":this.fixed(n)};return o&&(i.rx=this.fixed(o)),this.svg("rect",i)},e.prototype.ellipse=function(t,e,r){var o=this.thickness;return this.svg("ellipse",{rx:this.fixed((t-o)/2),ry:this.fixed((e+r-o)/2),cx:this.fixed(t/2),cy:this.fixed((e-r)/2),fill:"none","stroke-width":this.fixed(o)})},e.prototype.path=function(t){for(var e=this,r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];return this.svg("path",{d:r.map((function(t){return"string"==typeof t?t:e.fixed(t)})).join(" "),style:{"stroke-width":this.fixed(this.thickness)},"stroke-linecap":"round","stroke-linejoin":t,fill:"none"})},e.prototype.fill=function(){for(var t=this,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return this.svg("path",{d:e.map((function(e){return"string"==typeof e?e:t.fixed(e)})).join(" ")})},e.kind=c.MmlMenclose.prototype.kind,e.notations=new Map([h.Border("top"),h.Border("right"),h.Border("bottom"),h.Border("left"),h.Border2("actuarial","top","right"),h.Border2("madruwb","bottom","right"),h.DiagonalStrike("up"),h.DiagonalStrike("down"),["horizontalstrike",{renderer:h.RenderLine("horizontal"),bbox:function(t){return[0,t.padding,0,t.padding]}}],["verticalstrike",{renderer:h.RenderLine("vertical"),bbox:function(t){return[t.padding,0,t.padding,0]}}],["box",{renderer:function(t,e){var r=t.getBBox(),o=r.w,n=r.h,i=r.d;t.adaptor.append(t.element,t.box(o,n,i))},bbox:h.fullBBox,border:h.fullBorder,remove:"left right top bottom"}],["roundedbox",{renderer:function(t,e){var r=t.getBBox(),o=r.w,n=r.h,i=r.d,s=t.thickness+t.padding;t.adaptor.append(t.element,t.box(o,n,i,s))},bbox:h.fullBBox}],["circle",{renderer:function(t,e){var r=t.getBBox(),o=r.w,n=r.h,i=r.d;t.adaptor.append(t.element,t.ellipse(o,n,i))},bbox:h.fullBBox}],["phasorangle",{renderer:function(t,e){var r=t.getBBox(),o=r.w,n=r.h,i=r.d,s=t.getArgMod(1.75*t.padding,n+i)[0],a=t.thickness/2,l=n+i,h=Math.cos(s);t.adaptor.append(t.element,t.path("mitre","M",o,a-i,"L",a+h*a,a-i,"L",h*l+a,l-i-a))},bbox:function(t){var e=t.padding/2,r=t.thickness;return[2*e,e,e+r,3*e+r]},border:function(t){return[0,0,t.thickness,0]},remove:"bottom"}],h.Arrow("up"),h.Arrow("down"),h.Arrow("left"),h.Arrow("right"),h.Arrow("updown"),h.Arrow("leftright"),h.DiagonalArrow("updiagonal"),h.DiagonalArrow("northeast"),h.DiagonalArrow("southeast"),h.DiagonalArrow("northwest"),h.DiagonalArrow("southwest"),h.DiagonalArrow("northeastsouthwest"),h.DiagonalArrow("northwestsoutheast"),["longdiv",{renderer:function(t,e){var r=t.getBBox(),o=r.w,n=r.h,i=r.d,s=t.thickness/2,a=t.padding;t.adaptor.append(t.element,t.path("round","M",s,s-i,"a",a-s/2,(n+i)/2-4*s,0,"0,1",0,n+i-2*s,"L",o-s,n-s))},bbox:function(t){var e=t.padding,r=t.thickness;return[e+r,e,e,2*e+r/2]}}],["radical",{renderer:function(t,e){t.msqrt.toSVG(e);var r=t.sqrtTRBL()[3];t.place(-r,0,e)},init:function(t){t.msqrt=t.createMsqrt(t.childNodes[0])},bbox:function(t){return t.sqrtTRBL()},renderChild:!0}]]),e}(l.CommonMencloseMixin(a.SVGWrapper));e.SVGmenclose=u},function(t,e,r){"use strict";var o=this&&this.__createBinding||(Object.create?function(t,e,r,o){void 0===o&&(o=r),Object.defineProperty(t,o,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,o){void 0===o&&(o=r),t[o]=e[r]}),n=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||o(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),e.Arrow=e.DiagonalArrow=e.DiagonalStrike=e.Border2=e.Border=e.RenderLine=e.lineData=e.computeLineData=void 0;var i=r(5);n(r(5),e),e.computeLineData={top:function(t,e,r,o){return[0,t-o,r,t-o]},right:function(t,e,r,o){return[r-o,-e,r-o,t]},bottom:function(t,e,r,o){return[0,o-e,r,o-e]},left:function(t,e,r,o){return[o,-e,o,t]},vertical:function(t,e,r,o){return[r/2-o,t,r/2-o,-e]},horizontal:function(t,e,r,o){return[0,(t-e)/2-o,r,(t-e)/2-o]},up:function(t,e,r,o){return[o,o-e,r-o,t-o]},down:function(t,e,r,o){return[o,t-o,r-o,o-e]}},e.lineData=function(t,r){var o=t.getBBox(),n=o.h,i=o.d,s=o.w,a=t.thickness/2;return e.computeLineData[r](n,i,s,a)},e.RenderLine=function(t){return function(r,o){r.adaptor.append(r.element,r.line(e.lineData(r,t)))}},e.Border=function(t){return i.CommonBorder((function(r,o){r.adaptor.append(r.element,r.line(e.lineData(r,t)))}))(t)},e.Border2=function(t,r,o){return i.CommonBorder2((function(t,n){t.adaptor.append(t.element,t.line(e.lineData(t,r))),t.adaptor.append(t.element,t.line(e.lineData(t,o)))}))(t,r,o)},e.DiagonalStrike=function(t){return i.CommonDiagonalStrike((function(r){return function(r,o){r.adaptor.append(r.element,r.line(e.lineData(r,t)))}}))(t)},e.DiagonalArrow=function(t){return i.CommonDiagonalArrow((function(t,e){t.adaptor.append(t.element,e)}))(t)},e.Arrow=function(t){return i.CommonArrow((function(t,e){t.adaptor.append(t.element,e)}))(t)}},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGxml=e.SVGannotationXML=e.SVGannotation=e.SVGsemantics=void 0;var i=r(0),s=r(40),a=r(102),l=r(3),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t);this.childNodes.length&&this.childNodes[0].toSVG(e)},e.kind=a.MmlSemantics.prototype.kind,e}(s.CommonSemanticsMixin(i.SVGWrapper));e.SVGsemantics=h;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){t.prototype.toSVG.call(this,e)},e.prototype.computeBBox=function(){return this.bbox},e.kind=a.MmlAnnotation.prototype.kind,e}(i.SVGWrapper);e.SVGannotation=c;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.kind=a.MmlAnnotationXML.prototype.kind,e.styles={"foreignObject[data-mjx-xml]":{"font-family":"initial","line-height":"normal",overflow:"visible"}},e}(i.SVGWrapper);e.SVGannotationXML=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.adaptor.clone(this.node.getXML()),r=this.jax.math.metrics.em*this.jax.math.metrics.scale,o=this.fixed(1/r),n=this.getBBox(),i=n.w,s=n.h,a=n.d;this.element=this.adaptor.append(t,this.svg("foreignObject",{"data-mjx-xml":!0,y:this.jax.fixed(-s*r)+"px",width:this.jax.fixed(i*r)+"px",height:this.jax.fixed((s+a)*r)+"px",transform:"scale("+o+") matrix(1 0 0 -1 0 0)"},[e]))},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.jax.measureXMLnode(this.node.getXML()),o=r.w,n=r.h,i=r.d;t.w=o,t.h=n,t.d=i},e.prototype.getStyles=function(){},e.prototype.getScale=function(){},e.prototype.getVariant=function(){},e.kind=l.XMLNode.prototype.kind,e.autoStyle=!1,e}(i.SVGWrapper);e.SVGxml=p},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGmglyph=void 0;var i=r(0),s=r(26),a=r(103),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e=this.standardSVGnode(t),r=this.node.attributes.getList("src","alt"),o=r.src,n=r.alt,i=this.fixed(this.height),s={width:this.fixed(this.width),height:i,transform:"translate(0 "+this.fixed(this.height+(this.valign||0))+") matrix(1 0 0 -1 0 0)",preserveAspectRatio:"none",alt:n,title:n,href:o},a=this.svg("image",s);this.adaptor.append(e,a)},e.kind=a.MmlMglyph.prototype.kind,e}(s.CommonMglyphMixin(i.SVGWrapper));e.SVGmglyph=l},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.SVGTeXAtom=void 0;var i=r(0),s=r(20),a=r(104),l=r(3),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(e){if(t.prototype.toSVG.call(this,e),this.adaptor.setAttribute(this.element,"data-mjx-texclass",l.TEXCLASSNAMES[this.node.texClass]),this.node.texClass===l.TEXCLASS.VCENTER){var r=this.childNodes[0].getBBox(),o=r.h,n=(o+r.d)/2+this.font.params.axis_height-o,i="translate(0 "+this.fixed(n)+")";this.adaptor.setAttribute(this.element,"transform",i)}},e.kind=a.TeXAtom.prototype.kind,e}(s.CommonTeXAtomMixin(i.SVGWrapper));e.SVGTeXAtom=h},function(t,e,r){"use strict";var o,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.SVGTextNode=void 0;var s=r(3),a=r(0),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.toSVG=function(t){var e,r,o=this.node.getText(),n=this.parent.variant;if("-explicitFont"===n)this.adaptor.append(t,this.jax.unknownText(o,n));else{var s=this.remappedText(o,n),a=0;try{for(var l=i(s),h=l.next();!h.done;h=l.next()){var c=h.value;a+=this.placeChar(c,a,0,t,n)}}catch(t){e={error:t}}finally{try{h&&!h.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}}this.element=this.adaptor.lastChild(t)},e.kind=s.TextNode.prototype.kind,e.styles={".MathJax path":{"stroke-width":3}},e}(r(21).CommonTextNodeMixin(a.SVGWrapper));e.SVGTextNode=l},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FontCache=void 0;var o=function(){function t(t){this.cache=new Map,this.defs=null,this.localID="",this.nextID=0,this.jax=t}return t.prototype.cachePath=function(t,e,r){var o="MJX-"+this.localID+(this.jax.font.getVariant(t).cacheID||"")+"-"+e;return this.cache.has(o)||(this.cache.set(o,r),this.jax.adaptor.append(this.defs,this.jax.svg("path",{id:o,d:r}))),o},t.prototype.clearLocalID=function(){this.localID=""},t.prototype.useLocalID=function(t){void 0===t&&(t=null),this.localID=(null==t?++this.nextID:t)+(""===t?"":"-")},t.prototype.clearCache=function(){this.cache=new Map,this.defs=this.jax.svg("defs")},t.prototype.getCache=function(){return this.defs},t}();e.FontCache=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Styles=MathJax._.util.Styles.Styles},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractOutputJax=MathJax._.core.OutputJax.AbstractOutputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.protoItem=MathJax._.core.MathItem.protoItem,e.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,e.STATE=MathJax._.core.MathItem.STATE,e.newState=MathJax._.core.MathItem.newState},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CssStyles=MathJax._.util.StyleList.CssStyles},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractWrapper=MathJax._.core.Tree.Wrapper.AbstractWrapper},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractWrapperFactory=MathJax._.core.Tree.WrapperFactory.AbstractWrapperFactory},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sum=MathJax._.util.numeric.sum,e.max=MathJax._.util.numeric.max},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMath=MathJax._.core.MmlTree.MmlNodes.math.MmlMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMrow=MathJax._.core.MmlTree.MmlNodes.mrow.MmlMrow,e.MmlInferredMrow=MathJax._.core.MmlTree.MmlNodes.mrow.MmlInferredMrow},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMi=MathJax._.core.MmlTree.MmlNodes.mi.MmlMi},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMo=MathJax._.core.MmlTree.MmlNodes.mo.MmlMo},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMn=MathJax._.core.MmlTree.MmlNodes.mn.MmlMn},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMs=MathJax._.core.MmlTree.MmlNodes.ms.MmlMs},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtext=MathJax._.core.MmlTree.MmlNodes.mtext.MmlMtext},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMerror=MathJax._.core.MmlTree.MmlNodes.merror.MmlMerror},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMspace=MathJax._.core.MmlTree.MmlNodes.mspace.MmlMspace},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMpadded=MathJax._.core.MmlTree.MmlNodes.mpadded.MmlMpadded},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMphantom=MathJax._.core.MmlTree.MmlNodes.mphantom.MmlMphantom},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfrac=MathJax._.core.MmlTree.MmlNodes.mfrac.MmlMfrac},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsqrt=MathJax._.core.MmlTree.MmlNodes.msqrt.MmlMsqrt},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMroot=MathJax._.core.MmlTree.MmlNodes.mroot.MmlMroot},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfenced=MathJax._.core.MmlTree.MmlNodes.mfenced.MmlMfenced},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsubsup=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsubsup,e.MmlMsub=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsub,e.MmlMsup=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsup},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMunderover=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMunderover,e.MmlMunder=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMunder,e.MmlMover=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMover},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMmultiscripts=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlMmultiscripts,e.MmlMprescripts=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlMprescripts,e.MmlNone=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlNone},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtable=MathJax._.core.MmlTree.MmlNodes.mtable.MmlMtable},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtr=MathJax._.core.MmlTree.MmlNodes.mtr.MmlMtr,e.MmlMlabeledtr=MathJax._.core.MmlTree.MmlNodes.mtr.MmlMlabeledtr},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtd=MathJax._.core.MmlTree.MmlNodes.mtd.MmlMtd},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMaction=MathJax._.core.MmlTree.MmlNodes.maction.MmlMaction},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMenclose=MathJax._.core.MmlTree.MmlNodes.menclose.MmlMenclose},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlSemantics=MathJax._.core.MmlTree.MmlNodes.semantics.MmlSemantics,e.MmlAnnotationXML=MathJax._.core.MmlTree.MmlNodes.semantics.MmlAnnotationXML,e.MmlAnnotation=MathJax._.core.MmlTree.MmlNodes.semantics.MmlAnnotation},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMglyph=MathJax._.core.MmlTree.MmlNodes.mglyph.MmlMglyph},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TeXAtom=MathJax._.core.MmlTree.MmlNodes.TeXAtom.TeXAtom},function(t,e,r){"use strict";r.r(e),r.d(e,"TeXFont",(function(){return c}));var o=r(15);function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=h(t);if(e){var n=h(this).constructor;r=Reflect.construct(o,arguments,n)}else r=o.apply(this,arguments);return l(this,r)}}function l(t,e){return!e||"object"!==n(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var c=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&s(t,e)}(r,t);var e=a(r);function r(){return i(this,r),e.apply(this,arguments)}return r}(o.FontData);c.OPTIONS={fontURL:"."}},function(t,e,r){"use strict";r.r(e);var o=r(14),n=r(2),i=r(5),s=r(17),a=r(18),l=r(19),h=r(20),c=r(21),u=r(10),p=r(22),d=r(23),f=r(24),y=r(25),m=r(26),v=r(27),b=r(28),x=r(29),g=r(30),_=r(31),M=r(32),S=r(11),w=r(33),O=r(34),C=r(35),B=r(6),j=r(36),P=r(37),V=r(38),A=r(12),G=r(7),k=r(39),N=r(40),T=r(9),D=r(70),L=r(15),W=r(65),R=r(0),I=r(41),E=r(42),F=r(68),J=r(69),H=r(63),z=r(43),X=r(64),q=r(50),K=r(56),U=r(54),Q=r(67),Y=r(45),$=r(59),Z=r(47),tt=r(46),et=r(52),rt=r(53),ot=r(55),nt=r(44),it=r(48),st=r(51),at=r(13),lt=r(8),ht=r(60),ct=r(62),ut=r(49),pt=r(61),dt=r(58),ft=r(57),yt=r(66);Object(o.combineWithMathJax)({_:{output:{common:{FontData:n,Notation:i,OutputJax:s,Wrapper:a,WrapperFactory:l,Wrappers:{TeXAtom:h,TextNode:c,maction:u,math:p,menclose:d,mfenced:f,mfrac:y,mglyph:m,mi:v,mmultiscripts:b,mn:x,mo:g,mpadded:_,mroot:M,mrow:S,ms:w,mspace:O,msqrt:C,msubsup:B,mtable:j,mtd:P,mtext:V,mtr:A,munderover:G,scriptbase:k,semantics:N}},svg_ts:T,svg:{FontCache:D,FontData:L,Notation:W,Wrapper:R,WrapperFactory:I,Wrappers_ts:E,Wrappers:{TeXAtom:F,TextNode:J,maction:H,math:z,menclose:X,merror:q,mfenced:K,mfrac:U,mglyph:Q,mi:Y,mmultiscripts:$,mn:Z,mo:tt,mpadded:et,mphantom:rt,mroot:ot,mrow:nt,ms:it,mspace:st,msqrt:at,msubsup:lt,mtable:ht,mtd:ct,mtext:ut,mtr:pt,munderover:dt,scriptbase:ft,semantics:yt}}}}}),MathJax.loader&&Object(o.combineDefaults)(MathJax.config.loader,"output/svg",{checkReady:function(){return MathJax.loader.load("output/svg/fonts/tex")}}),MathJax.startup&&(MathJax.startup.registerConstructor("svg",T.SVG),MathJax.startup.useOutput("svg"))}]);