!function(t){var e={};function r(o){if(e[o])return e[o].exports;var i=e[o]={i:o,l:!1,exports:{}};return t[o].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=t,r.c=e,r.d=function(t,e,o){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(r.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(o,i,function(e){return t[e]}.bind(null,i));return o},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=101)}([function(t,e,r){"use strict";var o,i,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLWrapper=e.SPACE=e.FONTSIZE=void 0;var l=r(2),h=r(21),c=r(1);e.FONTSIZE={"70.7%":"s","70%":"s","50%":"ss","60%":"Tn","85%":"sm","120%":"lg","144%":"Lg","173%":"LG","207%":"hg","249%":"HG"},e.SPACE=((i={})[l.em(2/18)]="1",i[l.em(3/18)]="2",i[l.em(4/18)]="3",i[l.em(5/18)]="4",i[l.em(6/18)]="5",i);var u=function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return e.chtml=null,e}return n(r,t),r.prototype.toCHTML=function(t){var e,r,o=this.standardCHTMLnode(t);try{for(var i=a(this.childNodes),n=i.next();!n.done;n=i.next()){n.value.toCHTML(o)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},r.prototype.standardCHTMLnode=function(t){this.markUsed();var e=this.createCHTMLnode(t);return this.handleStyles(),this.handleVariant(),this.handleScale(),this.handleColor(),this.handleSpace(),this.handleAttributes(),this.handlePWidth(),e},r.prototype.markUsed=function(){this.constructor.used=!0},r.prototype.createCHTMLnode=function(t){var e=this.node.attributes.get("href");return e&&(t=this.adaptor.append(t,this.html("a",{href:e}))),this.chtml=this.adaptor.append(t,this.html("mjx-"+this.node.kind)),this.chtml},r.prototype.handleStyles=function(){if(this.styles){var t=this.styles.cssText;if(t){this.adaptor.setAttribute(this.chtml,"style",t);var e=this.styles.get("font-family");e&&this.adaptor.setStyle(this.chtml,"font-family","MJXZERO, "+e)}}},r.prototype.handleVariant=function(){this.node.isToken&&"-explicitFont"!==this.variant&&this.adaptor.setAttribute(this.chtml,"class",(this.font.getVariant(this.variant)||this.font.getVariant("normal")).classes)},r.prototype.handleScale=function(){this.setScale(this.chtml,this.bbox.rscale)},r.prototype.setScale=function(t,r){var o=Math.abs(r-1)<.001?1:r;if(t&&1!==o){var i=this.percent(o);e.FONTSIZE[i]?this.adaptor.setAttribute(t,"size",e.FONTSIZE[i]):this.adaptor.setStyle(t,"fontSize",i)}return t},r.prototype.handleSpace=function(){var t,r;try{for(var o=a([[this.bbox.L,"space","marginLeft"],[this.bbox.R,"rspace","marginRight"]]),i=o.next();!i.done;i=o.next()){var n=i.value,l=s(n,3),h=l[0],c=l[1],u=l[2];if(h){var p=this.em(h);e.SPACE[p]?this.adaptor.setAttribute(this.chtml,c,e.SPACE[p]):this.adaptor.setStyle(this.chtml,u,p)}}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}},r.prototype.handleColor=function(){var t=this.node.attributes,e=t.getExplicit("mathcolor"),r=t.getExplicit("color"),o=t.getExplicit("mathbackground"),i=t.getExplicit("background");(e||r)&&this.adaptor.setStyle(this.chtml,"color",e||r),(o||i)&&this.adaptor.setStyle(this.chtml,"backgroundColor",o||i)},r.prototype.handleAttributes=function(){var t,e,o,i,n=this.node.attributes,s=n.getAllDefaults(),l=r.skipAttributes;try{for(var h=a(n.getExplicitNames()),c=h.next();!c.done;c=h.next()){var u=c.value;!1!==l[u]&&(u in s||l[u]||this.adaptor.hasAttribute(this.chtml,u))||this.adaptor.setAttribute(this.chtml,u,n.getExplicit(u))}}catch(e){t={error:e}}finally{try{c&&!c.done&&(e=h.return)&&e.call(h)}finally{if(t)throw t.error}}if(n.get("class")){var p=n.get("class").trim().split(/ +/);try{for(var d=a(p),f=d.next();!f.done;f=d.next()){var m=f.value;this.adaptor.addClass(this.chtml,m)}}catch(t){o={error:t}}finally{try{f&&!f.done&&(i=d.return)&&i.call(d)}finally{if(o)throw o.error}}}},r.prototype.handlePWidth=function(){this.bbox.pwidth&&(this.bbox.pwidth===c.BBox.fullWidth?this.adaptor.setAttribute(this.chtml,"width","full"):this.adaptor.setStyle(this.chtml,"width",this.bbox.pwidth))},r.prototype.setIndent=function(t,e,r){var o=this.adaptor;if("center"===e||"left"===e){var i=this.getBBox().L;o.setStyle(t,"margin-left",this.em(r+i))}if("center"===e||"right"===e){var n=this.getBBox().R;o.setStyle(t,"margin-right",this.em(-r+n))}},r.prototype.drawBBox=function(){var t=this.getBBox(),e=t.w,r=t.h,o=t.d,i=t.R,n=this.html("mjx-box",{style:{opacity:.25,"margin-left":this.em(-e-i)}},[this.html("mjx-box",{style:{height:this.em(r),width:this.em(e),"background-color":"red"}}),this.html("mjx-box",{style:{height:this.em(o),width:this.em(e),"margin-left":this.em(-e),"vertical-align":this.em(-o),"background-color":"green"}})]),a=this.chtml||this.parent.chtml,s=this.adaptor.getAttribute(a,"size");s&&this.adaptor.setAttribute(n,"size",s);var l=this.adaptor.getStyle(a,"fontSize");l&&this.adaptor.setStyle(n,"fontSize",l),this.adaptor.append(this.adaptor.parent(a),n),this.adaptor.setStyle(a,"backgroundColor","#FFEE00")},r.prototype.html=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.jax.html(t,e,r)},r.prototype.text=function(t){return this.jax.text(t)},r.prototype.char=function(t){return this.font.charSelector(t).substr(1)},r.kind="unknown",r.autoStyle=!0,r.used=!1,r}(h.CommonWrapper);e.CHTMLWrapper=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BBoxStyleAdjust=MathJax._.util.BBox.BBoxStyleAdjust,e.BBox=MathJax._.util.BBox.BBox},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BIGDIMEN=MathJax._.util.lengths.BIGDIMEN,e.UNITS=MathJax._.util.lengths.UNITS,e.RELUNITS=MathJax._.util.lengths.RELUNITS,e.MATHSPACE=MathJax._.util.lengths.MATHSPACE,e.length2em=MathJax._.util.lengths.length2em,e.percent=MathJax._.util.lengths.percent,e.em=MathJax._.util.lengths.em,e.emRounded=MathJax._.util.lengths.emRounded,e.px=MathJax._.util.lengths.px},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sortLength=MathJax._.util.string.sortLength,e.quotePattern=MathJax._.util.string.quotePattern,e.unicodeChars=MathJax._.util.string.unicodeChars,e.isPercent=MathJax._.util.string.isPercent,e.split=MathJax._.util.string.split},function(t,e,r){"use strict";var o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},n=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(i(arguments[e]));return t},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.FontData=e.NOSTRETCH=e.H=e.V=void 0,e.V=1,e.H=2,e.NOSTRETCH={dir:0};var s=function(){function t(){var t,e;this.variant={},this.delimiters={},this.cssFontMap={},this.remapChars={};var r=this.constructor;this.params=o({},r.defaultParams),this.sizeVariants=n(r.defaultSizeVariants),this.cssFontMap=o({},r.defaultCssFonts),this.cssFamilyPrefix=r.defaultCssFamilyPrefix,this.createVariants(r.defaultVariants),this.defineDelimiters(r.defaultDelimiters);try{for(var i=a(Object.keys(r.defaultChars)),s=i.next();!s.done;s=i.next()){var l=s.value;this.defineChars(l,r.defaultChars[l])}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}this.defineRemap("accent",r.defaultAccentMap),this.defineRemap("mo",r.defaultMoMap),this.defineRemap("mn",r.defaultMnMap)}return t.charOptions=function(t,e){var r=t[e];return 3===r.length&&(r[3]={}),r[3]},t.prototype.createVariant=function(t,e,r){void 0===e&&(e=null),void 0===r&&(r=null);var o={linked:[],chars:e?Object.create(this.variant[e].chars):{}};r&&this.variant[r]&&(Object.assign(o.chars,this.variant[r].chars),this.variant[r].linked.push(o.chars),o.chars=Object.create(o.chars)),this.remapSmpChars(o.chars,t),this.variant[t]=o},t.prototype.remapSmpChars=function(t,e){var r,o,n,s,l=this.constructor;if(l.VariantSmp[e]){var h=l.SmpRemap,c=[null,null,l.SmpRemapGreekU,l.SmpRemapGreekL];try{for(var u=a(l.SmpRanges),p=u.next();!p.done;p=u.next()){var d=i(p.value,3),f=d[0],m=d[1],y=d[2],v=l.VariantSmp[e][f];if(v){for(var b=m;b<=y;b++)if(930!==b){var x=v+b-m;t[b]=this.smpChar(h[x]||x)}if(c[f])try{for(var g=(n=void 0,a(Object.keys(c[f]).map((function(t){return parseInt(t)})))),M=g.next();!M.done;M=g.next()){t[b=M.value]=this.smpChar(v+c[f][b])}}catch(t){n={error:t}}finally{try{M&&!M.done&&(s=g.return)&&s.call(g)}finally{if(n)throw n.error}}}}}catch(t){r={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}}"bold"===e&&(t[988]=this.smpChar(120778),t[989]=this.smpChar(120779))},t.prototype.smpChar=function(t){return[,,,{smp:t}]},t.prototype.createVariants=function(t){var e,r;try{for(var o=a(t),i=o.next();!i.done;i=o.next()){var n=i.value;this.createVariant(n[0],n[1],n[2])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.prototype.defineChars=function(t,e){var r,o,i=this.variant[t];Object.assign(i.chars,e);try{for(var n=a(i.linked),s=n.next();!s.done;s=n.next()){var l=s.value;Object.assign(l,e)}}catch(t){r={error:t}}finally{try{s&&!s.done&&(o=n.return)&&o.call(n)}finally{if(r)throw r.error}}},t.prototype.defineDelimiters=function(t){Object.assign(this.delimiters,t)},t.prototype.defineRemap=function(t,e){this.remapChars.hasOwnProperty(t)||(this.remapChars[t]={}),Object.assign(this.remapChars[t],e)},t.prototype.getDelimiter=function(t){return this.delimiters[t]},t.prototype.getSizeVariant=function(t,e){return this.delimiters[t].variants&&(e=this.delimiters[t].variants[e]),this.sizeVariants[e]},t.prototype.getChar=function(t,e){return this.variant[t].chars[e]},t.prototype.getVariant=function(t){return this.variant[t]},t.prototype.getCssFont=function(t){return this.cssFontMap[t]||["serif",!1,!1]},t.prototype.getFamily=function(t){return this.cssFamilyPrefix?this.cssFamilyPrefix+", "+t:t},t.prototype.getRemappedChar=function(t,e){return(this.remapChars[t]||{})[e]},t.OPTIONS={},t.defaultVariants=[["normal"],["bold","normal"],["italic","normal"],["bold-italic","italic","bold"],["double-struck","bold"],["fraktur","normal"],["bold-fraktur","bold","fraktur"],["script","italic"],["bold-script","bold-italic","script"],["sans-serif","normal"],["bold-sans-serif","bold","sans-serif"],["sans-serif-italic","italic","sans-serif"],["sans-serif-bold-italic","bold-italic","bold-sans-serif"],["monospace","normal"]],t.defaultCssFonts={normal:["serif",!1,!1],bold:["serif",!1,!0],italic:["serif",!0,!1],"bold-italic":["serif",!0,!0],"double-struck":["serif",!1,!0],fraktur:["serif",!1,!1],"bold-fraktur":["serif",!1,!0],script:["cursive",!1,!1],"bold-script":["cursive",!1,!0],"sans-serif":["sans-serif",!1,!1],"bold-sans-serif":["sans-serif",!1,!0],"sans-serif-italic":["sans-serif",!0,!1],"sans-serif-bold-italic":["sans-serif",!0,!0],monospace:["monospace",!1,!1]},t.defaultCssFamilyPrefix="",t.VariantSmp={bold:[119808,119834,120488,120514,120782],italic:[119860,119886,120546,120572],"bold-italic":[119912,119938,120604,120630],script:[119964,119990],"bold-script":[120016,120042],fraktur:[120068,120094],"double-struck":[120120,120146,,,120792],"bold-fraktur":[120172,120198],"sans-serif":[120224,120250,,,120802],"bold-sans-serif":[120276,120302,120662,120688,120812],"sans-serif-italic":[120328,120354],"sans-serif-bold-italic":[120380,120406,120720,120746],monospace:[120432,120458,,,120822]},t.SmpRanges=[[0,65,90],[1,97,122],[2,913,937],[3,945,969],[4,48,57]],t.SmpRemap={119893:8462,119965:8492,119968:8496,119969:8497,119971:8459,119972:8464,119975:8466,119976:8499,119981:8475,119994:8495,119996:8458,120004:8500,120070:8493,120075:8460,120076:8465,120085:8476,120093:8488,120122:8450,120127:8461,120133:8469,120135:8473,120136:8474,120137:8477,120145:8484},t.SmpRemapGreekU={8711:25,1012:17},t.SmpRemapGreekL={977:27,981:29,982:31,1008:28,1009:30,1013:26,8706:25},t.defaultAccentMap={768:"\u02cb",769:"\u02ca",770:"\u02c6",771:"\u02dc",772:"\u02c9",774:"\u02d8",775:"\u02d9",776:"\xa8",778:"\u02da",780:"\u02c7",8594:"\u20d7",8242:"'",8243:"''",8244:"'''",8245:"`",8246:"``",8247:"```",8279:"''''",8400:"\u21bc",8401:"\u21c0",8406:"\u2190",8417:"\u2194",8432:"*",8411:"...",8412:"....",8428:"\u21c1",8429:"\u21bd",8430:"\u2190",8431:"\u2192"},t.defaultMoMap={45:"\u2212"},t.defaultMnMap={45:"\u2212"},t.defaultParams={x_height:.442,quad:1,num1:.676,num2:.394,num3:.444,denom1:.686,denom2:.345,sup1:.413,sup2:.363,sup3:.289,sub1:.15,sub2:.247,sup_drop:.386,sub_drop:.05,delim1:2.39,delim2:1,axis_height:.25,rule_thickness:.06,big_op_spacing1:.111,big_op_spacing2:.167,big_op_spacing3:.2,big_op_spacing4:.6,big_op_spacing5:.1,surd_height:.075,scriptspace:.05,nulldelimiterspace:.12,delimiterfactor:901,delimitershortfall:.3,min_rule_thickness:1.25},t.defaultDelimiters={},t.defaultChars={},t.defaultSizeVariants=[],t}();e.FontData=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEXCLASS=MathJax._.core.MmlTree.MmlNode.TEXCLASS,e.TEXCLASSNAMES=MathJax._.core.MmlTree.MmlNode.TEXCLASSNAMES,e.indentAttributes=MathJax._.core.MmlTree.MmlNode.indentAttributes,e.AbstractMmlNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlNode,e.AbstractMmlTokenNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlTokenNode,e.AbstractMmlLayoutNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlLayoutNode,e.AbstractMmlBaseNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlBaseNode,e.AbstractMmlEmptyNode=MathJax._.core.MmlTree.MmlNode.AbstractMmlEmptyNode,e.TextNode=MathJax._.core.MmlTree.MmlNode.TextNode,e.XMLNode=MathJax._.core.MmlTree.MmlNode.XMLNode},function(t,e,r){"use strict";var o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonArrow=e.CommonDiagonalArrow=e.CommonDiagonalStrike=e.CommonBorder2=e.CommonBorder=e.arrowBBox=e.diagonalArrowDef=e.arrowDef=e.arrowBBoxW=e.arrowBBoxHD=e.arrowHead=e.fullBorder=e.fullPadding=e.fullBBox=e.sideNames=e.sideIndex=e.SOLID=e.PADDING=e.THICKNESS=e.ARROWY=e.ARROWDX=e.ARROWX=void 0,e.ARROWX=4,e.ARROWDX=1,e.ARROWY=2,e.THICKNESS=.067,e.PADDING=.2,e.SOLID=e.THICKNESS+"em solid",e.sideIndex={top:0,right:1,bottom:2,left:3},e.sideNames=Object.keys(e.sideIndex),e.fullBBox=function(t){return new Array(4).fill(t.thickness+t.padding)},e.fullPadding=function(t){return new Array(4).fill(t.padding)},e.fullBorder=function(t){return new Array(4).fill(t.thickness)},e.arrowHead=function(t){return Math.max(t.padding,t.thickness*(t.arrowhead.x+t.arrowhead.dx+1))},e.arrowBBoxHD=function(t,e){if(t.childNodes[0]){var r=t.childNodes[0].getBBox(),o=r.h,i=r.d;e[0]=e[2]=Math.max(0,t.thickness*t.arrowhead.y-(o+i)/2)}return e},e.arrowBBoxW=function(t,e){if(t.childNodes[0]){var r=t.childNodes[0].getBBox().w;e[1]=e[3]=Math.max(0,t.thickness*t.arrowhead.y-r/2)}return e},e.arrowDef={up:[-Math.PI/2,!1,!0,"verticalstrike"],down:[Math.PI/2,!1,!0,"verticakstrike"],right:[0,!1,!1,"horizontalstrike"],left:[Math.PI,!1,!1,"horizontalstrike"],updown:[Math.PI/2,!0,!0,"verticalstrike uparrow downarrow"],leftright:[0,!0,!1,"horizontalstrike leftarrow rightarrow"]},e.diagonalArrowDef={updiagonal:[-1,0,!1,"updiagonalstrike northeastarrow"],northeast:[-1,0,!1,"updiagonalstrike updiagonalarrow"],southeast:[1,0,!1,"downdiagonalstrike"],northwest:[1,Math.PI,!1,"downdiagonalstrike"],southwest:[-1,Math.PI,!1,"updiagonalstrike"],northeastsouthwest:[-1,0,!0,"updiagonalstrike northeastarrow updiagonalarrow southwestarrow"],northwestsoutheast:[1,0,!0,"downdiagonalstrike northwestarrow southeastarrow"]},e.arrowBBox={up:function(t){return e.arrowBBoxW(t,[e.arrowHead(t),0,t.padding,0])},down:function(t){return e.arrowBBoxW(t,[t.padding,0,e.arrowHead(t),0])},right:function(t){return e.arrowBBoxHD(t,[0,e.arrowHead(t),0,t.padding])},left:function(t){return e.arrowBBoxHD(t,[0,t.padding,0,e.arrowHead(t)])},updown:function(t){return e.arrowBBoxW(t,[e.arrowHead(t),0,e.arrowHead(t),0])},leftright:function(t){return e.arrowBBoxHD(t,[0,e.arrowHead(t),0,e.arrowHead(t)])}},e.CommonBorder=function(t){return function(r){var o=e.sideIndex[r];return[r,{renderer:t,bbox:function(t){var e=[0,0,0,0];return e[o]=t.thickness+t.padding,e},border:function(t){var e=[0,0,0,0];return e[o]=t.thickness,e}}]}},e.CommonBorder2=function(t){return function(r,o,i){var n=e.sideIndex[o],a=e.sideIndex[i];return[r,{renderer:t,bbox:function(t){var e=t.thickness+t.padding,r=[0,0,0,0];return r[n]=r[a]=e,r},border:function(t){var e=[0,0,0,0];return e[n]=e[a]=t.thickness,e},remove:o+" "+i}]}},e.CommonDiagonalStrike=function(t){return function(r){var o="mjx-"+r.charAt(0)+"strike";return[r+"diagonalstrike",{renderer:t(o),bbox:e.fullBBox}]}},e.CommonDiagonalArrow=function(t){return function(r){var i=o(e.diagonalArrowDef[r],4),n=i[0],a=i[1],s=i[2];return[r+"arrow",{renderer:function(e,r){var o=e.arrowData(),i=o.a,l=o.W,h=e.arrow(l,n*(i-a),s);t(e,h)},bbox:function(t){var e=t.arrowData(),r=e.a,i=e.x,n=e.y,a=o([t.arrowhead.x,t.arrowhead.y,t.arrowhead.dx],3),s=a[0],l=a[1],h=a[2],c=o(t.getArgMod(s+h,l),2),u=c[0],p=c[1],d=n+(u>r?t.thickness*p*Math.sin(u-r):0),f=i+(u>Math.PI/2-r?t.thickness*p*Math.sin(u+r-Math.PI/2):0);return[d,f,d,f]},remove:i[3]}]}},e.CommonArrow=function(t){return function(r){var i=o(e.arrowDef[r],4),n=i[0],a=i[1],s=i[2],l=i[3];return[r+"arrow",{renderer:function(e,r){var o=e.getBBox(),i=o.w,l=o.h,h=o.d,c=s?l+h:i,u=e.arrow(c,n,a);t(e,u)},bbox:e.arrowBBox[r],remove:l}]}}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmsubsup=e.CHTMLmsup=e.CHTMLmsub=void 0;var a=r(49),s=r(8),l=r(8),h=r(8),c=r(89),u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=c.MmlMsub.prototype.kind,e.useIC=!1,e}(s.CommonMsubMixin(a.CHTMLscriptbase));e.CHTMLmsub=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=c.MmlMsup.prototype.kind,e.useIC=!0,e}(l.CommonMsupMixin(a.CHTMLscriptbase));e.CHTMLmsup=p;var d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.markUsed=function(){t.prototype.markUsed.call(this),e.used=!0},e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=n([this.baseChild,this.supChild,this.subChild],3),o=r[0],i=r[1],a=r[2],s=n(this.getUVQ(o.getBBox(),a.getBBox(),i.getBBox()),3),l=s[1],h=s[2],c=this.baseCore.bbox.ic?this.coreIC()*this.coreScale():0,u={"vertical-align":this.em(l)};o.toCHTML(e);var p=this.adaptor.append(e,this.html("mjx-script",{style:u}));i.toCHTML(p),this.adaptor.append(p,this.html("mjx-spacer",{style:{"margin-top":this.em(h)}})),a.toCHTML(p),this.baseCore.bbox.ic&&this.adaptor.setStyle(i.chtml,"marginLeft",this.em(c/i.bbox.rscale))},e.kind=c.MmlMsubsup.prototype.kind,e.styles={"mjx-script":{display:"inline-block","padding-right":".05em"},"mjx-script > *":{display:"block"}},e.useIC=!1,e}(h.CommonMsubsupMixin(a.CHTMLscriptbase));e.CHTMLmsubsup=d},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsubsupMixin=e.CommonMsupMixin=e.CommonMsubMixin=void 0,e.CommonMsubMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.sub]},enumerable:!1,configurable:!0}),e.prototype.getOffset=function(t,e){return[0,-this.getV(t,e)]},e}(t)},e.CommonMsupMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.sup]},enumerable:!1,configurable:!0}),e.prototype.getOffset=function(t,e){return[(this.baseCore.bbox.ic?.05*this.baseCore.bbox.ic+.05:0)*this.coreScale(),this.getU(t,e)]},e}(t)},e.CommonMsubsupMixin=function(t){return function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.UVQ=null,e}return i(e,t),Object.defineProperty(e.prototype,"subChild",{get:function(){return this.childNodes[this.node.sub]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"supChild",{get:function(){return this.childNodes[this.node.sup]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.baseChild.getBBox(),o=this.subChild.getBBox(),i=this.supChild.getBBox();t.empty(),t.append(r);var a=t.w,s=n(this.getUVQ(r,o,i),2),l=s[0],h=s[1],c=this.baseCore.bbox.ic?this.coreIC()*this.coreScale():0;t.combine(o,a,h),t.combine(i,a+c,l),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(e)},e.prototype.getUVQ=function(t,e,r){if(this.UVQ)return this.UVQ;var o=this.font.params,i=3*o.rule_thickness,a=this.length2em(this.node.attributes.get("subscriptshift"),o.sub2),s=this.isCharBase()?0:t.d*t.rscale+o.sub_drop*e.rscale,l=n([this.getU(t,r),Math.max(s,a)],2),h=l[0],c=l[1],u=h-r.d*r.rscale-(e.h*e.rscale-c);if(u<i){c+=i-u;var p=.8*o.x_height-(h-r.d*r.rscale);p>0&&(h+=p,c-=p)}return h=Math.max(this.length2em(this.node.attributes.get("superscriptshift"),h),h),c=Math.max(this.length2em(this.node.attributes.get("subscriptshift"),c),c),u=h-r.d*r.rscale-(e.h*e.rscale-c),this.UVQ=[h,-c,u],this.UVQ},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMunderoverMixin=e.CommonMoverMixin=e.CommonMunderMixin=void 0,e.CommonMunderMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.stretchChildren(),o}return i(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.under]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(e,r){if(void 0===r&&(r=!1),this.hasMovableLimits())t.prototype.computeBBox.call(this,e,r);else{e.empty();var o=this.baseChild.getBBox(),i=this.script.getBBox(),a=this.getUnderKV(o,i)[1],s=this.getDelta(!0),l=n(this.getDeltaW([o,i],[0,-s]),2),h=l[0],c=l[1];e.combine(o,h,0),e.combine(i,c,a),e.d+=this.font.params.big_op_spacing5,e.ic=-this.baseCore.bbox.ic,e.clean(),this.setChildPWidths(r)}},e}(t)},e.CommonMoverMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.stretchChildren(),o}return i(e,t),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[this.node.over]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(e){if(this.hasMovableLimits())t.prototype.computeBBox.call(this,e);else{e.empty();var r=this.baseChild.getBBox(),o=this.script.getBBox(),i=this.getOverKU(r,o)[1],a=this.getDelta(),s=n(this.getDeltaW([r,o],[0,a]),2),l=s[0],h=s[1];e.combine(r,l,0),e.combine(o,h,i),e.h+=this.font.params.big_op_spacing5,e.ic=-this.baseCore.bbox.ic,e.clean()}},e}(t)},e.CommonMunderoverMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.stretchChildren(),o}return i(e,t),Object.defineProperty(e.prototype,"underChild",{get:function(){return this.childNodes[this.node.under]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overChild",{get:function(){return this.childNodes[this.node.over]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subChild",{get:function(){return this.underChild},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"supChild",{get:function(){return this.overChild},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(e){if(this.hasMovableLimits())t.prototype.computeBBox.call(this,e);else{e.empty();var r=this.overChild.getBBox(),o=this.baseChild.getBBox(),i=this.underChild.getBBox(),a=this.getOverKU(o,r)[1],s=this.getUnderKV(o,i)[1],l=this.getDelta(),h=n(this.getDeltaW([o,i,r],[0,-l,l]),3),c=h[0],u=h[1],p=h[2];e.combine(o,c,0),e.combine(r,p,a),e.combine(i,u,s);var d=this.font.params.big_op_spacing5;e.h+=d,e.d+=d,e.ic=-this.baseCore.bbox.ic,e.clean()}},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonInferredMrowMixin=e.CommonMrowMixin=void 0;var l=r(1);e.CommonMrowMixin=function(t){return function(t){function e(){for(var e,r,o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];var n=t.apply(this,a(o))||this;n.stretchChildren();try{for(var h=s(n.childNodes),c=h.next();!c.done;c=h.next()){var u=c.value;if(u.bbox.pwidth){n.bbox.pwidth=l.BBox.fullWidth;break}}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}return n}return i(e,t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.stretchChildren=function(){var t,e,r,o,i,n,a=[];try{for(var l=s(this.childNodes),h=l.next();!h.done;h=l.next()){(w=h.value).canStretch(1)&&a.push(w)}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}var c=a.length,u=this.childNodes.length;if(c&&u>1){var p=0,d=0,f=c>1&&c===u;try{for(var m=s(this.childNodes),y=m.next();!y.done;y=m.next()){var v=0===(w=y.value).stretch.dir;if(f||v){var b=w.getBBox(v),x=b.h,g=b.d;x>p&&(p=x),g>d&&(d=g)}}}catch(t){r={error:t}}finally{try{y&&!y.done&&(o=m.return)&&o.call(m)}finally{if(r)throw r.error}}try{for(var M=s(a),_=M.next();!_.done;_=M.next()){var w;(w=_.value).coreMO().getStretchedVariant([p,d])}}catch(t){i={error:t}}finally{try{_&&!_.done&&(n=M.return)&&n.call(M)}finally{if(i)throw i.error}}}},e}(t)},e.CommonInferredMrowMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.getScale=function(){this.bbox.scale=this.parent.bbox.scale,this.bbox.rscale=1},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmsqrt=void 0;var a=r(0),s=r(46),l=r(87),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e,r,o,i,a=this.childNodes[this.surd],s=this.childNodes[this.base],l=a.getBBox(),h=s.getBBox(),c=n(this.getPQ(l),2)[1],u=this.font.params.rule_thickness,p=h.h+c+u,d=this.standardCHTMLnode(t);null!=this.root&&(o=this.adaptor.append(d,this.html("mjx-root")),i=this.childNodes[this.root]);var f=this.adaptor.append(d,this.html("mjx-sqrt",{},[e=this.html("mjx-surd"),r=this.html("mjx-box",{style:{paddingTop:this.em(c)}})]));this.addRoot(o,i,l,p),a.toCHTML(e),s.toCHTML(r),a.size<0&&this.adaptor.addClass(f,"mjx-tall")},e.prototype.addRoot=function(t,e,r,o){},e.kind=l.MmlMsqrt.prototype.kind,e.styles={"mjx-root":{display:"inline-block","white-space":"nowrap"},"mjx-surd":{display:"inline-block","vertical-align":"top"},"mjx-sqrt":{display:"inline-block","padding-top":".07em"},"mjx-sqrt > mjx-box":{"border-top":".07em solid"},"mjx-sqrt.mjx-tall > mjx-box":{"padding-left":".3em","margin-left":"-.3em"}},e}(s.CommonMsqrtMixin(a.CHTMLWrapper));e.CHTMLmsqrt=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMlabeledtrMixin=e.CommonMtrMixin=void 0,e.CommonMtrMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"numCells",{get:function(){return this.childNodes.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"labeled",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableCells",{get:function(){return this.childNodes},enumerable:!1,configurable:!0}),e.prototype.getChild=function(t){return this.childNodes[t]},e.prototype.getChildBBoxes=function(){return this.childNodes.map((function(t){return t.getBBox()}))},e.prototype.stretchChildren=function(t){var e,r,o,i,a,s;void 0===t&&(t=null);var l=[],h=this.labeled?this.childNodes.slice(1):this.childNodes;try{for(var c=n(h),u=c.next();!u.done;u=c.next()){(j=u.value.childNodes[0]).canStretch(1)&&l.push(j)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}var p=l.length,d=this.childNodes.length;if(p&&d>1){if(null===t){var f=0,m=0,y=p>1&&p===d;try{for(var v=n(h),b=v.next();!b.done;b=v.next()){var x=0===(j=b.value.childNodes[0]).stretch.dir;if(y||x){var g=j.getBBox(x),M=g.h,_=g.d;M>f&&(f=M),_>m&&(m=_)}}}catch(t){o={error:t}}finally{try{b&&!b.done&&(i=v.return)&&i.call(v)}finally{if(o)throw o.error}}t=[f,m]}try{for(var w=n(l),C=w.next();!C.done;C=w.next()){var j;(j=C.value).coreMO().getStretchedVariant(t)}}catch(t){a={error:t}}finally{try{C&&!C.done&&(s=w.return)&&s.call(w)}finally{if(a)throw a.error}}}},e}(t)},e.CommonMlabeledtrMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"numCells",{get:function(){return Math.max(0,this.childNodes.length-1)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"labeled",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"tableCells",{get:function(){return this.childNodes.slice(1)},enumerable:!1,configurable:!0}),e.prototype.getChild=function(t){return this.childNodes[t+1]},e.prototype.getChildBBoxes=function(){return this.childNodes.slice(1).map((function(t){return t.getBBox()}))},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMactionMixin=e.TooltipData=void 0;var s=r(3);e.TooltipData={dx:".2em",dy:".1em",postDelay:600,clearDelay:100,hoverTimer:new Map,clearTimer:new Map,stopTimers:function(t,e){e.clearTimer.has(t)&&(clearTimeout(e.clearTimer.get(t)),e.clearTimer.delete(t)),e.hoverTimer.has(t)&&(clearTimeout(e.hoverTimer.get(t)),e.hoverTimer.delete(t))}},e.CommonMactionMixin=function(t){return function(t){function r(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this,i=o.constructor.actions,s=o.node.attributes.get("actiontype"),l=n(i.get(s)||[function(t,e){},{}],2),h=l[0],c=l[1];return o.action=h,o.data=c,o.getParameters(),o}return i(r,t),Object.defineProperty(r.prototype,"selected",{get:function(){var t=this.node.attributes.get("selection"),e=Math.max(1,Math.min(this.childNodes.length,t))-1;return this.childNodes[e]||this.wrap(this.node.selected)},enumerable:!1,configurable:!0}),r.prototype.getParameters=function(){var t=this.node.attributes.get("data-offsets"),r=n(s.split(t||""),2),o=r[0],i=r[1];this.dx=this.length2em(o||e.TooltipData.dx),this.dy=this.length2em(i||e.TooltipData.dy)},r.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.updateFrom(this.selected.getBBox()),this.selected.setChildPWidths(e)},r}(t)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTML=void 0;var s=r(17),l=r(18),h=r(100),c=r(2),u=r(3),p=function(t){function e(e){void 0===e&&(e=null);var r=t.call(this,e,l.CHTMLWrapperFactory,h.TeXFont)||this;return r.chtmlStyles=null,r.font.adaptiveCSS(r.options.adaptiveCSS),r}return i(e,t),e.prototype.escaped=function(t,e){return this.setDocument(e),this.html("span",{},[this.text(t.math)])},e.prototype.styleSheet=function(r){if(this.chtmlStyles&&!this.options.adaptiveCSS)return null;var o=this.chtmlStyles=t.prototype.styleSheet.call(this,r);return this.adaptor.setAttribute(o,"id",e.STYLESHEETID),o},e.prototype.addClassStyles=function(e){var r;this.options.adaptiveCSS&&!e.used||(e.autoStyle&&"unknown"!==e.kind&&this.cssStyles.addStyles(((r={})["mjx-"+e.kind]={display:"inline-block","text-align":"left"},r)),t.prototype.addClassStyles.call(this,e))},e.prototype.processMath=function(t,e){this.factory.wrap(t).toCHTML(e)},e.prototype.clearCache=function(){var t,e;this.cssStyles.clear(),this.font.clearCache();try{for(var r=a(this.factory.getKinds()),o=r.next();!o.done;o=r.next()){var i=o.value;this.factory.getNodeClass(i).used=!1}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.unknownText=function(t,e){var r={},o=100/this.math.metrics.scale;if(100!==o&&(r["font-size"]=this.fixed(o,1)+"%",r.padding=c.em(75/o)+" 0 "+c.em(20/o)+" 0"),"-explicitFont"!==e){var i=u.unicodeChars(t);(1!==i.length||i[0]<119808||i[0]>120831)&&this.cssFontStyles(this.font.getCssFont(e),r)}return this.html("mjx-utext",{variant:e,style:r},[this.text(t)])},e.prototype.measureTextNode=function(t){var e=this.adaptor;t=e.clone(t);var r=this.html("mjx-measure-text",{style:{position:"absolute","white-space":"nowrap"}},[t]);e.append(e.parent(this.math.start.node),this.container),e.append(this.container,r);var o=e.nodeSize(t,this.math.metrics.em)[0]/this.math.metrics.scale;return e.remove(this.container),e.remove(r),{w:o,h:.75,d:.2}},e.NAME="CHTML",e.OPTIONS=n(n({},s.CommonOutputJax.OPTIONS),{adaptiveCSS:!0}),e.commonStyles={'mjx-container[jax="CHTML"]':{"line-height":0},'mjx-container [space="1"]':{"margin-left":".111em"},'mjx-container [space="2"]':{"margin-left":".167em"},'mjx-container [space="3"]':{"margin-left":".222em"},'mjx-container [space="4"]':{"margin-left":".278em"},'mjx-container [space="5"]':{"margin-left":".333em"},'mjx-container [rspace="1"]':{"margin-right":".111em"},'mjx-container [rspace="2"]':{"margin-right":".167em"},'mjx-container [rspace="3"]':{"margin-right":".222em"},'mjx-container [rspace="4"]':{"margin-right":".278em"},'mjx-container [rspace="5"]':{"margin-right":".333em"},'mjx-container [size="s"]':{"font-size":"70.7%"},'mjx-container [size="ss"]':{"font-size":"50%"},'mjx-container [size="Tn"]':{"font-size":"60%"},'mjx-container [size="sm"]':{"font-size":"85%"},'mjx-container [size="lg"]':{"font-size":"120%"},'mjx-container [size="Lg"]':{"font-size":"144%"},'mjx-container [size="LG"]':{"font-size":"173%"},'mjx-container [size="hg"]':{"font-size":"207%"},'mjx-container [size="HG"]':{"font-size":"249%"},'mjx-container [width="full"]':{width:"100%"},"mjx-box":{display:"inline-block"},"mjx-block":{display:"block"},"mjx-itable":{display:"inline-table"},"mjx-row":{display:"table-row"},"mjx-row > *":{display:"table-cell"},"mjx-mtext":{display:"inline-block"},"mjx-mstyle":{display:"inline-block"},"mjx-merror":{display:"inline-block",color:"red","background-color":"yellow"},"mjx-mphantom":{visibility:"hidden"}},e.STYLESHEETID="MJX-CHTML-styles",e}(s.CommonOutputJax);e.CHTML=p},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__createBinding||(Object.create?function(t,e,r,o){void 0===o&&(o=r),Object.defineProperty(t,o,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,o){void 0===o&&(o=r),t[o]=e[r]}),s=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||a(e,t,r)},l=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},h=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.AddCSS=e.CHTMLFontData=void 0;var c=r(4),u=r(2),p=r(68);s(r(4),e);var d=function(t){function e(e){void 0===e&&(e=null);var r=t.call(this)||this,o=r.constructor;return r.options=p.userOptions(p.defaultOptions({},o.OPTIONS),e),r}return i(e,t),e.charOptions=function(e,r){return t.charOptions.call(this,e,r)},e.prototype.adaptiveCSS=function(t){this.options.adaptiveCSS=t},e.prototype.clearCache=function(){var t,e,r,o,i,n;if(this.options.adaptiveCSS){try{for(var a=l(Object.keys(this.delimiters)),s=a.next();!s.done;s=a.next()){var h=s.value;this.delimiters[parseInt(h)].used=!1}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}try{for(var c=l(Object.keys(this.variant)),u=c.next();!u.done;u=c.next()){var p=u.value,d=this.variant[p].chars;try{for(var f=(i=void 0,l(Object.keys(d))),m=f.next();!m.done;m=f.next()){h=m.value;var y=d[parseInt(h)][3];y&&(y.used=!1)}}catch(t){i={error:t}}finally{try{m&&!m.done&&(n=f.return)&&n.call(f)}finally{if(i)throw i.error}}}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}},e.prototype.createVariant=function(e,r,o){void 0===r&&(r=null),void 0===o&&(o=null),t.prototype.createVariant.call(this,e,r,o);var i=this.constructor;this.variant[e].classes=i.defaultVariantClasses[e],this.variant[e].letter=i.defaultVariantLetters[e]},e.prototype.defineChars=function(r,o){var i,n;t.prototype.defineChars.call(this,r,o);var a=this.variant[r].letter;try{for(var s=l(Object.keys(o)),h=s.next();!h.done;h=s.next()){var c=h.value,u=e.charOptions(o,parseInt(c));void 0===u.f&&(u.f=a)}}catch(t){i={error:t}}finally{try{h&&!h.done&&(n=s.return)&&n.call(s)}finally{if(i)throw i.error}}},Object.defineProperty(e.prototype,"styles",{get:function(){var t,e,r=this.constructor,o=n({},r.defaultStyles);this.addFontURLs(o,r.defaultFonts,this.options.fontURL);try{for(var i=l(Object.keys(this.delimiters)),a=i.next();!a.done;a=i.next()){var s=a.value,h=parseInt(s);this.addDelimiterStyles(o,h,this.delimiters[h])}}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return this.addVariantChars(o),o},enumerable:!1,configurable:!0}),e.prototype.addVariantChars=function(t){var e,r,o,i,n=!this.options.adaptiveCSS;try{for(var a=l(Object.keys(this.variant)),s=a.next();!s.done;s=a.next()){var h=s.value,c=this.variant[h],u=c.letter;try{for(var p=(o=void 0,l(Object.keys(c.chars))),d=p.next();!d.done;d=p.next()){var f=d.value,m=parseInt(f),y=c.chars[m];(y[3]||{}).smp||(n&&y.length<4&&(y[3]={}),(4===y.length||n)&&this.addCharStyles(t,u,m,y))}}catch(t){o={error:t}}finally{try{d&&!d.done&&(i=p.return)&&i.call(p)}finally{if(o)throw o.error}}}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},e.prototype.addFontURLs=function(t,e,r){var o,i;try{for(var a=l(Object.keys(e)),s=a.next();!s.done;s=a.next()){var h=s.value,c=n({},e[h]);c.src=c.src.replace(/%%URL%%/,r),t[h]=c}}catch(t){o={error:t}}finally{try{s&&!s.done&&(i=a.return)&&i.call(a)}finally{if(o)throw o.error}}},e.prototype.addDelimiterStyles=function(t,e,r){if(!this.options.adaptiveCSS||r.used){var o=this.charSelector(e);r.c&&r.c!==e&&(t[".mjx-stretched mjx-c"+o+"::before"]={content:this.charContent(r.c)}),r.stretch&&(1===r.dir?this.addDelimiterVStyles(t,o,r):this.addDelimiterHStyles(t,o,r))}},e.prototype.addDelimiterVStyles=function(t,e,r){var o=r.HDW[2],i=h(r.stretch,4),n=i[0],a=i[1],s=i[2],l=i[3],c=this.addDelimiterVPart(t,e,o,"beg",n);this.addDelimiterVPart(t,e,o,"ext",a);var u=this.addDelimiterVPart(t,e,o,"end",s),p={};if(l){var d=this.addDelimiterVPart(t,e,o,"mid",l);p.height="50%",t["mjx-stretchy-v"+e+" > mjx-mid"]={"margin-top":this.em(-d/2),"margin-bottom":this.em(-d/2)}}c&&(p["border-top-width"]=this.em0(c-.03)),u&&(p["border-bottom-width"]=this.em0(u-.03),t["mjx-stretchy-v"+e+" > mjx-end"]={"margin-top":this.em(-u)}),Object.keys(p).length&&(t["mjx-stretchy-v"+e+" > mjx-ext"]=p)},e.prototype.addDelimiterVPart=function(t,e,r,o,i){if(!i)return 0;var n=this.getDelimiterData(i),a=(r-n[2])/2,s={content:this.charContent(i)};return"ext"!==o?s.padding=this.padding(n,a):a&&(s["padding-left"]=this.em0(a)),t["mjx-stretchy-v"+e+" mjx-"+o+" mjx-c::before"]=s,n[0]+n[1]},e.prototype.addDelimiterHStyles=function(t,e,r){var o=h(r.stretch,4),i=o[0],n=o[1],a=o[2],s=o[3];this.addDelimiterHPart(t,e,"beg",i),this.addDelimiterHPart(t,e,"ext",n,!(i||a)),this.addDelimiterHPart(t,e,"end",a),s&&(this.addDelimiterHPart(t,e,"mid",s),t["mjx-stretchy-h"+e+" > mjx-ext"]={width:"50%"})},e.prototype.addDelimiterHPart=function(t,e,r,o,i){if(void 0===i&&(i=!1),o){var n=this.getDelimiterData(o),a=n[3],s={content:a&&a.c?'"'+a.c+'"':this.charContent(o)};("ext"!==r||i)&&(s.padding=this.padding(n,0,-n[2])),t["mjx-stretchy-h"+e+" mjx-"+r+" mjx-c::before"]=s}},e.prototype.addCharStyles=function(t,e,r,o){var i=h(o,4),n=i[2],a=i[3];if(!this.options.adaptiveCSS||a.used){var s=void 0!==a.f?a.f:e,l="mjx-c"+this.charSelector(r)+(s?".TEX-"+s:"");t[l+"::before"]={padding:this.padding(o,0,a.ic||0),content:null!=a.c?'"'+a.c+'"':this.charContent(r)},a.ic&&(t["[noIC] "+l+":last-child::before"]={"padding-right":this.em(n)})}},e.prototype.getDelimiterData=function(t){return this.getChar("-smallop",t)},e.prototype.em=function(t){return u.em(t)},e.prototype.em0=function(t){return u.em(Math.max(0,t))},e.prototype.padding=function(t,e,r){var o=h(t,3),i=o[0],n=o[1];return void 0===e&&(e=0),void 0===r&&(r=0),[i,o[2]+r,n,e].map(this.em0).join(" ")},e.prototype.charContent=function(t){return'"'+(t>=32&&t<=126&&34!==t&&39!==t&&92!==t?String.fromCharCode(t):"\\"+t.toString(16).toUpperCase())+'"'},e.prototype.charSelector=function(t){return".mjx-c"+t.toString(16).toUpperCase()},e.OPTIONS={fontURL:"js/output/chtml/fonts/tex-woff-v2"},e.defaultVariantClasses={},e.defaultVariantLetters={},e.defaultStyles={"mjx-c::before":{display:"block",width:0}},e.defaultFonts={"@font-face /* 0 */":{"font-family":"MJXZERO",src:'url("%%URL%%/MathJax_Zero.woff") format("woff")'}},e}(c.FontData);e.CHTMLFontData=d,e.AddCSS=function(t,e){var r,o;try{for(var i=l(Object.keys(e)),n=i.next();!n.done;n=i.next()){var a=n.value,s=parseInt(a);Object.assign(c.FontData.charOptions(t,s),e[s])}}catch(t){r={error:t}}finally{try{n&&!n.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}return t}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonOutputJax=void 0;var l=r(70),h=r(71),c=r(68),u=r(2),p=r(69),d=r(72),f=function(t){function e(e,r,o){void 0===e&&(e=null),void 0===r&&(r=null),void 0===o&&(o=null);var i=this,n=a(c.separateOptions(e,o.OPTIONS),2),s=n[0],l=n[1];return(i=t.call(this,s)||this).factory=i.options.wrapperFactory||new r,i.factory.jax=i,i.cssStyles=i.options.cssStyles||new d.CssStyles,i.font=i.options.font||new o(l),i.unknownCache=new Map,i}return i(e,t),e.prototype.typeset=function(t,e){this.setDocument(e);var r=this.createNode();return this.toDOM(t,r,e),r},e.prototype.createNode=function(){var t=this.constructor.NAME;return this.html("mjx-container",{class:"MathJax",jax:t})},e.prototype.setScale=function(t){var e=this.math.metrics.scale*this.options.scale;1!==e&&this.adaptor.setStyle(t,"fontSize",u.percent(e))},e.prototype.toDOM=function(t,e,r){void 0===r&&(r=null),this.setDocument(r),this.math=t,this.pxPerEm=t.metrics.ex/this.font.params.x_height,t.root.setTeXclass(null),this.setScale(e),this.nodeMap=new Map,this.container=e,this.processMath(t.root,e),this.nodeMap=null,this.executeFilters(this.postFilters,t,r,e)},e.prototype.getBBox=function(t,e){this.setDocument(e),this.math=t,t.root.setTeXclass(null),this.nodeMap=new Map;var r=this.factory.wrap(t.root).getBBox();return this.nodeMap=null,r},e.prototype.getMetrics=function(t){var e,r;this.setDocument(t);var o=this.adaptor,i=this.getMetricMaps(t);try{for(var n=s(t.math),a=n.next();!a.done;a=n.next()){var l=a.value,c=o.parent(l.start.node);if(l.state()<h.STATE.METRICS&&c){var u=i[l.display?1:0].get(c),p=u.em,d=u.ex,f=u.containerWidth,m=u.lineWidth,y=u.scale,v=u.family;l.setMetrics(p,d,f,m,y),this.options.mtextInheritFont&&(l.outputData.mtextFamily=v),this.options.merrorInheritFont&&(l.outputData.merrorFamily=v),l.state(h.STATE.METRICS)}}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},e.prototype.getMetricsFor=function(t,e){var r=this.options.mtextInheritFont||this.options.merrorInheritFont,o=this.getTestElement(t,e),i=this.measureMetrics(o,r);return this.adaptor.remove(o),i},e.prototype.getMetricMaps=function(t){var e,r,o,i,n,a,l,c,u,p,d=this.adaptor,f=[new Map,new Map];try{for(var m=s(t.math),y=m.next();!y.done;y=m.next()){var v=y.value;if((S=d.parent(v.start.node))&&v.state()<h.STATE.METRICS){var b=f[v.display?1:0];b.has(S)||b.set(S,this.getTestElement(S,v.display))}}}catch(t){e={error:t}}finally{try{y&&!y.done&&(r=m.return)&&r.call(m)}finally{if(e)throw e.error}}var x=this.options.mtextInheritFont||this.options.merrorInheritFont,g=[new Map,new Map];try{for(var M=s(g.keys()),_=M.next();!_.done;_=M.next()){var w=_.value;try{for(var C=(n=void 0,s(f[w].keys())),j=C.next();!j.done;j=C.next()){var S=j.value;g[w].set(S,this.measureMetrics(f[w].get(S),x))}}catch(t){n={error:t}}finally{try{j&&!j.done&&(a=C.return)&&a.call(C)}finally{if(n)throw n.error}}}}catch(t){o={error:t}}finally{try{_&&!_.done&&(i=M.return)&&i.call(M)}finally{if(o)throw o.error}}try{for(var T=s(g.keys()),O=T.next();!O.done;O=T.next()){w=O.value;try{for(var L=(u=void 0,s(f[w].values())),B=L.next();!B.done;B=L.next()){S=B.value;d.remove(S)}}catch(t){u={error:t}}finally{try{B&&!B.done&&(p=L.return)&&p.call(L)}finally{if(u)throw u.error}}}}catch(t){l={error:t}}finally{try{O&&!O.done&&(c=T.return)&&c.call(T)}finally{if(l)throw l.error}}return g},e.prototype.getTestElement=function(t,e){var r=this.adaptor;if(!this.testInline){this.testInline=this.html("mjx-test",{style:{display:"inline-block",width:"100%","font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","text-indent":0,"text-transform":"none","letter-spacing":"normal","word-spacing":"normal",overflow:"hidden",height:"1px","margin-right":"-1px"}},[this.html("mjx-left-box",{style:{display:"inline-block",width:0,float:"left"}}),this.html("mjx-ex-box",{style:{position:"absolute",overflow:"hidden",width:"1px",height:"60ex"}}),this.html("mjx-right-box",{style:{display:"inline-block",width:0,float:"right"}})]),this.testDisplay=r.clone(this.testInline),r.setStyle(this.testDisplay,"display","table"),r.setStyle(this.testDisplay,"margin-right",""),r.setStyle(r.firstChild(this.testDisplay),"display","none");var o=r.lastChild(this.testDisplay);r.setStyle(o,"display","table-cell"),r.setStyle(o,"width","10000em"),r.setStyle(o,"float","")}return r.append(t,r.clone(e?this.testDisplay:this.testInline))},e.prototype.measureMetrics=function(t,e){var r=this.adaptor,o=e?r.fontFamily(t):"",i=r.fontSize(t),n=r.nodeSize(r.childNode(t,1))[1]/60||i*this.options.exFactor;return{em:i,ex:n,containerWidth:"table"===r.getStyle(t,"display")?r.nodeSize(r.lastChild(t))[0]-1:r.nodeBBox(r.lastChild(t)).left-r.nodeBBox(r.firstChild(t)).left-2,lineWidth:1e6,scale:Math.max(this.options.minScale,this.options.matchFontHeight?n/this.font.params.x_height/i:1),family:o}},e.prototype.styleSheet=function(t){var e,r,o,i;if(this.setDocument(t),this.cssStyles.clear(),this.cssStyles.addStyles(this.constructor.commonStyles),"getStyles"in t)try{for(var n=s(t.getStyles()),a=n.next();!a.done;a=n.next()){var l=a.value;this.cssStyles.addStyles(l)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}try{for(var h=s(this.factory.getKinds()),c=h.next();!c.done;c=h.next()){var u=c.value;this.addClassStyles(this.factory.getNodeClass(u))}}catch(t){o={error:t}}finally{try{c&&!c.done&&(i=h.return)&&i.call(h)}finally{if(o)throw o.error}}return this.cssStyles.addStyles(this.font.styles),this.html("style",{id:"MJX-styles"},[this.text("\n"+this.cssStyles.cssText+"\n")])},e.prototype.addClassStyles=function(t){this.cssStyles.addStyles(t.styles)},e.prototype.setDocument=function(t){t&&(this.document=t,this.adaptor.document=t.document)},e.prototype.html=function(t,e,r,o){return void 0===e&&(e={}),void 0===r&&(r=[]),this.adaptor.node(t,e,r,o)},e.prototype.text=function(t){return this.adaptor.text(t)},e.prototype.fixed=function(t,e){return void 0===e&&(e=3),Math.abs(t)<6e-4?"0":t.toFixed(e).replace(/\.?0+$/,"")},e.prototype.measureText=function(t,e,r){void 0===r&&(r=["",!1,!1]);var o=this.unknownText(t,e);if("-explicitFont"===e){var i=this.cssFontStyles(r);this.adaptor.setAttributes(o,{style:i})}return this.measureTextNodeWithCache(o,t,e,r)},e.prototype.measureTextNodeWithCache=function(t,e,r,o){void 0===o&&(o=["",!1,!1]),"-explicitFont"===r&&(r=[o[0],o[1]?"T":"F",o[2]?"T":"F",""].join("-")),this.unknownCache.has(r)||this.unknownCache.set(r,new Map);var i=this.unknownCache.get(r),n=i.get(e);if(n)return n;var a=this.measureTextNode(t);return i.set(e,a),a},e.prototype.measureXMLnode=function(t){var e=this.adaptor,r=this.html("mjx-xml-block",{style:{display:"inline-block"}},[e.clone(t)]),o=this.html("mjx-baseline",{style:{display:"inline-block",width:0,height:0}}),i=this.html("mjx-measure-xml",{style:{position:"absolute",display:"inline-block","font-family":"initial","line-height":"normal"}},[o,r]);e.append(e.parent(this.math.start.node),this.container),e.append(this.container,i);var n=this.math.metrics.em*this.math.metrics.scale,a=e.nodeBBox(r),s=a.left,l=a.right,h=a.bottom,c=a.top,u=(l-s)/n,p=(e.nodeBBox(o).top-c)/n,d=(h-c)/n-p;return e.remove(this.container),e.remove(i),{w:u,h:p,d:d}},e.prototype.cssFontStyles=function(t,e){void 0===e&&(e={});var r=a(t,3),o=r[0],i=r[1],n=r[2];return e["font-family"]=this.font.getFamily(o),i&&(e["font-style"]="italic"),n&&(e["font-weight"]="bold"),e},e.prototype.getFontData=function(t){return t||(t=new p.Styles),[this.font.getFamily(t.get("font-family")),"italic"===t.get("font-style"),"bold"===t.get("font-weight")]},e.NAME="Common",e.OPTIONS=n(n({},l.AbstractOutputJax.OPTIONS),{scale:1,minScale:.5,matchFontHeight:!0,mtextInheritFont:!1,merrorInheritFont:!1,mtextFont:"",merrorFont:"serif",mathmlSpacing:!1,skipAttributes:{},exFactor:.5,displayAlign:"center",displayIndent:"0",wrapperFactory:null,font:null,cssStyles:null}),e.commonStyles={},e}(l.AbstractOutputJax);e.CommonOutputJax=f},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLWrapperFactory=void 0;var n=r(19),a=r(20),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.defaultNodes=a.CHTMLWrappers,e}(n.CommonWrapperFactory);e.CHTMLWrapperFactory=s},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonWrapperFactory=void 0;var n=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.jax=null,e}return i(e,t),Object.defineProperty(e.prototype,"Wrappers",{get:function(){return this.node},enumerable:!1,configurable:!0}),e.defaultNodes={},e}(r(73).AbstractWrapperFactory);e.CommonWrapperFactory=n},function(t,e,r){"use strict";var o;Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLWrappers=void 0;var i=r(0),n=r(22),a=r(24),s=r(26),l=r(28),h=r(30),c=r(32),u=r(34),p=r(36),d=r(38),f=r(41),m=r(42),y=r(44),v=r(11),b=r(47),x=r(7),g=r(51),M=r(52),_=r(54),w=r(56),C=r(57),j=r(59),S=r(60),T=r(62),O=r(64),L=r(66);e.CHTMLWrappers=((o={})[n.CHTMLmath.kind]=n.CHTMLmath,o[f.CHTMLmrow.kind]=f.CHTMLmrow,o[f.CHTMLinferredMrow.kind]=f.CHTMLinferredMrow,o[a.CHTMLmi.kind]=a.CHTMLmi,o[s.CHTMLmo.kind]=s.CHTMLmo,o[l.CHTMLmn.kind]=l.CHTMLmn,o[h.CHTMLms.kind]=h.CHTMLms,o[c.CHTMLmtext.kind]=c.CHTMLmtext,o[u.CHTMLmspace.kind]=u.CHTMLmspace,o[p.CHTMLmpadded.kind]=p.CHTMLmpadded,o[d.CHTMLmenclose.kind]=d.CHTMLmenclose,o[y.CHTMLmfrac.kind]=y.CHTMLmfrac,o[v.CHTMLmsqrt.kind]=v.CHTMLmsqrt,o[b.CHTMLmroot.kind]=b.CHTMLmroot,o[x.CHTMLmsub.kind]=x.CHTMLmsub,o[x.CHTMLmsup.kind]=x.CHTMLmsup,o[x.CHTMLmsubsup.kind]=x.CHTMLmsubsup,o[g.CHTMLmunder.kind]=g.CHTMLmunder,o[g.CHTMLmover.kind]=g.CHTMLmover,o[g.CHTMLmunderover.kind]=g.CHTMLmunderover,o[M.CHTMLmmultiscripts.kind]=M.CHTMLmmultiscripts,o[m.CHTMLmfenced.kind]=m.CHTMLmfenced,o[_.CHTMLmtable.kind]=_.CHTMLmtable,o[w.CHTMLmtr.kind]=w.CHTMLmtr,o[w.CHTMLmlabeledtr.kind]=w.CHTMLmlabeledtr,o[C.CHTMLmtd.kind]=C.CHTMLmtd,o[j.CHTMLmaction.kind]=j.CHTMLmaction,o[S.CHTMLmglyph.kind]=S.CHTMLmglyph,o[T.CHTMLsemantics.kind]=T.CHTMLsemantics,o[T.CHTMLannotation.kind]=T.CHTMLannotation,o[T.CHTMLannotationXML.kind]=T.CHTMLannotationXML,o[T.CHTMLxml.kind]=T.CHTMLxml,o[O.CHTMLTeXAtom.kind]=O.CHTMLTeXAtom,o[L.CHTMLTextNode.kind]=L.CHTMLTextNode,o[i.CHTMLWrapper.kind]=i.CHTMLWrapper,o)},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonWrapper=void 0;var l=r(74),h=r(5),c=r(3),u=r(2),p=r(69),d=r(1),f=r(4);function m(t,e){return t?e<.1111111111111111?0:.1111111111111111:e}var y=function(t){function e(e,r,o){void 0===o&&(o=null);var i=t.call(this,e,r)||this;return i.parent=null,i.removedStyles=null,i.styles=null,i.variant="",i.bboxComputed=!1,i.stretch=f.NOSTRETCH,i.font=null,i.parent=o,i.font=e.jax.font,i.bbox=d.BBox.zero(),i.getStyles(),i.getVariant(),i.getScale(),i.getSpace(),i.childNodes=r.childNodes.map((function(t){var e=i.wrap(t);return e.bbox.pwidth&&(r.notParent||r.isKind("math"))&&(i.bbox.pwidth=d.BBox.fullWidth),e})),i}return i(e,t),Object.defineProperty(e.prototype,"jax",{get:function(){return this.factory.jax},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"adaptor",{get:function(){return this.factory.jax.adaptor},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metrics",{get:function(){return this.factory.jax.math.metrics},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!this.node.notParent&&!this.node.isToken},enumerable:!1,configurable:!0}),e.prototype.wrap=function(t,e){void 0===e&&(e=null);var r=this.factory.wrap(t,e||this);return e&&e.childNodes.push(r),this.jax.nodeMap.set(t,r),r},e.prototype.getBBox=function(t){if(void 0===t&&(t=!0),this.bboxComputed)return this.bbox;var e=t?this.bbox:d.BBox.zero();return this.computeBBox(e),this.bboxComputed=t,e},e.prototype.computeBBox=function(t,e){var r,o;void 0===e&&(e=!1),t.empty();try{for(var i=n(this.childNodes),a=i.next();!a.done;a=i.next()){var s=a.value;t.append(s.getBBox())}}catch(t){r={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}t.clean(),this.fixesPWidth&&this.setChildPWidths(e)&&this.computeBBox(t,!0)},e.prototype.setChildPWidths=function(t,e,r){var o,i;if(void 0===e&&(e=null),void 0===r&&(r=!0),t)return!1;r&&(this.bbox.pwidth="");var a=!1;try{for(var s=n(this.childNodes),l=s.next();!l.done;l=s.next()){var h=l.value,c=h.getBBox();c.pwidth&&h.setChildPWidths(t,null===e?c.w:e,r)&&(a=!0)}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=s.return)&&i.call(s)}finally{if(o)throw o.error}}return a},e.prototype.invalidateBBox=function(){this.bboxComputed&&(this.bboxComputed=!1,this.parent&&this.parent.invalidateBBox())},e.prototype.copySkewIC=function(t){var e=this.childNodes[0];e&&e.bbox.sk&&(t.sk=e.bbox.sk);var r=this.childNodes[this.childNodes.length-1];r&&r.bbox.ic&&(t.ic=r.bbox.ic,t.w+=t.ic)},e.prototype.getStyles=function(){var t=this.node.attributes.getExplicit("style");if(t)for(var r=this.styles=new p.Styles(t),o=0,i=e.removeStyles.length;o<i;o++){var n=e.removeStyles[o];r.get(n)&&(this.removedStyles||(this.removedStyles={}),this.removedStyles[n]=r.get(n),r.set(n,""))}},e.prototype.getVariant=function(){if(this.node.isToken){var t=this.node.attributes,r=t.get("mathvariant");if(!t.getExplicit("mathvariant")){var o=t.getList("fontfamily","fontweight","fontstyle");if(this.removedStyles){var i=this.removedStyles;i.fontFamily&&(o.family=i.fontFamily),i.fontWeight&&(o.weight=i.fontWeight),i.fontStyle&&(o.style=i.fontStyle)}o.fontfamily&&(o.family=o.fontfamily),o.fontweight&&(o.weight=o.fontweight),o.fontstyle&&(o.style=o.fontstyle),o.weight&&o.weight.match(/^\d+$/)&&(o.weight=parseInt(o.weight)>600?"bold":"normal"),o.family?r=this.explicitVariant(o.family,o.weight,o.style):(this.node.getProperty("variantForm")&&(r="-tex-variant"),r=(e.BOLDVARIANTS[o.weight]||{})[r]||r,r=(e.ITALICVARIANTS[o.style]||{})[r]||r)}this.variant=r}},e.prototype.explicitVariant=function(t,e,r){var o=this.styles;return o||(o=this.styles=new p.Styles),o.set("fontFamily",t),e&&o.set("fontWeight",e),r&&o.set("fontStyle",r),"-explicitFont"},e.prototype.getScale=function(){var t=1,e=this.parent,r=e?e.bbox.scale:1,o=this.node.attributes,i=Math.min(o.get("scriptlevel"),2),n=o.get("fontsize"),a=this.node.isToken||this.node.isKind("mstyle")?o.get("mathsize"):o.getInherited("mathsize");if(0!==i){t=Math.pow(o.get("scriptsizemultiplier"),i);var s=this.length2em(o.get("scriptminsize"),.8,1);t<s&&(t=s)}this.removedStyles&&this.removedStyles.fontSize&&!n&&(n=this.removedStyles.fontSize),n&&!o.getExplicit("mathsize")&&(a=n),"1"!==a&&(t*=this.length2em(a,1,1)),this.bbox.scale=t,this.bbox.rscale=t/r},e.prototype.getSpace=function(){var t=this.isTopEmbellished(),e=this.node.hasSpacingAttributes();this.jax.options.mathmlSpacing||e?t&&this.getMathMLSpacing():this.getTeXSpacing(t,e)},e.prototype.getMathMLSpacing=function(){var t=this.node.coreMO(),e=t.attributes,r=e.get("scriptlevel")>0;this.bbox.L=e.isSet("lspace")?Math.max(0,this.length2em(e.get("lspace"))):m(r,t.lspace),this.bbox.R=e.isSet("rspace")?Math.max(0,this.length2em(e.get("rspace"))):m(r,t.rspace)},e.prototype.getTeXSpacing=function(t,e){if(!e){var r=this.node.texSpacing();r&&(this.bbox.L=this.length2em(r))}if(t||e){var o=this.node.coreMO().attributes;o.isSet("lspace")&&(this.bbox.L=Math.max(0,this.length2em(o.get("lspace")))),o.isSet("rspace")&&(this.bbox.R=Math.max(0,this.length2em(o.get("rspace"))))}},e.prototype.isTopEmbellished=function(){return this.node.isEmbellished&&!(this.node.Parent&&this.node.Parent.isEmbellished)},e.prototype.core=function(){return this.jax.nodeMap.get(this.node.core())},e.prototype.coreMO=function(){return this.jax.nodeMap.get(this.node.coreMO())},e.prototype.getText=function(){var t,e,r="";if(this.node.isToken)try{for(var o=n(this.node.childNodes),i=o.next();!i.done;i=o.next()){var a=i.value;a instanceof h.TextNode&&(r+=a.getText())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},e.prototype.canStretch=function(t){if(this.stretch=f.NOSTRETCH,this.node.isEmbellished){var e=this.core();e&&e.node!==this.node&&e.canStretch(t)&&(this.stretch=e.stretch)}return 0!==this.stretch.dir},e.prototype.getAlignShift=function(){var t,e=(t=this.node.attributes).getList.apply(t,s(h.indentAttributes)),r=e.indentalign,o=e.indentshift,i=e.indentalignfirst,n=e.indentshiftfirst;return"indentalign"!==i&&(r=i),"auto"===r&&(r=this.jax.options.displayAlign),"indentshift"!==n&&(o=n),"auto"===o&&(o=this.jax.options.displayIndent,"right"!==r||o.match(/^\s*0[a-z]*\s*$/)||(o=("-"+o.trim()).replace(/^--/,""))),[r,this.length2em(o,this.metrics.containerWidth)]},e.prototype.getAlignX=function(t,e,r){return"right"===r?t-(e.w+e.R)*e.rscale:"left"===r?e.L*e.rscale:(t-e.w*e.rscale)/2},e.prototype.getAlignY=function(t,e,r,o,i){return"top"===i?t-r:"bottom"===i?o-e:"middle"===i?(t-r-(e-o))/2:0},e.prototype.getWrapWidth=function(t){return this.childNodes[t].getBBox().w},e.prototype.getChildAlign=function(t){return"left"},e.prototype.percent=function(t){return u.percent(t)},e.prototype.em=function(t){return u.em(t)},e.prototype.px=function(t,e){return void 0===e&&(e=-u.BIGDIMEN),u.px(t,e,this.metrics.em)},e.prototype.length2em=function(t,e,r){return void 0===e&&(e=1),void 0===r&&(r=null),null===r&&(r=this.bbox.scale),u.length2em(t,e,r,this.jax.pxPerEm)},e.prototype.unicodeChars=function(t,e){void 0===e&&(e=this.variant);var r=c.unicodeChars(t),o=this.font.getVariant(e);if(o&&o.chars){var i=o.chars;r=r.map((function(t){return((i[t]||[])[3]||{}).smp||t}))}return r},e.prototype.remapChars=function(t){return t},e.prototype.mmlText=function(t){return this.node.factory.create("text").setText(t)},e.prototype.mmlNode=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r=[]),this.node.factory.create(t,e,r)},e.prototype.createMo=function(t){var e=this.node.factory,r=e.create("text").setText(t),o=e.create("mo",{stretchy:!0},[r]);o.inheritAttributesFrom(this.node);var i=this.wrap(o);return i.parent=this,i},e.prototype.getVariantChar=function(t,e){var r=this.font.getChar(t,e)||[0,0,0,{unknown:!0}];return 3===r.length&&(r[3]={}),r},e.kind="unknown",e.styles={},e.removeStyles=["fontSize","fontFamily","fontWeight","fontStyle","fontVariant","font"],e.skipAttributes={fontfamily:!0,fontsize:!0,fontweight:!0,fontstyle:!0,color:!0,background:!0,class:!0,href:!0,style:!0,xmlns:!0},e.BOLDVARIANTS={bold:{normal:"bold",italic:"bold-italic",fraktur:"bold-fraktur",script:"bold-script","sans-serif":"bold-sans-serif","sans-serif-italic":"sans-serif-bold-italic"},normal:{bold:"normal","bold-italic":"italic","bold-fraktur":"fraktur","bold-script":"script","bold-sans-serif":"sans-serif","sans-serif-bold-italic":"sans-serif-italic"}},e.ITALICVARIANTS={italic:{normal:"italic",bold:"bold-italic","sans-serif":"sans-serif-italic","bold-sans-serif":"sans-serif-bold-italic"},normal:{italic:"normal","bold-italic":"bold","sans-serif-italic":"sans-serif","sans-serif-bold-italic":"bold-sans-serif"}},e}(l.AbstractWrapper);e.CommonWrapper=y},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmath=void 0;var a=r(0),s=r(23),l=r(75),h=r(1),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){t.prototype.toCHTML.call(this,e);var r=this.chtml,o=this.adaptor;"block"===this.node.attributes.get("display")?(o.setAttribute(r,"display","true"),o.setAttribute(e,"display","true"),this.handleDisplay(e)):this.handleInline(e),o.addClass(r,"MJX-TEX")},e.prototype.handleDisplay=function(t){var e=this.adaptor,r=n(this.getAlignShift(),2),o=r[0],i=r[1];if("center"!==o&&e.setAttribute(t,"justify",o),this.bbox.pwidth===h.BBox.fullWidth){if(e.setAttribute(t,"width","full"),this.jax.table){var a=this.jax.table.getBBox(),s=a.L,l=a.w,c=a.R;"right"===o?c=Math.max(c||-i,-i):"left"===o?s=Math.max(s||i,i):"center"===o&&(l+=2*Math.abs(i));var u=this.em(Math.max(0,s+l+c));e.setStyle(t,"min-width",u),e.setStyle(this.jax.table.chtml,"min-width",u)}}else this.setIndent(this.chtml,o,i)},e.prototype.handleInline=function(t){var e=this.adaptor,r=e.getStyle(this.chtml,"margin-right");r&&(e.setStyle(this.chtml,"margin-right",""),e.setStyle(t,"margin-right",r),e.setStyle(t,"width","0"))},e.prototype.setChildPWidths=function(e,r,o){return void 0===r&&(r=null),void 0===o&&(o=!0),!!this.parent&&t.prototype.setChildPWidths.call(this,e,r,o)},e.kind=l.MmlMath.prototype.kind,e.styles={"mjx-math":{"line-height":0,"text-align":"left","text-indent":0,"font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","letter-spacing":"normal","word-wrap":"normal","word-spacing":"normal","white-space":"nowrap",direction:"ltr",padding:"1px 0"},'mjx-container[jax="CHTML"][display="true"]':{display:"block","text-align":"center",margin:"1em 0"},'mjx-container[jax="CHTML"][display="true"][width="full"]':{display:"flex"},'mjx-container[jax="CHTML"][display="true"] mjx-math':{padding:0},'mjx-container[jax="CHTML"][justify="left"]':{"text-align":"left"},'mjx-container[jax="CHTML"][justify="right"]':{"text-align":"right"}},e}(s.CommonMathMixin(a.CHTMLWrapper));e.CHTMLmath=c},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMathMixin=void 0,e.CommonMathMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.getWrapWidth=function(t){return this.parent?this.getBBox().w:this.metrics.containerWidth/this.jax.pxPerEm},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmi=void 0;var n=r(0),a=r(25),s=r(76),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){t.prototype.toCHTML.call(this,e),this.noIC&&this.adaptor.setAttribute(this.chtml,"noIC","true")},e.kind=s.MmlMi.prototype.kind,e}(a.CommonMiMixin(n.CHTMLWrapper));e.CHTMLmi=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMiMixin=void 0,e.CommonMiMixin=function(t){return function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.noIC=!1,e}return i(e,t),e.prototype.computeBBox=function(e,r){void 0===r&&(r=!1),t.prototype.computeBBox.call(this,e),this.copySkewIC(e),this.noIC&&(e.w-=e.ic)},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmo=void 0;var a=r(0),s=r(27),l=r(77),h=r(1),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){var r,o,i=this.node.attributes,a=i.get("symmetric")&&2!==this.stretch.dir,s=0!==this.stretch.dir;s&&null===this.size&&this.getStretchedVariant([]);var l=this.standardCHTMLnode(e);if(this.noIC&&this.adaptor.setAttribute(l,"noIC","true"),s&&this.size<0)this.stretchHTML(l);else{if(a||i.get("largeop")){var c=h.BBox.empty();t.prototype.computeBBox.call(this,c);var u=this.em((c.d-c.h)/2+this.font.params.axis_height);"0"!==u&&this.adaptor.setStyle(l,"verticalAlign",u)}try{for(var p=n(this.childNodes),d=p.next();!d.done;d=p.next()){d.value.toCHTML(l)}}catch(t){r={error:t}}finally{try{d&&!d.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}}},e.prototype.stretchHTML=function(t){var e=this.getText().codePointAt(0),r=this.stretch;r.used=!0;var o=r.stretch,i=[];o[0]&&i.push(this.html("mjx-beg",{},[this.html("mjx-c")])),i.push(this.html("mjx-ext",{},[this.html("mjx-c")])),4===o.length&&i.push(this.html("mjx-mid",{},[this.html("mjx-c")]),this.html("mjx-ext",{},[this.html("mjx-c")])),o[2]&&i.push(this.html("mjx-end",{},[this.html("mjx-c")]));var n={},a=this.bbox,l=a.h,h=a.d,c=a.w;1===r.dir?(i.push(this.html("mjx-mark")),n.height=this.em(l+h),n.verticalAlign=this.em(-h)):n.width=this.em(c);var u=s.DirectionVH[r.dir],p={class:this.char(r.c||e),style:n},d=this.html("mjx-stretchy-"+u,p,i);this.adaptor.append(t,d)},e.kind=l.MmlMo.prototype.kind,e.styles={"mjx-stretchy-h":{display:"inline-table",width:"100%"},"mjx-stretchy-h > *":{display:"table-cell",width:0},"mjx-stretchy-h > * > mjx-c":{display:"inline-block",transform:"scalex(1.0000001)"},"mjx-stretchy-h > * > mjx-c::before":{display:"inline-block",padding:".001em 0",width:"initial"},"mjx-stretchy-h > mjx-ext":{overflow:"hidden",width:"100%"},"mjx-stretchy-h > mjx-ext > mjx-c::before":{transform:"scalex(500)"},"mjx-stretchy-h > mjx-ext > mjx-c":{width:0},"mjx-stretchy-h > mjx-beg > mjx-c":{"margin-right":"-.1em"},"mjx-stretchy-h > mjx-end > mjx-c":{"margin-left":"-.1em"},"mjx-stretchy-v":{display:"inline-block"},"mjx-stretchy-v > *":{display:"block"},"mjx-stretchy-v > mjx-beg":{height:0},"mjx-stretchy-v > mjx-end > mjx-c":{display:"block"},"mjx-stretchy-v > * > mjx-c":{transform:"scaley(1.0000001)","transform-origin":"left center",overflow:"hidden"},"mjx-stretchy-v > mjx-ext":{display:"block",height:"100%","box-sizing":"border-box",border:"0px solid transparent",overflow:"hidden"},"mjx-stretchy-v > mjx-ext > mjx-c::before":{width:"initial"},"mjx-stretchy-v > mjx-ext > mjx-c":{transform:"scaleY(500) translateY(.1em)",overflow:"visible"},"mjx-mark":{display:"inline-block",height:"0px"}},e}(s.CommonMoMixin(a.CHTMLWrapper));e.CHTMLmo=c},function(t,e,r){"use strict";var o,i,n=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},s=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(a(arguments[e]));return t},l=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMoMixin=e.DirectionVH=void 0;var h=r(4);e.DirectionVH=((i={})[1]="v",i[2]="h",i),e.CommonMoMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,s(e))||this;return o.noIC=!1,o.size=null,o.isAccent=o.node.isAccent,o}return n(e,t),e.prototype.computeBBox=function(e,r){void 0===r&&(r=!1);var o=0!==this.stretch.dir;if(o&&null===this.size&&this.getStretchedVariant([0]),!(o&&this.size<0)&&(t.prototype.computeBBox.call(this,e),this.copySkewIC(e),this.noIC&&(e.w-=e.ic),this.node.attributes.get("symmetric")&&2!==this.stretch.dir)){var i=(e.h+e.d)/2+this.font.params.axis_height-e.h;e.h+=i,e.d-=i}},e.prototype.getVariant=function(){this.node.attributes.get("largeop")?this.variant=this.node.attributes.get("displaystyle")?"-largeop":"-smallop":t.prototype.getVariant.call(this)},e.prototype.canStretch=function(t){if(0!==this.stretch.dir)return this.stretch.dir===t;if(!this.node.attributes.get("stretchy"))return!1;var e=this.getText();if(1!==Array.from(e).length)return!1;var r=this.font.getDelimiter(e.codePointAt(0));return this.stretch=r&&r.dir===t?r:h.NOSTRETCH,0!==this.stretch.dir},e.prototype.getStretchedVariant=function(t,e){var r,o;if(void 0===e&&(e=!1),0!==this.stretch.dir){var i=this.getWH(t),n=this.getSize("minsize",0),a=this.getSize("maxsize",1/0);i=Math.max(n,Math.min(a,i));var s=n||e?i:Math.max(i*this.font.params.delimiterfactor/1e3,i-this.font.params.delimitershortfall),h=this.stretch,c=h.c||this.getText().codePointAt(0),u=0;if(h.sizes)try{for(var p=l(h.sizes),d=p.next();!d.done;d=p.next()){if(d.value>=s)return this.variant=this.font.getSizeVariant(c,u),void(this.size=u);u++}}catch(t){r={error:t}}finally{try{d&&!d.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}h.stretch?(this.size=-1,this.invalidateBBox(),this.getStretchBBox(t,i,h)):(this.variant=this.font.getSizeVariant(c,u-1),this.size=u-1)}},e.prototype.getSize=function(t,e){var r=this.node.attributes;return r.isSet(t)&&(e=this.length2em(r.get(t),1,1)),e},e.prototype.getWH=function(t){if(0===t.length)return 0;if(1===t.length)return t[0];var e=a(t,2),r=e[0],o=e[1],i=this.font.params.axis_height;return this.node.attributes.get("symmetric")?2*Math.max(r-i,o+i):r+o},e.prototype.getStretchBBox=function(t,e,r){var o;r.hasOwnProperty("min")&&r.min>e&&(e=r.min);var i=a(r.HDW,3),n=i[0],s=i[1],l=i[2];1===this.stretch.dir?(n=(o=a(this.getBaseline(t,e,r),2))[0],s=o[1]):l=e,this.bbox.h=n,this.bbox.d=s,this.bbox.w=l},e.prototype.getBaseline=function(t,e,r){var o=2===t.length&&t[0]+t[1]===e,i=this.node.attributes.get("symmetric"),n=a(o?t:[e,0],2),s=n[0],l=n[1],h=a([s+l,0],2),c=h[0],u=h[1];if(i){var p=this.font.params.axis_height;o&&(c=2*Math.max(s-p,l+p)),u=c/2-p}else if(o)u=l;else{var d=a(r.HDW||[.75,.25],2),f=d[0],m=d[1];u=m*(c/(f+m))}return[c-u,u]},e.prototype.remapChars=function(t){if(1===t.length){var e=this.node.coreParent().parent,r=this.isAccent&&!e.isKind("mrow")?"accent":"mo",o=this.font.getRemappedChar(r,t[0]);o&&(t=this.unicodeChars(o,this.variant))}return t},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmn=void 0;var n=r(0),a=r(29),s=r(78),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=s.MmlMn.prototype.kind,e}(a.CommonMnMixin(n.CHTMLWrapper));e.CHTMLmn=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMnMixin=void 0,e.CommonMnMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.remapChars=function(t){if(t.length){var e=this.font.getRemappedChar("mn",t[0]);if(e){var r=this.unicodeChars(e,this.variant);1===r.length?t[0]=r[0]:t=r.concat(t.slice(1))}}return t},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLms=void 0;var n=r(0),a=r(31),s=r(79),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=s.MmlMs.prototype.kind,e}(a.CommonMsMixin(n.CHTMLWrapper));e.CHTMLms=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsMixin=void 0,e.CommonMsMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this,i=o.node.attributes,n=i.getList("lquote","rquote");return"monospace"!==o.variant&&(i.isSet("lquote")||'"'!==n.lquote||(n.lquote="\u201c"),i.isSet("rquote")||'"'!==n.rquote||(n.rquote="\u201d")),o.childNodes.unshift(o.createText(n.lquote)),o.childNodes.push(o.createText(n.rquote)),o}return i(e,t),e.prototype.createText=function(t){var e=this.wrap(this.mmlText(t));return e.parent=this,e},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmtext=void 0;var n=r(0),a=r(33),s=r(80),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=s.MmlMtext.prototype.kind,e}(a.CommonMtextMixin(n.CHTMLWrapper));e.CHTMLmtext=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtextMixin=void 0,e.CommonMtextMixin=function(t){var e;return(e=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.getVariant=function(){var e=this.jax.options,r=this.jax.math.outputData,o=(!!r.merrorFamily||!!e.merrorFont)&&this.node.Parent.isKind("merror");if(r.mtextFamily||e.mtextFont||o){var i=this.node.attributes.get("mathvariant"),n=this.constructor.INHERITFONTS[i]||this.jax.font.getCssFont(i),a=n[0]||(o?r.merrorFamily||e.merrorFont:r.mtextFamily||e.mtextFont);this.variant=this.explicitVariant(a,n[2]?"bold":"",n[1]?"italic":"")}else t.prototype.getVariant.call(this)},e}(t)).INHERITFONTS={normal:["",!1,!1],bold:["",!1,!0],italic:["",!0,!1],"bold-italic":["",!0,!0]},e}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmspace=void 0;var n=r(0),a=r(35),s=r(81),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=this.getBBox(),o=r.w,i=r.h,n=r.d;o<0&&(this.adaptor.setStyle(e,"marginRight",this.em(o)),o=0),o&&this.adaptor.setStyle(e,"width",this.em(o)),(i=Math.max(0,i+n))&&this.adaptor.setStyle(e,"height",this.em(Math.max(0,i))),n&&this.adaptor.setStyle(e,"verticalAlign",this.em(-n))},e.kind=s.MmlMspace.prototype.kind,e}(a.CommonMspaceMixin(n.CHTMLWrapper));e.CHTMLmspace=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMspaceMixin=void 0,e.CommonMspaceMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.node.attributes;t.w=this.length2em(r.get("width"),0),t.h=this.length2em(r.get("height"),0),t.d=this.length2em(r.get("depth"),0)},e.prototype.handleVariant=function(){},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmpadded=void 0;var s=r(0),l=r(37),h=r(82),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e,r,o=this.standardCHTMLnode(t),i=[],s={},l=n(this.getDimens(),9),h=l[2],c=l[3],u=l[4],p=l[5],d=l[6],f=l[7],m=l[8];if(p&&(s.width=this.em(h+p)),(c||u)&&(s.margin=this.em(c)+" 0 "+this.em(u)),d+m||f){s.position="relative";var y=this.html("mjx-rbox",{style:{left:this.em(d+m),top:this.em(-f)}});d+m&&this.childNodes[0].getBBox().pwidth&&(this.adaptor.setAttribute(y,"width","full"),this.adaptor.setStyle(y,"left",this.em(d))),i.push(y)}o=this.adaptor.append(o,this.html("mjx-block",{style:s},i));try{for(var v=a(this.childNodes),b=v.next();!b.done;b=v.next()){b.value.toCHTML(i[0]||o)}}catch(t){e={error:t}}finally{try{b&&!b.done&&(r=v.return)&&r.call(v)}finally{if(e)throw e.error}}},e.kind=h.MmlMpadded.prototype.kind,e.styles={"mjx-mpadded":{display:"inline-block"},"mjx-rbox":{display:"inline-block",position:"relative"}},e}(l.CommonMpaddedMixin(s.CHTMLWrapper));e.CHTMLmpadded=c},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMpaddedMixin=void 0,e.CommonMpaddedMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.getDimens=function(){var t=this.node.attributes.getList("width","height","depth","lspace","voffset"),e=this.childNodes[0].getBBox(),r=e.w,o=e.h,i=e.d,n=r,a=o,s=i,l=0,h=0,c=0;""!==t.width&&(r=this.dimen(t.width,e,"w",0)),""!==t.height&&(o=this.dimen(t.height,e,"h",0)),""!==t.depth&&(i=this.dimen(t.depth,e,"d",0)),""!==t.voffset&&(h=this.dimen(t.voffset,e)),""!==t.lspace&&(l=this.dimen(t.lspace,e));var u=this.node.attributes.get("data-align");return u&&(c=this.getAlignX(r,e,u)),[a,s,n,o-a,i-s,r-n,l,h,c]},e.prototype.dimen=function(t,e,r,o){void 0===r&&(r=""),void 0===o&&(o=null);var i=(t=String(t)).match(/width|height|depth/),n=i?e[i[0].charAt(0)]:r?e[r]:0,a=this.length2em(t,n)||0;return t.match(/^[-+]/)&&r&&(a+=n),null!=o&&(a=Math.max(o,a)),a},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=n(this.getDimens(),6),o=r[0],i=r[1],a=r[2],s=r[3],l=r[4],h=r[5];t.w=a+h,t.h=o+s,t.d=i+l,this.setChildPWidths(e,t.w)},e.prototype.getWrapWidth=function(t){return this.getBBox().w},e.prototype.getChildAlign=function(t){return this.node.attributes.get("data-align")||"left"},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmenclose=void 0;var s=r(0),l=r(39),h=r(40),c=r(83),u=r(2);function p(t,e){return Math.atan2(t,e).toFixed(3).replace(/\.?0+$/,"")}var d=p(h.ARROWDX,h.ARROWY),f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e,r,o,i,a=this.adaptor,s=this.standardCHTMLnode(t),l=a.append(s,this.html("mjx-box"));this.renderChild?this.renderChild(this,l):this.childNodes[0].toCHTML(l);try{for(var c=n(Object.keys(this.notations)),u=c.next();!u.done;u=c.next()){var p=u.value,d=this.notations[p];!d.renderChild&&d.renderer(this,l)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}var f=this.getPadding();try{for(var m=n(h.sideNames),y=m.next();!y.done;y=m.next()){var v=y.value,b=h.sideIndex[v];f[b]>0&&a.setStyle(l,"padding-"+v,this.em(f[b]))}}catch(t){o={error:t}}finally{try{y&&!y.done&&(i=m.return)&&i.call(m)}finally{if(o)throw o.error}}},e.prototype.arrow=function(t,e,r){void 0===r&&(r=!1);var o=this.getBBox().w,i={width:this.em(t)};o!==t&&(i.left=this.em((o-t)/2)),e&&(i.transform="rotate("+this.fixed(e)+"rad)");var n=this.html("mjx-arrow",{style:i},[this.html("mjx-aline"),this.html("mjx-rthead"),this.html("mjx-rbhead")]);return r&&(this.adaptor.append(n,this.html("mjx-lthead")),this.adaptor.append(n,this.html("mjx-lbhead")),this.adaptor.setAttribute(n,"double","true")),this.adjustArrow(n,r),n},e.prototype.adjustArrow=function(t,e){var r=this,o=this.thickness,i=this.arrowhead;if(i.x!==h.ARROWX||i.y!==h.ARROWY||i.dx!==h.ARROWDX||o!==h.THICKNESS){var n=a([o*i.x,o*i.y].map((function(t){return r.em(t)})),2),s=n[0],l=n[1],c=p(i.dx,i.y),u=a(this.adaptor.childNodes(t),5),d=u[0],f=u[1],m=u[2],y=u[3],v=u[4];this.adjustHead(f,[l,"0","1px",s],c),this.adjustHead(m,["1px","0",l,s],"-"+c),this.adjustHead(y,[l,s,"1px","0"],"-"+c),this.adjustHead(v,["1px",s,l,"0"],c),this.adjustLine(d,o,i.x,e)}},e.prototype.adjustHead=function(t,e,r){t&&(this.adaptor.setStyle(t,"border-width",e.join(" ")),this.adaptor.setStyle(t,"transform","skewX("+r+"rad)"))},e.prototype.adjustLine=function(t,e,r,o){this.adaptor.setStyle(t,"borderTop",this.em(e)+" solid"),this.adaptor.setStyle(t,"top",this.em(-e/2)),this.adaptor.setStyle(t,"right",this.em(e*(r-1))),o&&this.adaptor.setStyle(t,"left",this.em(e*(r-1)))},e.prototype.adjustBorder=function(t){return this.thickness!==h.THICKNESS&&this.adaptor.setStyle(t,"borderWidth",this.em(this.thickness)),t},e.prototype.adjustThickness=function(t){return this.thickness!==h.THICKNESS&&this.adaptor.setStyle(t,"strokeWidth",this.fixed(this.thickness)),t},e.prototype.fixed=function(t,e){return void 0===e&&(e=3),Math.abs(t)<6e-4?"0":t.toFixed(e).replace(/\.?0+$/,"")},e.prototype.em=function(e){return t.prototype.em.call(this,e)},e.kind=c.MmlMenclose.prototype.kind,e.styles={"mjx-menclose":{position:"relative"},"mjx-menclose > mjx-dstrike":{display:"inline-block",left:0,top:0,position:"absolute","border-top":h.SOLID,"transform-origin":"top left"},"mjx-menclose > mjx-ustrike":{display:"inline-block",left:0,bottom:0,position:"absolute","border-top":h.SOLID,"transform-origin":"bottom left"},"mjx-menclose > mjx-hstrike":{"border-top":h.SOLID,position:"absolute",left:0,right:0,bottom:"50%",transform:"translateY("+u.em(h.THICKNESS/2)+")"},"mjx-menclose > mjx-vstrike":{"border-left":h.SOLID,position:"absolute",top:0,bottom:0,right:"50%",transform:"translateX("+u.em(h.THICKNESS/2)+")"},"mjx-menclose > mjx-rbox":{position:"absolute",top:0,bottom:0,right:0,left:0,border:h.SOLID,"border-radius":u.em(h.THICKNESS+h.PADDING)},"mjx-menclose > mjx-cbox":{position:"absolute",top:0,bottom:0,right:0,left:0,border:h.SOLID,"border-radius":"50%"},"mjx-menclose > mjx-arrow":{position:"absolute",left:0,bottom:"50%",height:0,width:0},"mjx-menclose > mjx-arrow > *":{display:"block",position:"absolute","transform-origin":"bottom","border-left":u.em(h.THICKNESS*h.ARROWX)+" solid","border-right":0,"box-sizing":"border-box"},"mjx-menclose > mjx-arrow > mjx-aline":{left:0,top:u.em(-h.THICKNESS/2),right:u.em(h.THICKNESS*(h.ARROWX-1)),height:0,"border-top":u.em(h.THICKNESS)+" solid","border-left":0},"mjx-menclose > mjx-arrow[double] > mjx-aline":{left:u.em(h.THICKNESS*(h.ARROWX-1)),height:0},"mjx-menclose > mjx-arrow > mjx-rthead":{transform:"skewX("+d+"rad)",right:0,bottom:"-1px","border-bottom":"1px solid transparent","border-top":u.em(h.THICKNESS*h.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-rbhead":{transform:"skewX(-"+d+"rad)","transform-origin":"top",right:0,top:"-1px","border-top":"1px solid transparent","border-bottom":u.em(h.THICKNESS*h.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-lthead":{transform:"skewX(-"+d+"rad)",left:0,bottom:"-1px","border-left":0,"border-right":u.em(h.THICKNESS*h.ARROWX)+" solid","border-bottom":"1px solid transparent","border-top":u.em(h.THICKNESS*h.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-lbhead":{transform:"skewX("+d+"rad)","transform-origin":"top",left:0,top:"-1px","border-left":0,"border-right":u.em(h.THICKNESS*h.ARROWX)+" solid","border-top":"1px solid transparent","border-bottom":u.em(h.THICKNESS*h.ARROWY)+" solid transparent"},"mjx-menclose > dbox":{position:"absolute",top:0,bottom:0,left:u.em(-1.5*h.PADDING),width:u.em(3*h.PADDING),border:u.em(h.THICKNESS)+" solid","border-radius":"50%","clip-path":"inset(0 0 0 "+u.em(1.5*h.PADDING)+")","box-sizing":"border-box"}},e.notations=new Map([h.Border("top"),h.Border("right"),h.Border("bottom"),h.Border("left"),h.Border2("actuarial","top","right"),h.Border2("madruwb","bottom","right"),h.DiagonalStrike("up",1),h.DiagonalStrike("down",-1),["horizontalstrike",{renderer:h.RenderElement("hstrike","Y"),bbox:function(t){return[0,t.padding,0,t.padding]}}],["verticalstrike",{renderer:h.RenderElement("vstrike","X"),bbox:function(t){return[t.padding,0,t.padding,0]}}],["box",{renderer:function(t,e){t.adaptor.setStyle(e,"border",t.em(t.thickness)+" solid")},bbox:h.fullBBox,border:h.fullBorder,remove:"left right top bottom"}],["roundedbox",{renderer:h.RenderElement("rbox"),bbox:h.fullBBox}],["circle",{renderer:h.RenderElement("cbox"),bbox:h.fullBBox}],["phasorangle",{renderer:function(t,e){var r=t.getBBox(),o=r.h,i=r.d,n=a(t.getArgMod(1.75*t.padding,o+i),2),s=n[0],l=n[1],h=t.thickness*Math.sin(s)*.9;t.adaptor.setStyle(e,"border-bottom",t.em(t.thickness)+" solid");var c=t.adjustBorder(t.html("mjx-ustrike",{style:{width:t.em(l),transform:"translateX("+t.em(h)+") rotate("+t.fixed(-s)+"rad)"}}));t.adaptor.append(t.chtml,c)},bbox:function(t){var e=t.padding/2,r=t.thickness;return[2*e,e,e+r,3*e+r]},border:function(t){return[0,0,t.thickness,0]},remove:"bottom"}],h.Arrow("up"),h.Arrow("down"),h.Arrow("left"),h.Arrow("right"),h.Arrow("updown"),h.Arrow("leftright"),h.DiagonalArrow("updiagonal"),h.DiagonalArrow("northeast"),h.DiagonalArrow("southeast"),h.DiagonalArrow("northwest"),h.DiagonalArrow("southwest"),h.DiagonalArrow("northeastsouthwest"),h.DiagonalArrow("northwestsoutheast"),["longdiv",{renderer:function(t,e){var r=t.adaptor;r.setStyle(e,"border-top",t.em(t.thickness)+" solid");var o=r.append(t.chtml,t.html("dbox")),i=t.thickness,n=t.padding;i!==h.THICKNESS&&r.setStyle(o,"border-width",t.em(i)),n!==h.PADDING&&(r.setStyle(o,"left",t.em(-1.5*n)),r.setStyle(o,"width",t.em(3*n)),r.setStyle(o,"clip-path","inset(0 0 0 "+t.em(1.5*n)+")"))},bbox:function(t){var e=t.padding,r=t.thickness;return[e+r,e,e,2*e+r/2]}}],["radical",{renderer:function(t,e){t.msqrt.toCHTML(e);var r=t.sqrtTRBL();t.adaptor.setStyle(t.msqrt.chtml,"margin",r.map((function(e){return t.em(-e)})).join(" "))},init:function(t){t.msqrt=t.createMsqrt(t.childNodes[0])},bbox:function(t){return t.sqrtTRBL()},renderChild:!0}]]),e}(l.CommonMencloseMixin(s.CHTMLWrapper));e.CHTMLmenclose=f},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMencloseMixin=void 0;var l=r(6),h=r(3);e.CommonMencloseMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.notations={},o.renderChild=null,o.msqrt=null,o.padding=l.PADDING,o.thickness=l.THICKNESS,o.arrowhead={x:l.ARROWX,y:l.ARROWY,dx:l.ARROWDX},o.getParameters(),o.getNotations(),o.removeRedundantNotations(),o.initializeNotations(),o}return i(e,t),e.prototype.getParameters=function(){var t=this.node.attributes,e=t.get("data-padding");void 0!==e&&(this.padding=this.length2em(e,l.PADDING));var r=t.get("data-thickness");void 0!==r&&(this.thickness=this.length2em(r,l.THICKNESS));var o=t.get("data-arrowhead");if(void 0!==o){var i=n(h.split(o),3),a=i[0],s=i[1],c=i[2];this.arrowhead={x:a?parseFloat(a):l.ARROWX,y:s?parseFloat(s):l.ARROWY,dx:c?parseFloat(c):l.ARROWDX}}},e.prototype.getNotations=function(){var t,e,r=this.constructor.notations;try{for(var o=s(h.split(this.node.attributes.get("notation"))),i=o.next();!i.done;i=o.next()){var n=i.value,a=r.get(n);a&&(this.notations[n]=a,a.renderChild&&(this.renderChild=a.renderer))}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}},e.prototype.removeRedundantNotations=function(){var t,e,r,o;try{for(var i=s(Object.keys(this.notations)),n=i.next();!n.done;n=i.next()){var a=n.value;if(this.notations[a]){var l=this.notations[a].remove||"";try{for(var h=(r=void 0,s(l.split(/ /))),c=h.next();!c.done;c=h.next()){var u=c.value;delete this.notations[u]}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=h.return)&&o.call(h)}finally{if(r)throw r.error}}}}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}},e.prototype.initializeNotations=function(){var t,e;try{for(var r=s(Object.keys(this.notations)),o=r.next();!o.done;o=r.next()){var i=o.value,n=this.notations[i].init;n&&n(this)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=n(this.getBBoxExtenders(),4),o=r[0],i=r[1],a=r[2],s=r[3],l=this.childNodes[0].getBBox();t.combine(l,s,0),t.h+=o,t.d+=a,t.w+=i,this.setChildPWidths(e)},e.prototype.getBBoxExtenders=function(){var t,e,r=[0,0,0,0];try{for(var o=s(Object.keys(this.notations)),i=o.next();!i.done;i=o.next()){var n=i.value;this.maximizeEntries(r,this.notations[n].bbox(this))}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return r},e.prototype.getPadding=function(){var t,e,r=[0,0,0,0],o=[0,0,0,0];try{for(var i=s(Object.keys(this.notations)),n=i.next();!n.done;n=i.next()){var a=n.value;this.maximizeEntries(r,this.notations[a].bbox(this));var l=this.notations[a].border;l&&this.maximizeEntries(o,l(this))}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}return[0,1,2,3].map((function(t){return r[t]-o[t]}))},e.prototype.maximizeEntries=function(t,e){for(var r=0;r<t.length;r++)t[r]<e[r]&&(t[r]=e[r])},e.prototype.getArgMod=function(t,e){return[Math.atan2(e,t),Math.sqrt(t*t+e*e)]},e.prototype.arrow=function(t,e,r){return void 0===r&&(r=!1),null},e.prototype.arrowData=function(){var t=n([this.padding,this.thickness],2),e=t[0],r=t[1]*(this.arrowhead.x+Math.max(1,this.arrowhead.dx)),o=this.childNodes[0].getBBox(),i=o.h,a=o.d,s=o.w,l=i+a,h=Math.sqrt(l*l+s*s),c=Math.max(e,r*s/h),u=Math.max(e,r*l/h),p=n(this.getArgMod(s+2*c,l+2*u),2);return{a:p[0],W:p[1],x:c,y:u}},e.prototype.createMsqrt=function(t){var e=this.node.factory.create("msqrt");e.inheritAttributesFrom(this.node),e.childNodes[0]=t.node;var r=this.wrap(e);return r.parent=this,r},e.prototype.sqrtTRBL=function(){var t=this.msqrt.getBBox(),e=this.msqrt.childNodes[0].getBBox();return[t.h-e.h,0,t.d-e.d,t.w-e.w]},e}(t)}},function(t,e,r){"use strict";var o=this&&this.__createBinding||(Object.create?function(t,e,r,o){void 0===o&&(o=r),Object.defineProperty(t,o,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,o){void 0===o&&(o=r),t[o]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||e.hasOwnProperty(r)||o(e,t,r)},n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.Arrow=e.DiagonalArrow=e.DiagonalStrike=e.Border2=e.Border=e.RenderElement=void 0;var a=r(6);i(r(6),e),e.RenderElement=function(t,e){return void 0===e&&(e=""),function(r,o){var i=r.adjustBorder(r.html("mjx-"+t));if(e&&r.thickness!==a.THICKNESS){var n="translate"+e+"("+r.em(r.thickness/2)+")";r.adaptor.setStyle(i,"transform",n)}r.adaptor.append(r.chtml,i)}},e.Border=function(t){return a.CommonBorder((function(e,r){e.adaptor.setStyle(r,"border-"+t,e.em(e.thickness)+" solid")}))(t)},e.Border2=function(t,e,r){return a.CommonBorder2((function(t,o){var i=t.em(t.thickness)+" solid";t.adaptor.setStyle(o,"border-"+e,i),t.adaptor.setStyle(o,"border-"+r,i)}))(t,e,r)},e.DiagonalStrike=function(t,e){return a.CommonDiagonalStrike((function(t){return function(r,o){var i=r.getBBox(),a=i.w,s=i.h,l=i.d,h=n(r.getArgMod(a,s+l),2),c=h[0],u=h[1],p=e*r.thickness/2,d=r.adjustBorder(r.html(t,{style:{width:r.em(u),transform:"rotate("+r.fixed(-e*c)+"rad) translateY("+p+"em)"}}));r.adaptor.append(r.chtml,d)}}))(t)},e.DiagonalArrow=function(t){return a.CommonDiagonalArrow((function(t,e){t.adaptor.append(t.chtml,e)}))(t)},e.Arrow=function(t){return a.CommonArrow((function(t,e){t.adaptor.append(t.chtml,e)}))(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLinferredMrow=e.CHTMLmrow=void 0;var a=r(0),s=r(10),l=r(10),h=r(84),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e,r,o=this.node.isInferred?this.chtml=t:this.standardCHTMLnode(t),i=!1;try{for(var a=n(this.childNodes),s=a.next();!s.done;s=a.next()){var l=s.value;l.toCHTML(o),l.bbox.w<0&&(i=!0)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}if(i){var h=this.getBBox().w;h&&(this.adaptor.setStyle(o,"width",this.em(Math.max(0,h))),h<0&&this.adaptor.setStyle(o,"marginRight",this.em(h)))}},e.kind=h.MmlMrow.prototype.kind,e}(s.CommonMrowMixin(a.CHTMLWrapper));e.CHTMLmrow=c;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=h.MmlInferredMrow.prototype.kind,e}(l.CommonInferredMrowMixin(c));e.CHTMLinferredMrow=u},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmfenced=void 0;var n=r(0),a=r(43),s=r(85),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t);this.mrow.toCHTML(e)},e.kind=s.MmlMfenced.prototype.kind,e}(a.CommonMfencedMixin(n.CHTMLWrapper));e.CHTMLmfenced=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMfencedMixin=void 0,e.CommonMfencedMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.mrow=null,o.createMrow(),o.addMrowChildren(),o}return i(e,t),e.prototype.createMrow=function(){var t=this.node.factory.create("inferredMrow");t.inheritAttributesFrom(this.node),this.mrow=this.wrap(t),this.mrow.parent=this},e.prototype.addMrowChildren=function(){var t,e,r=this.node,o=this.mrow;this.addMo(r.open),this.childNodes.length&&o.childNodes.push(this.childNodes[0]);var i=0;try{for(var n=s(this.childNodes.slice(1)),a=n.next();!a.done;a=n.next()){var l=a.value;this.addMo(r.separators[i++]),o.childNodes.push(l)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}this.addMo(r.close),o.stretchChildren()},e.prototype.addMo=function(t){if(t){var e=this.wrap(t);this.mrow.childNodes.push(e),e.parent=this.mrow}},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.updateFrom(this.mrow.getBBox()),this.setChildPWidths(e)},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,o=arguments.length;r<o;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmfrac=void 0;var a=r(0),s=r(45),l=r(86),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){this.standardCHTMLnode(t);var e=this.node.attributes.getList("linethickness","bevelled"),r=e.linethickness,o=e.bevelled,i=this.isDisplay();if(o)this.makeBevelled(i);else{var n=this.length2em(String(r),.06);0===n?this.makeAtop(i):this.makeFraction(i,n)}},e.prototype.makeFraction=function(t,e){var r,o,i=this.node.attributes.getList("numalign","denomalign"),a=i.numalign,s=i.denomalign,l=t?{type:"d"}:{},h=this.node.getProperty("withDelims")?n(n({},l),{delims:"true"}):n({},l),c="center"!==a?{align:a}:{},u="center"!==s?{align:s}:{},p=n({},l),d=n({},l),f=this.font.params;if(.06!==e){var m=f.axis_height,y=this.em(e),v=this.getTUV(t,e),b=v.T,x=v.u,g=v.v,M=(t?this.em(3*e):y)+" -.1em";l.style={height:y,"border-top":y+" solid",margin:M};var _=this.em(Math.max(0,x));d.style={height:_,"vertical-align":"-"+_},p.style={height:this.em(Math.max(0,g))},h.style={"vertical-align":this.em(m-b)}}this.adaptor.append(this.chtml,this.html("mjx-frac",h,[r=this.html("mjx-num",c,[this.html("mjx-nstrut",d)]),this.html("mjx-dbox",{},[this.html("mjx-dtable",{},[this.html("mjx-line",l),this.html("mjx-row",{},[o=this.html("mjx-den",u,[this.html("mjx-dstrut",p)])])])])])),this.childNodes[0].toCHTML(r),this.childNodes[1].toCHTML(o)},e.prototype.makeAtop=function(t){var e,r,o=this.node.attributes.getList("numalign","denomalign"),i=o.numalign,a=o.denomalign,s=t?{type:"d",atop:!0}:{atop:!0},l=this.node.getProperty("withDelims")?n(n({},s),{delims:!0}):n({},s),h="center"!==i?{align:i}:{},c="center"!==a?{align:a}:{},u=this.getUVQ(t),p=u.v,d=u.q;h.style={"padding-bottom":this.em(d)},l.style={"vertical-align":this.em(-p)},this.adaptor.append(this.chtml,this.html("mjx-frac",l,[e=this.html("mjx-num",h),r=this.html("mjx-den",c)])),this.childNodes[0].toCHTML(e),this.childNodes[1].toCHTML(r)},e.prototype.makeBevelled=function(t){var e=this.adaptor;e.setAttribute(this.chtml,"bevelled","ture");var r=e.append(this.chtml,this.html("mjx-num"));this.childNodes[0].toCHTML(r),this.bevel.toCHTML(this.chtml);var o=e.append(this.chtml,this.html("mjx-den"));this.childNodes[1].toCHTML(o);var i=this.getBevelData(t),n=i.u,a=i.v,s=i.delta,l=i.nbox,h=i.dbox;n&&e.setStyle(r,"verticalAlign",this.em(n/l.scale)),a&&e.setStyle(o,"verticalAlign",this.em(a/h.scale));var c=this.em(-s/2);e.setStyle(this.bevel.chtml,"marginLeft",c),e.setStyle(this.bevel.chtml,"marginRight",c)},e.kind=l.MmlMfrac.prototype.kind,e.styles={"mjx-frac":{display:"inline-block","vertical-align":"0.17em",padding:"0 .22em"},'mjx-frac[type="d"]':{"vertical-align":".04em"},"mjx-frac[delims]":{padding:"0 .1em"},"mjx-frac[atop]":{padding:"0 .12em"},"mjx-frac[atop][delims]":{padding:"0"},"mjx-dtable":{display:"inline-table",width:"100%"},"mjx-dtable > *":{"font-size":"2000%"},"mjx-dbox":{display:"block","font-size":"5%"},"mjx-num":{display:"block","text-align":"center"},"mjx-den":{display:"block","text-align":"center"},"mjx-mfrac[bevelled] > mjx-num":{display:"inline-block"},"mjx-mfrac[bevelled] > mjx-den":{display:"inline-block"},'mjx-den[align="right"], mjx-num[align="right"]':{"text-align":"right"},'mjx-den[align="left"], mjx-num[align="left"]':{"text-align":"left"},"mjx-nstrut":{display:"inline-block",height:".054em",width:0,"vertical-align":"-.054em"},'mjx-nstrut[type="d"]':{height:".217em","vertical-align":"-.217em"},"mjx-dstrut":{display:"inline-block",height:".505em",width:0},'mjx-dstrut[type="d"]':{height:".726em"},"mjx-line":{display:"block","box-sizing":"border-box","min-height":"1px",height:".06em","border-top":".06em solid",margin:".06em -.1em",overflow:"hidden"},'mjx-line[type="d"]':{margin:".18em -.1em"}},e}(s.CommonMfracMixin(a.CHTMLWrapper));e.CHTMLmfrac=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMfracMixin=void 0,e.CommonMfracMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;if(o.bevel=null,o.pad=o.node.getProperty("withDelims")?0:o.font.params.nulldelimiterspace,o.node.attributes.get("bevelled")){var i=o.getBevelData(o.isDisplay()).H,n=o.bevel=o.createMo("/");n.canStretch(1),n.getStretchedVariant([i],!0)}return o}return i(e,t),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.empty();var r=this.node.attributes.getList("linethickness","bevelled"),o=r.linethickness,i=r.bevelled,n=this.isDisplay(),a=null;if(i)this.getBevelledBBox(t,n);else{var s=this.length2em(String(o),.06);a=-2*this.pad,0===s?this.getAtopBBox(t,n):(this.getFractionBBox(t,n,s),a-=.2),a+=t.w}t.clean(),this.setChildPWidths(e,a)},e.prototype.getFractionBBox=function(t,e,r){var o=this.childNodes[0].getBBox(),i=this.childNodes[1].getBBox(),n=this.font.params.axis_height,a=this.getTUV(e,r),s=a.T,l=a.u,h=a.v;t.combine(o,0,n+s+Math.max(o.d*o.rscale,l)),t.combine(i,0,n-s-Math.max(i.h*i.rscale,h)),t.w+=2*this.pad+.2},e.prototype.getTUV=function(t,e){var r=this.font.params,o=r.axis_height,i=(t?3.5:1.5)*e;return{T:(t?3.5:1.5)*e,u:(t?r.num1:r.num2)-o-i,v:(t?r.denom1:r.denom2)+o-i}},e.prototype.getAtopBBox=function(t,e){var r=this.getUVQ(e),o=r.u,i=r.v,n=r.nbox,a=r.dbox;t.combine(n,0,o),t.combine(a,0,-i),t.w+=2*this.pad},e.prototype.getUVQ=function(t){var e=this.childNodes[0].getBBox(),r=this.childNodes[1].getBBox(),o=this.font.params,i=n(t?[o.num1,o.denom1]:[o.num3,o.denom2],2),a=i[0],s=i[1],l=(t?7:3)*o.rule_thickness,h=a-e.d*e.scale-(r.h*r.scale-s);return h<l&&(a+=(l-h)/2,s+=(l-h)/2,h=l),{u:a,v:s,q:h,nbox:e,dbox:r}},e.prototype.getBevelledBBox=function(t,e){var r=this.getBevelData(e),o=r.u,i=r.v,n=r.delta,a=r.nbox,s=r.dbox,l=this.bevel.getBBox();t.combine(a,0,o),t.combine(l,t.w-n/2,0),t.combine(s,t.w-n/2,i)},e.prototype.getBevelData=function(t){var e=this.childNodes[0].getBBox(),r=this.childNodes[1].getBBox(),o=t?.4:.15,i=Math.max(e.scale*(e.h+e.d),r.scale*(r.h+r.d))+2*o,n=this.font.params.axis_height;return{H:i,delta:o,u:e.scale*(e.d-e.h)/2+n+o,v:r.scale*(r.d-r.h)/2+n-o,nbox:e,dbox:r}},e.prototype.canStretch=function(t){return!1},e.prototype.isDisplay=function(){var t=this.node.attributes.getList("displaystyle","scriptlevel"),e=t.displaystyle,r=t.scriptlevel;return e&&0===r},e.prototype.getWrapWidth=function(t){var e=this.node.attributes;return e.get("bevelled")?this.childNodes[t].getBBox().w:this.getBBox().w-(this.length2em(e.get("linethickness"))?.2:0)-2*this.pad},e.prototype.getChildAlign=function(t){var e=this.node.attributes;return e.get("bevelled")?"left":e.get(["numalign","denomalign"][t])},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMsqrtMixin=void 0;var s=r(1);e.CommonMsqrtMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this,i=o.createMo("\u221a");i.canStretch(1);var n=o.childNodes[o.base].getBBox(),s=n.h,l=n.d,h=o.font.params.rule_thickness,c=o.node.attributes.get("displaystyle")?o.font.params.x_height:h;return o.surdH=s+l+2*h+c/4,i.getStretchedVariant([o.surdH-l,l],!0),o}return i(e,t),Object.defineProperty(e.prototype,"base",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"surd",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"root",{get:function(){return null},enumerable:!1,configurable:!0}),e.prototype.createMo=function(e){var r=t.prototype.createMo.call(this,e);return this.childNodes.push(r),r},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.childNodes[this.surd].getBBox(),o=new s.BBox(this.childNodes[this.base].getBBox()),i=this.getPQ(r)[1],a=this.font.params.rule_thickness,l=o.h+i+a,h=n(this.getRootDimens(r,l),1)[0];t.h=l+a,this.combineRootBBox(t,r,l),t.combine(r,h,l-r.h),t.combine(o,h+r.w,0),t.clean(),this.setChildPWidths(e)},e.prototype.combineRootBBox=function(t,e,r){},e.prototype.getPQ=function(t){var e=this.font.params.rule_thickness,r=this.node.attributes.get("displaystyle")?this.font.params.x_height:e;return[r,t.h+t.d>this.surdH?(t.h+t.d-(this.surdH-2*e-r/2))/2:e+r/4]},e.prototype.getRootDimens=function(t,e){return[0,0,0,0]},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmroot=void 0;var a=r(11),s=r(48),l=r(88),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.addRoot=function(t,e,r,o){e.toCHTML(t);var i=n(this.getRootDimens(r,o),3),a=i[0],s=i[1],l=i[2];this.adaptor.setStyle(t,"verticalAlign",this.em(s)),this.adaptor.setStyle(t,"width",this.em(a)),l&&this.adaptor.setStyle(this.adaptor.firstChild(t),"paddingLeft",this.em(l))},e.kind=l.MmlMroot.prototype.kind,e}(s.CommonMrootMixin(a.CHTMLmsqrt));e.CHTMLmroot=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMrootMixin=void 0,e.CommonMrootMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"surd",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"root",{get:function(){return 1},enumerable:!1,configurable:!0}),e.prototype.combineRootBBox=function(t,e,r){var o=this.childNodes[this.root].getBBox(),i=this.getRootDimens(e,r)[1];t.combine(o,0,i)},e.prototype.getRootDimens=function(t,e){var r=this.childNodes[this.surd],o=this.childNodes[this.root].getBBox(),i=(r.size<0?.5:.6)*t.w,n=o.w,a=o.rscale,s=Math.max(n,i/a),l=Math.max(0,s-n);return[s*a-i,this.rootHeight(o,t,r.size,e),l]},e.prototype.rootHeight=function(t,e,r,o){var i=e.h+e.d;return(r<0?1.9:.55*i)-(i-o)+Math.max(0,t.d*t.rscale)},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLscriptbase=void 0;var s=r(0),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){this.chtml=this.standardCHTMLnode(t);var e=n(this.getOffset(this.baseChild.getBBox(),this.script.getBBox()),2),r=e[0],o=e[1],i={"vertical-align":this.em(o)};r&&(i["margin-left"]=this.em(r)),this.baseChild.toCHTML(this.chtml),this.script.toCHTML(this.adaptor.append(this.chtml,this.html("mjx-script",{style:i})))},e.prototype.setDeltaW=function(t,e){for(var r=0;r<e.length;r++)e[r]&&this.adaptor.setStyle(t[r],"paddingLeft",this.em(e[r]))},e.prototype.adjustOverDepth=function(t,e){e.d>=0||this.adaptor.setStyle(t,"marginBottom",this.em(e.d*e.rscale))},e.prototype.adjustUnderDepth=function(t,e){var r,o;if(!(e.d>=0)){var i=this.adaptor,n=this.em(e.d),s=this.html("mjx-box",{style:{"margin-bottom":n,"vertical-align":n}});try{for(var l=a(i.childNodes(i.firstChild(t))),h=l.next();!h.done;h=l.next()){var c=h.value;i.append(s,c)}}catch(t){r={error:t}}finally{try{h&&!h.done&&(o=l.return)&&o.call(l)}finally{if(r)throw r.error}}i.append(i.firstChild(t),s)}},e.kind="scriptbase",e.useIC=!1,e}(r(50).CommonScriptbaseMixin(s.CHTMLWrapper));e.CHTMLscriptbase=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonScriptbaseMixin=void 0;e.CommonScriptbaseMixin=function(t){var e;return(e=function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this,i=o.baseCore=o.childNodes[0];if(!i)return o;for(;1===i.childNodes.length&&(i.node.isKind("mrow")||i.node.isKind("TeXAtom")||i.node.isKind("mstyle")||i.node.isKind("mpadded")||i.node.isKind("mphantom")||i.node.isKind("semantics"));)if(!(i=i.childNodes[0]))return o;return"noIC"in i?(o.baseCore=i,o.constructor.useIC||(i.noIC=!0),o):o}return i(e,t),Object.defineProperty(e.prototype,"baseChild",{get:function(){return this.childNodes[this.node.base]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"script",{get:function(){return this.childNodes[1]},enumerable:!1,configurable:!0}),e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.baseChild.getBBox(),o=this.script.getBBox(),i=n(this.getOffset(r,o),2),a=i[0],s=i[1];t.append(r),t.combine(o,t.w+a,s),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(e)},e.prototype.coreIC=function(){var t=this.baseCore.getBBox();return t.ic?1.05*t.ic+.05:0},e.prototype.coreScale=function(){for(var t=this.baseChild.getBBox().rscale,e=this.baseChild;(e.node.isKind("mstyle")||e.node.isKind("mrow")||e.node.isKind("TeXAtom"))&&1===e.childNodes.length;)t*=(e=e.childNodes[0]).getBBox().rscale;return t},e.prototype.isCharBase=function(){for(var t=this.baseChild;(t.node.isKind("mstyle")||t.node.isKind("mrow"))&&1===t.childNodes.length;)t=t.childNodes[0];return(t.node.isKind("mo")||t.node.isKind("mi")||t.node.isKind("mn"))&&1===t.bbox.rscale&&1===Array.from(t.getText()).length&&!t.node.attributes.get("largeop")},e.prototype.getOffset=function(t,e){return[0,0]},e.prototype.getV=function(t,e){var r=this.font.params,o=this.length2em(this.node.attributes.get("subscriptshift"),r.sub1);return Math.max(this.isCharBase()?0:t.d*t.rscale+r.sub_drop*e.rscale,o,e.h*e.rscale-.8*r.x_height)},e.prototype.getU=function(t,e){var r=this.font.params,o=this.node.attributes.getList("displaystyle","superscriptshift"),i=this.node.getProperty("texprimestyle")?r.sup3:o.displaystyle?r.sup1:r.sup2,n=this.length2em(o.superscriptshift,i);return Math.max(this.isCharBase()?0:t.h*t.rscale-r.sup_drop*e.rscale,n,e.d*e.rscale+1/4*r.x_height)},e.prototype.hasMovableLimits=function(){var t=this.node.attributes.get("displaystyle"),e=this.baseChild.coreMO().node;return!t&&!!e.attributes.get("movablelimits")},e.prototype.getOverKU=function(t,e){var r=this.node.attributes.get("accent"),o=this.font.params,i=e.d*e.rscale,n=(r?o.rule_thickness:Math.max(o.big_op_spacing1,o.big_op_spacing3-Math.max(0,i)))-(this.baseChild.node.isKind("munderover")?.1:0);return[n,t.h*t.rscale+n+i]},e.prototype.getUnderKV=function(t,e){var r=this.node.attributes.get("accentunder"),o=this.font.params,i=e.h*e.rscale,n=(r?o.rule_thickness:Math.max(o.big_op_spacing2,o.big_op_spacing4-i))-(this.baseChild.node.isKind("munderover")?.1:0);return[n,-(t.d*t.rscale+n+i)]},e.prototype.getDeltaW=function(t,e){var r,o,i,n;void 0===e&&(e=[0,0,0]);var l=this.node.attributes.get("align"),h=t.map((function(t){return t.w*t.rscale})),c=Math.max.apply(Math,a(h)),u=[],p=0;try{for(var d=s(h.keys()),f=d.next();!f.done;f=d.next()){u[v=f.value]=("center"===l?(c-h[v])/2:"right"===l?c-h[v]:0)+e[v],u[v]<p&&(p=-u[v])}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}if(p)try{for(var m=s(u.keys()),y=m.next();!y.done;y=m.next()){var v;u[v=y.value]+=p}}catch(t){i={error:t}}finally{try{y&&!y.done&&(n=m.return)&&n.call(m)}finally{if(i)throw i.error}}return u},e.prototype.getDelta=function(t){void 0===t&&(t=!1);var e=this.node.attributes.get("accent")&&!t?this.baseChild.coreMO().bbox.sk:0;return(1.5*this.baseCore.bbox.ic/2+e)*this.coreScale()},e.prototype.stretchChildren=function(){var t,e,r,o,i,n,a=[];try{for(var l=s(this.childNodes),h=l.next();!h.done;h=l.next()){(_=h.value).canStretch(2)&&a.push(_)}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}var c=a.length,u=this.childNodes.length;if(c&&u>1){var p=0,d=c>1&&c===u;try{for(var f=s(this.childNodes),m=f.next();!m.done;m=f.next()){var y=0===(_=m.value).stretch.dir;if(d||y){var v=_.getBBox(y),b=v.w,x=v.rscale;b*x>p&&(p=b*x)}}}catch(t){r={error:t}}finally{try{m&&!m.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}try{for(var g=s(a),M=g.next();!M.done;M=g.next()){var _;(_=M.value).coreMO().getStretchedVariant([p/_.bbox.rscale])}}catch(t){i={error:t}}finally{try{M&&!M.done&&(n=g.return)&&n.call(g)}finally{if(i)throw i.error}}}},e}(t)).useIC=!1,e}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmunderover=e.CHTMLmover=e.CHTMLmunder=void 0;var n=r(7),a=r(9),s=r(9),l=r(9),h=r(90),c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){if(this.hasMovableLimits())return t.prototype.toCHTML.call(this,e),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(e);var r=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-row")),this.html("mjx-base")),o=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-row")),this.html("mjx-under"));this.baseChild.toCHTML(r),this.script.toCHTML(o);var i=this.baseChild.getBBox(),n=this.script.getBBox(),a=this.getUnderKV(i,n)[0],s=this.getDelta(!0);this.adaptor.setStyle(o,"paddingTop",this.em(a)),this.setDeltaW([r,o],this.getDeltaW([i,n],[0,-s])),this.adjustUnderDepth(o,n)},e.kind=h.MmlMunder.prototype.kind,e.useIC=!0,e.styles={"mjx-over":{"text-align":"left"},'mjx-munder:not([limits="false"])':{display:"inline-table"},"mjx-munder > mjx-row":{"text-align":"left"},"mjx-under":{"padding-bottom":".1em"}},e}(a.CommonMunderMixin(n.CHTMLmsub));e.CHTMLmunder=c;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){if(this.hasMovableLimits())return t.prototype.toCHTML.call(this,e),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(e);var r=this.adaptor.append(this.chtml,this.html("mjx-over")),o=this.adaptor.append(this.chtml,this.html("mjx-base"));this.script.toCHTML(r),this.baseChild.toCHTML(o);var i=this.script.getBBox(),n=this.baseChild.getBBox(),a=this.getOverKU(n,i)[0],s=this.getDelta();this.adaptor.setStyle(r,"paddingBottom",this.em(a)),this.setDeltaW([o,r],this.getDeltaW([n,i],[0,s])),this.adjustOverDepth(r,i)},e.kind=h.MmlMover.prototype.kind,e.useIC=!0,e.styles={'mjx-mover:not([limits="false"])':{"padding-top":".1em"},'mjx-mover:not([limits="false"]) > *':{display:"block","text-align":"left"}},e}(s.CommonMoverMixin(n.CHTMLmsup));e.CHTMLmover=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){if(this.hasMovableLimits())return t.prototype.toCHTML.call(this,e),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(e);var r=this.adaptor.append(this.chtml,this.html("mjx-over")),o=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-box")),this.html("mjx-munder")),i=this.adaptor.append(this.adaptor.append(o,this.html("mjx-row")),this.html("mjx-base")),n=this.adaptor.append(this.adaptor.append(o,this.html("mjx-row")),this.html("mjx-under"));this.overChild.toCHTML(r),this.baseChild.toCHTML(i),this.underChild.toCHTML(n);var a=this.overChild.getBBox(),s=this.baseChild.getBBox(),l=this.underChild.getBBox(),h=this.getOverKU(s,a)[0],c=this.getUnderKV(s,l)[0],u=this.getDelta();this.adaptor.setStyle(r,"paddingBottom",this.em(h)),this.adaptor.setStyle(n,"paddingTop",this.em(c)),this.setDeltaW([i,n,r],this.getDeltaW([s,l,a],[0,-u,u])),this.adjustOverDepth(r,a),this.adjustUnderDepth(n,l)},e.kind=h.MmlMunderover.prototype.kind,e.useIC=!0,e.styles={'mjx-munderover:not([limits="false"])':{"padding-top":".1em"},'mjx-munderover:not([limits="false"]) > *':{display:"block"}},e}(l.CommonMunderoverMixin(n.CHTMLmsubsup));e.CHTMLmunderover=p},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmmultiscripts=void 0;var a=r(7),s=r(53),l=r(91),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=this.getScriptData(),o=this.combinePrePost(r.sub,r.psub),i=this.combinePrePost(r.sup,r.psup),a=n(this.getUVQ(r.base,o,i),2),s=a[0],l=a[1];r.numPrescripts&&this.addScripts(s,-l,!0,r.psub,r.psup,this.firstPrescript,r.numPrescripts),this.childNodes[0].toCHTML(e),r.numScripts&&this.addScripts(s,-l,!1,r.sub,r.sup,1,r.numScripts)},e.prototype.addScripts=function(t,e,r,o,i,n,a){var s=this.adaptor,l=t-i.d+(e-o.h),h=t<0&&0===e?o.h+t:t,c=l>0?{style:{height:this.em(l)}}:{},u=h?{style:{"vertical-align":this.em(h)}}:{},p=this.html("mjx-row"),d=this.html("mjx-row",c),f=this.html("mjx-row"),m="mjx-"+(r?"pre":"")+"scripts";s.append(this.chtml,this.html(m,u,[p,d,f]));for(var y=n+2*a;n<y;)this.childNodes[n++].toCHTML(s.append(f,this.html("mjx-cell"))),this.childNodes[n++].toCHTML(s.append(p,this.html("mjx-cell")))},e.kind=l.MmlMmultiscripts.prototype.kind,e.styles={"mjx-prescripts":{display:"inline-table","padding-left":".05em"},"mjx-scripts":{display:"inline-table","padding-right":".05em"},"mjx-prescripts > mjx-row > mjx-cell":{"text-align":"right"}},e}(s.CommonMmultiscriptsMixin(a.CHTMLmsubsup));e.CHTMLmmultiscripts=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMmultiscriptsMixin=e.ScriptNames=e.NextScript=void 0;var s=r(1);e.NextScript={base:"subList",subList:"supList",supList:"subList",psubList:"psupList",psupList:"psubList"},e.ScriptNames=["sup","sup","psup","psub"],e.CommonMmultiscriptsMixin=function(t){return function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return e.scriptData=null,e.firstPrescript=0,e}return i(r,t),r.prototype.combinePrePost=function(t,e){var r=new s.BBox(t);return r.combine(e,0,0),r},r.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.font.params.scriptspace,o=this.getScriptData(),i=this.combinePrePost(o.sub,o.psub),a=this.combinePrePost(o.sup,o.psup),s=n(this.getUVQ(o.base,i,a),2),l=s[0],h=s[1];if(t.empty(),o.numPrescripts&&(t.combine(o.psup,r,l),t.combine(o.psub,r,h)),t.append(o.base),o.numScripts){var c=t.w;t.combine(o.sup,c,l),t.combine(o.sub,c,h),t.w+=r}t.clean(),this.setChildPWidths(e)},r.prototype.getScriptData=function(){if(this.scriptData)return this.scriptData;var t=this.scriptData={base:null,sub:s.BBox.empty(),sup:s.BBox.empty(),psub:s.BBox.empty(),psup:s.BBox.empty(),numPrescripts:0,numScripts:0},e=this.getScriptBBoxLists();return this.combineBBoxLists(t.sub,t.sup,e.subList,e.supList),this.combineBBoxLists(t.psub,t.psup,e.psubList,e.psupList),this.scriptData.base=e.base[0],this.scriptData.numPrescripts=e.psubList.length,this.scriptData.numScripts=e.subList.length,this.scriptData},r.prototype.getScriptBBoxLists=function(){var t,r,o={base:[],subList:[],supList:[],psubList:[],psupList:[]},i="base";try{for(var n=a(this.childNodes),s=n.next();!s.done;s=n.next()){var l=s.value;l.node.isKind("mprescripts")?i="psubList":(o[i].push(l.getBBox()),i=e.NextScript[i])}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}return this.firstPrescript=o.subList.length+o.supList.length+2,this.padLists(o.subList,o.supList),this.padLists(o.psubList,o.psupList),o},r.prototype.padLists=function(t,e){t.length>e.length&&e.push(s.BBox.empty())},r.prototype.combineBBoxLists=function(t,e,r,o){for(var i=0;i<r.length;i++){var a=n(this.getScaledWHD(r[i]),3),s=a[0],l=a[1],h=a[2],c=n(this.getScaledWHD(o[i]),3),u=c[0],p=c[1],d=c[2],f=Math.max(s,u);t.w+=f,e.w+=f,l>t.h&&(t.h=l),h>t.d&&(t.d=h),p>e.h&&(e.h=p),d>e.d&&(e.d=d)}},r.prototype.getScaledWHD=function(t){var e=t.w,r=t.h,o=t.d,i=t.rscale;return[e*i,r*i,o*i]},r.prototype.getUVQ=function(e,r,o){var i;if(!this.UVQ){var a=n([0,0,0],3),s=a[0],l=a[1],h=a[2];0===r.h&&0===r.d?s=this.getU(e,o):0===o.h&&0===o.d?s=-this.getV(e,r):(s=(i=n(t.prototype.getUVQ.call(this,e,r,o),3))[0],l=i[1],h=i[2]),this.UVQ=[s,l,h]}return this.UVQ},r}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmtable=void 0;var s=r(0),l=r(55),h=r(93),c=r(3),u=function(t){function e(e,r,o){void 0===o&&(o=null);var i=t.call(this,e,r,o)||this;return i.itable=i.html("mjx-itable"),i.labels=i.html("mjx-itable"),i}return i(e,t),e.prototype.getAlignShift=function(){var e=t.prototype.getAlignShift.call(this);return this.isTop||(e[1]=0),e},e.prototype.toCHTML=function(t){var e,r,o=this.standardCHTMLnode(t);this.adaptor.append(o,this.html("mjx-table",{},[this.itable]));try{for(var i=n(this.childNodes),a=i.next();!a.done;a=i.next()){a.value.toCHTML(this.itable)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}this.padRows(),this.handleColumnSpacing(),this.handleColumnLines(),this.handleColumnWidths(),this.handleRowSpacing(),this.handleRowLines(),this.handleEqualRows(),this.handleFrame(),this.handleWidth(),this.handleLabels(),this.handleAlign(),this.handleJustify(),this.shiftColor()},e.prototype.shiftColor=function(){var t=this.adaptor,e=t.getStyle(this.chtml,"backgroundColor");e&&(t.setStyle(this.chtml,"backgroundColor",""),t.setStyle(this.itable,"backgroundColor",e))},e.prototype.padRows=function(){var t,e,r=this.adaptor;try{for(var o=n(r.childNodes(this.itable)),i=o.next();!i.done;i=o.next())for(var a=i.value;r.childNodes(a).length<this.numCols;)r.append(a,this.html("mjx-mtd"))}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}},e.prototype.handleColumnSpacing=function(){var t,e,r,o,i=this.getEmHalfSpacing(this.fSpace[0],this.cSpace),a=this.frame;try{for(var s=n(this.tableRows),l=s.next();!l.done;l=s.next()){var h=l.value,c=0;try{for(var u=(r=void 0,n(h.tableCells)),p=u.next();!p.done;p=u.next()){var d=p.value,f=i[c++],m=i[c],y=d?d.chtml:this.adaptor.childNodes(h.chtml)[c];(c>1&&"0.4em"!==f||a&&1===c)&&this.adaptor.setStyle(y,"paddingLeft",f),(c<this.numCols&&"0.4em"!==m||a&&c===this.numCols)&&this.adaptor.setStyle(y,"paddingRight",m)}}catch(t){r={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}}}catch(e){t={error:e}}finally{try{l&&!l.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}},e.prototype.handleColumnLines=function(){var t,e,r,o;if("none"!==this.node.attributes.get("columnlines")){var i=this.getColumnAttributes("columnlines");try{for(var a=n(this.childNodes),s=a.next();!s.done;s=a.next()){var l=s.value,h=0;try{for(var c=(r=void 0,n(this.adaptor.childNodes(l.chtml).slice(1))),u=c.next();!u.done;u=c.next()){var p=u.value,d=i[h++];"none"!==d&&this.adaptor.setStyle(p,"borderLeft",".07em "+d)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}}}catch(e){t={error:e}}finally{try{s&&!s.done&&(e=a.return)&&e.call(a)}finally{if(t)throw t.error}}}},e.prototype.handleColumnWidths=function(){var t,e,r,o;try{for(var i=n(this.childNodes),a=i.next();!a.done;a=i.next()){var s=a.value,l=0;try{for(var h=(r=void 0,n(this.adaptor.childNodes(s.chtml))),c=h.next();!c.done;c=h.next()){var u=c.value,p=this.cWidths[l++];if(null!==p){var d="number"==typeof p?this.em(p):p;this.adaptor.setStyle(u,"width",d),this.adaptor.setStyle(u,"maxWidth",d),this.adaptor.setStyle(u,"minWidth",d)}}}catch(t){r={error:t}}finally{try{c&&!c.done&&(o=h.return)&&o.call(h)}finally{if(r)throw r.error}}}}catch(e){t={error:e}}finally{try{a&&!a.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}},e.prototype.handleRowSpacing=function(){var t,e,r,o,i=this.getEmHalfSpacing(this.fSpace[1],this.rSpace),a=this.frame,s=0;try{for(var l=n(this.childNodes),h=l.next();!h.done;h=l.next()){var c=h.value,u=i[s++],p=i[s];try{for(var d=(r=void 0,n(c.childNodes)),f=d.next();!f.done;f=d.next()){var m=f.value;(s>1&&"0.215em"!==u||a&&1===s)&&this.adaptor.setStyle(m.chtml,"paddingTop",u),(s<this.numRows&&"0.215em"!==p||a&&s===this.numRows)&&this.adaptor.setStyle(m.chtml,"paddingBottom",p)}}catch(t){r={error:t}}finally{try{f&&!f.done&&(o=d.return)&&o.call(d)}finally{if(r)throw r.error}}}}catch(e){t={error:e}}finally{try{h&&!h.done&&(e=l.return)&&e.call(l)}finally{if(t)throw t.error}}},e.prototype.handleRowLines=function(){var t,e,r,o;if("none"!==this.node.attributes.get("rowlines")){var i=this.getRowAttributes("rowlines"),a=0;try{for(var s=n(this.childNodes.slice(1)),l=s.next();!l.done;l=s.next()){var h=l.value,c=i[a++];if("none"!==c)try{for(var u=(r=void 0,n(this.adaptor.childNodes(h.chtml))),p=u.next();!p.done;p=u.next()){var d=p.value;this.adaptor.setStyle(d,"borderTop",".07em "+c)}}catch(t){r={error:t}}finally{try{p&&!p.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}}}catch(e){t={error:e}}finally{try{l&&!l.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}}},e.prototype.handleEqualRows=function(){if(this.node.attributes.get("equalrows"))for(var t=this.getRowHalfSpacing(),e=this.getTableData(),r=e.H,o=e.D,i=e.NH,n=e.ND,a=this.getEqualRowHeight(),s=0;s<this.numRows;s++){var l=this.childNodes[s];a!==i[s]+n[s]&&this.setRowHeight(l,a,(a-r[s]+o[s])/2,t[s]+t[s+1])}},e.prototype.setRowHeight=function(t,e,r,o){var i,a;this.adaptor.setStyle(t.chtml,"height",this.em(e+o));var s=t.node.attributes.get("rowalign");try{for(var l=n(t.childNodes),h=l.next();!h.done;h=l.next()){var c=h.value;if(this.setCellBaseline(c,s,e,r))break}}catch(t){i={error:t}}finally{try{h&&!h.done&&(a=l.return)&&a.call(l)}finally{if(i)throw i.error}}},e.prototype.setCellBaseline=function(t,e,r,o){var i=t.node.attributes.get("rowalign");if("baseline"===i||"axis"===i){var n=this.adaptor,a=n.lastChild(t.chtml);n.setStyle(a,"height",this.em(r)),n.setStyle(a,"verticalAlign",this.em(-o));var s=t.parent;if(!(s.node.isKind("mlabeledtr")&&t===s.childNodes[0]||"baseline"!==e&&"axis"!==e))return!0}return!1},e.prototype.handleFrame=function(){this.frame&&this.adaptor.setStyle(this.itable,"border",".07em "+this.node.attributes.get("frame"))},e.prototype.handleWidth=function(){var t=this.adaptor,e=this.getBBox(),r=e.w,o=e.L,i=e.R;t.setStyle(this.chtml,"minWidth",this.em(o+r+i));var n=this.node.attributes.get("width");if(c.isPercent(n))t.setStyle(this.chtml,"width",""),t.setAttribute(this.chtml,"width","full");else if(!this.hasLabels){if("auto"===n)return;n=this.em(this.length2em(n)+2*this.fLine)}var a=t.firstChild(this.chtml);t.setStyle(a,"width",n),t.setStyle(a,"minWidth",this.em(r)),(o||i)&&(t.setStyle(this.chtml,"margin",""),o===i?t.setStyle(a,"margin","0 "+this.em(i)):t.setStyle(a,"margin","0 "+this.em(i)+" 0 "+this.em(o))),t.setAttribute(this.itable,"width","full")},e.prototype.handleAlign=function(){var t=a(this.getAlignmentRow(),2),e=t[0],r=t[1];if(null===r)"axis"!==e&&this.adaptor.setAttribute(this.chtml,"align",e);else{var o=this.getVerticalPosition(r,e);this.adaptor.setAttribute(this.chtml,"align","top"),this.adaptor.setStyle(this.chtml,"verticalAlign",this.em(o))}},e.prototype.handleJustify=function(){var t=this.getAlignShift()[0];"center"!==t&&this.adaptor.setAttribute(this.chtml,"justify",t)},e.prototype.handleLabels=function(){if(this.hasLabels){var t=this.labels,e=this.node.attributes,r=this.adaptor,o=e.get("side");r.setAttribute(this.chtml,"side",o),r.setAttribute(t,"align",o),r.setStyle(t,o,"0");var i=a(this.addLabelPadding(o),2),n=i[0],s=i[1];if(s){var l=r.firstChild(this.chtml);this.setIndent(l,n,s)}this.updateRowHeights(),this.addLabelSpacing()}},e.prototype.addLabelPadding=function(t){var e=a(this.getPadAlignShift(t),3),r=e[1],o=e[2],i={};if("right"===t){var n=this.node.attributes.get("width"),s=this.getBBox(),l=s.w,h=s.L,u=s.R;i.style={width:c.isPercent(n)?"calc("+n+" + "+this.em(h+u)+")":this.em(h+l+u)}}return this.adaptor.append(this.chtml,this.html("mjx-labels",i,[this.labels])),[r,o]},e.prototype.updateRowHeights=function(){if(!this.node.attributes.get("equalrows"))for(var t=this.getTableData(),e=t.H,r=t.D,o=t.NH,i=t.ND,n=this.getRowHalfSpacing(),a=0;a<this.numRows;a++){var s=this.childNodes[a];e[a]!==o[a]||r[a]!==i[a]?this.setRowHeight(s,e[a]+r[a],r[a],n[a]+n[a+1]):s.node.isKind("mlabeledtr")&&this.setCellBaseline(s.childNodes[0],"",e[a]+r[a],r[a])}},e.prototype.addLabelSpacing=function(){for(var t=this.adaptor,e=this.node.attributes.get("equalrows"),r=this.getTableData(),o=r.H,i=r.D,n=e?this.getEqualRowHeight():0,a=this.getRowHalfSpacing(),s=this.fLine,l=t.firstChild(this.labels),h=0;h<this.numRows;h++){this.childNodes[h].node.isKind("mlabeledtr")?(s&&t.insert(this.html("mjx-mtr",{style:{height:this.em(s)}}),l),t.setStyle(l,"height",this.em((e?n:o[h]+i[h])+a[h]+a[h+1])),l=t.next(l),s=this.rLines[h]):s+=a[h]+(e?n:o[h]+i[h])+a[h+1]+this.rLines[h]}},e.kind=h.MmlMtable.prototype.kind,e.styles={"mjx-mtable":{"vertical-align":".25em","text-align":"center",position:"relative","box-sizing":"border-box"},"mjx-labels":{position:"absolute",left:0,top:0},"mjx-table":{display:"inline-block","vertical-align":"-.5ex"},"mjx-table > mjx-itable":{"vertical-align":"middle","text-align":"left","box-sizing":"border-box"},"mjx-labels > mjx-itable":{position:"absolute",top:0},'mjx-mtable[justify="left"]':{"text-align":"left"},'mjx-mtable[justify="right"]':{"text-align":"right"},'mjx-mtable[justify="left"][side="left"]':{"padding-right":"0 ! important"},'mjx-mtable[justify="left"][side="right"]':{"padding-left":"0 ! important"},'mjx-mtable[justify="right"][side="left"]':{"padding-right":"0 ! important"},'mjx-mtable[justify="right"][side="right"]':{"padding-left":"0 ! important"},"mjx-mtable[align]":{"vertical-align":"baseline"},'mjx-mtable[align="top"] > mjx-table':{"vertical-align":"top"},'mjx-mtable[align="bottom"] > mjx-table':{"vertical-align":"bottom"},'mjx-mtable[side="right"] mjx-labels':{"min-width":"100%"}},e}(l.CommonMtableMixin(s.CHTMLWrapper));e.CHTMLmtable=u},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtableMixin=void 0;var l=r(1),h=r(3),c=r(92);e.CommonMtableMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;o.numCols=0,o.numRows=0,o.data=null,o.pwidthCells=[],o.pWidth=0,o.numCols=c.max(o.tableRows.map((function(t){return t.numCells}))),o.numRows=o.childNodes.length,o.hasLabels=o.childNodes.reduce((function(t,e){return t||e.node.isKind("mlabeledtr")}),!1),o.findContainer(),o.isTop=!o.container||o.container.node.isKind("math")&&!o.container.parent,o.isTop&&(o.jax.table=o),o.getPercentageWidth();var i=o.node.attributes;return o.frame="none"!==i.get("frame"),o.fLine=o.frame?.07:0,o.fSpace=o.frame?o.convertLengths(o.getAttributeArray("framespacing")):[0,0],o.cSpace=o.convertLengths(o.getColumnAttributes("columnspacing")),o.rSpace=o.convertLengths(o.getRowAttributes("rowspacing")),o.cLines=o.getColumnAttributes("columnlines").map((function(t){return"none"===t?0:.07})),o.rLines=o.getRowAttributes("rowlines").map((function(t){return"none"===t?0:.07})),o.cWidths=o.getColumnWidths(),o.stretchRows(),o.stretchColumns(),o}return i(e,t),Object.defineProperty(e.prototype,"tableRows",{get:function(){return this.childNodes},enumerable:!1,configurable:!0}),e.prototype.findContainer=function(){for(var t=this,e=t.parent;e&&(e.node.notParent||e.node.isKind("mrow"));)t=e,e=e.parent;this.container=e,this.containerI=t.node.childPosition()},e.prototype.getPercentageWidth=function(){if(this.hasLabels)this.bbox.pwidth=l.BBox.fullWidth;else{var t=this.node.attributes.get("width");h.isPercent(t)&&(this.bbox.pwidth=t)}},e.prototype.stretchRows=function(){for(var t=this.node.attributes.get("equalrows"),e=t?this.getEqualRowHeight():0,r=t?this.getTableData():{H:[0],D:[0]},o=r.H,i=r.D,n=this.tableRows,a=0;a<this.numRows;a++){var s=t?[(e+o[a]-i[a])/2,(e-o[a]+i[a])/2]:null;n[a].stretchChildren(s)}},e.prototype.stretchColumns=function(){for(var t=0;t<this.numCols;t++){var e="number"==typeof this.cWidths[t]?this.cWidths[t]:null;this.stretchColumn(t,e)}},e.prototype.stretchColumn=function(t,e){var r,o,i,n,a,l,h=[];try{for(var c=s(this.tableRows),u=c.next();!u.done;u=c.next()){if(v=u.value.getChild(t))0===(_=v.childNodes[0]).stretch.dir&&_.canStretch(2)&&h.push(_)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}var p=h.length,d=this.childNodes.length;if(p&&d>1){if(null===e){e=0;var f=p>1&&p===d;try{for(var m=s(this.tableRows),y=m.next();!y.done;y=m.next()){var v;if(v=y.value.getChild(t)){var b=0===(_=v.childNodes[0]).stretch.dir;if(f||b){var x=_.getBBox(b).w;x>e&&(e=x)}}}}catch(t){i={error:t}}finally{try{y&&!y.done&&(n=m.return)&&n.call(m)}finally{if(i)throw i.error}}}try{for(var g=s(h),M=g.next();!M.done;M=g.next()){var _;(_=M.value).coreMO().getStretchedVariant([e])}}catch(t){a={error:t}}finally{try{M&&!M.done&&(l=g.return)&&l.call(g)}finally{if(a)throw a.error}}}},e.prototype.getTableData=function(){if(this.data)return this.data;for(var t=new Array(this.numRows).fill(0),e=new Array(this.numRows).fill(0),r=new Array(this.numCols).fill(0),o=new Array(this.numRows),i=new Array(this.numRows),n=[0],a=this.tableRows,s=0;s<a.length;s++){for(var l=a[s],h=0;h<l.numCells;h++){var c=l.getChild(h);this.updateHDW(c,h,s,t,e,r),this.recordPWidthCell(c,h)}o[s]=t[s],i[s]=e[s],l.labeled&&this.updateHDW(l.childNodes[0],0,s,t,e,n)}var u=n[0];return this.data={H:t,D:e,W:r,NH:o,ND:i,L:u},this.data},e.prototype.updateHDW=function(t,e,r,o,i,n){void 0===n&&(n=null);var a=t.getBBox(),s=a.h,l=a.d,h=a.w;s<.75&&(s=.75),l<.25&&(l=.25),s>o[r]&&(o[r]=s),l>i[r]&&(i[r]=l),n&&h>n[e]&&(n[e]=h)},e.prototype.recordPWidthCell=function(t,e){t.childNodes[0]&&t.childNodes[0].getBBox().pwidth&&this.pwidthCells.push([t,e])},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r,o,i=this.getTableData(),a=i.H,s=i.D;if(this.node.attributes.get("equalrows")){var l=this.getEqualRowHeight();r=c.sum([].concat(this.rLines,this.rSpace))+l*this.numRows}else r=c.sum(a.concat(s,this.rLines,this.rSpace));r+=2*(this.fLine+this.fSpace[1]);var u=this.getComputedWidths();o=c.sum(u.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]);var p=this.node.attributes.get("width");"auto"!==p&&(o=Math.max(this.length2em(p,0)+2*this.fLine,o));var d=n(this.getBBoxHD(r),2),f=d[0],m=d[1];t.h=f,t.d=m,t.w=o;var y=n(this.getBBoxLR(),2),v=y[0],b=y[1];t.L=v,t.R=b,h.isPercent(p)||this.setColumnPWidths()},e.prototype.setChildPWidths=function(t,e,r){var o=this.node.attributes.get("width");if(!h.isPercent(o))return!1;this.hasLabels||(this.bbox.pwidth="",this.container.bbox.pwidth="");var i=this.bbox,n=i.w,a=i.L,s=i.R,l=Math.max(n,this.length2em(o,Math.max(e,a+n+s))),u=this.node.attributes.get("equalcolumns")?Array(this.numCols).fill(this.percent(1/Math.max(1,this.numCols))):this.getColumnAttributes("columnwidth",0);this.cWidths=this.getColumnWidthsFixed(u,l);var p=this.getComputedWidths();return this.pWidth=c.sum(p.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]),this.isTop&&(this.bbox.w=this.pWidth),this.setColumnPWidths(),this.pWidth!==n&&this.parent.invalidateBBox(),this.pWidth!==n},e.prototype.setColumnPWidths=function(){var t,e,r=this.cWidths;try{for(var o=s(this.pwidthCells),i=o.next();!i.done;i=o.next()){var a=n(i.value,2),l=a[0],h=a[1];l.setChildPWidths(!1,r[h])&&(l.invalidateBBox(),l.getBBox())}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}},e.prototype.getBBoxHD=function(t){var e=n(this.getAlignmentRow(),2),r=e[0],o=e[1];if(null===o){var i=this.font.params.axis_height,a=t/2;return{top:[0,t],center:[a,a],bottom:[t,0],baseline:[a,a],axis:[a+i,a-i]}[r]||[a,a]}var s=this.getVerticalPosition(o,r);return[s,t-s]},e.prototype.getBBoxLR=function(){if(this.hasLabels){var t=this.node.attributes.get("side"),e=n(this.getPadAlignShift(t),2),r=e[0];return"center"===e[1]?[r,r]:"left"===t?[r,0]:[0,r]}return[0,0]},e.prototype.getPadAlignShift=function(t){var e=this.getTableData().L+this.length2em(this.node.attributes.get("minlabelspacing")),r=n(null==this.styles?["",""]:[this.styles.get("padding-left"),this.styles.get("padding-right")],2),o=r[0],i=r[1];(o||i)&&(e=Math.max(e,this.length2em(o||"0"),this.length2em(i||"0")));var a=n(this.getAlignShift(),2),s=a[0],l=a[1];return s===t&&(l="left"===t?Math.max(e,l)-e:Math.min(-e,l)+e),[e,s,l]},e.prototype.getAlignShift=function(){return this.isTop?t.prototype.getAlignShift.call(this):[this.container.getChildAlign(this.containerI),0]},e.prototype.getWidth=function(){return this.pWidth||this.getBBox().w},e.prototype.getEqualRowHeight=function(){var t=this.getTableData(),e=t.H,r=t.D,o=Array.from(e.keys()).map((function(t){return e[t]+r[t]}));return Math.max.apply(Math,o)},e.prototype.getComputedWidths=function(){var t=this,e=this.getTableData().W,r=Array.from(e.keys()).map((function(r){return"number"==typeof t.cWidths[r]?t.cWidths[r]:e[r]}));return this.node.attributes.get("equalcolumns")&&(r=Array(r.length).fill(c.max(r))),r},e.prototype.getColumnWidths=function(){var t=this.node.attributes.get("width");if(this.node.attributes.get("equalcolumns"))return this.getEqualColumns(t);var e=this.getColumnAttributes("columnwidth",0);return"auto"===t?this.getColumnWidthsAuto(e):h.isPercent(t)?this.getColumnWidthsPercent(e):this.getColumnWidthsFixed(e,this.length2em(t))},e.prototype.getEqualColumns=function(t){var e,r=Math.max(1,this.numCols);if("auto"===t){var o=this.getTableData().W;e=c.max(o)}else if(h.isPercent(t))e=this.percent(1/r);else{var i=c.sum([].concat(this.cLines,this.cSpace))+2*this.fSpace[0];e=Math.max(0,this.length2em(t)-i)/r}return Array(this.numCols).fill(e)},e.prototype.getColumnWidthsAuto=function(t){var e=this;return t.map((function(t){return"auto"===t||"fit"===t?null:h.isPercent(t)?t:e.length2em(t)}))},e.prototype.getColumnWidthsPercent=function(t){var e=this,r=t.indexOf("fit")>=0,o=(r?this.getTableData():{W:null}).W;return Array.from(t.keys()).map((function(i){var n=t[i];return"fit"===n?null:"auto"===n?r?o[i]:null:h.isPercent(n)?n:e.length2em(n)}))},e.prototype.getColumnWidthsFixed=function(t,e){var r=this,o=Array.from(t.keys()),i=o.filter((function(e){return"fit"===t[e]})),n=o.filter((function(e){return"auto"===t[e]})),a=i.length||n.length,s=(a?this.getTableData():{W:null}).W,l=e-c.sum([].concat(this.cLines,this.cSpace))-2*this.fSpace[0],h=l;o.forEach((function(o){var i=t[o];h-="fit"===i||"auto"===i?s[o]:r.length2em(i,e)}));var u=a&&h>0?h/a:0;return o.map((function(e){var o=t[e];return"fit"===o?s[e]+u:"auto"===o?s[e]+(0===i.length?u:0):r.length2em(o,l)}))},e.prototype.getVerticalPosition=function(t,e){for(var r=this.node.attributes.get("equalrows"),o=this.getTableData(),i=o.H,a=o.D,s=r?this.getEqualRowHeight():0,l=this.getRowHalfSpacing(),h=this.fLine,c=0;c<t;c++)h+=l[c]+(r?s:i[c]+a[c])+l[c+1]+this.rLines[c];var u=n(r?[(s+i[t]-a[t])/2,(s-i[t]+a[t])/2]:[i[t],a[t]],2),p=u[0],d=u[1];return h+={top:0,center:l[t]+(p+d)/2,bottom:l[t]+p+d+l[t+1],baseline:l[t]+p,axis:l[t]+p-.25}[e]||0},e.prototype.getEmHalfSpacing=function(t,e){var r=this.em(t),o=this.addEm(e,2);return o.unshift(r),o.push(r),o},e.prototype.getRowHalfSpacing=function(){var t=this.rSpace.map((function(t){return t/2}));return t.unshift(this.fSpace[1]),t.push(this.fSpace[1]),t},e.prototype.getColumnHalfSpacing=function(){var t=this.cSpace.map((function(t){return t/2}));return t.unshift(this.fSpace[0]),t.push(this.fSpace[0]),t},e.prototype.getAlignmentRow=function(){var t=n(h.split(this.node.attributes.get("align")),2),e=t[0],r=t[1];if(null==r)return[e,null];var o=parseInt(r);return o<0&&(o+=this.numRows+1),[e,o<1||o>this.numRows?null:o-1]},e.prototype.getColumnAttributes=function(t,e){void 0===e&&(e=1);var r=this.numCols-e,o=this.getAttributeArray(t);if(0===o.length)return null;for(;o.length<r;)o.push(o[o.length-1]);return o.length>r&&o.splice(r),o},e.prototype.getRowAttributes=function(t,e){void 0===e&&(e=1);var r=this.numRows-e,o=this.getAttributeArray(t);if(0===o.length)return null;for(;o.length<r;)o.push(o[o.length-1]);return o.length>r&&o.splice(r),o},e.prototype.getAttributeArray=function(t){var e=this.node.attributes.get(t);return e?h.split(e):[this.node.attributes.getDefault(t)]},e.prototype.addEm=function(t,e){var r=this;return void 0===e&&(e=1),t?t.map((function(t){return r.em(t/e)})):null},e.prototype.convertLengths=function(t){var e=this;return t?t.map((function(t){return e.length2em(t)})):null},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmlabeledtr=e.CHTMLmtr=void 0;var n=r(0),a=r(12),s=r(12),l=r(94),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){t.prototype.toCHTML.call(this,e);var r=this.node.attributes.get("rowalign");"baseline"!==r&&this.adaptor.setAttribute(this.chtml,"rowalign",r)},e.kind=l.MmlMtr.prototype.kind,e.styles={"mjx-mtr":{display:"table-row"},'mjx-mtr[rowalign="top"] > mjx-mtd':{"vertical-align":"top"},'mjx-mtr[rowalign="center"] > mjx-mtd':{"vertical-align":"middle"},'mjx-mtr[rowalign="bottom"] > mjx-mtd':{"vertical-align":"bottom"},'mjx-mtr[rowalign="baseline"] > mjx-mtd':{"vertical-align":"baseline"},'mjx-mtr[rowalign="axis"] > mjx-mtd':{"vertical-align":".25em"}},e}(a.CommonMtrMixin(n.CHTMLWrapper));e.CHTMLmtr=h;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){t.prototype.toCHTML.call(this,e);var r=this.adaptor.firstChild(this.chtml);if(r){this.adaptor.remove(r);var o=this.node.attributes.get("rowalign"),i="baseline"!==o&&"axis"!==o?{rowalign:o}:{},n=this.html("mjx-mtr",i,[r]);h.used=!0,this.adaptor.append(this.parent.labels,n)}},e.kind=l.MmlMlabeledtr.prototype.kind,e.styles={"mjx-mlabeledtr":{display:"table-row"},'mjx-mlabeledtr[rowalign="top"] > mjx-mtd':{"vertical-align":"top"},'mjx-mlabeledtr[rowalign="center"] > mjx-mtd':{"vertical-align":"middle"},'mjx-mlabeledtr[rowalign="bottom"] > mjx-mtd':{"vertical-align":"bottom"},'mjx-mlabeledtr[rowalign="baseline"] > mjx-mtd':{"vertical-align":"baseline"},'mjx-mlabeledtr[rowalign="axis"] > mjx-mtd':{"vertical-align":".25em"}},e}(s.CommonMlabeledtrMixin(h));e.CHTMLmlabeledtr=c},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmtd=void 0;var n=r(0),a=r(58),s=r(95),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){t.prototype.toCHTML.call(this,e);var r=this.node.attributes.get("rowalign"),o=this.node.attributes.get("columnalign");r!==this.parent.node.attributes.get("rowalign")&&this.adaptor.setAttribute(this.chtml,"rowalign",r),"center"===o||"mlabeledtr"===this.parent.kind&&this===this.parent.childNodes[0]&&o===this.parent.parent.node.attributes.get("side")||this.adaptor.setStyle(this.chtml,"textAlign",o),this.adaptor.append(this.chtml,this.html("mjx-tstrut"))},e.kind=s.MmlMtd.prototype.kind,e.styles={"mjx-mtd":{display:"table-cell","text-align":"center",padding:".215em .4em"},"mjx-mtd:first-child":{"padding-left":0},"mjx-mtd:last-child":{"padding-right":0},"mjx-mtable > * > mjx-itable > *:first-child > mjx-mtd":{"padding-top":0},"mjx-mtable > * > mjx-itable > *:last-child > mjx-mtd":{"padding-bottom":0},"mjx-tstrut":{display:"inline-block",height:"1em","vertical-align":"-.25em"},'mjx-labels[align="left"] > mjx-mtr > mjx-mtd':{"text-align":"left"},'mjx-labels[align="right"] > mjx-mtr > mjx-mtd':{"text-align":"right"},'mjx-mtr mjx-mtd[rowalign="top"], mjx-mlabeledtr mjx-mtd[rowalign="top"]':{"vertical-align":"top"},'mjx-mtr mjx-mtd[rowalign="center"], mjx-mlabeledtr mjx-mtd[rowalign="center"]':{"vertical-align":"middle"},'mjx-mtr mjx-mtd[rowalign="bottom"], mjx-mlabeledtr mjx-mtd[rowalign="bottom"]':{"vertical-align":"bottom"},'mjx-mtr mjx-mtd[rowalign="baseline"], mjx-mlabeledtr mjx-mtd[rowalign="baseline"]':{"vertical-align":"baseline"},'mjx-mtr mjx-mtd[rowalign="axis"], mjx-mlabeledtr mjx-mtd[rowalign="axis"]':{"vertical-align":".25em"}},e}(a.CommonMtdMixin(n.CHTMLWrapper));e.CHTMLmtd=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMtdMixin=void 0,e.CommonMtdMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),Object.defineProperty(e.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.invalidateBBox=function(){this.bboxComputed=!1},e.prototype.getWrapWidth=function(t){var e=this.parent.parent,r=this.parent,o=this.node.childPosition()-(r.labeled?1:0);return"number"==typeof e.cWidths[o]?e.cWidths[o]:e.getTableData().W[o]},e.prototype.getChildAlign=function(t){return this.node.attributes.get("columnalign")},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmaction=void 0;var n=r(0),a=r(13),s=r(13),l=r(96),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t);this.selected.toCHTML(e),this.action(this,this.data)},e.prototype.setEventHandler=function(t,e){this.chtml.addEventListener(t,e)},e.kind=l.MmlMaction.prototype.kind,e.styles={"mjx-maction":{position:"relative"},"mjx-maction > mjx-tool":{display:"none",position:"absolute",bottom:0,right:0,width:0,height:0,"z-index":500},"mjx-tool > mjx-tip":{display:"inline-block",padding:".2em",border:"1px solid #888","font-size":"70%","background-color":"#F8F8F8",color:"black","box-shadow":"2px 2px 5px #AAAAAA"},"mjx-maction[toggle]":{cursor:"pointer"},"mjx-status":{display:"block",position:"fixed",left:"1em",bottom:"1em","min-width":"25%",padding:".2em .4em",border:"1px solid #888","font-size":"90%","background-color":"#F8F8F8",color:"black"}},e.actions=new Map([["toggle",[function(t,e){t.adaptor.setAttribute(t.chtml,"toggle",t.node.attributes.get("selection"));var r=t.factory.jax.math,o=t.factory.jax.document,i=t.node;t.setEventHandler("click",(function(t){r.end.node||(r.start.node=r.end.node=r.typesetRoot,r.start.n=r.end.n=0),i.nextToggleSelection(),r.rerender(o),t.stopPropagation()}))},{}]],["tooltip",[function(t,e){var r=t.childNodes[1];if(r)if(r.node.isKind("mtext")){var o=r.node.getText();t.adaptor.setAttribute(t.chtml,"title",o)}else{var i=t.adaptor,n=i.append(t.chtml,t.html("mjx-tool",{style:{bottom:t.em(-t.dy),right:t.em(-t.dx)}},[t.html("mjx-tip")]));r.toCHTML(i.firstChild(n)),t.setEventHandler("mouseover",(function(r){e.stopTimers(t,e);var o=setTimeout((function(){return i.setStyle(n,"display","block")}),e.postDelay);e.hoverTimer.set(t,o),r.stopPropagation()})),t.setEventHandler("mouseout",(function(r){e.stopTimers(t,e);var o=setTimeout((function(){return i.setStyle(n,"display","")}),e.clearDelay);e.clearTimer.set(t,o),r.stopPropagation()}))}},s.TooltipData]],["statusline",[function(t,e){var r=t.childNodes[1];if(r&&r.node.isKind("mtext")){var o=t.adaptor,i=r.node.getText();o.setAttribute(t.chtml,"statusline",i),t.setEventHandler("mouseover",(function(r){if(null===e.status){var n=o.body(o.document);e.status=o.append(n,t.html("mjx-status",{},[t.text(i)]))}r.stopPropagation()})),t.setEventHandler("mouseout",(function(t){e.status&&(o.remove(e.status),e.status=null),t.stopPropagation()}))}},{status:null}]]]),e}(a.CommonMactionMixin(n.CHTMLWrapper));e.CHTMLmaction=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLmglyph=void 0;var n=r(0),a=r(61),s=r(97),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t),r=this.node.attributes.getList("src","alt"),o=r.src,i=r.alt,n={width:this.em(this.width),height:this.em(this.height)};this.valign&&(n.verticalAlign=this.em(this.valign));var a=this.html("img",{src:o,style:n,alt:i,title:i});this.adaptor.append(e,a)},e.kind=s.MmlMglyph.prototype.kind,e.styles={"mjx-mglyph > img":{display:"inline-block",border:0,padding:0}},e}(a.CommonMglyphMixin(n.CHTMLWrapper));e.CHTMLmglyph=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a},a=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(n(arguments[e]));return t};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonMglyphMixin=void 0,e.CommonMglyphMixin=function(t){return function(t){function e(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var o=t.apply(this,a(e))||this;return o.getParameters(),o}return i(e,t),e.prototype.getParameters=function(){var t=this.node.attributes.getList("width","height","valign"),e=t.width,r=t.height,o=t.valign;this.width="auto"===e?1:this.length2em(e),this.height="auto"===r?1:this.length2em(r),this.valign=this.length2em(o||"0")},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1),t.w=this.width,t.h=this.height+this.valign,t.d=-this.valign},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLxml=e.CHTMLannotationXML=e.CHTMLannotation=e.CHTMLsemantics=void 0;var n=r(0),a=r(63),s=r(98),l=r(5),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e=this.standardCHTMLnode(t);this.childNodes.length&&this.childNodes[0].toCHTML(e)},e.kind=s.MmlSemantics.prototype.kind,e}(a.CommonSemanticsMixin(n.CHTMLWrapper));e.CHTMLsemantics=h;var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){t.prototype.toCHTML.call(this,e)},e.prototype.computeBBox=function(){return this.bbox},e.kind=s.MmlAnnotation.prototype.kind,e}(n.CHTMLWrapper);e.CHTMLannotation=c;var u=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.kind=s.MmlAnnotationXML.prototype.kind,e.styles={"mjx-annotation-xml":{"font-family":"initial","line-height":"normal"}},e}(n.CHTMLWrapper);e.CHTMLannotationXML=u;var p=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){this.chtml=this.adaptor.append(t,this.adaptor.clone(this.node.getXML()))},e.prototype.computeBBox=function(t,e){void 0===e&&(e=!1);var r=this.jax.measureXMLnode(this.node.getXML()),o=r.w,i=r.h,n=r.d;t.w=o,t.h=i,t.d=n},e.prototype.getStyles=function(){},e.prototype.getScale=function(){},e.prototype.getVariant=function(){},e.kind=l.XMLNode.prototype.kind,e.autoStyle=!1,e}(n.CHTMLWrapper);e.CHTMLxml=p},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonSemanticsMixin=void 0,e.CommonSemanticsMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.computeBBox=function(t,e){if(void 0===e&&(e=!1),this.childNodes.length){var r=this.childNodes[0].getBBox(),o=r.w,i=r.h,n=r.d;t.w=o,t.h=i,t.d=n}},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLTeXAtom=void 0;var n=r(0),a=r(65),s=r(99),l=r(5),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(e){if(t.prototype.toCHTML.call(this,e),this.adaptor.setAttribute(this.chtml,"texclass",l.TEXCLASSNAMES[this.node.texClass]),this.node.texClass===l.TEXCLASS.VCENTER){var r=this.childNodes[0].getBBox(),o=r.h,i=(o+r.d)/2+this.font.params.axis_height-o;this.adaptor.setStyle(this.chtml,"verticalAlign",this.em(i))}},e.kind=s.TeXAtom.prototype.kind,e}(a.CommonTeXAtomMixin(n.CHTMLWrapper));e.CHTMLTeXAtom=h},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTeXAtomMixin=void 0;var n=r(5);e.CommonTeXAtomMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.computeBBox=function(e,r){if(void 0===r&&(r=!1),t.prototype.computeBBox.call(this,e,r),this.childNodes[0]&&this.childNodes[0].bbox.ic&&(e.ic=this.childNodes[0].bbox.ic),this.node.texClass===n.TEXCLASS.VCENTER){var o=e.h,i=(o+e.d)/2+this.font.params.axis_height-o;e.h+=i,e.d-=i}},e}(t)}},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.CHTMLTextNode=void 0;var a=r(5),s=r(0),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.toCHTML=function(t){var e,r;this.markUsed();var o=this.adaptor,i=this.parent.variant,a=this.node.getText();if("-explicitFont"===i){var s=this.jax.getFontData(this.parent.styles);o.append(t,this.jax.unknownText(a,i,s))}else{var l=this.remappedText(a,i);try{for(var h=n(l),c=h.next();!c.done;c=h.next()){var u=c.value,p=this.getVariantChar(i,u)[3],d=(s=p.f?" TEX-"+p.f:"",p.unknown?this.jax.unknownText(String.fromCodePoint(u),i):this.html("mjx-c",{class:this.char(u)+s}));o.append(t,d),p.used=!0}}catch(t){e={error:t}}finally{try{c&&!c.done&&(r=h.return)&&r.call(h)}finally{if(e)throw e.error}}}},e.kind=a.TextNode.prototype.kind,e.autoStyle=!1,e.styles={"mjx-c":{display:"inline-block"},"mjx-utext":{display:"inline-block",padding:".75em 0 .2em 0"}},e}(r(67).CommonTextNodeMixin(s.CHTMLWrapper));e.CHTMLTextNode=l},function(t,e,r){"use strict";var o,i=this&&this.__extends||(o=function(t,e){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],o=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var o,i,n=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(o=n.next()).done;)a.push(o.value)}catch(t){i={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.CommonTextNodeMixin=void 0,e.CommonTextNodeMixin=function(t){return function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype.computeBBox=function(t,e){var r,o;void 0===e&&(e=!1);var i=this.parent.variant,s=this.node.getText();if("-explicitFont"===i){var l=this.jax.getFontData(this.parent.styles),h=this.jax.measureText(s,i,l),c=h.w,u=h.h,p=h.d;t.h=u,t.d=p,t.w=c}else{var d=this.remappedText(s,i);t.empty();try{for(var f=n(d),m=f.next();!m.done;m=f.next()){var y=m.value,v=a(this.getVariantChar(i,y),4),b=(u=v[0],p=v[1],c=v[2],v[3]);if(b.unknown){var x=this.jax.measureText(String.fromCodePoint(y),i);c=x.w,u=x.h,p=x.d}t.w+=c,u>t.h&&(t.h=u),p>t.d&&(t.d=p),t.ic=b.ic||0,t.sk=b.sk||0}}catch(t){r={error:t}}finally{try{m&&!m.done&&(o=f.return)&&o.call(f)}finally{if(r)throw r.error}}d.length>1&&(t.sk=0),t.clean()}},e.prototype.remappedText=function(t,e){var r=this.parent.stretch.c;return r?[r]:this.parent.remapChars(this.unicodeChars(t,e))},e.prototype.getStyles=function(){},e.prototype.getVariant=function(){},e.prototype.getScale=function(){},e.prototype.getSpace=function(){},e}(t)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Styles=MathJax._.util.Styles.Styles},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractOutputJax=MathJax._.core.OutputJax.AbstractOutputJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.protoItem=MathJax._.core.MathItem.protoItem,e.AbstractMathItem=MathJax._.core.MathItem.AbstractMathItem,e.STATE=MathJax._.core.MathItem.STATE,e.newState=MathJax._.core.MathItem.newState},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.CssStyles=MathJax._.util.StyleList.CssStyles},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractWrapperFactory=MathJax._.core.Tree.WrapperFactory.AbstractWrapperFactory},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AbstractWrapper=MathJax._.core.Tree.Wrapper.AbstractWrapper},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMath=MathJax._.core.MmlTree.MmlNodes.math.MmlMath},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMi=MathJax._.core.MmlTree.MmlNodes.mi.MmlMi},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMo=MathJax._.core.MmlTree.MmlNodes.mo.MmlMo},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMn=MathJax._.core.MmlTree.MmlNodes.mn.MmlMn},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMs=MathJax._.core.MmlTree.MmlNodes.ms.MmlMs},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtext=MathJax._.core.MmlTree.MmlNodes.mtext.MmlMtext},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMspace=MathJax._.core.MmlTree.MmlNodes.mspace.MmlMspace},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMpadded=MathJax._.core.MmlTree.MmlNodes.mpadded.MmlMpadded},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMenclose=MathJax._.core.MmlTree.MmlNodes.menclose.MmlMenclose},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMrow=MathJax._.core.MmlTree.MmlNodes.mrow.MmlMrow,e.MmlInferredMrow=MathJax._.core.MmlTree.MmlNodes.mrow.MmlInferredMrow},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfenced=MathJax._.core.MmlTree.MmlNodes.mfenced.MmlMfenced},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMfrac=MathJax._.core.MmlTree.MmlNodes.mfrac.MmlMfrac},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsqrt=MathJax._.core.MmlTree.MmlNodes.msqrt.MmlMsqrt},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMroot=MathJax._.core.MmlTree.MmlNodes.mroot.MmlMroot},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMsubsup=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsubsup,e.MmlMsub=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsub,e.MmlMsup=MathJax._.core.MmlTree.MmlNodes.msubsup.MmlMsup},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMunderover=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMunderover,e.MmlMunder=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMunder,e.MmlMover=MathJax._.core.MmlTree.MmlNodes.munderover.MmlMover},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMmultiscripts=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlMmultiscripts,e.MmlMprescripts=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlMprescripts,e.MmlNone=MathJax._.core.MmlTree.MmlNodes.mmultiscripts.MmlNone},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sum=MathJax._.util.numeric.sum,e.max=MathJax._.util.numeric.max},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtable=MathJax._.core.MmlTree.MmlNodes.mtable.MmlMtable},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtr=MathJax._.core.MmlTree.MmlNodes.mtr.MmlMtr,e.MmlMlabeledtr=MathJax._.core.MmlTree.MmlNodes.mtr.MmlMlabeledtr},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMtd=MathJax._.core.MmlTree.MmlNodes.mtd.MmlMtd},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMaction=MathJax._.core.MmlTree.MmlNodes.maction.MmlMaction},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlMglyph=MathJax._.core.MmlTree.MmlNodes.mglyph.MmlMglyph},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MmlSemantics=MathJax._.core.MmlTree.MmlNodes.semantics.MmlSemantics,e.MmlAnnotationXML=MathJax._.core.MmlTree.MmlNodes.semantics.MmlAnnotationXML,e.MmlAnnotation=MathJax._.core.MmlTree.MmlNodes.semantics.MmlAnnotation},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TeXAtom=MathJax._.core.MmlTree.MmlNodes.TeXAtom.TeXAtom},function(t,e,r){"use strict";r.r(e),r.d(e,"TeXFont",(function(){return c}));var o=r(16);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){return(a=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function s(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=h(t);if(e){var i=h(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return l(this,r)}}function l(t,e){return!e||"object"!==i(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var c=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&a(t,e)}(r,t);var e=s(r);function r(){return n(this,r),e.apply(this,arguments)}return r}(o.FontData);c.OPTIONS={fontURL:"."}},function(t,e,r){"use strict";r.r(e);var o=r(14),i=r(15),n=r(16),a=r(40),s=r(0),l=r(18),h=r(20),c=r(64),u=r(66),p=r(59),d=r(22),f=r(38),m=r(42),y=r(44),v=r(60),b=r(24),x=r(52),g=r(28),M=r(26),_=r(36),w=r(47),C=r(41),j=r(30),S=r(34),T=r(11),O=r(7),L=r(54),B=r(57),P=r(32),H=r(56),k=r(51),A=r(49),N=r(62),D=r(4),W=r(6),R=r(17),E=r(21),I=r(19),F=r(65),J=r(67),V=r(13),z=r(23),X=r(39),K=r(43),q=r(45),U=r(61),Q=r(25),G=r(53),Y=r(29),Z=r(27),$=r(37),tt=r(48),et=r(10),rt=r(31),ot=r(35),it=r(46),nt=r(8),at=r(55),st=r(58),lt=r(33),ht=r(12),ct=r(9),ut=r(50),pt=r(63);Object(o.combineWithMathJax)({_:{output:{chtml_ts:i,chtml:{FontData:n,Notation:a,Wrapper:s,WrapperFactory:l,Wrappers_ts:h,Wrappers:{TeXAtom:c,TextNode:u,maction:p,math:d,menclose:f,mfenced:m,mfrac:y,mglyph:v,mi:b,mmultiscripts:x,mn:g,mo:M,mpadded:_,mroot:w,mrow:C,ms:j,mspace:S,msqrt:T,msubsup:O,mtable:L,mtd:B,mtext:P,mtr:H,munderover:k,scriptbase:A,semantics:N}},common:{FontData:D,Notation:W,OutputJax:R,Wrapper:E,WrapperFactory:I,Wrappers:{TeXAtom:F,TextNode:J,maction:V,math:z,menclose:X,mfenced:K,mfrac:q,mglyph:U,mi:Q,mmultiscripts:G,mn:Y,mo:Z,mpadded:$,mroot:tt,mrow:et,ms:rt,mspace:ot,msqrt:it,msubsup:nt,mtable:at,mtd:st,mtext:lt,mtr:ht,munderover:ct,scriptbase:ut,semantics:pt}}}}}),MathJax.loader&&Object(o.combineDefaults)(MathJax.config.loader,"output/chtml",{checkReady:function(){return MathJax.loader.load("output/chtml/fonts/tex")}}),MathJax.startup&&(MathJax.startup.registerConstructor("chtml",i.CHTML),MathJax.startup.useOutput("chtml"))}]);