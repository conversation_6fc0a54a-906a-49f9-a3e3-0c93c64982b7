!function(t){var e={};function r(a){if(e[a])return e[a].exports;var n=e[a]={i:a,l:!1,exports:{}};return t[a].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=t,r.c=e,r.d=function(t,e,a){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(a,n,function(e){return t[e]}.bind(null,n));return a},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(t,e,r){"use strict";var a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],a=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&a>=t.length&&(t=void 0),{value:t&&t[a++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var a,n,o=r.call(t),i=[];try{for(;(void 0===e||e-- >0)&&!(a=o.next()).done;)i.push(a.value)}catch(t){n={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return i};Object.defineProperty(e,"__esModule",{value:!0}),e.loadLatest=void 0;var o=new Map([["cdnjs.cloudflare.com",{api:"https://api.cdnjs.com/libraries/mathjax?fields=version",key:"version",base:"https://cdnjs.cloudflare.com/ajax/libs/mathjax/"}],["rawcdn.githack.com",{api:"https://api.github.com/repos/mathjax/mathjax/releases/latest",key:"tag_name",base:"https://rawcdn.githack.com/mathjax/MathJax/"}],["gitcdn.xyz",{api:"https://api.github.com/repos/mathjax/mathjax/releases/latest",key:"tag_name",base:"https://gitcdn.xyz/mathjax/MathJax/"}],["cdn.statically.io",{api:"https://api.github.com/repos/mathjax/mathjax/releases/latest",key:"tag_name",base:"https://cdn.statically.io/gh/mathjax/MathJax/"}],["unpkg.com",{api:"https://api.github.com/repos/mathjax/mathjax/releases/latest",key:"tag_name",base:"https://unpkg.com/mathjax@"}],["cdn.jsdelivr.net",{api:"https://api.github.com/repos/mathjax/mathjax/releases/latest",key:"tag_name",base:"https://cdn.jsdelivr.net/npm/mathjax@"}]]),i={api:"https://api.github.com/repos/mathjax/mathjax/releases",key:"tag_name"},c=null;function s(t){console&&console.error&&console.error("MathJax(latest.js): "+t)}function l(t,e){void 0===e&&(e=null),t.parentNode.removeChild(t);var r=t.src,a=r.replace(/.*?\/latest\.js(\?|$)/,"");""===a&&(a="startup.js",r=r.replace(/\?$/,"")+"?"+a);var n=(r.match(/(\d+\.\d+\.\d+)(\/es\d+)?\/latest.js\?/)||["",""])[1],o=(r.match(/(\/es\d+)\/latest.js\?/)||["",""])[1]||"";return{tag:t,src:r,id:t.id,version:n,dir:o,file:a,cdn:e}}function u(t){var e,r;try{for(var n=a(o.keys()),i=n.next();!i.done;i=n.next()){var c=i.value,s=o.get(c),u=s.base,d=t.src;if(d&&d.substr(0,u.length)===u&&d.match(/\/latest\.js(\?|$)/))return l(t,s)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return null}function d(t,e){var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.src=t,e&&(r.id=e);var a=document.head||document.getElementsByTagName("head")[0]||document.body;a?a.appendChild(r):s("Can't find the document <head> element")}function f(){c?d(c.src.replace(/\/latest\.js\?/,"/"),c.id):s("Can't determine the URL for loading MathJax")}function p(t){c.version&&c.version!==t&&(c.file="latest.js?"+c.file),d(c.cdn.base+t+c.dir+"/"+c.file,c.id)}function h(t){return 3===parseInt(t.split(/\./)[0])&&!t.match(/-(beta|rc)/)&&(function(t){try{var e=t+" "+Date.now();localStorage.setItem("mjx-latest-version",e)}catch(t){}}(t),p(t),!0)}function m(t,e,r){var a=function(){if(window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject){try{return new window.ActiveXObject("Msxml2.XMLHTTP")}catch(t){}try{return new window.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}return null}();a?(a.onreadystatechange=function(){4===a.readyState&&(200===a.status?!e(JSON.parse(a.responseText))&&r():(s("Problem acquiring MathJax version: status = "+a.status),r()))},a.open("GET",t.api,!0),a.send(null)):(s("Can't create XMLHttpRequest object"),r())}function y(){m(c.cdn,(function(t){return t instanceof Array&&(t=t[0]),h(t[c.cdn.key])||m(i,(function(t){var e,r;if(!(t instanceof Array))return!1;try{for(var n=a(t),o=n.next();!o.done;o=n.next())if(h(o.value[i.key]))return!0}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return!1}),f),!0}),f)}e.loadLatest=function(){if((c=function(){var t,e;if(document.currentScript)return l(document.currentScript);var r=document.getElementById("MathJax-script");if(r&&"script"===r.nodeName.toLowerCase())return u(r);var n=document.getElementsByTagName("script");try{for(var o=a(Array.from(n)),i=o.next();!i.done;i=o.next()){var c=u(i.value);if(c)return c}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=o.return)&&e.call(o)}finally{if(t)throw t.error}}return null}())&&c.cdn){var t=function(){try{var t=n(localStorage.getItem("mjx-latest-version").split(/ /),2),e=t[0],r=t[1];if(r&&Date.now()-parseInt(r)<6048e5)return e}catch(t){}return null}();t?p(t):y()}else f()}},function(t,e,r){"use strict";r.r(e);var a=r(0);Object(a.loadLatest)()}]);