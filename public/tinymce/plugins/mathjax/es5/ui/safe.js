!function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=6)}([function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__assign||function(){return(a=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o},l=this&&this.__spread||function(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(o(arguments[e]));return t},s=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.SafeHandler=e.SafeMathDocumentMixin=void 0;var u=r(1);function f(t){var e;return(e=function(t){function e(){for(var e,r,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var a=t.apply(this,l(n))||this;a.safe=new a.options.SafeClass(a,a.options.safeOptions);var o=a.constructor.ProcessBits;o.has("safe")||o.allocate("safe");try{for(var u=s(a.inputJax),f=u.next();!f.done;f=u.next()){var c=f.value;c.name.match(/MathML/)?(c.mathml.filterAttribute=a.safe.mmlAttribute.bind(a.safe),c.mathml.filterClassList=a.safe.mmlClassList.bind(a.safe)):c.name.match(/TeX/)&&c.postFilters.add(a.sanitize.bind(c),-5.5)}}catch(t){e={error:t}}finally{try{f&&!f.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}return a}return i(e,t),e.prototype.sanitize=function(t){t.math.root=this.parseOptions.root,t.document.safe.sanitize(t.math,t.document)},e}(t)).OPTIONS=a(a({},t.OPTIONS),{safeOptions:a({},u.Safe.OPTIONS),SafeClass:u.Safe}),e}e.SafeMathDocumentMixin=f,e.SafeHandler=function(t){return t.documentClass=f(t.documentClass),t}},function(t,e,r){"use strict";var n=this&&this.__assign||function(){return(n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.Safe=void 0;var a=r(4),o=r(2),l=function(){function t(t,e){this.filterAttributes=new Map([["href","filterURL"],["src","filterURL"],["altimg","filterURL"],["class","filterClassList"],["style","filterStyles"],["id","filterID"],["fontsize","filterFontSize"],["mathsize","filterFontSize"],["scriptminsize","filterFontSize"],["scriptsizemultiplier","filterSizeMultiplier"],["scriptlevel","filterScriptLevel"],["data-","filterData"]]),this.filterMethods=n({},o.SafeMethods),this.adaptor=t.adaptor,this.options=e,this.allow=this.options.allow}return t.prototype.sanitize=function(t,e){try{t.root.walkTree(this.sanitizeNode.bind(this))}catch(r){e.options.compileError(e,t,r)}},t.prototype.sanitizeNode=function(t){var e,r,n=t.attributes.getAllAttributes();try{for(var a=i(Object.keys(n)),o=a.next();!o.done;o=a.next()){var l=o.value,s=this.filterAttributes.get(l);if(s){var u=this.filterMethods[s](this,n[l]);u?u!==("number"==typeof u?parseFloat(n[l]):n[l])&&(n[l]=u):delete n[l]}}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}},t.prototype.mmlAttribute=function(t,e){if("class"===t)return null;var r=this.filterAttributes.get(t)||("data-"===t.substr(0,5)?this.filterAttributes.get("data-"):null);if(!r)return e;var n=this.filterMethods[r](this,e,t);return"number"==typeof n||"boolean"==typeof n?String(n):n},t.prototype.mmlClassList=function(t){var e=this;return t.map((function(t){return e.filterMethods.filterClass(e,t)})).filter((function(t){return null!==t}))},t.OPTIONS={allow:{URLs:"safe",classes:"safe",cssIDs:"safe",styles:"safe"},lengthMax:3,scriptsizemultiplierRange:[.6,1],scriptlevelRange:[-2,2],classPattern:/^mjx-[-a-zA-Z0-9_.]+$/,idPattern:/^mjx-[-a-zA-Z0-9_.]+$/,dataPattern:/^data-mjx-/,safeProtocols:a.expandable({http:!0,https:!0,file:!0,javascript:!1,data:!1}),safeStyles:a.expandable({color:!0,backgroundColor:!0,border:!0,cursor:!0,margin:!0,padding:!0,textShadow:!0,fontFamily:!0,fontSize:!0,fontStyle:!0,fontWeight:!0,opacity:!0,outline:!0}),styleParts:a.expandable({border:!0,padding:!0,margin:!0,outline:!0}),styleLengths:a.expandable({borderTop:"borderTopWidth",borderRight:"borderRightWidth",borderBottom:"borderBottomWidth",borderLeft:"borderLeftWidth",paddingTop:!0,paddingRight:!0,paddingBottom:!0,paddingLeft:!0,marginTop:!0,marginRight:!0,marginBottom:!0,marginLeft:!0,outlineTop:!0,outlineRight:!0,outlineBottom:!0,outlineLeft:!0,fontSize:[.707,1.44]})},t}();e.Safe=l},function(t,e,r){"use strict";var n=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||e-- >0)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.SafeMethods=void 0;var a=r(5);e.SafeMethods={filterURL:function(t,e){var r=(e.match(/^\s*([a-z]+):/i)||[null,""])[1].toLowerCase(),n=t.allow.URLs;return"all"===n||"safe"===n&&(t.options.safeProtocols[r]||!r)?e:null},filterClassList:function(t,e){var r=this;return e.trim().replace(/\s\s+/g," ").split(/ /).map((function(e){return r.filterClass(t,e)||""})).join(" ").trim().replace(/\s\s+/g,"")},filterClass:function(t,e){var r=t.allow.classes;return"all"===r||"safe"===r&&e.match(t.options.classPattern)?e:null},filterID:function(t,e){var r=t.allow.cssIDs;return"all"===r||"safe"===r&&e.match(t.options.idPattern)?e:null},filterStyles:function(t,e){var r,i,a,o;if("all"===t.allow.styles)return e;if("safe"!==t.allow.styles)return null;var l=t.adaptor,s=t.options;try{var u=l.node("div",{style:e}),f=l.node("div");try{for(var c=n(Object.keys(s.safeStyles)),p=c.next();!p.done;p=c.next()){var h=p.value;if(s.styleParts[h])try{for(var d=(a=void 0,n(["Top","Right","Bottom","Left"])),y=d.next();!y.done;y=d.next()){var m,b=h+y.value;(m=this.filterStyle(t,b,u))&&l.setStyle(f,b,m)}}catch(t){a={error:t}}finally{try{y&&!y.done&&(o=d.return)&&o.call(d)}finally{if(a)throw a.error}}else(m=this.filterStyle(t,h,u))&&l.setStyle(f,h,m)}}catch(t){r={error:t}}finally{try{p&&!p.done&&(i=c.return)&&i.call(c)}finally{if(r)throw r.error}}e=l.allStyles(f)}catch(t){e=""}return e},filterStyle:function(t,e,r){var n=t.adaptor.getStyle(r,e);if("string"!=typeof n||""===n||n.match(/^\s*calc/)||n.match(/javascript:/)&&!t.options.safeProtocols.javascript||n.match(/data:/)&&!t.options.safeProtocols.data)return null;var i=e.replace(/Top|Right|Left|Bottom/,"");return t.options.safeStyles[e]||t.options.safeStyles[i]?this.filterStyleValue(t,e,n,r):null},filterStyleValue:function(t,e,r,n){var i=t.options.styleLengths[e];if(!i)return r;if("string"!=typeof i)return this.filterStyleLength(t,e,r);var a=this.filterStyleLength(t,i,t.adaptor.getStyle(n,i));return a?(t.adaptor.setStyle(n,i,a),t.adaptor.getStyle(n,e)):null},filterStyleLength:function(t,e,r){if(!r.match(/^(.+)(em|ex|ch|rem|px|mm|cm|in|pt|pc|%)$/))return null;var n=a.length2em(r,1),o=t.options.styleLengths[e],l=i(Array.isArray(o)?o:[-t.options.lengthMax,t.options.lengthMax],2),s=l[0],u=l[1];return s<=n&&n<=u?r:(n<s?s:u).toFixed(3).replace(/\.?0+$/,"")+"em"},filterFontSize:function(t,e){return this.filterStyleLength(t,"fontSize",e)},filterSizeMultiplier:function(t,e){var r=i(t.options.scriptsizemultiplierRange||[-1/0,1/0],2),n=r[0],a=r[1];return Math.min(a,Math.max(n,parseFloat(e)))},filterScriptLevel:function(t,e){var r=i(t.options.scriptlevelRange||[-1/0,1/0],2),n=r[0],a=r[1];return Math.min(a,Math.max(n,parseInt(e)))},filterData:function(t,e,r){return r.match(t.options.dataPattern)?e:null}}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isObject=MathJax._.components.global.isObject,e.combineConfig=MathJax._.components.global.combineConfig,e.combineDefaults=MathJax._.components.global.combineDefaults,e.combineWithMathJax=MathJax._.components.global.combineWithMathJax,e.MathJax=MathJax._.components.global.MathJax},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.APPEND=MathJax._.util.Options.APPEND,e.REMOVE=MathJax._.util.Options.REMOVE,e.Expandable=MathJax._.util.Options.Expandable,e.expandable=MathJax._.util.Options.expandable,e.makeArray=MathJax._.util.Options.makeArray,e.keys=MathJax._.util.Options.keys,e.copy=MathJax._.util.Options.copy,e.insert=MathJax._.util.Options.insert,e.defaultOptions=MathJax._.util.Options.defaultOptions,e.userOptions=MathJax._.util.Options.userOptions,e.selectOptions=MathJax._.util.Options.selectOptions,e.selectOptionsFromKeys=MathJax._.util.Options.selectOptionsFromKeys,e.separateOptions=MathJax._.util.Options.separateOptions},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BIGDIMEN=MathJax._.util.lengths.BIGDIMEN,e.UNITS=MathJax._.util.lengths.UNITS,e.RELUNITS=MathJax._.util.lengths.RELUNITS,e.MATHSPACE=MathJax._.util.lengths.MATHSPACE,e.length2em=MathJax._.util.lengths.length2em,e.percent=MathJax._.util.lengths.percent,e.em=MathJax._.util.lengths.em,e.emRounded=MathJax._.util.lengths.emRounded,e.px=MathJax._.util.lengths.px},function(t,e,r){"use strict";r.r(e);var n=r(3),i=r(0),a=r(2),o=r(1);Object(n.combineWithMathJax)({_:{ui:{safe:{SafeHandler:i,SafeMethods:a,safe:o}}}}),MathJax.startup&&"undefined"!=typeof window&&MathJax.startup.extendHandler((function(t){return Object(i.SafeHandler)(t)}))}]);