!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=6)}([function(e,t,n){"use strict";(function(e){var r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.CONFIG=t.MathJax=t.Loader=void 0;var o,a=n(1),i=n(2),u=n(2);Object.defineProperty(t,"Package",{enumerable:!0,get:function(){return u.Package}}),Object.defineProperty(t,"PackageError",{enumerable:!0,get:function(){return u.PackageError}}),function(n){n.ready=function(){for(var e,t,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];0===n.length&&(n=Array.from(i.Package.packages.keys()));var a=[];try{for(var u=r(n),c=u.next();!c.done;c=u.next()){var s=c.value,l=i.Package.packages.get(s)||new i.Package(s,!0);a.push(l.promise)}}catch(t){e={error:t}}finally{try{c&&!c.done&&(t=u.return)&&t.call(u)}finally{if(e)throw e.error}}return Promise.all(a)},n.load=function(){for(var e,n,o=[],a=0;a<arguments.length;a++)o[a]=arguments[a];if(0===o.length)return Promise.resolve();var u=[];try{for(var c=r(o),s=c.next();!s.done;s=c.next()){var l=s.value,d=i.Package.packages.get(l);d||(d=new i.Package(l)).provides(t.CONFIG.provides[l]),d.checkNoLoad(),u.push(d.promise)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(n=c.return)&&n.call(c)}finally{if(e)throw e.error}}return i.Package.loadAll(),Promise.all(u)},n.preLoad=function(){for(var e,n,o=[],a=0;a<arguments.length;a++)o[a]=arguments[a];try{for(var u=r(o),c=u.next();!c.done;c=u.next()){var s=c.value,l=i.Package.packages.get(s);l||(l=new i.Package(s,!0)).provides(t.CONFIG.provides[s]),l.loaded()}}catch(t){e={error:t}}finally{try{c&&!c.done&&(n=u.return)&&n.call(u)}finally{if(e)throw e.error}}},n.defaultReady=function(){void 0!==t.MathJax.startup&&t.MathJax.config.startup.ready()},n.getRoot=function(){var t=e+"/../../es5";if("undefined"!=typeof document){var n=document.currentScript||document.getElementById("MathJax-script");n&&(t=n.src.replace(/\/[^\/]*$/,""))}return t}}(o=t.Loader||(t.Loader={})),t.MathJax=a.MathJax,void 0===t.MathJax.loader&&(a.combineDefaults(t.MathJax.config,"loader",{paths:{mathjax:o.getRoot()},source:{},dependencies:{},provides:{},load:[],ready:o.defaultReady.bind(o),failed:function(e){return console.log("MathJax("+(e.package||"?")+"): "+e.message)},require:null}),a.combineWithMathJax({loader:o})),t.CONFIG=t.MathJax.config.loader}).call(this,"/")},function(e,t,n){"use strict";(function(e){var n=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};function r(e){return"object"==typeof e&&null!==e}function o(e,t){var a,i;try{for(var u=n(Object.keys(t)),c=u.next();!c.done;c=u.next()){var s=c.value;"__esModule"!==s&&(!r(e[s])||!r(t[s])||t[s]instanceof Promise?null!==t[s]&&void 0!==t[s]&&(e[s]=t[s]):o(e[s],t[s]))}}catch(e){a={error:e}}finally{try{c&&!c.done&&(i=u.return)&&i.call(u)}finally{if(a)throw a.error}}return e}Object.defineProperty(t,"__esModule",{value:!0}),t.MathJax=t.combineWithMathJax=t.combineDefaults=t.combineConfig=t.isObject=void 0,t.isObject=r,t.combineConfig=o,t.combineDefaults=function e(t,o,a){var i,u;t[o]||(t[o]={}),t=t[o];try{for(var c=n(Object.keys(a)),s=c.next();!s.done;s=c.next()){var l=s.value;r(t[l])&&r(a[l])?e(t,l,a[l]):null==t[l]&&null!=a[l]&&(t[l]=a[l])}}catch(e){i={error:e}}finally{try{s&&!s.done&&(u=c.return)&&u.call(c)}finally{if(i)throw i.error}}return t},t.combineWithMathJax=function(e){return o(t.MathJax,e)},void 0===e.MathJax&&(e.MathJax={}),e.MathJax.version||(e.MathJax={version:"3.1.2",_:{},config:e.MathJax}),t.MathJax=e.MathJax}).call(this,n(3))},function(e,t,n){"use strict";var r,o=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),a=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},i=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,a=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i},u=this&&this.__spread||function(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(i(arguments[t]));return e};Object.defineProperty(t,"__esModule",{value:!0}),t.Package=t.PackageError=void 0;var c=n(0),s=function(e){function t(t,n){var r=e.call(this,t)||this;return r.package=n,r}return o(t,e),t}(Error);t.PackageError=s;var l=function(){function e(t,n){void 0===n&&(n=!1),this.isLoaded=!1,this.isLoading=!1,this.hasFailed=!1,this.dependents=[],this.dependencies=[],this.dependencyCount=0,this.provided=[],this.name=t,this.noLoad=n,e.packages.set(t,this),this.promise=this.makePromise(this.makeDependencies())}return Object.defineProperty(e.prototype,"canLoad",{get:function(){return 0===this.dependencyCount&&!this.noLoad&&!this.isLoading&&!this.hasFailed},enumerable:!1,configurable:!0}),e.resolvePath=function(e,t){void 0===t&&(t=!0);var n,r=c.CONFIG.source[e]||e;for(r.match(/^(?:[a-z]+:\/)?\/|[a-z]:\\|\[/i)||(r="[mathjax]/"+r.replace(/^\.\//,"")),t&&!r.match(/\.[^\/]+$/)&&(r+=".js");(n=r.match(/^\[([^\]]*)\]/))&&c.CONFIG.paths.hasOwnProperty(n[1]);)r=c.CONFIG.paths[n[1]]+r.substr(n[0].length);return r},e.loadAll=function(){var e,t;try{for(var n=a(this.packages.values()),r=n.next();!r.done;r=n.next()){var o=r.value;o.canLoad&&o.load()}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}},e.prototype.makeDependencies=function(){var t,n,r=[],o=e.packages,i=this.noLoad,s=this.name,l=[];c.CONFIG.dependencies.hasOwnProperty(s)?l.push.apply(l,u(c.CONFIG.dependencies[s])):"core"!==s&&l.push("core");try{for(var d=a(l),f=d.next();!f.done;f=d.next()){var h=f.value,p=o.get(h)||new e(h,i);this.dependencies.indexOf(p)<0&&(p.addDependent(this,i),this.dependencies.push(p),p.isLoaded||(this.dependencyCount++,r.push(p.promise)))}}catch(e){t={error:e}}finally{try{f&&!f.done&&(n=d.return)&&n.call(d)}finally{if(t)throw t.error}}return r},e.prototype.makePromise=function(e){var t=this,n=new Promise((function(e,n){t.resolve=e,t.reject=n})),r=c.CONFIG[this.name]||{};return r.ready&&(n=n.then((function(e){return r.ready(t.name)}))),e.length&&(e.push(n),n=Promise.all(e).then((function(e){return e.join(", ")}))),r.failed&&n.catch((function(e){return r.failed(new s(e,t.name))})),n},e.prototype.load=function(){if(!this.isLoaded&&!this.isLoading&&!this.noLoad){this.isLoading=!0;var t=e.resolvePath(this.name);c.CONFIG.require?this.loadCustom(t):this.loadScript(t)}},e.prototype.loadCustom=function(e){var t=this;try{var n=c.CONFIG.require(e);n instanceof Promise?n.then((function(){return t.checkLoad()})).catch((function(n){return t.failed("Can't load \""+e+'"\n'+n.message.trim())})):this.checkLoad()}catch(e){this.failed(e.message)}},e.prototype.loadScript=function(e){var t=this,n=document.createElement("script");n.src=e,n.charset="UTF-8",n.onload=function(e){return t.checkLoad()},n.onerror=function(n){return t.failed("Can't load \""+e+'"')},document.head.appendChild(n)},e.prototype.loaded=function(){var e,t,n,r;this.isLoaded=!0,this.isLoading=!1;try{for(var o=a(this.dependents),i=o.next();!i.done;i=o.next()){i.value.requirementSatisfied()}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=o.return)&&t.call(o)}finally{if(e)throw e.error}}try{for(var u=a(this.provided),c=u.next();!c.done;c=u.next()){c.value.loaded()}}catch(e){n={error:e}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}this.resolve(this.name)},e.prototype.failed=function(e){this.hasFailed=!0,this.isLoading=!1,this.reject(new s(e,this.name))},e.prototype.checkLoad=function(){var e=this;((c.CONFIG[this.name]||{}).checkReady||function(){return Promise.resolve()})().then((function(){return e.loaded()})).catch((function(t){return e.failed(t)}))},e.prototype.requirementSatisfied=function(){this.dependencyCount&&(this.dependencyCount--,this.canLoad&&this.load())},e.prototype.provides=function(t){var n,r;void 0===t&&(t=[]);try{for(var o=a(t),i=o.next();!i.done;i=o.next()){var u=i.value,s=e.packages.get(u);s||(c.CONFIG.dependencies[u]||(c.CONFIG.dependencies[u]=[]),c.CONFIG.dependencies[u].push(u),(s=new e(u,!0)).isLoading=!0),this.provided.push(s)}}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}},e.prototype.addDependent=function(e,t){this.dependents.push(e),t||this.checkNoLoad()},e.prototype.checkNoLoad=function(){var e,t;if(this.noLoad){this.noLoad=!1;try{for(var n=a(this.dependencies),r=n.next();!r.done;r=n.next()){r.value.checkNoLoad()}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}}},e.packages=new Map,e}();t.Package=l},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";(function(e){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.CONFIG=t.MathJax=t.Startup=void 0;var a,i=n(1),u=n(5);!function(n){var a,i,s=new u.PrioritizedList;function l(e){return a.visitTree(e,n.document)}function d(){a=new t.MathJax._.core.MmlTree.SerializedMmlVisitor.SerializedMmlVisitor,i=t.MathJax._.mathjax.mathjax,n.input=y(),n.output=v(),n.adaptor=b(),n.handler&&i.handlers.unregister(n.handler),n.handler=g(),n.handler&&(i.handlers.register(n.handler),n.document=O())}function f(){var e,t;n.input&&n.output&&h();var r=n.output?n.output.name.toLowerCase():"";try{for(var a=o(n.input),i=a.next();!i.done;i=a.next()){var u=i.value,c=u.name.toLowerCase();m(c,u),x(c,u),n.output&&p(c,r,u)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}}function h(){t.MathJax.typeset=function(e){void 0===e&&(e=null),n.document.options.elements=e,n.document.reset(),n.document.render()},t.MathJax.typesetPromise=function(e){return void 0===e&&(e=null),n.document.options.elements=e,n.document.reset(),i.handleRetriesFor((function(){n.document.render()}))},t.MathJax.typesetClear=function(e){void 0===e&&(e=null),e?n.document.clearMathItemsWithin(e):n.document.clear()}}function p(e,r,o){var a=e+"2"+r;t.MathJax[a]=function(e,t){return void 0===t&&(t={}),t.format=o.name,n.document.convert(e,t)},t.MathJax[a+"Promise"]=function(e,t){return void 0===t&&(t={}),t.format=o.name,i.handleRetriesFor((function(){return n.document.convert(e,t)}))},t.MathJax[r+"Stylesheet"]=function(){return n.output.styleSheet(n.document)},"getMetricsFor"in n.output&&(t.MathJax.getMetricsFor=function(e,t){return n.output.getMetricsFor(e,t)})}function m(e,r){var o=t.MathJax._.core.MathItem.STATE;t.MathJax[e+"2mml"]=function(e,t){return void 0===t&&(t={}),t.end=o.CONVERT,t.format=r.name,l(n.document.convert(e,t))},t.MathJax[e+"2mmlPromise"]=function(e,t){return void 0===t&&(t={}),t.end=o.CONVERT,t.format=r.name,i.handleRetriesFor((function(){return l(n.document.convert(e,t))}))}}function x(e,n){"tex"===e&&(t.MathJax.texReset=function(e){return void 0===e&&(e=0),n.parseOptions.tags.reset(e)})}function y(){var e,r,a=[];try{for(var i=o(t.CONFIG.input),u=i.next();!u.done;u=i.next()){var c=u.value,s=n.constructors[c];if(!s)throw Error('Input Jax "'+c+'" is not defined (has it been loaded?)');a.push(new s(t.MathJax.config[c]))}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return a}function v(){var e=t.CONFIG.output;if(!e)return null;var r=n.constructors[e];if(!r)throw Error('Output Jax "'+e+'" is not defined (has it been loaded?)');return new r(t.MathJax.config[e])}function b(){var e=t.CONFIG.adaptor;if(!e||"none"===e)return null;var r=n.constructors[e];if(!r)throw Error('DOMAdaptor "'+e+'" is not defined (has it been loaded?)');return r(t.MathJax.config[e])}function g(){var e,r,a=t.CONFIG.handler;if(!a||"none"===a||!n.adaptor)return null;var i=n.constructors[a];if(!i)throw Error('Handler "'+a+'" is not defined (has it been loaded?)');var u=new i(n.adaptor,5);try{for(var c=o(s),l=c.next();!l.done;l=c.next()){u=l.value.item(u)}}catch(t){e={error:t}}finally{try{l&&!l.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}return u}function O(e){return void 0===e&&(e=null),i.document(e||t.CONFIG.document,r(r({},t.MathJax.config.options),{InputJax:n.input,OutputJax:n.output}))}n.constructors={},n.input=[],n.output=null,n.handler=null,n.adaptor=null,n.elements=null,n.document=null,n.promise=new Promise((function(e,t){n.promiseResolve=e,n.promiseReject=t})),n.pagePromise=new Promise((function(t,n){var r=e.document;if(r&&r.readyState&&"complete"!==r.readyState&&"interactive"!==r.readyState){var o=function(){return t()};r.defaultView.addEventListener("load",o,!0),r.defaultView.addEventListener("DOMContentLoaded",o,!0)}else t()})),n.toMML=l,n.registerConstructor=function(e,t){n.constructors[e]=t},n.useHandler=function(e,n){void 0===n&&(n=!1),t.CONFIG.handler&&!n||(t.CONFIG.handler=e)},n.useAdaptor=function(e,n){void 0===n&&(n=!1),t.CONFIG.adaptor&&!n||(t.CONFIG.adaptor=e)},n.useInput=function(e,n){void 0===n&&(n=!1),c&&!n||t.CONFIG.input.push(e)},n.useOutput=function(e,n){void 0===n&&(n=!1),t.CONFIG.output&&!n||(t.CONFIG.output=e)},n.extendHandler=function(e,t){void 0===t&&(t=10),s.add(e,t)},n.defaultReady=function(){d(),f(),n.pagePromise.then((function(){return t.CONFIG.pageReady()})).then((function(){return n.promiseResolve()})).catch((function(e){return n.promiseReject(e)}))},n.defaultPageReady=function(){return t.CONFIG.typeset&&t.MathJax.typesetPromise?t.MathJax.typesetPromise(t.CONFIG.elements):Promise.resolve()},n.getComponents=d,n.makeMethods=f,n.makeTypesetMethods=h,n.makeOutputMethods=p,n.makeMmlMethods=m,n.makeResetMethod=x,n.getInputJax=y,n.getOutputJax=v,n.getAdaptor=b,n.getHandler=g,n.getDocument=O}(a=t.Startup||(t.Startup={})),t.MathJax=i.MathJax,void 0===t.MathJax._.startup&&(i.combineDefaults(t.MathJax.config,"startup",{input:[],output:"",handler:null,adaptor:null,document:"undefined"==typeof document?"":document,elements:null,typeset:!0,ready:a.defaultReady.bind(a),pageReady:a.defaultPageReady.bind(a)}),i.combineWithMathJax({startup:a,options:{}})),t.CONFIG=t.MathJax.config.startup;var c=0!==t.CONFIG.input.length}).call(this,n(3))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PrioritizedList=void 0;var r=function(){function e(){this.items=[],this.items=[]}return e.prototype[Symbol.iterator]=function(){var e=0,t=this.items;return{next:function(){return{value:t[e++],done:e>t.length}}}},e.prototype.add=function(t,n){void 0===n&&(n=e.DEFAULTPRIORITY);var r=this.items.length;do{r--}while(r>=0&&n<this.items[r].priority);return this.items.splice(r+1,0,{item:t,priority:n}),t},e.prototype.remove=function(e){var t=this.items.length;do{t--}while(t>=0&&this.items[t].item!==e);t>=0&&this.items.splice(t,1)},e.prototype.toArray=function(){return Array.from(this)},e.DEFAULTPRIORITY=5,e}();t.PrioritizedList=r},function(e,t,n){"use strict";n.r(t);var r=n(1),o=n(0),a=n(2),i=n(4);Object(r.combineWithMathJax)({_:{components:{loader:o,package:a,startup:i}}});var u,c={tex:"[mathjax]/input/tex/extensions",sre:"[mathjax]/sre/"+("undefined"==typeof window?"sre-node":"sre_browser")},s=["[tex]/action","[tex]/ams","[tex]/amscd","[tex]/bbox","[tex]/boldsymbol","[tex]/braket","[tex]/bussproofs","[tex]/cancel","[tex]/color","[tex]/configmacros","[tex]/enclose","[tex]/extpfeil","[tex]/html","[tex]/mhchem","[tex]/newcommand","[tex]/noerrors","[tex]/noundefined","[tex]/physics","[tex]/require","[tex]/tagformat","[tex]/textmacros","[tex]/unicode","[tex]/verb"],l={startup:["loader"],"input/tex":["input/tex-base","[tex]/ams","[tex]/newcommand","[tex]/noundefined","[tex]/require","[tex]/autoload","[tex]/configmacros"],"input/tex-full":["input/tex-base","[tex]/all-packages"].concat(s),"[tex]/all-packages":s};function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Object(r.combineDefaults)(MathJax.config.loader,"dependencies",{"a11y/semantic-enrich":["input/mml","[sre]"],"a11y/complexity":["a11y/semantic-enrich"],"a11y/explorer":["a11y/semantic-enrich","ui/menu"],"[tex]/all-packages":["input/tex-base"],"[tex]/action":["input/tex-base","[tex]/newcommand"],"[tex]/autoload":["input/tex-base","[tex]/require"],"[tex]/ams":["input/tex-base"],"[tex]/amscd":["input/tex-base"],"[tex]/bbox":["input/tex-base","[tex]/ams","[tex]/newcommand"],"[tex]/boldsymbol":["input/tex-base"],"[tex]/braket":["input/tex-base"],"[tex]/bussproofs":["input/tex-base"],"[tex]/cancel":["input/tex-base","[tex]/enclose"],"[tex]/color":["input/tex-base"],"[tex]/colorv2":["input/tex-base"],"[tex]/configmacros":["input/tex-base","[tex]/newcommand"],"[tex]/enclose":["input/tex-base"],"[tex]/extpfeil":["input/tex-base","[tex]/newcommand","[tex]/ams"],"[tex]/html":["input/tex-base"],"[tex]/mhchem":["input/tex-base","[tex]/ams"],"[tex]/newcommand":["input/tex-base"],"[tex]/noerrors":["input/tex-base"],"[tex]/noundefined":["input/tex-base"],"[tex]/physics":["input/tex-base"],"[tex]/require":["input/tex-base"],"[tex]/tagformat":["input/tex-base"],"[tex]/textmacros":["input/tex-base"],"[tex]/unicode":["input/tex-base"],"[tex]/verb":["input/tex-base"]}),Object(r.combineDefaults)(MathJax.config.loader,"paths",c),Object(r.combineDefaults)(MathJax.config.loader,"provides",l),Object(r.combineDefaults)(MathJax.config.loader,"source",{"[tex]/amsCd":"[tex]/amscd","[tex]/colorV2":"[tex]/colorv2","[tex]/configMacros":"[tex]/configmacros","[tex]/tagFormat":"[tex]/tagformat"}),o.Loader.preLoad("loader"),o.Loader.load.apply(o.Loader,(u=o.CONFIG.load,function(e){if(Array.isArray(e))return d(e)}(u)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(u)||function(e,t){if(e){if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}(u)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())).then((function(){return o.CONFIG.ready()})).catch((function(e){return o.CONFIG.failed(e)}))}]);