/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.5.1 (2020-10-01)
 */
!function(){"use strict";var u,n,t,e,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),r=function(){return(r=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)},m=function(n){var t=n;return{get:function(){return t},set:function(n){t=n}}},a=Object.prototype.hasOwnProperty,c=function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)a.call(r,i)&&(e[i]=u(e[i],r[i]))}return e},i=function(n){return function(){return n}},l=i(!(u=function(n,t){return t})),s=i(!0),f=function(){return g},g=(n=function(n){return n.isNone()},{fold:function(n,t){return n()},is:l,isSome:l,isNone:s,getOr:e=function(n){return n},getOrThunk:t=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:i(null),getOrUndefined:i(undefined),or:e,orThunk:t,map:f,each:function(){},bind:f,exists:l,forall:s,filter:f,equals:n,equals_:n,toArray:function(){return[]},toString:i("none()")}),d=function(e){var n=i(e),t=function(){return r},o=function(n){return n(e)},r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:s,isNone:l,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:t,orThunk:t,map:function(n){return d(n(e))},each:function(n){n(e)},bind:o,exists:o,forall:o,filter:function(n){return n(e)?r:g},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(l,function(n){return t(e,n)})}};return r},y={some:d,none:f,from:function(n){return null===n||n===undefined?g:d(n)}},h=Object.keys,v=Object.hasOwnProperty,p=function(n,t){for(var e=h(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},b=function(n,o){var r={};return p(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},w=tinymce.util.Tools.resolve("tinymce.Resource"),O=tinymce.util.Tools.resolve("tinymce.util.Delay"),C=tinymce.util.Tools.resolve("tinymce.util.Promise"),k="All",j={symbols:"Symbols",people:"People",animals_and_nature:"Animals and Nature",food_and_drink:"Food and Drink",activity:"Activity",travel_and_places:"Travel and Places",objects:"Objects",flags:"Flags",user:"User Defined"},A=function(n,t){return e=n,o=t,v.call(e,o)?n[t]:t;var e,o},T=function(n){var e,t=n.getParam("emoticons_append",{},"object");return e=function(n){return r({keywords:[],category:"user"},n)},b(t,function(n,t){return{k:t,v:e(n,t)}})},_=function(o,r,n){var u=m(y.none()),a=m(y.none());o.on("init",function(){w.load(n,r).then(function(n){var t,r,i,e=T(o);t=c(n,e),r={},i=[],p(t,function(n,t){var e={title:t,keywords:n.keywords,"char":n["char"],category:A(j,n.category)},o=r[e.category]!==undefined?r[e.category]:[];r[e.category]=o.concat([e]),i.push(e)}),u.set(y.some(r)),a.set(y.some(i))},function(n){console.log("Failed to load emoticons: "+n),u.set(y.some({})),a.set(y.some([]))})});var e=function(){return a.get().getOr([])},i=function(){return u.get().isSome()&&a.get().isSome()};return{listCategories:function(){return[k].concat(h(u.get().getOr({})))},hasLoaded:i,waitForLoad:function(){return i()?C.resolve(!0):new C(function(n,t){var e=15,o=O.setInterval(function(){i()?(O.clearInterval(o),n(!0)):--e<0&&(console.log("Could not load emojis from url: "+r),O.clearInterval(o),t(!1))},100)})},listAll:e,listCategory:function(t){return t===k?e():u.get().bind(function(n){return y.from(n[t])}).getOr([])}}},D=function(n,t){return-1!==n.indexOf(t)},P=function(n,t,e){for(var o=[],r=t.toLowerCase(),i=e.fold(function(){return l},function(t){return function(n){return t<=n}}),u=0;u<n.length&&(0!==t.length&&!function(n,t){return D(n.title.toLowerCase(),t)||function(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}(n.keywords,function(n){return D(n.toLowerCase(),t)})}(n[u],r)||(o.push({value:n[u]["char"],text:n[u].title,icon:n[u]["char"]}),!i(o.length)));u++);return o},x="pattern",L=function(r,u){var e,o,i,n={pattern:"",results:P(u.listAll(),"",y.some(300))},a=m(k),c=(e=function(n){var t,e,o,r,i;e=(t=n).getData(),o=a.get(),r=u.listCategory(o),i=P(r,e[x],o===k?y.some(300):y.none()),t.setData({results:i})},o=200,i=null,{cancel:function(){null!==i&&(clearTimeout(i),i=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==i&&clearTimeout(i),i=setTimeout(function(){e.apply(null,n),i=null},o)}}),t={label:"Search",type:"input",name:x},l={type:"collection",name:"results"},s=function(){return{title:"Emoticons",size:"normal",body:{type:"tabpanel",tabs:function(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}(u.listCategories(),function(n){return{title:n,name:n,items:[t,l]}})},initialData:n,onTabChange:function(n,t){a.set(t.newTabName),c.throttle(n)},onChange:c.throttle,onAction:function(n,t){var e,o;"results"===t.name&&(e=r,o=t.value,e.insertContent(o),n.close())},buttons:[{type:"cancel",text:"Close",primary:!0}]}},f=r.windowManager.open(s());f.focus(x),u.hasLoaded()||(f.block("Loading emoticons..."),u.waitForLoad().then(function(){f.redial(s()),c.throttle(f),f.focus(x),f.unblock()})["catch"](function(n){f.redial({title:"Emoticons",body:{type:"panel",items:[{type:"alertbanner",level:"error",icon:"warning",text:"<p>Could not load emoticons</p>"}]},buttons:[{type:"cancel",text:"Close",primary:!0}],initialData:{pattern:"",results:[]}}),f.focus(x),f.unblock()}))};o.add("emoticons",function(n,t){var e,o,r,i,u,a,c,l=(o=t,(e=n).getParam("emoticons_database_url",o+"/js/emojis"+e.suffix+".js")),s=n.getParam("emoticons_database_id","tinymce.plugins.emoticons","string"),f=_(n,l,s);i=f,u=function(){return L(r,i)},(r=n).ui.registry.addButton("emoticons",{tooltip:"Emoticons",icon:"emoji",onAction:u}),r.ui.registry.addMenuItem("emoticons",{text:"Emoticons...",icon:"emoji",onAction:u}),c=f,(a=n).ui.registry.addAutocompleter("emoticons",{ch:":",columns:"auto",minChars:2,fetch:function(t,e){return c.waitForLoad().then(function(){var n=c.listAll();return P(n,t,y.some(e))})},onAction:function(n,t,e){a.selection.setRng(t),a.insertContent(e),n.hide()}})})}();